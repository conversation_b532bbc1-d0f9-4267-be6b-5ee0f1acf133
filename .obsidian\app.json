{"showLineNumber": false, "alwaysUpdateLinks": true, "newFileLocation": "current", "attachmentFolderPath": ".attachments", "showUnsupportedFiles": true, "newLinkFormat": "relative", "vimMode": true, "readableLineLength": false, "uriCallbacks": true, "promptDelete": false, "useMarkdownLinks": true, "focusNewTab": true, "pdfExportSettings": {"includeName": true, "pageSize": "Letter", "landscape": false, "margin": "0", "downscalePercent": 100}, "userIgnoreFilters": [".code-links/", ".code-links/**", "*.class", "*.jar", "target/", "node_modules/", "logs/", "*.log"]}