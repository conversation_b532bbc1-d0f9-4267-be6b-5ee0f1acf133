/*
THIS IS A GENERATED/BUNDLED FILE
If you want to view the source, visit the plugins github repository
https://github.com/ozntel/oz-image-in-editor-obsidian
*/

var cu=Object.create;var Se=Object.defineProperty;var su=Object.getOwnPropertyDescriptor;var lu=Object.getOwnPropertyNames,Rr=Object.getOwnPropertySymbols,fu=Object.getPrototypeOf,Ir=Object.prototype.hasOwnProperty,pu=Object.prototype.propertyIsEnumerable;var Fr=(e,n,r)=>n in e?Se(e,n,{enumerable:!0,configurable:!0,writable:!0,value:r}):e[n]=r,cr=(e,n)=>{for(var r in n||(n={}))Ir.call(n,r)&&Fr(e,r,n[r]);if(Rr)for(var r of Rr(n))pu.call(n,r)&&Fr(e,r,n[r]);return e};var jr=e=>Se(e,"__esModule",{value:!0});var H=(e,n)=>()=>(n||e((n={exports:{}}).exports,n),n.exports),mu=(e,n)=>{jr(e);for(var r in n)Se(e,r,{get:n[r],enumerable:!0})},hu=(e,n,r)=>{if(n&&typeof n=="object"||typeof n=="function")for(let a of lu(n))!Ir.call(e,a)&&a!=="default"&&Se(e,a,{get:()=>n[a],enumerable:!(r=su(n,a))||r.enumerable});return e},Z=e=>hu(jr(Se(e!=null?cu(fu(e)):{},"default",e&&e.__esModule&&"default"in e?{get:()=>e.default,enumerable:!0}:{value:e,enumerable:!0})),e);var ne=(e,n,r)=>new Promise((a,d)=>{var o=_=>{try{m(r.next(_))}catch(x){d(x)}},c=_=>{try{m(r.throw(_))}catch(x){d(x)}},m=_=>_.done?a(_.value):Promise.resolve(_.value).then(o,c);m((r=r.apply(e,n)).next())});var Vr=H((Ur,Ue)=>{(function(){function e(i){"use strict";var t={omitExtraWLInCodeBlocks:{defaultValue:!1,describe:"Omit the default extra whiteline added to code blocks",type:"boolean"},noHeaderId:{defaultValue:!1,describe:"Turn on/off generated header id",type:"boolean"},prefixHeaderId:{defaultValue:!1,describe:"Add a prefix to the generated header ids. Passing a string will prefix that string to the header id. Setting to true will add a generic 'section-' prefix",type:"string"},rawPrefixHeaderId:{defaultValue:!1,describe:'Setting this option to true will prevent showdown from modifying the prefix. This might result in malformed IDs (if, for instance, the " char is used in the prefix)',type:"boolean"},ghCompatibleHeaderId:{defaultValue:!1,describe:"Generate header ids compatible with github style (spaces are replaced with dashes, a bunch of non alphanumeric chars are removed)",type:"boolean"},rawHeaderId:{defaultValue:!1,describe:`Remove only spaces, ' and " from generated header ids (including prefixes), replacing them with dashes (-). WARNING: This might result in malformed ids`,type:"boolean"},headerLevelStart:{defaultValue:!1,describe:"The header blocks level start",type:"integer"},parseImgDimensions:{defaultValue:!1,describe:"Turn on/off image dimension parsing",type:"boolean"},simplifiedAutoLink:{defaultValue:!1,describe:"Turn on/off GFM autolink style",type:"boolean"},excludeTrailingPunctuationFromURLs:{defaultValue:!1,describe:"Excludes trailing punctuation from links generated with autoLinking",type:"boolean"},literalMidWordUnderscores:{defaultValue:!1,describe:"Parse midword underscores as literal underscores",type:"boolean"},literalMidWordAsterisks:{defaultValue:!1,describe:"Parse midword asterisks as literal asterisks",type:"boolean"},strikethrough:{defaultValue:!1,describe:"Turn on/off strikethrough support",type:"boolean"},tables:{defaultValue:!1,describe:"Turn on/off tables support",type:"boolean"},tablesHeaderId:{defaultValue:!1,describe:"Add an id to table headers",type:"boolean"},ghCodeBlocks:{defaultValue:!0,describe:"Turn on/off GFM fenced code blocks support",type:"boolean"},tasklists:{defaultValue:!1,describe:"Turn on/off GFM tasklist support",type:"boolean"},smoothLivePreview:{defaultValue:!1,describe:"Prevents weird effects in live previews due to incomplete input",type:"boolean"},smartIndentationFix:{defaultValue:!1,description:"Tries to smartly fix indentation in es6 strings",type:"boolean"},disableForced4SpacesIndentedSublists:{defaultValue:!1,description:"Disables the requirement of indenting nested sublists by 4 spaces",type:"boolean"},simpleLineBreaks:{defaultValue:!1,description:"Parses simple line breaks as <br> (GFM Style)",type:"boolean"},requireSpaceBeforeHeadingText:{defaultValue:!1,description:"Makes adding a space between `#` and the header text mandatory (GFM Style)",type:"boolean"},ghMentions:{defaultValue:!1,description:"Enables github @mentions",type:"boolean"},ghMentionsLink:{defaultValue:"https://github.com/{u}",description:"Changes the link generated by @mentions. Only applies if ghMentions option is enabled.",type:"string"},encodeEmails:{defaultValue:!0,description:"Encode e-mail addresses through the use of Character Entities, transforming ASCII e-mail addresses into its equivalent decimal entities",type:"boolean"},openLinksInNewWindow:{defaultValue:!1,description:"Open all links in new windows",type:"boolean"},backslashEscapesHTMLTags:{defaultValue:!1,description:"Support for HTML Tag escaping. ex: <div>foo</div>",type:"boolean"},emoji:{defaultValue:!1,description:"Enable emoji support. Ex: `this is a :smile: emoji`",type:"boolean"},underline:{defaultValue:!1,description:"Enable support for underline. Syntax is double or triple underscores: `__underline word__`. With this option enabled, underscores no longer parses into `<em>` and `<strong>`",type:"boolean"},completeHTMLDocument:{defaultValue:!1,description:"Outputs a complete html document, including `<html>`, `<head>` and `<body>` tags",type:"boolean"},metadata:{defaultValue:!1,description:"Enable support for document metadata (defined at the top of the document between `\xAB\xAB\xAB` and `\xBB\xBB\xBB` or between `---` and `---`).",type:"boolean"},splitAdjacentBlockquotes:{defaultValue:!1,description:"Split adjacent blockquote blocks",type:"boolean"}};if(i===!1)return JSON.parse(JSON.stringify(t));var u={};for(var s in t)t.hasOwnProperty(s)&&(u[s]=t[s].defaultValue);return u}function n(){"use strict";var i=e(!0),t={};for(var u in i)i.hasOwnProperty(u)&&(t[u]=!0);return t}var r={},a={},d={},o=e(!0),c="vanilla",m={github:{omitExtraWLInCodeBlocks:!0,simplifiedAutoLink:!0,excludeTrailingPunctuationFromURLs:!0,literalMidWordUnderscores:!0,strikethrough:!0,tables:!0,tablesHeaderId:!0,ghCodeBlocks:!0,tasklists:!0,disableForced4SpacesIndentedSublists:!0,simpleLineBreaks:!0,requireSpaceBeforeHeadingText:!0,ghCompatibleHeaderId:!0,ghMentions:!0,backslashEscapesHTMLTags:!0,emoji:!0,splitAdjacentBlockquotes:!0},original:{noHeaderId:!0,ghCodeBlocks:!1},ghost:{omitExtraWLInCodeBlocks:!0,parseImgDimensions:!0,simplifiedAutoLink:!0,excludeTrailingPunctuationFromURLs:!0,literalMidWordUnderscores:!0,strikethrough:!0,tables:!0,tablesHeaderId:!0,ghCodeBlocks:!0,tasklists:!0,smoothLivePreview:!0,simpleLineBreaks:!0,requireSpaceBeforeHeadingText:!0,ghMentions:!1,encodeEmails:!0},vanilla:e(!0),allOn:n()};r.helper={},r.extensions={},r.setOption=function(i,t){"use strict";return o[i]=t,this},r.getOption=function(i){"use strict";return o[i]},r.getOptions=function(){"use strict";return o},r.resetOptions=function(){"use strict";o=e(!0)},r.setFlavor=function(i){"use strict";if(!m.hasOwnProperty(i))throw Error(i+" flavor was not found");r.resetOptions();var t=m[i];c=i;for(var u in t)t.hasOwnProperty(u)&&(o[u]=t[u])},r.getFlavor=function(){"use strict";return c},r.getFlavorOptions=function(i){"use strict";if(m.hasOwnProperty(i))return m[i]},r.getDefaultOptions=function(i){"use strict";return e(i)},r.subParser=function(i,t){"use strict";if(r.helper.isString(i))if(typeof t!="undefined")a[i]=t;else{if(a.hasOwnProperty(i))return a[i];throw Error("SubParser named "+i+" not registered!")}},r.extension=function(i,t){"use strict";if(!r.helper.isString(i))throw Error("Extension 'name' must be a string");if(i=r.helper.stdExtName(i),r.helper.isUndefined(t)){if(!d.hasOwnProperty(i))throw Error("Extension named "+i+" is not registered!");return d[i]}else{typeof t=="function"&&(t=t()),r.helper.isArray(t)||(t=[t]);var u=_(t,i);if(u.valid)d[i]=t;else throw Error(u.error)}},r.getAllExtensions=function(){"use strict";return d},r.removeExtension=function(i){"use strict";delete d[i]},r.resetExtensions=function(){"use strict";d={}};function _(i,t){"use strict";var u=t?"Error in "+t+" extension->":"Error in unnamed extension",s={valid:!0,error:""};r.helper.isArray(i)||(i=[i]);for(var p=0;p<i.length;++p){var h=u+" sub-extension "+p+": ",f=i[p];if(typeof f!="object")return s.valid=!1,s.error=h+"must be an object, but "+typeof f+" given",s;if(!r.helper.isString(f.type))return s.valid=!1,s.error=h+'property "type" must be a string, but '+typeof f.type+" given",s;var g=f.type=f.type.toLowerCase();if(g==="language"&&(g=f.type="lang"),g==="html"&&(g=f.type="output"),g!=="lang"&&g!=="output"&&g!=="listener")return s.valid=!1,s.error=h+"type "+g+' is not recognized. Valid values: "lang/language", "output/html" or "listener"',s;if(g==="listener"){if(r.helper.isUndefined(f.listeners))return s.valid=!1,s.error=h+'. Extensions of type "listener" must have a property called "listeners"',s}else if(r.helper.isUndefined(f.filter)&&r.helper.isUndefined(f.regex))return s.valid=!1,s.error=h+g+' extensions must define either a "regex" property or a "filter" method',s;if(f.listeners){if(typeof f.listeners!="object")return s.valid=!1,s.error=h+'"listeners" property must be an object but '+typeof f.listeners+" given",s;for(var T in f.listeners)if(f.listeners.hasOwnProperty(T)&&typeof f.listeners[T]!="function")return s.valid=!1,s.error=h+'"listeners" property must be an hash of [event name]: [callback]. listeners.'+T+" must be a function but "+typeof f.listeners[T]+" given",s}if(f.filter){if(typeof f.filter!="function")return s.valid=!1,s.error=h+'"filter" must be a function, but '+typeof f.filter+" given",s}else if(f.regex){if(r.helper.isString(f.regex)&&(f.regex=new RegExp(f.regex,"g")),!(f.regex instanceof RegExp))return s.valid=!1,s.error=h+'"regex" property must either be a string or a RegExp object, but '+typeof f.regex+" given",s;if(r.helper.isUndefined(f.replace))return s.valid=!1,s.error=h+'"regex" extensions must implement a replace string or function',s}}return s}r.validateExtension=function(i){"use strict";var t=_(i,null);return t.valid?!0:(console.warn(t.error),!1)},r.hasOwnProperty("helper")||(r.helper={}),r.helper.isString=function(i){"use strict";return typeof i=="string"||i instanceof String},r.helper.isFunction=function(i){"use strict";var t={};return i&&t.toString.call(i)==="[object Function]"},r.helper.isArray=function(i){"use strict";return Array.isArray(i)},r.helper.isUndefined=function(i){"use strict";return typeof i=="undefined"},r.helper.forEach=function(i,t){"use strict";if(r.helper.isUndefined(i))throw new Error("obj param is required");if(r.helper.isUndefined(t))throw new Error("callback param is required");if(!r.helper.isFunction(t))throw new Error("callback param must be a function/closure");if(typeof i.forEach=="function")i.forEach(t);else if(r.helper.isArray(i))for(var u=0;u<i.length;u++)t(i[u],u,i);else if(typeof i=="object")for(var s in i)i.hasOwnProperty(s)&&t(i[s],s,i);else throw new Error("obj does not seem to be an array or an iterable object")},r.helper.stdExtName=function(i){"use strict";return i.replace(/[_?*+\/\\.^-]/g,"").replace(/\s/g,"").toLowerCase()};function x(i,t){"use strict";var u=t.charCodeAt(0);return"\xA8E"+u+"E"}r.helper.escapeCharactersCallback=x,r.helper.escapeCharacters=function(i,t,u){"use strict";var s="(["+t.replace(/([\[\]\\])/g,"\\$1")+"])";u&&(s="\\\\"+s);var p=new RegExp(s,"g");return i=i.replace(p,x),i},r.helper.unescapeHTMLEntities=function(i){"use strict";return i.replace(/&quot;/g,'"').replace(/&lt;/g,"<").replace(/&gt;/g,">").replace(/&amp;/g,"&")};var S=function(i,t,u,s){"use strict";var p=s||"",h=p.indexOf("g")>-1,f=new RegExp(t+"|"+u,"g"+p.replace(/g/g,"")),g=new RegExp(t,p.replace(/g/g,"")),T=[],b,w,y,l,k;do for(b=0;y=f.exec(i);)if(g.test(y[0]))b++||(w=f.lastIndex,l=w-y[0].length);else if(b&&!--b){k=y.index+y[0].length;var C={left:{start:l,end:w},match:{start:w,end:y.index},right:{start:y.index,end:k},wholeMatch:{start:l,end:k}};if(T.push(C),!h)return T}while(b&&(f.lastIndex=w));return T};r.helper.matchRecursiveRegExp=function(i,t,u,s){"use strict";for(var p=S(i,t,u,s),h=[],f=0;f<p.length;++f)h.push([i.slice(p[f].wholeMatch.start,p[f].wholeMatch.end),i.slice(p[f].match.start,p[f].match.end),i.slice(p[f].left.start,p[f].left.end),i.slice(p[f].right.start,p[f].right.end)]);return h},r.helper.replaceRecursiveRegExp=function(i,t,u,s,p){"use strict";if(!r.helper.isFunction(t)){var h=t;t=function(){return h}}var f=S(i,u,s,p),g=i,T=f.length;if(T>0){var b=[];f[0].wholeMatch.start!==0&&b.push(i.slice(0,f[0].wholeMatch.start));for(var w=0;w<T;++w)b.push(t(i.slice(f[w].wholeMatch.start,f[w].wholeMatch.end),i.slice(f[w].match.start,f[w].match.end),i.slice(f[w].left.start,f[w].left.end),i.slice(f[w].right.start,f[w].right.end))),w<T-1&&b.push(i.slice(f[w].wholeMatch.end,f[w+1].wholeMatch.start));f[T-1].wholeMatch.end<i.length&&b.push(i.slice(f[T-1].wholeMatch.end)),g=b.join("")}return g},r.helper.regexIndexOf=function(i,t,u){"use strict";if(!r.helper.isString(i))throw"InvalidArgumentError: first parameter of showdown.helper.regexIndexOf function must be a string";if(!(t instanceof RegExp))throw"InvalidArgumentError: second parameter of showdown.helper.regexIndexOf function must be an instance of RegExp";var s=i.substring(u||0).search(t);return s>=0?s+(u||0):s},r.helper.splitAtIndex=function(i,t){"use strict";if(!r.helper.isString(i))throw"InvalidArgumentError: first parameter of showdown.helper.regexIndexOf function must be a string";return[i.substring(0,t),i.substring(t)]},r.helper.encodeEmailAddress=function(i){"use strict";var t=[function(u){return"&#"+u.charCodeAt(0)+";"},function(u){return"&#x"+u.charCodeAt(0).toString(16)+";"},function(u){return u}];return i=i.replace(/./g,function(u){if(u==="@")u=t[Math.floor(Math.random()*2)](u);else{var s=Math.random();u=s>.9?t[2](u):s>.45?t[1](u):t[0](u)}return u}),i},r.helper.padEnd=function(t,u,s){"use strict";return u=u>>0,s=String(s||" "),t.length>u?String(t):(u=u-t.length,u>s.length&&(s+=s.repeat(u/s.length)),String(t)+s.slice(0,u))},typeof console=="undefined"&&(console={warn:function(i){"use strict";alert(i)},log:function(i){"use strict";alert(i)},error:function(i){"use strict";throw i}}),r.helper.regexes={asteriskDashAndColon:/([*_:~])/g},r.helper.emojis={"+1":"\u{1F44D}","-1":"\u{1F44E}","100":"\u{1F4AF}","1234":"\u{1F522}","1st_place_medal":"\u{1F947}","2nd_place_medal":"\u{1F948}","3rd_place_medal":"\u{1F949}","8ball":"\u{1F3B1}",a:"\u{1F170}\uFE0F",ab:"\u{1F18E}",abc:"\u{1F524}",abcd:"\u{1F521}",accept:"\u{1F251}",aerial_tramway:"\u{1F6A1}",airplane:"\u2708\uFE0F",alarm_clock:"\u23F0",alembic:"\u2697\uFE0F",alien:"\u{1F47D}",ambulance:"\u{1F691}",amphora:"\u{1F3FA}",anchor:"\u2693\uFE0F",angel:"\u{1F47C}",anger:"\u{1F4A2}",angry:"\u{1F620}",anguished:"\u{1F627}",ant:"\u{1F41C}",apple:"\u{1F34E}",aquarius:"\u2652\uFE0F",aries:"\u2648\uFE0F",arrow_backward:"\u25C0\uFE0F",arrow_double_down:"\u23EC",arrow_double_up:"\u23EB",arrow_down:"\u2B07\uFE0F",arrow_down_small:"\u{1F53D}",arrow_forward:"\u25B6\uFE0F",arrow_heading_down:"\u2935\uFE0F",arrow_heading_up:"\u2934\uFE0F",arrow_left:"\u2B05\uFE0F",arrow_lower_left:"\u2199\uFE0F",arrow_lower_right:"\u2198\uFE0F",arrow_right:"\u27A1\uFE0F",arrow_right_hook:"\u21AA\uFE0F",arrow_up:"\u2B06\uFE0F",arrow_up_down:"\u2195\uFE0F",arrow_up_small:"\u{1F53C}",arrow_upper_left:"\u2196\uFE0F",arrow_upper_right:"\u2197\uFE0F",arrows_clockwise:"\u{1F503}",arrows_counterclockwise:"\u{1F504}",art:"\u{1F3A8}",articulated_lorry:"\u{1F69B}",artificial_satellite:"\u{1F6F0}",astonished:"\u{1F632}",athletic_shoe:"\u{1F45F}",atm:"\u{1F3E7}",atom_symbol:"\u269B\uFE0F",avocado:"\u{1F951}",b:"\u{1F171}\uFE0F",baby:"\u{1F476}",baby_bottle:"\u{1F37C}",baby_chick:"\u{1F424}",baby_symbol:"\u{1F6BC}",back:"\u{1F519}",bacon:"\u{1F953}",badminton:"\u{1F3F8}",baggage_claim:"\u{1F6C4}",baguette_bread:"\u{1F956}",balance_scale:"\u2696\uFE0F",balloon:"\u{1F388}",ballot_box:"\u{1F5F3}",ballot_box_with_check:"\u2611\uFE0F",bamboo:"\u{1F38D}",banana:"\u{1F34C}",bangbang:"\u203C\uFE0F",bank:"\u{1F3E6}",bar_chart:"\u{1F4CA}",barber:"\u{1F488}",baseball:"\u26BE\uFE0F",basketball:"\u{1F3C0}",basketball_man:"\u26F9\uFE0F",basketball_woman:"\u26F9\uFE0F&zwj;\u2640\uFE0F",bat:"\u{1F987}",bath:"\u{1F6C0}",bathtub:"\u{1F6C1}",battery:"\u{1F50B}",beach_umbrella:"\u{1F3D6}",bear:"\u{1F43B}",bed:"\u{1F6CF}",bee:"\u{1F41D}",beer:"\u{1F37A}",beers:"\u{1F37B}",beetle:"\u{1F41E}",beginner:"\u{1F530}",bell:"\u{1F514}",bellhop_bell:"\u{1F6CE}",bento:"\u{1F371}",biking_man:"\u{1F6B4}",bike:"\u{1F6B2}",biking_woman:"\u{1F6B4}&zwj;\u2640\uFE0F",bikini:"\u{1F459}",biohazard:"\u2623\uFE0F",bird:"\u{1F426}",birthday:"\u{1F382}",black_circle:"\u26AB\uFE0F",black_flag:"\u{1F3F4}",black_heart:"\u{1F5A4}",black_joker:"\u{1F0CF}",black_large_square:"\u2B1B\uFE0F",black_medium_small_square:"\u25FE\uFE0F",black_medium_square:"\u25FC\uFE0F",black_nib:"\u2712\uFE0F",black_small_square:"\u25AA\uFE0F",black_square_button:"\u{1F532}",blonde_man:"\u{1F471}",blonde_woman:"\u{1F471}&zwj;\u2640\uFE0F",blossom:"\u{1F33C}",blowfish:"\u{1F421}",blue_book:"\u{1F4D8}",blue_car:"\u{1F699}",blue_heart:"\u{1F499}",blush:"\u{1F60A}",boar:"\u{1F417}",boat:"\u26F5\uFE0F",bomb:"\u{1F4A3}",book:"\u{1F4D6}",bookmark:"\u{1F516}",bookmark_tabs:"\u{1F4D1}",books:"\u{1F4DA}",boom:"\u{1F4A5}",boot:"\u{1F462}",bouquet:"\u{1F490}",bowing_man:"\u{1F647}",bow_and_arrow:"\u{1F3F9}",bowing_woman:"\u{1F647}&zwj;\u2640\uFE0F",bowling:"\u{1F3B3}",boxing_glove:"\u{1F94A}",boy:"\u{1F466}",bread:"\u{1F35E}",bride_with_veil:"\u{1F470}",bridge_at_night:"\u{1F309}",briefcase:"\u{1F4BC}",broken_heart:"\u{1F494}",bug:"\u{1F41B}",building_construction:"\u{1F3D7}",bulb:"\u{1F4A1}",bullettrain_front:"\u{1F685}",bullettrain_side:"\u{1F684}",burrito:"\u{1F32F}",bus:"\u{1F68C}",business_suit_levitating:"\u{1F574}",busstop:"\u{1F68F}",bust_in_silhouette:"\u{1F464}",busts_in_silhouette:"\u{1F465}",butterfly:"\u{1F98B}",cactus:"\u{1F335}",cake:"\u{1F370}",calendar:"\u{1F4C6}",call_me_hand:"\u{1F919}",calling:"\u{1F4F2}",camel:"\u{1F42B}",camera:"\u{1F4F7}",camera_flash:"\u{1F4F8}",camping:"\u{1F3D5}",cancer:"\u264B\uFE0F",candle:"\u{1F56F}",candy:"\u{1F36C}",canoe:"\u{1F6F6}",capital_abcd:"\u{1F520}",capricorn:"\u2651\uFE0F",car:"\u{1F697}",card_file_box:"\u{1F5C3}",card_index:"\u{1F4C7}",card_index_dividers:"\u{1F5C2}",carousel_horse:"\u{1F3A0}",carrot:"\u{1F955}",cat:"\u{1F431}",cat2:"\u{1F408}",cd:"\u{1F4BF}",chains:"\u26D3",champagne:"\u{1F37E}",chart:"\u{1F4B9}",chart_with_downwards_trend:"\u{1F4C9}",chart_with_upwards_trend:"\u{1F4C8}",checkered_flag:"\u{1F3C1}",cheese:"\u{1F9C0}",cherries:"\u{1F352}",cherry_blossom:"\u{1F338}",chestnut:"\u{1F330}",chicken:"\u{1F414}",children_crossing:"\u{1F6B8}",chipmunk:"\u{1F43F}",chocolate_bar:"\u{1F36B}",christmas_tree:"\u{1F384}",church:"\u26EA\uFE0F",cinema:"\u{1F3A6}",circus_tent:"\u{1F3AA}",city_sunrise:"\u{1F307}",city_sunset:"\u{1F306}",cityscape:"\u{1F3D9}",cl:"\u{1F191}",clamp:"\u{1F5DC}",clap:"\u{1F44F}",clapper:"\u{1F3AC}",classical_building:"\u{1F3DB}",clinking_glasses:"\u{1F942}",clipboard:"\u{1F4CB}",clock1:"\u{1F550}",clock10:"\u{1F559}",clock1030:"\u{1F565}",clock11:"\u{1F55A}",clock1130:"\u{1F566}",clock12:"\u{1F55B}",clock1230:"\u{1F567}",clock130:"\u{1F55C}",clock2:"\u{1F551}",clock230:"\u{1F55D}",clock3:"\u{1F552}",clock330:"\u{1F55E}",clock4:"\u{1F553}",clock430:"\u{1F55F}",clock5:"\u{1F554}",clock530:"\u{1F560}",clock6:"\u{1F555}",clock630:"\u{1F561}",clock7:"\u{1F556}",clock730:"\u{1F562}",clock8:"\u{1F557}",clock830:"\u{1F563}",clock9:"\u{1F558}",clock930:"\u{1F564}",closed_book:"\u{1F4D5}",closed_lock_with_key:"\u{1F510}",closed_umbrella:"\u{1F302}",cloud:"\u2601\uFE0F",cloud_with_lightning:"\u{1F329}",cloud_with_lightning_and_rain:"\u26C8",cloud_with_rain:"\u{1F327}",cloud_with_snow:"\u{1F328}",clown_face:"\u{1F921}",clubs:"\u2663\uFE0F",cocktail:"\u{1F378}",coffee:"\u2615\uFE0F",coffin:"\u26B0\uFE0F",cold_sweat:"\u{1F630}",comet:"\u2604\uFE0F",computer:"\u{1F4BB}",computer_mouse:"\u{1F5B1}",confetti_ball:"\u{1F38A}",confounded:"\u{1F616}",confused:"\u{1F615}",congratulations:"\u3297\uFE0F",construction:"\u{1F6A7}",construction_worker_man:"\u{1F477}",construction_worker_woman:"\u{1F477}&zwj;\u2640\uFE0F",control_knobs:"\u{1F39B}",convenience_store:"\u{1F3EA}",cookie:"\u{1F36A}",cool:"\u{1F192}",policeman:"\u{1F46E}",copyright:"\xA9\uFE0F",corn:"\u{1F33D}",couch_and_lamp:"\u{1F6CB}",couple:"\u{1F46B}",couple_with_heart_woman_man:"\u{1F491}",couple_with_heart_man_man:"\u{1F468}&zwj;\u2764\uFE0F&zwj;\u{1F468}",couple_with_heart_woman_woman:"\u{1F469}&zwj;\u2764\uFE0F&zwj;\u{1F469}",couplekiss_man_man:"\u{1F468}&zwj;\u2764\uFE0F&zwj;\u{1F48B}&zwj;\u{1F468}",couplekiss_man_woman:"\u{1F48F}",couplekiss_woman_woman:"\u{1F469}&zwj;\u2764\uFE0F&zwj;\u{1F48B}&zwj;\u{1F469}",cow:"\u{1F42E}",cow2:"\u{1F404}",cowboy_hat_face:"\u{1F920}",crab:"\u{1F980}",crayon:"\u{1F58D}",credit_card:"\u{1F4B3}",crescent_moon:"\u{1F319}",cricket:"\u{1F3CF}",crocodile:"\u{1F40A}",croissant:"\u{1F950}",crossed_fingers:"\u{1F91E}",crossed_flags:"\u{1F38C}",crossed_swords:"\u2694\uFE0F",crown:"\u{1F451}",cry:"\u{1F622}",crying_cat_face:"\u{1F63F}",crystal_ball:"\u{1F52E}",cucumber:"\u{1F952}",cupid:"\u{1F498}",curly_loop:"\u27B0",currency_exchange:"\u{1F4B1}",curry:"\u{1F35B}",custard:"\u{1F36E}",customs:"\u{1F6C3}",cyclone:"\u{1F300}",dagger:"\u{1F5E1}",dancer:"\u{1F483}",dancing_women:"\u{1F46F}",dancing_men:"\u{1F46F}&zwj;\u2642\uFE0F",dango:"\u{1F361}",dark_sunglasses:"\u{1F576}",dart:"\u{1F3AF}",dash:"\u{1F4A8}",date:"\u{1F4C5}",deciduous_tree:"\u{1F333}",deer:"\u{1F98C}",department_store:"\u{1F3EC}",derelict_house:"\u{1F3DA}",desert:"\u{1F3DC}",desert_island:"\u{1F3DD}",desktop_computer:"\u{1F5A5}",male_detective:"\u{1F575}\uFE0F",diamond_shape_with_a_dot_inside:"\u{1F4A0}",diamonds:"\u2666\uFE0F",disappointed:"\u{1F61E}",disappointed_relieved:"\u{1F625}",dizzy:"\u{1F4AB}",dizzy_face:"\u{1F635}",do_not_litter:"\u{1F6AF}",dog:"\u{1F436}",dog2:"\u{1F415}",dollar:"\u{1F4B5}",dolls:"\u{1F38E}",dolphin:"\u{1F42C}",door:"\u{1F6AA}",doughnut:"\u{1F369}",dove:"\u{1F54A}",dragon:"\u{1F409}",dragon_face:"\u{1F432}",dress:"\u{1F457}",dromedary_camel:"\u{1F42A}",drooling_face:"\u{1F924}",droplet:"\u{1F4A7}",drum:"\u{1F941}",duck:"\u{1F986}",dvd:"\u{1F4C0}","e-mail":"\u{1F4E7}",eagle:"\u{1F985}",ear:"\u{1F442}",ear_of_rice:"\u{1F33E}",earth_africa:"\u{1F30D}",earth_americas:"\u{1F30E}",earth_asia:"\u{1F30F}",egg:"\u{1F95A}",eggplant:"\u{1F346}",eight_pointed_black_star:"\u2734\uFE0F",eight_spoked_asterisk:"\u2733\uFE0F",electric_plug:"\u{1F50C}",elephant:"\u{1F418}",email:"\u2709\uFE0F",end:"\u{1F51A}",envelope_with_arrow:"\u{1F4E9}",euro:"\u{1F4B6}",european_castle:"\u{1F3F0}",european_post_office:"\u{1F3E4}",evergreen_tree:"\u{1F332}",exclamation:"\u2757\uFE0F",expressionless:"\u{1F611}",eye:"\u{1F441}",eye_speech_bubble:"\u{1F441}&zwj;\u{1F5E8}",eyeglasses:"\u{1F453}",eyes:"\u{1F440}",face_with_head_bandage:"\u{1F915}",face_with_thermometer:"\u{1F912}",fist_oncoming:"\u{1F44A}",factory:"\u{1F3ED}",fallen_leaf:"\u{1F342}",family_man_woman_boy:"\u{1F46A}",family_man_boy:"\u{1F468}&zwj;\u{1F466}",family_man_boy_boy:"\u{1F468}&zwj;\u{1F466}&zwj;\u{1F466}",family_man_girl:"\u{1F468}&zwj;\u{1F467}",family_man_girl_boy:"\u{1F468}&zwj;\u{1F467}&zwj;\u{1F466}",family_man_girl_girl:"\u{1F468}&zwj;\u{1F467}&zwj;\u{1F467}",family_man_man_boy:"\u{1F468}&zwj;\u{1F468}&zwj;\u{1F466}",family_man_man_boy_boy:"\u{1F468}&zwj;\u{1F468}&zwj;\u{1F466}&zwj;\u{1F466}",family_man_man_girl:"\u{1F468}&zwj;\u{1F468}&zwj;\u{1F467}",family_man_man_girl_boy:"\u{1F468}&zwj;\u{1F468}&zwj;\u{1F467}&zwj;\u{1F466}",family_man_man_girl_girl:"\u{1F468}&zwj;\u{1F468}&zwj;\u{1F467}&zwj;\u{1F467}",family_man_woman_boy_boy:"\u{1F468}&zwj;\u{1F469}&zwj;\u{1F466}&zwj;\u{1F466}",family_man_woman_girl:"\u{1F468}&zwj;\u{1F469}&zwj;\u{1F467}",family_man_woman_girl_boy:"\u{1F468}&zwj;\u{1F469}&zwj;\u{1F467}&zwj;\u{1F466}",family_man_woman_girl_girl:"\u{1F468}&zwj;\u{1F469}&zwj;\u{1F467}&zwj;\u{1F467}",family_woman_boy:"\u{1F469}&zwj;\u{1F466}",family_woman_boy_boy:"\u{1F469}&zwj;\u{1F466}&zwj;\u{1F466}",family_woman_girl:"\u{1F469}&zwj;\u{1F467}",family_woman_girl_boy:"\u{1F469}&zwj;\u{1F467}&zwj;\u{1F466}",family_woman_girl_girl:"\u{1F469}&zwj;\u{1F467}&zwj;\u{1F467}",family_woman_woman_boy:"\u{1F469}&zwj;\u{1F469}&zwj;\u{1F466}",family_woman_woman_boy_boy:"\u{1F469}&zwj;\u{1F469}&zwj;\u{1F466}&zwj;\u{1F466}",family_woman_woman_girl:"\u{1F469}&zwj;\u{1F469}&zwj;\u{1F467}",family_woman_woman_girl_boy:"\u{1F469}&zwj;\u{1F469}&zwj;\u{1F467}&zwj;\u{1F466}",family_woman_woman_girl_girl:"\u{1F469}&zwj;\u{1F469}&zwj;\u{1F467}&zwj;\u{1F467}",fast_forward:"\u23E9",fax:"\u{1F4E0}",fearful:"\u{1F628}",feet:"\u{1F43E}",female_detective:"\u{1F575}\uFE0F&zwj;\u2640\uFE0F",ferris_wheel:"\u{1F3A1}",ferry:"\u26F4",field_hockey:"\u{1F3D1}",file_cabinet:"\u{1F5C4}",file_folder:"\u{1F4C1}",film_projector:"\u{1F4FD}",film_strip:"\u{1F39E}",fire:"\u{1F525}",fire_engine:"\u{1F692}",fireworks:"\u{1F386}",first_quarter_moon:"\u{1F313}",first_quarter_moon_with_face:"\u{1F31B}",fish:"\u{1F41F}",fish_cake:"\u{1F365}",fishing_pole_and_fish:"\u{1F3A3}",fist_raised:"\u270A",fist_left:"\u{1F91B}",fist_right:"\u{1F91C}",flags:"\u{1F38F}",flashlight:"\u{1F526}",fleur_de_lis:"\u269C\uFE0F",flight_arrival:"\u{1F6EC}",flight_departure:"\u{1F6EB}",floppy_disk:"\u{1F4BE}",flower_playing_cards:"\u{1F3B4}",flushed:"\u{1F633}",fog:"\u{1F32B}",foggy:"\u{1F301}",football:"\u{1F3C8}",footprints:"\u{1F463}",fork_and_knife:"\u{1F374}",fountain:"\u26F2\uFE0F",fountain_pen:"\u{1F58B}",four_leaf_clover:"\u{1F340}",fox_face:"\u{1F98A}",framed_picture:"\u{1F5BC}",free:"\u{1F193}",fried_egg:"\u{1F373}",fried_shrimp:"\u{1F364}",fries:"\u{1F35F}",frog:"\u{1F438}",frowning:"\u{1F626}",frowning_face:"\u2639\uFE0F",frowning_man:"\u{1F64D}&zwj;\u2642\uFE0F",frowning_woman:"\u{1F64D}",middle_finger:"\u{1F595}",fuelpump:"\u26FD\uFE0F",full_moon:"\u{1F315}",full_moon_with_face:"\u{1F31D}",funeral_urn:"\u26B1\uFE0F",game_die:"\u{1F3B2}",gear:"\u2699\uFE0F",gem:"\u{1F48E}",gemini:"\u264A\uFE0F",ghost:"\u{1F47B}",gift:"\u{1F381}",gift_heart:"\u{1F49D}",girl:"\u{1F467}",globe_with_meridians:"\u{1F310}",goal_net:"\u{1F945}",goat:"\u{1F410}",golf:"\u26F3\uFE0F",golfing_man:"\u{1F3CC}\uFE0F",golfing_woman:"\u{1F3CC}\uFE0F&zwj;\u2640\uFE0F",gorilla:"\u{1F98D}",grapes:"\u{1F347}",green_apple:"\u{1F34F}",green_book:"\u{1F4D7}",green_heart:"\u{1F49A}",green_salad:"\u{1F957}",grey_exclamation:"\u2755",grey_question:"\u2754",grimacing:"\u{1F62C}",grin:"\u{1F601}",grinning:"\u{1F600}",guardsman:"\u{1F482}",guardswoman:"\u{1F482}&zwj;\u2640\uFE0F",guitar:"\u{1F3B8}",gun:"\u{1F52B}",haircut_woman:"\u{1F487}",haircut_man:"\u{1F487}&zwj;\u2642\uFE0F",hamburger:"\u{1F354}",hammer:"\u{1F528}",hammer_and_pick:"\u2692",hammer_and_wrench:"\u{1F6E0}",hamster:"\u{1F439}",hand:"\u270B",handbag:"\u{1F45C}",handshake:"\u{1F91D}",hankey:"\u{1F4A9}",hatched_chick:"\u{1F425}",hatching_chick:"\u{1F423}",headphones:"\u{1F3A7}",hear_no_evil:"\u{1F649}",heart:"\u2764\uFE0F",heart_decoration:"\u{1F49F}",heart_eyes:"\u{1F60D}",heart_eyes_cat:"\u{1F63B}",heartbeat:"\u{1F493}",heartpulse:"\u{1F497}",hearts:"\u2665\uFE0F",heavy_check_mark:"\u2714\uFE0F",heavy_division_sign:"\u2797",heavy_dollar_sign:"\u{1F4B2}",heavy_heart_exclamation:"\u2763\uFE0F",heavy_minus_sign:"\u2796",heavy_multiplication_x:"\u2716\uFE0F",heavy_plus_sign:"\u2795",helicopter:"\u{1F681}",herb:"\u{1F33F}",hibiscus:"\u{1F33A}",high_brightness:"\u{1F506}",high_heel:"\u{1F460}",hocho:"\u{1F52A}",hole:"\u{1F573}",honey_pot:"\u{1F36F}",horse:"\u{1F434}",horse_racing:"\u{1F3C7}",hospital:"\u{1F3E5}",hot_pepper:"\u{1F336}",hotdog:"\u{1F32D}",hotel:"\u{1F3E8}",hotsprings:"\u2668\uFE0F",hourglass:"\u231B\uFE0F",hourglass_flowing_sand:"\u23F3",house:"\u{1F3E0}",house_with_garden:"\u{1F3E1}",houses:"\u{1F3D8}",hugs:"\u{1F917}",hushed:"\u{1F62F}",ice_cream:"\u{1F368}",ice_hockey:"\u{1F3D2}",ice_skate:"\u26F8",icecream:"\u{1F366}",id:"\u{1F194}",ideograph_advantage:"\u{1F250}",imp:"\u{1F47F}",inbox_tray:"\u{1F4E5}",incoming_envelope:"\u{1F4E8}",tipping_hand_woman:"\u{1F481}",information_source:"\u2139\uFE0F",innocent:"\u{1F607}",interrobang:"\u2049\uFE0F",iphone:"\u{1F4F1}",izakaya_lantern:"\u{1F3EE}",jack_o_lantern:"\u{1F383}",japan:"\u{1F5FE}",japanese_castle:"\u{1F3EF}",japanese_goblin:"\u{1F47A}",japanese_ogre:"\u{1F479}",jeans:"\u{1F456}",joy:"\u{1F602}",joy_cat:"\u{1F639}",joystick:"\u{1F579}",kaaba:"\u{1F54B}",key:"\u{1F511}",keyboard:"\u2328\uFE0F",keycap_ten:"\u{1F51F}",kick_scooter:"\u{1F6F4}",kimono:"\u{1F458}",kiss:"\u{1F48B}",kissing:"\u{1F617}",kissing_cat:"\u{1F63D}",kissing_closed_eyes:"\u{1F61A}",kissing_heart:"\u{1F618}",kissing_smiling_eyes:"\u{1F619}",kiwi_fruit:"\u{1F95D}",koala:"\u{1F428}",koko:"\u{1F201}",label:"\u{1F3F7}",large_blue_circle:"\u{1F535}",large_blue_diamond:"\u{1F537}",large_orange_diamond:"\u{1F536}",last_quarter_moon:"\u{1F317}",last_quarter_moon_with_face:"\u{1F31C}",latin_cross:"\u271D\uFE0F",laughing:"\u{1F606}",leaves:"\u{1F343}",ledger:"\u{1F4D2}",left_luggage:"\u{1F6C5}",left_right_arrow:"\u2194\uFE0F",leftwards_arrow_with_hook:"\u21A9\uFE0F",lemon:"\u{1F34B}",leo:"\u264C\uFE0F",leopard:"\u{1F406}",level_slider:"\u{1F39A}",libra:"\u264E\uFE0F",light_rail:"\u{1F688}",link:"\u{1F517}",lion:"\u{1F981}",lips:"\u{1F444}",lipstick:"\u{1F484}",lizard:"\u{1F98E}",lock:"\u{1F512}",lock_with_ink_pen:"\u{1F50F}",lollipop:"\u{1F36D}",loop:"\u27BF",loud_sound:"\u{1F50A}",loudspeaker:"\u{1F4E2}",love_hotel:"\u{1F3E9}",love_letter:"\u{1F48C}",low_brightness:"\u{1F505}",lying_face:"\u{1F925}",m:"\u24C2\uFE0F",mag:"\u{1F50D}",mag_right:"\u{1F50E}",mahjong:"\u{1F004}\uFE0F",mailbox:"\u{1F4EB}",mailbox_closed:"\u{1F4EA}",mailbox_with_mail:"\u{1F4EC}",mailbox_with_no_mail:"\u{1F4ED}",man:"\u{1F468}",man_artist:"\u{1F468}&zwj;\u{1F3A8}",man_astronaut:"\u{1F468}&zwj;\u{1F680}",man_cartwheeling:"\u{1F938}&zwj;\u2642\uFE0F",man_cook:"\u{1F468}&zwj;\u{1F373}",man_dancing:"\u{1F57A}",man_facepalming:"\u{1F926}&zwj;\u2642\uFE0F",man_factory_worker:"\u{1F468}&zwj;\u{1F3ED}",man_farmer:"\u{1F468}&zwj;\u{1F33E}",man_firefighter:"\u{1F468}&zwj;\u{1F692}",man_health_worker:"\u{1F468}&zwj;\u2695\uFE0F",man_in_tuxedo:"\u{1F935}",man_judge:"\u{1F468}&zwj;\u2696\uFE0F",man_juggling:"\u{1F939}&zwj;\u2642\uFE0F",man_mechanic:"\u{1F468}&zwj;\u{1F527}",man_office_worker:"\u{1F468}&zwj;\u{1F4BC}",man_pilot:"\u{1F468}&zwj;\u2708\uFE0F",man_playing_handball:"\u{1F93E}&zwj;\u2642\uFE0F",man_playing_water_polo:"\u{1F93D}&zwj;\u2642\uFE0F",man_scientist:"\u{1F468}&zwj;\u{1F52C}",man_shrugging:"\u{1F937}&zwj;\u2642\uFE0F",man_singer:"\u{1F468}&zwj;\u{1F3A4}",man_student:"\u{1F468}&zwj;\u{1F393}",man_teacher:"\u{1F468}&zwj;\u{1F3EB}",man_technologist:"\u{1F468}&zwj;\u{1F4BB}",man_with_gua_pi_mao:"\u{1F472}",man_with_turban:"\u{1F473}",tangerine:"\u{1F34A}",mans_shoe:"\u{1F45E}",mantelpiece_clock:"\u{1F570}",maple_leaf:"\u{1F341}",martial_arts_uniform:"\u{1F94B}",mask:"\u{1F637}",massage_woman:"\u{1F486}",massage_man:"\u{1F486}&zwj;\u2642\uFE0F",meat_on_bone:"\u{1F356}",medal_military:"\u{1F396}",medal_sports:"\u{1F3C5}",mega:"\u{1F4E3}",melon:"\u{1F348}",memo:"\u{1F4DD}",men_wrestling:"\u{1F93C}&zwj;\u2642\uFE0F",menorah:"\u{1F54E}",mens:"\u{1F6B9}",metal:"\u{1F918}",metro:"\u{1F687}",microphone:"\u{1F3A4}",microscope:"\u{1F52C}",milk_glass:"\u{1F95B}",milky_way:"\u{1F30C}",minibus:"\u{1F690}",minidisc:"\u{1F4BD}",mobile_phone_off:"\u{1F4F4}",money_mouth_face:"\u{1F911}",money_with_wings:"\u{1F4B8}",moneybag:"\u{1F4B0}",monkey:"\u{1F412}",monkey_face:"\u{1F435}",monorail:"\u{1F69D}",moon:"\u{1F314}",mortar_board:"\u{1F393}",mosque:"\u{1F54C}",motor_boat:"\u{1F6E5}",motor_scooter:"\u{1F6F5}",motorcycle:"\u{1F3CD}",motorway:"\u{1F6E3}",mount_fuji:"\u{1F5FB}",mountain:"\u26F0",mountain_biking_man:"\u{1F6B5}",mountain_biking_woman:"\u{1F6B5}&zwj;\u2640\uFE0F",mountain_cableway:"\u{1F6A0}",mountain_railway:"\u{1F69E}",mountain_snow:"\u{1F3D4}",mouse:"\u{1F42D}",mouse2:"\u{1F401}",movie_camera:"\u{1F3A5}",moyai:"\u{1F5FF}",mrs_claus:"\u{1F936}",muscle:"\u{1F4AA}",mushroom:"\u{1F344}",musical_keyboard:"\u{1F3B9}",musical_note:"\u{1F3B5}",musical_score:"\u{1F3BC}",mute:"\u{1F507}",nail_care:"\u{1F485}",name_badge:"\u{1F4DB}",national_park:"\u{1F3DE}",nauseated_face:"\u{1F922}",necktie:"\u{1F454}",negative_squared_cross_mark:"\u274E",nerd_face:"\u{1F913}",neutral_face:"\u{1F610}",new:"\u{1F195}",new_moon:"\u{1F311}",new_moon_with_face:"\u{1F31A}",newspaper:"\u{1F4F0}",newspaper_roll:"\u{1F5DE}",next_track_button:"\u23ED",ng:"\u{1F196}",no_good_man:"\u{1F645}&zwj;\u2642\uFE0F",no_good_woman:"\u{1F645}",night_with_stars:"\u{1F303}",no_bell:"\u{1F515}",no_bicycles:"\u{1F6B3}",no_entry:"\u26D4\uFE0F",no_entry_sign:"\u{1F6AB}",no_mobile_phones:"\u{1F4F5}",no_mouth:"\u{1F636}",no_pedestrians:"\u{1F6B7}",no_smoking:"\u{1F6AD}","non-potable_water":"\u{1F6B1}",nose:"\u{1F443}",notebook:"\u{1F4D3}",notebook_with_decorative_cover:"\u{1F4D4}",notes:"\u{1F3B6}",nut_and_bolt:"\u{1F529}",o:"\u2B55\uFE0F",o2:"\u{1F17E}\uFE0F",ocean:"\u{1F30A}",octopus:"\u{1F419}",oden:"\u{1F362}",office:"\u{1F3E2}",oil_drum:"\u{1F6E2}",ok:"\u{1F197}",ok_hand:"\u{1F44C}",ok_man:"\u{1F646}&zwj;\u2642\uFE0F",ok_woman:"\u{1F646}",old_key:"\u{1F5DD}",older_man:"\u{1F474}",older_woman:"\u{1F475}",om:"\u{1F549}",on:"\u{1F51B}",oncoming_automobile:"\u{1F698}",oncoming_bus:"\u{1F68D}",oncoming_police_car:"\u{1F694}",oncoming_taxi:"\u{1F696}",open_file_folder:"\u{1F4C2}",open_hands:"\u{1F450}",open_mouth:"\u{1F62E}",open_umbrella:"\u2602\uFE0F",ophiuchus:"\u26CE",orange_book:"\u{1F4D9}",orthodox_cross:"\u2626\uFE0F",outbox_tray:"\u{1F4E4}",owl:"\u{1F989}",ox:"\u{1F402}",package:"\u{1F4E6}",page_facing_up:"\u{1F4C4}",page_with_curl:"\u{1F4C3}",pager:"\u{1F4DF}",paintbrush:"\u{1F58C}",palm_tree:"\u{1F334}",pancakes:"\u{1F95E}",panda_face:"\u{1F43C}",paperclip:"\u{1F4CE}",paperclips:"\u{1F587}",parasol_on_ground:"\u26F1",parking:"\u{1F17F}\uFE0F",part_alternation_mark:"\u303D\uFE0F",partly_sunny:"\u26C5\uFE0F",passenger_ship:"\u{1F6F3}",passport_control:"\u{1F6C2}",pause_button:"\u23F8",peace_symbol:"\u262E\uFE0F",peach:"\u{1F351}",peanuts:"\u{1F95C}",pear:"\u{1F350}",pen:"\u{1F58A}",pencil2:"\u270F\uFE0F",penguin:"\u{1F427}",pensive:"\u{1F614}",performing_arts:"\u{1F3AD}",persevere:"\u{1F623}",person_fencing:"\u{1F93A}",pouting_woman:"\u{1F64E}",phone:"\u260E\uFE0F",pick:"\u26CF",pig:"\u{1F437}",pig2:"\u{1F416}",pig_nose:"\u{1F43D}",pill:"\u{1F48A}",pineapple:"\u{1F34D}",ping_pong:"\u{1F3D3}",pisces:"\u2653\uFE0F",pizza:"\u{1F355}",place_of_worship:"\u{1F6D0}",plate_with_cutlery:"\u{1F37D}",play_or_pause_button:"\u23EF",point_down:"\u{1F447}",point_left:"\u{1F448}",point_right:"\u{1F449}",point_up:"\u261D\uFE0F",point_up_2:"\u{1F446}",police_car:"\u{1F693}",policewoman:"\u{1F46E}&zwj;\u2640\uFE0F",poodle:"\u{1F429}",popcorn:"\u{1F37F}",post_office:"\u{1F3E3}",postal_horn:"\u{1F4EF}",postbox:"\u{1F4EE}",potable_water:"\u{1F6B0}",potato:"\u{1F954}",pouch:"\u{1F45D}",poultry_leg:"\u{1F357}",pound:"\u{1F4B7}",rage:"\u{1F621}",pouting_cat:"\u{1F63E}",pouting_man:"\u{1F64E}&zwj;\u2642\uFE0F",pray:"\u{1F64F}",prayer_beads:"\u{1F4FF}",pregnant_woman:"\u{1F930}",previous_track_button:"\u23EE",prince:"\u{1F934}",princess:"\u{1F478}",printer:"\u{1F5A8}",purple_heart:"\u{1F49C}",purse:"\u{1F45B}",pushpin:"\u{1F4CC}",put_litter_in_its_place:"\u{1F6AE}",question:"\u2753",rabbit:"\u{1F430}",rabbit2:"\u{1F407}",racehorse:"\u{1F40E}",racing_car:"\u{1F3CE}",radio:"\u{1F4FB}",radio_button:"\u{1F518}",radioactive:"\u2622\uFE0F",railway_car:"\u{1F683}",railway_track:"\u{1F6E4}",rainbow:"\u{1F308}",rainbow_flag:"\u{1F3F3}\uFE0F&zwj;\u{1F308}",raised_back_of_hand:"\u{1F91A}",raised_hand_with_fingers_splayed:"\u{1F590}",raised_hands:"\u{1F64C}",raising_hand_woman:"\u{1F64B}",raising_hand_man:"\u{1F64B}&zwj;\u2642\uFE0F",ram:"\u{1F40F}",ramen:"\u{1F35C}",rat:"\u{1F400}",record_button:"\u23FA",recycle:"\u267B\uFE0F",red_circle:"\u{1F534}",registered:"\xAE\uFE0F",relaxed:"\u263A\uFE0F",relieved:"\u{1F60C}",reminder_ribbon:"\u{1F397}",repeat:"\u{1F501}",repeat_one:"\u{1F502}",rescue_worker_helmet:"\u26D1",restroom:"\u{1F6BB}",revolving_hearts:"\u{1F49E}",rewind:"\u23EA",rhinoceros:"\u{1F98F}",ribbon:"\u{1F380}",rice:"\u{1F35A}",rice_ball:"\u{1F359}",rice_cracker:"\u{1F358}",rice_scene:"\u{1F391}",right_anger_bubble:"\u{1F5EF}",ring:"\u{1F48D}",robot:"\u{1F916}",rocket:"\u{1F680}",rofl:"\u{1F923}",roll_eyes:"\u{1F644}",roller_coaster:"\u{1F3A2}",rooster:"\u{1F413}",rose:"\u{1F339}",rosette:"\u{1F3F5}",rotating_light:"\u{1F6A8}",round_pushpin:"\u{1F4CD}",rowing_man:"\u{1F6A3}",rowing_woman:"\u{1F6A3}&zwj;\u2640\uFE0F",rugby_football:"\u{1F3C9}",running_man:"\u{1F3C3}",running_shirt_with_sash:"\u{1F3BD}",running_woman:"\u{1F3C3}&zwj;\u2640\uFE0F",sa:"\u{1F202}\uFE0F",sagittarius:"\u2650\uFE0F",sake:"\u{1F376}",sandal:"\u{1F461}",santa:"\u{1F385}",satellite:"\u{1F4E1}",saxophone:"\u{1F3B7}",school:"\u{1F3EB}",school_satchel:"\u{1F392}",scissors:"\u2702\uFE0F",scorpion:"\u{1F982}",scorpius:"\u264F\uFE0F",scream:"\u{1F631}",scream_cat:"\u{1F640}",scroll:"\u{1F4DC}",seat:"\u{1F4BA}",secret:"\u3299\uFE0F",see_no_evil:"\u{1F648}",seedling:"\u{1F331}",selfie:"\u{1F933}",shallow_pan_of_food:"\u{1F958}",shamrock:"\u2618\uFE0F",shark:"\u{1F988}",shaved_ice:"\u{1F367}",sheep:"\u{1F411}",shell:"\u{1F41A}",shield:"\u{1F6E1}",shinto_shrine:"\u26E9",ship:"\u{1F6A2}",shirt:"\u{1F455}",shopping:"\u{1F6CD}",shopping_cart:"\u{1F6D2}",shower:"\u{1F6BF}",shrimp:"\u{1F990}",signal_strength:"\u{1F4F6}",six_pointed_star:"\u{1F52F}",ski:"\u{1F3BF}",skier:"\u26F7",skull:"\u{1F480}",skull_and_crossbones:"\u2620\uFE0F",sleeping:"\u{1F634}",sleeping_bed:"\u{1F6CC}",sleepy:"\u{1F62A}",slightly_frowning_face:"\u{1F641}",slightly_smiling_face:"\u{1F642}",slot_machine:"\u{1F3B0}",small_airplane:"\u{1F6E9}",small_blue_diamond:"\u{1F539}",small_orange_diamond:"\u{1F538}",small_red_triangle:"\u{1F53A}",small_red_triangle_down:"\u{1F53B}",smile:"\u{1F604}",smile_cat:"\u{1F638}",smiley:"\u{1F603}",smiley_cat:"\u{1F63A}",smiling_imp:"\u{1F608}",smirk:"\u{1F60F}",smirk_cat:"\u{1F63C}",smoking:"\u{1F6AC}",snail:"\u{1F40C}",snake:"\u{1F40D}",sneezing_face:"\u{1F927}",snowboarder:"\u{1F3C2}",snowflake:"\u2744\uFE0F",snowman:"\u26C4\uFE0F",snowman_with_snow:"\u2603\uFE0F",sob:"\u{1F62D}",soccer:"\u26BD\uFE0F",soon:"\u{1F51C}",sos:"\u{1F198}",sound:"\u{1F509}",space_invader:"\u{1F47E}",spades:"\u2660\uFE0F",spaghetti:"\u{1F35D}",sparkle:"\u2747\uFE0F",sparkler:"\u{1F387}",sparkles:"\u2728",sparkling_heart:"\u{1F496}",speak_no_evil:"\u{1F64A}",speaker:"\u{1F508}",speaking_head:"\u{1F5E3}",speech_balloon:"\u{1F4AC}",speedboat:"\u{1F6A4}",spider:"\u{1F577}",spider_web:"\u{1F578}",spiral_calendar:"\u{1F5D3}",spiral_notepad:"\u{1F5D2}",spoon:"\u{1F944}",squid:"\u{1F991}",stadium:"\u{1F3DF}",star:"\u2B50\uFE0F",star2:"\u{1F31F}",star_and_crescent:"\u262A\uFE0F",star_of_david:"\u2721\uFE0F",stars:"\u{1F320}",station:"\u{1F689}",statue_of_liberty:"\u{1F5FD}",steam_locomotive:"\u{1F682}",stew:"\u{1F372}",stop_button:"\u23F9",stop_sign:"\u{1F6D1}",stopwatch:"\u23F1",straight_ruler:"\u{1F4CF}",strawberry:"\u{1F353}",stuck_out_tongue:"\u{1F61B}",stuck_out_tongue_closed_eyes:"\u{1F61D}",stuck_out_tongue_winking_eye:"\u{1F61C}",studio_microphone:"\u{1F399}",stuffed_flatbread:"\u{1F959}",sun_behind_large_cloud:"\u{1F325}",sun_behind_rain_cloud:"\u{1F326}",sun_behind_small_cloud:"\u{1F324}",sun_with_face:"\u{1F31E}",sunflower:"\u{1F33B}",sunglasses:"\u{1F60E}",sunny:"\u2600\uFE0F",sunrise:"\u{1F305}",sunrise_over_mountains:"\u{1F304}",surfing_man:"\u{1F3C4}",surfing_woman:"\u{1F3C4}&zwj;\u2640\uFE0F",sushi:"\u{1F363}",suspension_railway:"\u{1F69F}",sweat:"\u{1F613}",sweat_drops:"\u{1F4A6}",sweat_smile:"\u{1F605}",sweet_potato:"\u{1F360}",swimming_man:"\u{1F3CA}",swimming_woman:"\u{1F3CA}&zwj;\u2640\uFE0F",symbols:"\u{1F523}",synagogue:"\u{1F54D}",syringe:"\u{1F489}",taco:"\u{1F32E}",tada:"\u{1F389}",tanabata_tree:"\u{1F38B}",taurus:"\u2649\uFE0F",taxi:"\u{1F695}",tea:"\u{1F375}",telephone_receiver:"\u{1F4DE}",telescope:"\u{1F52D}",tennis:"\u{1F3BE}",tent:"\u26FA\uFE0F",thermometer:"\u{1F321}",thinking:"\u{1F914}",thought_balloon:"\u{1F4AD}",ticket:"\u{1F3AB}",tickets:"\u{1F39F}",tiger:"\u{1F42F}",tiger2:"\u{1F405}",timer_clock:"\u23F2",tipping_hand_man:"\u{1F481}&zwj;\u2642\uFE0F",tired_face:"\u{1F62B}",tm:"\u2122\uFE0F",toilet:"\u{1F6BD}",tokyo_tower:"\u{1F5FC}",tomato:"\u{1F345}",tongue:"\u{1F445}",top:"\u{1F51D}",tophat:"\u{1F3A9}",tornado:"\u{1F32A}",trackball:"\u{1F5B2}",tractor:"\u{1F69C}",traffic_light:"\u{1F6A5}",train:"\u{1F68B}",train2:"\u{1F686}",tram:"\u{1F68A}",triangular_flag_on_post:"\u{1F6A9}",triangular_ruler:"\u{1F4D0}",trident:"\u{1F531}",triumph:"\u{1F624}",trolleybus:"\u{1F68E}",trophy:"\u{1F3C6}",tropical_drink:"\u{1F379}",tropical_fish:"\u{1F420}",truck:"\u{1F69A}",trumpet:"\u{1F3BA}",tulip:"\u{1F337}",tumbler_glass:"\u{1F943}",turkey:"\u{1F983}",turtle:"\u{1F422}",tv:"\u{1F4FA}",twisted_rightwards_arrows:"\u{1F500}",two_hearts:"\u{1F495}",two_men_holding_hands:"\u{1F46C}",two_women_holding_hands:"\u{1F46D}",u5272:"\u{1F239}",u5408:"\u{1F234}",u55b6:"\u{1F23A}",u6307:"\u{1F22F}\uFE0F",u6708:"\u{1F237}\uFE0F",u6709:"\u{1F236}",u6e80:"\u{1F235}",u7121:"\u{1F21A}\uFE0F",u7533:"\u{1F238}",u7981:"\u{1F232}",u7a7a:"\u{1F233}",umbrella:"\u2614\uFE0F",unamused:"\u{1F612}",underage:"\u{1F51E}",unicorn:"\u{1F984}",unlock:"\u{1F513}",up:"\u{1F199}",upside_down_face:"\u{1F643}",v:"\u270C\uFE0F",vertical_traffic_light:"\u{1F6A6}",vhs:"\u{1F4FC}",vibration_mode:"\u{1F4F3}",video_camera:"\u{1F4F9}",video_game:"\u{1F3AE}",violin:"\u{1F3BB}",virgo:"\u264D\uFE0F",volcano:"\u{1F30B}",volleyball:"\u{1F3D0}",vs:"\u{1F19A}",vulcan_salute:"\u{1F596}",walking_man:"\u{1F6B6}",walking_woman:"\u{1F6B6}&zwj;\u2640\uFE0F",waning_crescent_moon:"\u{1F318}",waning_gibbous_moon:"\u{1F316}",warning:"\u26A0\uFE0F",wastebasket:"\u{1F5D1}",watch:"\u231A\uFE0F",water_buffalo:"\u{1F403}",watermelon:"\u{1F349}",wave:"\u{1F44B}",wavy_dash:"\u3030\uFE0F",waxing_crescent_moon:"\u{1F312}",wc:"\u{1F6BE}",weary:"\u{1F629}",wedding:"\u{1F492}",weight_lifting_man:"\u{1F3CB}\uFE0F",weight_lifting_woman:"\u{1F3CB}\uFE0F&zwj;\u2640\uFE0F",whale:"\u{1F433}",whale2:"\u{1F40B}",wheel_of_dharma:"\u2638\uFE0F",wheelchair:"\u267F\uFE0F",white_check_mark:"\u2705",white_circle:"\u26AA\uFE0F",white_flag:"\u{1F3F3}\uFE0F",white_flower:"\u{1F4AE}",white_large_square:"\u2B1C\uFE0F",white_medium_small_square:"\u25FD\uFE0F",white_medium_square:"\u25FB\uFE0F",white_small_square:"\u25AB\uFE0F",white_square_button:"\u{1F533}",wilted_flower:"\u{1F940}",wind_chime:"\u{1F390}",wind_face:"\u{1F32C}",wine_glass:"\u{1F377}",wink:"\u{1F609}",wolf:"\u{1F43A}",woman:"\u{1F469}",woman_artist:"\u{1F469}&zwj;\u{1F3A8}",woman_astronaut:"\u{1F469}&zwj;\u{1F680}",woman_cartwheeling:"\u{1F938}&zwj;\u2640\uFE0F",woman_cook:"\u{1F469}&zwj;\u{1F373}",woman_facepalming:"\u{1F926}&zwj;\u2640\uFE0F",woman_factory_worker:"\u{1F469}&zwj;\u{1F3ED}",woman_farmer:"\u{1F469}&zwj;\u{1F33E}",woman_firefighter:"\u{1F469}&zwj;\u{1F692}",woman_health_worker:"\u{1F469}&zwj;\u2695\uFE0F",woman_judge:"\u{1F469}&zwj;\u2696\uFE0F",woman_juggling:"\u{1F939}&zwj;\u2640\uFE0F",woman_mechanic:"\u{1F469}&zwj;\u{1F527}",woman_office_worker:"\u{1F469}&zwj;\u{1F4BC}",woman_pilot:"\u{1F469}&zwj;\u2708\uFE0F",woman_playing_handball:"\u{1F93E}&zwj;\u2640\uFE0F",woman_playing_water_polo:"\u{1F93D}&zwj;\u2640\uFE0F",woman_scientist:"\u{1F469}&zwj;\u{1F52C}",woman_shrugging:"\u{1F937}&zwj;\u2640\uFE0F",woman_singer:"\u{1F469}&zwj;\u{1F3A4}",woman_student:"\u{1F469}&zwj;\u{1F393}",woman_teacher:"\u{1F469}&zwj;\u{1F3EB}",woman_technologist:"\u{1F469}&zwj;\u{1F4BB}",woman_with_turban:"\u{1F473}&zwj;\u2640\uFE0F",womans_clothes:"\u{1F45A}",womans_hat:"\u{1F452}",women_wrestling:"\u{1F93C}&zwj;\u2640\uFE0F",womens:"\u{1F6BA}",world_map:"\u{1F5FA}",worried:"\u{1F61F}",wrench:"\u{1F527}",writing_hand:"\u270D\uFE0F",x:"\u274C",yellow_heart:"\u{1F49B}",yen:"\u{1F4B4}",yin_yang:"\u262F\uFE0F",yum:"\u{1F60B}",zap:"\u26A1\uFE0F",zipper_mouth_face:"\u{1F910}",zzz:"\u{1F4A4}",octocat:'<img alt=":octocat:" height="20" width="20" align="absmiddle" src="https://assets-cdn.github.com/images/icons/emoji/octocat.png">',showdown:`<span style="font-family: 'Anonymous Pro', monospace; text-decoration: underline; text-decoration-style: dashed; text-decoration-color: #3e8b8a;text-underline-position: under;">S</span>`},r.Converter=function(i){"use strict";var t={},u=[],s=[],p={},h=c,f={parsed:{},raw:"",format:""};g();function g(){i=i||{};for(var l in o)o.hasOwnProperty(l)&&(t[l]=o[l]);if(typeof i=="object")for(var k in i)i.hasOwnProperty(k)&&(t[k]=i[k]);else throw Error("Converter expects the passed parameter to be an object, but "+typeof i+" was passed instead.");t.extensions&&r.helper.forEach(t.extensions,T)}function T(l,k){if(k=k||null,r.helper.isString(l))if(l=r.helper.stdExtName(l),k=l,r.extensions[l]){console.warn("DEPRECATION WARNING: "+l+" is an old extension that uses a deprecated loading method.Please inform the developer that the extension should be updated!"),b(r.extensions[l],l);return}else if(!r.helper.isUndefined(d[l]))l=d[l];else throw Error('Extension "'+l+'" could not be loaded. It was either not found or is not a valid extension.');typeof l=="function"&&(l=l()),r.helper.isArray(l)||(l=[l]);var C=_(l,k);if(!C.valid)throw Error(C.error);for(var L=0;L<l.length;++L){switch(l[L].type){case"lang":u.push(l[L]);break;case"output":s.push(l[L]);break}if(l[L].hasOwnProperty("listeners"))for(var j in l[L].listeners)l[L].listeners.hasOwnProperty(j)&&w(j,l[L].listeners[j])}}function b(l,k){typeof l=="function"&&(l=l(new r.Converter)),r.helper.isArray(l)||(l=[l]);var C=_(l,k);if(!C.valid)throw Error(C.error);for(var L=0;L<l.length;++L)switch(l[L].type){case"lang":u.push(l[L]);break;case"output":s.push(l[L]);break;default:throw Error("Extension loader error: Type unrecognized!!!")}}function w(l,k){if(!r.helper.isString(l))throw Error("Invalid argument in converter.listen() method: name must be a string, but "+typeof l+" given");if(typeof k!="function")throw Error("Invalid argument in converter.listen() method: callback must be a function, but "+typeof k+" given");p.hasOwnProperty(l)||(p[l]=[]),p[l].push(k)}function y(l){var k=l.match(/^\s*/)[0].length,C=new RegExp("^\\s{0,"+k+"}","gm");return l.replace(C,"")}this._dispatch=function(k,C,L,j){if(p.hasOwnProperty(k))for(var P=0;P<p[k].length;++P){var q=p[k][P](k,C,this,L,j);q&&typeof q!="undefined"&&(C=q)}return C},this.listen=function(l,k){return w(l,k),this},this.makeHtml=function(l){if(!l)return l;var k={gHtmlBlocks:[],gHtmlMdBlocks:[],gHtmlSpans:[],gUrls:{},gTitles:{},gDimensions:{},gListLevel:0,hashLinkCounts:{},langExtensions:u,outputModifiers:s,converter:this,ghCodeBlocks:[],metadata:{parsed:{},raw:"",format:""}};return l=l.replace(/¨/g,"\xA8T"),l=l.replace(/\$/g,"\xA8D"),l=l.replace(/\r\n/g,`
`),l=l.replace(/\r/g,`
`),l=l.replace(/\u00A0/g,"&nbsp;"),t.smartIndentationFix&&(l=y(l)),l=`

`+l+`

`,l=r.subParser("detab")(l,t,k),l=l.replace(/^[ \t]+$/mg,""),r.helper.forEach(u,function(C){l=r.subParser("runExtension")(C,l,t,k)}),l=r.subParser("metadata")(l,t,k),l=r.subParser("hashPreCodeTags")(l,t,k),l=r.subParser("githubCodeBlocks")(l,t,k),l=r.subParser("hashHTMLBlocks")(l,t,k),l=r.subParser("hashCodeTags")(l,t,k),l=r.subParser("stripLinkDefinitions")(l,t,k),l=r.subParser("blockGamut")(l,t,k),l=r.subParser("unhashHTMLSpans")(l,t,k),l=r.subParser("unescapeSpecialChars")(l,t,k),l=l.replace(/¨D/g,"$$"),l=l.replace(/¨T/g,"\xA8"),l=r.subParser("completeHTMLDocument")(l,t,k),r.helper.forEach(s,function(C){l=r.subParser("runExtension")(C,l,t,k)}),f=k.metadata,l},this.makeMarkdown=this.makeMd=function(l,k){if(l=l.replace(/\r\n/g,`
`),l=l.replace(/\r/g,`
`),l=l.replace(/>[ \t]+</,">\xA8NBSP;<"),!k)if(window&&window.document)k=window.document;else throw new Error("HTMLParser is undefined. If in a webworker or nodejs environment, you need to provide a WHATWG DOM and HTML such as JSDOM");var C=k.createElement("div");C.innerHTML=l;var L={preList:J(C)};Y(C);for(var j=C.childNodes,P="",q=0;q<j.length;q++)P+=r.subParser("makeMarkdown.node")(j[q],L);function Y(D){for(var U=0;U<D.childNodes.length;++U){var ee=D.childNodes[U];ee.nodeType===3?/\S/.test(ee.nodeValue)?(ee.nodeValue=ee.nodeValue.split(`
`).join(" "),ee.nodeValue=ee.nodeValue.replace(/(\s)+/g,"$1")):(D.removeChild(ee),--U):ee.nodeType===1&&Y(ee)}}function J(D){for(var U=D.querySelectorAll("pre"),ee=[],re=0;re<U.length;++re)if(U[re].childElementCount===1&&U[re].firstChild.tagName.toLowerCase()==="code"){var tr=U[re].firstChild.innerHTML.trim(),dr=U[re].firstChild.getAttribute("data-language")||"";if(dr==="")for(var Pr=U[re].firstChild.className.split(" "),or=0;or<Pr.length;++or){var Mr=Pr[or].match(/^language-(.+)$/);if(Mr!==null){dr=Mr[1];break}}tr=r.helper.unescapeHTMLEntities(tr),ee.push(tr),U[re].outerHTML='<precode language="'+dr+'" precodenum="'+re.toString()+'"></precode>'}else ee.push(U[re].innerHTML),U[re].innerHTML="",U[re].setAttribute("prenum",re.toString());return ee}return P},this.setOption=function(l,k){t[l]=k},this.getOption=function(l){return t[l]},this.getOptions=function(){return t},this.addExtension=function(l,k){k=k||null,T(l,k)},this.useExtension=function(l){T(l)},this.setFlavor=function(l){if(!m.hasOwnProperty(l))throw Error(l+" flavor was not found");var k=m[l];h=l;for(var C in k)k.hasOwnProperty(C)&&(t[C]=k[C])},this.getFlavor=function(){return h},this.removeExtension=function(l){r.helper.isArray(l)||(l=[l]);for(var k=0;k<l.length;++k){for(var C=l[k],L=0;L<u.length;++L)u[L]===C&&u[L].splice(L,1);for(var j=0;j<s.length;++L)s[j]===C&&s[j].splice(L,1)}},this.getAllExtensions=function(){return{language:u,output:s}},this.getMetadata=function(l){return l?f.raw:f.parsed},this.getMetadataFormat=function(){return f.format},this._setMetadataPair=function(l,k){f.parsed[l]=k},this._setMetadataFormat=function(l){f.format=l},this._setMetadataRaw=function(l){f.raw=l}},r.subParser("anchors",function(i,t,u){"use strict";i=u.converter._dispatch("anchors.before",i,t,u);var s=function(p,h,f,g,T,b,w){if(r.helper.isUndefined(w)&&(w=""),f=f.toLowerCase(),p.search(/\(<?\s*>? ?(['"].*['"])?\)$/m)>-1)g="";else if(!g)if(f||(f=h.toLowerCase().replace(/ ?\n/g," ")),g="#"+f,!r.helper.isUndefined(u.gUrls[f]))g=u.gUrls[f],r.helper.isUndefined(u.gTitles[f])||(w=u.gTitles[f]);else return p;g=g.replace(r.helper.regexes.asteriskDashAndColon,r.helper.escapeCharactersCallback);var y='<a href="'+g+'"';return w!==""&&w!==null&&(w=w.replace(/"/g,"&quot;"),w=w.replace(r.helper.regexes.asteriskDashAndColon,r.helper.escapeCharactersCallback),y+=' title="'+w+'"'),t.openLinksInNewWindow&&!/^#/.test(g)&&(y+=' rel="noopener noreferrer" target="\xA8E95Eblank"'),y+=">"+h+"</a>",y};return i=i.replace(/\[((?:\[[^\]]*]|[^\[\]])*)] ?(?:\n *)?\[(.*?)]()()()()/g,s),i=i.replace(/\[((?:\[[^\]]*]|[^\[\]])*)]()[ \t]*\([ \t]?<([^>]*)>(?:[ \t]*((["'])([^"]*?)\5))?[ \t]?\)/g,s),i=i.replace(/\[((?:\[[^\]]*]|[^\[\]])*)]()[ \t]*\([ \t]?<?([\S]+?(?:\([\S]*?\)[\S]*?)?)>?(?:[ \t]*((["'])([^"]*?)\5))?[ \t]?\)/g,s),i=i.replace(/\[([^\[\]]+)]()()()()()/g,s),t.ghMentions&&(i=i.replace(/(^|\s)(\\)?(@([a-z\d]+(?:[a-z\d.-]+?[a-z\d]+)*))/gmi,function(p,h,f,g,T){if(f==="\\")return h+g;if(!r.helper.isString(t.ghMentionsLink))throw new Error("ghMentionsLink option must be a string");var b=t.ghMentionsLink.replace(/\{u}/g,T),w="";return t.openLinksInNewWindow&&(w=' rel="noopener noreferrer" target="\xA8E95Eblank"'),h+'<a href="'+b+'"'+w+">"+g+"</a>"})),i=u.converter._dispatch("anchors.after",i,t,u),i});var A=/([*~_]+|\b)(((https?|ftp|dict):\/\/|www\.)[^'">\s]+?\.[^'">\s]+?)()(\1)?(?=\s|$)(?!["<>])/gi,E=/([*~_]+|\b)(((https?|ftp|dict):\/\/|www\.)[^'">\s]+\.[^'">\s]+?)([.!?,()\[\]])?(\1)?(?=\s|$)(?!["<>])/gi,v=/()<(((https?|ftp|dict):\/\/|www\.)[^'">\s]+)()>()/gi,M=/(^|\s)(?:mailto:)?([A-Za-z0-9!#$%&'*+-/=?^_`{|}~.]+@[-a-z0-9]+(\.[-a-z0-9]+)*\.[a-z]+)(?=$|\s)/gmi,F=/<()(?:mailto:)?([-.\w]+@[-a-z0-9]+(\.[-a-z0-9]+)*\.[a-z]+)>/gi,I=function(i){"use strict";return function(t,u,s,p,h,f,g){s=s.replace(r.helper.regexes.asteriskDashAndColon,r.helper.escapeCharactersCallback);var T=s,b="",w="",y=u||"",l=g||"";return/^www\./i.test(s)&&(s=s.replace(/^www\./i,"http://www.")),i.excludeTrailingPunctuationFromURLs&&f&&(b=f),i.openLinksInNewWindow&&(w=' rel="noopener noreferrer" target="\xA8E95Eblank"'),y+'<a href="'+s+'"'+w+">"+T+"</a>"+b+l}},W=function(i,t){"use strict";return function(u,s,p){var h="mailto:";return s=s||"",p=r.subParser("unescapeSpecialChars")(p,i,t),i.encodeEmails?(h=r.helper.encodeEmailAddress(h+p),p=r.helper.encodeEmailAddress(p)):h=h+p,s+'<a href="'+h+'">'+p+"</a>"}};r.subParser("autoLinks",function(i,t,u){"use strict";return i=u.converter._dispatch("autoLinks.before",i,t,u),i=i.replace(v,I(t)),i=i.replace(F,W(t,u)),i=u.converter._dispatch("autoLinks.after",i,t,u),i}),r.subParser("simplifiedAutoLinks",function(i,t,u){"use strict";return t.simplifiedAutoLink&&(i=u.converter._dispatch("simplifiedAutoLinks.before",i,t,u),t.excludeTrailingPunctuationFromURLs?i=i.replace(E,I(t)):i=i.replace(A,I(t)),i=i.replace(M,W(t,u)),i=u.converter._dispatch("simplifiedAutoLinks.after",i,t,u)),i}),r.subParser("blockGamut",function(i,t,u){"use strict";return i=u.converter._dispatch("blockGamut.before",i,t,u),i=r.subParser("blockQuotes")(i,t,u),i=r.subParser("headers")(i,t,u),i=r.subParser("horizontalRule")(i,t,u),i=r.subParser("lists")(i,t,u),i=r.subParser("codeBlocks")(i,t,u),i=r.subParser("tables")(i,t,u),i=r.subParser("hashHTMLBlocks")(i,t,u),i=r.subParser("paragraphs")(i,t,u),i=u.converter._dispatch("blockGamut.after",i,t,u),i}),r.subParser("blockQuotes",function(i,t,u){"use strict";i=u.converter._dispatch("blockQuotes.before",i,t,u),i=i+`

`;var s=/(^ {0,3}>[ \t]?.+\n(.+\n)*\n*)+/gm;return t.splitAdjacentBlockquotes&&(s=/^ {0,3}>[\s\S]*?(?:\n\n)/gm),i=i.replace(s,function(p){return p=p.replace(/^[ \t]*>[ \t]?/gm,""),p=p.replace(/¨0/g,""),p=p.replace(/^[ \t]+$/gm,""),p=r.subParser("githubCodeBlocks")(p,t,u),p=r.subParser("blockGamut")(p,t,u),p=p.replace(/(^|\n)/g,"$1  "),p=p.replace(/(\s*<pre>[^\r]+?<\/pre>)/gm,function(h,f){var g=f;return g=g.replace(/^  /mg,"\xA80"),g=g.replace(/¨0/g,""),g}),r.subParser("hashBlock")(`<blockquote>
`+p+`
</blockquote>`,t,u)}),i=u.converter._dispatch("blockQuotes.after",i,t,u),i}),r.subParser("codeBlocks",function(i,t,u){"use strict";i=u.converter._dispatch("codeBlocks.before",i,t,u),i+="\xA80";var s=/(?:\n\n|^)((?:(?:[ ]{4}|\t).*\n+)+)(\n*[ ]{0,3}[^ \t\n]|(?=¨0))/g;return i=i.replace(s,function(p,h,f){var g=h,T=f,b=`
`;return g=r.subParser("outdent")(g,t,u),g=r.subParser("encodeCode")(g,t,u),g=r.subParser("detab")(g,t,u),g=g.replace(/^\n+/g,""),g=g.replace(/\n+$/g,""),t.omitExtraWLInCodeBlocks&&(b=""),g="<pre><code>"+g+b+"</code></pre>",r.subParser("hashBlock")(g,t,u)+T}),i=i.replace(/¨0/,""),i=u.converter._dispatch("codeBlocks.after",i,t,u),i}),r.subParser("codeSpans",function(i,t,u){"use strict";return i=u.converter._dispatch("codeSpans.before",i,t,u),typeof i=="undefined"&&(i=""),i=i.replace(/(^|[^\\])(`+)([^\r]*?[^`])\2(?!`)/gm,function(s,p,h,f){var g=f;return g=g.replace(/^([ \t]*)/g,""),g=g.replace(/[ \t]*$/g,""),g=r.subParser("encodeCode")(g,t,u),g=p+"<code>"+g+"</code>",g=r.subParser("hashHTMLSpans")(g,t,u),g}),i=u.converter._dispatch("codeSpans.after",i,t,u),i}),r.subParser("completeHTMLDocument",function(i,t,u){"use strict";if(!t.completeHTMLDocument)return i;i=u.converter._dispatch("completeHTMLDocument.before",i,t,u);var s="html",p=`<!DOCTYPE HTML>
`,h="",f=`<meta charset="utf-8">
`,g="",T="";typeof u.metadata.parsed.doctype!="undefined"&&(p="<!DOCTYPE "+u.metadata.parsed.doctype+`>
`,s=u.metadata.parsed.doctype.toString().toLowerCase(),(s==="html"||s==="html5")&&(f='<meta charset="utf-8">'));for(var b in u.metadata.parsed)if(u.metadata.parsed.hasOwnProperty(b))switch(b.toLowerCase()){case"doctype":break;case"title":h="<title>"+u.metadata.parsed.title+`</title>
`;break;case"charset":s==="html"||s==="html5"?f='<meta charset="'+u.metadata.parsed.charset+`">
`:f='<meta name="charset" content="'+u.metadata.parsed.charset+`">
`;break;case"language":case"lang":g=' lang="'+u.metadata.parsed[b]+'"',T+='<meta name="'+b+'" content="'+u.metadata.parsed[b]+`">
`;break;default:T+='<meta name="'+b+'" content="'+u.metadata.parsed[b]+`">
`}return i=p+"<html"+g+`>
<head>
`+h+f+T+`</head>
<body>
`+i.trim()+`
</body>
</html>`,i=u.converter._dispatch("completeHTMLDocument.after",i,t,u),i}),r.subParser("detab",function(i,t,u){"use strict";return i=u.converter._dispatch("detab.before",i,t,u),i=i.replace(/\t(?=\t)/g,"    "),i=i.replace(/\t/g,"\xA8A\xA8B"),i=i.replace(/¨B(.+?)¨A/g,function(s,p){for(var h=p,f=4-h.length%4,g=0;g<f;g++)h+=" ";return h}),i=i.replace(/¨A/g,"    "),i=i.replace(/¨B/g,""),i=u.converter._dispatch("detab.after",i,t,u),i}),r.subParser("ellipsis",function(i,t,u){"use strict";return i=u.converter._dispatch("ellipsis.before",i,t,u),i=i.replace(/\.\.\./g,"\u2026"),i=u.converter._dispatch("ellipsis.after",i,t,u),i}),r.subParser("emoji",function(i,t,u){"use strict";if(!t.emoji)return i;i=u.converter._dispatch("emoji.before",i,t,u);var s=/:([\S]+?):/g;return i=i.replace(s,function(p,h){return r.helper.emojis.hasOwnProperty(h)?r.helper.emojis[h]:p}),i=u.converter._dispatch("emoji.after",i,t,u),i}),r.subParser("encodeAmpsAndAngles",function(i,t,u){"use strict";return i=u.converter._dispatch("encodeAmpsAndAngles.before",i,t,u),i=i.replace(/&(?!#?[xX]?(?:[0-9a-fA-F]+|\w+);)/g,"&amp;"),i=i.replace(/<(?![a-z\/?$!])/gi,"&lt;"),i=i.replace(/</g,"&lt;"),i=i.replace(/>/g,"&gt;"),i=u.converter._dispatch("encodeAmpsAndAngles.after",i,t,u),i}),r.subParser("encodeBackslashEscapes",function(i,t,u){"use strict";return i=u.converter._dispatch("encodeBackslashEscapes.before",i,t,u),i=i.replace(/\\(\\)/g,r.helper.escapeCharactersCallback),i=i.replace(/\\([`*_{}\[\]()>#+.!~=|-])/g,r.helper.escapeCharactersCallback),i=u.converter._dispatch("encodeBackslashEscapes.after",i,t,u),i}),r.subParser("encodeCode",function(i,t,u){"use strict";return i=u.converter._dispatch("encodeCode.before",i,t,u),i=i.replace(/&/g,"&amp;").replace(/</g,"&lt;").replace(/>/g,"&gt;").replace(/([*_{}\[\]\\=~-])/g,r.helper.escapeCharactersCallback),i=u.converter._dispatch("encodeCode.after",i,t,u),i}),r.subParser("escapeSpecialCharsWithinTagAttributes",function(i,t,u){"use strict";i=u.converter._dispatch("escapeSpecialCharsWithinTagAttributes.before",i,t,u);var s=/<\/?[a-z\d_:-]+(?:[\s]+[\s\S]+?)?>/gi,p=/<!(--(?:(?:[^>-]|-[^>])(?:[^-]|-[^-])*)--)>/gi;return i=i.replace(s,function(h){return h.replace(/(.)<\/?code>(?=.)/g,"$1`").replace(/([\\`*_~=|])/g,r.helper.escapeCharactersCallback)}),i=i.replace(p,function(h){return h.replace(/([\\`*_~=|])/g,r.helper.escapeCharactersCallback)}),i=u.converter._dispatch("escapeSpecialCharsWithinTagAttributes.after",i,t,u),i}),r.subParser("githubCodeBlocks",function(i,t,u){"use strict";return t.ghCodeBlocks?(i=u.converter._dispatch("githubCodeBlocks.before",i,t,u),i+="\xA80",i=i.replace(/(?:^|\n)(?: {0,3})(```+|~~~+)(?: *)([^\s`~]*)\n([\s\S]*?)\n(?: {0,3})\1/g,function(s,p,h,f){var g=t.omitExtraWLInCodeBlocks?"":`
`;return f=r.subParser("encodeCode")(f,t,u),f=r.subParser("detab")(f,t,u),f=f.replace(/^\n+/g,""),f=f.replace(/\n+$/g,""),f="<pre><code"+(h?' class="'+h+" language-"+h+'"':"")+">"+f+g+"</code></pre>",f=r.subParser("hashBlock")(f,t,u),`

\xA8G`+(u.ghCodeBlocks.push({text:s,codeblock:f})-1)+`G

`}),i=i.replace(/¨0/,""),u.converter._dispatch("githubCodeBlocks.after",i,t,u)):i}),r.subParser("hashBlock",function(i,t,u){"use strict";return i=u.converter._dispatch("hashBlock.before",i,t,u),i=i.replace(/(^\n+|\n+$)/g,""),i=`

\xA8K`+(u.gHtmlBlocks.push(i)-1)+`K

`,i=u.converter._dispatch("hashBlock.after",i,t,u),i}),r.subParser("hashCodeTags",function(i,t,u){"use strict";i=u.converter._dispatch("hashCodeTags.before",i,t,u);var s=function(p,h,f,g){var T=f+r.subParser("encodeCode")(h,t,u)+g;return"\xA8C"+(u.gHtmlSpans.push(T)-1)+"C"};return i=r.helper.replaceRecursiveRegExp(i,s,"<code\\b[^>]*>","</code>","gim"),i=u.converter._dispatch("hashCodeTags.after",i,t,u),i}),r.subParser("hashElement",function(i,t,u){"use strict";return function(s,p){var h=p;return h=h.replace(/\n\n/g,`
`),h=h.replace(/^\n/,""),h=h.replace(/\n+$/g,""),h=`

\xA8K`+(u.gHtmlBlocks.push(h)-1)+`K

`,h}}),r.subParser("hashHTMLBlocks",function(i,t,u){"use strict";i=u.converter._dispatch("hashHTMLBlocks.before",i,t,u);var s=["pre","div","h1","h2","h3","h4","h5","h6","blockquote","table","dl","ol","ul","script","noscript","form","fieldset","iframe","math","style","section","header","footer","nav","article","aside","address","audio","canvas","figure","hgroup","output","video","p"],p=function(l,k,C,L){var j=l;return C.search(/\bmarkdown\b/)!==-1&&(j=C+u.converter.makeHtml(k)+L),`

\xA8K`+(u.gHtmlBlocks.push(j)-1)+`K

`};t.backslashEscapesHTMLTags&&(i=i.replace(/\\<(\/?[^>]+?)>/g,function(l,k){return"&lt;"+k+"&gt;"}));for(var h=0;h<s.length;++h)for(var f,g=new RegExp("^ {0,3}(<"+s[h]+"\\b[^>]*>)","im"),T="<"+s[h]+"\\b[^>]*>",b="</"+s[h]+">";(f=r.helper.regexIndexOf(i,g))!==-1;){var w=r.helper.splitAtIndex(i,f),y=r.helper.replaceRecursiveRegExp(w[1],p,T,b,"im");if(y===w[1])break;i=w[0].concat(y)}return i=i.replace(/(\n {0,3}(<(hr)\b([^<>])*?\/?>)[ \t]*(?=\n{2,}))/g,r.subParser("hashElement")(i,t,u)),i=r.helper.replaceRecursiveRegExp(i,function(l){return`

\xA8K`+(u.gHtmlBlocks.push(l)-1)+`K

`},"^ {0,3}<!--","-->","gm"),i=i.replace(/(?:\n\n)( {0,3}(?:<([?%])[^\r]*?\2>)[ \t]*(?=\n{2,}))/g,r.subParser("hashElement")(i,t,u)),i=u.converter._dispatch("hashHTMLBlocks.after",i,t,u),i}),r.subParser("hashHTMLSpans",function(i,t,u){"use strict";i=u.converter._dispatch("hashHTMLSpans.before",i,t,u);function s(p){return"\xA8C"+(u.gHtmlSpans.push(p)-1)+"C"}return i=i.replace(/<[^>]+?\/>/gi,function(p){return s(p)}),i=i.replace(/<([^>]+?)>[\s\S]*?<\/\1>/g,function(p){return s(p)}),i=i.replace(/<([^>]+?)\s[^>]+?>[\s\S]*?<\/\1>/g,function(p){return s(p)}),i=i.replace(/<[^>]+?>/gi,function(p){return s(p)}),i=u.converter._dispatch("hashHTMLSpans.after",i,t,u),i}),r.subParser("unhashHTMLSpans",function(i,t,u){"use strict";i=u.converter._dispatch("unhashHTMLSpans.before",i,t,u);for(var s=0;s<u.gHtmlSpans.length;++s){for(var p=u.gHtmlSpans[s],h=0;/¨C(\d+)C/.test(p);){var f=RegExp.$1;if(p=p.replace("\xA8C"+f+"C",u.gHtmlSpans[f]),h===10){console.error("maximum nesting of 10 spans reached!!!");break}++h}i=i.replace("\xA8C"+s+"C",p)}return i=u.converter._dispatch("unhashHTMLSpans.after",i,t,u),i}),r.subParser("hashPreCodeTags",function(i,t,u){"use strict";i=u.converter._dispatch("hashPreCodeTags.before",i,t,u);var s=function(p,h,f,g){var T=f+r.subParser("encodeCode")(h,t,u)+g;return`

\xA8G`+(u.ghCodeBlocks.push({text:p,codeblock:T})-1)+`G

`};return i=r.helper.replaceRecursiveRegExp(i,s,"^ {0,3}<pre\\b[^>]*>\\s*<code\\b[^>]*>","^ {0,3}</code>\\s*</pre>","gim"),i=u.converter._dispatch("hashPreCodeTags.after",i,t,u),i}),r.subParser("headers",function(i,t,u){"use strict";i=u.converter._dispatch("headers.before",i,t,u);var s=isNaN(parseInt(t.headerLevelStart))?1:parseInt(t.headerLevelStart),p=t.smoothLivePreview?/^(.+)[ \t]*\n={2,}[ \t]*\n+/gm:/^(.+)[ \t]*\n=+[ \t]*\n+/gm,h=t.smoothLivePreview?/^(.+)[ \t]*\n-{2,}[ \t]*\n+/gm:/^(.+)[ \t]*\n-+[ \t]*\n+/gm;i=i.replace(p,function(T,b){var w=r.subParser("spanGamut")(b,t,u),y=t.noHeaderId?"":' id="'+g(b)+'"',l=s,k="<h"+l+y+">"+w+"</h"+l+">";return r.subParser("hashBlock")(k,t,u)}),i=i.replace(h,function(T,b){var w=r.subParser("spanGamut")(b,t,u),y=t.noHeaderId?"":' id="'+g(b)+'"',l=s+1,k="<h"+l+y+">"+w+"</h"+l+">";return r.subParser("hashBlock")(k,t,u)});var f=t.requireSpaceBeforeHeadingText?/^(#{1,6})[ \t]+(.+?)[ \t]*#*\n+/gm:/^(#{1,6})[ \t]*(.+?)[ \t]*#*\n+/gm;i=i.replace(f,function(T,b,w){var y=w;t.customizedHeaderId&&(y=w.replace(/\s?\{([^{]+?)}\s*$/,""));var l=r.subParser("spanGamut")(y,t,u),k=t.noHeaderId?"":' id="'+g(w)+'"',C=s-1+b.length,L="<h"+C+k+">"+l+"</h"+C+">";return r.subParser("hashBlock")(L,t,u)});function g(T){var b,w;if(t.customizedHeaderId){var y=T.match(/\{([^{]+?)}\s*$/);y&&y[1]&&(T=y[1])}return b=T,r.helper.isString(t.prefixHeaderId)?w=t.prefixHeaderId:t.prefixHeaderId===!0?w="section-":w="",t.rawPrefixHeaderId||(b=w+b),t.ghCompatibleHeaderId?b=b.replace(/ /g,"-").replace(/&amp;/g,"").replace(/¨T/g,"").replace(/¨D/g,"").replace(/[&+$,\/:;=?@"#{}|^¨~\[\]`\\*)(%.!'<>]/g,"").toLowerCase():t.rawHeaderId?b=b.replace(/ /g,"-").replace(/&amp;/g,"&").replace(/¨T/g,"\xA8").replace(/¨D/g,"$").replace(/["']/g,"-").toLowerCase():b=b.replace(/[^\w]/g,"").toLowerCase(),t.rawPrefixHeaderId&&(b=w+b),u.hashLinkCounts[b]?b=b+"-"+u.hashLinkCounts[b]++:u.hashLinkCounts[b]=1,b}return i=u.converter._dispatch("headers.after",i,t,u),i}),r.subParser("horizontalRule",function(i,t,u){"use strict";i=u.converter._dispatch("horizontalRule.before",i,t,u);var s=r.subParser("hashBlock")("<hr />",t,u);return i=i.replace(/^ {0,2}( ?-){3,}[ \t]*$/gm,s),i=i.replace(/^ {0,2}( ?\*){3,}[ \t]*$/gm,s),i=i.replace(/^ {0,2}( ?_){3,}[ \t]*$/gm,s),i=u.converter._dispatch("horizontalRule.after",i,t,u),i}),r.subParser("images",function(i,t,u){"use strict";i=u.converter._dispatch("images.before",i,t,u);var s=/!\[([^\]]*?)][ \t]*()\([ \t]?<?([\S]+?(?:\([\S]*?\)[\S]*?)?)>?(?: =([*\d]+[A-Za-z%]{0,4})x([*\d]+[A-Za-z%]{0,4}))?[ \t]*(?:(["'])([^"]*?)\6)?[ \t]?\)/g,p=/!\[([^\]]*?)][ \t]*()\([ \t]?<([^>]*)>(?: =([*\d]+[A-Za-z%]{0,4})x([*\d]+[A-Za-z%]{0,4}))?[ \t]*(?:(?:(["'])([^"]*?)\6))?[ \t]?\)/g,h=/!\[([^\]]*?)][ \t]*()\([ \t]?<?(data:.+?\/.+?;base64,[A-Za-z0-9+/=\n]+?)>?(?: =([*\d]+[A-Za-z%]{0,4})x([*\d]+[A-Za-z%]{0,4}))?[ \t]*(?:(["'])([^"]*?)\6)?[ \t]?\)/g,f=/!\[([^\]]*?)] ?(?:\n *)?\[([\s\S]*?)]()()()()()/g,g=/!\[([^\[\]]+)]()()()()()/g;function T(w,y,l,k,C,L,j,P){return k=k.replace(/\s/g,""),b(w,y,l,k,C,L,j,P)}function b(w,y,l,k,C,L,j,P){var q=u.gUrls,Y=u.gTitles,J=u.gDimensions;if(l=l.toLowerCase(),P||(P=""),w.search(/\(<?\s*>? ?(['"].*['"])?\)$/m)>-1)k="";else if(k===""||k===null)if((l===""||l===null)&&(l=y.toLowerCase().replace(/ ?\n/g," ")),k="#"+l,!r.helper.isUndefined(q[l]))k=q[l],r.helper.isUndefined(Y[l])||(P=Y[l]),r.helper.isUndefined(J[l])||(C=J[l].width,L=J[l].height);else return w;y=y.replace(/"/g,"&quot;").replace(r.helper.regexes.asteriskDashAndColon,r.helper.escapeCharactersCallback),k=k.replace(r.helper.regexes.asteriskDashAndColon,r.helper.escapeCharactersCallback);var D='<img src="'+k+'" alt="'+y+'"';return P&&r.helper.isString(P)&&(P=P.replace(/"/g,"&quot;").replace(r.helper.regexes.asteriskDashAndColon,r.helper.escapeCharactersCallback),D+=' title="'+P+'"'),C&&L&&(C=C==="*"?"auto":C,L=L==="*"?"auto":L,D+=' width="'+C+'"',D+=' height="'+L+'"'),D+=" />",D}return i=i.replace(f,b),i=i.replace(h,T),i=i.replace(p,b),i=i.replace(s,b),i=i.replace(g,b),i=u.converter._dispatch("images.after",i,t,u),i}),r.subParser("italicsAndBold",function(i,t,u){"use strict";i=u.converter._dispatch("italicsAndBold.before",i,t,u);function s(p,h,f){return h+p+f}return t.literalMidWordUnderscores?(i=i.replace(/\b___(\S[\s\S]*?)___\b/g,function(p,h){return s(h,"<strong><em>","</em></strong>")}),i=i.replace(/\b__(\S[\s\S]*?)__\b/g,function(p,h){return s(h,"<strong>","</strong>")}),i=i.replace(/\b_(\S[\s\S]*?)_\b/g,function(p,h){return s(h,"<em>","</em>")})):(i=i.replace(/___(\S[\s\S]*?)___/g,function(p,h){return/\S$/.test(h)?s(h,"<strong><em>","</em></strong>"):p}),i=i.replace(/__(\S[\s\S]*?)__/g,function(p,h){return/\S$/.test(h)?s(h,"<strong>","</strong>"):p}),i=i.replace(/_([^\s_][\s\S]*?)_/g,function(p,h){return/\S$/.test(h)?s(h,"<em>","</em>"):p})),t.literalMidWordAsterisks?(i=i.replace(/([^*]|^)\B\*\*\*(\S[\s\S]*?)\*\*\*\B(?!\*)/g,function(p,h,f){return s(f,h+"<strong><em>","</em></strong>")}),i=i.replace(/([^*]|^)\B\*\*(\S[\s\S]*?)\*\*\B(?!\*)/g,function(p,h,f){return s(f,h+"<strong>","</strong>")}),i=i.replace(/([^*]|^)\B\*(\S[\s\S]*?)\*\B(?!\*)/g,function(p,h,f){return s(f,h+"<em>","</em>")})):(i=i.replace(/\*\*\*(\S[\s\S]*?)\*\*\*/g,function(p,h){return/\S$/.test(h)?s(h,"<strong><em>","</em></strong>"):p}),i=i.replace(/\*\*(\S[\s\S]*?)\*\*/g,function(p,h){return/\S$/.test(h)?s(h,"<strong>","</strong>"):p}),i=i.replace(/\*([^\s*][\s\S]*?)\*/g,function(p,h){return/\S$/.test(h)?s(h,"<em>","</em>"):p})),i=u.converter._dispatch("italicsAndBold.after",i,t,u),i}),r.subParser("lists",function(i,t,u){"use strict";function s(f,g){u.gListLevel++,f=f.replace(/\n{2,}$/,`
`),f+="\xA80";var T=/(\n)?(^ {0,3})([*+-]|\d+[.])[ \t]+((\[(x|X| )?])?[ \t]*[^\r]+?(\n{1,2}))(?=\n*(¨0| {0,3}([*+-]|\d+[.])[ \t]+))/gm,b=/\n[ \t]*\n(?!¨0)/.test(f);return t.disableForced4SpacesIndentedSublists&&(T=/(\n)?(^ {0,3})([*+-]|\d+[.])[ \t]+((\[(x|X| )?])?[ \t]*[^\r]+?(\n{1,2}))(?=\n*(¨0|\2([*+-]|\d+[.])[ \t]+))/gm),f=f.replace(T,function(w,y,l,k,C,L,j){j=j&&j.trim()!=="";var P=r.subParser("outdent")(C,t,u),q="";return L&&t.tasklists&&(q=' class="task-list-item" style="list-style-type: none;"',P=P.replace(/^[ \t]*\[(x|X| )?]/m,function(){var Y='<input type="checkbox" disabled style="margin: 0px 0.35em 0.25em -1.6em; vertical-align: middle;"';return j&&(Y+=" checked"),Y+=">",Y})),P=P.replace(/^([-*+]|\d\.)[ \t]+[\S\n ]*/g,function(Y){return"\xA8A"+Y}),y||P.search(/\n{2,}/)>-1?(P=r.subParser("githubCodeBlocks")(P,t,u),P=r.subParser("blockGamut")(P,t,u)):(P=r.subParser("lists")(P,t,u),P=P.replace(/\n$/,""),P=r.subParser("hashHTMLBlocks")(P,t,u),P=P.replace(/\n\n+/g,`

`),b?P=r.subParser("paragraphs")(P,t,u):P=r.subParser("spanGamut")(P,t,u)),P=P.replace("\xA8A",""),P="<li"+q+">"+P+`</li>
`,P}),f=f.replace(/¨0/g,""),u.gListLevel--,g&&(f=f.replace(/\s+$/,"")),f}function p(f,g){if(g==="ol"){var T=f.match(/^ *(\d+)\./);if(T&&T[1]!=="1")return' start="'+T[1]+'"'}return""}function h(f,g,T){var b=t.disableForced4SpacesIndentedSublists?/^ ?\d+\.[ \t]/gm:/^ {0,3}\d+\.[ \t]/gm,w=t.disableForced4SpacesIndentedSublists?/^ ?[*+-][ \t]/gm:/^ {0,3}[*+-][ \t]/gm,y=g==="ul"?b:w,l="";if(f.search(y)!==-1)(function C(L){var j=L.search(y),P=p(f,g);j!==-1?(l+=`

<`+g+P+`>
`+s(L.slice(0,j),!!T)+"</"+g+`>
`,g=g==="ul"?"ol":"ul",y=g==="ul"?b:w,C(L.slice(j))):l+=`

<`+g+P+`>
`+s(L,!!T)+"</"+g+`>
`})(f);else{var k=p(f,g);l=`

<`+g+k+`>
`+s(f,!!T)+"</"+g+`>
`}return l}return i=u.converter._dispatch("lists.before",i,t,u),i+="\xA80",u.gListLevel?i=i.replace(/^(( {0,3}([*+-]|\d+[.])[ \t]+)[^\r]+?(¨0|\n{2,}(?=\S)(?![ \t]*(?:[*+-]|\d+[.])[ \t]+)))/gm,function(f,g,T){var b=T.search(/[*+-]/g)>-1?"ul":"ol";return h(g,b,!0)}):i=i.replace(/(\n\n|^\n?)(( {0,3}([*+-]|\d+[.])[ \t]+)[^\r]+?(¨0|\n{2,}(?=\S)(?![ \t]*(?:[*+-]|\d+[.])[ \t]+)))/gm,function(f,g,T,b){var w=b.search(/[*+-]/g)>-1?"ul":"ol";return h(T,w,!1)}),i=i.replace(/¨0/,""),i=u.converter._dispatch("lists.after",i,t,u),i}),r.subParser("metadata",function(i,t,u){"use strict";if(!t.metadata)return i;i=u.converter._dispatch("metadata.before",i,t,u);function s(p){u.metadata.raw=p,p=p.replace(/&/g,"&amp;").replace(/"/g,"&quot;"),p=p.replace(/\n {4}/g," "),p.replace(/^([\S ]+): +([\s\S]+?)$/gm,function(h,f,g){return u.metadata.parsed[f]=g,""})}return i=i.replace(/^\s*«««+(\S*?)\n([\s\S]+?)\n»»»+\n/,function(p,h,f){return s(f),"\xA8M"}),i=i.replace(/^\s*---+(\S*?)\n([\s\S]+?)\n---+\n/,function(p,h,f){return h&&(u.metadata.format=h),s(f),"\xA8M"}),i=i.replace(/¨M/g,""),i=u.converter._dispatch("metadata.after",i,t,u),i}),r.subParser("outdent",function(i,t,u){"use strict";return i=u.converter._dispatch("outdent.before",i,t,u),i=i.replace(/^(\t|[ ]{1,4})/gm,"\xA80"),i=i.replace(/¨0/g,""),i=u.converter._dispatch("outdent.after",i,t,u),i}),r.subParser("paragraphs",function(i,t,u){"use strict";i=u.converter._dispatch("paragraphs.before",i,t,u),i=i.replace(/^\n+/g,""),i=i.replace(/\n+$/g,"");for(var s=i.split(/\n{2,}/g),p=[],h=s.length,f=0;f<h;f++){var g=s[f];g.search(/¨(K|G)(\d+)\1/g)>=0?p.push(g):g.search(/\S/)>=0&&(g=r.subParser("spanGamut")(g,t,u),g=g.replace(/^([ \t]*)/g,"<p>"),g+="</p>",p.push(g))}for(h=p.length,f=0;f<h;f++){for(var T="",b=p[f],w=!1;/¨(K|G)(\d+)\1/.test(b);){var y=RegExp.$1,l=RegExp.$2;y==="K"?T=u.gHtmlBlocks[l]:w?T=r.subParser("encodeCode")(u.ghCodeBlocks[l].text,t,u):T=u.ghCodeBlocks[l].codeblock,T=T.replace(/\$/g,"$$$$"),b=b.replace(/(\n\n)?¨(K|G)\d+\2(\n\n)?/,T),/^<pre\b[^>]*>\s*<code\b[^>]*>/.test(b)&&(w=!0)}p[f]=b}return i=p.join(`
`),i=i.replace(/^\n+/g,""),i=i.replace(/\n+$/g,""),u.converter._dispatch("paragraphs.after",i,t,u)}),r.subParser("runExtension",function(i,t,u,s){"use strict";if(i.filter)t=i.filter(t,s.converter,u);else if(i.regex){var p=i.regex;p instanceof RegExp||(p=new RegExp(p,"g")),t=t.replace(p,i.replace)}return t}),r.subParser("spanGamut",function(i,t,u){"use strict";return i=u.converter._dispatch("spanGamut.before",i,t,u),i=r.subParser("codeSpans")(i,t,u),i=r.subParser("escapeSpecialCharsWithinTagAttributes")(i,t,u),i=r.subParser("encodeBackslashEscapes")(i,t,u),i=r.subParser("images")(i,t,u),i=r.subParser("anchors")(i,t,u),i=r.subParser("autoLinks")(i,t,u),i=r.subParser("simplifiedAutoLinks")(i,t,u),i=r.subParser("emoji")(i,t,u),i=r.subParser("underline")(i,t,u),i=r.subParser("italicsAndBold")(i,t,u),i=r.subParser("strikethrough")(i,t,u),i=r.subParser("ellipsis")(i,t,u),i=r.subParser("hashHTMLSpans")(i,t,u),i=r.subParser("encodeAmpsAndAngles")(i,t,u),t.simpleLineBreaks?/\n\n¨K/.test(i)||(i=i.replace(/\n+/g,`<br />
`)):i=i.replace(/  +\n/g,`<br />
`),i=u.converter._dispatch("spanGamut.after",i,t,u),i}),r.subParser("strikethrough",function(i,t,u){"use strict";function s(p){return t.simplifiedAutoLink&&(p=r.subParser("simplifiedAutoLinks")(p,t,u)),"<del>"+p+"</del>"}return t.strikethrough&&(i=u.converter._dispatch("strikethrough.before",i,t,u),i=i.replace(/(?:~){2}([\s\S]+?)(?:~){2}/g,function(p,h){return s(h)}),i=u.converter._dispatch("strikethrough.after",i,t,u)),i}),r.subParser("stripLinkDefinitions",function(i,t,u){"use strict";var s=/^ {0,3}\[(.+)]:[ \t]*\n?[ \t]*<?([^>\s]+)>?(?: =([*\d]+[A-Za-z%]{0,4})x([*\d]+[A-Za-z%]{0,4}))?[ \t]*\n?[ \t]*(?:(\n*)["|'(](.+?)["|')][ \t]*)?(?:\n+|(?=¨0))/gm,p=/^ {0,3}\[(.+)]:[ \t]*\n?[ \t]*<?(data:.+?\/.+?;base64,[A-Za-z0-9+/=\n]+?)>?(?: =([*\d]+[A-Za-z%]{0,4})x([*\d]+[A-Za-z%]{0,4}))?[ \t]*\n?[ \t]*(?:(\n*)["|'(](.+?)["|')][ \t]*)?(?:\n\n|(?=¨0)|(?=\n\[))/gm;i+="\xA80";var h=function(f,g,T,b,w,y,l){return g=g.toLowerCase(),T.match(/^data:.+?\/.+?;base64,/)?u.gUrls[g]=T.replace(/\s/g,""):u.gUrls[g]=r.subParser("encodeAmpsAndAngles")(T,t,u),y?y+l:(l&&(u.gTitles[g]=l.replace(/"|'/g,"&quot;")),t.parseImgDimensions&&b&&w&&(u.gDimensions[g]={width:b,height:w}),"")};return i=i.replace(p,h),i=i.replace(s,h),i=i.replace(/¨0/,""),i}),r.subParser("tables",function(i,t,u){"use strict";if(!t.tables)return i;var s=/^ {0,3}\|?.+\|.+\n {0,3}\|?[ \t]*:?[ \t]*(?:[-=]){2,}[ \t]*:?[ \t]*\|[ \t]*:?[ \t]*(?:[-=]){2,}[\s\S]+?(?:\n\n|¨0)/gm,p=/^ {0,3}\|.+\|[ \t]*\n {0,3}\|[ \t]*:?[ \t]*(?:[-=]){2,}[ \t]*:?[ \t]*\|[ \t]*\n( {0,3}\|.+\|[ \t]*\n)*(?:\n|¨0)/gm;function h(w){return/^:[ \t]*--*$/.test(w)?' style="text-align:left;"':/^--*[ \t]*:[ \t]*$/.test(w)?' style="text-align:right;"':/^:[ \t]*--*[ \t]*:$/.test(w)?' style="text-align:center;"':""}function f(w,y){var l="";return w=w.trim(),(t.tablesHeaderId||t.tableHeaderId)&&(l=' id="'+w.replace(/ /g,"_").toLowerCase()+'"'),w=r.subParser("spanGamut")(w,t,u),"<th"+l+y+">"+w+`</th>
`}function g(w,y){var l=r.subParser("spanGamut")(w,t,u);return"<td"+y+">"+l+`</td>
`}function T(w,y){for(var l=`<table>
<thead>
<tr>
`,k=w.length,C=0;C<k;++C)l+=w[C];for(l+=`</tr>
</thead>
<tbody>
`,C=0;C<y.length;++C){l+=`<tr>
`;for(var L=0;L<k;++L)l+=y[C][L];l+=`</tr>
`}return l+=`</tbody>
</table>
`,l}function b(w){var y,l=w.split(`
`);for(y=0;y<l.length;++y)/^ {0,3}\|/.test(l[y])&&(l[y]=l[y].replace(/^ {0,3}\|/,"")),/\|[ \t]*$/.test(l[y])&&(l[y]=l[y].replace(/\|[ \t]*$/,"")),l[y]=r.subParser("codeSpans")(l[y],t,u);var k=l[0].split("|").map(function(D){return D.trim()}),C=l[1].split("|").map(function(D){return D.trim()}),L=[],j=[],P=[],q=[];for(l.shift(),l.shift(),y=0;y<l.length;++y)l[y].trim()!==""&&L.push(l[y].split("|").map(function(D){return D.trim()}));if(k.length<C.length)return w;for(y=0;y<C.length;++y)P.push(h(C[y]));for(y=0;y<k.length;++y)r.helper.isUndefined(P[y])&&(P[y]=""),j.push(f(k[y],P[y]));for(y=0;y<L.length;++y){for(var Y=[],J=0;J<j.length;++J)r.helper.isUndefined(L[y][J]),Y.push(g(L[y][J],P[J]));q.push(Y)}return T(j,q)}return i=u.converter._dispatch("tables.before",i,t,u),i=i.replace(/\\(\|)/g,r.helper.escapeCharactersCallback),i=i.replace(s,b),i=i.replace(p,b),i=u.converter._dispatch("tables.after",i,t,u),i}),r.subParser("underline",function(i,t,u){"use strict";return t.underline&&(i=u.converter._dispatch("underline.before",i,t,u),t.literalMidWordUnderscores?(i=i.replace(/\b___(\S[\s\S]*?)___\b/g,function(s,p){return"<u>"+p+"</u>"}),i=i.replace(/\b__(\S[\s\S]*?)__\b/g,function(s,p){return"<u>"+p+"</u>"})):(i=i.replace(/___(\S[\s\S]*?)___/g,function(s,p){return/\S$/.test(p)?"<u>"+p+"</u>":s}),i=i.replace(/__(\S[\s\S]*?)__/g,function(s,p){return/\S$/.test(p)?"<u>"+p+"</u>":s})),i=i.replace(/(_)/g,r.helper.escapeCharactersCallback),i=u.converter._dispatch("underline.after",i,t,u)),i}),r.subParser("unescapeSpecialChars",function(i,t,u){"use strict";return i=u.converter._dispatch("unescapeSpecialChars.before",i,t,u),i=i.replace(/¨E(\d+)E/g,function(s,p){var h=parseInt(p);return String.fromCharCode(h)}),i=u.converter._dispatch("unescapeSpecialChars.after",i,t,u),i}),r.subParser("makeMarkdown.blockquote",function(i,t){"use strict";var u="";if(i.hasChildNodes())for(var s=i.childNodes,p=s.length,h=0;h<p;++h){var f=r.subParser("makeMarkdown.node")(s[h],t);f!==""&&(u+=f)}return u=u.trim(),u="> "+u.split(`
`).join(`
> `),u}),r.subParser("makeMarkdown.codeBlock",function(i,t){"use strict";var u=i.getAttribute("language"),s=i.getAttribute("precodenum");return"```"+u+`
`+t.preList[s]+"\n```"}),r.subParser("makeMarkdown.codeSpan",function(i){"use strict";return"`"+i.innerHTML+"`"}),r.subParser("makeMarkdown.emphasis",function(i,t){"use strict";var u="";if(i.hasChildNodes()){u+="*";for(var s=i.childNodes,p=s.length,h=0;h<p;++h)u+=r.subParser("makeMarkdown.node")(s[h],t);u+="*"}return u}),r.subParser("makeMarkdown.header",function(i,t,u){"use strict";var s=new Array(u+1).join("#"),p="";if(i.hasChildNodes()){p=s+" ";for(var h=i.childNodes,f=h.length,g=0;g<f;++g)p+=r.subParser("makeMarkdown.node")(h[g],t)}return p}),r.subParser("makeMarkdown.hr",function(){"use strict";return"---"}),r.subParser("makeMarkdown.image",function(i){"use strict";var t="";return i.hasAttribute("src")&&(t+="!["+i.getAttribute("alt")+"](",t+="<"+i.getAttribute("src")+">",i.hasAttribute("width")&&i.hasAttribute("height")&&(t+=" ="+i.getAttribute("width")+"x"+i.getAttribute("height")),i.hasAttribute("title")&&(t+=' "'+i.getAttribute("title")+'"'),t+=")"),t}),r.subParser("makeMarkdown.links",function(i,t){"use strict";var u="";if(i.hasChildNodes()&&i.hasAttribute("href")){var s=i.childNodes,p=s.length;u="[";for(var h=0;h<p;++h)u+=r.subParser("makeMarkdown.node")(s[h],t);u+="](",u+="<"+i.getAttribute("href")+">",i.hasAttribute("title")&&(u+=' "'+i.getAttribute("title")+'"'),u+=")"}return u}),r.subParser("makeMarkdown.list",function(i,t,u){"use strict";var s="";if(!i.hasChildNodes())return"";for(var p=i.childNodes,h=p.length,f=i.getAttribute("start")||1,g=0;g<h;++g)if(!(typeof p[g].tagName=="undefined"||p[g].tagName.toLowerCase()!=="li")){var T="";u==="ol"?T=f.toString()+". ":T="- ",s+=T+r.subParser("makeMarkdown.listItem")(p[g],t),++f}return s+=`
<!-- -->
`,s.trim()}),r.subParser("makeMarkdown.listItem",function(i,t){"use strict";for(var u="",s=i.childNodes,p=s.length,h=0;h<p;++h)u+=r.subParser("makeMarkdown.node")(s[h],t);return/\n$/.test(u)?u=u.split(`
`).join(`
    `).replace(/^ {4}$/gm,"").replace(/\n\n+/g,`

`):u+=`
`,u}),r.subParser("makeMarkdown.node",function(i,t,u){"use strict";u=u||!1;var s="";if(i.nodeType===3)return r.subParser("makeMarkdown.txt")(i,t);if(i.nodeType===8)return"<!--"+i.data+`-->

`;if(i.nodeType!==1)return"";var p=i.tagName.toLowerCase();switch(p){case"h1":u||(s=r.subParser("makeMarkdown.header")(i,t,1)+`

`);break;case"h2":u||(s=r.subParser("makeMarkdown.header")(i,t,2)+`

`);break;case"h3":u||(s=r.subParser("makeMarkdown.header")(i,t,3)+`

`);break;case"h4":u||(s=r.subParser("makeMarkdown.header")(i,t,4)+`

`);break;case"h5":u||(s=r.subParser("makeMarkdown.header")(i,t,5)+`

`);break;case"h6":u||(s=r.subParser("makeMarkdown.header")(i,t,6)+`

`);break;case"p":u||(s=r.subParser("makeMarkdown.paragraph")(i,t)+`

`);break;case"blockquote":u||(s=r.subParser("makeMarkdown.blockquote")(i,t)+`

`);break;case"hr":u||(s=r.subParser("makeMarkdown.hr")(i,t)+`

`);break;case"ol":u||(s=r.subParser("makeMarkdown.list")(i,t,"ol")+`

`);break;case"ul":u||(s=r.subParser("makeMarkdown.list")(i,t,"ul")+`

`);break;case"precode":u||(s=r.subParser("makeMarkdown.codeBlock")(i,t)+`

`);break;case"pre":u||(s=r.subParser("makeMarkdown.pre")(i,t)+`

`);break;case"table":u||(s=r.subParser("makeMarkdown.table")(i,t)+`

`);break;case"code":s=r.subParser("makeMarkdown.codeSpan")(i,t);break;case"em":case"i":s=r.subParser("makeMarkdown.emphasis")(i,t);break;case"strong":case"b":s=r.subParser("makeMarkdown.strong")(i,t);break;case"del":s=r.subParser("makeMarkdown.strikethrough")(i,t);break;case"a":s=r.subParser("makeMarkdown.links")(i,t);break;case"img":s=r.subParser("makeMarkdown.image")(i,t);break;default:s=i.outerHTML+`

`}return s}),r.subParser("makeMarkdown.paragraph",function(i,t){"use strict";var u="";if(i.hasChildNodes())for(var s=i.childNodes,p=s.length,h=0;h<p;++h)u+=r.subParser("makeMarkdown.node")(s[h],t);return u=u.trim(),u}),r.subParser("makeMarkdown.pre",function(i,t){"use strict";var u=i.getAttribute("prenum");return"<pre>"+t.preList[u]+"</pre>"}),r.subParser("makeMarkdown.strikethrough",function(i,t){"use strict";var u="";if(i.hasChildNodes()){u+="~~";for(var s=i.childNodes,p=s.length,h=0;h<p;++h)u+=r.subParser("makeMarkdown.node")(s[h],t);u+="~~"}return u}),r.subParser("makeMarkdown.strong",function(i,t){"use strict";var u="";if(i.hasChildNodes()){u+="**";for(var s=i.childNodes,p=s.length,h=0;h<p;++h)u+=r.subParser("makeMarkdown.node")(s[h],t);u+="**"}return u}),r.subParser("makeMarkdown.table",function(i,t){"use strict";var u="",s=[[],[]],p=i.querySelectorAll("thead>tr>th"),h=i.querySelectorAll("tbody>tr"),f,g;for(f=0;f<p.length;++f){var T=r.subParser("makeMarkdown.tableCell")(p[f],t),b="---";if(p[f].hasAttribute("style")){var w=p[f].getAttribute("style").toLowerCase().replace(/\s/g,"");switch(w){case"text-align:left;":b=":---";break;case"text-align:right;":b="---:";break;case"text-align:center;":b=":---:";break}}s[0][f]=T.trim(),s[1][f]=b}for(f=0;f<h.length;++f){var y=s.push([])-1,l=h[f].getElementsByTagName("td");for(g=0;g<p.length;++g){var k=" ";typeof l[g]!="undefined"&&(k=r.subParser("makeMarkdown.tableCell")(l[g],t)),s[y].push(k)}}var C=3;for(f=0;f<s.length;++f)for(g=0;g<s[f].length;++g){var L=s[f][g].length;L>C&&(C=L)}for(f=0;f<s.length;++f){for(g=0;g<s[f].length;++g)f===1?s[f][g].slice(-1)===":"?s[f][g]=r.helper.padEnd(s[f][g].slice(-1),C-1,"-")+":":s[f][g]=r.helper.padEnd(s[f][g],C,"-"):s[f][g]=r.helper.padEnd(s[f][g],C);u+="| "+s[f].join(" | ")+` |
`}return u.trim()}),r.subParser("makeMarkdown.tableCell",function(i,t){"use strict";var u="";if(!i.hasChildNodes())return"";for(var s=i.childNodes,p=s.length,h=0;h<p;++h)u+=r.subParser("makeMarkdown.node")(s[h],t,!0);return u.trim()}),r.subParser("makeMarkdown.txt",function(i){"use strict";var t=i.nodeValue;return t=t.replace(/ +/g," "),t=t.replace(/¨NBSP;/g," "),t=r.helper.unescapeHTMLEntities(t),t=t.replace(/([*_~|`])/g,"\\$1"),t=t.replace(/^(\s*)>/g,"\\$1>"),t=t.replace(/^#/gm,"\\#"),t=t.replace(/^(\s*)([-=]{3,})(\s*)$/,"$1\\$2$3"),t=t.replace(/^( {0,3}\d+)\./gm,"$1\\."),t=t.replace(/^( {0,3})([+-])/gm,"$1\\$2"),t=t.replace(/]([\s]*)\(/g,"\\]$1\\("),t=t.replace(/^ {0,3}\[([\S \t]*?)]:/gm,"\\[$1]:"),t});var ue=this;typeof define=="function"&&define.amd?define(function(){"use strict";return r}):typeof Ue!="undefined"&&Ue.exports?Ue.exports=r:ue.showdown=r}).call(Ur)});var pe=H((hl,fe)=>{"use strict";function Gr(e){return typeof e=="undefined"||e===null}function Vu(e){return typeof e=="object"&&e!==null}function Wu(e){return Array.isArray(e)?e:Gr(e)?[]:[e]}function Gu(e,n){var r,a,d,o;if(n)for(o=Object.keys(n),r=0,a=o.length;r<a;r+=1)d=o[r],e[d]=n[d];return e}function Yu(e,n){var r="",a;for(a=0;a<n;a+=1)r+=e;return r}function Ku(e){return e===0&&Number.NEGATIVE_INFINITY===1/e}fe.exports.isNothing=Gr;fe.exports.isObject=Vu;fe.exports.toArray=Wu;fe.exports.repeat=Yu;fe.exports.isNegativeZero=Ku;fe.exports.extend=Gu});var ve=H((gl,Yr)=>{"use strict";function Me(e,n){Error.call(this),this.name="YAMLException",this.reason=e,this.mark=n,this.message=(this.reason||"(unknown reason)")+(this.mark?" "+this.mark.toString():""),Error.captureStackTrace?Error.captureStackTrace(this,this.constructor):this.stack=new Error().stack||""}Me.prototype=Object.create(Error.prototype);Me.prototype.constructor=Me;Me.prototype.toString=function(n){var r=this.name+": ";return r+=this.reason||"(unknown reason)",!n&&this.mark&&(r+=" "+this.mark.toString()),r};Yr.exports=Me});var Zr=H((_l,Jr)=>{"use strict";var Kr=pe();function pr(e,n,r,a,d){this.name=e,this.buffer=n,this.position=r,this.line=a,this.column=d}pr.prototype.getSnippet=function(n,r){var a,d,o,c,m;if(!this.buffer)return null;for(n=n||4,r=r||75,a="",d=this.position;d>0&&`\0\r
\x85\u2028\u2029`.indexOf(this.buffer.charAt(d-1))===-1;)if(d-=1,this.position-d>r/2-1){a=" ... ",d+=5;break}for(o="",c=this.position;c<this.buffer.length&&`\0\r
\x85\u2028\u2029`.indexOf(this.buffer.charAt(c))===-1;)if(c+=1,c-this.position>r/2-1){o=" ... ",c-=5;break}return m=this.buffer.slice(d,c),Kr.repeat(" ",n)+a+m+o+`
`+Kr.repeat(" ",n+this.position-d+a.length)+"^"};pr.prototype.toString=function(n){var r,a="";return this.name&&(a+='in "'+this.name+'" '),a+="at line "+(this.line+1)+", column "+(this.column+1),n||(r=this.getSnippet(),r&&(a+=`:
`+r)),a};Jr.exports=pr});var V=H((wl,Xr)=>{"use strict";var Qr=ve(),Ju=["kind","resolve","construct","instanceOf","predicate","represent","defaultStyle","styleAliases"],Zu=["scalar","sequence","mapping"];function Qu(e){var n={};return e!==null&&Object.keys(e).forEach(function(r){e[r].forEach(function(a){n[String(a)]=r})}),n}function Xu(e,n){if(n=n||{},Object.keys(n).forEach(function(r){if(Ju.indexOf(r)===-1)throw new Qr('Unknown option "'+r+'" is met in definition of "'+e+'" YAML type.')}),this.tag=e,this.kind=n.kind||null,this.resolve=n.resolve||function(){return!0},this.construct=n.construct||function(r){return r},this.instanceOf=n.instanceOf||null,this.predicate=n.predicate||null,this.represent=n.represent||null,this.defaultStyle=n.defaultStyle||null,this.styleAliases=Qu(n.styleAliases||null),Zu.indexOf(this.kind)===-1)throw new Qr('Unknown kind "'+this.kind+'" is specified for "'+e+'" YAML type.')}Xr.exports=Xu});var me=H((bl,rn)=>{"use strict";var en=pe(),Ge=ve(),ea=V();function mr(e,n,r){var a=[];return e.include.forEach(function(d){r=mr(d,n,r)}),e[n].forEach(function(d){r.forEach(function(o,c){o.tag===d.tag&&o.kind===d.kind&&a.push(c)}),r.push(d)}),r.filter(function(d,o){return a.indexOf(o)===-1})}function ra(){var e={scalar:{},sequence:{},mapping:{},fallback:{}},n,r;function a(d){e[d.kind][d.tag]=e.fallback[d.tag]=d}for(n=0,r=arguments.length;n<r;n+=1)arguments[n].forEach(a);return e}function ye(e){this.include=e.include||[],this.implicit=e.implicit||[],this.explicit=e.explicit||[],this.implicit.forEach(function(n){if(n.loadKind&&n.loadKind!=="scalar")throw new Ge("There is a non-scalar type in the implicit list of a schema. Implicit resolving of such types is not supported.")}),this.compiledImplicit=mr(this,"implicit",[]),this.compiledExplicit=mr(this,"explicit",[]),this.compiledTypeMap=ra(this.compiledImplicit,this.compiledExplicit)}ye.DEFAULT=null;ye.create=function(){var n,r;switch(arguments.length){case 1:n=ye.DEFAULT,r=arguments[0];break;case 2:n=arguments[0],r=arguments[1];break;default:throw new Ge("Wrong number of arguments for Schema.create function")}if(n=en.toArray(n),r=en.toArray(r),!n.every(function(a){return a instanceof ye}))throw new Ge("Specified list of super schemas (or a single Schema object) contains a non-Schema object.");if(!r.every(function(a){return a instanceof ea}))throw new Ge("Specified list of YAML types (or a single Type object) contains a non-Type object.");return new ye({include:n,explicit:r})};rn.exports=ye});var un=H((vl,nn)=>{"use strict";var na=V();nn.exports=new na("tag:yaml.org,2002:str",{kind:"scalar",construct:function(e){return e!==null?e:""}})});var tn=H((yl,an)=>{"use strict";var ia=V();an.exports=new ia("tag:yaml.org,2002:seq",{kind:"sequence",construct:function(e){return e!==null?e:[]}})});var on=H((kl,dn)=>{"use strict";var ua=V();dn.exports=new ua("tag:yaml.org,2002:map",{kind:"mapping",construct:function(e){return e!==null?e:{}}})});var Ye=H((Tl,cn)=>{"use strict";var aa=me();cn.exports=new aa({explicit:[un(),tn(),on()]})});var ln=H((xl,sn)=>{"use strict";var ta=V();function da(e){if(e===null)return!0;var n=e.length;return n===1&&e==="~"||n===4&&(e==="null"||e==="Null"||e==="NULL")}function oa(){return null}function ca(e){return e===null}sn.exports=new ta("tag:yaml.org,2002:null",{kind:"scalar",resolve:da,construct:oa,predicate:ca,represent:{canonical:function(){return"~"},lowercase:function(){return"null"},uppercase:function(){return"NULL"},camelcase:function(){return"Null"}},defaultStyle:"lowercase"})});var pn=H((Al,fn)=>{"use strict";var sa=V();function la(e){if(e===null)return!1;var n=e.length;return n===4&&(e==="true"||e==="True"||e==="TRUE")||n===5&&(e==="false"||e==="False"||e==="FALSE")}function fa(e){return e==="true"||e==="True"||e==="TRUE"}function pa(e){return Object.prototype.toString.call(e)==="[object Boolean]"}fn.exports=new sa("tag:yaml.org,2002:bool",{kind:"scalar",resolve:la,construct:fa,predicate:pa,represent:{lowercase:function(e){return e?"true":"false"},uppercase:function(e){return e?"TRUE":"FALSE"},camelcase:function(e){return e?"True":"False"}},defaultStyle:"lowercase"})});var hn=H((El,mn)=>{"use strict";var ma=pe(),ha=V();function ga(e){return 48<=e&&e<=57||65<=e&&e<=70||97<=e&&e<=102}function _a(e){return 48<=e&&e<=55}function wa(e){return 48<=e&&e<=57}function ba(e){if(e===null)return!1;var n=e.length,r=0,a=!1,d;if(!n)return!1;if(d=e[r],(d==="-"||d==="+")&&(d=e[++r]),d==="0"){if(r+1===n)return!0;if(d=e[++r],d==="b"){for(r++;r<n;r++)if(d=e[r],d!=="_"){if(d!=="0"&&d!=="1")return!1;a=!0}return a&&d!=="_"}if(d==="x"){for(r++;r<n;r++)if(d=e[r],d!=="_"){if(!ga(e.charCodeAt(r)))return!1;a=!0}return a&&d!=="_"}for(;r<n;r++)if(d=e[r],d!=="_"){if(!_a(e.charCodeAt(r)))return!1;a=!0}return a&&d!=="_"}if(d==="_")return!1;for(;r<n;r++)if(d=e[r],d!=="_"){if(d===":")break;if(!wa(e.charCodeAt(r)))return!1;a=!0}return!a||d==="_"?!1:d!==":"?!0:/^(:[0-5]?[0-9])+$/.test(e.slice(r))}function va(e){var n=e,r=1,a,d,o=[];return n.indexOf("_")!==-1&&(n=n.replace(/_/g,"")),a=n[0],(a==="-"||a==="+")&&(a==="-"&&(r=-1),n=n.slice(1),a=n[0]),n==="0"?0:a==="0"?n[1]==="b"?r*parseInt(n.slice(2),2):n[1]==="x"?r*parseInt(n,16):r*parseInt(n,8):n.indexOf(":")!==-1?(n.split(":").forEach(function(c){o.unshift(parseInt(c,10))}),n=0,d=1,o.forEach(function(c){n+=c*d,d*=60}),r*n):r*parseInt(n,10)}function ya(e){return Object.prototype.toString.call(e)==="[object Number]"&&e%1==0&&!ma.isNegativeZero(e)}mn.exports=new ha("tag:yaml.org,2002:int",{kind:"scalar",resolve:ba,construct:va,predicate:ya,represent:{binary:function(e){return e>=0?"0b"+e.toString(2):"-0b"+e.toString(2).slice(1)},octal:function(e){return e>=0?"0"+e.toString(8):"-0"+e.toString(8).slice(1)},decimal:function(e){return e.toString(10)},hexadecimal:function(e){return e>=0?"0x"+e.toString(16).toUpperCase():"-0x"+e.toString(16).toUpperCase().slice(1)}},defaultStyle:"decimal",styleAliases:{binary:[2,"bin"],octal:[8,"oct"],decimal:[10,"dec"],hexadecimal:[16,"hex"]}})});var wn=H((Cl,_n)=>{"use strict";var gn=pe(),ka=V(),Ta=new RegExp("^(?:[-+]?(?:0|[1-9][0-9_]*)(?:\\.[0-9_]*)?(?:[eE][-+]?[0-9]+)?|\\.[0-9_]+(?:[eE][-+]?[0-9]+)?|[-+]?[0-9][0-9_]*(?::[0-5]?[0-9])+\\.[0-9_]*|[-+]?\\.(?:inf|Inf|INF)|\\.(?:nan|NaN|NAN))$");function xa(e){return!(e===null||!Ta.test(e)||e[e.length-1]==="_")}function Aa(e){var n,r,a,d;return n=e.replace(/_/g,"").toLowerCase(),r=n[0]==="-"?-1:1,d=[],"+-".indexOf(n[0])>=0&&(n=n.slice(1)),n===".inf"?r===1?Number.POSITIVE_INFINITY:Number.NEGATIVE_INFINITY:n===".nan"?NaN:n.indexOf(":")>=0?(n.split(":").forEach(function(o){d.unshift(parseFloat(o,10))}),n=0,a=1,d.forEach(function(o){n+=o*a,a*=60}),r*n):r*parseFloat(n,10)}var Ea=/^[-+]?[0-9]+e/;function Ca(e,n){var r;if(isNaN(e))switch(n){case"lowercase":return".nan";case"uppercase":return".NAN";case"camelcase":return".NaN"}else if(Number.POSITIVE_INFINITY===e)switch(n){case"lowercase":return".inf";case"uppercase":return".INF";case"camelcase":return".Inf"}else if(Number.NEGATIVE_INFINITY===e)switch(n){case"lowercase":return"-.inf";case"uppercase":return"-.INF";case"camelcase":return"-.Inf"}else if(gn.isNegativeZero(e))return"-0.0";return r=e.toString(10),Ea.test(r)?r.replace("e",".e"):r}function Sa(e){return Object.prototype.toString.call(e)==="[object Number]"&&(e%1!=0||gn.isNegativeZero(e))}_n.exports=new ka("tag:yaml.org,2002:float",{kind:"scalar",resolve:xa,construct:Aa,predicate:Sa,represent:Ca,defaultStyle:"lowercase"})});var hr=H((Sl,bn)=>{"use strict";var La=me();bn.exports=new La({include:[Ye()],implicit:[ln(),pn(),hn(),wn()]})});var gr=H((Ll,vn)=>{"use strict";var Pa=me();vn.exports=new Pa({include:[hr()]})});var xn=H((Pl,Tn)=>{"use strict";var Ma=V(),yn=new RegExp("^([0-9][0-9][0-9][0-9])-([0-9][0-9])-([0-9][0-9])$"),kn=new RegExp("^([0-9][0-9][0-9][0-9])-([0-9][0-9]?)-([0-9][0-9]?)(?:[Tt]|[ \\t]+)([0-9][0-9]?):([0-9][0-9]):([0-9][0-9])(?:\\.([0-9]*))?(?:[ \\t]*(Z|([-+])([0-9][0-9]?)(?::([0-9][0-9]))?))?$");function Ra(e){return e===null?!1:yn.exec(e)!==null||kn.exec(e)!==null}function Ia(e){var n,r,a,d,o,c,m,_=0,x=null,S,A,E;if(n=yn.exec(e),n===null&&(n=kn.exec(e)),n===null)throw new Error("Date resolve error");if(r=+n[1],a=+n[2]-1,d=+n[3],!n[4])return new Date(Date.UTC(r,a,d));if(o=+n[4],c=+n[5],m=+n[6],n[7]){for(_=n[7].slice(0,3);_.length<3;)_+="0";_=+_}return n[9]&&(S=+n[10],A=+(n[11]||0),x=(S*60+A)*6e4,n[9]==="-"&&(x=-x)),E=new Date(Date.UTC(r,a,d,o,c,m,_)),x&&E.setTime(E.getTime()-x),E}function Fa(e){return e.toISOString()}Tn.exports=new Ma("tag:yaml.org,2002:timestamp",{kind:"scalar",resolve:Ra,construct:Ia,instanceOf:Date,represent:Fa})});var En=H((Ml,An)=>{"use strict";var ja=V();function Ha(e){return e==="<<"||e===null}An.exports=new ja("tag:yaml.org,2002:merge",{kind:"scalar",resolve:Ha})});var Ln=H((Rl,Sn)=>{"use strict";var he;try{Cn=require,he=Cn("buffer").Buffer}catch(e){}var Cn,za=V(),_r=`ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=
\r`;function Oa(e){if(e===null)return!1;var n,r,a=0,d=e.length,o=_r;for(r=0;r<d;r++)if(n=o.indexOf(e.charAt(r)),!(n>64)){if(n<0)return!1;a+=6}return a%8==0}function Da(e){var n,r,a=e.replace(/[\r\n=]/g,""),d=a.length,o=_r,c=0,m=[];for(n=0;n<d;n++)n%4==0&&n&&(m.push(c>>16&255),m.push(c>>8&255),m.push(c&255)),c=c<<6|o.indexOf(a.charAt(n));return r=d%4*6,r===0?(m.push(c>>16&255),m.push(c>>8&255),m.push(c&255)):r===18?(m.push(c>>10&255),m.push(c>>2&255)):r===12&&m.push(c>>4&255),he?he.from?he.from(m):new he(m):m}function Na(e){var n="",r=0,a,d,o=e.length,c=_r;for(a=0;a<o;a++)a%3==0&&a&&(n+=c[r>>18&63],n+=c[r>>12&63],n+=c[r>>6&63],n+=c[r&63]),r=(r<<8)+e[a];return d=o%3,d===0?(n+=c[r>>18&63],n+=c[r>>12&63],n+=c[r>>6&63],n+=c[r&63]):d===2?(n+=c[r>>10&63],n+=c[r>>4&63],n+=c[r<<2&63],n+=c[64]):d===1&&(n+=c[r>>2&63],n+=c[r<<4&63],n+=c[64],n+=c[64]),n}function Ba(e){return he&&he.isBuffer(e)}Sn.exports=new za("tag:yaml.org,2002:binary",{kind:"scalar",resolve:Oa,construct:Da,predicate:Ba,represent:Na})});var Mn=H((Il,Pn)=>{"use strict";var qa=V(),$a=Object.prototype.hasOwnProperty,Ua=Object.prototype.toString;function Va(e){if(e===null)return!0;var n=[],r,a,d,o,c,m=e;for(r=0,a=m.length;r<a;r+=1){if(d=m[r],c=!1,Ua.call(d)!=="[object Object]")return!1;for(o in d)if($a.call(d,o))if(!c)c=!0;else return!1;if(!c)return!1;if(n.indexOf(o)===-1)n.push(o);else return!1}return!0}function Wa(e){return e!==null?e:[]}Pn.exports=new qa("tag:yaml.org,2002:omap",{kind:"sequence",resolve:Va,construct:Wa})});var In=H((Fl,Rn)=>{"use strict";var Ga=V(),Ya=Object.prototype.toString;function Ka(e){if(e===null)return!0;var n,r,a,d,o,c=e;for(o=new Array(c.length),n=0,r=c.length;n<r;n+=1){if(a=c[n],Ya.call(a)!=="[object Object]"||(d=Object.keys(a),d.length!==1))return!1;o[n]=[d[0],a[d[0]]]}return!0}function Ja(e){if(e===null)return[];var n,r,a,d,o,c=e;for(o=new Array(c.length),n=0,r=c.length;n<r;n+=1)a=c[n],d=Object.keys(a),o[n]=[d[0],a[d[0]]];return o}Rn.exports=new Ga("tag:yaml.org,2002:pairs",{kind:"sequence",resolve:Ka,construct:Ja})});var jn=H((jl,Fn)=>{"use strict";var Za=V(),Qa=Object.prototype.hasOwnProperty;function Xa(e){if(e===null)return!0;var n,r=e;for(n in r)if(Qa.call(r,n)&&r[n]!==null)return!1;return!0}function et(e){return e!==null?e:{}}Fn.exports=new Za("tag:yaml.org,2002:set",{kind:"mapping",resolve:Xa,construct:et})});var ke=H((Hl,Hn)=>{"use strict";var rt=me();Hn.exports=new rt({include:[gr()],implicit:[xn(),En()],explicit:[Ln(),Mn(),In(),jn()]})});var On=H((zl,zn)=>{"use strict";var nt=V();function it(){return!0}function ut(){}function at(){return""}function tt(e){return typeof e=="undefined"}zn.exports=new nt("tag:yaml.org,2002:js/undefined",{kind:"scalar",resolve:it,construct:ut,predicate:tt,represent:at})});var Nn=H((Ol,Dn)=>{"use strict";var dt=V();function ot(e){if(e===null||e.length===0)return!1;var n=e,r=/\/([gim]*)$/.exec(e),a="";return!(n[0]==="/"&&(r&&(a=r[1]),a.length>3||n[n.length-a.length-1]!=="/"))}function ct(e){var n=e,r=/\/([gim]*)$/.exec(e),a="";return n[0]==="/"&&(r&&(a=r[1]),n=n.slice(1,n.length-a.length-1)),new RegExp(n,a)}function st(e){var n="/"+e.source+"/";return e.global&&(n+="g"),e.multiline&&(n+="m"),e.ignoreCase&&(n+="i"),n}function lt(e){return Object.prototype.toString.call(e)==="[object RegExp]"}Dn.exports=new dt("tag:yaml.org,2002:js/regexp",{kind:"scalar",resolve:ot,construct:ct,predicate:lt,represent:st})});var $n=H((Dl,qn)=>{"use strict";var Ke;try{Bn=require,Ke=Bn("esprima")}catch(e){typeof window!="undefined"&&(Ke=window.esprima)}var Bn,ft=V();function pt(e){if(e===null)return!1;try{var n="("+e+")",r=Ke.parse(n,{range:!0});return!(r.type!=="Program"||r.body.length!==1||r.body[0].type!=="ExpressionStatement"||r.body[0].expression.type!=="ArrowFunctionExpression"&&r.body[0].expression.type!=="FunctionExpression")}catch(a){return!1}}function mt(e){var n="("+e+")",r=Ke.parse(n,{range:!0}),a=[],d;if(r.type!=="Program"||r.body.length!==1||r.body[0].type!=="ExpressionStatement"||r.body[0].expression.type!=="ArrowFunctionExpression"&&r.body[0].expression.type!=="FunctionExpression")throw new Error("Failed to resolve function");return r.body[0].expression.params.forEach(function(o){a.push(o.name)}),d=r.body[0].expression.body.range,r.body[0].expression.body.type==="BlockStatement"?new Function(a,n.slice(d[0]+1,d[1]-1)):new Function(a,"return "+n.slice(d[0],d[1]))}function ht(e){return e.toString()}function gt(e){return Object.prototype.toString.call(e)==="[object Function]"}qn.exports=new ft("tag:yaml.org,2002:js/function",{kind:"scalar",resolve:pt,construct:mt,predicate:gt,represent:ht})});var Re=H((Nl,Vn)=>{"use strict";var Un=me();Vn.exports=Un.DEFAULT=new Un({include:[ke()],explicit:[On(),Nn(),$n()]})});var si=H((Bl,Ie)=>{"use strict";var oe=pe(),Wn=ve(),_t=Zr(),Gn=ke(),wt=Re(),ce=Object.prototype.hasOwnProperty,Je=1,Yn=2,Kn=3,Ze=4,wr=1,bt=2,Jn=3,vt=/[\x00-\x08\x0B\x0C\x0E-\x1F\x7F-\x84\x86-\x9F\uFFFE\uFFFF]|[\uD800-\uDBFF](?![\uDC00-\uDFFF])|(?:[^\uD800-\uDBFF]|^)[\uDC00-\uDFFF]/,yt=/[\x85\u2028\u2029]/,kt=/[,\[\]\{\}]/,Zn=/^(?:!|!!|![a-z\-]+!)$/i,Qn=/^(?:!|[^,\[\]\{\}])(?:%[0-9a-f]{2}|[0-9a-z\-#;\/\?:@&=\+\$,_\.!~\*'\(\)\[\]])*$/i;function Xn(e){return Object.prototype.toString.call(e)}function ae(e){return e===10||e===13}function ge(e){return e===9||e===32}function X(e){return e===9||e===32||e===10||e===13}function Te(e){return e===44||e===91||e===93||e===123||e===125}function Tt(e){var n;return 48<=e&&e<=57?e-48:(n=e|32,97<=n&&n<=102?n-97+10:-1)}function xt(e){return e===120?2:e===117?4:e===85?8:0}function At(e){return 48<=e&&e<=57?e-48:-1}function ei(e){return e===48?"\0":e===97?"\x07":e===98?"\b":e===116||e===9?"	":e===110?`
`:e===118?"\v":e===102?"\f":e===114?"\r":e===101?"":e===32?" ":e===34?'"':e===47?"/":e===92?"\\":e===78?"\x85":e===95?"\xA0":e===76?"\u2028":e===80?"\u2029":""}function Et(e){return e<=65535?String.fromCharCode(e):String.fromCharCode((e-65536>>10)+55296,(e-65536&1023)+56320)}var ri=new Array(256),ni=new Array(256);for(_e=0;_e<256;_e++)ri[_e]=ei(_e)?1:0,ni[_e]=ei(_e);var _e;function Ct(e,n){this.input=e,this.filename=n.filename||null,this.schema=n.schema||wt,this.onWarning=n.onWarning||null,this.legacy=n.legacy||!1,this.json=n.json||!1,this.listener=n.listener||null,this.implicitTypes=this.schema.compiledImplicit,this.typeMap=this.schema.compiledTypeMap,this.length=e.length,this.position=0,this.line=0,this.lineStart=0,this.lineIndent=0,this.documents=[]}function ii(e,n){return new Wn(n,new _t(e.filename,e.input,e.position,e.line,e.position-e.lineStart))}function R(e,n){throw ii(e,n)}function Qe(e,n){e.onWarning&&e.onWarning.call(null,ii(e,n))}var ui={YAML:function(n,r,a){var d,o,c;n.version!==null&&R(n,"duplication of %YAML directive"),a.length!==1&&R(n,"YAML directive accepts exactly one argument"),d=/^([0-9]+)\.([0-9]+)$/.exec(a[0]),d===null&&R(n,"ill-formed argument of the YAML directive"),o=parseInt(d[1],10),c=parseInt(d[2],10),o!==1&&R(n,"unacceptable YAML version of the document"),n.version=a[0],n.checkLineBreaks=c<2,c!==1&&c!==2&&Qe(n,"unsupported YAML version of the document")},TAG:function(n,r,a){var d,o;a.length!==2&&R(n,"TAG directive accepts exactly two arguments"),d=a[0],o=a[1],Zn.test(d)||R(n,"ill-formed tag handle (first argument) of the TAG directive"),ce.call(n.tagMap,d)&&R(n,'there is a previously declared suffix for "'+d+'" tag handle'),Qn.test(o)||R(n,"ill-formed tag prefix (second argument) of the TAG directive"),n.tagMap[d]=o}};function se(e,n,r,a){var d,o,c,m;if(n<r){if(m=e.input.slice(n,r),a)for(d=0,o=m.length;d<o;d+=1)c=m.charCodeAt(d),c===9||32<=c&&c<=1114111||R(e,"expected valid JSON character");else vt.test(m)&&R(e,"the stream contains non-printable characters");e.result+=m}}function ai(e,n,r,a){var d,o,c,m;for(oe.isObject(r)||R(e,"cannot merge mappings; the provided source object is unacceptable"),d=Object.keys(r),c=0,m=d.length;c<m;c+=1)o=d[c],ce.call(n,o)||(n[o]=r[o],a[o]=!0)}function xe(e,n,r,a,d,o,c,m){var _,x;if(Array.isArray(d))for(d=Array.prototype.slice.call(d),_=0,x=d.length;_<x;_+=1)Array.isArray(d[_])&&R(e,"nested arrays are not supported inside keys"),typeof d=="object"&&Xn(d[_])==="[object Object]"&&(d[_]="[object Object]");if(typeof d=="object"&&Xn(d)==="[object Object]"&&(d="[object Object]"),d=String(d),n===null&&(n={}),a==="tag:yaml.org,2002:merge")if(Array.isArray(o))for(_=0,x=o.length;_<x;_+=1)ai(e,n,o[_],r);else ai(e,n,o,r);else!e.json&&!ce.call(r,d)&&ce.call(n,d)&&(e.line=c||e.line,e.position=m||e.position,R(e,"duplicated mapping key")),n[d]=o,delete r[d];return n}function br(e){var n;n=e.input.charCodeAt(e.position),n===10?e.position++:n===13?(e.position++,e.input.charCodeAt(e.position)===10&&e.position++):R(e,"a line break is expected"),e.line+=1,e.lineStart=e.position}function $(e,n,r){for(var a=0,d=e.input.charCodeAt(e.position);d!==0;){for(;ge(d);)d=e.input.charCodeAt(++e.position);if(n&&d===35)do d=e.input.charCodeAt(++e.position);while(d!==10&&d!==13&&d!==0);if(ae(d))for(br(e),d=e.input.charCodeAt(e.position),a++,e.lineIndent=0;d===32;)e.lineIndent++,d=e.input.charCodeAt(++e.position);else break}return r!==-1&&a!==0&&e.lineIndent<r&&Qe(e,"deficient indentation"),a}function Xe(e){var n=e.position,r;return r=e.input.charCodeAt(n),!!((r===45||r===46)&&r===e.input.charCodeAt(n+1)&&r===e.input.charCodeAt(n+2)&&(n+=3,r=e.input.charCodeAt(n),r===0||X(r)))}function vr(e,n){n===1?e.result+=" ":n>1&&(e.result+=oe.repeat(`
`,n-1))}function St(e,n,r){var a,d,o,c,m,_,x,S,A=e.kind,E=e.result,v;if(v=e.input.charCodeAt(e.position),X(v)||Te(v)||v===35||v===38||v===42||v===33||v===124||v===62||v===39||v===34||v===37||v===64||v===96||(v===63||v===45)&&(d=e.input.charCodeAt(e.position+1),X(d)||r&&Te(d)))return!1;for(e.kind="scalar",e.result="",o=c=e.position,m=!1;v!==0;){if(v===58){if(d=e.input.charCodeAt(e.position+1),X(d)||r&&Te(d))break}else if(v===35){if(a=e.input.charCodeAt(e.position-1),X(a))break}else{if(e.position===e.lineStart&&Xe(e)||r&&Te(v))break;if(ae(v))if(_=e.line,x=e.lineStart,S=e.lineIndent,$(e,!1,-1),e.lineIndent>=n){m=!0,v=e.input.charCodeAt(e.position);continue}else{e.position=c,e.line=_,e.lineStart=x,e.lineIndent=S;break}}m&&(se(e,o,c,!1),vr(e,e.line-_),o=c=e.position,m=!1),ge(v)||(c=e.position+1),v=e.input.charCodeAt(++e.position)}return se(e,o,c,!1),e.result?!0:(e.kind=A,e.result=E,!1)}function Lt(e,n){var r,a,d;if(r=e.input.charCodeAt(e.position),r!==39)return!1;for(e.kind="scalar",e.result="",e.position++,a=d=e.position;(r=e.input.charCodeAt(e.position))!==0;)if(r===39)if(se(e,a,e.position,!0),r=e.input.charCodeAt(++e.position),r===39)a=e.position,e.position++,d=e.position;else return!0;else ae(r)?(se(e,a,d,!0),vr(e,$(e,!1,n)),a=d=e.position):e.position===e.lineStart&&Xe(e)?R(e,"unexpected end of the document within a single quoted scalar"):(e.position++,d=e.position);R(e,"unexpected end of the stream within a single quoted scalar")}function Pt(e,n){var r,a,d,o,c,m;if(m=e.input.charCodeAt(e.position),m!==34)return!1;for(e.kind="scalar",e.result="",e.position++,r=a=e.position;(m=e.input.charCodeAt(e.position))!==0;){if(m===34)return se(e,r,e.position,!0),e.position++,!0;if(m===92){if(se(e,r,e.position,!0),m=e.input.charCodeAt(++e.position),ae(m))$(e,!1,n);else if(m<256&&ri[m])e.result+=ni[m],e.position++;else if((c=xt(m))>0){for(d=c,o=0;d>0;d--)m=e.input.charCodeAt(++e.position),(c=Tt(m))>=0?o=(o<<4)+c:R(e,"expected hexadecimal character");e.result+=Et(o),e.position++}else R(e,"unknown escape sequence");r=a=e.position}else ae(m)?(se(e,r,a,!0),vr(e,$(e,!1,n)),r=a=e.position):e.position===e.lineStart&&Xe(e)?R(e,"unexpected end of the document within a double quoted scalar"):(e.position++,a=e.position)}R(e,"unexpected end of the stream within a double quoted scalar")}function Mt(e,n){var r=!0,a,d=e.tag,o,c=e.anchor,m,_,x,S,A,E={},v,M,F,I;if(I=e.input.charCodeAt(e.position),I===91)_=93,A=!1,o=[];else if(I===123)_=125,A=!0,o={};else return!1;for(e.anchor!==null&&(e.anchorMap[e.anchor]=o),I=e.input.charCodeAt(++e.position);I!==0;){if($(e,!0,n),I=e.input.charCodeAt(e.position),I===_)return e.position++,e.tag=d,e.anchor=c,e.kind=A?"mapping":"sequence",e.result=o,!0;r||R(e,"missed comma between flow collection entries"),M=v=F=null,x=S=!1,I===63&&(m=e.input.charCodeAt(e.position+1),X(m)&&(x=S=!0,e.position++,$(e,!0,n))),a=e.line,Ae(e,n,Je,!1,!0),M=e.tag,v=e.result,$(e,!0,n),I=e.input.charCodeAt(e.position),(S||e.line===a)&&I===58&&(x=!0,I=e.input.charCodeAt(++e.position),$(e,!0,n),Ae(e,n,Je,!1,!0),F=e.result),A?xe(e,o,E,M,v,F):x?o.push(xe(e,null,E,M,v,F)):o.push(v),$(e,!0,n),I=e.input.charCodeAt(e.position),I===44?(r=!0,I=e.input.charCodeAt(++e.position)):r=!1}R(e,"unexpected end of the stream within a flow collection")}function Rt(e,n){var r,a,d=wr,o=!1,c=!1,m=n,_=0,x=!1,S,A;if(A=e.input.charCodeAt(e.position),A===124)a=!1;else if(A===62)a=!0;else return!1;for(e.kind="scalar",e.result="";A!==0;)if(A=e.input.charCodeAt(++e.position),A===43||A===45)wr===d?d=A===43?Jn:bt:R(e,"repeat of a chomping mode identifier");else if((S=At(A))>=0)S===0?R(e,"bad explicit indentation width of a block scalar; it cannot be less than one"):c?R(e,"repeat of an indentation width identifier"):(m=n+S-1,c=!0);else break;if(ge(A)){do A=e.input.charCodeAt(++e.position);while(ge(A));if(A===35)do A=e.input.charCodeAt(++e.position);while(!ae(A)&&A!==0)}for(;A!==0;){for(br(e),e.lineIndent=0,A=e.input.charCodeAt(e.position);(!c||e.lineIndent<m)&&A===32;)e.lineIndent++,A=e.input.charCodeAt(++e.position);if(!c&&e.lineIndent>m&&(m=e.lineIndent),ae(A)){_++;continue}if(e.lineIndent<m){d===Jn?e.result+=oe.repeat(`
`,o?1+_:_):d===wr&&o&&(e.result+=`
`);break}for(a?ge(A)?(x=!0,e.result+=oe.repeat(`
`,o?1+_:_)):x?(x=!1,e.result+=oe.repeat(`
`,_+1)):_===0?o&&(e.result+=" "):e.result+=oe.repeat(`
`,_):e.result+=oe.repeat(`
`,o?1+_:_),o=!0,c=!0,_=0,r=e.position;!ae(A)&&A!==0;)A=e.input.charCodeAt(++e.position);se(e,r,e.position,!1)}return!0}function ti(e,n){var r,a=e.tag,d=e.anchor,o=[],c,m=!1,_;for(e.anchor!==null&&(e.anchorMap[e.anchor]=o),_=e.input.charCodeAt(e.position);_!==0&&!(_!==45||(c=e.input.charCodeAt(e.position+1),!X(c)));){if(m=!0,e.position++,$(e,!0,-1)&&e.lineIndent<=n){o.push(null),_=e.input.charCodeAt(e.position);continue}if(r=e.line,Ae(e,n,Kn,!1,!0),o.push(e.result),$(e,!0,-1),_=e.input.charCodeAt(e.position),(e.line===r||e.lineIndent>n)&&_!==0)R(e,"bad indentation of a sequence entry");else if(e.lineIndent<n)break}return m?(e.tag=a,e.anchor=d,e.kind="sequence",e.result=o,!0):!1}function It(e,n,r){var a,d,o,c,m=e.tag,_=e.anchor,x={},S={},A=null,E=null,v=null,M=!1,F=!1,I;for(e.anchor!==null&&(e.anchorMap[e.anchor]=x),I=e.input.charCodeAt(e.position);I!==0;){if(a=e.input.charCodeAt(e.position+1),o=e.line,c=e.position,(I===63||I===58)&&X(a))I===63?(M&&(xe(e,x,S,A,E,null),A=E=v=null),F=!0,M=!0,d=!0):M?(M=!1,d=!0):R(e,"incomplete explicit mapping pair; a key node is missed; or followed by a non-tabulated empty line"),e.position+=1,I=a;else if(Ae(e,r,Yn,!1,!0))if(e.line===o){for(I=e.input.charCodeAt(e.position);ge(I);)I=e.input.charCodeAt(++e.position);if(I===58)I=e.input.charCodeAt(++e.position),X(I)||R(e,"a whitespace character is expected after the key-value separator within a block mapping"),M&&(xe(e,x,S,A,E,null),A=E=v=null),F=!0,M=!1,d=!1,A=e.tag,E=e.result;else if(F)R(e,"can not read an implicit mapping pair; a colon is missed");else return e.tag=m,e.anchor=_,!0}else if(F)R(e,"can not read a block mapping entry; a multiline key may not be an implicit key");else return e.tag=m,e.anchor=_,!0;else break;if((e.line===o||e.lineIndent>n)&&(Ae(e,n,Ze,!0,d)&&(M?E=e.result:v=e.result),M||(xe(e,x,S,A,E,v,o,c),A=E=v=null),$(e,!0,-1),I=e.input.charCodeAt(e.position)),e.lineIndent>n&&I!==0)R(e,"bad indentation of a mapping entry");else if(e.lineIndent<n)break}return M&&xe(e,x,S,A,E,null),F&&(e.tag=m,e.anchor=_,e.kind="mapping",e.result=x),F}function Ft(e){var n,r=!1,a=!1,d,o,c;if(c=e.input.charCodeAt(e.position),c!==33)return!1;if(e.tag!==null&&R(e,"duplication of a tag property"),c=e.input.charCodeAt(++e.position),c===60?(r=!0,c=e.input.charCodeAt(++e.position)):c===33?(a=!0,d="!!",c=e.input.charCodeAt(++e.position)):d="!",n=e.position,r){do c=e.input.charCodeAt(++e.position);while(c!==0&&c!==62);e.position<e.length?(o=e.input.slice(n,e.position),c=e.input.charCodeAt(++e.position)):R(e,"unexpected end of the stream within a verbatim tag")}else{for(;c!==0&&!X(c);)c===33&&(a?R(e,"tag suffix cannot contain exclamation marks"):(d=e.input.slice(n-1,e.position+1),Zn.test(d)||R(e,"named tag handle cannot contain such characters"),a=!0,n=e.position+1)),c=e.input.charCodeAt(++e.position);o=e.input.slice(n,e.position),kt.test(o)&&R(e,"tag suffix cannot contain flow indicator characters")}return o&&!Qn.test(o)&&R(e,"tag name cannot contain such characters: "+o),r?e.tag=o:ce.call(e.tagMap,d)?e.tag=e.tagMap[d]+o:d==="!"?e.tag="!"+o:d==="!!"?e.tag="tag:yaml.org,2002:"+o:R(e,'undeclared tag handle "'+d+'"'),!0}function jt(e){var n,r;if(r=e.input.charCodeAt(e.position),r!==38)return!1;for(e.anchor!==null&&R(e,"duplication of an anchor property"),r=e.input.charCodeAt(++e.position),n=e.position;r!==0&&!X(r)&&!Te(r);)r=e.input.charCodeAt(++e.position);return e.position===n&&R(e,"name of an anchor node must contain at least one character"),e.anchor=e.input.slice(n,e.position),!0}function Ht(e){var n,r,a;if(a=e.input.charCodeAt(e.position),a!==42)return!1;for(a=e.input.charCodeAt(++e.position),n=e.position;a!==0&&!X(a)&&!Te(a);)a=e.input.charCodeAt(++e.position);return e.position===n&&R(e,"name of an alias node must contain at least one character"),r=e.input.slice(n,e.position),ce.call(e.anchorMap,r)||R(e,'unidentified alias "'+r+'"'),e.result=e.anchorMap[r],$(e,!0,-1),!0}function Ae(e,n,r,a,d){var o,c,m,_=1,x=!1,S=!1,A,E,v,M,F;if(e.listener!==null&&e.listener("open",e),e.tag=null,e.anchor=null,e.kind=null,e.result=null,o=c=m=Ze===r||Kn===r,a&&$(e,!0,-1)&&(x=!0,e.lineIndent>n?_=1:e.lineIndent===n?_=0:e.lineIndent<n&&(_=-1)),_===1)for(;Ft(e)||jt(e);)$(e,!0,-1)?(x=!0,m=o,e.lineIndent>n?_=1:e.lineIndent===n?_=0:e.lineIndent<n&&(_=-1)):m=!1;if(m&&(m=x||d),(_===1||Ze===r)&&(Je===r||Yn===r?M=n:M=n+1,F=e.position-e.lineStart,_===1?m&&(ti(e,F)||It(e,F,M))||Mt(e,M)?S=!0:(c&&Rt(e,M)||Lt(e,M)||Pt(e,M)?S=!0:Ht(e)?(S=!0,(e.tag!==null||e.anchor!==null)&&R(e,"alias node should not have any properties")):St(e,M,Je===r)&&(S=!0,e.tag===null&&(e.tag="?")),e.anchor!==null&&(e.anchorMap[e.anchor]=e.result)):_===0&&(S=m&&ti(e,F))),e.tag!==null&&e.tag!=="!")if(e.tag==="?"){for(e.result!==null&&e.kind!=="scalar"&&R(e,'unacceptable node kind for !<?> tag; it should be "scalar", not "'+e.kind+'"'),A=0,E=e.implicitTypes.length;A<E;A+=1)if(v=e.implicitTypes[A],v.resolve(e.result)){e.result=v.construct(e.result),e.tag=v.tag,e.anchor!==null&&(e.anchorMap[e.anchor]=e.result);break}}else ce.call(e.typeMap[e.kind||"fallback"],e.tag)?(v=e.typeMap[e.kind||"fallback"][e.tag],e.result!==null&&v.kind!==e.kind&&R(e,"unacceptable node kind for !<"+e.tag+'> tag; it should be "'+v.kind+'", not "'+e.kind+'"'),v.resolve(e.result)?(e.result=v.construct(e.result),e.anchor!==null&&(e.anchorMap[e.anchor]=e.result)):R(e,"cannot resolve a node with !<"+e.tag+"> explicit tag")):R(e,"unknown tag !<"+e.tag+">");return e.listener!==null&&e.listener("close",e),e.tag!==null||e.anchor!==null||S}function zt(e){var n=e.position,r,a,d,o=!1,c;for(e.version=null,e.checkLineBreaks=e.legacy,e.tagMap={},e.anchorMap={};(c=e.input.charCodeAt(e.position))!==0&&($(e,!0,-1),c=e.input.charCodeAt(e.position),!(e.lineIndent>0||c!==37));){for(o=!0,c=e.input.charCodeAt(++e.position),r=e.position;c!==0&&!X(c);)c=e.input.charCodeAt(++e.position);for(a=e.input.slice(r,e.position),d=[],a.length<1&&R(e,"directive name must not be less than one character in length");c!==0;){for(;ge(c);)c=e.input.charCodeAt(++e.position);if(c===35){do c=e.input.charCodeAt(++e.position);while(c!==0&&!ae(c));break}if(ae(c))break;for(r=e.position;c!==0&&!X(c);)c=e.input.charCodeAt(++e.position);d.push(e.input.slice(r,e.position))}c!==0&&br(e),ce.call(ui,a)?ui[a](e,a,d):Qe(e,'unknown document directive "'+a+'"')}if($(e,!0,-1),e.lineIndent===0&&e.input.charCodeAt(e.position)===45&&e.input.charCodeAt(e.position+1)===45&&e.input.charCodeAt(e.position+2)===45?(e.position+=3,$(e,!0,-1)):o&&R(e,"directives end mark is expected"),Ae(e,e.lineIndent-1,Ze,!1,!0),$(e,!0,-1),e.checkLineBreaks&&yt.test(e.input.slice(n,e.position))&&Qe(e,"non-ASCII line breaks are interpreted as content"),e.documents.push(e.result),e.position===e.lineStart&&Xe(e)){e.input.charCodeAt(e.position)===46&&(e.position+=3,$(e,!0,-1));return}if(e.position<e.length-1)R(e,"end of the stream or a document separator is expected");else return}function di(e,n){e=String(e),n=n||{},e.length!==0&&(e.charCodeAt(e.length-1)!==10&&e.charCodeAt(e.length-1)!==13&&(e+=`
`),e.charCodeAt(0)===65279&&(e=e.slice(1)));var r=new Ct(e,n),a=e.indexOf("\0");for(a!==-1&&(r.position=a,R(r,"null byte is not allowed in input")),r.input+="\0";r.input.charCodeAt(r.position)===32;)r.lineIndent+=1,r.position+=1;for(;r.position<r.length-1;)zt(r);return r.documents}function oi(e,n,r){n!==null&&typeof n=="object"&&typeof r=="undefined"&&(r=n,n=null);var a=di(e,r);if(typeof n!="function")return a;for(var d=0,o=a.length;d<o;d+=1)n(a[d])}function ci(e,n){var r=di(e,n);if(r.length!==0){if(r.length===1)return r[0];throw new Wn("expected a single document in the stream, but found more")}}function Ot(e,n,r){return typeof n=="object"&&n!==null&&typeof r=="undefined"&&(r=n,n=null),oi(e,n,oe.extend({schema:Gn},r))}function Dt(e,n){return ci(e,oe.extend({schema:Gn},n))}Ie.exports.loadAll=oi;Ie.exports.load=ci;Ie.exports.safeLoadAll=Ot;Ie.exports.safeLoad=Dt});var Ii=H((ql,xr)=>{"use strict";var Fe=pe(),je=ve(),Nt=Re(),Bt=ke(),li=Object.prototype.toString,fi=Object.prototype.hasOwnProperty,qt=9,He=10,$t=13,Ut=32,Vt=33,Wt=34,pi=35,Gt=37,Yt=38,Kt=39,Jt=42,mi=44,Zt=45,hi=58,Qt=61,Xt=62,ed=63,rd=64,gi=91,_i=93,nd=96,wi=123,id=124,bi=125,K={};K[0]="\\0";K[7]="\\a";K[8]="\\b";K[9]="\\t";K[10]="\\n";K[11]="\\v";K[12]="\\f";K[13]="\\r";K[27]="\\e";K[34]='\\"';K[92]="\\\\";K[133]="\\N";K[160]="\\_";K[8232]="\\L";K[8233]="\\P";var ud=["y","Y","yes","Yes","YES","on","On","ON","n","N","no","No","NO","off","Off","OFF"];function ad(e,n){var r,a,d,o,c,m,_;if(n===null)return{};for(r={},a=Object.keys(n),d=0,o=a.length;d<o;d+=1)c=a[d],m=String(n[c]),c.slice(0,2)==="!!"&&(c="tag:yaml.org,2002:"+c.slice(2)),_=e.compiledTypeMap.fallback[c],_&&fi.call(_.styleAliases,m)&&(m=_.styleAliases[m]),r[c]=m;return r}function vi(e){var n,r,a;if(n=e.toString(16).toUpperCase(),e<=255)r="x",a=2;else if(e<=65535)r="u",a=4;else if(e<=4294967295)r="U",a=8;else throw new je("code point within a string may not be greater than 0xFFFFFFFF");return"\\"+r+Fe.repeat("0",a-n.length)+n}function td(e){this.schema=e.schema||Nt,this.indent=Math.max(1,e.indent||2),this.noArrayIndent=e.noArrayIndent||!1,this.skipInvalid=e.skipInvalid||!1,this.flowLevel=Fe.isNothing(e.flowLevel)?-1:e.flowLevel,this.styleMap=ad(this.schema,e.styles||null),this.sortKeys=e.sortKeys||!1,this.lineWidth=e.lineWidth||80,this.noRefs=e.noRefs||!1,this.noCompatMode=e.noCompatMode||!1,this.condenseFlow=e.condenseFlow||!1,this.implicitTypes=this.schema.compiledImplicit,this.explicitTypes=this.schema.compiledExplicit,this.tag=null,this.result="",this.duplicates=[],this.usedDuplicates=null}function yi(e,n){for(var r=Fe.repeat(" ",n),a=0,d=-1,o="",c,m=e.length;a<m;)d=e.indexOf(`
`,a),d===-1?(c=e.slice(a),a=m):(c=e.slice(a,d+1),a=d+1),c.length&&c!==`
`&&(o+=r),o+=c;return o}function yr(e,n){return`
`+Fe.repeat(" ",e.indent*n)}function dd(e,n){var r,a,d;for(r=0,a=e.implicitTypes.length;r<a;r+=1)if(d=e.implicitTypes[r],d.resolve(n))return!0;return!1}function kr(e){return e===Ut||e===qt}function Ee(e){return 32<=e&&e<=126||161<=e&&e<=55295&&e!==8232&&e!==8233||57344<=e&&e<=65533&&e!==65279||65536<=e&&e<=1114111}function od(e){return Ee(e)&&!kr(e)&&e!==65279&&e!==$t&&e!==He}function ki(e,n){return Ee(e)&&e!==65279&&e!==mi&&e!==gi&&e!==_i&&e!==wi&&e!==bi&&e!==hi&&(e!==pi||n&&od(n))}function cd(e){return Ee(e)&&e!==65279&&!kr(e)&&e!==Zt&&e!==ed&&e!==hi&&e!==mi&&e!==gi&&e!==_i&&e!==wi&&e!==bi&&e!==pi&&e!==Yt&&e!==Jt&&e!==Vt&&e!==id&&e!==Qt&&e!==Xt&&e!==Kt&&e!==Wt&&e!==Gt&&e!==rd&&e!==nd}function Ti(e){var n=/^\n* /;return n.test(e)}var xi=1,Ai=2,Ei=3,Ci=4,er=5;function sd(e,n,r,a,d){var o,c,m,_=!1,x=!1,S=a!==-1,A=-1,E=cd(e.charCodeAt(0))&&!kr(e.charCodeAt(e.length-1));if(n)for(o=0;o<e.length;o++){if(c=e.charCodeAt(o),!Ee(c))return er;m=o>0?e.charCodeAt(o-1):null,E=E&&ki(c,m)}else{for(o=0;o<e.length;o++){if(c=e.charCodeAt(o),c===He)_=!0,S&&(x=x||o-A-1>a&&e[A+1]!==" ",A=o);else if(!Ee(c))return er;m=o>0?e.charCodeAt(o-1):null,E=E&&ki(c,m)}x=x||S&&o-A-1>a&&e[A+1]!==" "}return!_&&!x?E&&!d(e)?xi:Ai:r>9&&Ti(e)?er:x?Ci:Ei}function ld(e,n,r,a){e.dump=function(){if(n.length===0)return"''";if(!e.noCompatMode&&ud.indexOf(n)!==-1)return"'"+n+"'";var d=e.indent*Math.max(1,r),o=e.lineWidth===-1?-1:Math.max(Math.min(e.lineWidth,40),e.lineWidth-d),c=a||e.flowLevel>-1&&r>=e.flowLevel;function m(_){return dd(e,_)}switch(sd(n,c,e.indent,o,m)){case xi:return n;case Ai:return"'"+n.replace(/'/g,"''")+"'";case Ei:return"|"+Si(n,e.indent)+Li(yi(n,d));case Ci:return">"+Si(n,e.indent)+Li(yi(fd(n,o),d));case er:return'"'+pd(n,o)+'"';default:throw new je("impossible error: invalid scalar style")}}()}function Si(e,n){var r=Ti(e)?String(n):"",a=e[e.length-1]===`
`,d=a&&(e[e.length-2]===`
`||e===`
`),o=d?"+":a?"":"-";return r+o+`
`}function Li(e){return e[e.length-1]===`
`?e.slice(0,-1):e}function fd(e,n){for(var r=/(\n+)([^\n]*)/g,a=function(){var x=e.indexOf(`
`);return x=x!==-1?x:e.length,r.lastIndex=x,Pi(e.slice(0,x),n)}(),d=e[0]===`
`||e[0]===" ",o,c;c=r.exec(e);){var m=c[1],_=c[2];o=_[0]===" ",a+=m+(!d&&!o&&_!==""?`
`:"")+Pi(_,n),d=o}return a}function Pi(e,n){if(e===""||e[0]===" ")return e;for(var r=/ [^ ]/g,a,d=0,o,c=0,m=0,_="";a=r.exec(e);)m=a.index,m-d>n&&(o=c>d?c:m,_+=`
`+e.slice(d,o),d=o+1),c=m;return _+=`
`,e.length-d>n&&c>d?_+=e.slice(d,c)+`
`+e.slice(c+1):_+=e.slice(d),_.slice(1)}function pd(e){for(var n="",r,a,d,o=0;o<e.length;o++){if(r=e.charCodeAt(o),r>=55296&&r<=56319&&(a=e.charCodeAt(o+1),a>=56320&&a<=57343)){n+=vi((r-55296)*1024+a-56320+65536),o++;continue}d=K[r],n+=!d&&Ee(r)?e[o]:d||vi(r)}return n}function md(e,n,r){var a="",d=e.tag,o,c;for(o=0,c=r.length;o<c;o+=1)we(e,n,r[o],!1,!1)&&(o!==0&&(a+=","+(e.condenseFlow?"":" ")),a+=e.dump);e.tag=d,e.dump="["+a+"]"}function hd(e,n,r,a){var d="",o=e.tag,c,m;for(c=0,m=r.length;c<m;c+=1)we(e,n+1,r[c],!0,!0)&&((!a||c!==0)&&(d+=yr(e,n)),e.dump&&He===e.dump.charCodeAt(0)?d+="-":d+="- ",d+=e.dump);e.tag=o,e.dump=d||"[]"}function gd(e,n,r){var a="",d=e.tag,o=Object.keys(r),c,m,_,x,S;for(c=0,m=o.length;c<m;c+=1)S="",c!==0&&(S+=", "),e.condenseFlow&&(S+='"'),_=o[c],x=r[_],!!we(e,n,_,!1,!1)&&(e.dump.length>1024&&(S+="? "),S+=e.dump+(e.condenseFlow?'"':"")+":"+(e.condenseFlow?"":" "),!!we(e,n,x,!1,!1)&&(S+=e.dump,a+=S));e.tag=d,e.dump="{"+a+"}"}function _d(e,n,r,a){var d="",o=e.tag,c=Object.keys(r),m,_,x,S,A,E;if(e.sortKeys===!0)c.sort();else if(typeof e.sortKeys=="function")c.sort(e.sortKeys);else if(e.sortKeys)throw new je("sortKeys must be a boolean or a function");for(m=0,_=c.length;m<_;m+=1)E="",(!a||m!==0)&&(E+=yr(e,n)),x=c[m],S=r[x],!!we(e,n+1,x,!0,!0,!0)&&(A=e.tag!==null&&e.tag!=="?"||e.dump&&e.dump.length>1024,A&&(e.dump&&He===e.dump.charCodeAt(0)?E+="?":E+="? "),E+=e.dump,A&&(E+=yr(e,n)),!!we(e,n+1,S,!0,A)&&(e.dump&&He===e.dump.charCodeAt(0)?E+=":":E+=": ",E+=e.dump,d+=E));e.tag=o,e.dump=d||"{}"}function Mi(e,n,r){var a,d,o,c,m,_;for(d=r?e.explicitTypes:e.implicitTypes,o=0,c=d.length;o<c;o+=1)if(m=d[o],(m.instanceOf||m.predicate)&&(!m.instanceOf||typeof n=="object"&&n instanceof m.instanceOf)&&(!m.predicate||m.predicate(n))){if(e.tag=r?m.tag:"?",m.represent){if(_=e.styleMap[m.tag]||m.defaultStyle,li.call(m.represent)==="[object Function]")a=m.represent(n,_);else if(fi.call(m.represent,_))a=m.represent[_](n,_);else throw new je("!<"+m.tag+'> tag resolver accepts not "'+_+'" style');e.dump=a}return!0}return!1}function we(e,n,r,a,d,o){e.tag=null,e.dump=r,Mi(e,r,!1)||Mi(e,r,!0);var c=li.call(e.dump);a&&(a=e.flowLevel<0||e.flowLevel>n);var m=c==="[object Object]"||c==="[object Array]",_,x;if(m&&(_=e.duplicates.indexOf(r),x=_!==-1),(e.tag!==null&&e.tag!=="?"||x||e.indent!==2&&n>0)&&(d=!1),x&&e.usedDuplicates[_])e.dump="*ref_"+_;else{if(m&&x&&!e.usedDuplicates[_]&&(e.usedDuplicates[_]=!0),c==="[object Object]")a&&Object.keys(e.dump).length!==0?(_d(e,n,e.dump,d),x&&(e.dump="&ref_"+_+e.dump)):(gd(e,n,e.dump),x&&(e.dump="&ref_"+_+" "+e.dump));else if(c==="[object Array]"){var S=e.noArrayIndent&&n>0?n-1:n;a&&e.dump.length!==0?(hd(e,S,e.dump,d),x&&(e.dump="&ref_"+_+e.dump)):(md(e,S,e.dump),x&&(e.dump="&ref_"+_+" "+e.dump))}else if(c==="[object String]")e.tag!=="?"&&ld(e,e.dump,n,o);else{if(e.skipInvalid)return!1;throw new je("unacceptable kind of an object to dump "+c)}e.tag!==null&&e.tag!=="?"&&(e.dump="!<"+e.tag+"> "+e.dump)}return!0}function wd(e,n){var r=[],a=[],d,o;for(Tr(e,r,a),d=0,o=a.length;d<o;d+=1)n.duplicates.push(r[a[d]]);n.usedDuplicates=new Array(o)}function Tr(e,n,r){var a,d,o;if(e!==null&&typeof e=="object")if(d=n.indexOf(e),d!==-1)r.indexOf(d)===-1&&r.push(d);else if(n.push(e),Array.isArray(e))for(d=0,o=e.length;d<o;d+=1)Tr(e[d],n,r);else for(a=Object.keys(e),d=0,o=a.length;d<o;d+=1)Tr(e[a[d]],n,r)}function Ri(e,n){n=n||{};var r=new td(n);return r.noRefs||wd(e,r),we(r,0,e,!0,!0)?r.dump+`
`:""}function bd(e,n){return Ri(e,Fe.extend({schema:Bt},n))}xr.exports.dump=Ri;xr.exports.safeDump=bd});var ji=H(($l,B)=>{"use strict";var rr=si(),Fi=Ii();function nr(e){return function(){throw new Error("Function "+e+" is deprecated and cannot be used.")}}B.exports.Type=V();B.exports.Schema=me();B.exports.FAILSAFE_SCHEMA=Ye();B.exports.JSON_SCHEMA=hr();B.exports.CORE_SCHEMA=gr();B.exports.DEFAULT_SAFE_SCHEMA=ke();B.exports.DEFAULT_FULL_SCHEMA=Re();B.exports.load=rr.load;B.exports.loadAll=rr.loadAll;B.exports.safeLoad=rr.safeLoad;B.exports.safeLoadAll=rr.safeLoadAll;B.exports.dump=Fi.dump;B.exports.safeDump=Fi.safeDump;B.exports.YAMLException=ve();B.exports.MINIMAL_SCHEMA=Ye();B.exports.SAFE_SCHEMA=ke();B.exports.DEFAULT_SCHEMA=Re();B.exports.scan=nr("scan");B.exports.parse=nr("parse");B.exports.compose=nr("compose");B.exports.addConstructor=nr("addConstructor")});var zi=H((Ul,Hi)=>{"use strict";var vd=ji();Hi.exports=vd});var Ni=H((Vl,Ar)=>{var Oi=zi(),yd="\\ufeff?",kd=typeof process!="undefined"?process.platform:"",Td="^("+yd+"(= yaml =|---)$([\\s\\S]*?)^(?:\\2|\\.\\.\\.)\\s*$"+(kd==="win32"?"\\r?":"")+"(?:\\n)?)",Di=new RegExp(Td,"m");Ar.exports=xd;Ar.exports.test=Cd;function xd(e,n){e=e||"";var r={allowUnsafe:!1};n=n instanceof Object?cr(cr({},r),n):r,n.allowUnsafe=Boolean(n.allowUnsafe);var a=e.split(/(\r?\n)/);return a[0]&&/= yaml =|---/.test(a[0])?Ed(e,n.allowUnsafe):{attributes:{},body:e,bodyBegin:1}}function Ad(e,n){for(var r=1,a=n.indexOf(`
`),d=e.index+e[0].length;a!==-1;){if(a>=d)return r;r++,a=n.indexOf(`
`,a+1)}return r}function Ed(e,n){var r=Di.exec(e);if(!r)return{attributes:{},body:e,bodyBegin:1};var a=n?Oi.load:Oi.safeLoad,d=r[r.length-1].replace(/^\s+|\s+$/g,""),o=a(d)||{},c=e.replace(r[0],""),m=Ad(r,e);return{attributes:o,body:c,bodyBegin:m,frontmatter:d}}function Cd(e){return e=e||"",Di.test(e)}});mu(exports,{default:()=>Lr});var le=Z(require("obsidian"));var ie=Z(require("obsidian")),Hr={cm6RenderAll:!0,renderImages:!0,renderPDF:!0,renderIframe:!1,renderExcalidraw:!1,renderMsgFile:!1,renderRichLink:!1,renderTransclusion:!1,previewOnHoverInternalLink:!1,refreshImagesAfterChange:!1,WYSIWYG:!1},sr=class extends ie.PluginSettingTab{constructor(n,r){super(n,r);this.plugin=r}display(){let{containerEl:n}=this;n.empty(),n.createEl("h1",{text:"Image in Editor Settings"}).addClass("image-in-editor-settings-main-header");let a=n.createDiv("tip");a.addClass("oz-tip-div");let o=a.createEl("a",{href:"https://revolut.me/ozante"}).createEl("img",{attr:{src:"https://raw.githubusercontent.com/ozntel/file-tree-alternative/main/images/tip%20the%20artist_v2.png"}});o.height=55;let c=n.createDiv("coffee");c.addClass("oz-coffee-div");let _=c.createEl("a",{href:"https://ko-fi.com/L3L356V6Q"}).createEl("img",{attr:{src:"https://cdn.ko-fi.com/cdn/kofi2.png?v=3"}});_.height=45;let x=n.createEl("div");x.innerHTML=`
        <p>
            The plugin will add image preview within the "Source Mode" of New Editor.
            In case you have Live Preview enabled, the plugin will automatically detect this and won't render additionally to avoid duplication.
        </p>
        `,new ie.Setting(n).setName("Render All").setDesc("Turn off this option if you want to stop rendering images in the editor source mode. Disabling requires vault reload.").addToggle(S=>{S.setValue(this.plugin.settings.cm6RenderAll).onChange(A=>{this.plugin.settings.cm6RenderAll=A,A?this.plugin.loadCM6Extension():this.plugin.unloadCM6Extension(),this.plugin.saveSettings()})}),new ie.Setting(n).setName("Render Images in Editor").setDesc("Turn on this option if you want Image files (jpeg, jpg, png, gif, svg, bmp, webp) to be rendered in Editor").addToggle(S=>S.setValue(this.plugin.settings.renderImages).onChange(A=>{this.plugin.settings.renderImages=A,this.plugin.saveSettings()})),new ie.Setting(n).setName("Render Transclusion in Editor").setDesc("Turn on this option if you want transclusions to be rendered in Editor. Once this is enabled, you will have custom options for transclusions below.").addToggle(S=>S.setValue(this.plugin.settings.renderTransclusion).onChange(A=>{this.plugin.settings.renderTransclusion=A,this.plugin.saveSettings()})),new ie.Setting(n).setName("Render PDFs in Editor").setDesc("Turn on this option if you want also PDF files to be rendered in Editor").addToggle(S=>S.setValue(this.plugin.settings.renderPDF).onChange(A=>{this.plugin.settings.renderPDF=A,this.plugin.saveSettings()})),new ie.Setting(n).setName("Render Iframes in Editor").setDesc("Turn on this option if you want iframes to be rendered in Editor").addToggle(S=>S.setValue(this.plugin.settings.renderIframe).onChange(A=>{this.plugin.settings.renderIframe=A,this.plugin.saveSettings()})),new ie.Setting(n).setName("Render Excalidraw in Editor").setDesc(`
                Turn on this option if you want drawings to be rendered in Editor.
                You need to have "Excalidraw" plugin installed so that this
                option works.
                `).addToggle(S=>S.setValue(this.plugin.settings.renderExcalidraw).onChange(A=>{this.plugin.settings.renderExcalidraw=A,this.plugin.saveSettings()})),new ie.Setting(n).setName("Render Outlook MSG Files in Editor").setDesc(`
                Turn on this option if you want outlook MSG Files to be rendered in Editor. 
                You need to have "MSG Handler" plugin installed so that
                this option works
                `).addToggle(S=>S.setValue(this.plugin.settings.renderMsgFile).onChange(A=>{this.plugin.settings.renderMsgFile=A,this.plugin.saveSettings()})),new ie.Setting(n).setName("Preview on Hover for File Links").setDesc("Turn on if you want to trigger preview when you hover on internal links within the rendered transclusion").addToggle(S=>S.setValue(this.plugin.settings.previewOnHoverInternalLink).onChange(A=>{this.plugin.settings.previewOnHoverInternalLink=A,this.plugin.saveSettings(),A?document.on("mouseover",".oz-obsidian-inner-link",this.plugin.filePreviewOnHover):document.off("mouseover",".oz-obsidian-inner-link",this.plugin.filePreviewOnHover)}))}};var Oe=Z(require("obsidian"));var De=(e,n)=>e.getResourcePath(n)+"?"+n.stat.mtime,zr=(e,n,r)=>{r.workspace.openLinkText(n,"/",Oe.Keymap.isModifier(e,"Mod")||e.button===1)},lr=e=>e.replace(/\s|\W|[#$%^&*()]/g,""),Ne=(e,n)=>e.plugins.getPlugin(n),fr=e=>{var n;return(n=e.vault.config)==null?void 0:n.livePreview},Or=()=>{var e;return((e=Oe.Platform)==null?void 0:e.resourcePathPrefix)||"app://local/"};var Dr=Z(require("obsidian"));var Be=e=>{let n=/[^(x|0-9)]/;if(e.match(n))return!1;let r=/[0-9]+x[0-9]+/,a=/[0-9]+/;var d=e.match(r);if(d){var o=d[0].indexOf("x");return{width:parseInt(d[0].substr(0,o)),height:parseInt(d[0].substr(o+1))}}else{var c=e.match(a);if(c)return{width:parseInt(c[0])}}return!1};var Nr=(e,n,r)=>{let a=new Dr.Menu(n.app);return a.addItem(d=>{d.setTitle("Copy Image to Clipboard"),d.setIcon("image-file"),d.onClick(o=>ne(void 0,null,function*(){var c=yield n.app.vault.adapter.readBinary(r.path),m=new Uint8Array(c),_=new Blob([m],{type:"image/png"});let x=new ClipboardItem({"image/png":_});window.navigator.clipboard.write([x])}))}),n.app.workspace.trigger("file-menu",a,r,"file-explorer"),a.showAtPosition({x:e.pageX,y:e.pageY}),!1};var qe=Z(require("@codemirror/state")),$e=Z(require("@codemirror/view"));function gu(){let e=qe.StateEffect.define(),n=qe.StateField.define({create(){return $e.Decoration.none},update(r,a){return a.effects.reduce((d,o)=>o.is(e)?o.value:d,r)},provide:r=>$e.EditorView.decorations.from(r)});return{update:e,field:n}}var Le=gu();var tu=Z(require("@codemirror/view"));var Ce=Z(require("obsidian")),uu=Z(require("@codemirror/state")),au=Z(require("@codemirror/view"));var Br=e=>Ne(e,"obsidian-excalidraw-plugin"),qr=e=>e.extension==="excalidraw"||ExcalidrawAutomate.isExcalidrawFile&&ExcalidrawAutomate.isExcalidrawFile(e),$r=e=>ne(void 0,null,function*(){ExcalidrawAutomate.reset();var n=yield ExcalidrawAutomate.createPNG(e.path);return n});var Bi=Z(Vr());var _u=function(){function e(n,r){for(var a=0;a<r.length;a++){var d=r[a];d.enumerable=d.enumerable||!1,d.configurable=!0,"value"in d&&(d.writable=!0),Object.defineProperty(n,d.key,d)}}return function(n,r,a){return r&&e(n.prototype,r),a&&e(n,a),n}}(),wu=bu(["",""],["",""]);function bu(e,n){return Object.freeze(Object.defineProperties(e,{raw:{value:Object.freeze(n)}}))}function vu(e,n){if(!(e instanceof n))throw new TypeError("Cannot call a class as a function")}var yu=function(){function e(){for(var n=this,r=arguments.length,a=Array(r),d=0;d<r;d++)a[d]=arguments[d];return vu(this,e),this.tag=function(o){for(var c=arguments.length,m=Array(c>1?c-1:0),_=1;_<c;_++)m[_-1]=arguments[_];return typeof o=="function"?n.interimTag.bind(n,o):typeof o=="string"?n.transformEndResult(o):(o=o.map(n.transformString.bind(n)),n.transformEndResult(o.reduce(n.processSubstitutions.bind(n,m))))},a.length>0&&Array.isArray(a[0])&&(a=a[0]),this.transformers=a.map(function(o){return typeof o=="function"?o():o}),this.tag}return _u(e,[{key:"interimTag",value:function(r,a){for(var d=arguments.length,o=Array(d>2?d-2:0),c=2;c<d;c++)o[c-2]=arguments[c];return this.tag(wu,r.apply(void 0,[a].concat(o)))}},{key:"processSubstitutions",value:function(r,a,d){var o=this.transformSubstitution(r.shift(),a);return"".concat(a,o,d)}},{key:"transformString",value:function(r){var a=function(o,c){return c.onString?c.onString(o):o};return this.transformers.reduce(a,r)}},{key:"transformSubstitution",value:function(r,a){var d=function(c,m){return m.onSubstitution?m.onSubstitution(c,a):c};return this.transformers.reduce(d,r)}},{key:"transformEndResult",value:function(r){var a=function(o,c){return c.onEndResult?c.onEndResult(o):o};return this.transformers.reduce(a,r)}}]),e}(),z=yu;var ku=function(){var n=arguments.length>0&&arguments[0]!==void 0?arguments[0]:"";return{onEndResult:function(a){if(n==="")return a.trim();if(n=n.toLowerCase(),n==="start"||n==="left")return a.replace(/^\s*/,"");if(n==="end"||n==="right")return a.replace(/\s*$/,"");throw new Error("Side not supported: "+n)}}},O=ku;function Tu(e){if(Array.isArray(e)){for(var n=0,r=Array(e.length);n<e.length;n++)r[n]=e[n];return r}else return Array.from(e)}var xu=function(){var n=arguments.length>0&&arguments[0]!==void 0?arguments[0]:"initial";return{onEndResult:function(a){if(n==="initial"){var d=a.match(/^[^\S\n]*(?=\S)/gm),o=d&&Math.min.apply(Math,Tu(d.map(function(m){return m.length})));if(o){var c=new RegExp("^.{"+o+"}","gm");return a.replace(c,"")}return a}if(n==="all")return a.replace(/^[^\S\n]+/gm,"");throw new Error("Unknown type: "+n)}}},G=xu;var Au=function(n,r){return{onEndResult:function(d){if(n==null||r==null)throw new Error("replaceResultTransformer requires at least 2 arguments.");return d.replace(n,r)}}},Q=Au;var Eu=function(n,r){return{onSubstitution:function(d,o){if(n==null||r==null)throw new Error("replaceSubstitutionTransformer requires at least 2 arguments.");return d==null?d:d.toString().replace(n,r)}}},de=Eu;var Su={separator:"",conjunction:"",serial:!1},Lu=function(){var n=arguments.length>0&&arguments[0]!==void 0?arguments[0]:Su;return{onSubstitution:function(a,d){if(Array.isArray(a)){var o=a.length,c=n.separator,m=n.conjunction,_=n.serial,x=d.match(/(\n?[^\S\n]+)$/);if(x?a=a.join(c+x[1]):a=a.join(c+" "),m&&o>1){var S=a.lastIndexOf(c);a=a.slice(0,S)+(_?c:"")+" "+m+a.slice(S+1)}}return a}}},N=Lu;var Pu=function(n){return{onSubstitution:function(a,d){if(n!=null&&typeof n=="string")typeof a=="string"&&a.includes(n)&&(a=a.split(n));else throw new Error("You need to specify a string character to split by.");return a}}},be=Pu;var Wr=function(n){return n!=null&&!Number.isNaN(n)&&typeof n!="boolean"},Mu=function(){return{onSubstitution:function(r){return Array.isArray(r)?r.filter(Wr):Wr(r)?r:""}}},Ve=Mu;var No=new z(N({separator:","}),G,O);var Yo=new z(N({separator:",",conjunction:"and"}),G,O);var nc=new z(N({separator:",",conjunction:"or"}),G,O);var fc=new z(be(`
`),Ve,N,G,O);var Ec=new z(be(`
`),N,G,O,de(/&/g,"&amp;"),de(/</g,"&lt;"),de(/>/g,"&gt;"),de(/"/g,"&quot;"),de(/'/g,"&#x27;"),de(/`/g,"&#x60;"));var Ic=new z(Q(/(?:\n(?:\s*))+/g," "),O);var Nc=new z(Q(/(?:\n\s*)/g,""),O);var Yc=new z(N({separator:","}),Q(/(?:\s+)/g," "),O);var ns=new z(N({separator:",",conjunction:"or"}),Q(/(?:\s+)/g," "),O);var ss=new z(N({separator:",",conjunction:"and"}),Q(/(?:\s+)/g," "),O);var ws=new z(N,G,O);var Es=new z(N,Q(/(?:\s+)/g," "),O);var Is=new z(G,O);var Uu=new z(G("all"),O),We=Uu;var qi=Z(Ni()),Sd=e=>{let n=e,r=/\[\[.*?\]\]/g,a=n.match(r);if(a){let d=/\[\[.*?(?=(\]|\|))/,o=/\|.*(?=]])/;for(let c of a){let m=c.match(d);if(m){let _=c.match(o),x=Ld(m[0].replace("[[",""),_?_[0].replace("|",""):"");n=n.replace(c,x)}}}return n},Ld=(e,n)=>`[${n}](${encodeURI(e)})`,Pd=/!\[\[(.*)#\^(.*)\]\]/,Md=/!\[\[(.*)#((?!\^).*)\]\]/;var Rd=e=>{let n=e,r=e.match(new RegExp(`(${Pd.source})|(${Md.source})`,"g"));return r==null||r.forEach(a=>n=n.replace(a,a.substring(1))),n},Id=e=>{let n=/( +)#[^\s#]+|^#[^\s#]+/gm,r=e.match(n);if(!r)return e;let a=e;for(let d of r)a=a.replace(d,`<span class="hashtag">${d}</span>`);return a};var Fd=e=>{let n=Id(e);return n=Rd(n),n=Sd(n),n=Od(n),Ki()&&(n=Ud(n)),n},ze=e=>{let n=Fd(e);return new Bi.default.Converter({tables:!0,simpleLineBreaks:!0,strikethrough:!0,tasklists:!0,smartIndentationFix:!0}).makeHtml(n)},$i=(e,n,r)=>{let a=document.createElement("div"),d=r.substr(e,n-e);return a.innerHTML=ze(d),a},Ui=(e,n)=>{let r=document.createElement("div"),a=e.position.start.offset,d=e.position.end.offset,o=n.substr(a,d-a),c=o.indexOf(`^${e.id}`);return c!==-1&&(o=o.slice(0,c)+o.slice(c+e.id.length+1)),r.innerHTML=ze(o),r},ir=(e,n)=>{jd(e),Hd(e,n.app),zd(e,n.app),Dd(e),qd()&&Bd(e),Ki()&&Vd(e)},jd=e=>{e.querySelectorAll("pre > code").forEach(r=>{r.addClass("line-numbers")})},Hd=(e,n)=>{e.querySelectorAll("img").forEach(a=>{if(a.getAttr("src")){let o=n.metadataCache.getFirstLinkpathDest(decodeURI(a.getAttr("src")),"");if(o){let c=De(n.vault,o);a.setAttr("src",c);let m=a.getAttr("alt");if(m){let _=Be(m);_&&(a.width=_.width,_.height&&(a.height=_.height))}}}})},zd=(e,n)=>{e.querySelectorAll("a").forEach(a=>{let d=a.getAttr("href");a.innerText===""&&(a.innerText=decodeURI(d)),d.match(new RegExp(".*#.*"))&&(d=d.match(new RegExp(".*(?=#)"))[0]),n.metadataCache.getFirstLinkpathDest(decodeURI(d),"")&&(a.setAttr("href",decodeURI(d)),a.addClass("oz-obsidian-inner-link")),a.innerText.startsWith("#")&&a.addClass("tag")})},Od=e=>{try{return(0,qi.default)(e).body}catch(n){return e}},Dd=e=>{let n=e.querySelectorAll('code[class*="language-ad-"]');n==null||n.forEach(r=>{let a=r.className,d=/language-ad-.*?(?=\s)/,o=a.match(d),c=o?o[0].replace("language-ad-",""):"Note",m=Nd(c,r.innerHTML);r.parentElement.replaceWith(m)})},Nd=(e,n)=>{let r=Vi[e]?Vi[e]:"68, 138, 255",a=document.createElement("div");a.addClass("oz-admonition"),a.style.cssText=`--oz-admonition-color: ${r};`;let d=a.createEl("div");d.addClass("oz-admonition-title");let o=d.createEl("div");o.addClass("oz-admonition-title-content");let c=o.createEl("div");c.addClass("oz-admonition-title-markdown"),c.innerText=e;let m=a.createEl("div");m.addClass("oz-admonition-content-holder");let _=m.createEl("div");return _.addClass("oz-admonition-content"),_.innerHTML=ze(n),a},Vi={abstract:"0, 176, 255",attention:"255, 145, 0",bug:"245, 0, 87",caution:"255, 145, 0",check:"0, 200, 83",cite:"158, 158, 158",danger:"255, 23, 68",done:"0, 200, 83",error:"255, 23, 68",example:"124, 77, 255",fail:"255, 82, 82",failure:"255, 82, 82",faq:"100, 221, 23",help:"100, 221, 23",hint:"0, 191, 165",important:"0, 191, 165",info:"0, 184, 212",missing:"255, 82, 82",note:"68, 138, 255",question:"100, 221, 23",quote:"158, 158, 158",seealso:"68, 138, 255",success:"0, 200, 83",summary:"0, 176, 255",tip:"0, 191, 165",todo:"0, 184, 212"},Bd=e=>{let n=e.querySelectorAll('code[class*="language-mermaid"]');n.length!==0&&(window.mermaid.initialize($d),n==null||n.forEach(r=>{let a=document.createElement("div"),d=Math.floor(Math.random()*999999);a.id=`mermaid-${d}`,a.innerHTML=r.innerHTML;try{window.mermaid.mermaidAPI.render(`mermaid-${d}`,Gi(r.innerHTML),o=>{a.innerHTML=o}),r.parentElement.replaceWith(a)}catch(o){let c=document.createElement("p");c.addClass("mermaid-error-information"),c.innerText="Syntax Error in Mermaid graph",r.parentElement.prepend(c)}}))},Wi={nbsp:" ",cent:"\xA2",pound:"\xA3",yen:"\xA5",euro:"\u20AC",copy:"\xA9",reg:"\xAE",lt:"<",gt:">",quot:'"',amp:"&",apos:"'"},Gi=e=>e.replace(/\&([^;]+);/g,function(n,r){var a;return r in Wi?Wi[r]:(a=r.match(/^#x([\da-fA-F]+)$/))?String.fromCharCode(parseInt(a[1],16)):(a=r.match(/^#(\d+)$/))?String.fromCharCode(~~a[1]):n}),qd=()=>window.mermaid,$d={startOnLoad:!0,flowchart:{useMaxWidth:!1,htmlLabels:!0,curve:"cardinal"},securityLevel:"loose",theme:"forest",logLevel:5},Ud=e=>{let n=e,r=/\$\$.*?\$\$/gs,a=/\$\$.*?(?=\$\$)/s,d=e.match(r);if(d&&d.length>0)for(let _ of d){let x=_.match(a);!x||(n=n.replace(_,Yi(x[0].replace("$$",""),"newline")))}let o=/\$[^\s].*[^\s]\$/g,c=/\$.*?(?=\$)/,m=e.match(o);if(m&&m.length>0)for(let _ of m){let x=_.match(c);x&&(n=n.replace(_,Yi(x[0].replace("$",""),"inline")))}return n},Yi=(e,n)=>We`
        <pre class="language-mathjax">
            <code class="language-mathjax ${n==="inline"?"inline":""}">
                ${e}
            </code>
        </pre>
    `,Vd=e=>{let n=e.querySelectorAll("code.language-mathjax");for(let r=0;r<n.length;r++){let a=n[r],d=a.classList.contains("inline");if(d){let o=a.parentElement.previousElementSibling;o&&o.classList.add("inline-block")}Wd(a.innerHTML,d?"inline":"newline").then(o=>{a.parentElement.replaceWith(o)})}},Wd=(e,n)=>ne(void 0,null,function*(){let r;n==="inline"?(r=document.createElement("span"),r.classList.add("inline-mathjax-block")):n==="newline"&&(r=document.createElement("div"),r.classList.add("newline-mathjax-block"));let a=Gi(e),d=yield window.MathJax.tex2chtmlPromise(a,{display:n!=="inline"}),o=window.MathJax.startup.adaptor;r.innerHTML=o.outerHTML(d);let c=document.createElement("style");return c.innerHTML=o.textContent(window.MathJax.chtmlStylesheet()),r.appendChild(c),r}),Ki=()=>{var e;return(e=window.MathJax)==null?void 0:e.version};var te=Z(require("@codemirror/view"));var ur=e=>te.Decoration.replace({widget:new Ji(e),inclusive:!1}),Er=e=>te.Decoration.replace({widget:new Zi(e),inclusive:!1}),Cr=e=>te.Decoration.replace({widget:new Qi(e),inclusive:!1}),ar=e=>te.Decoration.replace({widget:new Xi(e),inclusive:!1}),Ji=class extends te.WidgetType{constructor({url:n,altText:r,filePath:a}){super();this.url=n,this.altText=r,this.filePath=a}eq(n){return n.altText===this.altText&&n.filePath===this.filePath}toDOM(){let n=document.createElement("div");n.addClass("oz-image-widget-cm6");let r=n.createEl("img");r.src=this.url,r.setAttr("data-path",this.filePath);let a=Be(this.altText);return a&&(r.width=a.width,a.height&&(r.height=a.height)),r.alt=this.altText,n}ignoreEvent(){return!0}},Zi=class extends te.WidgetType{constructor({url:n,filePath:r}){super();this.url=n,this.filePath=r}toDOM(){let n=document.createElement("div");n.addClass("oz-pdf-widget-cm6");let r=n.createEl("embed");return r.src=this.url,r.type="application/pdf",r.width="100%",r.height="800px",n}eq(n){return n.filePath===this.filePath||n.url===this.url}ignoreEvent(){return!0}},Qi=class extends te.WidgetType{constructor({htmlText:n,htmlEl:r}){super();this.htmlText=n,this.htmlEl=r}toDOM(){let n=document.createElement("div");return n.addClass("oz-custom-html-widget-cm6"),this.htmlText?n.innerHTML=this.htmlText.trim():n.appendChild(this.htmlEl),n}eq(n){return n.htmlText===this.htmlText}ignoreEvent(){return!0}},Xi=class extends te.WidgetType{constructor({type:n,htmlDivElement:r,filePath:a,blockRef:d}){super();this.type=n,this.htmlDivElement=r,this.filePath=a,this.blockRef=d}toDOM(){let n=document.createElement("div");return n.addClasses(["oz-transclusion-widget-cm6",this.type]),n.appendChild(this.htmlDivElement),n}eq(n){return n.filePath+n.blockRef===this.filePath+this.blockRef}ignoreEvent(){return!0}};var eu=["file-transclusion","header-transclusion","blockid-transclusion"],ru=e=>{let{lineText:n,plugin:r,sourceFile:a}=e,d=/!\[\[.*?(jpe?g|png|gif|svg|bmp|webp).*?\]\]/,o=n.match(d);if(o){let g=/\[\[.*(jpe?g|png|gif|svg|bmp|webp)/,T=o[0].match(g);if(T){let b=T[0].replace("[[",""),w=r.app.metadataCache.getFirstLinkpathDest(decodeURIComponent(b),a.path);if(w){let y=/\|.*(?=]])/,l=o[0].match(y);return{type:"vault-image",match:o[0],linkText:b,altText:l?l[0].replace("|",""):"",blockRef:"",file:w}}}}let c=/!\[\[.*(pdf)(.*)?\]\]/,m=n.match(c);if(m){let g=/\[\[.*.pdf/,T=m[0].match(g);if(T){let b=T[0].replace("[[",""),w=r.app.metadataCache.getFirstLinkpathDest(decodeURIComponent(b),a.path);if(w){let y=new RegExp("#page=[0-9]+"),l=m[0].match(y);return{type:"pdf-file",match:m[0],linkText:"",altText:"",blockRef:l?l[0]:"",file:w}}}}let _=/!\[(^$|.*)\]\(.*(pdf)(.*)?\)/,x=n.match(_);if(x){let g=/\(.*.pdf/,T=x[0].match(g);if(T){let b=T[0].replace("(",""),w=/(http[s]?:\/\/)([^\/\s]+\/)(.*)/,y=new RegExp("#page=[0-9]+"),l=x[0].match(y);if(w.test(b))return{type:"pdf-link",match:x[0],linkText:b,altText:"",blockRef:l?l[0]:""};{let k=r.app.metadataCache.getFirstLinkpathDest(decodeURIComponent(b),a.path);if(k)return{type:"pdf-file",match:x[0],linkText:k.path,altText:"",blockRef:l?l[0]:"",file:k}}}}let S=/(http[s]?:\/\/)([^\/\s]+\/)(.*)/,A=/!\[[^)]*\]\([a-z][a-z0-9+\-.]+:\/[^)]*\)/,E=n.match(A);if(E){let g=/\(.*(?=\))/,T=E[0].match(g);if(T&&T[0].replace("(","").match(S)){let b=/\[(^$|.*)(?=\])/,w=E[0].match(b);return{type:"external-image",match:E[0],linkText:T[0].replace("(",""),altText:w?w[0].replace("[",""):"",blockRef:""}}}let v=/!\[(^$|.*?)\]\(.*?(jpe?g|png|gif|svg|bmp|webp)\)/,M=n.match(v);if(M){let g=/\(.*(jpe?g|png|gif|svg|bmp|webp)/,T=M[0].match(g);if(T){let b=T[0].replace("(",""),w=r.app.metadataCache.getFirstLinkpathDest(decodeURIComponent(b),a.path);if(w){let y=/\[(^$|.*)(?=\])/,l=M[0].match(y);return{type:"vault-image",match:M[0],linkText:b,altText:l?l[0].replace("[",""):"",blockRef:"",file:w}}}}let F=/!\[\[.*(msg|eml)(.*)?\]\]/,I=n.match(F);if(I){let g=/\[\[.*.(msg|eml)/,T=I[0].match(g);if(T){let b=T[0].replace("[[",""),w=r.app.metadataCache.getFirstLinkpathDest(decodeURIComponent(b),a.path);if(w)return{type:"msg-file",match:I[0],linkText:"",altText:"",blockRef:"",file:w}}}let W=/!\[(^$|.*?)\]\(.*?\)/,ue=/!\[\[.*?\]\]/,i=n.match(W),t=n.match(ue);if(i||t){let g=/\]\(.*?(?=\))/,T=/\[\[.*?((?=\|))|\[\[.*?(?=\]\])/,b=n.match(i?g:T);if(b){let w=b[0].replace("](","").replace("[[",""),y=r.app.metadataCache.getFirstLinkpathDest(decodeURIComponent(w),a.path);if(y&&Br(r.app)&&qr(y)){let C=i?/\[(^$|.*)(?=\])/:/\|.*(?=]])/,L=n.match(C);return{type:"excalidraw",match:i?i[0]:t[0],linkText:y.path,altText:L?L[0].replace("[","").replace("|",""):"",blockRef:"",file:y}}}if(t){let w=/!\[\[(.*)(?=#)/,y=/!\[\[(.*)#\^(.*)\]\]/,l=n.match(y);if(l){let J=l[0].match(w)[0].replace("![[",""),D=r.app.metadataCache.getFirstLinkpathDest(decodeURIComponent(J),a.path);if(D&&D.extension==="md"){let U=/#\^.*(?=]])/;return{type:"blockid-transclusion",match:t[0],linkText:D.path,altText:"",blockRef:n.match(U)[0].replace("#^",""),file:D}}}let k=/!\[\[(.*)#((?!\^).*)\]\]/,C=n.match(k);if(C){let J=C[0].match(w)[0].replace("![[",""),D=r.app.metadataCache.getFirstLinkpathDest(decodeURIComponent(J),a.path);if(D&&D.extension==="md"){let U=/#.*(?=]])/;return{type:"header-transclusion",match:t[0],linkText:D.path,altText:"",blockRef:n.match(U)[0].replace("#",""),file:D}}}let L=/\[\[.*?(?=\]\])/,P=n.match(L)[0].replace("[[",""),q=r.app.metadataCache.getFirstLinkpathDest(decodeURIComponent(P),a.path);if(q&&P!==""&&q.extension==="md")return{type:"file-transclusion",match:t[0],linkText:q.path,altText:"",blockRef:"",file:q}}}let u=/(?:<iframe[^>]*)(?:(?:\/>)|(?:>.*?<\/iframe>))/,s=n.match(u);if(s)return{type:"iframe",match:s[0],linkText:"",altText:"",blockRef:""};let p=/!\[(^$|.*)\]\((file\:\/\/\/|app\:\/\/local\/).*(.pdf|.jpe?g|.png|.gif|.svg|.bmp|.webp)(.*)?\)/,h=/(file\:\/\/\/|app\:\/\/local\/).*(.pdf|.jpe?g|.png|.gif|.svg|.bmp|.webp)/,f=n.match(p);if(f){let g=f[0].match(h);if(g){let T=Or(),b=g[0].replace(g[0].startsWith("file:///")?"file:///":"app://local/",T),w=/#page=[0-9]+\)/,y=f[0].match(w),l=/\[(^$|.*)(?=\])/,k=f[0].match(l);return{type:b.endsWith(".pdf")?"local-pdf":"local-image",match:f[0],linkText:decodeURI(b),altText:k?k[0].replace("[",""):"",blockRef:y?y[0].replace(")",""):""}}}return null};var nu=Z(require("@codemirror/language")),iu=(e,n)=>{let r=[];return n.length>0&&(r=Array.from({length:n.lines},(a,d)=>d+1)),r};var Sr=class{constructor(n){this.decoCache={};this.debouncedUpdate=(0,Ce.debounce)(this.updateAsyncDecorations,50,!0);this.editor=n}updateAsyncDecorations(n){return ne(this,null,function*(){let{view:r,state:a,newDoc:d,plugin:o}=n,c=iu(a||r.state,d);if(c.length>0){let m=yield this.getDecorationsForLines({lineNrs:c,view:r,newDoc:d,plugin:o});(m||this.editor.state.field(Le.field).size)&&this.editor.dispatch({effects:Le.update.of(m||au.Decoration.none)})}})}getDecorationsForLines(n){return ne(this,null,function*(){var x;let{newDoc:r,view:a,lineNrs:d,plugin:o}=n,c=new uu.RangeSetBuilder,_=a.state.field(Ce.editorViewField).file;if(d.length>0)for(let S of d){let A=r.line(S),E=null,v=ru({lineText:A.text,plugin:o,sourceFile:_});if(v&&["external-image","local-image"].includes(v.type)&&o.settings.renderImages){let M=v.linkText+v.altText;E=this.decoCache[M],E||(E=this.decoCache[M]=ur({url:v.linkText,altText:v.altText,filePath:v.linkText}))}else if(v&&v.type==="vault-image"&&o.settings.renderImages){let M=v.file.path+v.altText;if(E=this.decoCache[M],!E){let F=De(o.app.vault,v.file);E=this.decoCache[M]=ur({url:F,altText:v.altText,filePath:v.file.path})}}else if(v&&v.type==="excalidraw"&&o.settings.renderExcalidraw){let M=v.file.path+v.altText;if(E=this.decoCache[M],!E){let F=yield $r(v.file);E=this.decoCache[M]=ur({url:URL.createObjectURL(F),altText:v.altText,filePath:v.file.path})}}else if(v&&["pdf-link","local-pdf"].includes(v.type)&&o.settings.renderPDF){let M=v.linkText+v.blockRef;E=this.decoCache[M],E||(E=this.decoCache[M]=Er({url:M,filePath:M}))}else if(v&&v.type==="pdf-file"&&o.settings.renderPDF){let M=v.file.path+v.blockRef;if(E=this.decoCache[M],!E){let F=yield o.app.vault.adapter.readBinary((0,Ce.normalizePath)(v.file.path)),I=new Uint8Array(F),W=new Blob([I],{type:"application/pdf"});E=Er({url:URL.createObjectURL(W)+v.blockRef,filePath:v.file.path})}}else if(o.settings.renderMsgFile&&v&&v.type==="msg-file"){let M=Ne(o.app,"msg-handler");if(M){let F=v.file,I=document.createElement("div");yield M.renderMSG({msgFile:F,targetEl:I}),E=this.decoCache[F.path]=Cr({htmlEl:I})}}else if(v&&eu.contains(v.type)&&o.settings.renderTransclusion){let M=v.file.path+v.blockRef+v.file.stat.mtime;if(E=this.decoCache[M],!E){let F=o.app.metadataCache.getCache(v.file.path),I=yield o.app.vault.cachedRead(v.file);if(v.type==="blockid-transclusion"){let W=v.blockRef;if(F=o.app.metadataCache.getCache(v.file.path),F.blocks&&F.blocks[W]){let ue=F.blocks[W];if(ue){let i=Ui(ue,I);ir(i,o),E=this.decoCache[M]=ar({htmlDivElement:i,type:v.type,filePath:v.file.path,blockRef:v.blockRef})}}}else if(v.type==="header-transclusion"){let W=(x=F.headings)==null?void 0:x.find(ue=>lr(ue.heading)===lr(v.blockRef));if(W){let ue=W.position.start.offset,i=F.headings.indexOf(W),t=I.length;for(let s of F.headings.slice(i+1))if(s.level<=W.level){t=s.position.start.offset;break}let u=$i(ue,t,I);ir(u,o),E=this.decoCache[M]=ar({htmlDivElement:u,type:v.type,filePath:v.file.path,blockRef:v.blockRef})}}else if(v.type==="file-transclusion"&&I!==""){let W=document.createElement("div");W.innerHTML=ze(I),ir(W,o),E=this.decoCache[M]=ar({htmlDivElement:W,type:v.type,filePath:v.file.path,blockRef:v.blockRef})}}}else v&&v.type==="iframe"&&o.settings.renderIframe&&(E=Cr({htmlText:v.match}));E!==null&&c.add(A.to,A.to,E)}return c.finish()})}};var du=e=>{let{plugin:n}=e;return tu.ViewPlugin.fromClass(class{constructor(a){if(this.decoManager=new Sr(a),!fr(n.app)){let d=a.state;this.decoManager.updateAsyncDecorations({view:a,state:d,newDoc:d.doc,plugin:n})}}update(a){if((a.docChanged||a.viewportChanged)&&!fr(n.app)){let d=a.view.state;this.decoManager.updateAsyncDecorations({view:a.view,plugin:n,newDoc:d.doc})}}destroy(){}})};var ou=e=>{let{plugin:n}=e;return[du({plugin:n}),Le.field]};var Lr=class extends le.Plugin{constructor(){super(...arguments);this.editorExtensions=[];this.loadCM6Extension=()=>{let n=ou({plugin:this});this.editorExtensions.push(n),this.app.workspace.updateOptions()};this.unloadCM6Extension=()=>{this.editorExtensions.length=0,this.app.workspace.updateOptions()};this.onImageMenu=(n,r)=>{let a=this.app.vault.getAbstractFileByPath(r.dataset.path);if(a instanceof le.TFile)return n.preventDefault(),n.stopPropagation(),Nr(n,this,a),!1};this.onClickTransclusionLink=(n,r)=>{n.preventDefault(),n.stopPropagation(),zr(n,r.getAttr("href"),this.app)};this.filePreviewOnHover=(n,r)=>{this.app.workspace.trigger("link-hover",{},n.target,r.getAttr("href"),r.getAttr("href"))}}onload(){return ne(this,null,function*(){console.log("Image in Editor Plugin is loaded"),this.addSettingTab(new sr(this.app,this)),yield this.loadSettings(),this.addCommand({id:"toggle-render-all",name:"Toggle Render All (Requires reload of vault)",callback:()=>{this.settings.cm6RenderAll=!this.settings.cm6RenderAll,this.saveSettings()}}),this.addCommand({id:"toggle-render-images",name:"Toggle Render Images",callback:()=>{this.settings.renderImages=!this.settings.renderImages,this.saveSettings()}}),this.addCommand({id:"toggle-render-pdfs",name:"Toggle Render PDF",callback:()=>{this.settings.renderPDF=!this.settings.renderPDF,this.saveSettings()}}),this.addCommand({id:"toggle-render-transclusion",name:"Toggle Render Transclusions",callback:()=>{this.settings.renderTransclusion=!this.settings.renderTransclusion,this.saveSettings()}}),this.addCommand({id:"toggle-render-iframe",name:"Toggle Render Iframes",callback:()=>{this.settings.renderIframe=!this.settings.renderIframe,this.saveSettings()}}),this.addCommand({id:"toggle-render-excalidraw",name:"Toggle Render Excalidraws",callback:()=>{this.settings.renderExcalidraw=!this.settings.renderExcalidraw,this.saveSettings()}});try{(0,le.loadMathJax)(),(0,le.loadMermaid)()}catch(n){console.log(n)}this.registerEditorExtension(this.editorExtensions),this.settings.cm6RenderAll&&this.loadCM6Extension(),document.on("click",".oz-obsidian-inner-link",this.onClickTransclusionLink),document.on("contextmenu","div.oz-image-widget-cm6 img[data-path]",this.onImageMenu,!1),this.settings.previewOnHoverInternalLink&&document.on("mouseover",".oz-obsidian-inner-link",this.filePreviewOnHover)})}onunload(){document.off("contextmenu","div.oz-image-widget-cm6 img[data-path]",this.onImageMenu,!1),document.off("click",".oz-obsidian-inner-link",this.onClickTransclusionLink),document.off("mouseover",".oz-obsidian-inner-link",this.filePreviewOnHover),console.log("Image in Editor Plugin is unloaded")}loadSettings(){return ne(this,null,function*(){this.settings=Object.assign({},Hr,yield this.loadData())})}saveSettings(){return ne(this,null,function*(){yield this.saveData(this.settings)})}};
/*! showdown v 1.9.1 - 02-11-2019 */

/* nosourcemap */