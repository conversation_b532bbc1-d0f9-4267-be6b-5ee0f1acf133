--- 查询资金账号手机号（股权激励发布App验证）
SELECT a.*, t.CUST_CODE, t.CUST_NAME, e.FUND_CODE, t.ID_CODE
FROM (SELECT t.TEL_JOINKEY, t.TEL_POST_ENCRY
      FROM dwd.TEL_ENCRY_MAPPING t
      WHERE t.TEL_JOINKEY IN (SELECT t.CON_MOBILE_JOINKEY
                              FROM dws.ACCOUNT_BASIC_INFO t
                              WHERE t.CUST_CODE IN ('************'))) a
         LEFT JOIN dws.ACCOUNT_BASIC_INFO t ON a.TEL_JOINKEY = t.CON_MOBILE_JOINKEY
         LEFT JOIN dws.ACCOUNT_FUND_INFO e ON t.CUST_CODE = e.CUST_CODE;

--- 查询历史融资行权委托
SELECT fundid, orderqty, ordersno, orderdate, ordertime, taxamt, orderprice
FROM (SELECT *
      FROM (SELECT fundid, orderqty, ordersno, orderdate, ordertime, cancelflag, orderstatus, taxamt, orderprice
            FROM run1_his.h_fjy_orderrec
            UNION ALL
            SELECT fundid, orderqty, ordersno, orderdate, ordertime, cancelflag, orderstatus, taxamt, orderprice
            FROM run2_his.h_fjy_orderrec
            UNION ALL
            SELECT fundid, orderqty, ordersno, orderdate, ordertime, cancelflag, orderstatus, taxamt, orderprice
            FROM run3_his.h_fjy_orderrec
            UNION ALL
            SELECT fundid, orderqty, ordersno, orderdate, ordertime, cancelflag, orderstatus, taxamt, orderprice
            FROM run4_his.h_fjy_orderrec
            UNION ALL
            SELECT fundid, orderqty, ordersno, orderdate, ordertime, cancelflag, orderstatus, taxamt, orderprice
            FROM run5_his.h_fjy_orderrec
            UNION ALL
            SELECT fundid, orderqty, ordersno, orderdate, ordertime, cancelflag, orderstatus, taxamt, orderprice
            FROM rzrq_his.h_fjy_orderrec
            UNION ALL
            SELECT fundid, orderqty, ordersno, orderdate, ordertime, cancelflag, orderstatus, taxamt, orderprice
            FROM run7_his.h_fjy_orderrec
            UNION ALL
            SELECT fundid, orderqty, ordersno, orderdate, ordertime, cancelflag, orderstatus, taxamt, orderprice
            FROM run8_his.h_fjy_orderrec
            UNION ALL
            SELECT fundid, orderqty, ordersno, orderdate, ordertime, cancelflag, orderstatus, taxamt, orderprice
            FROM run9_his.h_fjy_orderrec) t
      WHERE t.cancelflag = 'F'
        AND t.orderstatus = '1'
        AND t.ordersno =  #{orderSno}
        AND t.orderdate = #{orderDate}
        AND t.fundid = 110200000012
     ) a
;





