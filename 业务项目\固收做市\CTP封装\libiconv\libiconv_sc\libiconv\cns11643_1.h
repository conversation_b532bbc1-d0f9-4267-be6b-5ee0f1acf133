/*
 * Copyright (C) 1999-2005 Free Software Foundation, Inc.
 * This file is part of the GNU LIBICONV Library.
 *
 * The GNU LIBICONV Library is free software; you can redistribute it
 * and/or modify it under the terms of the GNU Library General Public
 * License as published by the Free Software Foundation; either version 2
 * of the License, or (at your option) any later version.
 *
 * The GNU LIBICONV Library is distributed in the hope that it will be
 * useful, but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
 * Library General Public License for more details.
 *
 * You should have received a copy of the GNU Library General Public
 * License along with the GNU LIBICONV Library; see the file COPYING.LIB.
 * If not, write to the Free Software Foundation, Inc., 51 Franklin Street,
 * Fifth Floor, Boston, MA 02110-1301, USA.
 */

/*
 * CNS 11643-1992 plane 1
 */

static const unsigned short cns11643_1_2uni_page21[500] = {
  /* 0x21 */
  0x3000, 0xff0c, 0x3001, 0x3002, 0xff0e, 0x30fb, 0xff1b, 0xff1a,
  0xff1f, 0xff01, 0xfe30, 0x2026, 0x2025, 0xfe50, 0xfe51, 0xfe52,
  0x00b7, 0xfe54, 0xfe55, 0xfe56, 0xfe57, 0xfe31, 0x2014, 0xfe32,
  0x2013, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xff08, 0xff09, 0xfe35,
  0xfe36, 0xff5b, 0xff5d, 0xfe37, 0xfe38, 0x3014, 0x3015, 0xfe39,
  0xfe3a, 0x3010, 0x3011, 0xfe3b, 0xfe3c, 0x300a, 0x300b, 0xfe3d,
  0xfe3e, 0x3008, 0x3009, 0xfe3f, 0xfe40, 0x300c, 0x300d, 0xfe41,
  0xfe42, 0x300e, 0x300f, 0xfe43, 0xfe44, 0xfe59, 0xfe5a, 0xfe5b,
  0xfe5c, 0xfe5d, 0xfe5e, 0x2018, 0x2019, 0x201c, 0x201d, 0x301d,
  0x301e, 0x2032, 0x2035, 0xff03, 0xff06, 0xff0a, 0x203b, 0x00a7,
  0x3003, 0x25cb, 0x25cf, 0x25b3, 0x25b2, 0x25ce, 0x2606, 0x2605,
  0x25c7, 0x25c6, 0x25a1, 0x25a0, 0x25bd, 0x25bc,
  /* 0x22 */
  0x32a3, 0x2105, 0x203e, 0xfffd, 0xff3f, 0xfffd, 0xfe49, 0xfe4a,
  0xfe4d, 0xfe4e, 0xfe4b, 0xfe4c, 0xfe5f, 0xfe60, 0xfe61, 0xff0b,
  0xff0d, 0x00d7, 0x00f7, 0x00b1, 0x221a, 0xff1c, 0xff1e, 0xff1d,
  0x2266, 0x2267, 0x2260, 0x221e, 0x2252, 0x2261, 0xfe62, 0xfe63,
  0xfe64, 0xfe66, 0xfe65, 0x223c, 0x2229, 0x222a, 0x22a5, 0x2220,
  0x221f, 0x22bf, 0x33d2, 0x33d1, 0x222b, 0x222e, 0x2235, 0x2234,
  0x2640, 0x2642, 0x2641, 0x2609, 0x2191, 0x2193, 0x2192, 0x2190,
  0x2196, 0x2197, 0x2199, 0x2198, 0x2016, 0xff5c, 0xff0f, 0xff3c,
  0x2215, 0xfe68, 0xff04, 0xffe5, 0x3012, 0xffe0, 0xffe1, 0xff05,
  0xff20, 0x2103, 0x2109, 0xfe69, 0xfe6a, 0xfe6b, 0x33d5, 0x339c,
  0x339d, 0x339e, 0x33ce, 0x33a1, 0x338e, 0x338f, 0x33c4, 0x00b0,
  0x5159, 0x515b, 0x515e, 0x515d, 0x5161, 0x5163,
  /* 0x23 */
  0x55e7, 0x74e9, 0x7cce, 0x2581, 0x2582, 0x2583, 0x2584, 0x2585,
  0x2586, 0x2587, 0x2588, 0x258f, 0x258e, 0x258d, 0x258c, 0x258b,
  0x258a, 0x2589, 0x253c, 0x2534, 0x252c, 0x2524, 0x251c, 0x2594,
  0x2500, 0x2502, 0x2595, 0x250c, 0x2510, 0x2514, 0x2518, 0x256d,
  0x256e, 0x2570, 0x256f, 0x2550, 0x255e, 0x256a, 0x2561, 0x25e2,
  0x25e3, 0x25e5, 0x25e4, 0x2571, 0x2572, 0x2573, 0xfffd, 0xfffd,
  0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd,
  0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd,
  0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd,
  0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd,
  0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd,
  0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd,
  /* 0x24 */
  0xff10, 0xff11, 0xff12, 0xff13, 0xff14, 0xff15, 0xff16, 0xff17,
  0xff18, 0xff19, 0x2160, 0x2161, 0x2162, 0x2163, 0x2164, 0x2165,
  0x2166, 0x2167, 0x2168, 0x2169, 0x3021, 0x3022, 0x3023, 0x3024,
  0x3025, 0x3026, 0x3027, 0x3028, 0x3029, 0xfffd, 0x5344, 0xfffd,
  0xff21, 0xff22, 0xff23, 0xff24, 0xff25, 0xff26, 0xff27, 0xff28,
  0xff29, 0xff2a, 0xff2b, 0xff2c, 0xff2d, 0xff2e, 0xff2f, 0xff30,
  0xff31, 0xff32, 0xff33, 0xff34, 0xff35, 0xff36, 0xff37, 0xff38,
  0xff39, 0xff3a, 0xff41, 0xff42, 0xff43, 0xff44, 0xff45, 0xff46,
  0xff47, 0xff48, 0xff49, 0xff4a, 0xff4b, 0xff4c, 0xff4d, 0xff4e,
  0xff4f, 0xff50, 0xff51, 0xff52, 0xff53, 0xff54, 0xff55, 0xff56,
  0xff57, 0xff58, 0xff59, 0xff5a, 0x0391, 0x0392, 0x0393, 0x0394,
  0x0395, 0x0396, 0x0397, 0x0398, 0x0399, 0x039a,
  /* 0x25 */
  0x039b, 0x039c, 0x039d, 0x039e, 0x039f, 0x03a0, 0x03a1, 0x03a3,
  0x03a4, 0x03a5, 0x03a6, 0x03a7, 0x03a8, 0x03a9, 0x03b1, 0x03b2,
  0x03b3, 0x03b4, 0x03b5, 0x03b6, 0x03b7, 0x03b8, 0x03b9, 0x03ba,
  0x03bb, 0x03bc, 0x03bd, 0x03be, 0x03bf, 0x03c0, 0x03c1, 0x03c3,
  0x03c4, 0x03c5, 0x03c6, 0x03c7, 0x03c8, 0x03c9, 0x3105, 0x3106,
  0x3107, 0x3108, 0x3109, 0x310a, 0x310b, 0x310c, 0x310d, 0x310e,
  0x310f, 0x3110, 0x3111, 0x3112, 0x3113, 0x3114, 0x3115, 0x3116,
  0x3117, 0x3118, 0x3119, 0x311a, 0x311b, 0x311c, 0x311d, 0x311e,
  0x311f, 0x3120, 0x3121, 0x3122, 0x3123, 0x3124, 0x3125, 0x3126,
  0x3127, 0x3128, 0x3129, 0x02d9, 0x02c9, 0x02ca, 0x02c7, 0x02cb,
  0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd,
  0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd,
  /* 0x26 */
  0x2460, 0x2461, 0x2462, 0x2463, 0x2464, 0x2465, 0x2466, 0x2467,
  0x2468, 0x2469, 0x2474, 0x2475, 0x2476, 0x2477, 0x2478, 0x2479,
  0x247a, 0x247b, 0x247c, 0x247d, 0x2170, 0x2171, 0x2172, 0x2173,
  0x2174, 0x2175, 0x2176, 0x2177, 0x2178, 0x2179,
};
static const unsigned short cns11643_1_2uni_page42[34] = {
  /* 0x42 */
  0x2400, 0x2401, 0x2402, 0x2403, 0x2404, 0x2405, 0x2406, 0x2407,
  0x2408, 0x2409, 0x240a, 0x240b, 0x240c, 0x240d, 0x240e, 0x240f,
  0x2410, 0x2411, 0x2412, 0x2413, 0x2414, 0x2415, 0x2416, 0x2417,
  0x2418, 0x2419, 0x241a, 0x241b, 0x241c, 0x241d, 0x241e, 0x241f,
  0x2421, 0x20ac,
};
static const unsigned short cns11643_1_2uni_page44[5401] = {
  /* 0x44 */
  0x4e00, 0x4e59, 0x4e01, 0x4e03, 0x4e43, 0x4e5d, 0x4e86, 0x4e8c,
  0x4eba, 0x513f, 0x5165, 0x516b, 0x51e0, 0x5200, 0x5201, 0x529b,
  0x5315, 0x5341, 0x535c, 0x53c8, 0x4e09, 0x4e0b, 0x4e08, 0x4e0a,
  0x4e2b, 0x4e38, 0x51e1, 0x4e45, 0x4e48, 0x4e5f, 0x4e5e, 0x4e8e,
  0x4ea1, 0x5140, 0x5203, 0x52fa, 0x5343, 0x53c9, 0x53e3, 0x571f,
  0x58eb, 0x5915, 0x5927, 0x5973, 0x5b50, 0x5b51, 0x5b53, 0x5bf8,
  0x5c0f, 0x5c22, 0x5c38, 0x5c71, 0x5ddd, 0x5de5, 0x5df1, 0x5df2,
  0x5df3, 0x5dfe, 0x5e72, 0x5efe, 0x5f0b, 0x5f13, 0x624d, 0x4e11,
  0x4e10, 0x4e0d, 0x4e2d, 0x4e30, 0x4e39, 0x4e4b, 0x5c39, 0x4e88,
  0x4e91, 0x4e95, 0x4e92, 0x4e94, 0x4ea2, 0x4ec1, 0x4ec0, 0x4ec3,
  0x4ec6, 0x4ec7, 0x4ecd, 0x4eca, 0x4ecb, 0x4ec4, 0x5143, 0x5141,
  0x5167, 0x516d, 0x516e, 0x516c, 0x5197, 0x51f6,
  /* 0x45 */
  0x5206, 0x5207, 0x5208, 0x52fb, 0x52fe, 0x52ff, 0x5316, 0x5339,
  0x5348, 0x5347, 0x5345, 0x535e, 0x5384, 0x53cb, 0x53ca, 0x53cd,
  0x58ec, 0x5929, 0x592b, 0x592a, 0x592d, 0x5b54, 0x5c11, 0x5c24,
  0x5c3a, 0x5c6f, 0x5df4, 0x5e7b, 0x5eff, 0x5f14, 0x5f15, 0x5fc3,
  0x6208, 0x6236, 0x624b, 0x624e, 0x652f, 0x6587, 0x6597, 0x65a4,
  0x65b9, 0x65e5, 0x66f0, 0x6708, 0x6728, 0x6b20, 0x6b62, 0x6b79,
  0x6bcb, 0x6bd4, 0x6bdb, 0x6c0f, 0x6c34, 0x706b, 0x722a, 0x7236,
  0x723b, 0x7247, 0x7259, 0x725b, 0x72ac, 0x738b, 0x4e19, 0x4e16,
  0x4e15, 0x4e14, 0x4e18, 0x4e3b, 0x4e4d, 0x4e4f, 0x4e4e, 0x4ee5,
  0x4ed8, 0x4ed4, 0x4ed5, 0x4ed6, 0x4ed7, 0x4ee3, 0x4ee4, 0x4ed9,
  0x4ede, 0x5145, 0x5144, 0x5189, 0x518a, 0x51ac, 0x51f9, 0x51fa,
  0x51f8, 0x520a, 0x52a0, 0x529f, 0x5305, 0x5306,
  /* 0x46 */
  0x5317, 0x531d, 0x4edf, 0x534a, 0x5349, 0x5361, 0x5360, 0x536f,
  0x536e, 0x53bb, 0x53ef, 0x53e4, 0x53f3, 0x53ec, 0x53ee, 0x53e9,
  0x53e8, 0x53fc, 0x53f8, 0x53f5, 0x53eb, 0x53e6, 0x53ea, 0x53f2,
  0x53f1, 0x53f0, 0x53e5, 0x53ed, 0x53fb, 0x56db, 0x56da, 0x5916,
  0x592e, 0x5931, 0x5974, 0x5976, 0x5b55, 0x5b83, 0x5c3c, 0x5de8,
  0x5de7, 0x5de6, 0x5e02, 0x5e03, 0x5e73, 0x5e7c, 0x5f01, 0x5f18,
  0x5f17, 0x5fc5, 0x620a, 0x6253, 0x6254, 0x6252, 0x6251, 0x65a5,
  0x65e6, 0x672e, 0x672c, 0x672a, 0x672b, 0x672d, 0x6b63, 0x6bcd,
  0x6c11, 0x6c10, 0x6c38, 0x6c41, 0x6c40, 0x6c3e, 0x72af, 0x7384,
  0x7389, 0x74dc, 0x74e6, 0x7518, 0x751f, 0x7528, 0x7529, 0x7530,
  0x7531, 0x7532, 0x7533, 0x758b, 0x767d, 0x76ae, 0x76bf, 0x76ee,
  0x77db, 0x77e2, 0x77f3, 0x793a, 0x79be, 0x7a74,
  /* 0x47 */
  0x7acb, 0x4e1e, 0x4e1f, 0x4e52, 0x4e53, 0x4e69, 0x4e99, 0x4ea4,
  0x4ea6, 0x4ea5, 0x4eff, 0x4f09, 0x4f19, 0x4f0a, 0x4f15, 0x4f0d,
  0x4f10, 0x4f11, 0x4f0f, 0x4ef2, 0x4ef6, 0x4efb, 0x4ef0, 0x4ef3,
  0x4efd, 0x4f01, 0x4f0b, 0x5149, 0x5147, 0x5146, 0x5148, 0x5168,
  0x5171, 0x518d, 0x51b0, 0x5217, 0x5211, 0x5212, 0x520e, 0x5216,
  0x52a3, 0x5308, 0x5321, 0x5320, 0x5370, 0x5371, 0x5409, 0x540f,
  0x540c, 0x540a, 0x5410, 0x5401, 0x540b, 0x5404, 0x5411, 0x540d,
  0x5408, 0x5403, 0x540e, 0x5406, 0x5412, 0x56e0, 0x56de, 0x56dd,
  0x5733, 0x5730, 0x5728, 0x572d, 0x572c, 0x572f, 0x5729, 0x5919,
  0x591a, 0x5937, 0x5938, 0x5984, 0x5978, 0x5983, 0x597d, 0x5979,
  0x5982, 0x5981, 0x5b57, 0x5b58, 0x5b87, 0x5b88, 0x5b85, 0x5b89,
  0x5bfa, 0x5c16, 0x5c79, 0x5dde, 0x5e06, 0x5e76,
  /* 0x48 */
  0x5e74, 0x5f0f, 0x5f1b, 0x5fd9, 0x5fd6, 0x620e, 0x620c, 0x620d,
  0x6210, 0x6263, 0x625b, 0x6258, 0x6536, 0x65e9, 0x65e8, 0x65ec,
  0x65ed, 0x66f2, 0x66f3, 0x6709, 0x673d, 0x6734, 0x6731, 0x6735,
  0x6b21, 0x6b64, 0x6b7b, 0x6c16, 0x6c5d, 0x6c57, 0x6c59, 0x6c5f,
  0x6c60, 0x6c50, 0x6c55, 0x6c61, 0x6c5b, 0x6c4d, 0x6c4e, 0x7070,
  0x725f, 0x725d, 0x767e, 0x7af9, 0x7c73, 0x7cf8, 0x7f36, 0x7f8a,
  0x7fbd, 0x8001, 0x8003, 0x800c, 0x8012, 0x8033, 0x807f, 0x8089,
  0x808b, 0x808c, 0x81e3, 0x81ea, 0x81f3, 0x81fc, 0x820c, 0x821b,
  0x821f, 0x826e, 0x8272, 0x827e, 0x866b, 0x8840, 0x884c, 0x8863,
  0x897f, 0x9621, 0x4e32, 0x4ea8, 0x4f4d, 0x4f4f, 0x4f47, 0x4f57,
  0x4f5e, 0x4f34, 0x4f5b, 0x4f55, 0x4f30, 0x4f50, 0x4f51, 0x4f3d,
  0x4f3a, 0x4f38, 0x4f43, 0x4f54, 0x4f3c, 0x4f46,
  /* 0x49 */
  0x4f63, 0x4f5c, 0x4f60, 0x4f2f, 0x4f4e, 0x4f36, 0x4f59, 0x4f5d,
  0x4f48, 0x4f5a, 0x514c, 0x514b, 0x514d, 0x5175, 0x51b6, 0x51b7,
  0x5225, 0x5224, 0x5229, 0x522a, 0x5228, 0x52ab, 0x52a9, 0x52aa,
  0x52ac, 0x5323, 0x5373, 0x5375, 0x541d, 0x542d, 0x541e, 0x543e,
  0x5426, 0x544e, 0x5427, 0x5446, 0x5443, 0x5433, 0x5448, 0x5442,
  0x541b, 0x5429, 0x544a, 0x5439, 0x543b, 0x5438, 0x542e, 0x5435,
  0x5436, 0x5420, 0x543c, 0x5440, 0x5431, 0x542b, 0x541f, 0x542c,
  0x56ea, 0x56f0, 0x56e4, 0x56eb, 0x574a, 0x5751, 0x5740, 0x574d,
  0x5747, 0x574e, 0x573e, 0x5750, 0x574f, 0x573b, 0x58ef, 0x593e,
  0x599d, 0x5992, 0x59a8, 0x599e, 0x59a3, 0x5999, 0x5996, 0x598d,
  0x59a4, 0x5993, 0x598a, 0x59a5, 0x5b5d, 0x5b5c, 0x5b5a, 0x5b5b,
  0x5b8c, 0x5b8b, 0x5b8f, 0x5c2c, 0x5c40, 0x5c41,
  /* 0x4a */
  0x5c3f, 0x5c3e, 0x5c90, 0x5c91, 0x5c94, 0x5c8c, 0x5deb, 0x5e0c,
  0x5e8f, 0x5e87, 0x5e8a, 0x5ef7, 0x5f04, 0x5f1f, 0x5f64, 0x5f62,
  0x5f77, 0x5f79, 0x5fd8, 0x5fcc, 0x5fd7, 0x5fcd, 0x5ff1, 0x5feb,
  0x5ff8, 0x5fea, 0x6212, 0x6211, 0x6284, 0x6297, 0x6296, 0x6280,
  0x6276, 0x6289, 0x626d, 0x628a, 0x627c, 0x627e, 0x6279, 0x6273,
  0x6292, 0x626f, 0x6298, 0x626e, 0x6295, 0x6293, 0x6291, 0x6286,
  0x6539, 0x653b, 0x6538, 0x65f1, 0x66f4, 0x675f, 0x674e, 0x674f,
  0x6750, 0x6751, 0x675c, 0x6756, 0x675e, 0x6749, 0x6746, 0x6760,
  0x6753, 0x6757, 0x6b65, 0x6bcf, 0x6c42, 0x6c5e, 0x6c99, 0x6c81,
  0x6c88, 0x6c89, 0x6c85, 0x6c9b, 0x6c6a, 0x6c7a, 0x6c90, 0x6c70,
  0x6c8c, 0x6c68, 0x6c96, 0x6c92, 0x6c7d, 0x6c83, 0x6c72, 0x6c7e,
  0x6c74, 0x6c86, 0x6c76, 0x6c8d, 0x6c94, 0x6c98,
  /* 0x4b */
  0x6c82, 0x7076, 0x707c, 0x707d, 0x7078, 0x7262, 0x7261, 0x7260,
  0x72c4, 0x72c2, 0x7396, 0x752c, 0x752b, 0x7537, 0x7538, 0x7682,
  0x76ef, 0x77e3, 0x79c1, 0x79c0, 0x79bf, 0x7a76, 0x7cfb, 0x7f55,
  0x8096, 0x8093, 0x809d, 0x8098, 0x809b, 0x809a, 0x80b2, 0x826f,
  0x8292, 0x828b, 0x828d, 0x898b, 0x89d2, 0x8a00, 0x8c37, 0x8c46,
  0x8c55, 0x8c9d, 0x8d64, 0x8d70, 0x8db3, 0x8eab, 0x8eca, 0x8f9b,
  0x8fb0, 0x8fc2, 0x8fc6, 0x8fc5, 0x8fc4, 0x5de1, 0x9091, 0x90a2,
  0x90aa, 0x90a6, 0x90a3, 0x9149, 0x91c6, 0x91cc, 0x9632, 0x962e,
  0x9631, 0x962a, 0x962c, 0x4e26, 0x4e56, 0x4e73, 0x4e8b, 0x4e9b,
  0x4e9e, 0x4eab, 0x4eac, 0x4f6f, 0x4f9d, 0x4f8d, 0x4f73, 0x4f7f,
  0x4f6c, 0x4f9b, 0x4f8b, 0x4f86, 0x4f83, 0x4f70, 0x4f75, 0x4f88,
  0x4f69, 0x4f7b, 0x4f96, 0x4f7e, 0x4f8f, 0x4f91,
  /* 0x4c */
  0x4f7a, 0x5154, 0x5152, 0x5155, 0x5169, 0x5177, 0x5176, 0x5178,
  0x51bd, 0x51fd, 0x523b, 0x5238, 0x5237, 0x523a, 0x5230, 0x522e,
  0x5236, 0x5241, 0x52be, 0x52bb, 0x5352, 0x5354, 0x5353, 0x5351,
  0x5366, 0x5377, 0x5378, 0x5379, 0x53d6, 0x53d4, 0x53d7, 0x5473,
  0x5475, 0x5496, 0x5478, 0x5495, 0x5480, 0x547b, 0x5477, 0x5484,
  0x5492, 0x5486, 0x547c, 0x5490, 0x5471, 0x5476, 0x548c, 0x549a,
  0x5462, 0x5468, 0x548b, 0x547d, 0x548e, 0x56fa, 0x5783, 0x5777,
  0x576a, 0x5769, 0x5761, 0x5766, 0x5764, 0x577c, 0x591c, 0x5949,
  0x5947, 0x5948, 0x5944, 0x5954, 0x59be, 0x59bb, 0x59d4, 0x59b9,
  0x59ae, 0x59d1, 0x59c6, 0x59d0, 0x59cd, 0x59cb, 0x59d3, 0x59ca,
  0x59af, 0x59b3, 0x59d2, 0x59c5, 0x5b5f, 0x5b64, 0x5b63, 0x5b97,
  0x5b9a, 0x5b98, 0x5b9c, 0x5b99, 0x5b9b, 0x5c1a,
  /* 0x4d */
  0x5c48, 0x5c45, 0x5c46, 0x5cb7, 0x5ca1, 0x5cb8, 0x5ca9, 0x5cab,
  0x5cb1, 0x5cb3, 0x5e18, 0x5e1a, 0x5e16, 0x5e15, 0x5e1b, 0x5e11,
  0x5e78, 0x5e9a, 0x5e97, 0x5e9c, 0x5e95, 0x5e96, 0x5ef6, 0x5f26,
  0x5f27, 0x5f29, 0x5f80, 0x5f81, 0x5f7f, 0x5f7c, 0x5fdd, 0x5fe0,
  0x5ffd, 0x5ff5, 0x5fff, 0x600f, 0x6014, 0x602f, 0x6035, 0x6016,
  0x602a, 0x6015, 0x6021, 0x6027, 0x6029, 0x602b, 0x601b, 0x6216,
  0x6215, 0x623f, 0x623e, 0x6240, 0x627f, 0x62c9, 0x62cc, 0x62c4,
  0x62bf, 0x62c2, 0x62b9, 0x62d2, 0x62db, 0x62ab, 0x62d3, 0x62d4,
  0x62cb, 0x62c8, 0x62a8, 0x62bd, 0x62bc, 0x62d0, 0x62d9, 0x62c7,
  0x62cd, 0x62b5, 0x62da, 0x62b1, 0x62d8, 0x62d6, 0x62d7, 0x62c6,
  0x62ac, 0x62ce, 0x653e, 0x65a7, 0x65bc, 0x65fa, 0x6614, 0x6613,
  0x660c, 0x6606, 0x6602, 0x660e, 0x6600, 0x660f,
  /* 0x4e */
  0x6615, 0x660a, 0x6607, 0x670d, 0x670b, 0x676d, 0x678b, 0x6795,
  0x6771, 0x679c, 0x6773, 0x6777, 0x6787, 0x679d, 0x6797, 0x676f,
  0x6770, 0x677f, 0x6789, 0x677e, 0x6790, 0x6775, 0x679a, 0x6793,
  0x677c, 0x676a, 0x6772, 0x6b23, 0x6b66, 0x6b67, 0x6b7f, 0x6c13,
  0x6c1b, 0x6ce3, 0x6ce8, 0x6cf3, 0x6cb1, 0x6ccc, 0x6ce5, 0x6cb3,
  0x6cbd, 0x6cbe, 0x6cbc, 0x6ce2, 0x6cab, 0x6cd5, 0x6cd3, 0x6cb8,
  0x6cc4, 0x6cb9, 0x6cc1, 0x6cae, 0x6cd7, 0x6cc5, 0x6cf1, 0x6cbf,
  0x6cbb, 0x6ce1, 0x6cdb, 0x6cca, 0x6cac, 0x6cef, 0x6cdc, 0x6cd6,
  0x6ce0, 0x7095, 0x708e, 0x7092, 0x708a, 0x7099, 0x722c, 0x722d,
  0x7238, 0x7248, 0x7267, 0x7269, 0x72c0, 0x72ce, 0x72d9, 0x72d7,
  0x72d0, 0x73a9, 0x73a8, 0x739f, 0x73ab, 0x73a5, 0x753d, 0x759d,
  0x7599, 0x759a, 0x7684, 0x76c2, 0x76f2, 0x76f4,
  /* 0x4f */
  0x77e5, 0x77fd, 0x793e, 0x7940, 0x7941, 0x79c9, 0x79c8, 0x7a7a,
  0x7a79, 0x7afa, 0x7cfe, 0x7f54, 0x7f8c, 0x7f8b, 0x8005, 0x80ba,
  0x80a5, 0x80a2, 0x80b1, 0x80a1, 0x80ab, 0x80a9, 0x80b4, 0x80aa,
  0x80af, 0x81e5, 0x81fe, 0x820d, 0x82b3, 0x829d, 0x8299, 0x82ad,
  0x82bd, 0x829f, 0x82b9, 0x82b1, 0x82ac, 0x82a5, 0x82af, 0x82b8,
  0x82a3, 0x82b0, 0x82be, 0x82b7, 0x864e, 0x8671, 0x521d, 0x8868,
  0x8ecb, 0x8fce, 0x8fd4, 0x8fd1, 0x90b5, 0x90b8, 0x90b1, 0x90b6,
  0x91c7, 0x91d1, 0x9577, 0x9580, 0x961c, 0x9640, 0x963f, 0x963b,
  0x9644, 0x9642, 0x96b9, 0x96e8, 0x9752, 0x975e, 0x4e9f, 0x4ead,
  0x4eae, 0x4fe1, 0x4fb5, 0x4faf, 0x4fbf, 0x4fe0, 0x4fd1, 0x4fcf,
  0x4fdd, 0x4fc3, 0x4fb6, 0x4fd8, 0x4fdf, 0x4fca, 0x4fd7, 0x4fae,
  0x4fd0, 0x4fc4, 0x4fc2, 0x4fda, 0x4fce, 0x4fde,
  /* 0x50 */
  0x4fb7, 0x5157, 0x5192, 0x5191, 0x51a0, 0x524e, 0x5243, 0x524a,
  0x524d, 0x524c, 0x524b, 0x5247, 0x52c7, 0x52c9, 0x52c3, 0x52c1,
  0x530d, 0x5357, 0x537b, 0x539a, 0x53db, 0x54ac, 0x54c0, 0x54a8,
  0x54ce, 0x54c9, 0x54b8, 0x54a6, 0x54b3, 0x54c7, 0x54c2, 0x54bd,
  0x54aa, 0x54c1, 0x54c4, 0x54c8, 0x54af, 0x54ab, 0x54b1, 0x54bb,
  0x54a9, 0x54a7, 0x54bf, 0x56ff, 0x5782, 0x578b, 0x57a0, 0x57a3,
  0x57a2, 0x57ce, 0x57ae, 0x5793, 0x5955, 0x5951, 0x594f, 0x594e,
  0x5950, 0x59dc, 0x59d8, 0x59ff, 0x59e3, 0x59e8, 0x5a03, 0x59e5,
  0x59ea, 0x59da, 0x59e6, 0x5a01, 0x59fb, 0x5b69, 0x5ba3, 0x5ba6,
  0x5ba4, 0x5ba2, 0x5ba5, 0x5c01, 0x5c4e, 0x5c4f, 0x5c4d, 0x5c4b,
  0x5cd9, 0x5cd2, 0x5df7, 0x5e1d, 0x5e25, 0x5e1f, 0x5e7d, 0x5ea0,
  0x5ea6, 0x5efa, 0x5f08, 0x5f2d, 0x5f65, 0x5f88,
  /* 0x51 */
  0x5f85, 0x5f8a, 0x5f8b, 0x5f87, 0x5f8c, 0x5f89, 0x6012, 0x601d,
  0x6020, 0x6025, 0x600e, 0x6028, 0x604d, 0x6070, 0x6068, 0x6062,
  0x6046, 0x6043, 0x606c, 0x606b, 0x606a, 0x6064, 0x6241, 0x62dc,
  0x6316, 0x6309, 0x62fc, 0x62ed, 0x6301, 0x62ee, 0x62fd, 0x6307,
  0x62f1, 0x62f7, 0x62ef, 0x62ec, 0x62fe, 0x62f4, 0x6311, 0x6302,
  0x653f, 0x6545, 0x65ab, 0x65bd, 0x65e2, 0x6625, 0x662d, 0x6620,
  0x6627, 0x662f, 0x661f, 0x6628, 0x6631, 0x6624, 0x66f7, 0x67ff,
  0x67d3, 0x67f1, 0x67d4, 0x67d0, 0x67ec, 0x67b6, 0x67af, 0x67f5,
  0x67e9, 0x67ef, 0x67c4, 0x67d1, 0x67b4, 0x67da, 0x67e5, 0x67b8,
  0x67cf, 0x67de, 0x67f3, 0x67b0, 0x67d9, 0x67e2, 0x67dd, 0x67d2,
  0x6b6a, 0x6b83, 0x6b86, 0x6bb5, 0x6bd2, 0x6bd7, 0x6c1f, 0x6cc9,
  0x6d0b, 0x6d32, 0x6d2a, 0x6d41, 0x6d25, 0x6d0c,
  /* 0x52 */
  0x6d31, 0x6d1e, 0x6d17, 0x6d3b, 0x6d3d, 0x6d3e, 0x6d36, 0x6d1b,
  0x6cf5, 0x6d39, 0x6d27, 0x6d38, 0x6d29, 0x6d2e, 0x6d35, 0x6d0e,
  0x6d2b, 0x70ab, 0x70ba, 0x70b3, 0x70ac, 0x70af, 0x70ad, 0x70b8,
  0x70ae, 0x70a4, 0x7230, 0x7272, 0x726f, 0x7274, 0x72e9, 0x72e0,
  0x72e1, 0x73b7, 0x73ca, 0x73bb, 0x73b2, 0x73cd, 0x73c0, 0x73b3,
  0x751a, 0x752d, 0x754f, 0x754c, 0x754e, 0x754b, 0x75ab, 0x75a4,
  0x75a5, 0x75a2, 0x75a3, 0x7678, 0x7686, 0x7687, 0x7688, 0x76c8,
  0x76c6, 0x76c3, 0x76c5, 0x7701, 0x76f9, 0x76f8, 0x7709, 0x770b,
  0x76fe, 0x76fc, 0x7707, 0x77dc, 0x7802, 0x7814, 0x780c, 0x780d,
  0x7946, 0x7949, 0x7948, 0x7947, 0x79b9, 0x79ba, 0x79d1, 0x79d2,
  0x79cb, 0x7a7f, 0x7a81, 0x7aff, 0x7afd, 0x7c7d, 0x7d02, 0x7d05,
  0x7d00, 0x7d09, 0x7d07, 0x7d04, 0x7d06, 0x7f38,
  /* 0x53 */
  0x7f8e, 0x7fbf, 0x8010, 0x800d, 0x8011, 0x8036, 0x80d6, 0x80e5,
  0x80da, 0x80c3, 0x80c4, 0x80cc, 0x80e1, 0x80db, 0x80ce, 0x80de,
  0x80e4, 0x80dd, 0x81f4, 0x8222, 0x82e7, 0x8303, 0x8305, 0x82e3,
  0x82db, 0x82e6, 0x8304, 0x82e5, 0x8302, 0x8309, 0x82d2, 0x82d7,
  0x82f1, 0x8301, 0x82dc, 0x82d4, 0x82d1, 0x82de, 0x82d3, 0x82df,
  0x82ef, 0x8306, 0x8650, 0x8679, 0x867b, 0x867a, 0x884d, 0x886b,
  0x8981, 0x89d4, 0x8a08, 0x8a02, 0x8a03, 0x8c9e, 0x8ca0, 0x8d74,
  0x8d73, 0x8db4, 0x8ecd, 0x8ecc, 0x8ff0, 0x8fe6, 0x8fe2, 0x8fea,
  0x8fe5, 0x8fed, 0x8feb, 0x8fe4, 0x8fe8, 0x90ca, 0x90ce, 0x90c1,
  0x90c3, 0x914b, 0x914a, 0x91cd, 0x9582, 0x9650, 0x964b, 0x964c,
  0x964d, 0x9762, 0x9769, 0x97cb, 0x97ed, 0x97f3, 0x9801, 0x98a8,
  0x98db, 0x98df, 0x9996, 0x9999, 0x4e58, 0x4eb3,
  /* 0x54 */
  0x500c, 0x500d, 0x5023, 0x4fef, 0x5026, 0x5025, 0x4ff8, 0x5029,
  0x5016, 0x5006, 0x503c, 0x501f, 0x501a, 0x5012, 0x5011, 0x4ffa,
  0x5000, 0x5014, 0x5028, 0x4ff1, 0x5021, 0x500b, 0x5019, 0x5018,
  0x4ff3, 0x4fee, 0x502d, 0x502a, 0x4ffe, 0x502b, 0x5009, 0x517c,
  0x51a4, 0x51a5, 0x51a2, 0x51cd, 0x51cc, 0x51c6, 0x51cb, 0x5256,
  0x525c, 0x5254, 0x525b, 0x525d, 0x532a, 0x537f, 0x539f, 0x539d,
  0x53df, 0x54e8, 0x5510, 0x5501, 0x5537, 0x54fc, 0x54e5, 0x54f2,
  0x5506, 0x54fa, 0x5514, 0x54e9, 0x54ed, 0x54e1, 0x5509, 0x54ee,
  0x54ea, 0x54e6, 0x5527, 0x5507, 0x54fd, 0x550f, 0x5703, 0x5704,
  0x57c2, 0x57d4, 0x57cb, 0x57c3, 0x5809, 0x590f, 0x5957, 0x5958,
  0x595a, 0x5a11, 0x5a18, 0x5a1c, 0x5a1f, 0x5a1b, 0x5a13, 0x59ec,
  0x5a20, 0x5a23, 0x5a29, 0x5a25, 0x5a0c, 0x5a09,
  /* 0x55 */
  0x5b6b, 0x5c58, 0x5bb0, 0x5bb3, 0x5bb6, 0x5bb4, 0x5bae, 0x5bb5,
  0x5bb9, 0x5bb8, 0x5c04, 0x5c51, 0x5c55, 0x5c50, 0x5ced, 0x5cfd,
  0x5cfb, 0x5cea, 0x5ce8, 0x5cf0, 0x5cf6, 0x5d01, 0x5cf4, 0x5dee,
  0x5e2d, 0x5e2b, 0x5eab, 0x5ead, 0x5ea7, 0x5f31, 0x5f92, 0x5f91,
  0x5f90, 0x6059, 0x6063, 0x6065, 0x6050, 0x6055, 0x606d, 0x6069,
  0x606f, 0x6084, 0x609f, 0x609a, 0x608d, 0x6094, 0x608c, 0x6085,
  0x6096, 0x6247, 0x62f3, 0x6308, 0x62ff, 0x634e, 0x633e, 0x632f,
  0x6355, 0x6342, 0x6346, 0x634f, 0x6349, 0x633a, 0x6350, 0x633d,
  0x632a, 0x632b, 0x6328, 0x634d, 0x634c, 0x6548, 0x6549, 0x6599,
  0x65c1, 0x65c5, 0x6642, 0x6649, 0x664f, 0x6643, 0x6652, 0x664c,
  0x6645, 0x6641, 0x66f8, 0x6714, 0x6715, 0x6717, 0x6821, 0x6838,
  0x6848, 0x6846, 0x6853, 0x6839, 0x6842, 0x6854,
  /* 0x56 */
  0x6829, 0x68b3, 0x6817, 0x684c, 0x6851, 0x683d, 0x67f4, 0x6850,
  0x6840, 0x683c, 0x6843, 0x682a, 0x6845, 0x6813, 0x6818, 0x6841,
  0x6b8a, 0x6b89, 0x6bb7, 0x6c23, 0x6c27, 0x6c28, 0x6c26, 0x6c24,
  0x6cf0, 0x6d6a, 0x6d95, 0x6d88, 0x6d87, 0x6d66, 0x6d78, 0x6d77,
  0x6d59, 0x6d93, 0x6d6c, 0x6d89, 0x6d6e, 0x6d5a, 0x6d74, 0x6d69,
  0x6d8c, 0x6d8a, 0x6d79, 0x6d85, 0x6d65, 0x6d94, 0x70ca, 0x70d8,
  0x70e4, 0x70d9, 0x70c8, 0x70cf, 0x7239, 0x7279, 0x72fc, 0x72f9,
  0x72fd, 0x72f8, 0x72f7, 0x7386, 0x73ed, 0x7409, 0x73ee, 0x73e0,
  0x73ea, 0x73de, 0x7554, 0x755d, 0x755c, 0x755a, 0x7559, 0x75be,
  0x75c5, 0x75c7, 0x75b2, 0x75b3, 0x75bd, 0x75bc, 0x75b9, 0x75c2,
  0x75b8, 0x768b, 0x76b0, 0x76ca, 0x76cd, 0x76ce, 0x7729, 0x771f,
  0x7720, 0x7728, 0x77e9, 0x7830, 0x7827, 0x7838,
  /* 0x57 */
  0x781d, 0x7834, 0x7837, 0x7825, 0x782d, 0x7820, 0x781f, 0x7832,
  0x7955, 0x7950, 0x7960, 0x795f, 0x7956, 0x795e, 0x795d, 0x7957,
  0x795a, 0x79e4, 0x79e3, 0x79e7, 0x79df, 0x79e6, 0x79e9, 0x79d8,
  0x7a84, 0x7a88, 0x7ad9, 0x7b06, 0x7b11, 0x7c89, 0x7d21, 0x7d17,
  0x7d0b, 0x7d0a, 0x7d20, 0x7d22, 0x7d14, 0x7d10, 0x7d15, 0x7d1a,
  0x7d1c, 0x7d0d, 0x7d19, 0x7d1b, 0x7f3a, 0x7f5f, 0x7f94, 0x7fc5,
  0x7fc1, 0x8006, 0x8004, 0x8018, 0x8015, 0x8019, 0x8017, 0x803d,
  0x803f, 0x80f1, 0x8102, 0x80f0, 0x8105, 0x80ed, 0x80f4, 0x8106,
  0x80f8, 0x80f3, 0x8108, 0x80fd, 0x810a, 0x80fc, 0x80ef, 0x81ed,
  0x81ec, 0x8200, 0x8210, 0x822a, 0x822b, 0x8228, 0x822c, 0x82bb,
  0x832b, 0x8352, 0x8354, 0x834a, 0x8338, 0x8350, 0x8349, 0x8335,
  0x8334, 0x834f, 0x8332, 0x8339, 0x8336, 0x8317,
  /* 0x58 */
  0x8340, 0x8331, 0x8328, 0x8343, 0x8654, 0x868a, 0x86aa, 0x8693,
  0x86a4, 0x86a9, 0x868c, 0x86a3, 0x869c, 0x8870, 0x8877, 0x8881,
  0x8882, 0x887d, 0x8879, 0x8a18, 0x8a10, 0x8a0e, 0x8a0c, 0x8a15,
  0x8a0a, 0x8a17, 0x8a13, 0x8a16, 0x8a0f, 0x8a11, 0x8c48, 0x8c7a,
  0x8c79, 0x8ca1, 0x8ca2, 0x8d77, 0x8eac, 0x8ed2, 0x8ed4, 0x8ecf,
  0x8fb1, 0x9001, 0x9006, 0x8ff7, 0x9000, 0x8ffa, 0x8ff4, 0x9003,
  0x8ffd, 0x9005, 0x8ff8, 0x9095, 0x90e1, 0x90dd, 0x90e2, 0x9152,
  0x914d, 0x914c, 0x91d8, 0x91dd, 0x91d7, 0x91dc, 0x91d9, 0x9583,
  0x9662, 0x9663, 0x9661, 0x965b, 0x965d, 0x9664, 0x9658, 0x965e,
  0x96bb, 0x98e2, 0x99ac, 0x9aa8, 0x9ad8, 0x9b25, 0x9b32, 0x9b3c,
  0x4e7e, 0x507a, 0x507d, 0x505c, 0x5047, 0x5043, 0x504c, 0x505a,
  0x5049, 0x5065, 0x5076, 0x504e, 0x5055, 0x5075,
  /* 0x59 */
  0x5074, 0x5077, 0x504f, 0x500f, 0x506f, 0x506d, 0x515c, 0x5195,
  0x51f0, 0x526a, 0x526f, 0x52d2, 0x52d9, 0x52d8, 0x52d5, 0x5310,
  0x530f, 0x5319, 0x533f, 0x5340, 0x533e, 0x53c3, 0x66fc, 0x5546,
  0x556a, 0x5566, 0x5544, 0x555e, 0x5561, 0x5543, 0x554a, 0x5531,
  0x5556, 0x554f, 0x5555, 0x552f, 0x5564, 0x5538, 0x552e, 0x555c,
  0x552c, 0x5563, 0x5533, 0x5541, 0x5557, 0x5708, 0x570b, 0x5709,
  0x57df, 0x5805, 0x580a, 0x5806, 0x57e0, 0x57e4, 0x57fa, 0x5802,
  0x5835, 0x57f7, 0x57f9, 0x5920, 0x5962, 0x5a36, 0x5a41, 0x5a49,
  0x5a66, 0x5a6a, 0x5a40, 0x5a3c, 0x5a62, 0x5a5a, 0x5a46, 0x5a4a,
  0x5b70, 0x5bc7, 0x5bc5, 0x5bc4, 0x5bc2, 0x5bbf, 0x5bc6, 0x5c09,
  0x5c08, 0x5c07, 0x5c60, 0x5c5c, 0x5c5d, 0x5d07, 0x5d06, 0x5d0e,
  0x5d1b, 0x5d16, 0x5d22, 0x5d11, 0x5d29, 0x5d14,
  /* 0x5a */
  0x5d19, 0x5d24, 0x5d27, 0x5d17, 0x5de2, 0x5e38, 0x5e36, 0x5e33,
  0x5e37, 0x5eb7, 0x5eb8, 0x5eb6, 0x5eb5, 0x5ebe, 0x5f35, 0x5f37,
  0x5f57, 0x5f6c, 0x5f69, 0x5f6b, 0x5f97, 0x5f99, 0x5f9e, 0x5f98,
  0x5fa1, 0x5fa0, 0x5f9c, 0x607f, 0x60a3, 0x6089, 0x60a0, 0x60a8,
  0x60cb, 0x60b4, 0x60e6, 0x60bd, 0x60c5, 0x60bb, 0x60b5, 0x60dc,
  0x60bc, 0x60d8, 0x60d5, 0x60c6, 0x60df, 0x60b8, 0x60da, 0x60c7,
  0x621a, 0x621b, 0x6248, 0x63a0, 0x63a7, 0x6372, 0x6396, 0x63a2,
  0x63a5, 0x6377, 0x6367, 0x6398, 0x63aa, 0x6371, 0x63a9, 0x6389,
  0x6383, 0x639b, 0x636b, 0x63a8, 0x6384, 0x6388, 0x6399, 0x63a1,
  0x63ac, 0x6392, 0x638f, 0x6380, 0x637b, 0x6369, 0x6368, 0x637a,
  0x655d, 0x6556, 0x6551, 0x6559, 0x6557, 0x555f, 0x654f, 0x6558,
  0x6555, 0x6554, 0x659c, 0x659b, 0x65ac, 0x65cf,
  /* 0x5b */
  0x65cb, 0x65cc, 0x65ce, 0x665d, 0x665a, 0x6664, 0x6668, 0x6666,
  0x665e, 0x66f9, 0x52d7, 0x671b, 0x6881, 0x68af, 0x68a2, 0x6893,
  0x68b5, 0x687f, 0x6876, 0x68b1, 0x68a7, 0x6897, 0x68b0, 0x6883,
  0x68c4, 0x68ad, 0x6886, 0x6885, 0x6894, 0x689d, 0x68a8, 0x689f,
  0x68a1, 0x6882, 0x6b32, 0x6bba, 0x6beb, 0x6bec, 0x6c2b, 0x6d8e,
  0x6dbc, 0x6df3, 0x6dd9, 0x6db2, 0x6de1, 0x6dcc, 0x6de4, 0x6dfb,
  0x6dfa, 0x6e05, 0x6dc7, 0x6dcb, 0x6daf, 0x6dd1, 0x6dae, 0x6dde,
  0x6df9, 0x6db8, 0x6df7, 0x6df5, 0x6dc5, 0x6dd2, 0x6e1a, 0x6db5,
  0x6dda, 0x6deb, 0x6dd8, 0x6dea, 0x6df1, 0x6dee, 0x6de8, 0x6dc6,
  0x6dc4, 0x6daa, 0x6dec, 0x6dbf, 0x6de6, 0x70f9, 0x7109, 0x710a,
  0x70fd, 0x70ef, 0x723d, 0x727d, 0x7281, 0x731c, 0x731b, 0x7316,
  0x7313, 0x7319, 0x7387, 0x7405, 0x740a, 0x7403,
  /* 0x5c */
  0x7406, 0x73fe, 0x740d, 0x74e0, 0x74f6, 0x74f7, 0x751c, 0x7522,
  0x7565, 0x7566, 0x7562, 0x7570, 0x758f, 0x75d4, 0x75d5, 0x75b5,
  0x75ca, 0x75cd, 0x768e, 0x76d4, 0x76d2, 0x76db, 0x7737, 0x773e,
  0x773c, 0x7736, 0x7738, 0x773a, 0x786b, 0x7843, 0x784e, 0x7965,
  0x7968, 0x796d, 0x79fb, 0x7a92, 0x7a95, 0x7b20, 0x7b28, 0x7b1b,
  0x7b2c, 0x7b26, 0x7b19, 0x7b1e, 0x7b2e, 0x7c92, 0x7c97, 0x7c95,
  0x7d46, 0x7d43, 0x7d71, 0x7d2e, 0x7d39, 0x7d3c, 0x7d40, 0x7d30,
  0x7d33, 0x7d44, 0x7d2f, 0x7d42, 0x7d32, 0x7d31, 0x7f3d, 0x7f9e,
  0x7f9a, 0x7fcc, 0x7fce, 0x7fd2, 0x801c, 0x804a, 0x8046, 0x812f,
  0x8116, 0x8123, 0x812b, 0x8129, 0x8130, 0x8124, 0x8202, 0x8235,
  0x8237, 0x8236, 0x8239, 0x838e, 0x839e, 0x8398, 0x8378, 0x83a2,
  0x8396, 0x83bd, 0x83ab, 0x8392, 0x838a, 0x8393,
  /* 0x5d */
  0x8389, 0x83a0, 0x8377, 0x837b, 0x837c, 0x8386, 0x83a7, 0x8655,
  0x5f6a, 0x86c7, 0x86c0, 0x86b6, 0x86c4, 0x86b5, 0x86c6, 0x86cb,
  0x86b1, 0x86af, 0x86c9, 0x8853, 0x889e, 0x8888, 0x88ab, 0x8892,
  0x8896, 0x888d, 0x888b, 0x8993, 0x898f, 0x8a2a, 0x8a1d, 0x8a23,
  0x8a25, 0x8a31, 0x8a2d, 0x8a1f, 0x8a1b, 0x8a22, 0x8c49, 0x8c5a,
  0x8ca9, 0x8cac, 0x8cab, 0x8ca8, 0x8caa, 0x8ca7, 0x8d67, 0x8d66,
  0x8dbe, 0x8dba, 0x8edb, 0x8edf, 0x9019, 0x900d, 0x901a, 0x9017,
  0x9023, 0x901f, 0x901d, 0x9010, 0x9015, 0x901e, 0x9020, 0x900f,
  0x9022, 0x9016, 0x901b, 0x9014, 0x90e8, 0x90ed, 0x90fd, 0x9157,
  0x91ce, 0x91f5, 0x91e6, 0x91e3, 0x91e7, 0x91ed, 0x91e9, 0x9589,
  0x966a, 0x9675, 0x9673, 0x9678, 0x9670, 0x9674, 0x9676, 0x9677,
  0x966c, 0x96c0, 0x96ea, 0x96e9, 0x7ae0, 0x7adf,
  /* 0x5e */
  0x9802, 0x9803, 0x9b5a, 0x9ce5, 0x9e75, 0x9e7f, 0x9ea5, 0x9ebb,
  0x50a2, 0x508d, 0x5085, 0x5099, 0x5091, 0x5080, 0x5096, 0x5098,
  0x509a, 0x6700, 0x51f1, 0x5272, 0x5274, 0x5275, 0x5269, 0x52de,
  0x52dd, 0x52db, 0x535a, 0x53a5, 0x557b, 0x5580, 0x55a7, 0x557c,
  0x558a, 0x559d, 0x5598, 0x5582, 0x559c, 0x55aa, 0x5594, 0x5587,
  0x558b, 0x5583, 0x55b3, 0x55ae, 0x559f, 0x553e, 0x55b2, 0x559a,
  0x55bb, 0x55ac, 0x55b1, 0x557e, 0x5589, 0x55ab, 0x5599, 0x570d,
  0x582f, 0x582a, 0x5834, 0x5824, 0x5830, 0x5831, 0x5821, 0x581d,
  0x5820, 0x58f9, 0x58fa, 0x5960, 0x5a77, 0x5a9a, 0x5a7f, 0x5a92,
  0x5a9b, 0x5aa7, 0x5b73, 0x5b71, 0x5bd2, 0x5bcc, 0x5bd3, 0x5bd0,
  0x5c0a, 0x5c0b, 0x5c31, 0x5d4c, 0x5d50, 0x5d34, 0x5d47, 0x5dfd,
  0x5e45, 0x5e3d, 0x5e40, 0x5e43, 0x5e7e, 0x5eca,
  /* 0x5f */
  0x5ec1, 0x5ec2, 0x5ec4, 0x5f3c, 0x5f6d, 0x5fa9, 0x5faa, 0x5fa8,
  0x60d1, 0x60e1, 0x60b2, 0x60b6, 0x60e0, 0x611c, 0x6123, 0x60fa,
  0x6115, 0x60f0, 0x60fb, 0x60f4, 0x6168, 0x60f1, 0x610e, 0x60f6,
  0x6109, 0x6100, 0x6112, 0x621f, 0x6249, 0x63a3, 0x638c, 0x63cf,
  0x63c0, 0x63e9, 0x63c9, 0x63c6, 0x63cd, 0x63d2, 0x63e3, 0x63d0,
  0x63e1, 0x63d6, 0x63ed, 0x63ee, 0x6376, 0x63f4, 0x63ea, 0x63db,
  0x6452, 0x63da, 0x63f9, 0x655e, 0x6566, 0x6562, 0x6563, 0x6591,
  0x6590, 0x65af, 0x666e, 0x6670, 0x6674, 0x6676, 0x666f, 0x6691,
  0x667a, 0x667e, 0x6677, 0x66fe, 0x66ff, 0x671f, 0x671d, 0x68fa,
  0x68d5, 0x68e0, 0x68d8, 0x68d7, 0x6905, 0x68df, 0x68f5, 0x68ee,
  0x68e7, 0x68f9, 0x68d2, 0x68f2, 0x68e3, 0x68cb, 0x68cd, 0x690d,
  0x6912, 0x690e, 0x68c9, 0x68da, 0x696e, 0x68fb,
  /* 0x60 */
  0x6b3e, 0x6b3a, 0x6b3d, 0x6b98, 0x6b96, 0x6bbc, 0x6bef, 0x6c2e,
  0x6c2f, 0x6c2c, 0x6e2f, 0x6e38, 0x6e54, 0x6e21, 0x6e32, 0x6e67,
  0x6e4a, 0x6e20, 0x6e25, 0x6e23, 0x6e1b, 0x6e5b, 0x6e58, 0x6e24,
  0x6e56, 0x6e6e, 0x6e2d, 0x6e26, 0x6e6f, 0x6e34, 0x6e4d, 0x6e3a,
  0x6e2c, 0x6e43, 0x6e1d, 0x6e3e, 0x6ecb, 0x6e89, 0x6e19, 0x6e4e,
  0x6e63, 0x6e44, 0x6e72, 0x6e69, 0x6e5f, 0x7119, 0x711a, 0x7126,
  0x7130, 0x7121, 0x7136, 0x716e, 0x711c, 0x724c, 0x7284, 0x7280,
  0x7336, 0x7325, 0x7334, 0x7329, 0x743a, 0x742a, 0x7433, 0x7422,
  0x7425, 0x7435, 0x7436, 0x7434, 0x742f, 0x741b, 0x7426, 0x7428,
  0x7525, 0x7526, 0x756b, 0x756a, 0x75e2, 0x75db, 0x75e3, 0x75d9,
  0x75d8, 0x75de, 0x75e0, 0x767b, 0x767c, 0x7696, 0x7693, 0x76b4,
  0x76dc, 0x774f, 0x77ed, 0x785d, 0x786c, 0x786f,
  /* 0x61 */
  0x7a0d, 0x7a08, 0x7a0b, 0x7a05, 0x7a00, 0x7a98, 0x7a97, 0x7a96,
  0x7ae5, 0x7ae3, 0x7b49, 0x7b56, 0x7b46, 0x7b50, 0x7b52, 0x7b54,
  0x7b4d, 0x7b4b, 0x7b4f, 0x7b51, 0x7c9f, 0x7ca5, 0x7d5e, 0x7d50,
  0x7d68, 0x7d55, 0x7d2b, 0x7d6e, 0x7d72, 0x7d61, 0x7d66, 0x7d62,
  0x7d70, 0x7d73, 0x5584, 0x7fd4, 0x7fd5, 0x800b, 0x8052, 0x8085,
  0x8155, 0x8154, 0x814b, 0x8151, 0x814e, 0x8139, 0x8146, 0x813e,
  0x814c, 0x8153, 0x8174, 0x8212, 0x821c, 0x83e9, 0x8403, 0x83f8,
  0x840d, 0x83e0, 0x83c5, 0x840b, 0x83c1, 0x83ef, 0x83f1, 0x83f4,
  0x8457, 0x840a, 0x83f0, 0x840c, 0x83cc, 0x83fd, 0x83f2, 0x83ca,
  0x8438, 0x840e, 0x8404, 0x83dc, 0x8407, 0x83d4, 0x83df, 0x865b,
  0x86df, 0x86d9, 0x86ed, 0x86d4, 0x86db, 0x86e4, 0x86d0, 0x86de,
  0x8857, 0x88c1, 0x88c2, 0x88b1, 0x8983, 0x8996,
  /* 0x62 */
  0x8a3b, 0x8a60, 0x8a55, 0x8a5e, 0x8a3c, 0x8a41, 0x8a54, 0x8a5b,
  0x8a50, 0x8a46, 0x8a34, 0x8a3a, 0x8a36, 0x8a56, 0x8c61, 0x8c82,
  0x8caf, 0x8cbc, 0x8cb3, 0x8cbd, 0x8cc1, 0x8cbb, 0x8cc0, 0x8cb4,
  0x8cb7, 0x8cb6, 0x8cbf, 0x8cb8, 0x8d8a, 0x8d85, 0x8d81, 0x8dce,
  0x8ddd, 0x8dcb, 0x8dda, 0x8dd1, 0x8dcc, 0x8ddb, 0x8dc6, 0x8efb,
  0x8ef8, 0x8efc, 0x8f9c, 0x902e, 0x9035, 0x9031, 0x9038, 0x9032,
  0x9036, 0x9102, 0x90f5, 0x9109, 0x90fe, 0x9163, 0x9165, 0x91cf,
  0x9214, 0x9215, 0x9223, 0x9209, 0x921e, 0x920d, 0x9210, 0x9207,
  0x9211, 0x9594, 0x958f, 0x958b, 0x9591, 0x9593, 0x9592, 0x958e,
  0x968a, 0x968e, 0x968b, 0x967d, 0x9685, 0x9686, 0x968d, 0x9672,
  0x9684, 0x96c1, 0x96c5, 0x96c4, 0x96c6, 0x96c7, 0x96ef, 0x96f2,
  0x97cc, 0x9805, 0x9806, 0x9808, 0x98e7, 0x98ea,
  /* 0x63 */
  0x98ef, 0x98e9, 0x98f2, 0x98ed, 0x99ae, 0x99ad, 0x9ec3, 0x9ecd,
  0x9ed1, 0x4e82, 0x50ad, 0x50b5, 0x50b2, 0x50b3, 0x50c5, 0x50be,
  0x50ac, 0x50b7, 0x50bb, 0x50af, 0x50c7, 0x527f, 0x5277, 0x527d,
  0x52df, 0x52e6, 0x52e4, 0x52e2, 0x52e3, 0x532f, 0x55df, 0x55e8,
  0x55d3, 0x55e6, 0x55ce, 0x55dc, 0x55c7, 0x55d1, 0x55e3, 0x55e4,
  0x55ef, 0x55da, 0x55e1, 0x55c5, 0x55c6, 0x55e5, 0x55c9, 0x5712,
  0x5713, 0x585e, 0x5851, 0x5858, 0x5857, 0x585a, 0x5854, 0x586b,
  0x584c, 0x586d, 0x584a, 0x5862, 0x5852, 0x584b, 0x5967, 0x5ac1,
  0x5ac9, 0x5acc, 0x5abe, 0x5abd, 0x5abc, 0x5ab3, 0x5ac2, 0x5ab2,
  0x5d69, 0x5d6f, 0x5e4c, 0x5e79, 0x5ec9, 0x5ec8, 0x5f12, 0x5f59,
  0x5fac, 0x5fae, 0x611a, 0x610f, 0x6148, 0x611f, 0x60f3, 0x611b,
  0x60f9, 0x6101, 0x6108, 0x614e, 0x614c, 0x6144,
  /* 0x64 */
  0x614d, 0x613e, 0x6134, 0x6127, 0x610d, 0x6106, 0x6137, 0x6221,
  0x6222, 0x6413, 0x643e, 0x641e, 0x642a, 0x642d, 0x643d, 0x642c,
  0x640f, 0x641c, 0x6414, 0x640d, 0x6436, 0x6416, 0x6417, 0x6406,
  0x656c, 0x659f, 0x65b0, 0x6697, 0x6689, 0x6687, 0x6688, 0x6696,
  0x6684, 0x6698, 0x668d, 0x6703, 0x6994, 0x696d, 0x695a, 0x6977,
  0x6960, 0x6954, 0x6975, 0x6930, 0x6982, 0x694a, 0x6968, 0x696b,
  0x695e, 0x6953, 0x6979, 0x6986, 0x695d, 0x6963, 0x695b, 0x6b47,
  0x6b72, 0x6bc0, 0x6bbf, 0x6bd3, 0x6bfd, 0x6ea2, 0x6eaf, 0x6ed3,
  0x6eb6, 0x6ec2, 0x6e90, 0x6e9d, 0x6ec7, 0x6ec5, 0x6ea5, 0x6e98,
  0x6ebc, 0x6eba, 0x6eab, 0x6ed1, 0x6e96, 0x6e9c, 0x6ec4, 0x6ed4,
  0x6eaa, 0x6ea7, 0x6eb4, 0x714e, 0x7159, 0x7169, 0x7164, 0x7149,
  0x7167, 0x715c, 0x716c, 0x7166, 0x714c, 0x7165,
  /* 0x65 */
  0x715e, 0x7146, 0x7168, 0x7156, 0x723a, 0x7252, 0x7337, 0x7345,
  0x733f, 0x733e, 0x746f, 0x745a, 0x7455, 0x745f, 0x745e, 0x7441,
  0x743f, 0x7459, 0x745b, 0x745c, 0x7576, 0x7578, 0x7600, 0x75f0,
  0x7601, 0x75f2, 0x75f1, 0x75fa, 0x75ff, 0x75f4, 0x75f3, 0x76de,
  0x76df, 0x775b, 0x776b, 0x7766, 0x775e, 0x7763, 0x7779, 0x776a,
  0x776c, 0x775c, 0x7765, 0x7768, 0x7762, 0x77ee, 0x788e, 0x78b0,
  0x7897, 0x7898, 0x788c, 0x7889, 0x787c, 0x7891, 0x7893, 0x787f,
  0x797a, 0x797f, 0x7981, 0x842c, 0x79bd, 0x7a1c, 0x7a1a, 0x7a20,
  0x7a14, 0x7a1f, 0x7a1e, 0x7a9f, 0x7aa0, 0x7b77, 0x7bc0, 0x7b60,
  0x7b6e, 0x7b67, 0x7cb1, 0x7cb3, 0x7cb5, 0x7d93, 0x7d79, 0x7d91,
  0x7d81, 0x7d8f, 0x7d5b, 0x7f6e, 0x7f69, 0x7f6a, 0x7f72, 0x7fa9,
  0x7fa8, 0x7fa4, 0x8056, 0x8058, 0x8086, 0x8084,
  /* 0x66 */
  0x8171, 0x8170, 0x8178, 0x8165, 0x816e, 0x8173, 0x816b, 0x8179,
  0x817a, 0x8166, 0x8205, 0x8247, 0x8482, 0x8477, 0x843d, 0x8431,
  0x8475, 0x8466, 0x846b, 0x8449, 0x846c, 0x845b, 0x843c, 0x8435,
  0x8461, 0x8463, 0x8469, 0x846d, 0x8446, 0x865e, 0x865c, 0x865f,
  0x86f9, 0x8713, 0x8708, 0x8707, 0x8700, 0x86fe, 0x86fb, 0x8702,
  0x8703, 0x8706, 0x870a, 0x8859, 0x88df, 0x88d4, 0x88d9, 0x88dc,
  0x88d8, 0x88dd, 0x88e1, 0x88ca, 0x88d5, 0x88d2, 0x899c, 0x89e3,
  0x8a6b, 0x8a72, 0x8a73, 0x8a66, 0x8a69, 0x8a70, 0x8a87, 0x8a7c,
  0x8a63, 0x8aa0, 0x8a71, 0x8a85, 0x8a6d, 0x8a62, 0x8a6e, 0x8a6c,
  0x8a79, 0x8a7b, 0x8a3e, 0x8a68, 0x8c62, 0x8c8a, 0x8c89, 0x8cca,
  0x8cc7, 0x8cc8, 0x8cc4, 0x8cb2, 0x8cc3, 0x8cc2, 0x8cc5, 0x8de1,
  0x8ddf, 0x8de8, 0x8def, 0x8df3, 0x8dfa, 0x8dea,
  /* 0x67 */
  0x8de4, 0x8de6, 0x8eb2, 0x8f03, 0x8f09, 0x8efe, 0x8f0a, 0x8f9f,
  0x8fb2, 0x904b, 0x904a, 0x9053, 0x9042, 0x9054, 0x903c, 0x9055,
  0x9050, 0x9047, 0x904f, 0x904e, 0x904d, 0x9051, 0x903e, 0x9041,
  0x9112, 0x9117, 0x916c, 0x916a, 0x9169, 0x91c9, 0x9237, 0x9257,
  0x9238, 0x923d, 0x9240, 0x923e, 0x925b, 0x924b, 0x9264, 0x9251,
  0x9234, 0x9249, 0x924d, 0x9245, 0x9239, 0x923f, 0x925a, 0x9598,
  0x9698, 0x9694, 0x9695, 0x96cd, 0x96cb, 0x96c9, 0x96ca, 0x96f7,
  0x96fb, 0x96f9, 0x96f6, 0x9756, 0x9774, 0x9776, 0x9810, 0x9811,
  0x9813, 0x980a, 0x9812, 0x980c, 0x98fc, 0x98f4, 0x98fd, 0x98fe,
  0x99b3, 0x99b1, 0x99b4, 0x9ae1, 0x9ce9, 0x9e82, 0x9f0e, 0x9f13,
  0x9f20, 0x50e7, 0x50ee, 0x50e5, 0x50d6, 0x50ed, 0x50da, 0x50d5,
  0x50cf, 0x50d1, 0x50f1, 0x50ce, 0x50e9, 0x5162,
  /* 0x68 */
  0x51f3, 0x5283, 0x5282, 0x5331, 0x53ad, 0x55fe, 0x5600, 0x561b,
  0x5617, 0x55fd, 0x5614, 0x5606, 0x5609, 0x560d, 0x560e, 0x55f7,
  0x5616, 0x561f, 0x5608, 0x5610, 0x55f6, 0x5718, 0x5716, 0x5875,
  0x587e, 0x5883, 0x5893, 0x588a, 0x5879, 0x5885, 0x587d, 0x58fd,
  0x5925, 0x5922, 0x5924, 0x596a, 0x5969, 0x5ae1, 0x5ae6, 0x5ae9,
  0x5ad7, 0x5ad6, 0x5ad8, 0x5ae3, 0x5b75, 0x5bde, 0x5be7, 0x5be1,
  0x5be5, 0x5be6, 0x5be8, 0x5be2, 0x5be4, 0x5bdf, 0x5c0d, 0x5c62,
  0x5d84, 0x5d87, 0x5e5b, 0x5e63, 0x5e55, 0x5e57, 0x5e54, 0x5ed3,
  0x5ed6, 0x5f0a, 0x5f46, 0x5f70, 0x5fb9, 0x6147, 0x613f, 0x614b,
  0x6177, 0x6162, 0x6163, 0x615f, 0x615a, 0x6158, 0x6175, 0x622a,
  0x6487, 0x6458, 0x6454, 0x64a4, 0x6478, 0x645f, 0x647a, 0x6451,
  0x6467, 0x6434, 0x646d, 0x647b, 0x6572, 0x65a1,
  /* 0x69 */
  0x65d7, 0x65d6, 0x66a2, 0x66a8, 0x669d, 0x699c, 0x69a8, 0x6995,
  0x69c1, 0x69ae, 0x69d3, 0x69cb, 0x699b, 0x69b7, 0x69bb, 0x69ab,
  0x69b4, 0x69d0, 0x69cd, 0x69ad, 0x69cc, 0x69a6, 0x69c3, 0x69a3,
  0x6b49, 0x6b4c, 0x6c33, 0x6f33, 0x6f14, 0x6efe, 0x6f13, 0x6ef4,
  0x6f29, 0x6f3e, 0x6f20, 0x6f2c, 0x6f0f, 0x6f02, 0x6f22, 0x6eff,
  0x6eef, 0x6f06, 0x6f31, 0x6f38, 0x6f32, 0x6f23, 0x6f15, 0x6f2b,
  0x6f2f, 0x6f88, 0x6f2a, 0x6eec, 0x6f01, 0x6ef2, 0x6ecc, 0x6ef7,
  0x7194, 0x7199, 0x717d, 0x718a, 0x7184, 0x7192, 0x723e, 0x7292,
  0x7296, 0x7344, 0x7350, 0x7464, 0x7463, 0x746a, 0x7470, 0x746d,
  0x7504, 0x7591, 0x7627, 0x760d, 0x760b, 0x7609, 0x7613, 0x76e1,
  0x76e3, 0x7784, 0x777d, 0x777f, 0x7761, 0x78c1, 0x789f, 0x78a7,
  0x78b3, 0x78a9, 0x78a3, 0x798e, 0x798f, 0x798d,
  /* 0x6a */
  0x7a2e, 0x7a31, 0x7aaa, 0x7aa9, 0x7aed, 0x7aef, 0x7ba1, 0x7b95,
  0x7b8b, 0x7b75, 0x7b97, 0x7b9d, 0x7b94, 0x7b8f, 0x7bb8, 0x7b87,
  0x7b84, 0x7cb9, 0x7cbd, 0x7cbe, 0x7dbb, 0x7db0, 0x7d9c, 0x7dbd,
  0x7dbe, 0x7da0, 0x7dca, 0x7db4, 0x7db2, 0x7db1, 0x7dba, 0x7da2,
  0x7dbf, 0x7db5, 0x7db8, 0x7dad, 0x7dd2, 0x7dc7, 0x7dac, 0x7f70,
  0x7fe0, 0x7fe1, 0x7fdf, 0x805e, 0x805a, 0x8087, 0x8150, 0x8180,
  0x818f, 0x8188, 0x818a, 0x817f, 0x8182, 0x81e7, 0x81fa, 0x8207,
  0x8214, 0x821e, 0x824b, 0x84c9, 0x84bf, 0x84c6, 0x84c4, 0x8499,
  0x849e, 0x84b2, 0x849c, 0x84cb, 0x84b8, 0x84c0, 0x84d3, 0x8490,
  0x84bc, 0x84d1, 0x84ca, 0x873f, 0x871c, 0x873b, 0x8722, 0x8725,
  0x8734, 0x8718, 0x8755, 0x8737, 0x8729, 0x88f3, 0x8902, 0x88f4,
  0x88f9, 0x88f8, 0x88fd, 0x88e8, 0x891a, 0x88ef,
  /* 0x6b */
  0x8aa6, 0x8a8c, 0x8a9e, 0x8aa3, 0x8a8d, 0x8aa1, 0x8a93, 0x8aa4,
  0x8aaa, 0x8aa5, 0x8aa8, 0x8a98, 0x8a91, 0x8a9a, 0x8aa7, 0x8c6a,
  0x8c8d, 0x8c8c, 0x8cd3, 0x8cd1, 0x8cd2, 0x8d6b, 0x8d99, 0x8d95,
  0x8dfc, 0x8f14, 0x8f12, 0x8f15, 0x8f13, 0x8fa3, 0x9060, 0x9058,
  0x905c, 0x9063, 0x9059, 0x905e, 0x9062, 0x905d, 0x905b, 0x9119,
  0x9118, 0x911e, 0x9175, 0x9178, 0x9177, 0x9174, 0x9278, 0x92ac,
  0x9280, 0x9285, 0x9298, 0x9296, 0x927b, 0x9293, 0x929c, 0x92a8,
  0x927c, 0x9291, 0x95a1, 0x95a8, 0x95a9, 0x95a3, 0x95a5, 0x95a4,
  0x9699, 0x969c, 0x969b, 0x96cc, 0x96d2, 0x9700, 0x977c, 0x9785,
  0x97f6, 0x9817, 0x9818, 0x98af, 0x98b1, 0x9903, 0x9905, 0x990c,
  0x9909, 0x99c1, 0x9aaf, 0x9ab0, 0x9ae6, 0x9b41, 0x9b42, 0x9cf4,
  0x9cf6, 0x9cf3, 0x9ebc, 0x9f3b, 0x9f4a, 0x5104,
  /* 0x6c */
  0x5100, 0x50fb, 0x50f5, 0x50f9, 0x5102, 0x5108, 0x5109, 0x5105,
  0x51dc, 0x5287, 0x5288, 0x5289, 0x528d, 0x528a, 0x52f0, 0x53b2,
  0x562e, 0x563b, 0x5639, 0x5632, 0x563f, 0x5634, 0x5629, 0x5653,
  0x564e, 0x5657, 0x5674, 0x5636, 0x562f, 0x5630, 0x5880, 0x589f,
  0x589e, 0x58b3, 0x589c, 0x58ae, 0x58a9, 0x58a6, 0x596d, 0x5b09,
  0x5afb, 0x5b0b, 0x5af5, 0x5b0c, 0x5b08, 0x5bee, 0x5bec, 0x5be9,
  0x5beb, 0x5c64, 0x5c65, 0x5d9d, 0x5d94, 0x5e62, 0x5e5f, 0x5e61,
  0x5ee2, 0x5eda, 0x5edf, 0x5edd, 0x5ee3, 0x5ee0, 0x5f48, 0x5f71,
  0x5fb7, 0x5fb5, 0x6176, 0x6167, 0x616e, 0x615d, 0x6155, 0x6182,
  0x617c, 0x6170, 0x616b, 0x617e, 0x61a7, 0x6190, 0x61ab, 0x618e,
  0x61ac, 0x619a, 0x61a4, 0x6194, 0x61ae, 0x622e, 0x6469, 0x646f,
  0x6479, 0x649e, 0x64b2, 0x6488, 0x6490, 0x64b0,
  /* 0x6d */
  0x64a5, 0x6493, 0x6495, 0x64a9, 0x6492, 0x64ae, 0x64ad, 0x64ab,
  0x649a, 0x64ac, 0x6499, 0x64a2, 0x64b3, 0x6575, 0x6577, 0x6578,
  0x66ae, 0x66ab, 0x66b4, 0x66b1, 0x6a23, 0x6a1f, 0x69e8, 0x6a01,
  0x6a1e, 0x6a19, 0x69fd, 0x6a21, 0x6a13, 0x6a0a, 0x69f3, 0x6a02,
  0x6a05, 0x69ed, 0x6a11, 0x6b50, 0x6b4e, 0x6ba4, 0x6bc5, 0x6bc6,
  0x6f3f, 0x6f7c, 0x6f84, 0x6f51, 0x6f66, 0x6f54, 0x6f86, 0x6f6d,
  0x6f5b, 0x6f78, 0x6f6e, 0x6f8e, 0x6f7a, 0x6f70, 0x6f64, 0x6f97,
  0x6f58, 0x6ed5, 0x6f6f, 0x6f60, 0x6f5f, 0x719f, 0x71ac, 0x71b1,
  0x71a8, 0x7256, 0x729b, 0x734e, 0x7357, 0x7469, 0x748b, 0x7483,
  0x747e, 0x7480, 0x757f, 0x7620, 0x7629, 0x761f, 0x7624, 0x7626,
  0x7621, 0x7622, 0x769a, 0x76ba, 0x76e4, 0x778e, 0x7787, 0x778c,
  0x7791, 0x778b, 0x78cb, 0x78c5, 0x78ba, 0x78ca,
  /* 0x6e */
  0x78be, 0x78d5, 0x78bc, 0x78d0, 0x7a3f, 0x7a3c, 0x7a40, 0x7a3d,
  0x7a37, 0x7a3b, 0x7aaf, 0x7aae, 0x7bad, 0x7bb1, 0x7bc4, 0x7bb4,
  0x7bc6, 0x7bc7, 0x7bc1, 0x7ba0, 0x7bcc, 0x7cca, 0x7de0, 0x7df4,
  0x7def, 0x7dfb, 0x7dd8, 0x7dec, 0x7ddd, 0x7de8, 0x7de3, 0x7dda,
  0x7dde, 0x7de9, 0x7d9e, 0x7dd9, 0x7df2, 0x7df9, 0x7f75, 0x7f77,
  0x7faf, 0x7fe9, 0x8026, 0x819b, 0x819c, 0x819d, 0x81a0, 0x819a,
  0x8198, 0x8517, 0x853d, 0x851a, 0x84ee, 0x852c, 0x852d, 0x8513,
  0x8511, 0x8523, 0x8521, 0x8514, 0x84ec, 0x8525, 0x84ff, 0x8506,
  0x8782, 0x8774, 0x8776, 0x8760, 0x8766, 0x8778, 0x8768, 0x8759,
  0x8757, 0x874c, 0x8753, 0x885b, 0x885d, 0x8910, 0x8907, 0x8912,
  0x8913, 0x8915, 0x890a, 0x8abc, 0x8ad2, 0x8ac7, 0x8ac4, 0x8a95,
  0x8acb, 0x8af8, 0x8ab2, 0x8ac9, 0x8ac2, 0x8abf,
  /* 0x6f */
  0x8ab0, 0x8ad6, 0x8acd, 0x8ab6, 0x8ab9, 0x8adb, 0x8c4c, 0x8c4e,
  0x8c6c, 0x8ce0, 0x8cde, 0x8ce6, 0x8ce4, 0x8cec, 0x8ced, 0x8ce2,
  0x8ce3, 0x8cdc, 0x8cea, 0x8ce1, 0x8d6d, 0x8d9f, 0x8da3, 0x8e2b,
  0x8e10, 0x8e1d, 0x8e22, 0x8e0f, 0x8e29, 0x8e1f, 0x8e21, 0x8e1e,
  0x8eba, 0x8f1d, 0x8f1b, 0x8f1f, 0x8f29, 0x8f26, 0x8f2a, 0x8f1c,
  0x8f1e, 0x8f25, 0x9069, 0x906e, 0x9068, 0x906d, 0x9077, 0x9130,
  0x912d, 0x9127, 0x9131, 0x9187, 0x9189, 0x918b, 0x9183, 0x92c5,
  0x92bb, 0x92b7, 0x92ea, 0x92e4, 0x92c1, 0x92b3, 0x92bc, 0x92d2,
  0x92c7, 0x92f0, 0x92b2, 0x95ad, 0x95b1, 0x9704, 0x9706, 0x9707,
  0x9709, 0x9760, 0x978d, 0x978b, 0x978f, 0x9821, 0x982b, 0x981c,
  0x98b3, 0x990a, 0x9913, 0x9912, 0x9918, 0x99dd, 0x99d0, 0x99df,
  0x99db, 0x99d1, 0x99d5, 0x99d2, 0x99d9, 0x9ab7,
  /* 0x70 */
  0x9aee, 0x9aef, 0x9b27, 0x9b45, 0x9b44, 0x9b77, 0x9b6f, 0x9d06,
  0x9d09, 0x9d03, 0x9ea9, 0x9ebe, 0x9ece, 0x58a8, 0x9f52, 0x5112,
  0x5118, 0x5114, 0x5110, 0x5115, 0x5180, 0x51aa, 0x51dd, 0x5291,
  0x5293, 0x52f3, 0x5659, 0x566b, 0x5679, 0x5669, 0x5664, 0x5678,
  0x566a, 0x5668, 0x5665, 0x5671, 0x566f, 0x566c, 0x5662, 0x5676,
  0x58c1, 0x58be, 0x58c7, 0x58c5, 0x596e, 0x5b1d, 0x5b34, 0x5b78,
  0x5bf0, 0x5c0e, 0x5f4a, 0x61b2, 0x6191, 0x61a9, 0x618a, 0x61cd,
  0x61b6, 0x61be, 0x61ca, 0x61c8, 0x6230, 0x64c5, 0x64c1, 0x64cb,
  0x64bb, 0x64bc, 0x64da, 0x64c4, 0x64c7, 0x64c2, 0x64cd, 0x64bf,
  0x64d2, 0x64d4, 0x64be, 0x6574, 0x66c6, 0x66c9, 0x66b9, 0x66c4,
  0x66c7, 0x66b8, 0x6a3d, 0x6a38, 0x6a3a, 0x6a59, 0x6a6b, 0x6a58,
  0x6a39, 0x6a44, 0x6a62, 0x6a61, 0x6a4b, 0x6a47,
  /* 0x71 */
  0x6a35, 0x6a5f, 0x6a48, 0x6b59, 0x6b77, 0x6c05, 0x6fc2, 0x6fb1,
  0x6fa1, 0x6fc3, 0x6fa4, 0x6fc1, 0x6fa7, 0x6fb3, 0x6fc0, 0x6fb9,
  0x6fb6, 0x6fa6, 0x6fa0, 0x6fb4, 0x71be, 0x71c9, 0x71d0, 0x71d2,
  0x71c8, 0x71d5, 0x71b9, 0x71ce, 0x71d9, 0x71dc, 0x71c3, 0x71c4,
  0x7368, 0x749c, 0x74a3, 0x7498, 0x749f, 0x749e, 0x74e2, 0x750c,
  0x750d, 0x7634, 0x7638, 0x763a, 0x76e7, 0x76e5, 0x77a0, 0x779e,
  0x779f, 0x77a5, 0x78e8, 0x78da, 0x78ec, 0x78e7, 0x79a6, 0x7a4d,
  0x7a4e, 0x7a46, 0x7a4c, 0x7a4b, 0x7aba, 0x7bd9, 0x7c11, 0x7bc9,
  0x7be4, 0x7bdb, 0x7be1, 0x7be9, 0x7be6, 0x7cd5, 0x7cd6, 0x7e0a,
  0x7e11, 0x7e08, 0x7e1b, 0x7e23, 0x7e1e, 0x7e1d, 0x7e09, 0x7e10,
  0x7f79, 0x7fb2, 0x7ff0, 0x7ff1, 0x7fee, 0x8028, 0x81b3, 0x81a9,
  0x81a8, 0x81fb, 0x8208, 0x8258, 0x8259, 0x854a,
  /* 0x72 */
  0x8559, 0x8548, 0x8568, 0x8569, 0x8543, 0x8549, 0x856d, 0x856a,
  0x855e, 0x8783, 0x879f, 0x879e, 0x87a2, 0x878d, 0x8861, 0x892a,
  0x8932, 0x8925, 0x892b, 0x8921, 0x89aa, 0x89a6, 0x8ae6, 0x8afa,
  0x8aeb, 0x8af1, 0x8b00, 0x8adc, 0x8ae7, 0x8aee, 0x8afe, 0x8b01,
  0x8b02, 0x8af7, 0x8aed, 0x8af3, 0x8af6, 0x8afc, 0x8c6b, 0x8c6d,
  0x8c93, 0x8cf4, 0x8e44, 0x8e31, 0x8e34, 0x8e42, 0x8e39, 0x8e35,
  0x8f3b, 0x8f2f, 0x8f38, 0x8f33, 0x8fa8, 0x8fa6, 0x9075, 0x9074,
  0x9078, 0x9072, 0x907c, 0x907a, 0x9134, 0x9192, 0x9320, 0x9336,
  0x92f8, 0x9333, 0x932f, 0x9322, 0x92fc, 0x932b, 0x9304, 0x931a,
  0x9310, 0x9326, 0x9321, 0x9315, 0x932e, 0x9319, 0x95bb, 0x96a7,
  0x96a8, 0x96aa, 0x96d5, 0x970e, 0x9711, 0x9716, 0x970d, 0x9713,
  0x970f, 0x975b, 0x975c, 0x9766, 0x9798, 0x9830,
  /* 0x73 */
  0x9838, 0x983b, 0x9837, 0x982d, 0x9839, 0x9824, 0x9910, 0x9928,
  0x991e, 0x991b, 0x9921, 0x991a, 0x99ed, 0x99e2, 0x99f1, 0x9ab8,
  0x9abc, 0x9afb, 0x9aed, 0x9b28, 0x9b91, 0x9d15, 0x9d23, 0x9d26,
  0x9d28, 0x9d12, 0x9d1b, 0x9ed8, 0x9ed4, 0x9f8d, 0x9f9c, 0x512a,
  0x511f, 0x5121, 0x5132, 0x52f5, 0x568e, 0x5680, 0x5690, 0x5685,
  0x5687, 0x568f, 0x58d5, 0x58d3, 0x58d1, 0x58ce, 0x5b30, 0x5b2a,
  0x5b24, 0x5b7a, 0x5c37, 0x5c68, 0x5dbc, 0x5dba, 0x5dbd, 0x5db8,
  0x5e6b, 0x5f4c, 0x5fbd, 0x61c9, 0x61c2, 0x61c7, 0x61e6, 0x61cb,
  0x6232, 0x6234, 0x64ce, 0x64ca, 0x64d8, 0x64e0, 0x64f0, 0x64e6,
  0x64ec, 0x64f1, 0x64e2, 0x64ed, 0x6582, 0x6583, 0x66d9, 0x66d6,
  0x6a80, 0x6a94, 0x6a84, 0x6aa2, 0x6a9c, 0x6adb, 0x6aa3, 0x6a7e,
  0x6a97, 0x6a90, 0x6aa0, 0x6b5c, 0x6bae, 0x6bda,
  /* 0x74 */
  0x6c08, 0x6fd8, 0x6ff1, 0x6fdf, 0x6fe0, 0x6fdb, 0x6fe4, 0x6feb,
  0x6fef, 0x6f80, 0x6fec, 0x6fe1, 0x6fe9, 0x6fd5, 0x6fee, 0x6ff0,
  0x71e7, 0x71df, 0x71ee, 0x71e6, 0x71e5, 0x71ed, 0x71ec, 0x71f4,
  0x71e0, 0x7235, 0x7246, 0x7370, 0x7372, 0x74a9, 0x74b0, 0x74a6,
  0x74a8, 0x7646, 0x7642, 0x764c, 0x76ea, 0x77b3, 0x77aa, 0x77b0,
  0x77ac, 0x77a7, 0x77ad, 0x77ef, 0x78f7, 0x78fa, 0x78f4, 0x78ef,
  0x7901, 0x79a7, 0x79aa, 0x7a57, 0x7abf, 0x7c07, 0x7c0d, 0x7bfe,
  0x7bf7, 0x7c0c, 0x7be0, 0x7ce0, 0x7cdc, 0x7cde, 0x7ce2, 0x7cdf,
  0x7cd9, 0x7cdd, 0x7e2e, 0x7e3e, 0x7e46, 0x7e37, 0x7e32, 0x7e43,
  0x7e2b, 0x7e3d, 0x7e31, 0x7e45, 0x7e41, 0x7e34, 0x7e39, 0x7e48,
  0x7e35, 0x7e3f, 0x7e2f, 0x7f44, 0x7ff3, 0x7ffc, 0x8071, 0x8072,
  0x8070, 0x806f, 0x8073, 0x81c6, 0x81c3, 0x81ba,
  /* 0x75 */
  0x81c2, 0x81c0, 0x81bf, 0x81bd, 0x81c9, 0x81be, 0x81e8, 0x8209,
  0x8271, 0x85aa, 0x8584, 0x857e, 0x859c, 0x8591, 0x8594, 0x85af,
  0x859b, 0x8587, 0x85a8, 0x858a, 0x85a6, 0x8667, 0x87c0, 0x87d1,
  0x87b3, 0x87d2, 0x87c6, 0x87ab, 0x87bb, 0x87ba, 0x87c8, 0x87cb,
  0x893b, 0x8936, 0x8944, 0x8938, 0x893d, 0x89ac, 0x8b0e, 0x8b17,
  0x8b19, 0x8b1b, 0x8b0a, 0x8b20, 0x8b1d, 0x8b04, 0x8b10, 0x8c41,
  0x8c3f, 0x8c73, 0x8cfa, 0x8cfd, 0x8cfc, 0x8cf8, 0x8cfb, 0x8da8,
  0x8e49, 0x8e4b, 0x8e48, 0x8e4a, 0x8f44, 0x8f3e, 0x8f42, 0x8f45,
  0x8f3f, 0x907f, 0x907d, 0x9084, 0x9081, 0x9082, 0x9080, 0x9139,
  0x91a3, 0x919e, 0x919c, 0x934d, 0x9382, 0x9328, 0x9375, 0x934a,
  0x9365, 0x934b, 0x9318, 0x937e, 0x936c, 0x935b, 0x9370, 0x935a,
  0x9354, 0x95ca, 0x95cb, 0x95cc, 0x95c8, 0x95c6,
  /* 0x76 */
  0x96b1, 0x96b8, 0x96d6, 0x971c, 0x971e, 0x97a0, 0x97d3, 0x9846,
  0x98b6, 0x9935, 0x9a01, 0x99ff, 0x9bae, 0x9bab, 0x9baa, 0x9bad,
  0x9d3b, 0x9d3f, 0x9e8b, 0x9ecf, 0x9ede, 0x9edc, 0x9edd, 0x9edb,
  0x9f3e, 0x9f4b, 0x53e2, 0x5695, 0x56ae, 0x58d9, 0x58d8, 0x5b38,
  0x5f5e, 0x61e3, 0x6233, 0x64f4, 0x64f2, 0x64fe, 0x6506, 0x64fa,
  0x64fb, 0x64f7, 0x65b7, 0x66dc, 0x6726, 0x6ab3, 0x6aac, 0x6ac3,
  0x6abb, 0x6ab8, 0x6ac2, 0x6aae, 0x6aaf, 0x6b5f, 0x6b78, 0x6baf,
  0x7009, 0x700b, 0x6ffe, 0x7006, 0x6ffa, 0x7011, 0x700f, 0x71fb,
  0x71fc, 0x71fe, 0x71f8, 0x7377, 0x7375, 0x74a7, 0x74bf, 0x7515,
  0x7656, 0x7658, 0x7652, 0x77bd, 0x77bf, 0x77bb, 0x77bc, 0x790e,
  0x79ae, 0x7a61, 0x7a62, 0x7a60, 0x7ac4, 0x7ac5, 0x7c2b, 0x7c27,
  0x7c2a, 0x7c1e, 0x7c23, 0x7c21, 0x7ce7, 0x7e54,
  /* 0x77 */
  0x7e55, 0x7e5e, 0x7e5a, 0x7e61, 0x7e52, 0x7e59, 0x7f48, 0x7ff9,
  0x7ffb, 0x8077, 0x8076, 0x81cd, 0x81cf, 0x820a, 0x85cf, 0x85a9,
  0x85cd, 0x85d0, 0x85c9, 0x85b0, 0x85ba, 0x85b9, 0x87ef, 0x87ec,
  0x87f2, 0x87e0, 0x8986, 0x89b2, 0x89f4, 0x8b28, 0x8b39, 0x8b2c,
  0x8b2b, 0x8c50, 0x8d05, 0x8e59, 0x8e63, 0x8e66, 0x8e64, 0x8e5f,
  0x8e55, 0x8ec0, 0x8f49, 0x8f4d, 0x9087, 0x9083, 0x9088, 0x91ab,
  0x91ac, 0x91d0, 0x9394, 0x938a, 0x9396, 0x93a2, 0x93b3, 0x93ae,
  0x93ac, 0x93b0, 0x9398, 0x939a, 0x9397, 0x95d4, 0x95d6, 0x95d0,
  0x95d5, 0x96e2, 0x96dc, 0x96d9, 0x96db, 0x96de, 0x9724, 0x97a3,
  0x97a6, 0x97ad, 0x97f9, 0x984d, 0x984f, 0x984c, 0x984e, 0x9853,
  0x98ba, 0x993e, 0x993f, 0x993d, 0x992e, 0x99a5, 0x9a0e, 0x9ac1,
  0x9b03, 0x9b06, 0x9b4f, 0x9b4e, 0x9b4d, 0x9bca,
  /* 0x78 */
  0x9bc9, 0x9bfd, 0x9bc8, 0x9bc0, 0x9d51, 0x9d5d, 0x9d60, 0x9ee0,
  0x9f15, 0x9f2c, 0x5133, 0x56a5, 0x56a8, 0x58de, 0x58df, 0x58e2,
  0x5bf5, 0x9f90, 0x5eec, 0x61f2, 0x61f7, 0x61f6, 0x61f5, 0x6500,
  0x650f, 0x66e0, 0x66dd, 0x6ae5, 0x6add, 0x6ada, 0x6ad3, 0x701b,
  0x701f, 0x7028, 0x701a, 0x701d, 0x7015, 0x7018, 0x7206, 0x720d,
  0x7258, 0x72a2, 0x7378, 0x737a, 0x74bd, 0x74ca, 0x74e3, 0x7587,
  0x7586, 0x765f, 0x7661, 0x77c7, 0x7919, 0x79b1, 0x7a6b, 0x7a69,
  0x7c3e, 0x7c3f, 0x7c38, 0x7c3d, 0x7c37, 0x7c40, 0x7e6b, 0x7e6d,
  0x7e79, 0x7e69, 0x7e6a, 0x7e73, 0x7f85, 0x7fb6, 0x7fb9, 0x7fb8,
  0x81d8, 0x85e9, 0x85dd, 0x85ea, 0x85d5, 0x85e4, 0x85e5, 0x85f7,
  0x87fb, 0x8805, 0x880d, 0x87f9, 0x87fe, 0x8960, 0x895f, 0x8956,
  0x895e, 0x8b41, 0x8b5c, 0x8b58, 0x8b49, 0x8b5a,
  /* 0x79 */
  0x8b4e, 0x8b4f, 0x8b46, 0x8b59, 0x8d08, 0x8d0a, 0x8e7c, 0x8e72,
  0x8e87, 0x8e76, 0x8e6c, 0x8e7a, 0x8e74, 0x8f54, 0x8f4e, 0x8fad,
  0x908a, 0x908b, 0x91b1, 0x91ae, 0x93e1, 0x93d1, 0x93df, 0x93c3,
  0x93c8, 0x93dc, 0x93dd, 0x93d6, 0x93e2, 0x93cd, 0x93d8, 0x93e4,
  0x93d7, 0x93e8, 0x95dc, 0x96b4, 0x96e3, 0x972a, 0x9727, 0x9761,
  0x97dc, 0x97fb, 0x985e, 0x9858, 0x985b, 0x98bc, 0x9945, 0x9949,
  0x9a16, 0x9a19, 0x9b0d, 0x9be8, 0x9be7, 0x9bd6, 0x9bdb, 0x9d89,
  0x9d61, 0x9d72, 0x9d6a, 0x9d6c, 0x9e92, 0x9e97, 0x9e93, 0x9eb4,
  0x52f8, 0x56b7, 0x56b6, 0x56b4, 0x56bc, 0x58e4, 0x5b40, 0x5b43,
  0x5b7d, 0x5bf6, 0x5dc9, 0x61f8, 0x61fa, 0x6518, 0x6514, 0x6519,
  0x66e6, 0x6727, 0x6aec, 0x703e, 0x7030, 0x7032, 0x7210, 0x737b,
  0x74cf, 0x7662, 0x7665, 0x7926, 0x792a, 0x792c,
  /* 0x7a */
  0x792b, 0x7ac7, 0x7af6, 0x7c4c, 0x7c43, 0x7c4d, 0x7cef, 0x7cf0,
  0x8fae, 0x7e7d, 0x7e7c, 0x7e82, 0x7f4c, 0x8000, 0x81da, 0x8266,
  0x85fb, 0x85f9, 0x8611, 0x85fa, 0x8606, 0x860b, 0x8607, 0x860a,
  0x8814, 0x8815, 0x8964, 0x89ba, 0x89f8, 0x8b70, 0x8b6c, 0x8b66,
  0x8b6f, 0x8b5f, 0x8b6b, 0x8d0f, 0x8d0d, 0x8e89, 0x8e81, 0x8e85,
  0x8e82, 0x91b4, 0x91cb, 0x9418, 0x9403, 0x93fd, 0x95e1, 0x9730,
  0x98c4, 0x9952, 0x9951, 0x99a8, 0x9a2b, 0x9a30, 0x9a37, 0x9a35,
  0x9c13, 0x9c0d, 0x9e79, 0x9eb5, 0x9ee8, 0x9f2f, 0x9f5f, 0x9f63,
  0x9f61, 0x5137, 0x5138, 0x56c1, 0x56c0, 0x56c2, 0x5914, 0x5c6c,
  0x5dcd, 0x61fc, 0x61fe, 0x651d, 0x651c, 0x6595, 0x66e9, 0x6afb,
  0x6b04, 0x6afa, 0x6bb2, 0x704c, 0x721b, 0x72a7, 0x74d6, 0x74d4,
  0x7669, 0x77d3, 0x7c50, 0x7e8f, 0x7e8c, 0x7fbc,
  /* 0x7b */
  0x8617, 0x862d, 0x861a, 0x8823, 0x8822, 0x8821, 0x881f, 0x896a,
  0x896c, 0x89bd, 0x8b74, 0x8b77, 0x8b7d, 0x8d13, 0x8e8a, 0x8e8d,
  0x8e8b, 0x8f5f, 0x8faf, 0x91ba, 0x942e, 0x9433, 0x9435, 0x943a,
  0x9438, 0x9432, 0x942b, 0x95e2, 0x9738, 0x9739, 0x9732, 0x97ff,
  0x9867, 0x9865, 0x9957, 0x9a45, 0x9a43, 0x9a40, 0x9a3e, 0x9acf,
  0x9b54, 0x9b51, 0x9c2d, 0x9c25, 0x9daf, 0x9db4, 0x9dc2, 0x9db8,
  0x9e9d, 0x9eef, 0x9f19, 0x9f5c, 0x9f66, 0x9f67, 0x513c, 0x513b,
  0x56c8, 0x56ca, 0x56c9, 0x5b7f, 0x5dd4, 0x5dd2, 0x5f4e, 0x61ff,
  0x6524, 0x6b0a, 0x6b61, 0x7051, 0x7058, 0x7380, 0x74e4, 0x758a,
  0x766e, 0x766c, 0x79b3, 0x7c60, 0x7c5f, 0x807e, 0x807d, 0x81df,
  0x8972, 0x896f, 0x89fc, 0x8b80, 0x8d16, 0x8d17, 0x8e91, 0x8e93,
  0x8f61, 0x9148, 0x9444, 0x9451, 0x9452, 0x973d,
  /* 0x7c */
  0x973e, 0x97c3, 0x97c1, 0x986b, 0x9955, 0x9a55, 0x9a4d, 0x9ad2,
  0x9b1a, 0x9c49, 0x9c31, 0x9c3e, 0x9c3b, 0x9dd3, 0x9dd7, 0x9f34,
  0x9f6c, 0x9f6a, 0x9f94, 0x56cc, 0x5dd6, 0x6200, 0x6523, 0x652b,
  0x652a, 0x66ec, 0x6b10, 0x74da, 0x7aca, 0x7c64, 0x7c63, 0x7c65,
  0x7e93, 0x7e96, 0x7e94, 0x81e2, 0x8638, 0x863f, 0x8831, 0x8b8a,
  0x9090, 0x908f, 0x9463, 0x9460, 0x9464, 0x9768, 0x986f, 0x995c,
  0x9a5a, 0x9a5b, 0x9a57, 0x9ad3, 0x9ad4, 0x9ad1, 0x9c54, 0x9c57,
  0x9c56, 0x9de5, 0x9e9f, 0x9ef4, 0x56d1, 0x58e9, 0x652c, 0x705e,
  0x7671, 0x7672, 0x77d7, 0x7f50, 0x7f88, 0x8836, 0x8839, 0x8862,
  0x8b93, 0x8b92, 0x8b96, 0x8277, 0x8d1b, 0x91c0, 0x946a, 0x9742,
  0x9748, 0x9744, 0x97c6, 0x9870, 0x9a5f, 0x9b22, 0x9b58, 0x9c5f,
  0x9df9, 0x9dfa, 0x9e7c, 0x9e7d, 0x9f07, 0x9f77,
  /* 0x7d */
  0x9f72, 0x5ef3, 0x6b16, 0x7063, 0x7c6c, 0x7c6e, 0x883b, 0x89c0,
  0x8ea1, 0x91c1, 0x9472, 0x9470, 0x9871, 0x995e, 0x9ad6, 0x9b23,
  0x9ecc, 0x7064, 0x77da, 0x8b9a, 0x9477, 0x97c9, 0x9a62, 0x9a65,
  0x7e9c, 0x8b9c, 0x8eaa, 0x91c5, 0x947d, 0x947e, 0x947c, 0x9c77,
  0x9c78, 0x9ef7, 0x8c54, 0x947f, 0x9e1a, 0x7228, 0x9a6a, 0x9b31,
  0x9e1b, 0x9e1e, 0x7c72,
};

static int
cns11643_1_mbtowc (conv_t conv, ucs4_t *pwc, const unsigned char *s, int n)
{
  unsigned char c1 = s[0];
  if ((c1 >= 0x21 && c1 <= 0x27) || (c1 == 0x42) || (c1 >= 0x44 && c1 <= 0x7d)) {
    if (n >= 2) {
      unsigned char c2 = s[1];
      if (c2 >= 0x21 && c2 < 0x7f) {
        unsigned int i = 94 * (c1 - 0x21) + (c2 - 0x21);
        unsigned short wc = 0xfffd;
        if (i < 3102) {
          if (i < 500)
            wc = cns11643_1_2uni_page21[i];
          else if (i == 571)
            wc = 0x4ea0;
          else if (i == 578)
            wc = 0x51ab;
          else if (i == 583)
            wc = 0x52f9;
        } else if (i < 3290) {
          if (i < 3136)
            wc = cns11643_1_2uni_page42[i-3102];
        } else {
          if (i < 8691)
            wc = cns11643_1_2uni_page44[i-3290];
        }
        if (wc != 0xfffd) {
          *pwc = (ucs4_t) wc;
          return 2;
        }
      }
      return RET_ILSEQ;
    }
    return RET_TOOFEW(0);
  }
  return RET_ILSEQ;
}

