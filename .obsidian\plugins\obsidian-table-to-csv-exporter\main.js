/*
THIS IS A GENERATED/BUNDLED FILE BY ESBUILD
if you want to view the source, please visit the github repository of this plugin
*/

var __create = Object.create;
var __defProp = Object.defineProperty;
var __getOwnPropDesc = Object.getOwnPropertyDescriptor;
var __getOwnPropNames = Object.getOwnPropertyNames;
var __getProtoOf = Object.getPrototypeOf;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __markAsModule = (target) => __defProp(target, "__esModule", { value: true });
var __export = (target, all) => {
  __markAsModule(target);
  for (var name in all)
    __defProp(target, name, { get: all[name], enumerable: true });
};
var __reExport = (target, module2, desc) => {
  if (module2 && typeof module2 === "object" || typeof module2 === "function") {
    for (let key of __getOwnPropNames(module2))
      if (!__hasOwnProp.call(target, key) && key !== "default")
        __defProp(target, key, { get: () => module2[key], enumerable: !(desc = __getOwnPropDesc(module2, key)) || desc.enumerable });
  }
  return target;
};
var __toModule = (module2) => {
  return __reExport(__markAsModule(__defProp(module2 != null ? __create(__getProtoOf(module2)) : {}, "default", module2 && module2.__esModule && "default" in module2 ? { get: () => module2.default, enumerable: true } : { value: module2, enumerable: true })), module2);
};
var __async = (__this, __arguments, generator) => {
  return new Promise((resolve, reject) => {
    var fulfilled = (value) => {
      try {
        step(generator.next(value));
      } catch (e) {
        reject(e);
      }
    };
    var rejected = (value) => {
      try {
        step(generator.throw(value));
      } catch (e) {
        reject(e);
      }
    };
    var step = (x) => x.done ? resolve(x.value) : Promise.resolve(x.value).then(fulfilled, rejected);
    step((generator = generator.apply(__this, __arguments)).next());
  });
};

// main.ts
__export(exports, {
  default: () => Table2CSVPlugin
});
var import_obsidian = __toModule(require("obsidian"));
var DEFAULT_SETTINGS = {
  exportPath: "./",
  baseFilename: "table-export",
  fileNumber: "001",
  sepChar: "sepChar-semicolon",
  quoteDataChar: "quoteChar-noQuote",
  saveToClipboardToo: false,
  removeCRLF: "removeCRLF-space"
};
var Table2CSVPlugin = class extends import_obsidian.Plugin {
  onload() {
    return __async(this, null, function* () {
      yield this.loadSettings();
      this.addCommand({
        id: "obsidian-table-to-csv-exporter",
        name: "Export table to CSV file",
        checkCallback: (checking) => {
          const view = this.app.workspace.getActiveViewOfType(import_obsidian.MarkdownView);
          if (view) {
            if (!checking) {
              const viewMode = view.getMode();
              if (viewMode == "preview") {
                const csvString = htmlToCSV(view.previewMode.containerEl, this.settings.sepChar, this.settings.quoteDataChar, this.settings.removeCRLF);
                if (csvString.length > 0) {
                  const filename = `${this.settings.baseFilename}-${this.settings.fileNumber}.csv`;
                  this.app.vault.create(filename, csvString).then(() => {
                    let fn = +this.settings.fileNumber;
                    fn++;
                    if (fn == 1e3)
                      fn = 1;
                    let newFileNumberString = fn + "";
                    while (newFileNumberString.length < 3)
                      newFileNumberString = "0" + newFileNumberString;
                    this.settings.fileNumber = newFileNumberString;
                    if (this.settings.saveToClipboardToo) {
                      navigator.clipboard.writeText(csvString).then(() => {
                        new import_obsidian.Notice(`The file ${filename} was successfully created in your vault. The contents was also copied to the clipboard.`);
                      }).catch((err) => {
                        new import_obsidian.Notice("There was an error with copying the contents to the clipboard.");
                      });
                    } else {
                      new import_obsidian.Notice(`The file ${filename} was successfully created in your vault.`);
                    }
                  }).catch((error) => {
                    const errorMessage = `Error: ${error.message}`;
                    new import_obsidian.Notice(errorMessage);
                  });
                } else {
                  new import_obsidian.Notice(`No table was found. No CSV file was written.`);
                }
              } else {
                new import_obsidian.Notice("This command only works on panes in reading mode! \u2013 No CSV files were written.");
              }
            }
            return true;
          }
          return false;
        }
      });
      this.addSettingTab(new Table2CSVSettingTab(this.app, this));
      console.log(`Table to CSV plugin: Version ${this.manifest.version} loaded.`);
    });
  }
  onunload() {
  }
  loadSettings() {
    return __async(this, null, function* () {
      this.settings = Object.assign({}, DEFAULT_SETTINGS, yield this.loadData());
    });
  }
  saveSettings() {
    return __async(this, null, function* () {
      yield this.saveData(this.settings);
    });
  }
};
function htmlToCSV(html, sepMode, quoteChar, removeCRLF) {
  var data = [];
  var table = html.querySelector("table");
  if (table) {
    var rows = table.rows;
    for (var i = 0; i < rows.length; i++) {
      var row = [], cols = rows[i].querySelectorAll("td, th");
      for (var j = 0; j < cols.length; j++) {
        var cellContent = cols[j].innerText;
        if (removeCRLF == "removeCRLF-clear") {
          cellContent = cellContent.replace(/(\r\n|\n|\r)/gm, "");
        } else if (removeCRLF == "removeCRLF-space") {
          cellContent = cellContent.replace(/(\r\n|\n|\r)/gm, " ");
        } else if (removeCRLF == "removeCRLF-string1") {
          cellContent = cellContent.replace(/(\r\n|\n|\r)/gm, "[CR]");
        }
        if (quoteChar == "quoteChar-doubleQuotes") {
          cellContent = '"' + cellContent + '"';
        } else if (quoteChar == "quoteChar-singleQuotes") {
          cellContent = "'" + cellContent + "'";
        }
        row.push(cellContent);
      }
      var sepChar = ";";
      switch (sepMode) {
        case "sepChar-semicolon":
          sepChar = ";";
          break;
        case "sepChar-comma":
          sepChar = ",";
          break;
        case "sepChar-tab":
          sepChar = "	";
          break;
        case "sepChar-pipe":
          sepChar = "|";
          break;
        case "sepChar-tilde":
          sepChar = "~";
          break;
        case "sepChar-caret":
          sepChar = "^";
          break;
        case "sepChar-colon":
          sepChar = ":";
          break;
      }
      data.push(row.join(sepChar));
    }
  }
  if (data.length > 0)
    return data.join("\n");
  else
    return "";
}
var Table2CSVSettingTab = class extends import_obsidian.PluginSettingTab {
  constructor(app, plugin) {
    super(app, plugin);
    this.plugin = plugin;
  }
  display() {
    const { containerEl } = this;
    containerEl.empty();
    containerEl.createEl("h2", { text: "Settings for the Table to CSV Plugin." });
    containerEl.createEl("p", { text: "NOTE: Currently, the exported CSV files are saved inside your vault main folder." });
    new import_obsidian.Setting(containerEl).setName("CSV file base filename").setDesc('Enter the base filename. The "File Number addendum" gets added after that and finally .csv').addText((text) => text.setPlaceholder("<enter a base filename").setValue(this.plugin.settings.baseFilename).onChange((value) => __async(this, null, function* () {
      this.plugin.settings.baseFilename = value;
      yield this.plugin.saveSettings();
    })));
    new import_obsidian.Setting(containerEl).setName("File Number addendum").setDesc("This number gets added to the base filename and incremented after each export. Normally, you shouldn't need to edit this.").addText((text) => text.setPlaceholder("").setValue(this.plugin.settings.fileNumber).onChange((value) => __async(this, null, function* () {
      this.plugin.settings.fileNumber = value;
      yield this.plugin.saveSettings();
    })));
    new import_obsidian.Setting(containerEl).setName("Data fields separation character/string").setDesc("This character will be put between each cell's data. Defaults to a semicolon.").addDropdown((dropdown) => dropdown.addOption("sepChar-semicolon", "; (semicolon)").addOption("sepChar-comma", ", (comma)").addOption("sepChar-tab", "\\t (tab)").addOption("sepChar-pipe", "| (pipe)").addOption("sepChar-tilde", "~ (tilde)").addOption("sepChar-caret", "^ (caret)").addOption("sepChar-colon", ": (colon)").setValue(this.plugin.settings.sepChar).onChange((value) => __async(this, null, function* () {
      this.plugin.settings.sepChar = value;
      yield this.plugin.saveSettings();
    })));
    new import_obsidian.Setting(containerEl).setName("Quote data").setDesc("Do you want quotation marks around each cell's data?").addDropdown((dropdown) => dropdown.addOption("quoteChar-noQuote", "Don't quote data").addOption("quoteChar-doubleQuotes", 'Quote data with double quote character (")').addOption("quoteChar-singleQuotes", "Quote data with single quote character (')").setValue(this.plugin.settings.quoteDataChar).onChange((value) => __async(this, null, function* () {
      this.plugin.settings.quoteDataChar = value;
      yield this.plugin.saveSettings();
    })));
    new import_obsidian.Setting(containerEl).setName("Handling of CR/LF in data").setDesc("Choose how to handle the occurance of return and linefeed characters in data cells.").addDropdown((dropdown) => dropdown.addOption("removeCRLF-clear", "Remove all CR & LF characters").addOption("removeCRLF-space", "Replace all CR & LF characters with one space").addOption("removeCRLF-string1", "Replace all CR & LF characters with string [CR]").setValue(this.plugin.settings.removeCRLF).onChange((value) => __async(this, null, function* () {
      this.plugin.settings.removeCRLF = value;
      yield this.plugin.saveSettings();
    })));
    new import_obsidian.Setting(containerEl).setName("Copy to clipboard, too").setDesc("Do you want to copy the contents of the CSV file to the system clipboard, too?").addToggle((toggle) => toggle.setValue(this.plugin.settings.saveToClipboardToo).onChange((value) => __async(this, null, function* () {
      this.plugin.settings.saveToClipboardToo = value;
      yield this.plugin.saveSettings();
    })));
  }
};


/* nosourcemap */