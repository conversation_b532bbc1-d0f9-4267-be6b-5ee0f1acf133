# 手动入库债券管理技术方案

## 1. 流程图

```plantuml
title 手动入库债券管理接口

participant 前端
participant em_ptms_ficc
participant MySQL

== 债券列表查询 ==
前端->em_ptms_ficc:POST /api/manualBondEntry/manualBondList
activate em_ptms_ficc
em_ptms_ficc->MySQL:查询手动入库债券列表
activate MySQL
em_ptms_ficc<--MySQL:返回债券列表数据
deactivate MySQL
em_ptms_ficc->前端:返回分页列表数据
deactivate em_ptms_ficc

== 新增债券 ==
前端->em_ptms_ficc:POST /api/manualBondEntry/addManualBond
activate em_ptms_ficc
em_ptms_ficc->MySQL:插入债券数据
activate MySQL
em_ptms_ficc<--MySQL:返回插入结果
deactivate MySQL
em_ptms_ficc->前端:返回操作结果
deactivate em_ptms_ficc

== 编辑债券 ==
前端->em_ptms_ficc:POST /api/manualBondEntry/updateManualBond
activate em_ptms_ficc
em_ptms_ficc->MySQL:更新债券数据
activate MySQL
em_ptms_ficc<--MySQL:返回更新结果
deactivate MySQL
em_ptms_ficc->前端:返回操作结果
deactivate em_ptms_ficc

== 删除债券 ==
前端->em_ptms_ficc:POST /api/manualBondEntry/deleteManualBond
activate em_ptms_ficc
em_ptms_ficc->MySQL:删除债券数据
activate MySQL
em_ptms_ficc<--MySQL:返回删除结果
deactivate MySQL
em_ptms_ficc->前端:返回操作结果
deactivate em_ptms_ficc

== Excel导入 ==
前端->em_ptms_ficc:POST /api/manualBondEntry/importManualBond
activate em_ptms_ficc
note over em_ptms_ficc:解析Excel文件\n验证数据格式
em_ptms_ficc->MySQL:批量插入债券数据
activate MySQL
em_ptms_ficc<--MySQL:返回批量插入结果
deactivate MySQL
em_ptms_ficc->前端:返回导入结果统计
deactivate em_ptms_ficc

== Excel导出 ==
前端->em_ptms_ficc:POST /api/manualBondEntry/exportManualBond
activate em_ptms_ficc
em_ptms_ficc->MySQL:查询符合条件的债券数据
activate MySQL
em_ptms_ficc<--MySQL:返回债券数据
deactivate MySQL
note over em_ptms_ficc:生成Excel文件
em_ptms_ficc->前端:返回Excel文件下载链接
deactivate em_ptms_ficc

== 模板下载 ==
前端->em_ptms_ficc:GET /api/manualBondEntry/downloadTemplate
activate em_ptms_ficc
note over em_ptms_ficc:生成Excel模板文件
em_ptms_ficc->前端:返回模板文件下载链接
deactivate em_ptms_ficc
```

## 2. 涉及的数据库、Redis、Kafka 等中间件的修改/新增图或者说明

### 2.1 新增数据库表

#### 2.1.1 手动入库债券表 (manual_bond_entry)

| 字段名 | 字段类型 | 长度 | 是否为空 | 默认值 | 描述 |
|--------|----------|------|----------|--------|------|
| EID | CHAR | 32 | NOT NULL | - | 主键UUID |
| EITIME | DATETIME | - | NOT NULL | - | 创建时间 |
| EUTIME | DATETIME | - | NOT NULL | - | 更新时间 |
| del | TINYINT | 1 | NOT NULL | 0 | 逻辑删除标志(0:未删除,1:已删除) |
| bondCode | VARCHAR | 20 | NOT NULL | - | 债券代码 |
| bondName | VARCHAR | 100 | NOT NULL | - | 债券简称 |
| groupType | VARCHAR | 10 | NOT NULL | - | 组别(自营/做市) |
| entryTime | DATE | - | NOT NULL | - | 入库时间 |
| endTime | DATE | - | NOT NULL | - | 终止时间 |

#### 2.1.2 索引设计

```sql
-- 主键索引
PRIMARY KEY (EID)

-- 债券代码索引
CREATE INDEX idx_bondCode ON ptms.manual_bond_entry (bondCode);


```

#### 2.1.3 建表SQL

```sql
CREATE TABLE ptms.manual_bond_entry
(
    EID       CHAR(32)             NOT NULL COMMENT '主键UUID'
        PRIMARY KEY,
    EITIME    DATETIME             NOT NULL COMMENT '创建时间',
    EUTIME    DATETIME             NOT NULL COMMENT '更新时间',
    del       TINYINT(1) DEFAULT 0 NOT NULL COMMENT '逻辑删除标志(0:未删除,1:已删除)',
    bondCode  VARCHAR(20)          NOT NULL COMMENT '债券代码',
    bondName  VARCHAR(100)         NOT NULL COMMENT '债券简称',
    groupType VARCHAR(10)          NOT NULL COMMENT '组别(自营/做市)',
    entryTime DATE                 NOT NULL COMMENT '入库时间',
    endTime   DATE                 NOT NULL COMMENT '终止时间',
    
)
    COMMENT '手动入库债券表';

-- 创建索引
CREATE INDEX idx_bondCode ON ptms.manual_bond_entry (bondCode);

```

## 3. 前后端交互接口信息

### 3.1 债券列表查询接口

#### 3.1.1 请求路径
POST /api/manualBondEntry/manualBondList

#### 3.1.2 入参
| 字段名       | 字段类型    | 描述               | 是否必传 | 默认值 |
| --------- | ------- | ---------------- | ---- | --- |
| pageNum   | Integer | 页码               | 是    | 1   |
| pageSize  | Integer | 每页大小             | 是    | 10  |
| bondCode  | String  | 债券代码(支持模糊搜索)     | 否    | -   |
| groupType | String  | 组别(自营/做市)        | 否    | -   |
| entryTime | String  | 入库时间(yyyy-MM-dd) | 否    | -   |

#### 3.1.3 出参
| 字段名              | 字段类型   | 描述     | 是否不为空 |
| ---------------- | ------ | ------ | ----- |
| total            | Long   | 总记录数   | 是     |
| list             | Array  | 债券列表   | 是     |
| list[].eid       | String | 主键UUID | 是     |
| list[].bondCode  | String | 债券代码   | 是     |
| list[].bondName  | String | 债券简称   | 是     |
| list[].groupType | String | 组别     | 是     |
| list[].entryTime | String | 入库时间   | 是     |
| list[].endTime   | String | 终止时间   | 是     |
| list[].eitime    | String | 创建时间   | 是     |

### 3.2 新增债券接口

#### 3.2.1 请求路径
POST /api/manualBondEntry/addManualBond

#### 3.2.2 入参
| 字段名 | 字段类型 | 描述 | 是否必传 |
|--------|----------|------|----------|
| bondCode | String | 债券代码 | 是 |
| bondName | String | 债券简称 | 是 |
| groupType | String | 组别(自营/做市) | 是 |
| entryTime | String | 入库时间(yyyy-MM-dd) | 是 |
| endTime | String | 终止时间(yyyy-MM-dd) | 是 |

#### 3.2.3 出参
| 字段名 | 字段类型 | 描述 | 是否不为空 |
|--------|----------|------|------------|
| success | Boolean | 操作是否成功 | 是 |
| message | String | 操作结果信息 | 是 |

### 3.3 编辑债券接口

#### 3.3.1 请求路径
POST /api/manualBondEntry/updateManualBond

#### 3.3.2 入参
| 字段名 | 字段类型 | 描述 | 是否必传 |
|--------|----------|------|----------|
| eid | String | 主键UUID | 是 |
| bondCode | String | 债券代码 | 是 |
| bondName | String | 债券简称 | 是 |
| groupType | String | 组别(自营/做市) | 是 |
| entryTime | String | 入库时间(yyyy-MM-dd) | 是 |
| endTime | String | 终止时间(yyyy-MM-dd) | 是 |

#### 3.3.3 出参
| 字段名 | 字段类型 | 描述 | 是否不为空 |
|--------|----------|------|------------|
| success | Boolean | 操作是否成功 | 是 |
| message | String | 操作结果信息 | 是 |

### 3.4 删除债券接口

#### 3.4.1 请求路径
POST /api/manualBondEntry/deleteManualBond

#### 3.4.2 入参
| 字段名 | 字段类型 | 描述 | 是否必传 |
|--------|----------|------|----------|
| eid | String | 主键UUID | 是 |

#### 3.4.3 出参
| 字段名 | 字段类型 | 描述 | 是否不为空 |
|--------|----------|------|------------|
| success | Boolean | 操作是否成功 | 是 |
| message | String | 操作结果信息 | 是 |

### 3.5 Excel导入接口

#### 3.5.1 请求路径
POST /api/manualBondEntry/importManualBond

#### 3.5.2 入参
| 字段名 | 字段类型 | 描述 | 是否必传 |
|--------|----------|------|----------|
| file | MultipartFile | Excel文件 | 是 |

#### 3.5.3 出参
| 字段名                  | 字段类型    | 描述     | 是否不为空 |
| -------------------- | ------- | ------ | ----- |
| success              | Boolean | 导入是否成功 | 是     |
| message              | String  | 导入结果信息 | 是     |


### 3.6 Excel导出接口

#### 3.6.1 请求路径
POST /api/manualBondEntry/exportManualBond

#### 3.6.2 入参
| 字段名          | 字段类型   | 描述                 | 是否必传 |
| ------------ | ------ | ------------------ | ---- |
| bondCode     | String | 债券代码(支持模糊搜索)       | 否    |
| groupType    | String | 组别(自营/做市)          | 否    |
| entryTime    | String | 入库时间(yyyy-MM-dd)   | 是    |

#### 3.6.3 出参
blob文件流

### 3.7 模板下载接口

#### 3.7.1 请求路径
GET /api/manualBondEntry/downloadTemplate

#### 3.7.2 入参
无

#### 3.7.3 出参
blob文件流

## 4. 配置&部署修改信息
不涉及

## 5. 新增技术组件说明
不涉及

## 6. 影响范围
- 新增手动入库债券管理功能
- 影响菜单：【综合管理】-【信用债管理】-【手动入库债券】

## 7. 外部依赖项
不涉及
