
(1) 如何安全、高效、智能地实现做市双边报价
实现自动化的做市双边报价是本项目的一个重要研究内容。当前市场上的做市报价系统主要依赖人工操作，存在效率低下、风险控制不足等问题。本项目计划通过程序化自动策略报价实现多支债券的自动化报价管理，但在实现过程中面临多个技术挑战：首先是策略管理的复杂性，系统需要支持新增策略时的分组管理，实现策略的灵活组织和批量操作，同时要确保分组内策略状态的一致性；其次是实时数据处理的挑战，系统需要同时处理来自CFETS行情和Choice估值的多源数据，并基于不同基准类型（中介最优价格、前一交易日中债估值、全市场最优价格）进行实时计算和策略执行；第三是策略重发机制的可靠性，系统需要精确监控行情变化，在达到指定阈值时触发策略重发，同时要防止过于频繁的重发。
针对上述问题，本项目创新设计了多维度的监控预警体系，包括策略重发频率监控、行情接收异常监控和策略重发时间监控等。同时，为确保订单管理的及时性，系统在策略编辑和停用时确保相关订单的及时撤销，在策略报价成交时自动处理订单状态并停用策略。在数据一致性方面，通过策略撤单监听任务实现订单状态的可靠处理，在批量修改策略时确保策略状态变更和订单撤销的原子性。系统采用分布式架构设计，通过NATS实现了高效的数据处理机制，通过Schedule框架建立了完善的监控体系，并通过异步任务处理和分布式锁确保了订单管理的可靠性。此外，系统还支持根据债券代码、债券类型、分组名称、交易员等多维度进行精确查询，并实现了组内排序功能。但是由于市场环境的复杂性和业务需求的持续演进，系统的性能优化和功能完善仍需持续投入。
