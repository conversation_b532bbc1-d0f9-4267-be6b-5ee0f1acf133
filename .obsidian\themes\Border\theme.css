/* @settings

name: Theme Info
id: Info
settings:
    - 
        id: Extras-info
        description: "🎨 Appearance presets: [get presets here](https://github.com/Akifyss/obsidian-border/blob/main/presets.md), click on “Import” in the top right corner and restart obsidian. <br> <br> 📭 If you have problems, please submit [issues](https://github.com/Akifyss/obsidian-border/issues) or [discuss](https://github.com/Akifyss/obsidian-border/discussions) on Github<br> <br> 🔼 View recent updates: [Github Commit History](https://github.com/Akifyss/obsidian-border/commits/main)"
        description.zh: "🎨 外观预设：[在此获取预设](https://github.com/Akifyss/obsidian-border/blob/main/presets.md)，点击右上角“Import”导入预设，重启obsidian来生效. <br> <br> 📭 如果遇到问题，可以在Github上 [提交issues](https://github.com/Akifyss/obsidian-border/issues) 或[讨论](https://github.com/Akifyss/obsidian-border/discussions)<br> <br> 🔼 查看最近更新：[Github Commit History](https://github.com/Akifyss/obsidian-border/commits/main)"
        type: info-text
        markdown: true
*/


/* @settings

name: Components
id: Components
settings:
    - 
        id: autohide
        title: Auto hide
        title.zh: 自动隐藏
        type: heading
        collapsed: ture
        level: 3
    - 
        id: tab-autohide
        title: Auto hide tab bar
        title.zh: 自动隐藏 tab栏
        description: Mac users can adjust the position of the traffic light using the 'Electron Window Tweaker' plugin
        description.zh: Mac 用户可以使用 “Electron Window Tweaker” 插件来调整左上角红绿灯的位置
        type: class-toggle
    - 
        id: status-bar-autohide
        title: Auto hide bottom status bar
        title.zh: 自动隐藏 底部状态栏
        type: class-toggle
    - 
        id: Ribbon-autohide
        title: Auto hide left ribbon menu
        title.zh: 自动隐藏 左侧功能区菜单
        type: class-toggle
    - 
        id: nav-header-autohide
        title: Auto hide header icons in sidepane
        title.zh: 自动隐藏 侧边面板中的头部按钮
        type: class-toggle
    - 
        id: tab-title-bar-autohide
        title: Auto hide tab title bar
        title.zh: 自动隐藏 标签页标题栏
        type: class-toggle
    - 
        id: vault-profile-autohide
        title: Auto hide vault profile
        title.zh: 自动隐藏 vault 配置栏
        type: class-toggle
    - 
        id: file-explorer
        title: File explorer
        title.zh: 文件列表
        type: heading
        collapsed: true
        level: 3
    - 
        id: CTA-BTN-enable
        title: Enable bigger "New note" button
        title.zh: 启用 更大的“New Note”按钮
        type: class-toggle
    - 
        id: file-names-untrim
        title: Untrim file names
        title.zh: 不修剪文件名
        type: class-toggle
    - 
        id: folder-font-bold
        title: Bold folder font
        title.zh: 加粗文件夹字体
        type: class-toggle
    - 
        id: file-explorer-icon
        title: icon
        title.zh: 图标
        type: heading
        collapsed: false
        level: 4
    - 
        id: file-icon-remove
        title: Remove custom icon
        title.zh: 移除 自定义图标
        type: class-toggle
    - 
        id: colorful-folder
        title: Enable colorful folder icon
        title.zh: 启用 多彩文件夹图标
        type: class-toggle
    - 
        id: outline-panel
        title: Outline Panel
        title.zh: 大纲面板
        type: heading
        collapsed: true
        level: 3
    - 
        id: outline-enhanced
        title: Enable enhanced outline style
        title.zh: 启用 增强的大纲样式
        description: from https://github.com/subframe7536/obsidian-theme-maple, author:@subframe7536
        type: class-toggle
    - 
        id: New-tab
        title: New tab
        title.zh: 新标签页
        type: heading
        collapsed: true
        level: 3
    - 
        id: new-tab-btn-select
        title: New tab buttons
        title.zh: 新标签页 按钮
        type: class-select
        allowEmpty: false
        default: new-tab-btn-default
        options:
            -   label: text buttons (obsidian default)
                value: new-tab-text-btn-restore
            -   label: default
                value: new-tab-btn-default
    - 
        id: new-tab-image-select
        title: New tab image
        title.zh: 新标签页 图像
        type: class-select
        allowEmpty: false
        default: new-tab-image-default
        options:
            -   label: none
                value: new-tab-image-none
            -   label: obsidian logo
                value: new-tab-image-default
            -   label: old default
                value: new-tab-image-old
            -   label: customize image
                value: new-tab-image-customize
    - 
        id: new-tab-image
        title: customize new tab image
        title.zh: 自定义新标签页图像
        type: variable-text
        description: Enter the url of the image into quotes
        description.zh: 向引号内输入图像的url
        default: url(" ")
    - 
        id: Graph-view
        title: Graph view
        title.zh: 关系图谱
        type: heading
        collapsed: true
        level: 3
    - 
		id: graph-text
		title: graph-text color
		type: variable-themed-color
		opacity: true
		format: hex
		default-light: '#'
		default-dark: '#'
    - 
		id: graph-line
		title: graph-line color
		type: variable-themed-color
		opacity: true
        format: hex
		default-light: '#'
		default-dark: '#'
    - 
		id: graph-node
		title: graph-node color
		type: variable-themed-color
		opacity: true
        format: hex
		default-light: '#'
		default-dark: '#'
    - 
		id: graph-node-unresolved
		title: graph-node-unresolved color
		type: variable-themed-color
		opacity: true
        format: hex
		default-light: '#'
		default-dark: '#'
    - 
		id: graph-node-focused
		title: graph-node-focused color
		type: variable-themed-color
		opacity: true
        format: hex
		default-light: '#'
		default-dark: '#'
    - 
		id: graph-node-tag
		title: graph-node-tag color
		type: variable-themed-color
		opacity: true
        format: hex
		default-light: '#'
		default-dark: '#'
    - 
		id: graph-node-attachment
		title: graph-node-attachment color
		type: variable-themed-color
		opacity: true
        format: hex
		default-light: '#'
		default-dark: '#'
    - 
        id: Canvas
        title: Canvas
        title.zh: 白板
        type: heading
        collapsed: true
        level: 3
    - 
        id: immersive-canvas
        title: immersive canvas
        title.zh: 沉浸式白板
        type: class-toggle
    - 
        id: media-embed-card-border-off
        title: Remove border effect of media card.
        title.zh: 移除媒体卡片的边框效果
        type: class-toggle
    - 
		id: canvas-background
		title: Customize canvas background color
        title.zh: 自定义 canvas 背景色
		type: variable-themed-color
		opacity: true
		format: hex
		default-light: '#'
		default-dark: '#'
    - 
		id: canvas-card-border-width
		title: canvas card border width
        title.zh: canvas 卡片边框大小
        type: variable-text
        description: Input any CSS border-width value
        description.zh: 输入任意 CSS border-width 值
        default: 2px
    - 
		id: canvas-card-border-style
		title: canvas card border style
        title.zh: canvas 卡片边框风格
        type: variable-text
        description: Input any CSS border-style value
        description.zh: 输入任意 CSS border-style 值
        default: solid
    - 
        id: canvas-card-menu
        title: canvas card menu position
        title.zh: canvas 卡片菜单位置
        type: class-select
        allowEmpty: false
        default: canvas-card-menu-center
        options:
            -   label: center(obsidian default)
                value: canvas-card-menu-center
            -   label: left
                value: canvas-card-menu-left
            -   label: right
                value: canvas-card-menu-right
    - 
        id: animation
        title: Animation
        title.zh: 动效
        type: heading
        collapsed: true
        level: 3
    - 
        id: extra-anim-remove
        title: Remove additional added animation
        title.zh: 移除 额外增加的动效
        type: class-toggle
    - 
        id: anim-speed
        title: Animation speed
        title.zh: 动效速度
        type: variable-number-slider
        default: 1
        min: 0.5
        max: 2
        step: 0.05
    - 
        id: Miscellaneous
        title: Miscellaneous
        title.zh: 杂项
        type: heading
        collapsed: true
        level: 3
    - 
        id: setting-item-title-icon-remove
        title: Remove setting item title icon
        title.zh: 移除 设置项标题图标
        type: class-toggle
    - 
        id: scrollbar-hide
        title: Hide scrollbar
        title.zh: 隐藏 滚动条
        type: class-toggle
    - 
        id: restored-scrollbars
        title: Restore scrollbar style
        title.zh: 还原 滚动条的样式
        type: class-toggle

*/

/* @settings

name: Appearance (light mode)
id: Appearance-light
settings:
    - 
        id: Presets-info-light
        description: "[Presets (light)](https://github.com/Akifyss/obsidian-border/blob/main/presets.md#light-mode) | Before importing, it is recommended to reset this entry to default / Remove preset↗️"
        description.zh: "[预设（亮色）](https://github.com/Akifyss/obsidian-border/blob/main/presets.md#light-mode) | 导入前，推荐先将此条目重设为默认 / 移除预设↗️ "
        type: info-text
        markdown: true
    - 
        id: card-layout-open-light
        title: Enable card layout(light mode)
        title.zh: 启用 卡片式布局（亮色模式）
        type: class-toggle
    - 
        id: theme-light-style-select
        title: Base style (light mode)
        title.zh: 基础风格（亮色模式）
        type: class-select
        allowEmpty: false
        default: theme-light-background-default
        options:
            -   label: default
                value: theme-light-background-default
            -   label: old default
                value: theme-light-background-old-default
            -   label: colored
                value: theme-light-background-adapt
    - 
        id: Color-light
        title: Color
        title.zh: 色彩
        type: heading
        collapsed: true
        level: 3
    - 
        id: Accent-color-light
        title: Accent color
        title.zh: 主题色
        type: heading
        collapsed: true
        level: 4
    - 
        id: accent-light
        title: Accent color(light mode)
        title.zh: 主题色（亮色模式）
        description: Set accent color for the light and dark modes separately
        description.zh: 分开来设置亮色模式与暗色模式的强调色
        type: variable-color
        format: hsl-split
        opacity: false
        default: '#5a6ded'
    - 
        id: accent-color-override-light
        title: Override accent color setting in "Settings-Appearance" menu(light mode)
        title.zh: 覆写 "设置-外观 "菜单中的强调色设置（亮色模式）
        type: class-toggle
    - 
        id: accent-advanced-light
        title: Advanced
        title.zh: 高级
        type: heading
        collapsed: true
        level: 5
    - 
		id: color-accent-hsl-light
		title: color-accent-hsl
        title.zh: color-accent-hsl
        type: variable-color
		format: hsl-values
        opacity: false
        default: '#'
    - 
		id: color-accent-light
		title: color-accent
        title.zh: color-accent
        type: variable-color
		format: hsl
        opacity: false
        default: '#'
    - 
		id: color-accent-1-light
		title: color-accent-1
        title.zh: color-accent-1
        type: variable-color
		format: hsl
        opacity: false
        default: '#'
    - 
		id: color-accent-2-light
		title: color-accent-2
        title.zh: color-accent-2
        type: variable-color
		format: hsl
        opacity: false
        default: '#'
    - 
		id: color-accent-3-light
		title: color-accent-3
        title.zh: color-accent-3
        type: variable-color
		format: hsl
        opacity: false
        default: '#'
    - 
        id: Extended-colors-light
        title: Extended colors
        title.zh: 拓展色
        type: heading
        collapsed: true
        level: 4
	- 
		id: color-red-rgb
		title: Red
		type: variable-themed-color
        format: 'rgb-values'
		default-light: '#dd2c38'
		default-dark: '#ff7881'
	- 
		id: color-orange-rgb
		title: Orange
		type: variable-themed-color
        format: 'rgb-values'
		default-light: '#de7417'
		default-dark: '#fbbb83'
	- 
		id: color-yellow-rgb
		title: Yellow
		type: variable-themed-color
        format: 'rgb-values'
		default-light: '#c09c0c'
		default-dark: '#ffe88b'
	- 
		id: color-green-rgb
		title: Green
		type: variable-themed-color
        format: 'rgb-values'
		default-light: '#1da51d'
		default-dark: '#7cd37c'
	- 
		id: color-cyan-rgb
		title: Cyan
		type: variable-themed-color
        format: 'rgb-values'
		default-light: '#16a6ab'
		default-dark: '#86dfe2'
	- 
		id: color-blue-rgb
		title: Blue
		type: variable-themed-color
        format: 'rgb-values'
		default-light: '#1775d9'
		default-dark: '#89bdf4'
	- 
		id: color-purple-rgb
		title: Purple
		type: variable-themed-color
        format: 'rgb-values'
		default-light: '#8f47e1'
		default-dark: '#cb9eff'
	- 
		id: color-pink-rgb
		title: Pink
		type: variable-themed-color
        format: 'rgb-values'
		default-light: '#dd1399'
		default-dark: '#f2b6de'
    - 
        id: Background-light
        title: Background
        title.zh: 背景
        type: heading
        collapsed: true
        level: 3
    - 
        id: mod-left-split-background-header-light
        title: Left sidepane background
        title.zh: 左侧面板背景
        type: heading
        collapsed: ture
        level: 4
    - 
        id: mod-left-split-background-select-light
        title: Left sidepane background(light mode)
        title.zh: 左侧面板背景（亮色模式）
        type: class-select
        allowEmpty: false
        default: mod-left-split-background-primary-light
        options:
            -   label: background-primary (default)
                value: mod-left-split-background-primary-light
            -   label: background-secondary
                value: mod-left-split-background-secondary-light
            -   label: transparent (card layout only)
                value: mod-left-split-background-transparent-light
            -   label: customize color
                value: mod-left-split-background-customize-light
            -   label: customize CSS
                value: mod-left-split-background-CSS-light
    - 
		id: background-mod-left-split-light
		title: Customize left sidepane background color (light mode)
        title.zh: 自定义 左侧面板 背景色（亮色模式）
		type: variable-color
        opacity: ture
        format: hex
        alt-format:
            -
                id: accent-rgb
                format: rgb
        default: '#'
    - 
        id: background-mod-left-CSS-light
        title: Left sidepane background (light mode)
        title.zh: 左侧面板background（亮色模式）
        description: Input any CSS background value
        description.zh: 输入任意 CSS background 值
        type: variable-text
        default: ''
    - 
        id: background-mod-left-CSS-blend-mode-light
        title: Left sidepane background-blend-mode (light mode)
        title.zh: 左侧面板background-blend-mode（亮色模式）
        description: Input any CSS background-blend-mode value
        description.zh: 输入任意 CSS background-blend-mode 值
        type: variable-text
        default: normal
    - 
        id: background-mod-left-CSS-backdrop-filter-light
        title: Left sidepane backdrop-filter (light mode)
        title.zh: 左侧面板backdrop-filter（亮色模式）
        description: Input any CSS backdrop-filter value
        description.zh: 输入任意 CSS backdrop-filter 值
        type: variable-text
        default: blur(32px)
    - 
        id: mod-right-split-background-header-light
        title: Right sidepane background
        title.zh: 右侧面板背景
        type: heading
        collapsed: ture
        level: 4
    - 
        id: mod-right-split-background-select-light
        title: Right sidepane background (light mode)
        title.zh: 右侧面板背景（亮色模式）
        type: class-select
        allowEmpty: false
        default: mod-right-split-background-primary-light
        options:
            -   label: background-primary (default)
                value: mod-right-split-background-primary-light
            -   label: background-secondary
                value: mod-right-split-background-secondary-light
            -   label: transparent (card layout only)
                value: mod-right-split-background-transparent-light
            -   label: customize color
                value: mod-right-split-background-customize-light
            -   label: customize CSS
                value: mod-right-split-background-CSS-light
    - 
		id: background-mod-right-split-light
		title: Customize right sidepane background color(light mode)
        title.zh: 自定义 右侧面板 背景色（亮色模式）
		type: variable-color
        opacity: ture
        format: hex
        alt-format:
            -
                id: accent-rgb
                format: rgb
        default: '#'
    - 
        id: background-mod-right-CSS-light
        title: Right sidepane background (light mode)
        title.zh: 右侧面板background（亮色模式）
        description: Input any CSS background value
        description.zh: 输入任意 CSS background 值
        type: variable-text
        default: ''
    - 
        id: background-mod-right-CSS-blend-mode-light
        title: Right sidepane background-blend-mode (light mode)
        title.zh: 右侧面板background-blend-mode（亮色模式）
        description: Input any CSS background-blend-mode value
        description.zh: 输入任意 CSS background-blend-mode 值
        type: variable-text
        default: normal
    - 
        id: background-mod-right-CSS-backdrop-filter-light
        title: Right sidepane backdrop-filter (light mode)
        title.zh: 右侧面板backdrop-filter（亮色模式）
        description: Input any CSS backdrop-filter value
        description.zh: 输入任意 CSS backdrop-filter 值
        type: variable-text
        default: blur(32px)
    - 
        id: mod-root-split-background-header-light
        title: Middle pane background
        title.zh: 中间面板背景
        type: heading
        collapsed: ture
        level: 4
    - 
        id: mod-root-split-background-select-light
        title: Middle pane background (light mode)
        title.zh: 中间面板背景（亮色模式）
        type: class-select
        allowEmpty: false
        default: mod-root-split-background-primary-light
        options:
            -   label: background-primary (default)
                value: mod-root-split-background-primary-light
            -   label: background-secondary
                value: mod-root-split-background-secondary-light
            -   label: transparent (card layout only)
                value: mod-root-split-background-transparent-light
            -   label: customize color
                value: mod-root-split-background-customize-light
            -   label: customize CSS
                value: mod-root-split-background-CSS-light
    - 
		id: background-mod-root-split-light
		title: Customize Middle pane background color(light mode)
        title.zh: 自定义 中间面板 背景色（亮色模式）
		type: variable-color
        opacity: ture
        format: hex
        alt-format:
            -
                id: accent-rgb
                format: rgb
        default: '#'
    - 
        id: background-mod-root-CSS-light
        title: Middle pane background (light mode)
        title.zh: 中间面板background（亮色模式）
        description: Input any CSS background value
        description.zh: 输入任意 CSS background 值
        type: variable-text
        default: ''
    - 
        id: background-mod-root-CSS-blend-mode-light
        title: Middle pane background-blend-mode (light mode)
        title.zh: 中间面板background-blend-mode（亮色模式）
        description: Input any CSS background-blend-mode value
        description.zh: 输入任意 CSS background-blend-mode 值
        type: variable-text
        default: normal
    - 
        id: background-mod-root-CSS-backdrop-filter-light
        title: Middle pane backdrop-filter (light mode)
        title.zh: 中间面板backdrop-filter（亮色模式）
        description: Input any CSS backdrop-filter value
        description.zh: 输入任意 CSS backdrop-filter 值
        type: variable-text
        default: blur(32px)
    - 
        id: underlying-background-header-light
        title: Underlying background
        title.zh: 底层背景
        type: heading
        collapsed: ture
        level: 4
    - 
        id: background-underlying-select-light
        title: underlying background(light mode)
        title.zh: 底层背景（亮色模式）
        type: class-select
        allowEmpty: false
        default: background-underlying-default-light
        options:
            -   label: background-primary
                value: background-underlying-primary-light
            -   label: background-secondary
                value: background-underlying-secondary-light
            -   label: adapt to accent color(default)
                value: background-underlying-default-light
            -   label: customize color
                value: background-underlying-Color-light
            -   label: customize CSS
                value: background-underlying-CSS-light
    - 
		id: background-underlying-light
		title: Customize underlying background color(light mode)
        title.zh: 自定义 底层背景色（亮色模式）
		type: variable-color
        opacity: ture
        format: hex
        alt-format:
            -
                id: accent-rgb
                format: rgb
        default: '#'
    - 
        id: background-underlying-CSS-light
        title: underlying background (light mode)
        title.zh: 底层background（亮色模式）
        description: Input any CSS background value
        description.zh: 输入任意 CSS background 值
        type: variable-text
        default: ''
    - 
        id: background-underlying-CSS-blend-mode-light
        title: underlying background-blend-mode (light mode)
        title.zh: 底层background-blend-mode（亮色模式）
        description: Input any CSS background-blend-mode value
        description.zh: 输入任意 CSS background-blend-mode 值
        type: variable-text
        default: overlay, color-burn
    - 
        id: underlying-foreground-header-light
        title: Underlying foreground
        title.zh: 底层前景
        description: Adjust foreground to fit the background
        description.zh: 调整前景以适应背景
        type: heading
        collapsed: ture
        level: 5
    - 
		id: on-border-light
        title: underlying foreground color(light mode)
        title.zh: 底层前景色（亮色模式）
        description: Color of elements (text&icon) displayed on underlying background, including top tab bar, ribbon menu, and status bar
        description.zh: 显示在底层背景上的元素(text&icon)颜色，包括顶部标签栏、功能区菜单和状态栏
		type: variable-color
        opacity: ture
        format: hex
        alt-format:
            -
                id: accent-rgb
                format: rgb
        default: '#'
    - 
        id: mix-blend-mode-on-border-light
        title: mix blend mode of underlying foreground(light mode)
        title.zh: 底层前景的混合模式（亮色模式）
        description: Input any CSS mix-blend-mode value
        description.zh: 输入任意 CSS mix-blend-mode 值
        type: variable-text
        default: normal
    - 
        id: Global-background-header-light
        title: Global background
        title.zh: 全局背景
        type: heading
        collapsed: ture
        level: 4
    - 
		id: background-primary
		title: background-primary(light mode)
        title.zh: background-primary（亮色模式）
		type: variable-themed-color
		opacity: true
		format: hex
		default-light: '#'
		default-dark: '#'
    - 
		id: background-primary-alt
		title: background-primary-alt(light mode)
        title.zh: background-primary-alt（亮色模式）
		type: variable-themed-color
		opacity: true
		format: hex
		default-light: '#'
		default-dark: '#'
    - 
		id: background-secondary
		title: background-secondary(light mode)
        title.zh: background-secondary（亮色模式）
		type: variable-themed-color
		opacity: true
		format: hex
		default-light: '#'
		default-dark: '#'
    - 
		id: background-secondary-alt
		title: background-secondary-alt(light mode)
        title.zh: background-secondary-alt（亮色模式）
		type: variable-themed-color
		opacity: true
		format: hex
		default-light: '#'
		default-dark: '#'
    - 
		id: background-tertiary
		title: background-tertiary(light mode)
        title.zh: background-tertiary（亮色模式）
		type: variable-themed-color
		opacity: true
		format: hex
		default-light: '#'
		default-dark: '#'
    - 
		id: workspace-background-translucent
		title: workspace-background-translucent(light mode)
        title.zh: workspace-background-translucent（亮色模式）
		type: variable-themed-color
		opacity: true
		format: hsl
		default-light: '#'
		default-dark: '#'
    - 
		id: background-modifier-hover
		title: background-modifier-hover(light mode)
        title.zh: background-modifier-hover（亮色模式）
		type: variable-themed-color
		opacity: true
		format: hex
		default-light: '#'
		default-dark: '#'
    - 
		id: background-modifier-active-hover
		title: background-modifier-active-hover(light mode)
        title.zh: background-modifier-active-hover（亮色模式）
		type: variable-themed-color
		opacity: true
		format: hex
		default-light: '#'
		default-dark: '#'
    - 
		id: background-modifier-border
		title: background-modifier-border(light mode)
        title.zh: background-modifier-border（亮色模式）
		type: variable-themed-color
		opacity: true
		format: hex
		default-light: '#'
		default-dark: '#'
    - 
		id: background-modifier-border-hover
		title: background-modifier-border-hover(light mode)
        title.zh: background-modifier-border-hover（亮色模式）
		type: variable-themed-color
		opacity: true
		format: hex
		default-light: '#'
		default-dark: '#'
    - 
		id: background-modifier-border-focus
		title: background-modifier-border-focus(light mode)
        title.zh: background-modifier-border-focus（亮色模式）
		type: variable-themed-color
		opacity: true
		format: hex
		default-light: '#'
		default-dark: '#'
    - 
        id: Foreground-header-light
        title: Foreground
        title.zh: 前景
        type: heading
        collapsed: ture
        level: 3
    - 
        id: text-color-light
        title: Text & icon color
        title.zh: 文本 & 图标色
        type: heading
        collapsed: ture
        level: 4
    - 
        id: text-normal
		title: text-normal(light mode)
        title.zh: text-normal（亮色模式）
		type: variable-themed-color
		format: hex
		default-light: '#'
		default-dark: '#'
    - 
        id: text-muted
		title: text-muted(light mode)
        title.zh: text-muted（亮色模式）
		type: variable-themed-color
		format: hex
		default-light: '#'
		default-dark: '#'
    - 
        id: text-faint
		title: text-faint(light mode)
        title.zh: text-faint（亮色模式）
		type: variable-themed-color
		format: hex
		default-light: '#'
		default-dark: '#'
    - 
        id: text-accent
		title: text-accent(light mode)
        title.zh: text-accent（亮色模式）
		type: variable-themed-color
		format: hex
		default-light: '#'
		default-dark: '#'
    - 
        id: text-accent-hover
		title: text-accent-hover(light mode)
        title.zh: text-accent-hover（亮色模式）
		type: variable-themed-color
		format: hex
		default-light: '#'
		default-dark: '#'
    - 
        id: text-on-accent
		title: text-on-accent(light mode)
        title.zh: text-on-accent（亮色模式）
		type: variable-themed-color
		format: hex
		default-light: '#'
		default-dark: '#'
    - 
        id: text-on-accent-inverted
		title: text-on-accent-inverted(light mode)
        title.zh: text-on-accent-inverted（亮色模式）
		type: variable-themed-color
		format: hex
		default-light: '#'
		default-dark: '#'
    - 
        id: interactive-color-light
        title: Interactive color
        title.zh: 交互色
        type: heading
        collapsed: ture
        level: 4
    - 
        id: interactive-normal
        title: interactive-normal(light mode)
        title.zh: interactive-normal（亮色模式）
		type: variable-themed-color
		format: hex
		default-light: '#'
		default-dark: '#'
    - 
        id: interactive-hover
        title: interactive-hover(light mode)
        title.zh: interactive-hover（亮色模式）
		type: variable-themed-color
		format: hex
		default-light: '#'
		default-dark: '#'
    - 
        id: interactive-accent
		title: interactive-accent(light mode)
		title.zh: interactive-accent（亮色模式）
		type: variable-themed-color
		format: hex
		default-light: '#'
		default-dark: '#'
    - 
        id: interactive-accent-hsl
		title: interactive-accent-hsl(light mode)
		title.zh: interactive-accent-hsl（亮色模式）
		type: variable-themed-color
		format: hsl-values
		default-light: '#'
		default-dark: '#'
    - 
        id: interactive-accent-hover
		title: interactive-accent-hover(light mode)
		title.zh: interactive-accent-hover（亮色模式）
		type: variable-themed-color
		format: hex
		default-light: '#'
		default-dark: '#'
    - 
        id: Card-light
        title: Card
        title.zh: 卡片
        type: heading
        collapsed: true
        level: 3
    - 
        id: card-border-radius-light
        title: Custom card border radius (light mode)
        title.zh: 自定义 卡片的圆角大小（亮色模式）
        description: Input your border-radius value here
        description.zh: 在这里输入你的 border-radius 值
        type: variable-text
        default: 8px
    - 
        id: card-shadow-light
        title: Custom card shadow (light mode)
        title.zh: 自定义 卡片阴影（亮色模式）
        description: Input your box-shadow value here
        description.zh: 在这里输入你的 box-shadow 值
        type: variable-text
        default: ''
    - 
        id: Active-states-light
        title: Active states
        title.zh: 激活态
        type: heading
        collapsed: true
        level: 3
    - 
        id: Active-states-Tabs-light
        title: Active states(Tabs)
        title.zh: 激活态（Tabs）
        type: heading
        collapsed: true
        level: 4
    - 
		id: color-activated-tab-header-light
		title: activated Tab header color(light mode)
        title.zh: 已激活Tab的标头颜色（亮色模式）
		type: variable-color
        opacity: ture
        format: hex
        alt-format:
            -
                id: accent-rgb
                format: rgb
        default: '#'
    - 
        id: color-to-tab-icon-light
        title: Apply custom color to the "close" icon(light mode)
        title.zh: 将自定义颜色应用于“关闭”图标（亮色模式）
        type: class-toggle
    - 
        id: border-radius-activated-tab-header-light
        title: activated Tab header border radius(light mode)
        title.zh: 已激活Tab的标头圆角大小（亮色模式）
        description: Input any CSS border-radius value
        description.zh: 输入任意 CSS border-radius 值
        type: variable-text
        default: 6px
    - 
        id: background-activated-tab-header-light
        title: activated Tab header background(light mode)
        title.zh: 已激活Tab的标头背景（亮色模式）
        description: Input any CSS background value
        description.zh: 输入任意 CSS background 值
        type: variable-text
        default: ''
    - 
        id: shadow-activated-tab-header-light
        title: activated Tab header shadow(light mode)
        title.zh: 已激活Tab的标头阴影（亮色模式）
        description: Input any CSS box-shadow value
        description.zh: 输入任意 CSS box-shadow 值
        type: variable-text
        default: ''
    - 
		id: color-activated-tab-header-underline-light
		title: activated Tab header underline color(light mode)
        title.zh: 已激活Tab的标头下划线颜色（亮色模式）
        description: Input any CSS background value
        description.zh: 输入任意 CSS background 值
        type: variable-text
        default: ''
    - 
        id: Active-states-file-explorer-light
        title: Active states(file explorer)
        title.zh: 激活态（文件列表）
        type: heading
        collapsed: true
        level: 4
    - 
        id: Active-states-file-explorer-select-light
        title: Activated file style(light mode)
        title.zh: 已激活文件 风格（亮色模式）
        type: class-select
        allowEmpty: false
        default: activated-file-default-light
        options:
            -   label: default
                value: activated-file-default-light
            -   label: Accent color
                value: activated-file-accent-light
            -   label: Consistent with tab style
                value: activated-file-tab-style-light
            -   label: customize
                value: activated-file-customize-light
    - 
		id: color-activated-file-light
		title: Activated file color(light mode)
        title.zh: 已激活文件的颜色（亮色模式）
		type: variable-color
        opacity: ture
        format: hex
        alt-format:
            -
                id: accent-rgb
                format: rgb
        default: '#'
    - 
        id: border-radius-activated-file-light
        title: Activated file border radius(light mode)
        title.zh: 已激活文件的圆角大小（亮色模式）
        description: Input any CSS border-radius value
        description.zh: 输入任意 CSS border-radius 值
        type: variable-text
        default: 4px
    - 
        id: background-activated-file-light
        title: Activated file background(light mode)
        title.zh: 已激活文件的背景（亮色模式）
        description: Input any CSS background value
        description.zh: 输入任意 CSS background 值
        type: variable-text
        default: ''
    - 
        id: shadow-activated-file-light
        title: Activated file shadow(light mode)
        title.zh: 已激活文件的阴影（亮色模式）
        description: Input any CSS box-shadow value
        description.zh: 输入任意 CSS box-shadow 值
        type: variable-text
        default: ''
    - 
        id: UI-Controls-light
        title: UI Controls
        title.zh: UI 控件
        type: heading
        collapsed: ture
        level: 3
    - 
        id: input-radius-light
        title: input-radius(light mode)
        title.zh: input-radius（亮色模式）
        description: border radius of buttons, input, etc.
        description.zh: 按钮，输入框等的圆角大小
        type: variable-number
        default: 5
        format: px
    - 
        id: input-shadow-light
        title: input-shadow(light mode)
        title.zh: input-shadow（亮色模式）
        description: shadow of button,dropdown, etc. Accepts any CSS box-shadow value
        description.zh: 按钮，下拉菜单等的阴影 接受任意 CSS box-shadow 值
        type: variable-text
        default: ''
    - 
        id: input-shadow-hover-light
        title: input-shadow-hover(light mode)
        title.zh: input-shadow-hover（亮色模式）
        description: shadow of button,dropdown, etc.(hovered) Accepts any CSS box-shadow value
        description.zh: 按钮，下拉菜单等的阴影（鼠标悬停时） 接受任意 CSS box-shadow 值
        type: variable-text
        default: ''
    - 
        id: toggle-light
        title: toggle switch
        title.zh: 滑动开关
        type: heading
        collapsed: ture
        level: 4
    - 
        id: toggle-thumb-color-light
        title: toggle-thumb-color(light mode)
        title.zh: toggle-thumb-color（亮色模式）
        description: background color of toggle thumb Accepts any CSS background-color value
        description.zh: 开关滑块的背景色 接受任意 CSS background-color 值
        type: variable-text
        default: ''
    - 
        id: toggle-thumb-enabled-color-light
        title: toggle-thumb-enabled-color(light mode)
        title.zh: toggle-thumb-enabled-color（亮色模式）
        description: background color of toggle thumb(enabled) Accepts any CSS background-color value
        description.zh: 开关滑块的背景色（开启时） 接受任意 CSS background-color 值
        type: variable-text
        default: ''
    - 
        id: toggle-thumb-shadow-light
        title: toggle-thumb-shadow(light mode)
        title.zh: toggle-thumb-shadow（亮色模式）
        description: shadow of toggle thumb Accepts any CSS box-shadow value
        description.zh: 开关滑块的阴影 接受任意 CSS box-shadow 值
        type: variable-text
        default: ''
    - 
        id: toggle-thumb-enabled-shadow-light
        title: toggle-thumb-enabled-shadow(light mode)
        title.zh: toggle-thumb-enabled-shadow（亮色模式）
        description: shadow of toggle thumb(enabled) Accepts any CSS box-shadow value
        description.zh: 开关滑块的阴影（开启时） 接受任意 CSS box-shadow 值
        type: variable-text
        default: ''
    - 
        id: toggle-track-color-light
        title: toggle-track-color(light mode)
        title.zh: toggle-track-color（亮色模式）
        description: background color of toggle track Accepts any CSS background-color value
        description.zh: 开关滑轨的背景色 接受任意 CSS background-color 值
        type: variable-text
        default: ''
    - 
        id: toggle-track-enabled-color-light
        title: toggle-track-enabled-color(light mode)
        title.zh: toggle-track-enabled-color（亮色模式）
        description: background color of toggle track(enabled) Accepts any CSS background-color value
        description.zh: 开关滑轨的背景色（开启时） 接受任意 CSS background-color 值
        type: variable-text
        default: ''
    - 
        id: toggle-track-shadow-light
        title: toggle-track-shadow(light mode)
        title.zh: toggle-track-shadow（亮色模式）
        description: shadow of toggle track Accepts any CSS box-shadow value
        description.zh: 开关滑轨的阴影 接受任意 CSS box-shadow 值
        type: variable-text
        default: ''
    - 
        id: toggle-track-hovered-shadow-light
        title: toggle-track-hovered-shadow(light mode)
        title.zh: toggle-track-hovered-shadow（亮色模式）
        description: shadow of toggle track(hovered) Accepts any CSS box-shadow value
        description.zh: 开关滑轨的阴影（鼠标悬停时） 接受任意 CSS box-shadow 值
        type: variable-text
        default: ''
    - 
        id: Miscellaneous-light
        title: Miscellaneous
        title.zh: 杂项
        type: heading
        collapsed: true
        level: 3
    - 
        id: card-highlight-light
        title: Highlight active card
        title.zh: 高亮当前活动卡片
        description: Only applicable for card layout
        description.zh: 仅限卡片式布局
        type: class-toggle
    - 
        id: workspace-divider-transparent-light
        title: Visually remove divider effect in workspace
        title.zh: 在视觉上移除工作区的分隔线效果
        description: For default layout
        description.zh: 针对默认布局
        type: class-toggle
	- 
		id: link-external-color
		title: External link color
        title.zh: 外部链接颜色
		type: variable-themed-color
		format: hex
		default-light: '#'
		default-dark: '#'
	- 
		id: link-external-color-hover
		title: External link color (hover)
        title.zh: 外部链接颜色（鼠标悬停时）
		type: variable-themed-color
		format: hex
		default-light: '#'
		default-dark: '#'

*/

/* @settings

name: Appearance (dark mode)
id: Appearance-dark
settings:
    - 
        id: Presets-info-dark
        description: "[Presets (dark)](https://github.com/Akifyss/obsidian-border/blob/main/presets.md#light-mode) | Before importing, it is recommended to reset this entry to default / Remove preset↗️"
        description.zh: "[预设（暗色）](https://github.com/Akifyss/obsidian-border/blob/main/presets.md#dark-mode) | 导入前，推荐先将此条目重设为默认 / 移除预设↗️ "
        type: info-text
        markdown: true
    - 
        id: card-layout-open-dark
        title: Enable card layout(dark mode)
        title.zh: 启用 卡片式布局（暗色模式）
        type: class-toggle
    - 
        id: theme-dark-style-select
        title: Base style (dark mode)
        title.zh: 基础风格（暗色模式）
        type: class-select
        allowEmpty: false
        default: theme-dark-background-default
        options:
            -   label: brighter
                value: theme-dark-background-brighter
            -   label: default
                value: theme-dark-background-default
            -   label: darker
                value: theme-dark-background-darker
            -   label: pure black
                value: theme-dark-background-black
    - 
        id: Color-dark
        title: Color
        title.zh: 色彩
        type: heading
        collapsed: true
        level: 3
    - 
        id: Accent-color-dark
        title: Accent color
        title.zh: 主题色
        type: heading
        collapsed: true
        level: 4
    - 
        id: accent-dark
        title: Accent color(dark mode)
        title.zh: 主题色（暗色模式）
        description: Set accent color for the light and dark modes separately
        description.zh: 分开来设置亮色模式与暗色模式的强调色
        type: variable-color
        format: hsl-split
        opacity: false
        default: '#8a96e5'
    - 
        id: accent-color-override-dark
        title: Override accent color setting in "Settings-Appearance" menu(dark mode)
        title.zh: 覆写 "设置-外观 "菜单中的强调色设置（暗色模式）
        type: class-toggle
    - 
        id: accent-advanced-dark
        title: Advanced
        title.zh: 高级
        type: heading
        collapsed: true
        level: 5
    - 
		id: color-accent-hsl-dark
		title: color-accent-hsl
        title.zh: color-accent-hsl
        type: variable-color
		format: hsl-values
        opacity: false
        default: '#'
    - 
		id: color-accent-dark
		title: color-accent
        title.zh: color-accent
        type: variable-color
		format: hsl
        opacity: false
        default: '#'
    - 
		id: color-accent-1-dark
		title: color-accent-1
        title.zh: color-accent-1
        type: variable-color
		format: hsl
        opacity: false
        default: '#'
    - 
		id: color-accent-2-dark
		title: color-accent-2
        title.zh: color-accent-2
        type: variable-color
		format: hsl
        opacity: false
        default: '#'
    - 
		id: color-accent-3-dark
		title: color-accent-3
        title.zh: color-accent-3
        type: variable-color
		format: hsl
        opacity: false
        default: '#'
    - 
        id: Extended-colors-dark
        title: Extended colors
        title.zh: 拓展色
        type: heading
        collapsed: true
        level: 4
	- 
		id: color-red-rgb
		title: Red
		type: variable-themed-color
        format: 'rgb-values'
		default-light: '#dd2c38'
		default-dark: '#ff7881'
	- 
		id: color-orange-rgb
		title: Orange
		type: variable-themed-color
        format: 'rgb-values'
		default-light: '#de7417'
		default-dark: '#fbbb83'
	- 
		id: color-yellow-rgb
		title: Yellow
		type: variable-themed-color
        format: 'rgb-values'
		default-light: '#c09c0c'
		default-dark: '#ffe88b'
	- 
		id: color-green-rgb
		title: Green
		type: variable-themed-color
        format: 'rgb-values'
		default-light: '#1da51d'
		default-dark: '#7cd37c'
	- 
		id: color-cyan-rgb
		title: Cyan
		type: variable-themed-color
        format: 'rgb-values'
		default-light: '#16a6ab'
		default-dark: '#86dfe2'
	- 
		id: color-blue-rgb
		title: Blue
		type: variable-themed-color
        format: 'rgb-values'
		default-light: '#1775d9'
		default-dark: '#89bdf4'
	- 
		id: color-purple-rgb
		title: Purple
		type: variable-themed-color
        format: 'rgb-values'
		default-light: '#8f47e1'
		default-dark: '#cb9eff'
	- 
		id: color-pink-rgb
		title: Pink
		type: variable-themed-color
        format: 'rgb-values'
		default-light: '#dd1399'
		default-dark: '#f2b6de'
    - 
        id: Background-dark
        title: Background
        title.zh: 背景
        type: heading
        collapsed: true
        level: 3
    - 
        id: mod-left-split-background-header-dark
        title: Left sidepane background
        title.zh: 左侧面板背景
        type: heading
        collapsed: ture
        level: 4
    - 
        id: mod-left-split-background-select-dark
        title: Left sidepane background(dark mode)
        title.zh: 左侧面板背景（暗色模式）
        type: class-select
        allowEmpty: false
        default: mod-left-split-background-primary-dark
        options:
            -   label: background-primary (default)
                value: mod-left-split-background-primary-dark
            -   label: background-secondary
                value: mod-left-split-background-secondary-dark
            -   label: transparent (card layout only)
                value: mod-left-split-background-transparent-dark
            -   label: customize color
                value: mod-left-split-background-customize-dark
            -   label: customize CSS
                value: mod-left-split-background-CSS-dark
    - 
		id: background-mod-left-split-dark
		title: Customize left sidepane background color (dark mode)
        title.zh: 自定义 左侧面板 背景色（暗色模式）
		type: variable-color
        opacity: ture
        format: hex
        alt-format:
            -
                id: accent-rgb
                format: rgb
        default: '#'
    - 
        id: background-mod-left-CSS-dark
        title: Left sidepane background (dark mode)
        title.zh: 左侧面板background（暗色模式）
        description: Input any CSS background value
        description.zh: 输入任意 CSS background 值
        type: variable-text
        default: ''
    - 
        id: background-mod-left-CSS-blend-mode-dark
        title: Left sidepane background-blend-mode (dark mode)
        title.zh: 左侧面板background-blend-mode（暗色模式）
        description: Input any CSS background-blend-mode value
        description.zh: 输入任意 CSS background-blend-mode 值
        type: variable-text
        default: normal
    - 
        id: background-mod-left-CSS-backdrop-filter-dark
        title: Left sidepane backdrop-filter (dark mode)
        title.zh: 左侧面板backdrop-filter（暗色模式）
        description: Input any CSS backdrop-filter value
        description.zh: 输入任意 CSS backdrop-filter 值
        type: variable-text
        default: blur(32px)
    - 
        id: mod-right-split-background-header-dark
        title: Right sidepane background
        title.zh: 右侧面板背景
        type: heading
        collapsed: ture
        level: 4
    - 
        id: mod-right-split-background-select-dark
        title: Right sidepane background(dark mode)
        title.zh: 右侧面板背景（暗色模式）
        type: class-select
        allowEmpty: false
        default: mod-right-split-background-primary-dark
        options:
            -   label: background-primary (default)
                value: mod-right-split-background-primary-dark
            -   label: background-secondary
                value: mod-right-split-background-secondary-dark
            -   label: transparent (card layout only)
                value: mod-right-split-background-transparent-dark
            -   label: customize color
                value: mod-right-split-background-customize-dark
            -   label: customize CSS
                value: mod-right-split-background-CSS-dark
    - 
		id: background-mod-right-split-dark
		title: Customize right sidepane background color(dark mode)
        title.zh: 自定义 右侧面板 背景色（暗色模式）
		type: variable-color
        opacity: ture
        format: hex
        alt-format:
            -
                id: accent-rgb
                format: rgb
        default: '#'
    - 
        id: background-mod-right-CSS-dark
        title: Right sidepane background (dark mode)
        title.zh: 右侧面板background（暗色模式）
        description: Input any CSS background value
        description.zh: 输入任意 CSS background 值
        type: variable-text
        default: ''
    - 
        id: background-mod-right-CSS-blend-mode-dark
        title: Right sidepane background-blend-mode (dark mode)
        title.zh: 右侧面板background-blend-mode（暗色模式）
        description: Input any CSS background-blend-mode value
        description.zh: 输入任意 CSS background-blend-mode 值
        type: variable-text
        default: normal
    - 
        id: background-mod-right-CSS-backdrop-filter-dark
        title: Right sidepane backdrop-filter (dark mode)
        title.zh: 右侧面板backdrop-filter（暗色模式）
        description: Input any CSS backdrop-filter value
        description.zh: 输入任意 CSS backdrop-filter 值
        type: variable-text
        default: blur(32px)
    - 
        id: mod-root-split-background-header-dark
        title: Middle pane background
        title.zh: 中间面板背景
        type: heading
        collapsed: ture
        level: 4
    - 
        id: mod-root-split-background-select-dark
        title: Middle pane background (dark mode)
        title.zh: 中间面板背景（暗色模式）
        type: class-select
        allowEmpty: false
        default: mod-root-split-background-primary-dark
        options:
            -   label: background-primary (default)
                value: mod-root-split-background-primary-dark
            -   label: background-secondary
                value: mod-root-split-background-secondary-dark
            -   label: transparent (card layout only)
                value: mod-root-split-background-transparent-dark
            -   label: customize color
                value: mod-root-split-background-customize-dark
            -   label: customize CSS
                value: mod-root-split-background-CSS-dark
    - 
		id: background-mod-root-split-dark
		title: Customize Middle pane background color (dark mode)
        title.zh: 自定义 中间面板 背景色（暗色模式）
		type: variable-color
        opacity: ture
        format: hex
        alt-format:
            -
                id: accent-rgb
                format: rgb
        default: '#'
    - 
        id: background-mod-root-CSS-dark
        title: Middle pane background (dark mode)
        title.zh: 中间面板background（暗色模式）
        description: Input any CSS background value
        description.zh: 输入任意 CSS background 值
        type: variable-text
        default: ''
    - 
        id: background-mod-root-CSS-blend-mode-dark
        title: Middle pane background-blend-mode (dark mode)
        title.zh: 中间面板background-blend-mode（暗色模式）
        description: Input any CSS background-blend-mode value
        description.zh: 输入任意 CSS background-blend-mode 值
        type: variable-text
        default: normal
    - 
        id: background-mod-root-CSS-backdrop-filter-dark
        title: Middle pane backdrop-filter (dark mode)
        title.zh: 中间面板backdrop-filter（暗色模式）
        description: Input any CSS backdrop-filter value
        description.zh: 输入任意 CSS backdrop-filter 值
        type: variable-text
        default: blur(32px)
    - 
        id: underlying-background-header-dark
        title: Underlying background
        title.zh: 底层背景
        type: heading
        collapsed: ture
        level: 4
    - 
        id: background-underlying-select-dark
        title: underlying background(dark mode)
        title.zh: 底层背景（暗色模式）
        type: class-select
        allowEmpty: false
        default: background-underlying-default-dark
        options:
            -   label: background-primary
                value: background-underlying-primary-dark
            -   label: background-secondary
                value: background-underlying-secondary-dark
            -   label: adapt to accent color(default)
                value: background-underlying-default-dark
            -   label: customize color
                value: background-underlying-Color-dark
            -   label: customize CSS
                value: background-underlying-CSS-dark
    - 
		id: background-underlying-dark
		title: Customize underlying background color(dark mode)
        title.zh: 自定义 底层背景色（暗色模式）
		type: variable-color
        opacity: ture
        format: hex
        alt-format:
            -
                id: accent-rgb
                format: rgb
        default: '#'
    - 
        id: background-underlying-CSS-dark
        title: underlying background (dark mode)
        title.zh: 底层background（暗色模式）
        description: Input any CSS background value
        description.zh: 输入任意 CSS background 值
        type: variable-text
        default: ''
    - 
        id: background-underlying-CSS-blend-mode-dark
        title: underlying background-blend-mode (dark mode)
        title.zh: 底层background-blend-mode（暗色模式）
        description: Input any CSS background-blend-mode value
        description.zh: 输入任意 CSS background-blend-mode 值
        type: variable-text
        default: soft-light, luminosity, hue, luminosity, hard-light
    - 
        id: underlying-foreground-header-dark
        title: Underlying foreground
        title.zh: 底层前景
        description: Adjust foreground to fit the background
        description.zh: 调整前景以适应背景
        type: heading
        collapsed: ture
        level: 5
    - 
		id: on-border-dark
        title: underlying foreground color(dark mode)
        title.zh: 底层前景色（暗色模式）
        description: Color of elements (text&icon) displayed on underlying background, including top tab bar, ribbon menu, and status bar
        description.zh: 显示在底层背景上的元素(text&icon)颜色，包括顶部标签栏、功能区菜单和状态栏
		type: variable-color
        opacity: ture
        format: hex
        alt-format:
            -
                id: accent-rgb
                format: rgb
        default: '#'
    - 
        id: mix-blend-mode-on-border-dark
        title: mix blend mode of underlying foreground(dark mode)
        title.zh: 底层前景的混合模式（暗色模式）
        description: Input any CSS mix-blend-mode value
        description.zh: 输入任意 CSS mix-blend-mode 值
        type: variable-text
        default: screen
    - 
        id: Global-background-header-dark
        title: Global background
        title.zh: 全局背景
        type: heading
        collapsed: ture
        level: 4
    - 
		id: background-primary
		title: background-primary(dark mode)
        title.zh: background-primary（暗色模式）
		type: variable-themed-color
		opacity: true
		format: hex
		default-light: '#'
		default-dark: '#'
    - 
		id: background-primary-alt
		title: background-primary-alt(dark mode)
        title.zh: background-primary-alt（暗色模式）
		type: variable-themed-color
		opacity: true
		format: hex
		default-light: '#'
		default-dark: '#'
    - 
		id: background-secondary
		title: background-secondary(dark mode)
        title.zh: background-secondary（暗色模式）
		type: variable-themed-color
		opacity: true
		format: hex
		default-light: '#'
		default-dark: '#'
    - 
		id: background-secondary-alt
		title: background-secondary-alt(dark mode)
        title.zh: background-secondary-alt（暗色模式）
		type: variable-themed-color
		opacity: true
		format: hex
		default-light: '#'
		default-dark: '#'
    - 
		id: background-tertiary
		title: background-tertiary(dark mode)
        title.zh: background-tertiary（暗色模式）
		type: variable-themed-color
		opacity: true
		format: hex
		default-light: '#'
		default-dark: '#'
    - 
		id: workspace-background-translucent
		title: workspace-background-translucent(dark mode)
        title.zh: workspace-background-translucent（暗色模式）
		type: variable-themed-color
		opacity: true
		format: hsl
		default-light: '#'
		default-dark: '#'
    - 
		id: background-modifier-hover
		title: background-modifier-hover(dark mode)
        title.zh: background-modifier-hover（暗色模式）
		type: variable-themed-color
		opacity: true
		format: hex
		default-light: '#'
		default-dark: '#'
    - 
		id: background-modifier-active-hover
		title: background-modifier-active-hover(dark mode)
        title.zh: background-modifier-active-hover（暗色模式）
		type: variable-themed-color
		opacity: true
		format: hex
		default-light: '#'
		default-dark: '#'
    - 
		id: background-modifier-border
		title: background-modifier-border(dark mode)
        title.zh: background-modifier-border（暗色模式）
		type: variable-themed-color
		opacity: true
		format: hex
		default-light: '#'
		default-dark: '#'
    - 
		id: background-modifier-border-hover
		title: background-modifier-border-hover(dark mode)
        title.zh: background-modifier-border-hover（暗色模式）
		type: variable-themed-color
		opacity: true
		format: hex
		default-light: '#'
		default-dark: '#'
    - 
		id: background-modifier-border-focus
		title: background-modifier-border-focus(dark mode)
        title.zh: background-modifier-border-focus（暗色模式）
		type: variable-themed-color
		opacity: true
		format: hex
		default-light: '#'
		default-dark: '#'
    - 
        id: Foreground-header-dark
        title: Foreground
        title.zh: 前景
        type: heading
        collapsed: ture
        level: 3
    - 
        id: text-color-dark
        title: Text & icon color
        title.zh: 文本 & 图标色
        type: heading
        collapsed: ture
        level: 4
    - 
        id: text-normal
		title: text-normal(dark mode)
        title.zh: text-normal（暗色模式）
		type: variable-themed-color
		format: hex
		default-light: '#'
		default-dark: '#'
    - 
        id: text-muted
		title: text-muted(dark mode)
        title.zh: text-muted（暗色模式）
		type: variable-themed-color
		format: hex
		default-light: '#'
		default-dark: '#'
    - 
        id: text-faint
		title: text-faint(dark mode)
        title.zh: text-faint（暗色模式）
		type: variable-themed-color
		format: hex
		default-light: '#'
		default-dark: '#'
    - 
        id: text-accent
		title: text-accent(dark mode)
        title.zh: text-accent（暗色模式）
		type: variable-themed-color
		format: hex
		default-light: '#'
		default-dark: '#'
    - 
        id: text-accent-hover
		title: text-accent-hover(dark mode)
        title.zh: text-accent-hover（暗色模式）
		type: variable-themed-color
		format: hex
		default-light: '#'
		default-dark: '#'
    - 
        id: text-on-accent
		title: text-on-accent(dark mode)
        title.zh: text-on-accent（暗色模式）
		type: variable-themed-color
		format: hex
		default-light: '#'
		default-dark: '#'
    - 
        id: text-on-accent-inverted
		title: text-on-accent-inverted(dark mode)
        title.zh: text-on-accent-inverted（暗色模式）
		type: variable-themed-color
		format: hex
		default-light: '#'
		default-dark: '#'
    - 
        id: interactive-color-dark
        title: Interactive color
        title.zh: 交互色
        type: heading
        collapsed: ture
        level: 4
    - 
        id: interactive-normal
        title: interactive-normal(dark mode)
        title.zh: interactive-normal（暗色模式）
		type: variable-themed-color
		format: hex
		default-light: '#'
		default-dark: '#'
    - 
        id: interactive-hover
        title: interactive-hover(dark mode)
        title.zh: interactive-hover（暗色模式）
		type: variable-themed-color
		format: hex
		default-light: '#'
		default-dark: '#'
    - 
        id: interactive-accent
		title: interactive-accent(dark mode)
		title.zh: interactive-accent（暗色模式）
		type: variable-themed-color
		format: hex
		default-light: '#'
		default-dark: '#'
    - 
        id: interactive-accent-hsl
		title: interactive-accent-hsl(dark mode)
		title.zh: interactive-accent-hsl（暗色模式）
		type: variable-themed-color
		format: hsl-values
		default-light: '#'
		default-dark: '#'
    - 
        id: interactive-accent-hover
		title: interactive-accent-hover(dark mode)
		title.zh: interactive-accent-hover（暗色模式）
		type: variable-themed-color
		format: hex
		default-light: '#'
		default-dark: '#'
    - 
        id: Card-dark
        title: Card
        title.zh: 卡片
        type: heading
        collapsed: true
        level: 3
    - 
        id: card-border-radius-dark
        title: Custom card border radius (dark mode)
        title.zh: 自定义 卡片的圆角大小（暗色模式）
        description: Input your border-radius value here
        description.zh: 在这里输入你的 border-radius 值
        type: variable-text
        default: 8px
    - 
        id: card-shadow-dark
        title: Custom card shadow (dark mode)
        title.zh: 自定义 卡片阴影（暗色模式）
        description: Input your box-shadow value here
        description.zh: 在这里输入你的 box-shadow 值
        type: variable-text
        default: ''
    - 
        id: Active-states-dark
        title: Active states
        title.zh: 激活态
        type: heading
        collapsed: true
        level: 3
    - 
        id: Active-states-Tabs-dark
        title: Active states(Tabs)
        title.zh: 激活态（Tabs）
        type: heading
        collapsed: true
        level: 4
    - 
		id: color-activated-tab-header-dark
		title: activated Tab header color(dark mode)
        title.zh: 已激活Tab的标头颜色（暗色模式）
		type: variable-color
        opacity: ture
        format: hex
        alt-format:
            -
                id: accent-rgb
                format: rgb
        default: '#'
    - 
        id: color-to-tab-icon-dark
        title: Apply custom color to the "close" icon(dark mode)
        title.zh: 将自定义颜色应用于“关闭”图标（暗色模式）
        type: class-toggle
    - 
        id: border-radius-activated-tab-header-dark
        title: activated Tab header border radius(dark mode)
        title.zh: 已激活Tab的标头圆角大小（暗色模式）
        description: Input any CSS border-radius value
        description.zh: 输入任意 CSS border-radius 值
        type: variable-text
        default: 6px
    - 
        id: background-activated-tab-header-dark
        title: activated Tab header background(dark mode)
        title.zh: 已激活Tab的标头背景（暗色模式）
        description: Input any CSS background value
        description.zh: 输入任意 CSS background 值
        type: variable-text
        default: ''
    - 
        id: shadow-activated-tab-header-dark
        title: activated Tab header shadow(dark mode)
        title.zh: 已激活Tab的标头阴影（暗色模式）
        description: Input any CSS box-shadow value
        description.zh: 输入任意 CSS box-shadow 值
        type: variable-text
        default: ''
    - 
		id: color-activated-tab-header-underline-dark
		title: activated Tab header underline color(dark mode)
        title.zh: 已激活Tab的标头下划线颜色（暗色模式）
        description: Input any CSS background value
        description.zh: 输入任意 CSS background 值
        type: variable-text
        default: ''
    - 
        id: Active-states-file-explorer-dark
        title: Active states(file explorer)
        title.zh: 激活态（文件列表）
        type: heading
        collapsed: true
        level: 4
    - 
        id: Active-states-file-explorer-select-dark
        title: Activated file style(dark mode)
        title.zh: 已激活文件 风格（暗色模式）
        type: class-select
        allowEmpty: false
        default: activated-file-default-dark
        options:
            -   label: default
                value: activated-file-default-dark
            -   label: Accent color
                value: activated-file-accent-dark
            -   label: Consistent with tab style
                value: activated-file-tab-style-dark
            -   label: customize
                value: activated-file-customize-dark
    - 
		id: color-activated-file-dark
		title: Activated file color(dark mode)
        title.zh: 已激活文件的颜色（暗色模式）
		type: variable-color
        opacity: ture
        format: hex
        alt-format:
            -
                id: accent-rgb
                format: rgb
        default: '#'
    - 
        id: border-radius-activated-file-dark
        title: Activated file border radius(dark mode)
        title.zh: 已激活文件的圆角大小（暗色模式）
        description: Input any CSS border-radius value
        description.zh: 输入任意 CSS border-radius 值
        type: variable-text
        default: 4px
    - 
        id: background-activated-file-dark
        title: Activated file background(dark mode)
        title.zh: 已激活文件的背景（暗色模式）
        description: Input any CSS background value
        description.zh: 输入任意 CSS background 值
        type: variable-text
        default: ''
    - 
        id: shadow-activated-file-dark
        title: Activated file shadow(dark mode)
        title.zh: 已激活文件的阴影（暗色模式）
        description: Input any CSS box-shadow value
        description.zh: 输入任意 CSS box-shadow 值
        type: variable-text
        default: ''
    - 
        id: UI-Controls-dark
        title: UI Controls
        title.zh: UI 控件
        type: heading
        collapsed: ture
        level: 3
    - 
        id: input-radius-dark
        title: input-radius(dark mode)
        title.zh: input-radius（暗色模式）
        description: border radius of button, input, etc.
        description.zh: 按钮，输入框等的圆角大小
        type: variable-number
        default: 5
        format: px
    - 
        id: input-shadow-dark
        title: input-shadow(dark mode)
        title.zh: input-shadow（暗色模式）
        description: shadow of button,dropdown, etc. Accepts any CSS box-shadow value
        description.zh: 按钮，下拉菜单等的阴影 接受任意 CSS box-shadow 值
        type: variable-text
        default: ''
    - 
        id: input-shadow-hover-dark
        title: input-shadow-hover(dark mode)
        title.zh: input-shadow-hover（暗色模式）
        description: shadow of button,dropdown, etc.(hovered) Accepts any CSS box-shadow value
        description.zh: 按钮，下拉菜单等的阴影（鼠标悬停时） 接受任意 CSS box-shadow 值
        type: variable-text
        default: ''
    - 
        id: toggle-dark
        title: toggle switch
        title.zh: 滑动开关
        type: heading
        collapsed: ture
        level: 4
    - 
        id: toggle-thumb-color-dark
        title: toggle-thumb-color(dark mode)
        title.zh: toggle-thumb-color（暗色模式）
        description: background color of toggle thumb Accepts any CSS background-color value
        description.zh: 开关滑块的背景色 接受任意 CSS background-color 值
        type: variable-text
        default: ''
    - 
        id: toggle-thumb-enabled-color-dark
        title: toggle-thumb-enabled-color(dark mode)
        title.zh: toggle-thumb-enabled-color（暗色模式）
        description: background color of toggle thumb(enabled) Accepts any CSS background-color value
        description.zh: 开关滑块的背景色（开启时） 接受任意 CSS background-color 值
        type: variable-text
        default: ''
    - 
        id: toggle-thumb-shadow-dark
        title: toggle-thumb-shadow(dark mode)
        title.zh: toggle-thumb-shadow（暗色模式）
        description: shadow of toggle thumb Accepts any CSS box-shadow value
        description.zh: 开关滑块的阴影 接受任意 CSS box-shadow 值
        type: variable-text
        default: ''
    - 
        id: toggle-thumb-enabled-shadow-dark
        title: toggle-thumb-enabled-shadow(dark mode)
        title.zh: toggle-thumb-enabled-shadow（暗色模式）
        description: shadow of toggle thumb(enabled) Accepts any CSS box-shadow value
        description.zh: 开关滑块的阴影（开启时） 接受任意 CSS box-shadow 值
        type: variable-text
        default: ''

    - 
        id: toggle-track-color-dark
        title: toggle-track-color(dark mode)
        title.zh: toggle-track-color（暗色模式）
        description: background color of toggle track Accepts any CSS background-color value
        description.zh: 开关滑轨的背景色 接受任意 CSS background-color 值
        type: variable-text
        default: ''
    - 
        id: toggle-track-enabled-color-dark
        title: toggle-track-enabled-color(dark mode)
        title.zh: toggle-track-enabled-color（暗色模式）
        description: background color of toggle track(enabled) Accepts any CSS background-color value
        description.zh: 开关滑轨的背景色（开启时） 接受任意 CSS background-color 值
        type: variable-text
        default: ''
    - 
        id: toggle-track-shadow-dark
        title: toggle-track-shadow(dark mode)
        title.zh: toggle-track-shadow（暗色模式）
        description: shadow of toggle track Accepts any CSS box-shadow value
        description.zh: 开关滑轨的阴影 接受任意 CSS box-shadow 值
        type: variable-text
        default: ''
    - 
        id: toggle-track-hovered-shadow-dark
        title: toggle-track-hovered-shadow(dark mode)
        title.zh: toggle-track-hovered-shadow（暗色模式）
        description: shadow of toggle track(hovered) Accepts any CSS box-shadow value
        description.zh: 开关滑轨的阴影（鼠标悬停时） 接受任意 CSS box-shadow 值
        type: variable-text
        default: ''
    - 
        id: Miscellaneous-dark
        title: Miscellaneous
        title.zh: 杂项
        type: heading
        collapsed: true
        level: 3
    - 
        id: card-highlight-dark
        title: Highlight active card
        title.zh: 高亮当前活动卡片
        description: Only applicable for card layout
        description.zh: 仅限卡片式布局
        type: class-toggle
    - 
        id: workspace-divider-transparent-dark
        title: Visually remove divider effect in workspace
        title.zh: 在视觉上移除工作区的分隔线效果
        description: For default layout
        description.zh: 针对默认布局
        type: class-toggle
	- 
		id: link-external-color
		title: External link color
        title.zh: 外部链接颜色
		type: variable-themed-color
		format: hex
		default-light: '#'
		default-dark: '#'
	- 
		id: link-external-color-hover
		title: External link color (hover)
        title.zh: 外部链接颜色（鼠标悬停时）
		type: variable-themed-color
		format: hex
		default-light: '#'
		default-dark: '#'

*/

/* @settings

name: Editor
id: Editor
settings:
    - 
        id: border-focus-mode-heading
        title: Focus mode
        title.zh: 专注模式
        type: heading
        collapsed: ture
        level: 3
    - 
        id: border-focus-mode-info
        description: "Highlight active line"
        description.zh: "突出显示当前活动行"
        type: info-text
        markdown: true
    - 
        id: border-focus-mode
        title: Enable focus mode
        title.zh: 启用 专注模式
        type: class-toggle
    - 
		id: line-active-bg
		title: Active line background color
        title.zh: 活动行背景色
		type: variable-themed-color
		opacity: true
		format: hex
		default-light: '#'
		default-dark: '#'
    - 
        id: line-normal-opacity
        title: Normal line opacity
        title.zh: 普通行透明度
        type: variable-number-slider
        default: 0.5
        min: 0.05
        max: 1
        step: 0.05
    - 
        id: line-emphasis
        title: Hover line indicator
        title.zh: 悬停行指示器
        type: heading
        collapsed: ture
        level: 3
    - 
        id: line-hover-indicator-info
        description: "Display a vertical line indicator before hovered line"
        description.zh: "在鼠标悬停行前显示一个竖线指示器"
        type: info-text
        markdown: true
    - 
        id: line-hover-indicator
        title: Enable hover line indicator
        title.zh: 启用 悬停行指示器
        type: class-toggle
    - 
		id: hover-indicator-color
		title: Customize indicator color
        title.zh: 自定义指示器的颜色
		type: variable-themed-color
		opacity: true
		format: hex
		default-light: '#'
		default-dark: '#'
    - 
        id: Editor-background-pattern
        title: Editor background pattern
        title.zh: 编辑器背景图案
        type: heading
        collapsed: true
        level: 3
    - 
        id: editor-grid-background-pattren
        title: Enable grid background pattern
        title.zh: 启用 网格背景图案
        type: class-toggle
    - 
		id: grid-background-pattern-color
		title: Customize grid background pattern color
        title.zh: 自定义 网格背景图案颜色
		type: variable-themed-color
		opacity: true
		format: hex
		default-light: '#'
		default-dark: '#'
    - 
        id: grid-background-pattern-size
        title: Custom grid background pattern size
        title.zh: 自定义 网格背景图案尺寸
        description: Accepts any CSS background-size value
        description.zh: 接受任何 CSS background-size 值
        type: variable-text
        default: 36px
    - 
        id: Headings
        title: Headings
        title.zh: 标题
        type: heading
        collapsed: true
        level: 3
    - 
        id: collapse-icon-restore
        title: Restore the collapse icon before headings
        title.zh: 还原标题前的折叠图标
        type: class-toggle
    - 
		id: heading-indicator-off
		title: Remove headings indicator
		title.zh: 移除标题左侧的指示器
		type: class-toggle
		default: false
    - 
		id: Inline-title
		title: Inline Title
        title.zh: 页内标题
		type: heading
		level: 4
		collapsed: true
    - 
        id: inline-title-divider-remove
        title: Remove inline title divider
        title.zh: 移除页内标题分隔线
        type: class-toggle
    - 
		id: inline-title-font
		title: inline title font
        title.zh: 页内标题 字体
		type: variable-text
		default: ''
	- 
		id: inline-title-size
		title: inline title size
        title.zh: 页内标题 字体大小
        description: Accepts any CSS font-size value
        description.zh: 接受任何 CSS font-size 值
		type: variable-text
		default: 1.5em
	- 
        id: inline-title-weight
        title: inline title weight
        title.zh: 页内标题 字重
        description: Accepts a value from 100 to 900
        description.zh: 接受 100-900 的值
        type: variable-number
        default: 700
    - 
		id: inline-title-text-transform
		title: inline title text-transform
		title.zh: 页内标题 text-transform
		description: Accepts any CSS text-transform value
		description.zh: 接受任何 CSS text-transform 值
		type: variable-text
		default: ''
    - 
        id: inline-title-color-select
        title: inline title text color
        title.zh: 页内标题 文本颜色
        type: class-select
        allowEmpty: false
        default: inline-title-color-default
        options:
            -   label: H1 color
                value: inline-title-color-default
            -   label: Customized color
                value: inline-title-color-customized
	- 
		id: inline-title-color
		title: Customize inline title text color
        title.zh: 自定义 页内标题 文本颜色
		type: variable-themed-color
		format: hex
		default-light: '#'
		default-dark: '#'
    - 
		id: level-1-headings
		title: Level 1 Headings
        title.zh: 一级标题
		type: heading
		level: 4
		collapsed: true
    - 
		id: h1-divider-on
		title: Enable H1 divider
		title.zh: 启用H1分隔线
		type: class-toggle
		default: false
	- 
		id: h1-font
		title: H1 font
        title.zh: H1 字体
		type: variable-text
		default: ''
	- 
		id: h1-size
		title: H1 font size
        title.zh: H1 字体大小
        description: Accepts any CSS font-size value
        description.zh: 接受任何 CSS font-size 值
		type: variable-text
		default: 1.5em
	- 
        id: h1-weight
        title: H1 font weight
        title.zh: H1 字重
        description: Accepts a value from 100 to 900
        description.zh: 接受 100-900 的值
        type: variable-number
        default: 700
    - 
		id: h1-text-transform
		title: H1 text-transform
		title.zh: H1 text-transform
		description: Accepts any CSS text-transform value
		description.zh: 接受任何 CSS text-transform 值
		type: variable-text
		default: ''
    - 
		id: h1-accent-color
		title: Customize H1 accent color
        title.zh: 自定义 H1 强调色
		type: variable-themed-color
		format: hex
		default-light: '#'
		default-dark: '#'
    - 
        id: h1-color-select
        title: H1 text color
        title.zh: H1 文本颜色
        type: class-select
        allowEmpty: false
        default: h1-color-default
        options:
            -   label: Default
                value: h1-color-default
            -   label: Accent color
                value: h1-color-designated
    - 
		id: level-2-headings
		title: Level 2 Headings
        title.zh: 二级标题
		type: heading
		level: 4
		collapsed: true
    - 
		id: h2-divider-on
		title: Enable H2 divider
		title.zh: 启用H2分隔线
		type: class-toggle
		default: false
	- 
		id: h2-font
		title: H2 font
        title.zh: H2 字体
		type: variable-text
		default: ''
	- 
		id: h2-size
		title: H2 font size
        title.zh: H2 字体大小
        description: Accepts any CSS font-size value
        description.zh: 接受任何 CSS font-size 值
		type: variable-text
		default: 1.425em
	- 
        id: h2-weight
        title: H2 font weight
        title.zh: H2 字重
        description: Accepts a value from 100 to 900
        description.zh: 接受 100-900 的值
        type: variable-number
        default: 675
    - 
		id: h2-text-transform
		title: H2 text-transform
		title.zh: H2 text-transform
		description: Accepts any CSS text-transform value
		description.zh: 接受任何 CSS text-transform 值
		type: variable-text
		default: ''
    - 
        id: h2-accent-color
		title: Customize H2 accent color
        title.zh: 自定义 H2 强调色
		type: variable-themed-color
		format: hex
		default-light: '#'
		default-dark: '#'
    - 
        id: h2-color-select
        title: H2 text color
        title.zh: H2 文本颜色
        type: class-select
        allowEmpty: false
        default: h2-color-default
        options:
            -   label: Default
                value: h2-color-default
            -   label: Accent color
                value: h2-color-designated
    - 
		id: level-3-headings
		title: Level 3 Headings
        title.zh: 三级标题
		type: heading
		level: 4
		collapsed: true
    - 
		id: h3-divider-on
		title: Enable H3 divider
		title.zh: 启用H3分隔线
		type: class-toggle
		default: false
	- 
		id: h3-font
		title: H3 font
        title.zh: H3 字体
		type: variable-text
		default: ''
	- 
		id: h3-size
		title: H3 font size
        title.zh: H3 字体大小
        description: Accepts any CSS font-size value
        description.zh: 接受任何 CSS font-size 值
		type: variable-text
		default: 1.35em
	- 
        id: h3-weight
        title: H3 font weight
        title.zh: H3 字重
        description: Accepts a value from 100 to 900
        description.zh: 接受 100-900 的值
        type: variable-number
        default: 650
    - 
		id: h3-text-transform
		title: H3 text-transform
		title.zh: H3 text-transform
		description: Accepts any CSS text-transform value
		description.zh: 接受任何 CSS text-transform 值
		type: variable-text
		default: ''
    - 
        id: h3-accent-color
		title: Customize H3 accent color
        title.zh: 自定义 H3 强调色
		type: variable-themed-color
		format: hex
		default-light: '#'
		default-dark: '#'
    - 
        id: h3-color-select
        title: H3 text color
        title.zh: H3 文本颜色
        type: class-select
        allowEmpty: false
        default: h3-color-default
        options:
            -   label: Default
                value: h3-color-default
            -   label: Accent color
                value: h3-color-designated
    - 
		id: level-4-headings
		title: Level 4 Headings
        title.zh: 四级标题
		type: heading
		level: 4
		collapsed: true
    - 
		id: h4-divider-on
		title: Enable H4 divider
		title.zh: 启用H4分隔线
		type: class-toggle
		default: false
	- 
		id: h4-font
		title: H4 font
        title.zh: H4 字体
		type: variable-text
		default: ''
	- 
		id: h4-size
		title: H4 font size
        title.zh: H4 字体大小
        description: Accepts any CSS font-size value
        description.zh: 接受任何 CSS font-size 值
		type: variable-text
		default: 1.275em
	- 
        id: h4-weight
        title: H4 font weight
        title.zh: H4 字重
        description: Accepts a value from 100 to 900
        description.zh: 接受 100-900 的值
        type: variable-number
        default: 625
    - 
		id: h4-text-transform
		title: H4 text-transform
    	title.zh: H4 text-transform
		description: Accepts any CSS text-transform value
		description.zh: 接受任何 CSS text-transform 值
		type: variable-text
		default: ''
    - 
        id: h4-accent-color
		title: Customize H4 accent color
        title.zh: 自定义 H4 强调色
		type: variable-themed-color
		format: hex
		default-light: '#'
		default-dark: '#'
    - 
        id: h4-color-select
        title: H4 text color
        title.zh: H4 文本颜色
        type: class-select
        allowEmpty: false
        default: h4-color-default
        options:
            -   label: Default
                value: h4-color-default
            -   label: Accent color
                value: h4-color-designated
    - 
		id: level-5-headings
		title: Level 5 Headings
        title.zh: 五级标题
		type: heading
		level: 4
		collapsed: true
    - 
		id: h5-divider-on
		title: Enable H5 divider
		title.zh: 启用H5分隔线
		type: class-toggle
		default: false
	- 
		id: h5-font
		title: H5 font
        title.zh: H5 字体
		type: variable-text
		default: ''
	- 
		id: h5-size
		title: H5 font size
        title.zh: H5 字体大小
        description: Accepts any CSS font-size value
        description.zh: 接受任何 CSS font-size 值
		type: variable-text
		default: 1.2em
	- 
        id: h5-weight
        title: H5 font weight
        title.zh: H5 字重
        description: Accepts a value from 100 to 900
        description.zh: 接受 100-900 的值
        type: variable-number
        default: 600
    - 
		id: h5-text-transform
		title: H5 text-transform
		title.zh: H5 text-transform
		description: Accepts any CSS text-transform value
		description.zh: 接受任何 CSS text-transform 值
		type: variable-text
		default: ''
    - 
        id: h5-accent-color
		title: Customize H5 accent color
        title.zh: 自定义 H5 强调色
		type: variable-themed-color
		format: hex
		default-light: '#'
		default-dark: '#'
    - 
        id: h5-color-select
        title: H5 text color
        title.zh: H5 文本颜色
        type: class-select
        allowEmpty: false
        default: h5-color-default
        options:
            -   label: Default
                value: h5-color-default
            -   label: Accent color
                value: h5-color-designated
	- 
		id: level-6-headings
		title: Level 6 Headings
        title.zh: 六级标题
		type: heading
		level: 4
		collapsed: true
    - 
		id: h6-divider-on
		title: Enable H6 divider
		title.zh: 启用H6分隔线
		type: class-toggle
		default: false
	- 
		id: h6-font
		title: H6 font
        title.zh: H6 字体
		type: variable-text
		default: ''
	- 
		id: h6-size
		title: H6 font size
        title.zh: H6 字体大小
        description: Accepts any CSS font-size value
        description.zh: 接受任何 CSS font-size 值
		type: variable-text
		default: 1.125em
	- 
        id: h6-weight
        title: H6 font weight
        title.zh: H6 字重
        description: Accepts a value from 100 to 900
        description.zh: 接受 100-900 的值
        type: variable-number
        default: 575
    - 
		id: h6-text-transform
		title: H6 text-transform
		title.zh: H6 text-transform
		description: Accepts any CSS text-transform value
		description.zh: 接受任何 CSS text-transform 值
		type: variable-text
		default: ''
    - 
        id: h6-accent-color
		title: Customize H6 accent color
        title.zh: 自定义 H6 强调色
		type: variable-themed-color
		format: hex
		default-light: '#'
		default-dark: '#'
    - 
        id: h6-color-select
        title: H6 text color
        title.zh: H6 文本颜色
        type: class-select
        allowEmpty: false
        default: h6-color-default
        options:
            -   label: Default
                value: h6-color-default
            -   label: Accent color
                value: h6-color-designated
    - 
        id: Paragraph
        title: Paragraph
        title.zh: 段落
        type: heading
        collapsed: true
        level: 3
    - 
		id: text-align-justify
		title: Justify paragraph text
		title.zh: 段落两端对齐
		type: class-toggle
		default: false
	- 
        id: line-height-customize
        title: Paragraph line height
        title.zh: 段落行高
        type: variable-number
        default: 1.5
	- 
		id: file-line-width
		title: Paragraph line width （Need to turn on “Readable line length”）
        title.zh:  段落行宽（需开启“缩减栏宽”）
		type: variable-text
		default: 700px
    - 
		id: p-spacing
		title: Paragraph spacing (Reading mode)
        title.zh:  段落间距（阅读模式）
		type: variable-text
		default: 1em
    - 
		id: p-spacing-br
		title: Paragraph spacing also takes effect after a line break (Reading mode)
		title.zh: 段间距也对换行后的内容生效（阅读模式）
		type: class-toggle
		default: false
    - 
        id: Text
        title: Text
        title.zh: 文本
        type: heading
        collapsed: true
        level: 3
    - 
        id: bold-color
		title: Bold text color
        title.zh: 粗体颜色
		type: variable-themed-color
		format: hex
		default-light: '#'
		default-dark: '#'
    - 
        id: italic-color
		title: Italic text color
        title.zh: 斜体颜色
		type: variable-themed-color
		format: hex
		default-light: '#'
		default-dark: '#'
    - 
        id: text-highlight-bg
		title: text highlight bg color
        title.zh: 文本高亮背景颜色
		type: variable-themed-color
		format: hex
		default-light: '#'
		default-dark: '#'
    - 
		id: inline-code-normal
		title: inline code text color
        title.zh: 内联代码颜色
		type: variable-themed-color
		format: hex
		default-light: '#'
		default-dark: '#'
    - 
        id: inline-code-background-light
        title: Customize inline code background (light mode)
        title.zh: 自定义 内联代码背景（亮色模式）
        description: Input any CSS background value
        description.zh: 输入任意 CSS background 值
        type: variable-text
        default: ''
    - 
        id: inline-code-background-dark
        title: Customize inline code background (dark mode)
        title.zh: 自定义 内联代码背景（暗色模式）
        description: Input any CSS background value
        description.zh: 输入任意 CSS background 值
        type: variable-text
        default: ''
    - 
        id: Links
        title: Links
        title.zh: 链接
        type: heading
        collapsed: true
        level: 3
	- 
		id: link-color
		title: Link color
        title.zh: 链接颜色
		type: variable-themed-color
		format: hex
		default-light: '#'
		default-dark: '#'
	- 
		id: link-color-hover
		title: Link color (hover)
        title.zh: 链接颜色（鼠标悬停时）
		type: variable-themed-color
		format: hex
		default-light: '#'
		default-dark: '#'
	- 
		id: link-decoration
		title: Link decoration
        title.zh: 链接装饰
        description: Accepts any CSS text-decoration-line value
        description.zh: 接受任何 CSS text-decoration-line 值
		type: variable-text
		default: underline
	- 
		id: link-decoration-hover
		title: Link decoration (hover)
        title.zh: 链接装饰（鼠标悬停时）
        description: Accepts any CSS text-decoration-line value
        description.zh: 接受任何 CSS text-decoration-line 值
		type: variable-text
		default: underline
	- 
		id: link-decoration-thickness
		title: Link decoration thickness
        title.zh: 链接装饰厚度
        description: Accepts any CSS text-decoration-thickness value
        description.zh: 接受任何 CSS text-decoration-thickness 值
		type: variable-text
		default: auto
    - 
		id: link-unresolved
		title: Unresolved link
        title.zh: 未创建链接
		type: heading
		level: 4
		collapsed: true
	- 
		id: link-unresolved-color
		title: Unresolved link color
        title.zh: 未创建链接颜色
		type: variable-themed-color
		format: hex
		default-light: '#'
		default-dark: '#'
    - 
		id: link-unresolved-opacity
		title: Unresolved link opacity
        title.zh: 未创建链接透明度
		type: variable-number-slider
		default: 0.7
		min: 0.25
		max: 1
		step: 0.05
	- 
		id: link-unresolved-filter
		title: Unresolved link filter
        title.zh: 未创建链接 filter
        description: Accepts any CSS filter value
        description.zh: 接受任何 CSS filter 值
		type: variable-text
		default: none
	- 
		id: link-unresolved-decoration-style
		title: Unresolved link decoration style
        title.zh: 未创建链接装饰风格
        description: Accepts any CSS text-decoration-style value
        description.zh: 接受任何 CSS text-decoration-style 值
		type: variable-text
		default: solid
	- 
		id: link-unresolved-decoration-color
		title: Unresolved link decoration color
        title.zh: 未创建链接装饰颜色
		type: variable-themed-color
		format: hex
		opacity:  true
		default-light: '#'
		default-dark: '#'
    - 
		id: links-external
		title: External links
        title.zh: 外部链接
		type: heading
		level: 4
		collapsed: true
	- 
		id: link-external-color
		title: External link color
        title.zh: 外部链接颜色
		type: variable-themed-color
		format: hex
		default-light: '#'
		default-dark: '#'
	- 
		id: link-external-color-hover
		title: External link color (hover)
        title.zh: 外部链接颜色（鼠标悬停时）
		type: variable-themed-color
		format: hex
		default-light: '#'
		default-dark: '#'
    - 
		id: link-external-decoration
		title: External link decoration
        title.zh: 外部链接装饰
        description: Accepts any CSS text-decoration-line value
        description.zh: 接受任何 CSS text-decoration-line 值
		type: variable-text
		default: underline
	- 
		id: link-external-decoration-hover
		title: External link decoration (hover)
        title.zh: 外部链接装饰（鼠标悬停时）
        description: Accepts any CSS text-decoration-line value
        description.zh: 接受任何 CSS text-decoration-line 值
		type: variable-text
		default: underline
	- 
		id: link-external-filter
		title: External link filter
        title.zh: 外部链接 filter
        description: Accepts any CSS filter value
        description.zh: 接受任何 CSS filter 值
		type: variable-text
		default: none
	- 
        id: Lists
        title: Lists
        title.zh: 列表
        type: heading
        collapsed: true
        level: 3
    - 
		id: list-indent
		title: list indent
        title.zh:  列表缩进
		type: variable-text
		default: 2.25em
    - 
		id: list-spacing
		title: list spacing
        title.zh:  列表间距
		type: variable-text
		default: 0.075em
    - 
		id: list-bullet-size
		title: list marker size
        title.zh: 列表标记大小
		type: variable-text
		default: 0.3em
	- 
		id: list-marker-color
		title: list marker color
        title.zh: 列表标记颜色
		type: variable-themed-color
		format: hex
		default-light: '#'
		default-dark: '#'
	- 
		id: list-marker-color-hover
		title: list marker color (hover)
        title.zh: 列表标记颜色（鼠标悬停时）
		type: variable-themed-color
		format: hex
		default-light: '#'
		default-dark: '#'
    - 
		id: list-marker-color-collapsed
		title: list marker color (collapsed)
        title.zh: 列表标记颜色（折叠时）
		type: variable-themed-color
		format: hex
		default-light: '#'
		default-dark: '#'
    - 
		id: ul-marker-restore
		title: Restore default style of unordered list
        title.zh: 恢复无序列表的默认样式
		type: class-toggle
		default: false
    - 
        id: Tags
        title: Tags
        title.zh: 标签
        type: heading
        collapsed: true
        level: 3
    - 
		id: tag-color
		title: Tag text color
        title.zh: 标签文本颜色
		type: variable-themed-color
		opacity: true
		format: hex
		default-light: '#'
		default-dark: '#'
	- 
		id: tag-background
		title: Tag background color
        title.zh: 标签背景颜色
		type: variable-themed-color
		opacity: true
		format: hex
		default-light: '#'
		default-dark: '#'
	- 
		id: tag-background-hover
		title: Tag background color (hover)
        title.zh: 标签背景颜色（鼠标悬停时）
		type: variable-themed-color
		opacity: true
		format: hex
		default-light: '#'
		default-dark: '#'
    - 
        id: Table
        title: Table
        title.zh: 表格
        type: heading
        collapsed: true
        level: 3
	- 
		id: table-header-color
		title: Customize table header color
        title.zh: 自定义 表头颜色（亮色模式）
		type: variable-themed-color
		opacity: true
		format: hex
		default-light: '#'
		default-dark: '#'
    - 
        id: table-header-background-light
        title: Customize table header background (light mode)
        title.zh: 自定义 表头背景（亮色模式）
        description: Input any CSS background value
        description.zh: 输入任意 CSS background 值
        type: variable-text
        default: ''
    - 
        id: table-header-background-dark
        title: Customize table header background (dark mode)
        title.zh: 自定义 表头背景（暗色模式）
        description: Input any CSS background value
        description.zh: 输入任意 CSS background 值
        type: variable-text
        default: ''
    - 
        id: table-width-select
        title: Table width
        title.zh: 表格宽度
        type: class-select
        allowEmpty: false
        default: table-width-default
        options:
            -   label: Consistent with line width (Default)
                value: table-width-default
            -   label: Obsidian default
                value: table-width-obsidian-default
            -   label: Customize table width
                value: table-width-customized
    - 
        id: table-width
        title: customize table width
        title.zh: 自定义表格宽度
        description: () percent of the editor panel width
        description.zh: 编辑器面板宽度的百分之（）
        type: variable-number-slider
        default: 88
        min: 10
        max: 100
        step: 1
        format: cqw
    - 
        id: Image
        title: Image
        title.zh: 图片
        type: heading
        collapsed: true
        level: 3
    - 
        id: img-center-align
        title: center-align the image
        title.zh: 图像居中显示
        type: class-toggle
    - 
        id: img-darken
        title: darken image in dark mode
        title.zh: 在暗黑模式下暗化图像
        description: Hover to restore image
        description.zh: 鼠标经过时还原
        type: class-toggle
    - 
        id: zoom-off
        title: Disable Image zoom
        title.zh: 关闭图像缩放
        description: from https://github.com/kepano/obsidian-minimal, author:@kepano
        type: class-toggle
    - 
        id: Code-heading
        title: Codeblock
        title.zh: 代码块
        type: heading
        collapsed: true
        level: 3
    - 
        id: codeblock-style-select
        title: Codeblock theme
        title.zh: 代码块主题
        type: class-select
        allowEmpty: false
        default: codeblock-style-customize
        options:
            -   label: Customize
                value: codeblock-style-customize
            -   label: Dracula
                value: codeblock-style-dracula
            -   label: Solarized light
                value: codeblock-style-solarized-light
            -   label: Solarized dark
                value: codeblock-style-solarized-dark
            -   label: One dark
                value: codeblock-style-one-dark
    - 
        id: code-background-light
        title: Customize codeblock background (light mode)
        title.zh: 自定义 代码块背景（亮色模式）
        description: Input any CSS background value
        description.zh: 输入任意 CSS background 值
        type: variable-text
        default: ''
    - 
        id: code-border-light
        title: Customize codeblock border (light mode)
        title.zh: 自定义 代码块边框（亮色模式）
        description: Input any CSS border value
        description.zh: 输入任意 CSS border 值
        type: variable-text
        default: ''
    - 
        id: code-background-dark
        title: Customize codeblock background (dark mode)
        title.zh: 自定义 代码块背景（暗色模式）
        description: Input any CSS background value
        description.zh: 输入任意 CSS background 值
        type: variable-text
        default: ''
    - 
        id: code-border-dark
        title: Customize codeblock border (dark mode)
        title.zh: 自定义 代码块边框（暗色模式）
        description: Input any CSS border value
        description.zh: 输入任意 CSS border 值
        type: variable-text
        default: ''
    - 
		id: code-normal
		title: code-normal
        title.zh: code-normal
		type: variable-themed-color
		opacity: true
		format: hex
		default-light: '#'
		default-dark: '#'
    - 
		id: code-comment
		title: code-comment
        title.zh: code-comment
		type: variable-themed-color
		opacity: true
		format: hex
		default-light: '#'
		default-dark: '#'
    - 
        id: code-function
		title: code-function
        title.zh: code-function
		type: variable-themed-color
		opacity: true
		format: hex
		default-light: '#'
		default-dark: '#'
    - 
        id: code-important
		title: code-important
        title.zh: code-important
		type: variable-themed-color
		opacity: true
		format: hex
		default-light: '#'
		default-dark: '#'
    - 
        id: code-keyword
		title: code-keyword
        title.zh: code-keyword
		type: variable-themed-color
		opacity: true
		format: hex
		default-light: '#'
		default-dark: '#'
    - 
        id: code-operator
		title: code-operator
        title.zh: code-operator
		type: variable-themed-color
		opacity: true
		format: hex
		default-light: '#'
		default-dark: '#'
    - 
        id: code-property
		title: code-property
        title.zh: code-property
		type: variable-themed-color
		opacity: true
		format: hex
		default-light: '#'
		default-dark: '#'
    - 
        id: code-punctuation
		title: code-punctuation
        title.zh: code-punctuation
		type: variable-themed-color
		opacity: true
		format: hex
		default-light: '#'
		default-dark: '#'
    - 
        id: code-string
		title: code-string
        title.zh: code-string
		type: variable-themed-color
		opacity: true
		format: hex
		default-light: '#'
		default-dark: '#'
    - 
        id: code-tag
		title: code-tag
        title.zh: code-tag
		type: variable-themed-color
		opacity: true
		format: hex
		default-light: '#'
		default-dark: '#'
    - 
        id: code-value
		title: code-value
        title.zh: code-value
		type: variable-themed-color
		opacity: true
		format: hex
		default-light: '#'
		default-dark: '#'
    - 
        id: Blockquote-heading
        title: Blockquote
        title.zh: 块引用
        type: heading
        collapsed: true
        level: 3
    - 
        id: blockquote-background-light
        title: Customize blockquote background (light mode)
        title.zh: 自定义 块引用背景（亮色模式）
        description: Input any CSS background value
        description.zh: 输入任意 CSS background 值
        type: variable-text
        default: ''
    - 
        id: blockquote-background-dark
        title: Customize blockquote background (dark mode)
        title.zh: 自定义 块引用背景（暗色模式）
        description: Input any CSS background value
        description.zh: 输入任意 CSS background 值
        type: variable-text
        default: ''
    - 
        id: Callouts
        title: Callouts
        title.zh: 标注
        type: heading
        collapsed: true
        level: 3
    - 
        id: callout-style-select
        title: callout style
        title.zh: 标注风格
        type: class-select
        allowEmpty: false
        default: callout-style-customize
        options:
            -   label: Customize
                value: callout-style-customize
            -   label: style 1
                value: callout-style-1
            -   label: style 2
                value: callout-style-2
            -   label: style 3
                value: callout-style-3
            -   label: style 4
                value: callout-style-4
    - 
		id: callout-border-width
		title: callout border width
        title.zh: 标注边框大小
        type: variable-text
        description: Input any CSS border-width value
        description.zh: 输入任意 CSS border-width 值
        default: 0px
    - 
        id: callout-border-opacity
        title: callout border opacity
        title.zh: 标注边框透明度
        type: variable-number-slider
        default: 0.25
        min: 0
        max: 1
        step: 0.05
    - 
		id: callout-padding
		title: callout padding
        title.zh: 标注边距
        description: Accepts any CSS padding value
        description.zh: 接受任何 CSS padding 值
		type: variable-text
		default: ''
    - 
        id: callout-radius
        title: callout radius
        title.zh: 标注圆角
        description: Input your border-radius value here
        description.zh: 在这里输入你的 border-radius 值
        type: variable-text
        default: ''
	- 
		id: callout-title-color
		title: callout title color
        title.zh: 标注标题颜色
		type: variable-themed-color
		opacity: true
		format: hex
		default-light: '#'
		default-dark: '#'
    - 
		id: callout-title-padding
		title: callout title padding
        title.zh: 标注标题边距
        description: Accepts any CSS padding value
        description.zh: 接受任何 CSS padding 值
		type: variable-text
		default: ''
    - 
		id: callout-title-size
		title: callout title size
        title.zh: 标注标题大小
        description: Accepts any CSS font-size value
        description.zh: 接受任何 CSS font-size 值
		type: variable-text
		default: ''
    - 
		id: callout-content-padding
		title: callout content padding
        title.zh: 标注内容边距
        description: Accepts any CSS padding value
        description.zh: 接受任何 CSS padding 值
		type: variable-text
		default: ''
    - 
		id: callout-content-background
		title: callout content background
        title.zh: 标注内容背景
		type: variable-themed-color
		opacity: true
		format: hex
		default-light: '#'
		default-dark: '#'
    - 
        id: callout-content-radius
        title: callout content radius
        title.zh: 标注内容圆角
        description: Input your border-radius value here
        description.zh: 在这里输入你的 border-radius 值
        type: variable-text
        default: ''
    - 
        id: Embeds
        title: Embeds
        title.zh: 内嵌文档
        type: heading
        collapsed: true
        level: 3
    - 
        id: seamless-embeds
        title: seamless embeds
        title.zh: 无缝嵌入
        type: class-toggle
    - 
        id: embed-background
        title: embed background
        title.zh: 内嵌文档 背景
		type: variable-themed-color
		opacity: true
		format: hex
		default-light: '#'
		default-dark: '#'
    - 
        id: embed-padding
        title: embed padding
        title.zh: 内嵌文档 padding
        description: Input any CSS padding value
        description.zh: 输入任意 CSS padding 值
        type: variable-text
        default: ''
    - 
        id: embed-border-radius
        title: embed border radius
        title.zh: 内嵌文档 圆角半径
        description: Input any CSS border-radius value
        description.zh: 输入任意 CSS border-radius 值
        type: variable-text
        default: ''
    - 
        id: embed-font-style
        title: embed font style
        title.zh: 内嵌文档 字体风格
        description: Input any CSS font-style value
        description.zh: 输入任意 CSS font-style 值
        type: variable-text
        default: ''
    - 
        id: embed-max-height
        title: embed max height
        title.zh: 内嵌文档 最大高度
        description: Input any CSS height value
        description.zh: 输入任意 CSS height 值
        type: variable-text
        default: ''
    - 
        id: Checkbox
        title: Checkbox
        title.zh: 复选框
        type: heading
        collapsed: true
        level: 3
    -
        id: disable-alternative-checkboxes
        title: Disable Alternative Checkboxes
        title.zh: 禁用备用复选框
        description: Disable this if you are using your own implementation via a CSS Snippet.
        type: class-toggle
    -
        id: checkbox-radius
        title: checkbox radius
        title.zh: 复选框 圆角半径
        description: Input any CSS border-radius value
        description.zh: 输入任意 CSS border-radius 值
        type: variable-text
        default: '6px'
    - 
		id: checkbox-marker-color
		title: checkbox marker color
        title.zh: 复选框标记颜色
		type: variable-themed-color
		opacity: true
		format: hex
		default-light: '#'
		default-dark: '#'
    - 
		id: checkbox-color
		title: checkbox color
        title.zh: 复选框颜色
		type: variable-themed-color
		opacity: true
		format: hex
		default-light: '#'
		default-dark: '#'
    - 
		id: checkbox-color-hover
		title: checkbox color (hover)
        title.zh: 复选框颜色（鼠标悬停时）
		type: variable-themed-color
		opacity: true
		format: hex
		default-light: '#'
		default-dark: '#'
    - 
		id: checkbox-border-color
		title: checkbox border color
        title.zh: 复选框边框颜色
		type: variable-themed-color
		opacity: true
		format: hex
		default-light: '#'
		default-dark: '#'
    - 
		id: checkbox-border-color-hover
		title: checkbox border color (hover)
        title.zh: 复选框边框颜色（鼠标悬停时）
		type: variable-themed-color
		opacity: true
		format: hex
		default-light: '#'
		default-dark: '#'


*/

/* @settings

name: Mobile
id: Mobile
settings:
    - 
        id: card-layout-pad-open
        title: Enable card layout (pad only)
        title.zh: 开启卡片式布局（平板端）
        type: class-toggle
    - 
        id: drawer-phone-full-width
        title: Side drawer full screen (phone only)
        title.zh: 侧边抽屉全屏（手机端）
        type: class-toggle

*/

/* @settings

name: Plugin
id: Plugin
settings:
    - 
        id: DB Folder
        title: DB Folder
        type: heading
        collapsed: true
        level: 3
    - 
        id: DB-table-full-width-off
        title: Disable full width table
        title.zh: 关闭 全宽表格
        type: class-toggle
    - 
        id: DB-table-bg-color
        title: background color
        title.zh: 背景颜色
        type: class-select
        allowEmpty: false
        default: DB-table-bg-color-default
        options:
            -   label: Default
                value: DB-table-bg-color-default
            -   label: Adapt to lower background
                value: DB-table-bg-color-adapt
            -   label: Unify background color
                value: DB-table-bg-color-unify
    - 
        id: Projects
        title: Projects
        type: heading
        collapsed: true
        level: 3
    - 
        id: Projects-bg-color
        title: background color
        title.zh: 背景颜色
        type: class-select
        allowEmpty: false
        default: Projects-bg-color-default
        options:
            -   label: Default
                value: Projects-bg-color-default
            -   label: Adapt to lower background
                value: Projects-bg-color-adapt
            -   label: Unify background color
                value: Projects-bg-color-unify
    - 
        id: Surfing
        title: Surfing
        type: heading
        collapsed: true
        level: 3
    - 
        id: Surfing-bookmark-bar-hide
        title: Hide bookmark bar when browsing web
        title.zh: 浏览网页时隐藏书签栏
        type: class-toggle
    - 
        id: Checklist
        title: Checklist
        type: heading
        collapsed: true
        level: 3
    - 
        id: colorful-checkbox
        title: colorful checkbox
        title.zh: 多彩选框
        type: class-toggle


*/


/*hide style setting option*/
body:not(.border-focus-mode) .setting-item:is([data-id=line-active-bg], [data-id=line-normal-opacity]),
body:not(.inline-title-color-customized) .setting-item[data-id=inline-title-color],
body:not(.background-underlying-Color-light) .setting-item[data-id=background-underlying-light],
body:not(.background-underlying-CSS-light) .setting-item:is([data-id=background-underlying-CSS-light], [data-id=background-underlying-CSS-blend-mode-light]),
body:not(.background-underlying-Color-dark) .setting-item[data-id=background-underlying-dark],
body:not(.background-underlying-CSS-dark) .setting-item:is([data-id=background-underlying-CSS-dark], [data-id=background-underlying-CSS-blend-mode-dark]),
body.mod-root-split-background-primary-light .setting-item[data-id=background-mod-root-split-light],
body.mod-root-split-background-primary-light .setting-item[data-id=background-mod-root-CSS-light],
body.mod-root-split-background-primary-light .setting-item[data-id=background-mod-root-CSS-blend-mode-light],
body.mod-root-split-background-primary-light .setting-item[data-id=background-mod-root-CSS-backdrop-filter-light],
body.mod-root-split-background-secondary-light .setting-item[data-id=background-mod-root-split-light],
body.mod-root-split-background-secondary-light .setting-item[data-id=background-mod-root-CSS-light],
body.mod-root-split-background-secondary-light .setting-item[data-id=background-mod-root-CSS-blend-mode-light],
body.mod-root-split-background-secondary-light .setting-item[data-id=background-mod-root-CSS-backdrop-filter-light],
body.mod-root-split-background-transparent-light .setting-item[data-id=background-mod-root-split-light],
body.mod-root-split-background-transparent-light .setting-item[data-id=background-mod-root-CSS-light],
body.mod-root-split-background-transparent-light .setting-item[data-id=background-mod-root-CSS-blend-mode-light],
body.mod-root-split-background-transparent-light .setting-item[data-id=background-mod-root-CSS-backdrop-filter-light],
body.mod-root-split-background-CSS-light .setting-item[data-id=background-mod-root-split-light],
body.mod-root-split-background-customize-light .setting-item[data-id=background-mod-root-CSS-light],
body.mod-root-split-background-customize-light .setting-item[data-id=background-mod-root-CSS-blend-mode-light],
body.mod-root-split-background-customize-light .setting-item[data-id=background-mod-root-CSS-backdrop-filter-light],
body.mod-root-split-background-primary-dark .setting-item[data-id=background-mod-root-split-dark],
body.mod-root-split-background-primary-dark .setting-item[data-id=background-mod-root-CSS-dark],
body.mod-root-split-background-primary-dark .setting-item[data-id=background-mod-root-CSS-blend-mode-dark],
body.mod-root-split-background-primary-dark .setting-item[data-id=background-mod-root-CSS-backdrop-filter-dark],
body.mod-root-split-background-secondary-dark .setting-item[data-id=background-mod-root-split-dark],
body.mod-root-split-background-secondary-dark .setting-item[data-id=background-mod-root-CSS-dark],
body.mod-root-split-background-secondary-dark .setting-item[data-id=background-mod-root-CSS-blend-mode-dark],
body.mod-root-split-background-secondary-dark .setting-item[data-id=background-mod-root-CSS-backdrop-filter-dark],
body.mod-root-split-background-transparent-dark .setting-item[data-id=background-mod-root-split-dark],
body.mod-root-split-background-transparent-dark .setting-item[data-id=background-mod-root-CSS-dark],
body.mod-root-split-background-transparent-dark .setting-item[data-id=background-mod-root-CSS-blend-mode-dark],
body.mod-root-split-background-transparent-dark .setting-item[data-id=background-mod-root-CSS-backdrop-filter-dark],
body.mod-root-split-background-CSS-dark .setting-item[data-id=background-mod-root-split-dark],
body.mod-root-split-background-customize-dark .setting-item[data-id=background-mod-root-CSS-dark],
body.mod-root-split-background-customize-dark .setting-item[data-id=background-mod-root-CSS-blend-mode-dark],
body.mod-root-split-background-customize-dark .setting-item[data-id=background-mod-root-CSS-backdrop-filter-dark],
body.mod-left-split-background-primary-light .setting-item[data-id=background-mod-left-split-light],
body.mod-left-split-background-primary-light .setting-item[data-id=background-mod-left-CSS-light],
body.mod-left-split-background-primary-light .setting-item[data-id=background-mod-left-CSS-blend-mode-light],
body.mod-left-split-background-primary-light .setting-item[data-id=background-mod-left-CSS-backdrop-filter-light],
body.mod-left-split-background-secondary-light .setting-item[data-id=background-mod-left-split-light],
body.mod-left-split-background-secondary-light .setting-item[data-id=background-mod-left-CSS-light],
body.mod-left-split-background-secondary-light .setting-item[data-id=background-mod-left-CSS-blend-mode-light],
body.mod-left-split-background-secondary-light .setting-item[data-id=background-mod-left-CSS-backdrop-filter-light],
body.mod-left-split-background-transparent-light .setting-item[data-id=background-mod-left-split-light],
body.mod-left-split-background-transparent-light .setting-item[data-id=background-mod-left-CSS-light],
body.mod-left-split-background-transparent-light .setting-item[data-id=background-mod-left-CSS-blend-mode-light],
body.mod-left-split-background-transparent-light .setting-item[data-id=background-mod-left-CSS-backdrop-filter-light],
body.mod-left-split-background-CSS-light .setting-item[data-id=background-mod-left-split-light],
body.mod-left-split-background-customize-light .setting-item[data-id=background-mod-left-CSS-light],
body.mod-left-split-background-customize-light .setting-item[data-id=background-mod-left-CSS-blend-mode-light],
body.mod-left-split-background-customize-light .setting-item[data-id=background-mod-left-CSS-backdrop-filter-light],
body.mod-left-split-background-primary-dark .setting-item[data-id=background-mod-left-split-dark],
body.mod-left-split-background-primary-dark .setting-item[data-id=background-mod-left-CSS-dark],
body.mod-left-split-background-primary-dark .setting-item[data-id=background-mod-left-CSS-blend-mode-dark],
body.mod-left-split-background-primary-dark .setting-item[data-id=background-mod-left-CSS-backdrop-filter-dark],
body.mod-left-split-background-secondary-dark .setting-item[data-id=background-mod-left-split-dark],
body.mod-left-split-background-secondary-dark .setting-item[data-id=background-mod-left-CSS-dark],
body.mod-left-split-background-secondary-dark .setting-item[data-id=background-mod-left-CSS-blend-mode-dark],
body.mod-left-split-background-secondary-dark .setting-item[data-id=background-mod-left-CSS-backdrop-filter-dark],
body.mod-left-split-background-transparent-dark .setting-item[data-id=background-mod-left-split-dark],
body.mod-left-split-background-transparent-dark .setting-item[data-id=background-mod-left-CSS-dark],
body.mod-left-split-background-transparent-dark .setting-item[data-id=background-mod-left-CSS-blend-mode-dark],
body.mod-left-split-background-transparent-dark .setting-item[data-id=background-mod-left-CSS-backdrop-filter-dark],
body.mod-left-split-background-CSS-dark .setting-item[data-id=background-mod-left-split-dark],
body.mod-left-split-background-customize-dark .setting-item[data-id=background-mod-left-CSS-dark],
body.mod-left-split-background-customize-dark .setting-item[data-id=background-mod-left-CSS-blend-mode-dark],
body.mod-left-split-background-customize-dark .setting-item[data-id=background-mod-left-CSS-backdrop-filter-dark],
body.mod-right-split-background-primary-light .setting-item[data-id=background-mod-right-split-light],
body.mod-right-split-background-primary-light .setting-item[data-id=background-mod-right-CSS-light],
body.mod-right-split-background-primary-light .setting-item[data-id=background-mod-right-CSS-blend-mode-light],
body.mod-right-split-background-primary-light .setting-item[data-id=background-mod-right-CSS-backdrop-filter-light],
body.mod-right-split-background-secondary-light .setting-item[data-id=background-mod-right-split-light],
body.mod-right-split-background-secondary-light .setting-item[data-id=background-mod-right-CSS-light],
body.mod-right-split-background-secondary-light .setting-item[data-id=background-mod-right-CSS-blend-mode-light],
body.mod-right-split-background-secondary-light .setting-item[data-id=background-mod-right-CSS-backdrop-filter-light],
body.mod-right-split-background-transparent-light .setting-item[data-id=background-mod-right-split-light],
body.mod-right-split-background-transparent-light .setting-item[data-id=background-mod-right-CSS-light],
body.mod-right-split-background-transparent-light .setting-item[data-id=background-mod-right-CSS-blend-mode-light],
body.mod-right-split-background-transparent-light .setting-item[data-id=background-mod-right-CSS-backdrop-filter-light],
body.mod-right-split-background-CSS-light .setting-item[data-id=background-mod-right-split-light],
body.mod-right-split-background-customize-light .setting-item[data-id=background-mod-right-CSS-light],
body.mod-right-split-background-customize-light .setting-item[data-id=background-mod-right-CSS-blend-mode-light],
body.mod-right-split-background-customize-light .setting-item[data-id=background-mod-right-CSS-backdrop-filter-light],
body.mod-right-split-background-primary-dark .setting-item[data-id=background-mod-right-split-dark],
body.mod-right-split-background-primary-dark .setting-item[data-id=background-mod-right-CSS-dark],
body.mod-right-split-background-primary-dark .setting-item[data-id=background-mod-right-CSS-blend-mode-dark],
body.mod-right-split-background-primary-dark .setting-item[data-id=background-mod-right-CSS-backdrop-filter-dark],
body.mod-right-split-background-secondary-dark .setting-item[data-id=background-mod-right-split-dark],
body.mod-right-split-background-secondary-dark .setting-item[data-id=background-mod-right-CSS-dark],
body.mod-right-split-background-secondary-dark .setting-item[data-id=background-mod-right-CSS-blend-mode-dark],
body.mod-right-split-background-secondary-dark .setting-item[data-id=background-mod-right-CSS-backdrop-filter-dark],
body.mod-right-split-background-transparent-dark .setting-item[data-id=background-mod-right-split-dark],
body.mod-right-split-background-transparent-dark .setting-item[data-id=background-mod-right-CSS-dark],
body.mod-right-split-background-transparent-dark .setting-item[data-id=background-mod-right-CSS-blend-mode-dark],
body.mod-right-split-background-transparent-dark .setting-item[data-id=background-mod-right-CSS-backdrop-filter-dark],
body.mod-right-split-background-CSS-dark .setting-item[data-id=background-mod-right-split-dark],
body.mod-right-split-background-customize-dark .setting-item[data-id=background-mod-right-CSS-dark],
body.mod-right-split-background-customize-dark .setting-item[data-id=background-mod-right-CSS-blend-mode-dark],
body.mod-right-split-background-customize-dark .setting-item[data-id=background-mod-right-CSS-backdrop-filter-dark],
body.activated-file-default-light .setting-item[data-id=color-activated-file-light],
body.activated-file-default-light .setting-item[data-id=border-radius-activated-file-light],
body.activated-file-default-light .setting-item[data-id=background-activated-file-light],
body.activated-file-default-light .setting-item[data-id=shadow-activated-file-light],
body.activated-file-accent-light .setting-item[data-id=color-activated-file-light],
body.activated-file-accent-light .setting-item[data-id=border-radius-activated-file-light],
body.activated-file-accent-light .setting-item[data-id=background-activated-file-light],
body.activated-file-accent-light .setting-item[data-id=shadow-activated-file-light],
body.activated-file-tab-style-light .setting-item[data-id=color-activated-file-light],
body.activated-file-tab-style-light .setting-item[data-id=border-radius-activated-file-light],
body.activated-file-tab-style-light .setting-item[data-id=background-activated-file-light],
body.activated-file-tab-style-light .setting-item[data-id=shadow-activated-file-light],
body.activated-file-default-dark .setting-item[data-id=color-activated-file-dark],
body.activated-file-default-dark .setting-item[data-id=border-radius-activated-file-dark],
body.activated-file-default-dark .setting-item[data-id=background-activated-file-dark],
body.activated-file-default-dark .setting-item[data-id=shadow-activated-file-dark],
body.activated-file-accent-dark .setting-item[data-id=color-activated-file-dark],
body.activated-file-accent-dark .setting-item[data-id=border-radius-activated-file-dark],
body.activated-file-accent-dark .setting-item[data-id=background-activated-file-dark],
body.activated-file-accent-dark .setting-item[data-id=shadow-activated-file-dark],
body.activated-file-tab-style-dark .setting-item[data-id=color-activated-file-dark],
body.activated-file-tab-style-dark .setting-item[data-id=border-radius-activated-file-dark],
body.activated-file-tab-style-dark .setting-item[data-id=background-activated-file-dark],
body.activated-file-tab-style-dark .setting-item[data-id=shadow-activated-file-dark],
body:not(.table-width-customized) .setting-item[data-id=table-width],
body.file-icon-remove .setting-item[data-id=colorful-folder],
body:not(.new-tab-image-customize) .setting-item[data-id=new-tab-image],
body:not(.line-hover-indicator) .setting-item[data-id=hover-indicator-color],
body:not(.codeblock-style-customize) .setting-item:is([data-id=code-background-light], [data-id=code-background-dark], [data-id=code-border-light], [data-id=code-border-dark], [data-id=code-normal], [data-id=code-comment], [data-id=code-function], [data-id=code-important], [data-id=code-keyword], [data-id=code-operator], [data-id=code-property], [data-id=code-punctuation], [data-id=code-string], [data-id=code-tag], [data-id=code-value]),
body:not(.callout-style-customize) .setting-item:is([data-id=callout-border-width], [data-id=callout-border-opacity], [data-id=callout-padding], [data-id=callout-radius], [data-id=callout-title-color], [data-id=callout-title-padding], [data-id=callout-title-padding], [data-id=callout-title-size], [data-id=callout-content-padding], [data-id=callout-content-background], [data-id=callout-content-radius]),
body:not(.editor-grid-background-pattren) .setting-item:is([data-id=grid-background-pattern-color], [data-id=grid-background-pattern-size]) {
    display: none;
}

.accent-color-override-light.theme-light .mod-settings .vertical-tab-content-container>.vertical-tab-content>.setting-item:nth-child(2)>.setting-item-control:has(>input[type=color]),
.accent-color-override-dark.theme-dark .mod-settings .vertical-tab-content-container>.vertical-tab-content>.setting-item:nth-child(2)>.setting-item-control:has(>input[type=color]) {
    position: relative;
    visibility: hidden;
}

.accent-color-override-light.theme-light .mod-settings .vertical-tab-content-container>.vertical-tab-content>.setting-item:nth-child(2)>.setting-item-control:has(>input[type=color])::before,
.accent-color-override-dark.theme-dark .mod-settings .vertical-tab-content-container>.vertical-tab-content>.setting-item:nth-child(2)>.setting-item-control:has(>input[type=color])::before {
    content: "Overridden by Style Settings";
    font-family: var(--font-interface);
    font-size: var(--font-ui-smaller);
    position: absolute;
    width: max-content;
    right: 0px;
    top: 50%;
    transform: translateY(-50%);
    visibility: visible;
    pointer-events: none;
}

.accent-color-override-light.theme-light.is-mobile .mod-settings .vertical-tab-content-container>.vertical-tab-content>.setting-item:nth-child(2)>.setting-item-control:has(>input[type=color])::before,
.accent-color-override-dark.theme-dark.is-mobile .mod-settings .vertical-tab-content-container>.vertical-tab-content>.setting-item:nth-child(2)>.setting-item-control:has(>input[type=color])::before {
    width: auto;
}

/* color */

body {
    /* Accent HSL values */
    --accent-h: 232;
    --accent-s: 80%;
    --accent-l: 64%;
}

.theme-light.accent-color-override-light {
    --accent-h: var(--accent-light-h) !important;
    --accent-s: var(--accent-light-s) !important;
    --accent-l: var(--accent-light-l) !important;
}

.theme-dark.accent-color-override-dark {
    --accent-h: var(--accent-dark-h) !important;
    --accent-s: var(--accent-dark-s) !important;
    --accent-l: var(--accent-dark-l) !important;
}

.theme-light {
    color-scheme: light;
    --text-normal: hsl(var(--accent-h), 12%, 12%);
    --text-muted: hsl(var(--accent-h), 9%, 36%);
    --text-faint: hsl(var(--accent-h), 6%, 64%);
    --highlight-mix-blend-mode: darken;
    --mono-rgb-0: 255, 255, 255;
    --mono-rgb-100: 0, 0, 0;
    --color-red-rgb: 221, 44, 56;
    --color-red: rgb(var(--color-red-rgb));
    --color-green-rgb: 29, 165, 29;
    --color-green: rgb(var(--color-green-rgb));
    --color-orange-rgb: 222, 116, 23;
    --color-orange: rgb(var(--color-orange-rgb));
    --color-yellow-rgb: 192, 156, 12;
    --color-yellow: rgb(var(--color-yellow-rgb));
    --color-cyan-rgb: 22, 166, 171;
    --color-cyan: rgb(var(--color-cyan-rgb));
    --color-blue-rgb: 23, 117, 217;
    --color-blue: rgb(var(--color-blue-rgb));
    --color-purple-rgb: 143, 71, 225;
    --color-purple: rgb(var(--color-purple-rgb));
    --color-pink-rgb: 221, 19, 153;
    --color-pink: rgb(var(--color-pink-rgb));
    --color-base-00: #ffffff;
    --color-base-05: #fcfcfc;
    --color-base-10: #fafafa;
    --color-base-15: #f7f7f7;
    --color-base-20: #F0F0F0;
    --color-base-25: #e3e3e3;
    --color-base-30: #e0e0e0;
    --color-base-35: #d4d4d4;
    --color-base-40: #bdbdbd;
    --color-base-50: #ababab;
    --color-base-60: #707070;
    --color-base-70: #5a5a5a;
    --color-base-100: #222222;
    --accent-h: var(--accent-light-h);
    --accent-s: var(--accent-light-s);
    --accent-l: var(--accent-light-l);
    --accent-light-h: 232;
    --accent-light-s: 80%;
    --accent-light-l: 64%;
    --color-accent-hsl: var(--color-accent-hsl-light);
    --color-accent: var(--color-accent-light);
    --color-accent-1: var(--color-accent-1-light);
    --color-accent-2: var(--color-accent-2-light);
    --color-accent-3: var(--color-accent-3-light);
    --color-accent-hsl-light: var(--accent-h),
        var(--accent-s),
        var(--accent-l);
    --color-accent-light: hsl(var(--accent-h), var(--accent-s), var(--accent-l));
    --color-accent-1-light: hsl(var(--accent-h), var(--accent-s), calc(var(--accent-l) + 2.5%));
    --color-accent-2-light: hsl(var(--accent-h), var(--accent-s), calc(var(--accent-l) + 5%));
    --color-accent-3-light: hsla(var(--accent-h), calc(0.4*var(--accent-s)), calc(var(--accent-l)));
    --background-modifier-border: hsla(var(--accent-h), calc(0.4*var(--accent-s)), calc(var(--accent-l)), 0.2);
    --background-modifier-border-hover: hsla(var(--accent-h), calc(0.4*var(--accent-s)), calc(var(--accent-l)), 0.25);
    --background-modifier-border-focus: hsla(var(--accent-h), calc(0.4*var(--accent-s)), calc(var(--accent-l)), 0.3);
    --background-primary: var(--color-base-00);
    --background-primary-alt: var(--color-base-10);
    --background-secondary: hsl(var(--accent-h),
            calc((var(--accent-s)) / 6),
            calc(100% - (100% - var(--accent-l)) / 16));
    --background-secondary-alt: var(--color-base-05);
    --background-tertiary: hsl(var(--accent-h),
            calc((var(--accent-s)) / 2),
            calc(100% - (100% - var(--accent-l)) / 12));
    --workspace-background-translucent: rgba(246, 246, 246, 0.8);
    --background-modifier-hover: hsla(var(--accent-h), 6%, 64%, 0.15);
    --background-modifier-active-hover: hsla(var(--interactive-accent-hsl), 0.15);
    --background-modifier-box-shadow: rgba(0, 0, 0, 0.1);
    --background-modifier-cover: rgba(220, 220, 220, 0.4);
    --background-modifier-form-field: transparent;
    --search-result-background: transparent;
    --text-highlight-bg: rgba(255, 208, 0, 0.4);
    --text-highlight-bg-active: rgba(255, 128, 0, 0.4);
    --shadow-s: 0px 1px 2px rgba(0, 0, 0, 0.028),
        0px 3.4px 6.7px rgba(0, 0, 0, .042),
        0px 15px 30px rgba(0, 0, 0, .07);
    --shadow-l: 0px 1.8px 7.3px rgba(0, 0, 0, 0.071),
        0px 6.3px 24.7px rgba(0, 0, 0, 0.112),
        0px 30px 90px rgba(0, 0, 0, 0.2);
}

.theme-light-background-old-default {
    --background-primary: var(--color-base-00);
    --background-primary-alt: var(--color-base-10);
    --background-secondary: hsl(var(--accent-h),
            calc((var(--accent-s) - 12%) / 6),
            calc(100% - (100% - var(--accent-l)) / 16));
    --background-secondary-alt: var(--color-base-05);
    --background-tertiary: hsl(calc(var(--accent-h) - 18),
            calc(var(--accent-s) - 4%),
            calc(100% - (100% - var(--accent-l)) / 8));
}

.theme-light.theme-light-background-adapt {
    --color-accent-3-light: hsla(var(--accent-h), calc(0.4*var(--accent-s)), calc(0.81*var(--accent-l)));
    --background-primary: hsl(var(--accent-h),
            calc(var(--accent-s) / 2),
            calc(100% - (100% - var(--accent-l)) / 12));
    --background-primary-alt: hsl(var(--accent-h),
            calc(var(--accent-s) / 2),
            calc(100% - (100% - var(--accent-l)) / 10));
    --background-secondary: hsl(var(--accent-h),
            calc(var(--accent-s) / 2),
            calc(100% - (100% - var(--accent-l)) / 8));
    --background-secondary-alt: hsl(var(--accent-h),
            calc(var(--accent-s) / 2),
            calc(100% - (100% - var(--accent-l)) / 7));
    --background-tertiary: hsl(var(--accent-h),
            calc(var(--accent-s)),
            calc(100% - (100% - var(--accent-l)) / 6));
    --interactive-normal: var(--color-base-00);
    --interactive-hover: var(--color-base-10);
    --background-modifier-hover: hsla(var(--accent-h), 6%, 64%, 0.225);
}

.theme-dark {
    color-scheme: dark;
    --text-normal: hsl(var(--accent-h), 24%, 84%);
    --text-muted: hsl(var(--accent-h), 18%, 72%);
    --text-faint: hsl(var(--accent-h), 12%, 44%);
    --highlight-mix-blend-mode: lighten;
    --mono-rgb-0: 0, 0, 0;
    --mono-rgb-100: 255, 255, 255;
    --color-red-rgb: 255, 120, 129;
    --color-red: rgb(var(--color-red-rgb));
    --color-green-rgb: 124, 211, 124;
    --color-green: rgb(var(--color-green-rgb));
    --color-orange-rgb: 251, 187, 131;
    --color-orange: rgb(var(--color-orange-rgb));
    --color-yellow-rgb: 255, 232, 139;
    --color-yellow: rgb(var(--color-yellow-rgb));
    --color-cyan-rgb: 134, 223, 226;
    --color-cyan: rgb(var(--color-cyan-rgb));
    --color-blue-rgb: 137, 189, 244;
    --color-blue: rgb(var(--color-blue-rgb));
    --color-purple-rgb: 203, 158, 255;
    --color-purple: rgb(var(--color-purple-rgb));
    --color-pink-rgb: 242, 182, 222;
    --color-pink: rgb(var(--color-pink-rgb));
    --color-base-00: #1e1e1e;
    --color-base-10: #242424;
    --color-base-15: #252525;
    --color-base-20: #262626;
    --color-base-25: #2a2a2a;
    --color-base-30: #363636;
    --color-base-35: #3F3F3F;
    --color-base-40: #555;
    --color-base-50: #666;
    --color-base-60: #999;
    --color-base-70: #bababa;
    --color-base-100: #dadada;
    --accent-h: var(--accent-dark-h);
    --accent-s: var(--accent-dark-s);
    --accent-l: var(--accent-dark-l);
    --accent-dark-h: 232;
    --accent-dark-s: 64%;
    --accent-dark-l: 72%;
    --color-accent-hsl: var(--color-accent-hsl-dark);
    --color-accent: var(--color-accent-dark);
    --color-accent-1: var(--color-accent-1-dark);
    --color-accent-2: var(--color-accent-2-dark);
    --color-accent-3: var(--color-accent-3-dark);
    --color-accent-hsl-dark: var(--accent-h),
        var(--accent-s),
        var(--accent-l);
    --color-accent-dark: hsl(var(--accent-h), var(--accent-s), var(--accent-l));
    --color-accent-1-dark: hsl(var(--accent-h), var(--accent-s), calc(var(--accent-l) - 3.8%));
    --color-accent-2-dark: hsl(var(--accent-h), var(--accent-s), calc(var(--accent-l) + 3.8%));
    --color-accent-3-dark: hsla(var(--accent-h), calc(0.4*var(--accent-s)), calc(0.9*var(--accent-l)));
    --background-modifier-border: hsla(var(--accent-h), calc(0.4*var(--accent-s)), calc(0.9*var(--accent-l)), 0.2);
    --background-modifier-border-hover: hsla(var(--accent-h), calc(0.4*var(--accent-s)), calc(0.9*var(--accent-l)), 0.25);
    --background-modifier-border-focus: hsla(var(--accent-h), calc(0.4*var(--accent-s)), calc(0.9*var(--accent-l)), 0.3);
    --background-primary: hsl(var(--accent-h), calc(var(--accent-s) / 5), calc(1.25*var(--accent-l) / 4.5));
    --background-primary-alt: hsl(var(--accent-h), calc(var(--accent-s) / 5), calc(1.25*var(--accent-l) / 3.9));
    --background-secondary: hsl(var(--accent-h), calc(var(--accent-s) / 5), calc(1.25*var(--accent-l) / 4.9));
    --background-secondary-alt: hsl(var(--accent-h),
            calc(var(--accent-s) / 5.4),
            calc(1.25*var(--accent-l) / 3));
    --background-tertiary: hsl(var(--accent-h),
            calc(var(--accent-s) / 5.4),
            calc(1.25*var(--accent-l) / 3.6));
    --workspace-background-translucent: hsla(var(--accent-h),
            calc(var(--accent-s) / 5.4),
            calc(1.25*var(--accent-l) / 3.6), 0.6);
    --background-modifier-hover: hsla(var(--accent-h), 12%, 40%, 0.25);
    --background-modifier-active-hover: hsla(var(--interactive-accent-hsl), 0.15);
    --background-modifier-box-shadow: rgba(0, 0, 0, 0.3);
    --background-modifier-cover: rgba(10, 10, 10, 0.4);
    --background-modifier-form-field: transparent;
    --search-result-background: transparent;
    --text-highlight-bg: rgba(255, 208, 0, 0.4);
    --text-highlight-bg-active: rgba(255, 128, 0, 0.4);
    --text-selection: hsla(var(--interactive-accent-hsl), 0.25);
    --shadow-s: 0px 1px 2px rgba(0, 0, 0, 0.121),
        0px 3.4px 6.7px rgba(0, 0, 0, 0.179),
        0px 15px 30px rgba(0, 0, 0, 0.3);
    --shadow-l: 0px 1.8px 7.3px rgba(0, 0, 0, 0.071),
        0px 6.3px 24.7px rgba(0, 0, 0, 0.112),
        0px 30px 90px rgba(0, 0, 0, 0.2);
}

.theme-dark.theme-dark-background-darker {
    --background-primary: hsl(var(--accent-h), calc(var(--accent-s) / 4), calc(var(--accent-l) / 5));
    --background-primary-alt: hsl(var(--accent-h), calc(var(--accent-s) / 4), calc(var(--accent-l) / 4));
    --background-secondary: hsl(var(--accent-h), calc(var(--accent-s) / 5), calc(var(--accent-l) / 6));
    --background-secondary-alt: hsl(var(--accent-h),
            calc(var(--accent-s) / 6),
            calc(var(--accent-l) / 3.6));
    --background-tertiary: hsl(var(--accent-h),
            calc(var(--accent-s) / 6),
            calc(var(--accent-l) / 4));
    --workspace-background-translucent: hsla(var(--accent-h),
            calc(var(--accent-s) / 6),
            calc(var(--accent-l) / 4), 0.6);
    --interactive-normal: hsl(var(--accent-h),
            calc(var(--accent-s) / 7),
            calc(var(--accent-l) / 3));
    --interactive-hover: hsl(var(--accent-h),
            calc(var(--accent-s) / 7),
            calc(var(--accent-l) / 2.7));
}


.theme-dark.theme-dark-background-brighter {
    --background-primary: hsl(var(--accent-h), calc(var(--accent-s) / 4.2), calc(1.25*var(--accent-l) / 3.5));
    --background-primary-alt: hsl(var(--accent-h), calc(var(--accent-s) / 4.2), calc(1.25*var(--accent-l) / 3.1));
    --background-secondary: hsl(var(--accent-h), calc(var(--accent-s) / 4.2), calc(1.25*var(--accent-l) / 4));
    --background-secondary-alt: hsl(var(--accent-h),
            calc(var(--accent-s) / 4.2),
            calc(1.25*var(--accent-l) / 3.7));
    --background-tertiary: hsl(var(--accent-h),
            calc(var(--accent-s) / 4.8),
            calc(1.25*var(--accent-l) / 3));
    --workspace-background-translucent: hsla(var(--accent-h),
            calc(var(--accent-s) / 4.8),
            calc(1.25*var(--accent-l) / 3), 0.6);
    --interactive-normal: hsl(var(--accent-h),
            calc(var(--accent-s) / 5.6),
            calc(1.25*var(--accent-l) / 2.4));
    --interactive-hover: hsl(var(--accent-h),
            calc(var(--accent-s) / 5.6),
            calc(1.25*var(--accent-l) / 2.2));
}


.theme-dark.theme-dark-background-black {
    --background-primary: black;
    --background-primary-alt: var(--color-base-00);
    --background-secondary: black;
    --background-secondary-alt: black;
    --background-tertiary: black;
    --workspace-background-translucent: rgba(0, 0, 0, 0.6);
    --interactive-normal: hsl(var(--accent-h),
            calc(var(--accent-s) / 7),
            calc(var(--accent-l) / 4));
    --interactive-hover: hsl(var(--accent-h),
            calc(var(--accent-s) / 7),
            calc(var(--accent-l) / 3.2));
    --background-table-rows: var(--color-base-00);
    --background-modifier-border: var(--color-base-30);
    --background-modifier-border-hover: var(--color-base-35);
    --background-modifier-border-focus: var(--color-base-40);
    --background-modifier-hover: rgba(var(--mono-rgb-100), 0.15);
}

/*====== Animations ======*/
body {
    /* Animations */
    --anim-speed: 1;
    --anim-duration-superfast: calc(75ms / var(--anim-speed));
    --anim-duration-fast: calc(150ms / var(--anim-speed));
    --anim-duration-moderate: calc(300ms / var(--anim-speed));
    --anim-duration-slow: calc(600ms / var(--anim-speed));
    --anim-in: var(--anim-duration-fast) var(--anim-duration-superfast) var(--anim-motion-swing);
    --anim-out: var(--anim-duration-moderate) var(--anim-duration-slow) var(--anim-motion-swing);
}

/*body:not(.is-phone, .extra-anim-remove) .graph-controls:not(.is-close),
body:not(.is-phone, .extra-anim-remove) .popover:not(.svelte-1xg3ic1),
body:not(.is-phone, .extra-anim-remove) .modal:not(:has(.checkbox-container)),
body:not(.is-phone, .extra-anim-remove) .menu:not(.mk-style-menu, .svelte-1xg3ic1),
body:not(.is-phone, .extra-anim-remove) .suggestion-container:not([data-popper-placement="bottom-start"]),*/
body:not(.is-phone, .extra-anim-remove) .prompt,
body:not(.is-mobile, .extra-anim-remove) .document-search-container {
    -webkit-animation: scale-up var(--anim-duration-fast) cubic-bezier(0.175, 0.885, 0.320, 1.095) both;
    animation: scale-up var(--anim-duration-fast) cubic-bezier(0.175, 0.885, 0.320, 1.095) both;
}

@media (prefers-reduced-motion) {

    /*body:not(.is-phone, .extra-anim-remove) .graph-controls:not(.is-close),
    body:not(.is-phone, .extra-anim-remove) .popover:not(.svelte-1xg3ic1),
    body:not(.is-phone, .extra-anim-remove) .modal:not(:has(.checkbox-container)),
    body:not(.is-phone, .extra-anim-remove) .menu:not(.mk-style-menu, .svelte-1xg3ic1),
    body:not(.is-phone, .extra-anim-remove) .suggestion-container:not([data-popper-placement="bottom-start"]),*/
    body:not(.is-phone, .extra-anim-remove) .prompt,
    body:not(.is-mobile, .extra-anim-remove) .document-search-container {
        -webkit-animation: unset;
        animation: unset;
    }
}

@-webkit-keyframes scale-up {
    0% {
        -webkit-transform: scale(0.75);
        transform: scale(0.75);
        opacity: 0;
    }

    100% {
        -webkit-transform: scale(1);
        transform: scale(1);
        opacity: 1;
    }
}

@keyframes scale-up {
    0% {
        -webkit-transform: scale(0.75);
        transform: scale(0.75);
        opacity: 0;
    }

    100% {
        -webkit-transform: scale(1);
        transform: scale(1);
        opacity: 1;
    }
}

body:not(.is-phone, .extra-anim-remove) .modal:has(.checkbox-container),
body:not(.is-phone, .extra-anim-remove) .modal.mod-settings,
body:not(.is-phone, .extra-anim-remove) .modal.mod-community-modal {
    -webkit-animation: var(--anim-duration-fast) fadeIn both;
    animation: var(--anim-duration-fast) fadeIn both;
}

/*----------workspace----------*/
/* ====== background ===== */
body {
    --background-underlying-light: var(--background-tertiary);
    --background-underlying-dark: var(--background-tertiary);
    --background-underlying: var(--background-tertiary);
    --background-mod-left-split-light: var(--background-primary);
    --background-mod-left-split-dark: var(--background-primary);
    --background-mod-left-split: var(--background-primary);
    --background-mod-right-split-light: var(--background-primary);
    --background-mod-right-split-dark: var(--background-primary);
    --background-mod-right-split: var(--background-primary);
    --background-mod-root-split-light: var(--background-primary);
    --background-mod-root-split-dark: var(--background-primary);
    --background-mod-root-split: var(--background-primary);

    --background-mod-root-CSS-light: rgba(255, 255, 255, 0.5);
    --background-mod-root-CSS-dark: rgba(0, 0, 0, 0.5);
    --background-mod-root-CSS-blend-mode-light: normal;
    --background-mod-root-CSS-blend-mode-dark: normal;
    --background-mod-root-CSS-backdrop-filter-light: blur(32px);
    --background-mod-root-CSS-backdrop-filter-dark: blur(32px);
    --background-mod-left-CSS-light: rgba(255, 255, 255, 0.5);
    --background-mod-left-CSS-dark: rgba(0, 0, 0, 0.5);
    --background-mod-left-CSS-blend-mode-light: normal;
    --background-mod-left-CSS-blend-mode-dark: normal;
    --background-mod-left-CSS-backdrop-filter-light: blur(32px);
    --background-mod-left-CSS-backdrop-filter-dark: blur(32px);
    --background-mod-right-CSS-light: rgba(255, 255, 255, 0.5);
    --background-mod-right-CSS-dark: rgba(0, 0, 0, 0.5);
    --background-mod-right-CSS-blend-mode-light: normal;
    --background-mod-right-CSS-blend-mode-dark: normal;
    --background-mod-right-CSS-backdrop-filter-light: blur(32px);
    --background-mod-right-CSS-backdrop-filter-dark: blur(32px);
    --background-underlying-CSS-light: radial-gradient(100% 100% at 13% 50%, rgb(90, 109, 237) 0%, rgb(224, 218, 247) 100%), linear-gradient(6deg, rgb(235, 242, 252) 0%, rgb(90, 109, 237) 5%, rgb(166, 126, 241) 7%, rgb(224, 218, 247) 11%, rgb(90, 109, 237) 16%), radial-gradient(100% 100% at 60% 71%, rgb(224, 218, 247) 0%, rgb(219, 244, 255) 100%), linear-gradient(97deg, rgb(90, 109, 237) 0%, rgb(90, 109, 237) 23%, rgb(90, 109, 237) 53%), linear-gradient(351deg, rgb(235, 242, 252) 0%, rgb(219, 244, 255) 1%, rgb(235, 242, 252) 6%, rgb(224, 218, 247) 10%, rgb(166, 126, 241) 13%, rgb(235, 242, 252) 29%), radial-gradient(100% 100% at 10% 32%, rgb(90, 109, 237) 0%, rgb(219, 244, 255) 100%), radial-gradient(100% 100% at 35% 63%, rgb(224, 218, 247) 0%, rgb(224, 218, 247) 100%);
    --background-underlying-CSS-dark: linear-gradient(317deg, rgb(112, 55, 205) 0%, rgb(29, 12, 32) 19%, rgb(101, 31, 113) 19%, rgb(101, 31, 113) 33%), radial-gradient(100% 100% at 90% 9%, rgb(29, 12, 32) 0%, rgb(29, 12, 32) 100%), linear-gradient(109deg, rgb(104, 178, 248) 0%, rgb(80, 110, 229) 15%, rgb(112, 55, 205) 34%, rgb(104, 178, 248) 54%, rgb(104, 178, 248) 65%), radial-gradient(100% 100% at 20% 45%, rgb(80, 110, 229) 0%, rgb(101, 31, 113) 100%), radial-gradient(100% 100% at 90% 27%, rgb(80, 110, 229) 0%, rgb(101, 31, 113) 100%), linear-gradient(339deg, rgb(101, 31, 113) 0%, rgb(104, 178, 248) 12%, rgb(101, 31, 113) 14%, rgb(80, 110, 229) 19%, rgb(101, 31, 113) 20%, rgb(29, 12, 32) 30%);
    --background-underlying-CSS-blend-mode-light: overlay, color-burn;
    --background-underlying-CSS-blend-mode-dark: soft-light, luminosity, hue, luminosity, hard-light;
}

/* background - transparent */
body {
    --tab-background-active: transparent;
    --tab-container-background: transparent;
    --titlebar-background: transparent;
    --titlebar-background-focused: transparent;
}

.sidebar-toggle-button,
.workspace-tabs.mod-top,
body.is-focused .sidebar-toggle-button,
body.is-focused .workspace-tabs.mod-top {
    --tab-container-background: transparent;
}

body:not(.is-mobile) .workspace-tabs:not(.mod-stacked) .workspace-leaf,
body:not(.is-mobile) .workspace-tabs:not(.mod-stacked) .workspace-leaf-content,
body:not(.is-mobile) .workspace-tabs:not(.mod-stacked) .view-header:not(.animate),
body:not(.is-mobile) .workspace-tabs:not(.mod-stacked) .view-content:not(.vignette-radial, .vignette-linear, .animate, .ptm-fullscreen-writing-focus-element),
.mod-left-split .workspace-tab-header.has-active-menu,
.mod-right-split .workspace-tab-header.has-active-menu,
body:not(.is-mobile) .workspace-tab-header-container,
.workspace>.workspace-split {
    background-color: transparent !important;
}

body:not(.is-mobile) .app-container,
body:not(.is-mobile) .workspace,
.workspace-ribbon.mod-left:before {
    background-color: transparent;
}

.workspace-ribbon,
.workspace-ribbon.mod-left.is-collapsed {
    border-right: none;
    background-color: transparent;
}

.workspace-tabs:not(.mod-stacked) .workspace-tab-header.is-active {
    background-color: transparent !important;
    box-shadow: none;
    overflow: visible;
}

body:not(.is-mobile) .view-header-title-container:not(.mod-at-start):before,
body:not(.is-mobile) .view-header-title-container:not(.mod-at-end):after {
    background: transparent;
}

/* background - underlying */
body:not(.is-mobile):has(>.app-container) {
    background: var(--background-underlying);
}

body.theme-light:not(.is-mobile):has(>.app-container).background-underlying-CSS-light {
    background: var(--background-underlying-CSS-light, --background-underlying) !important;
    background-blend-mode: var(--background-underlying-CSS-blend-mode-light) !important;
}

body.theme-dark:not(.is-mobile):has(>.app-container).background-underlying-CSS-dark {
    background: var(--background-underlying-CSS-dark, --background-underlying) !important;
    background-blend-mode: var(--background-underlying-CSS-blend-mode-dark) !important;
}

body.background-underlying-primary-light.theme-light,
body.background-underlying-primary-dark.theme-dark {
    --background-underlying: var(--background-primary) !important;
}

body.background-underlying-secondary-light.theme-light,
body.background-underlying-secondary-dark.theme-dark {
    --background-underlying: var(--background-secondary) !important;
}

body.background-underlying-default-light.theme-light,
body.background-underlying-default-dark.theme-dark {
    --background-underlying: var(--background-tertiary) !important;
}

body.background-underlying-Color-light.theme-light {
    --background-underlying: var(--background-underlying-light) !important;
}

body.background-underlying-Color-dark.theme-dark {
    --background-underlying: var(--background-underlying-dark) !important;
}


/* foreground - underlying */
body {
    --on-border-light: var(--color-accent-3);
    --on-border-dark: var(--color-accent-3);
    --mix-blend-mode-on-border-light: normal;
    --mix-blend-mode-on-border-dark: screen;
}

.theme-light:not(.is-mobile) .titlebar,
.theme-light:not(.is-mobile) .status-bar,
.theme-light:not(.is-mobile) .workspace-ribbon,
.theme-light:not(.is-mobile) .workspace-tabs.mod-top .workspace-tab-header-container,
.theme-light:not(.is-mobile).card-layout-open-light .workspace-tabs .workspace-tab-header-container {
    --text-muted: var(--on-border-light);
    --text-faint: var(--on-border-light);

    --titlebar-text-color: var(--text-muted);
    --titlebar-text-color-focused: var(--text-muted);
    --tab-text-color-active: var(--text-muted);
    --tab-text-color-focused: var(--text-muted);
    --tab-text-color-focused-active: var(--text-muted);
    --status-bar-text-color: var(--text-muted);
    --icon-color: var(--text-muted);
    --icon-color-hover: var(--text-muted);

    --tab-text-color: var(--text-faint);

    --icon-opacity: 1;

    mix-blend-mode: var(--mix-blend-mode-on-border-light);
}

.theme-dark:not(.is-mobile) .titlebar,
.theme-dark:not(.is-mobile) .status-bar,
.theme-dark:not(.is-mobile) .workspace-ribbon,
.theme-dark:not(.is-mobile) .workspace-tabs.mod-top .workspace-tab-header-container,
.theme-dark:not(.is-mobile).card-layout-open-dark .workspace-tabs .workspace-tab-header-container {
    --text-muted: var(--on-border-dark);
    --text-faint: var(--on-border-dark);

    --titlebar-text-color: var(--text-muted);
    --titlebar-text-color-focused: var(--text-muted);
    --tab-text-color-active: var(--text-muted);
    --tab-text-color-focused: var(--text-muted);
    --tab-text-color-focused-active: var(--text-muted);
    --status-bar-text-color: var(--text-muted);
    --icon-color: var(--text-muted);
    --icon-color-hover: var(--text-muted);

    --tab-text-color: var(--text-faint);

    --icon-opacity: 1;

    mix-blend-mode: var(--mix-blend-mode-on-border-dark);
}


/* background - mod-left */

body.theme-light.mod-left-split-background-primary-light,
body.theme-dark.mod-left-split-background-primary-dark,
body.theme-light.mod-left-split-background-transparent-light:not(.card-layout-open-light),
body.theme-dark.mod-left-split-background-transparent-dark:not(.card-layout-open-dark) {
    --background-mod-left-split: var(--background-primary) !important;
}

body.theme-light.mod-left-split-background-secondary-light,
body.theme-dark.mod-left-split-background-secondary-dark {
    --background-mod-left-split: var(--background-secondary) !important;
}

body.theme-light.mod-left-split-background-transparent-light.card-layout-open-light,
body.theme-dark.mod-left-split-background-transparent-dark.card-layout-open-dark {
    --background-mod-left-split: transparent !important;
}

body.theme-light:not(.is-mobile).card-layout-open-light.mod-left-split-background-transparent-light .mod-left-split .workspace-tab-container {
    --card-shadow-light: transparent;
}

body.theme-dark:not(.is-mobile).card-layout-open-dark.mod-left-split-background-transparent-dark .mod-left-split .workspace-tab-container {
    --card-shadow-dark: transparent;
}

body.theme-light.mod-left-split-background-customize-light {
    --background-mod-left-split: var(--background-mod-left-split-light) !important;
}

body.theme-dark.mod-left-split-background-customize-dark {
    --background-mod-left-split: var(--background-mod-left-split-dark) !important;
}

/* --- */

body:not(.is-mobile).card-layout-open-light.theme-light .workspace-split.mod-left-split .workspace-tab-container,
body:not(.is-mobile).card-layout-open-dark.theme-dark .workspace-split.mod-left-split .workspace-tab-container,
body:not(.is-mobile):not(.card-layout-open-light).theme-light .workspace-split.mod-left-split .workspace-tabs.mod-top .workspace-tab-container,
body:not(.is-mobile):not(.card-layout-open-dark).theme-dark .workspace-split.mod-left-split .workspace-tabs.mod-top .workspace-tab-container,
body:not(.is-mobile):not(.card-layout-open-light).theme-light .workspace-split.mod-left-split .workspace-tabs:not(.mod-top),
body:not(.is-mobile):not(.card-layout-open-dark).theme-dark .workspace-split.mod-left-split .workspace-tabs:not(.mod-top) {
    background: var(--background-mod-left-split) !important;
}

body:not(.is-mobile).theme-light.card-layout-open-light.mod-left-split-background-CSS-light .workspace-split.mod-left-split .workspace-tab-container,
body:not(.is-mobile).theme-light:not(.card-layout-open-light).mod-left-split-background-CSS-light .workspace-split.mod-left-split .workspace-tabs.mod-top .workspace-tab-container,
body:not(.is-mobile).theme-light:not(.card-layout-open-light).mod-left-split-background-CSS-light .workspace-split.mod-left-split .workspace-tabs:not(.mod-top) {
    background: var(--background-mod-left-CSS-light) !important;
    background-blend-mode: var(--background-mod-left-CSS-blend-mode-light);
    backdrop-filter: var(--background-mod-left-CSS-backdrop-filter-light);
    -webkit-backdrop-filter: var(--background-mod-left-CSS-backdrop-filter-light);
}

body:not(.is-mobile).theme-dark.card-layout-open-dark.mod-left-split-background-CSS-dark .workspace-split.mod-left-split .workspace-tab-container,
body:not(.is-mobile).theme-dark:not(.card-layout-open-dark).mod-left-split-background-CSS-dark .workspace-split.mod-left-split .workspace-tabs.mod-top .workspace-tab-container,
body:not(.is-mobile).theme-dark:not(.card-layout-open-dark).mod-left-split-background-CSS-dark .workspace-split.mod-left-split .workspace-tabs:not(.mod-top) {
    background: var(--background-mod-left-CSS-dark) !important;
    background-blend-mode: var(--background-mod-left-CSS-blend-mode-dark);
    backdrop-filter: var(--background-mod-left-CSS-backdrop-filter-dark);
    -webkit-backdrop-filter: var(--background-mod-left-CSS-backdrop-filter-dark);
}


/* background - mod-right */

body.theme-light.mod-right-split-background-primary-light,
body.theme-dark.mod-right-split-background-primary-dark,
body.theme-light.mod-right-split-background-transparent-light:not(.card-layout-open-light),
body.theme-dark.mod-right-split-background-transparent-dark:not(.card-layout-open-dark) {
    --background-mod-right-split: var(--background-primary) !important;
}

body.theme-light.mod-right-split-background-secondary-light,
body.theme-dark.mod-right-split-background-secondary-dark {
    --background-mod-right-split: var(--background-secondary) !important;
}

body.theme-light.mod-right-split-background-transparent-light.card-layout-open-light,
body.theme-dark.mod-right-split-background-transparent-dark.card-layout-open-dark {
    --background-mod-right-split: transparent !important;
}

body.theme-light:not(.is-mobile).card-layout-open-light.mod-right-split-background-transparent-light .mod-right-split .workspace-tab-container {
    --card-shadow-light: transparent;
}

body.theme-dark:not(.is-mobile).card-layout-open-dark.mod-right-split-background-transparent-dark .mod-right-split .workspace-tab-container {
    --card-shadow-dark: transparent;
}

body.theme-light.mod-right-split-background-customize-light {
    --background-mod-right-split: var(--background-mod-right-split-light) !important;
}

body.theme-dark.mod-right-split-background-customize-dark {
    --background-mod-right-split: var(--background-mod-right-split-dark) !important;
}

/* --- */
body:not(.is-mobile).card-layout-open-light.theme-light .workspace-split.mod-right-split .workspace-tab-container,
body:not(.is-mobile).card-layout-open-dark.theme-dark .workspace-split.mod-right-split .workspace-tab-container,
body:not(.is-mobile):not(.card-layout-open-light).theme-light .workspace-split.mod-right-split .workspace-tabs.mod-top .workspace-tab-container,
body:not(.is-mobile):not(.card-layout-open-dark).theme-dark .workspace-split.mod-right-split .workspace-tabs.mod-top .workspace-tab-container,
body:not(.is-mobile):not(.card-layout-open-light).theme-light .workspace-split.mod-right-split .workspace-tabs:not(.mod-top),
body:not(.is-mobile):not(.card-layout-open-dark).theme-dark .workspace-split.mod-right-split .workspace-tabs:not(.mod-top) {
    background: var(--background-mod-right-split) !important;
}

body:not(.is-mobile).theme-light.card-layout-open-light.mod-right-split-background-CSS-light .workspace-split.mod-right-split .workspace-tab-container,
body:not(.is-mobile).theme-light:not(.card-layout-open-light).mod-right-split-background-CSS-light .workspace-split.mod-right-split .workspace-tabs.mod-top .workspace-tab-container,
body:not(.is-mobile).theme-light:not(.card-layout-open-light).mod-right-split-background-CSS-light .workspace-split.mod-right-split .workspace-tabs:not(.mod-top) {
    background: var(--background-mod-right-CSS-light) !important;
    background-blend-mode: var(--background-mod-right-CSS-blend-mode-light);
    backdrop-filter: var(--background-mod-right-CSS-backdrop-filter-light);
    -webkit-backdrop-filter: var(--background-mod-right-CSS-backdrop-filter-light);
}

body:not(.is-mobile).theme-dark.card-layout-open-dark.mod-right-split-background-CSS-dark .workspace-split.mod-right-split .workspace-tab-container,
body:not(.is-mobile).theme-dark:not(.card-layout-open-dark).mod-right-split-background-CSS-dark .workspace-split.mod-right-split .workspace-tabs.mod-top .workspace-tab-container,
body:not(.is-mobile).theme-dark:not(.card-layout-open-dark).mod-right-split-background-CSS-dark .workspace-split.mod-right-split .workspace-tabs:not(.mod-top) {
    background: var(--background-mod-right-CSS-dark) !important;
    background-blend-mode: var(--background-mod-right-CSS-blend-mode-dark);
    backdrop-filter: var(--background-mod-right-CSS-backdrop-filter-dark);
    -webkit-backdrop-filter: var(--background-mod-right-CSS-backdrop-filter-dark);
}

/* background - mod-root */

body.theme-light.mod-root-split-background-primary-light,
body.theme-dark.mod-root-split-background-primary-dark,
body.theme-light.mod-root-split-background-transparent-light:not(.card-layout-open-light),
body.theme-dark.mod-root-split-background-transparent-dark:not(.card-layout-open-dark),
body.theme-light:is(.mod-root-split-background-transparent-light, .mod-root-split-background-CSS-light) .mod-stacked,
body.theme-dark:is(.mod-root-split-background-transparent-dark, .mod-root-split-background-CSS-dark) .mod-stacked {
    --background-mod-root-split: var(--background-primary) !important;
}

body.theme-light.mod-root-split-background-secondary-light,
body.theme-dark.mod-root-split-background-secondary-dark {
    --background-mod-root-split: var(--background-secondary) !important;
}

body.theme-light.mod-root-split-background-transparent-light.card-layout-open-light,
body.theme-dark.mod-root-split-background-transparent-dark.card-layout-open-dark {
    --background-mod-root-split: transparent !important;
}

body.theme-light:not(.is-mobile).card-layout-open-light.mod-root-split-background-transparent-light .mod-root .workspace-tabs:not(.mod-stacked) .workspace-tab-container {
    --card-shadow-light: transparent;
}

body.theme-dark:not(.is-mobile).card-layout-open-dark.mod-root-split-background-transparent-dark .mod-root .workspace-tabs:not(.mod-stacked) .workspace-tab-container {
    --card-shadow-dark: transparent;
}

body.theme-light.mod-root-split-background-customize-light {
    --background-mod-root-split: var(--background-mod-root-split-light) !important;
}

body.theme-dark.mod-root-split-background-customize-dark {
    --background-mod-root-split: var(--background-mod-root-split-dark) !important;
}

/* --- */

body:not(.is-mobile).card-layout-open-light.theme-light .workspace-split.mod-root .workspace-tab-container,
body:not(.is-mobile).card-layout-open-dark.theme-dark .workspace-split.mod-root .workspace-tab-container,
body:not(.is-mobile):not(.card-layout-open-light).theme-light .workspace-split.mod-root .workspace-tabs.mod-top .workspace-tab-container,
body:not(.is-mobile):not(.card-layout-open-dark).theme-dark .workspace-split.mod-root .workspace-tabs.mod-top .workspace-tab-container,
body:not(.is-mobile):not(.card-layout-open-light).theme-light .workspace-split.mod-root .workspace-tabs:not(.mod-top),
body:not(.is-mobile):not(.card-layout-open-dark).theme-dark .workspace-split.mod-root .workspace-tabs:not(.mod-top) {
    background: var(--background-mod-root-split) !important;
}

body:not(.is-mobile).theme-light.card-layout-open-light.mod-root-split-background-CSS-light .workspace-split.mod-root .workspace-tab-container,
body:not(.is-mobile).theme-light:not(.card-layout-open-light).mod-root-split-background-CSS-light .workspace-split.mod-root .workspace-tabs.mod-top .workspace-tab-container,
body:not(.is-mobile).theme-light:not(.card-layout-open-light).mod-root-split-background-CSS-light .workspace-split.mod-root .workspace-tabs:not(.mod-top) {
    background: var(--background-mod-root-CSS-light) !important;
    background-blend-mode: var(--background-mod-root-CSS-blend-mode-light);
    backdrop-filter: var(--background-mod-root-CSS-backdrop-filter-light);
    -webkit-backdrop-filter: var(--background-mod-root-CSS-backdrop-filter-light);
}

body:not(.is-mobile).theme-dark.card-layout-open-dark.mod-root-split-background-CSS-dark .workspace-split.mod-root .workspace-tab-container,
body:not(.is-mobile).theme-dark:not(.card-layout-open-dark).mod-root-split-background-CSS-dark .workspace-split.mod-root .workspace-tabs.mod-top .workspace-tab-container,
body:not(.is-mobile).theme-dark:not(.card-layout-open-dark).mod-root-split-background-CSS-dark .workspace-split.mod-root .workspace-tabs:not(.mod-top) {
    background: var(--background-mod-root-CSS-dark) !important;
    background-blend-mode: var(--background-mod-root-CSS-blend-mode-dark);
    backdrop-filter: var(--background-mod-root-CSS-backdrop-filter-dark);
    -webkit-backdrop-filter: var(--background-mod-root-CSS-backdrop-filter-dark);
}

/* ====== translucent window ===== */
.is-translucent:not(.is-fullscreen) {
    --divider-color: var(--background-modifier-border);
}

.is-translucent:not(.is-fullscreen) .titlebar,
.is-translucent:not(.is-fullscreen) .app-container,
.is-translucent .workspace-ribbon::after,
.is-translucent:not(.is-fullscreen) .workspace-tabs.mod-top,
.is-translucent .workspace-split.mod-horizontal.mod-left-split,
.is-translucent.is-hidden-frameless .titlebar,
.is-translucent .titlebar-button {
    background-color: transparent !important;
}

.is-translucent:not(.is-fullscreen) {
    background-color: var(--workspace-background-translucent) !important;
}

.is-translucent .titlebar-button:hover {
    background-color: var(--background-modifier-hover) !important;
}

.is-translucent .workspace .mod-root .workspace-tab-header-inner::after {
    background-color: var(--divider-color);
}

/* ====== Layout ===== */
/* default Layout*/
body {
    --card-shadow-light: 0px 0px 6px hsla(var(--accent-h), 18%, 80%, 0.4), 0px 0px 2px hsla(var(--accent-h), 18%, 80%, 0.2), 0 0 0 1px var(--background-modifier-border);
    --card-shadow-dark: 0px 0px 0px 1px var(--background-modifier-border);
    --card-border-radius-light: 8px;
    --card-border-radius-dark: 8px;
    --card-shadow-border-radius-light: calc(var(--card-border-radius-light) - 1px);
    --card-shadow-border-radius-dark: calc(var(--card-border-radius-dark) - 1px);
}

body.is-frameless:not(.is-hidden-frameless).is-fullscreen {
    padding-top: 0;
}

body:not(.is-mobile) .horizontal-main-container {
    padding-bottom: 24px;
}

body:not(.is-mobile).is-popout-window .horizontal-main-container {
    padding-bottom: 16px;
}

body:not(.is-mobile) .workspace {
    margin-right: 16px;
    position: relative;
    overflow: visible;
}

body:not(.is-mobile).is-popout-window .workspace {
    margin-right: 16px;
    margin-left: 16px;
}

body:not(.is-mobile) .mod-top-left-space .workspace-tab-container,
body.is-popout-window .workspace-tabs.mod-top-left-space>.workspace-tab-container {
    border-top-left-radius: var(--card-border-radius-light, 8px) !important;
}

body:not(.is-mobile).theme-dark .mod-top-left-space .workspace-tab-container,
body.is-popout-window.theme-dark .workspace-tabs.mod-top-left-space>.workspace-tab-container {
    border-top-left-radius: var(--card-border-radius-dark, 8px) !important;
}

body:not(.is-mobile) .mod-top-right-space .workspace-tab-container {
    border-top-right-radius: var(--card-border-radius-light, 8px);
    overflow: hidden;
}

body:not(.is-mobile).theme-dark .mod-top-right-space .workspace-tab-container {
    border-top-right-radius: var(--card-border-radius-dark, 8px);
}

body:not(.is-mobile) .workspace-split.mod-left-split,
body:not(.is-mobile) .workspace:not(.is-left-sidedock-open) .workspace-split.mod-root {
    border-bottom-left-radius: var(--card-border-radius-light, 8px);
    overflow: hidden;
}

body:not(.is-mobile).theme-dark .workspace-split.mod-left-split,
body:not(.is-mobile).theme-dark .workspace:not(.is-left-sidedock-open) .workspace-split.mod-root {
    border-bottom-left-radius: var(--card-border-radius-dark, 8px);
}

body:not(.is-mobile) .workspace:not(.is-right-sidedock-open) .workspace-split.mod-root,
body:not(.is-mobile) .workspace-split.mod-right-split {
    border-bottom-right-radius: var(--card-border-radius-light, 8px);
    overflow: hidden;
}

body:not(.is-mobile).theme-dark .workspace:not(.is-right-sidedock-open) .workspace-split.mod-root,
body:not(.is-mobile).theme-dark .workspace-split.mod-right-split {
    border-bottom-right-radius: var(--card-border-radius-dark, 8px);
}

.workspace-window .mod-top-left-space .workspace-tab-container,
.workspace-window .mod-top-left-space .workspace-tab-container .workspace-leaf-content {
    border-top-left-radius: 0px !important;
}


body:not(.is-mobile) .workspace::before {
    content: " ";
    width: calc(100% - 44px);
    height: calc(100% - 44px);
    border-radius: var(--card-shadow-border-radius-light, 7px);
    background-color: transparent;
    box-shadow: var(--card-shadow-light);
    position: absolute;
    bottom: 0px;
    right: 0px;
}

body:not(.is-mobile).theme-dark .workspace::before {
    border-radius: var(--card-shadow-border-radius-dark, 7px);
    box-shadow: var(--card-shadow-dark, 0px 0px 0px 1px var(--background-modifier-border));
}

body:not(.is-mobile).is-popout-window .workspace::before {
    width: calc(100%) !important;
    height: calc(100% - 44px);
}


.workspace-tab-header-container,
.workspace-tabs.mod-top .workspace-tab-header-container,
.workspace-ribbon.mod-left:before {
    border-bottom: 1px solid transparent;
}

body:not(.is-mobile) .workspace-ribbon {
    padding: 10px 4px 0px;
}

.workspace-tabs {
    gap: 4px;
}

.workspace-tabs:not(.mod-top) .workspace-tab-container>.workspace-leaf {
    box-shadow: inset 0px 1px 0px var(--workspace-divider-color);
}

body.theme-light.card-layout-open-light .workspace-tabs:not(.mod-top) .workspace-tab-container>.workspace-leaf,
body.theme-dark.card-layout-open-dark .workspace-tabs:not(.mod-top) .workspace-tab-container>.workspace-leaf {
    box-shadow: inset 0px 1px 0px transparent;
}

body.theme-light.mod-left-split-background-transparent-light.card-layout-open-light.tab-autohide .mod-left-split .workspace-tabs:not(.mod-top) .workspace-tab-container>.workspace-leaf,
body.theme-dark.mod-left-split-background-transparent-dark.card-layout-open-dark.tab-autohide .mod-left-split .workspace-tabs:not(.mod-top) .workspace-tab-container>.workspace-leaf,
body.theme-light.mod-right-split-background-transparent-light.card-layout-open-light.tab-autohide .mod-right-split .workspace-tabs:not(.mod-top) .workspace-tab-container>.workspace-leaf,
body.theme-dark.mod-right-split-background-transparent-dark.card-layout-open-dark.tab-autohide .mod-right-split .workspace-tabs:not(.mod-top) .workspace-tab-container>.workspace-leaf,
body.theme-light.mod-root-split-background-transparent-light.card-layout-open-light.tab-autohide .mod-root .workspace-tabs:not(.mod-top) .workspace-tab-container>.workspace-leaf,
body.theme-dark.mod-root-split-background-transparent-dark.card-layout-open-dark.tab-autohide .mod-root .workspace-tabs:not(.mod-top) .workspace-tab-container>.workspace-leaf {
    box-shadow: inset 0px 1px 0px var(--divider-color);
    border-top-left-radius: 0px !important;
    border-top-right-radius: 0px !important;
}

/* card layout light*/
body:not(.is-mobile).card-layout-open-light.theme-light .workspace::before {
    display: none;
}

body:not(.is-mobile).card-layout-open-light.theme-light .workspace.is-left-sidedock-open .workspace-split.mod-horizontal.mod-left-split {
    margin-right: 16px;
}

body:not(.is-mobile).card-layout-open-light.theme-light .workspace.is-right-sidedock-open .workspace-split.mod-horizontal.mod-right-split {
    margin-left: 16px;
}

body:not(.is-mobile).card-layout-open-light.theme-light .workspace-split.mod-vertical {
    gap: 16px
}

body:not(.is-mobile).card-layout-open-light.theme-light .workspace-tab-container {
    border-radius: var(--card-border-radius-light, 8px) !important;
    overflow: hidden !important;
    box-shadow: var(--card-shadow-light);
}

body:not(.is-mobile).card-layout-open-light.theme-light .workspace-tabs,
body:not(.is-mobile).card-layout-open-light.theme-light .workspace-split {
    overflow: visible !important;
}

body:not(.is-mobile).card-layout-open-light.theme-light .workspace-tabs,
body:not(.is-mobile).card-layout-open-light.theme-light .workspace-tab-header-container {
    background-color: transparent !important;
}


body:not(.is-mobile).card-layout-open-light.theme-light .workspace-tabs:not(.mod-top) .workspace-tab-container {
    border-top: unset;
}

/* card layout dark*/
body:not(.is-mobile).card-layout-open-dark.theme-dark .workspace::before {
    display: none;
}

body:not(.is-mobile).card-layout-open-dark.theme-dark .workspace.is-left-sidedock-open .workspace-split.mod-horizontal.mod-left-split {
    margin-right: 16px;
}

body:not(.is-mobile).card-layout-open-dark.theme-dark .workspace.is-right-sidedock-open .workspace-split.mod-horizontal.mod-right-split {
    margin-left: 16px;
}

body:not(.is-mobile).card-layout-open-dark.theme-dark .workspace-split.mod-vertical {
    gap: 16px
}

body:not(.is-mobile).theme-dark.card-layout-open-dark .workspace-tab-container {
    border-radius: var(--card-border-radius-dark, 8px) !important;
    overflow: hidden !important;
    box-shadow: var(--card-shadow-dark, 0px 0px 0px 1px var(--background-modifier-border));
}

body:not(.is-mobile).card-layout-open-dark.theme-dark .workspace-tabs,
body:not(.is-mobile).card-layout-open-dark.theme-dark .workspace-split {
    overflow: visible !important;
}

body:not(.is-mobile).card-layout-open-dark.theme-dark .workspace-tabs,
body:not(.is-mobile).card-layout-open-dark.theme-dark .workspace-tab-header-container {
    background-color: transparent !important;
}

body:not(.is-mobile).card-layout-open-dark.theme-dark .workspace-tabs:not(.mod-top) .workspace-tab-container {
    border-top: unset;
}

/* card highlight */
body:not(.is-mobile).card-highlight-light.card-layout-open-light.theme-light .mod-root:has(.workspace-tabs+.workspace-tabs) .workspace-tabs.mod-active .workspace-tab-container,
body:not(.is-mobile).card-highlight-dark.card-layout-open-dark.theme-dark .mod-root:has(.workspace-tabs+.workspace-tabs) .workspace-tabs.mod-active .workspace-tab-container {
    outline-offset: 0px;
    outline: 2px solid var(--color-accent-3);
}

/* scrollbar */
body {
    /* Scrollbars */
    --scrollbar-bg: rgba(var(--mono-rgb-100), 0);
    --scrollbar-thumb-bg: rgba(var(--mono-rgb-100), 0.1);
    --scrollbar-active-thumb-bg: rgba(var(--mono-rgb-100), 0.15);
}

body:not(.restored-scrollbars) ::-webkit-scrollbar,
body:not(.restored-scrollbars) .kanban-plugin__scroll-container::-webkit-scrollbar {
    width: 11px;
    height: 11px;
}

body:not(.restored-scrollbars) ::-webkit-scrollbar-thumb,
body:not(.restored-scrollbars) .kanban-plugin__scroll-container::-webkit-scrollbar-thumb {
    background-clip: padding-box;
    border-radius: 20px;
    border: 3px solid transparent;
    border-width: 3px 3px 3px 3px;
    min-height: 45px
}

body:not(.restored-scrollbars) .mobile-toolbar-options-container::-webkit-scrollbar {
    width: 0 !important;
    height: 0 !important;
}

body:not(.restored-scrollbars) ::-webkit-scrollbar-track,
body:not(.restored-scrollbars) ::-webkit-scrollbar-track-piece,
body:not(.restored-scrollbars):not(.is-mobile) ::-webkit-scrollbar-thumb {
    background-color: transparent !important;
}

body:not(.restored-scrollbars):not(.is-mobile) :hover::-webkit-scrollbar-thumb {
    background-color: var(--scrollbar-thumb-bg) !important;
}

body:not(.restored-scrollbars):not(.is-mobile) ::-webkit-scrollbar-thumb:hover,
body:not(.restored-scrollbars):not(.is-mobile) ::-webkit-scrollbar-thumb:active {
    background-color: var(--scrollbar-active-thumb-bg) !important;
}

.workspace-leaf-content:is([data-type="surfing-view"], [data-type="graph"], [data-type="localgraph"]) ::-webkit-scrollbar,
.scrollbar-hide ::-webkit-scrollbar {
    display: none;
}

/* ====== titlebar ===== */
body:not(.is-translucent) .titlebar-text {
    opacity: 1;
}

.is-hidden-frameless .titlebar .titlebar-button-container {
    background-color: transparent !important;
}

.mod-windows .titlebar .titlebar-button.mod-close:hover,
.mod-windows .titlebar .titlebar-button.mod-close:focus {
    background-color: var(--background-modifier-error) !important;
}

.mod-windows .titlebar .titlebar-button.mod-close:hover::after,
.mod-windows .titlebar .titlebar-button.mod-close:focus::after {
    background-color: var(--text-on-accent) !important;
}

.mod-windows .titlebar .titlebar-button.mod-close:hover,
.mod-windows .titlebar .titlebar-button.mod-close:focus {
    color: var(--text-on-accent) !important;
}

.is-hidden-frameless .titlebar-button-container.mod-right .titlebar-button {
    height: 44px;
    width: 44px;
    padding: 0;
}

.titlebar-button-container.mod-right .titlebar-button:hover {
    background-color: var(--background-modifier-hover);
}

.titlebar-button-container.mod-right .titlebar-button:last-of-type {
    margin-right: 0px;
}

.is-hidden-frameless .titlebar-button-container.mod-right {
    height: 44px;
}

.titlebar-button:not(.mod-logo)>svg {
    display: none;
}

.titlebar-button-container.mod-right .titlebar-button::after {
    content: '';
    background-color: var(--on-border-light);
    width: 16px;
    height: 16px;
    margin: auto;
}

.theme-dark .titlebar-button-container.mod-right .titlebar-button::after {
    background-color: var(--on-border-dark);
}

.titlebar-button-container.mod-right .titlebar-button.mod-close::after {
    -webkit-mask-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' fill='none' viewBox='0 0 16 16'%3e%3cg clip-path='url(%23a)'%3e%3cpath stroke='black' stroke-width='2' d='m3.757 12.243 8.486-8.486m0 8.486L3.757 3.757'/%3e%3c/g%3e%3cdefs%3e%3cclipPath id='a'%3e%3cpath fill='white' d='M0 0h8v8H0z' transform='translate(4 4)'/%3e%3c/clipPath%3e%3c/defs%3e%3c/svg%3e");
}

.titlebar-button-container.mod-right .titlebar-button.mod-maximize::after {
    -webkit-mask-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' fill='none' viewBox='0 0 16 16'%3e%3cpath stroke='black' stroke-width='2' d='M5 5h6v6H5z'/%3e%3c/svg%3e");
}

.titlebar-button-container.mod-right .titlebar-button.mod-minimize::after {
    -webkit-mask-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' fill='none' viewBox='0 0 16 16'%3e%3cpath stroke='black' stroke-width='2' d='M4 11h8'/%3e%3c/svg%3e");
}

@media (hover: hover) {
    .titlebar-button:hover {
        background-color: transparent;
    }
}

/* ====== status bar====== */

body {
    --status-bar-background: transparent;
    --status-bar-border-color: transparent;
    --status-bar-border-width: 0;
}

.status-bar {
    padding: 0 16px 0 0;
    width: calc(100% - 44px);
    min-height: 16px;
}

.status-bar-item.mod-clickable:hover {
    color: var(--status-bar-text-color);
}

.status-bar-item {
    height: 24px;
}

body.is-frameless.is-hidden-frameless:not(.qe-hide-breadcrumbs) .status-bar,
body:not(.is-frameless):not(.qe-hide-breadcrumbs) .status-bar {
    position: var(--status-bar-position);
}

/* ====== ribbon====== */
body:not(.is-mobile):not(.show-ribbon) .workspace {
    margin-left: 16px;
}

body:not(.is-mobile):not(.show-ribbon) .workspace::before {
    width: 100%;
}

.mod-macos:not(.show-ribbon) {
    --frame-left-space: calc(80px - 16px);
}

/* tab */
/* tab layout */

.mod-windows,
.mod-linux {
    --frame-right-space: 110px;
}

.workspace-tab-header-container {
    padding-left: 0px;
    padding-right: var(--size-2-3);
}

.is-hidden-frameless:not(.is-fullscreen) .workspace-tabs.mod-top-left-space .workspace-tab-header-container {
    padding-left: calc(0px + var(--frame-left-space));
}

body:not(.is-hidden-frameless):not(.is-mobile) .workspace .workspace-tabs.mod-top-right-space .workspace-tab-header-container,
.mod-macos.is-hidden-frameless .workspace .workspace-tabs.mod-top-right-space .workspace-tab-header-container {
    padding-right: 0px !important;
}

.workspace-tab-header::before,
.workspace-tab-header::after {
    display: none;
}

.sidebar-toggle-button {
    padding-top: 6px;
}

.mod-macos.is-hidden-frameless:not(.is-popout-window) .sidebar-toggle-button.mod-right {
    padding-right: 0px;
    position: unset;
}

.workspace-tab-header-tab-list {
    margin-right: 0;
}

body:not(.hider-sidebar-buttons) .workspace-tab-header-tab-list:has(+.sidebar-toggle-button.mod-right) {
    margin-right: var(--size-4-1);
}

.workspace-tab-header-inner {
    border-radius: 6px;
}

.workspace .mod-root .workspace-tab-header {
    padding: 0px 3px 2px;
}

.workspace .mod-root .workspace-tab-header:first-child {
    padding: 0px 3px 2px 0px;
}

.workspace .mod-root .workspace-tab-header-inner::after {
    background-color: var(--divider-color);
}

.mod-root .workspace-tab-header-inner {
    padding: 0 6px 0 8px
}

.mod-root .workspace-tab-header-container-inner {
    margin: 0px;
    padding: 6px 8px 4px 6px;
    height: calc(var(--header-height) + 4px);
}

.mod-left-split .workspace-tab-header-container-inner,
.mod-right-split .workspace-tab-header-container-inner {
    padding: 6px;
    margin: 0px;
    gap: 2px;
    height: 44px;
    display: flex;
    align-items: center;
    border-radius: 6px;
    overflow-y: visible;
}

.workspace-tab-header {
    cursor: var(--cursor);
}

:is(.theme-dark.card-layout-open-dark.mod-root-split-background-transparent-dark, .theme-light.card-layout-open-light.mod-root-split-background-transparent-light) .is-sidedock-collapsed,
:is(.theme-dark.card-layout-open-dark, .theme-light.card-layout-open-light) .is-sidedock-collapsed :is(.mod-top-left-space .workspace-tab-header-container, .workspace-sidedock-vault-profile) {
    visibility: hidden;
}

/* tab style */
body {
    --border-radius-activated-tab-header-light: 6px;
    --border-radius-activated-tab-header-dark: 6px;
    --color-activated-tab-header-light: var(--text-normal);
    --color-activated-tab-header-dark: var(--text-normal);
    --background-activated-tab-header-light: var(--background-primary);
    --shadow-activated-tab-header-light: 0px 0px 6px hsla(var(--accent-h), 18%, 80%, 0.4), 0px 0px 2px hsla(var(--accent-h), 18%, 80%, 0.2), 0 0 0 1px var(--background-modifier-border);
    --background-activated-tab-header-dark: var(--background-modifier-active-hover);
    --shadow-activated-tab-header-dark: inset 0 0 0 1px var(--background-modifier-border);
}


body.theme-light.color-to-tab-icon-light .workspace-tab-header.is-active svg,
body.theme-light .workspace-tab-header.is-active .workspace-tab-header-inner-icon,
body.theme-light .workspace-tab-header.is-active .workspace-tab-header-inner-title {
    color: var(--color-activated-tab-header-light) !important;
    --icon-opacity: 1;
}

body.theme-dark.color-to-tab-icon-dark .workspace-tab-header.is-active svg,
body.theme-dark .workspace-tab-header.is-active .workspace-tab-header-inner-icon,
body.theme-dark .workspace-tab-header.is-active .workspace-tab-header-inner-title {
    color: var(--color-activated-tab-header-dark) !important;
    --icon-opacity: 1;
}

body:not(.is-mobile) .clickable-icon.side-dock-ribbon-action,
body:not(.is-mobile) .sidebar-toggle-button .clickable-icon,
body:not(.is-mobile) .workspace-tab-header-tab-list .clickable-icon,
body:not(.is-mobile) .workspace-tab-header-new-tab .clickable-icon,
body:not(.is-mobile) .workspace-tab-header .workspace-tab-header-inner {
    border-radius: var(--border-radius-activated-tab-header-light);
}

body:not(.is-mobile).theme-dark .clickable-icon.side-dock-ribbon-action,
body:not(.is-mobile).theme-dark .sidebar-toggle-button .clickable-icon,
body:not(.is-mobile).theme-dark .workspace-tab-header-tab-list .clickable-icon,
body:not(.is-mobile).theme-dark .workspace-tab-header-new-tab .clickable-icon,
body:not(.is-mobile).theme-dark .workspace-tab-header .workspace-tab-header-inner {
    border-radius: var(--border-radius-activated-tab-header-dark);
}

body:not(.is-mobile) .workspace-tabs:not(.mod-stacked) .workspace-tab-header.is-active .workspace-tab-header-inner {
    background: var(--background-activated-tab-header-light);
    box-shadow: var(--shadow-activated-tab-header-light);
    border-radius: var(--border-radius-activated-tab-header-light);
}

body:not(.is-mobile).theme-dark .workspace-tabs:not(.mod-stacked) .workspace-tab-header.is-active .workspace-tab-header-inner {
    box-shadow: var(--shadow-activated-tab-header-dark);
    background: var(--background-activated-tab-header-dark);
    border-radius: var(--border-radius-activated-tab-header-dark);
}

body:not(.is-mobile) .workspace-tabs:not(.mod-stacked) .workspace-tab-header.is-active .workspace-tab-header-inner::before {
    content: "";
    width: calc(100% - 6px);
    height: 3px;
    background-color: var(--color-activated-tab-header-underline-light);
    position: absolute;
    top: 35px;
    left: 3px;
}

body:not(.is-mobile).theme-dark .workspace-tabs:not(.mod-stacked) .workspace-tab-header.is-active .workspace-tab-header-inner::before {
    content: "";
    width: calc(100% - 6px);
    height: 3px;
    background-color: var(--color-activated-tab-header-underline-dark);
    position: absolute;
    top: 35px;
    left: 3px;
}

/* tab - mobile*/

.is-mobile .workspace-tab-header.is-active .workspace-tab-header-inner {
    background: var(--background-modifier-hover);
    box-shadow: inset 0 0 0 1px var(--background-modifier-border);
}

/* divider */
body {
    --workspace-divider-color: var(--divider-color);
}

body:not(.is-mobile).card-layout-open-light.theme-light,
body:not(.is-mobile).card-layout-open-dark.theme-dark,
body.workspace-divider-transparent-light.theme-light,
body.workspace-divider-transparent-dark.theme-dark {
    --workspace-divider-color: transparent;
}

.workspace-leaf-resize-handle {
    border-color: var(--workspace-divider-color);
}


/* ====== divider vertical height ===== */
.is-hidden-frameless .workspace-split.mod-vertical>*>.workspace-leaf-resize-handle,
.is-hidden-frameless .workspace-split.mod-left-split>.workspace-leaf-resize-handle,
.is-hidden-frameless .workspace-split.mod-right-split>.workspace-leaf-resize-handle {
    height: calc(var(--divider-vertical-height) - var(--header-height) - 4px);
}

.workspace-split.mod-vertical>*>.workspace-leaf-resize-handle,
.workspace-split.mod-left-split>.workspace-leaf-resize-handle,
.workspace-split.mod-right-split>.workspace-leaf-resize-handle {
    height: calc(var(--divider-vertical-height) - 4px);
}

.workspace-split.mod-vertical:not(:has(.mod-top))>*>.workspace-leaf-resize-handle {
    height: 100% !important;
}

/* ====== autohide ===== */

/*autohide transition*/
body:not(.is-mobile) .workspace::before {
    transition: width var(--anim-out), height var(--anim-out);
}

body:not(.is-mobile):has(.mod-top>.workspace-tab-header-container:hover, .titlebar-button-container:hover, .status-bar:hover) .workspace::before {
    transition: width var(--anim-out), height var(--anim-in);
}

body:not(.is-mobile):has(.workspace-ribbon.side-dock-ribbon:hover) .workspace::before {
    transition: width var(--anim-in), height var(--anim-out);
}

/*tab autohide*/

body:not(.is-mobile).tab-autohide .workspace-tabs {
    gap: 0px;
    transition: gap var(--anim-out);
}

body:not(.is-mobile).tab-autohide :has(.workspace-tab-header-container:hover) .workspace-tabs:hover {
    gap: 4px;
    transition: gap var(--anim-in);
}

body:not(.is-mobile).tab-autohide .workspace-tab-header-container {
    height: 16px;
    opacity: 0;
    transition: height var(--anim-out), opacity var(--anim-out);
}

body:not(.is-mobile).tab-autohide .workspace-tab-header-container:hover {
    height: 40px;
    opacity: 1;
    transition: height var(--anim-in), opacity var(--anim-in);
}

body:not(.is-mobile).tab-autohide .workspace-tab-header-container-inner {
    transform: translateY(-22px);
    opacity: 0;
    transition: transform var(--anim-out), opacity var(--anim-out);
}

body:not(.is-mobile).tab-autohide .workspace-tab-header-container:hover .workspace-tab-header-container-inner,
body:not(.is-mobile).tab-autohide:has(.mod-top>.workspace-tab-header-container:hover, .titlebar-button-container:hover) .mod-top .workspace-tab-header-container-inner {
    transform: translateY(0px);
    opacity: 1;
    transition: transform var(--anim-in), opacity var(--anim-in);
}

body:not(.is-mobile).tab-autohide:not(.card-layout-open-light).theme-light .workspace-tabs:not(.mod-top) .workspace-tab-container>.workspace-leaf,
body:not(.is-mobile).tab-autohide:not(.card-layout-open-dark).theme-dark .workspace-tabs:not(.mod-top) .workspace-tab-container>.workspace-leaf {
    box-shadow: inset 0px 1px 0px transparent;
    transition: box-shadow var(--anim-out);
}

body:not(.is-mobile).tab-autohide:not(.card-layout-open-light).theme-light :has(.workspace-tab-header-container:hover) .workspace-tabs:not(.mod-top):hover .workspace-tab-container>.workspace-leaf,
body:not(.is-mobile).tab-autohide:not(.card-layout-open-dark).theme-dark :has(.workspace-tab-header-container:hover) .workspace-tabs:not(.mod-top):hover .workspace-tab-container>.workspace-leaf {
    box-shadow: inset 0px 1px 0px var(--workspace-divider-color);
    transition: box-shadow var(--anim-in);
}


/*tab autohide mod-top*/

body:not(.is-mobile).tab-autohide .workspace-split.mod-vertical>*>.workspace-leaf-resize-handle,
body:not(.is-mobile).tab-autohide .workspace-split.mod-left-split>.workspace-leaf-resize-handle,
body:not(.is-mobile).tab-autohide .workspace-split.mod-right-split>.workspace-leaf-resize-handle {
    height: calc(var(--divider-vertical-height) + 24px);
    transition: height var(--anim-out), background-color 200ms ease-in-out, border-color 200ms ease-in-out, opacity 200ms ease-in-out;
}

body:not(.is-mobile).tab-autohide.status-bar-autohide:has(.status-bar:hover) .workspace-split.mod-vertical>*>.workspace-leaf-resize-handle,
body:not(.is-mobile).tab-autohide.status-bar-autohide:has(.status-bar:hover) .workspace-split.mod-left-split>.workspace-leaf-resize-handle,
body:not(.is-mobile).tab-autohide.status-bar-autohide:has(.status-bar:hover) .workspace-split.mod-right-split>.workspace-leaf-resize-handle {
    transition: height var(--anim-in), background-color 200ms ease-in-out, border-color 200ms ease-in-out, opacity 200ms ease-in-out;
}

body:not(.is-mobile).tab-autohide:has(.mod-top>.workspace-tab-header-container:hover, .titlebar-button-container:hover) .workspace-split.mod-vertical>*>.workspace-leaf-resize-handle,
body:not(.is-mobile).tab-autohide:has(.mod-top>.workspace-tab-header-container:hover, .titlebar-button-container:hover) .workspace-split.mod-left-split>.workspace-leaf-resize-handle,
body:not(.is-mobile).tab-autohide:has(.mod-top>.workspace-tab-header-container:hover, .titlebar-button-container:hover) .workspace-split.mod-right-split>.workspace-leaf-resize-handle {
    height: calc(var(--divider-vertical-height) - 4px);
    transition: height var(--anim-in), background-color 200ms ease-in-out, border-color 200ms ease-in-out, opacity 200ms ease-in-out;
}

body:not(.is-mobile).tab-autohide.is-hidden-frameless .workspace-split.mod-vertical>*>.workspace-leaf-resize-handle,
body:not(.is-mobile).tab-autohide.is-hidden-frameless .workspace-split.mod-left-split>.workspace-leaf-resize-handle,
body:not(.is-mobile).tab-autohide.is-hidden-frameless .workspace-split.mod-right-split>.workspace-leaf-resize-handle {
    height: calc(var(--divider-vertical-height) - 16px);
    transition: height var(--anim-out), background-color 200ms ease-in-out, border-color 200ms ease-in-out, opacity 200ms ease-in-out;
}

body:not(.is-mobile).tab-autohide.status-bar-autohide:has(.status-bar:hover).is-hidden-frameless .workspace-split.mod-vertical>*>.workspace-leaf-resize-handle,
body:not(.is-mobile).tab-autohide.status-bar-autohide:has(.status-bar:hover).is-hidden-frameless .workspace-split.mod-left-split>.workspace-leaf-resize-handle,
body:not(.is-mobile).tab-autohide.status-bar-autohide:has(.status-bar:hover).is-hidden-frameless .workspace-split.mod-right-split>.workspace-leaf-resize-handle {
    transition: height var(--anim-in), background-color 200ms ease-in-out, border-color 200ms ease-in-out, opacity 200ms ease-in-out;
}

body:not(.is-mobile).tab-autohide.is-hidden-frameless:has(.mod-top>.workspace-tab-header-container:hover, .titlebar-button-container:hover) .workspace-split.mod-vertical>*>.workspace-leaf-resize-handle,
body:not(.is-mobile).tab-autohide.is-hidden-frameless:has(.mod-top>.workspace-tab-header-container:hover, .titlebar-button-container:hover) .workspace-split.mod-left-split>.workspace-leaf-resize-handle,
body:not(.is-mobile).tab-autohide.is-hidden-frameless:has(.mod-top>.workspace-tab-header-container:hover, .titlebar-button-container:hover) .workspace-split.mod-right-split>.workspace-leaf-resize-handle {
    height: calc(var(--divider-vertical-height) - var(--header-height) - 4px);
    transition: height var(--anim-in), background-color 200ms ease-in-out, border-color 200ms ease-in-out, opacity 200ms ease-in-out;
}

/*---*/

body:not(.is-mobile).tab-autohide .workspace-tabs.mod-top {
    gap: 0px;
    transition: gap var(--anim-out);
}

body:not(.is-mobile).tab-autohide.status-bar-autohide:has(.status-bar:hover) .workspace-tabs.mod-top {
    transition: gap var(--anim-in);
}

body:not(.is-mobile).tab-autohide:has(.mod-top>.workspace-tab-header-container:hover, .titlebar-button-container:hover) .workspace-tabs.mod-top {
    gap: 4px;
    transition: gap var(--anim-in);
}

body:not(.is-mobile).tab-autohide .workspace::before {
    height: calc(100% - 16px);
}

body:not(.is-mobile).tab-autohide:has(.mod-top>.workspace-tab-header-container:hover, .titlebar-button-container:hover) .workspace::before {
    height: calc(100% - 44px);
}

body:not(.is-mobile).tab-autohide.is-hidden-frameless .titlebar-button-container,
body:not(.is-mobile).tab-autohide .mod-top .workspace-tab-header-container {
    height: 16px;
    opacity: 0;
    transition: height var(--anim-out), opacity var(--anim-out);
}

body:not(.is-mobile).tab-autohide:has(.mod-top>.workspace-tab-header-container:hover, .titlebar-button-container:hover) .mod-top .workspace-tab-header-container {
    height: 40px;
    opacity: 1;
    transition: height var(--anim-in), opacity var(--anim-in);
}

body:not(.is-mobile).tab-autohide:has(.mod-top>.workspace-tab-header-container:hover, .titlebar-button-container:hover).is-hidden-frameless .titlebar-button-container {
    height: 44px;
    opacity: 1;
    transition: height var(--anim-in), opacity var(--anim-in);
}

body:not(.is-mobile).tab-autohide.status-bar-autohide:has(.status-bar:hover).is-hidden-frameless .titlebar-button-container,
body:not(.is-mobile).tab-autohide.status-bar-autohide:has(.status-bar:hover) .mod-top .workspace-tab-header-container {
    transition: height var(--anim-in), opacity var(--anim-in);
}


/*status bar autohide*/
body:not(.is-mobile).status-bar-autohide .status-bar {
    opacity: 0;
    height: 16px;
    transition: height var(--anim-out), opacity var(--anim-out);
}

body:not(.is-mobile).status-bar-autohide .status-bar:hover {
    opacity: 1;
    height: 24px;
    transition: height var(--anim-in), opacity var(--anim-in);
}

body:not(.is-mobile).status-bar-autohide .horizontal-main-container {
    padding-bottom: 16px;
    transition: padding-bottom var(--anim-out);
}

body:not(.is-mobile).status-bar-autohide:has(.status-bar:hover) .horizontal-main-container {
    padding-bottom: 24px;
    transition: padding-bottom var(--anim-in);
}

/*ribbon autohide*/
body:not(.is-mobile).Ribbon-autohide.show-ribbon .workspace-ribbon.side-dock-ribbon.mod-left {
    --ribbon-width: 16px;
    transition: all var(--anim-out);
}

body:not(.is-mobile).Ribbon-autohide.show-ribbon .workspace-ribbon.side-dock-ribbon.mod-left:hover {
    --ribbon-width: 44px;
    transition: all var(--anim-in);
}

body:not(.is-mobile).Ribbon-autohide.show-ribbon .workspace-ribbon.side-dock-ribbon.mod-left>* {
    opacity: 0;
    transition: all var(--anim-out);
}

body:not(.is-mobile).Ribbon-autohide.show-ribbon .workspace-ribbon.side-dock-ribbon.mod-left:hover>* {
    opacity: 1;
    transition: all var(--anim-in);
}

body:not(.is-mobile).Ribbon-autohide.show-ribbon .workspace::before {
    width: calc(100% - 16px);
}

body:not(.is-mobile).Ribbon-autohide.show-ribbon:has(.workspace-ribbon.side-dock-ribbon:hover) .workspace::before {
    width: calc(100% - 44px);
}

/* macos autohide fix*/
body:not(.is-mobile):not(.is-popout-window).Ribbon-autohide.show-ribbon.mod-macos.is-hidden-frameless:not(.is-fullscreen) .mod-left-split .mod-top-left-space .workspace-tab-header-container,
body:not(.is-mobile):not(.is-popout-window).Ribbon-autohide.show-ribbon.mod-macos.is-hidden-frameless:not(.is-fullscreen) .workspace:not(.is-left-sidedock-open) .sidebar-toggle-button.mod-left {
    margin-left: 28px;
}

body:not(.is-mobile):not(.is-popout-window).Ribbon-autohide.show-ribbon.mod-macos.is-hidden-frameless:not(.is-fullscreen):has(.workspace-ribbon.side-dock-ribbon:hover) .mod-left-split .mod-top-left-space .workspace-tab-header-container,
body:not(.is-mobile):not(.is-popout-window).Ribbon-autohide.show-ribbon.mod-macos.is-hidden-frameless:not(.is-fullscreen):has(.workspace-ribbon.side-dock-ribbon:hover) .workspace:not(.is-left-sidedock-open) .sidebar-toggle-button.mod-left {
    margin-left: 0px;
}

body:not(.is-mobile):not(.is-popout-window).Ribbon-autohide.show-ribbon.mod-macos.is-hidden-frameless:not(.is-fullscreen) .mod-left-split .mod-top-left-space .workspace-tab-header-container,
body:not(.is-mobile):not(.is-popout-window).Ribbon-autohide.show-ribbon.mod-macos.is-hidden-frameless:not(.is-fullscreen) .workspace:not(.is-left-sidedock-open) .sidebar-toggle-button.mod-left,
body:not(.is-mobile):not(.is-popout-window).tab-autohide.mod-macos.is-hidden-frameless:not(.is-fullscreen) .mod-left-split .mod-top-left-space .workspace-tab-header-container,
body:not(.is-mobile):not(.is-popout-window).tab-autohide.mod-macos.is-hidden-frameless:not(.is-fullscreen) .workspace:not(.is-left-sidedock-open) .sidebar-toggle-button.mod-left {
    transition: height var(--anim-out), opacity var(--anim-out), margin-left var(--anim-out);
}

body:not(.is-mobile):not(.is-popout-window).Ribbon-autohide.show-ribbon.mod-macos.is-hidden-frameless:not(.is-fullscreen):has(.workspace-ribbon.side-dock-ribbon:hover) .mod-left-split .mod-top-left-space .workspace-tab-header-container,
body:not(.is-mobile):not(.is-popout-window).Ribbon-autohide.show-ribbon.mod-macos.is-hidden-frameless:not(.is-fullscreen):has(.workspace-ribbon.side-dock-ribbon:hover) .workspace:not(.is-left-sidedock-open) .sidebar-toggle-button.mod-left {
    transition: height var(--anim-out), opacity var(--anim-out), margin-left var(--anim-in);
}

body:not(.is-mobile):not(.is-popout-window).tab-autohide.mod-macos.is-hidden-frameless:not(.is-fullscreen):has(.mod-top>.workspace-tab-header-container:hover, .titlebar-button-container:hover) .mod-left-split .mod-top-left-space .workspace-tab-header-container,
body:not(.is-mobile):not(.is-popout-window).tab-autohide.mod-macos.is-hidden-frameless:not(.is-fullscreen):has(.mod-top>.workspace-tab-header-container:hover, .titlebar-button-container:hover) .workspace:not(.is-left-sidedock-open) .sidebar-toggle-button.mod-left {
    transition: height var(--anim-in), opacity var(--anim-in), margin-left var(--anim-out);
}

/* hider plugin*/
.hider-sidebar-buttons.mod-macos .sidebar-toggle-button.mod-left {
    display: unset;
    -webkit-app-region: drag;
}

.hider-sidebar-buttons.mod-macos .sidebar-toggle-button.mod-left>.clickable-icon {
    display: none;
}

/*nav header autohide*/
body:not(.is-mobile).nav-header-autohide :is(.workspace-leaf-content, .view-content)>.nav-header .nav-buttons-container {
    height: 8px;
    opacity: 0;
    transition: height var(--anim-out), opacity var(--anim-out);
}

body:not(.is-mobile).nav-header-autohide :is(.workspace-leaf-content, .view-content)>.nav-header:hover .nav-buttons-container,
body:not(.is-mobile).nav-header-autohide .workspace-tab-header-container:hover~.workspace-tab-container :is(.workspace-leaf-content, .view-content)>.nav-header .nav-buttons-container,
body:not(.is-mobile).tab-title-bar-autohide.nav-header-autohide .mod-root .workspace-leaf-content[data-type="backlink"] .view-header:hover~.view-content>.nav-header .nav-buttons-container {
    height: 32px;
    opacity: 1;
    transition: height var(--anim-in), opacity var(--anim-in);
}

body:not(.is-mobile).nav-header-autohide :is(.workspace-leaf-content, .view-content)>.nav-header .search-input-container {
    height: 0px;
    opacity: 0;
    margin: 0px auto 0px;
    transition: height var(--anim-out), opacity var(--anim-out), margin var(--anim-out);
}

body:not(.is-mobile).nav-header-autohide :is(.workspace-leaf-content, .view-content)>.nav-header:hover .search-input-container,
body:not(.is-mobile).nav-header-autohide .workspace-tab-header-container:hover~.workspace-tab-container :is(.workspace-leaf-content, .view-content)>.nav-header .search-input-container,
body:not(.is-mobile).tab-title-bar-autohide.nav-header-autohide .mod-root .workspace-leaf-content[data-type="backlink"] .view-header:hover~.view-content>.nav-header .search-input-container {
    height: 30px;
    opacity: 1;
    margin: 6px auto 4px;
    transition: height var(--anim-in), opacity var(--anim-in), margin var(--anim-in);
    ;
}

/*view-header autohide */

body:not(.is-mobile).tab-title-bar-autohide .view-header::after {
    width: 100%;
    content: " ";
    background-color: transparent;
    height: var(--size-4-4);
    position: absolute;
    z-index: -9;
    top: 1px;
    right: 0;
}

body:not(.is-mobile).tab-title-bar-autohide .mod-root .workspace-leaf .view-header {
    height: 0;
    margin-top: -1px;
    transition: height var(--anim-out), margin var(--anim-out);
}

body:not(.is-mobile).tab-title-bar-autohide .mod-root .workspace-leaf .view-header:focus-within,
body:not(.is-mobile).tab-title-bar-autohide .mod-root .workspace-leaf .view-header:hover,
body:not(.is-mobile).tab-title-bar-autohide .mod-root .workspace-tab-header-container:hover~.workspace-tab-container .view-header {
    height: var(--header-height);
    margin-top: 0px;
    transition: height var(--anim-in), margin var(--anim-in);
}

body:not(.is-mobile).tab-title-bar-autohide .view-actions,
body:not(.is-mobile).tab-title-bar-autohide .view-header-nav-buttons,
body:not(.is-mobile).tab-title-bar-autohide .view-header-title-container {
    opacity: 0;
    transition: opacity var(--anim-out);
}

body:not(.is-mobile).tab-title-bar-autohide .mod-root .workspace-tab-header-container:hover~.workspace-tab-container .view-header :is(.view-actions, .view-header-nav-buttons, .view-header-title-container),
body:not(.is-mobile).tab-title-bar-autohide .view-header:focus-within :is(.view-actions, .view-header-nav-buttons, .view-header-title-container),
body:not(.is-mobile).tab-title-bar-autohide .view-header:hover :is(.view-actions, .view-header-nav-buttons, .view-header-title-container) {
    opacity: 1;
    transition: opacity var(--anim-in);
}

/*view-header*/

body:not(.is-mobile) .view-header {
    padding: 0 6px;
    border: 0;
}

body:not(.is-mobile) .view-header>* {
    padding-top: var(--size-2-3);
    padding-bottom: var(--size-2-1);
    --icon-size: var(--icon-m);
    --icon-stroke: var(--icon-m-stroke-width);
}

/*view-header surfing*/
body:not(.is-mobile) .workspace-leaf-content[data-type="empty"]:has(.wb-bookmark-bar) .view-header {
    margin-top: 0px !important;
    height: var(--header-height) !important;
}

body:not(.is-mobile) .workspace-leaf-content[data-type="empty"]:has(.wb-bookmark-bar) :is(.view-actions, .view-header-nav-buttons, .view-header-title-container) {
    opacity: 1;
}


/* vault profile */

body:not(.is-mobile) .workspace-split.mod-left-split {
    position: relative;
}

body:not(.is-mobile) .workspace-split.mod-left-split .workspace-tabs:last-of-type .workspace-tab-container {
    padding-bottom: 44px;
}

body:not(.is-mobile) .workspace-split.mod-left-split .workspace-sidedock-vault-profile {
    padding: 6px;
    background-color: transparent;
    position: absolute;
    bottom: 0;
    z-index: 10;
    height: 44px;
}

body:not(.is-mobile) .workspace-split.mod-left-split .workspace-sidedock-vault-profile .workspace-drawer-vault-switcher {
    height: 32px;
    cursor: pointer;
}

body:not(.is-mobile) .workspace-split.mod-left-split .workspace-sidedock-vault-profile .workspace-drawer-vault-actions {
    gap: var(--size-2-1);
}


/* vault profile autohide*/
body:not(.is-mobile).vault-profile-autohide .workspace-split.mod-left-split .workspace-sidedock-vault-profile {
    height: 16px;
    border-top: 0 solid transparent;
    opacity: 0;
    transition: height var(--anim-out), border-top var(--anim-out), opacity var(--anim-out);
}

body:not(.is-mobile).vault-profile-autohide .workspace-split.mod-left-split .workspace-sidedock-vault-profile:hover {
    height: 44px;
    border-top: var(--tab-outline-width) solid var(--tab-outline-color);
    opacity: 1;
    transition: height var(--anim-in), border-top var(--anim-in), opacity var(--anim-in);
}

body:not(.is-mobile).vault-profile-autohide .workspace-split.mod-left-split .workspace-tabs:last-of-type .workspace-tab-container {
    padding-bottom: 0px;
    transition: padding-bottom var(--anim-out);
}

body:not(.is-mobile).vault-profile-autohide:has(.workspace-sidedock-vault-profile:hover) .workspace-split.mod-left-split .workspace-tabs:last-of-type .workspace-tab-container {
    padding-bottom: 44px;
    transition: padding-bottom var(--anim-in);
}

/* ====== workspace mod root====== */
/* ====== stack mod===== */
.workspace .mod-root .workspace-tabs.mod-stacked .workspace-tab-container :is(.workspace-tab-header, .workspace-tab-header-inner, .view-header, .view-content) {
    background-color: var(--background-mod-root-split);
}

.workspace .mod-root .workspace-tabs.mod-stacked .workspace-tab-container .workspace-tab-header-inner-icon {
    width: 22px;
    height: 22px;
}

body:not(.restored-scrollbars) .mod-stacked ::-webkit-scrollbar-track {
    background-color: var(--background-primary) !important;
}

.mod-stacked .workspace-tab-header.is-active .workspace-tab-header-inner {
    box-shadow: unset;
}

.workspace .mod-root .workspace-tabs.mod-stacked .workspace-tab-container .workspace-tab-header {
    box-shadow: none;
}

.workspace .mod-root .workspace-tabs.mod-stacked .workspace-tab-container .workspace-tab-header-inner {
    border-left: 1px solid var(--divider-color);
    border-top-left-radius: var(--card-border-radius-light, 8px);
    box-shadow: var(--tab-stacked-shadow);
}

.theme-dark .workspace .mod-root .workspace-tabs.mod-stacked .workspace-tab-container .workspace-tab-header-inner {
    border-top-left-radius: var(--card-border-radius-dark, 8px);
}

.workspace .mod-root .workspace-tabs.mod-stacked .workspace-tab-container .workspace-tab-header:first-of-type .workspace-tab-header-inner {
    border-left: none;
}

.workspace-split.mod-root.workspace-window .workspace-tabs.mod-stacked .workspace-tab-container .workspace-tab-header:first-of-type .workspace-tab-header-inner {
    border-top-left-radius: 0px;
    border-left: none;
}

.workspace .workspace-tabs.mod-stacked:not(.mod-top-left-space) .workspace-tab-container .workspace-tab-header:first-of-type .workspace-tab-header-inner {
    border-top-left-radius: 0px;
    border-left: none;
}

.workspace.is-left-sidedock-open .mod-stacked .workspace-tab-header:first-of-type,
.workspace.is-left-sidedock-open .mod-stacked .workspace-tab-header:first-of-type .workspace-tab-header-inner {
    border-top-left-radius: 0px !important;
    border-left: none;
}

/* embedded backlinks */
body:not(.is-mobile) .embedded-backlinks .nav-header~.search-input-container {
    margin: 15px 0 0 0;
}

.is-mobile .embedded-backlinks .nav-header~.search-input-container {
    margin: 10px 0 0 0;
}

/* document search replace */
#cMenuToolbarModalBar.top {
    z-index: var(--layer-status-bar);
}

body:not(.is-mobile) .mod-active .document-search-container,
body:not(.is-mobile) .document-search-container {
    background-color: var(--background-primary);
}

body:not(.is-mobile) .workspace-leaf-content[data-type=markdown] .document-search-container {
    position: absolute;
    top: var(--size-4-4);
    border-radius: var(--size-4-2);
    padding: var(--size-4-2) 0;
    height: fit-content;
    box-shadow: 0px 20px 30px -10px rgba(0, 0, 0, 0.3);
    margin: 0px;
    margin-left: calc((600px - 100%) / -2);
    max-width: 600px;
    width: calc(100% - 8px);
    border: 1px solid var(--background-modifier-border);
}

body:not(.is-mobile).theme-dark .workspace-leaf-content[data-type=markdown] .document-search-container {
    box-shadow: 0px 20px 30px -10px rgba(0, 0, 0, 0.6);
}

@container (max-width: 600px) {
    body:not(.is-mobile) .workspace-leaf-content[data-type=markdown] .document-search-container {
        margin-left: 4px;
    }
}

/* canvas  */
body {
    --canvas-dot-pattern: var(--color-base-30);
    --canvas-background: transparent;
    --canvas-card-border-style: solid;
    --canvas-card-border-width: 2px;
}

.canvas-node-container {
    border-width: var(--canvas-card-border-width);
    border-style: var(--canvas-card-border-style);
}


.canvas-card-menu-left .canvas-card-menu {
    left: var(--size-4-4);
    transform: translatex(0%);
}

.canvas-card-menu-right .canvas-card-menu {
    right: var(--size-4-4);
    left: unset;
    transform: translatex(0%);
}

.canvas-controls button {
    border-bottom: 1px solid var(--divider-color);
}

.canvas-wrapper.is-screenshotting {
    --canvas-background: var(--background-primary);
}

.canvas-controls-group {
    background-color: transparent;
    border: 1px solid var(--background-modifier-border);
}

.immersive-canvas .canvas-card-menu,
.immersive-canvas .canvas-controls {
    opacity: 0;
    transition: opacity var(--anim-out);
}


.immersive-canvas .canvas-card-menu:hover,
.immersive-canvas .canvas-controls:hover {
    opacity: 1;
    transition: opacity var(--anim-in);
}


/* canvas hover effect*/
.canvas-node-container:hover {
    border-color: var(--color-accent);
}

/* canvas hover effect*/

.media-embed-card-border-off .canvas-node-container:has(> .media-embed) {
    border-width: 0;
    border-style: none;
    box-shadow: none;
}

/* graph view*/
.graph-controls {
    right: var(--size-4-2);
    top: var(--size-4-2);
}

.graph-controls.is-close {
    border: 1px solid var(--background-modifier-border);
    padding: 0px;
    border-radius: var(--radius-s);
}

.graph-controls-button.mod-open,
.graph-controls-button.mod-animate {
    width: 32px;
    height: 32px;
    border-radius: 0px;
    --icon-color: var(--text-normal);
    background-color: var(--interactive-normal);
}

.graph-controls-button.mod-animate {
    height: 33px;
}

.graph-controls-button.mod-open svg,
.graph-controls-button.mod-animate svg {
    --icon-size: var(--icon-s);
    opacity: var(--icon-opacity);
}

.graph-controls.is-close,
.workspace-split:not(.mod-root) .graph-controls.is-close {
    background-color: var(--background-primary);
    box-shadow: var(--input-shadow);
}

.graph-controls-button.mod-open:hover,
.graph-controls-button.mod-animate:hover {
    opacity: 1;
    color: var(--icon-color);
    background-color: var(--interactive-hover);
}

.graph-controls-button.mod-animate {
    border-top: 1px solid var(--divider-color);
}

.graph-controls-button.mod-animate {
    margin-top: 0px;
}

/*====== new tab ======*/
body:not(.new-tab-text-btn-restore) .workspace-leaf-content[data-type="empty"] .empty-state-action-list {
    display: flex;
    gap: 0px;
    border-radius: var(--radius-s);
    box-shadow: var(--input-shadow);
    overflow: hidden;
}

body:not(.new-tab-text-btn-restore).is-mobile.theme-light .workspace-leaf-content[data-type="empty"] .empty-state-action-list {
    box-shadow: var(--input-shadow-light);
}

body:not(.new-tab-text-btn-restore).is-mobile.theme-dark .workspace-leaf-content[data-type="empty"] .empty-state-action-list {
    box-shadow: var(--input-shadow-dark);
}

body:not(.new-tab-text-btn-restore) .workspace-leaf-content[data-type="empty"] .empty-state-action {
    font-size: 0;
    background-color: transparent !important;
    color: var(--text-muted);
    cursor: var(--cursor);
    height: 40px;
    width: 44px;
    padding: 10px 12px;
    border-radius: 0;
}

body.is-mobile:not(.new-tab-text-btn-restore) .workspace-leaf-content[data-type="empty"] .empty-state-action {
    height: 44px;
    width: 46px;
    padding: 10px 12px;
    margin: 0;
}

body:not(.new-tab-text-btn-restore) .workspace-leaf-content[data-type="empty"] .empty-state-action:nth-child(1),
body:not(.new-tab-text-btn-restore) .workspace-leaf-content[data-type="empty"] .empty-state-action:nth-child(2) {
    box-shadow: inset -1px 0px 0px var(--divider-color);
}

body:not(.new-tab-text-btn-restore) .workspace-leaf-content[data-type="empty"] .empty-state-action:hover {
    background-color: var(--background-modifier-hover) !important;
    color: var(--text-normal);
}

body.is-mobile:not(.new-tab-text-btn-restore) .workspace-leaf-content[data-type="empty"] .empty-state-action:before {
    height: 24px !important;
    width: 24px !important;
}

body:not(.new-tab-text-btn-restore) .workspace-leaf-content[data-type="empty"] .empty-state-action:nth-child(1):before {
    content: " ";
    display: block;
    height: 20px;
    width: 20px;
    background-color: var(--icon-color);
    -webkit-mask-image: url('data:image/svg+xml;utf8,<?xml version="1.0" encoding="UTF-8"?><svg id="svg0" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20"><defs><style>.cls-1{fill:none;}</style></defs><path d="M15,19.17H3.33c-1.38,0-2.5-1.12-2.5-2.5V5c0-1.38,1.12-2.5,2.5-2.5h5.83c.46,0,.83,.37,.83,.83s-.37,.83-.83,.83H3.33c-.46,0-.83,.37-.83,.83v11.67c0,.46,.37,.83,.83,.83H15c.46,0,.83-.37,.83-.83v-5.83c0-.46,.37-.83,.83-.83s.83,.37,.83,.83v5.83c0,1.38-1.12,2.5-2.5,2.5Z"/><path d="M6.67,14.17c-.22,0-.43-.09-.59-.24-.21-.21-.29-.51-.22-.79l.83-3.33c.04-.15,.11-.28,.22-.39L14.83,1.49h0c1.01-1.01,2.66-1.01,3.68,0,.49,.49,.76,1.14,.76,1.84s-.27,1.35-.76,1.84l-7.92,7.92c-.11,.11-.24,.18-.39,.22l-3.33,.83c-.07,.02-.13,.03-.2,.03Zm1.59-3.74l-.44,1.76,1.76-.44,7.75-7.75c.18-.18,.27-.41,.27-.66s-.1-.48-.27-.66c-.36-.36-.96-.36-1.32,0l-7.75,7.75Z"/><rect class="cls-1" width="20" height="20"/></svg>');
}

body:not(.new-tab-text-btn-restore) .workspace-leaf-content[data-type="empty"] .empty-state-action:nth-child(2):before {
    content: " ";
    display: block;
    height: 20px;
    width: 20px;
    background-color: var(--icon-color);
    -webkit-mask-image: url('data:image/svg+xml;utf8,<?xml version="1.0" encoding="UTF-8"?><svg id="svg0" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20"><defs><style>.cls-1{fill:none;}</style></defs><g><path d="M17.26,5.66L12.67,1.08c-.16-.16-.37-.24-.59-.24H5c-1.38,0-2.5,1.12-2.5,2.5v2.5c0,.46,.37,.83,.83,.83s.83-.37,.83-.83V3.33c0-.46,.37-.83,.83-.83h5.83V6.67c0,.46,.37,.83,.83,.83h4.17v9.17c0,.46-.37,.83-.83,.83H3.33c-.46,0-.83,.37-.83,.83s.37,.83,.83,.83H15c1.38,0,2.5-1.12,2.5-2.5V6.25c0-.22-.09-.43-.24-.59Zm-4.76-2.4l2.57,2.57h-2.57V3.26Z"/><path d="M5.85,14.53l1.06,1.06c.16,.16,.38,.24,.59,.24s.43-.08,.59-.24c.33-.33,.33-.85,0-1.18l-1.06-1.06c.29-.5,.47-1.07,.47-1.68,0-1.84-1.5-3.33-3.33-3.33s-3.33,1.49-3.33,3.33,1.5,3.33,3.33,3.33c.62,0,1.19-.18,1.68-.47Zm-3.35-2.86c0-.92,.75-1.67,1.67-1.67s1.67,.75,1.67,1.67-.75,1.67-1.67,1.67-1.67-.75-1.67-1.67Z"/></g><rect class="cls-1" width="20" height="20"/></svg>');
}

body:not(.new-tab-text-btn-restore) .workspace-leaf-content[data-type="empty"] .empty-state-action:nth-child(3) {
    display: none;
}

body:not(.new-tab-text-btn-restore) .workspace-leaf-content[data-type="empty"] .empty-state-action:nth-child(4):before {
    content: " ";
    display: block;
    height: 20px;
    width: 20px;
    background-color: var(--icon-color);
    -webkit-mask-image: url('data:image/svg+xml;utf8,<?xml version="1.0" encoding="UTF-8"?><svg id="svg0" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20"><defs><style>.cls-1{fill:none;}</style></defs><path d="M11.18,10l5.24-5.24c.33-.33,.33-.85,0-1.18s-.85-.33-1.18,0l-5.24,5.24L4.76,3.58c-.33-.33-.85-.33-1.18,0s-.33,.85,0,1.18l5.24,5.24L3.58,15.24c-.33,.33-.33,.85,0,1.18,.16,.16,.38,.24,.59,.24s.43-.08,.59-.24l5.24-5.24,5.24,5.24c.16,.16,.38,.24,.59,.24s.43-.08,.59-.24c.33-.33,.33-.85,0-1.18l-5.24-5.24Z"/><rect class="cls-1" width="20" height="20"/></svg>');
}

/*new tab image*/
.workspace-leaf-content[data-type="empty"] .empty-state::before {
    content: "";
    width: 100%;
    height: 30%;
    background-color: var(--icon-color);
    -webkit-mask-size: contain;
    -webkit-mask-position: center;
    -webkit-mask-repeat: no-repeat;
    -webkit-mask-image: url("data:image/svg+xml,%3Csvg%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20width%3D%2280%22%20height%3D%2280%22%20fill%3D%22none%22%20viewBox%3D%220%200%2080%2080%22%3E%3Cpath%20fill%3D%22%23000%22%20fill-opacity%3D%22.1%22%20fill-rule%3D%22evenodd%22%20d%3D%22M41.025%2044.046c-1.565-4.198-2.066-7.455-1.95-10.13.126-2.9.975-5.058%201.89-6.923.243-.495.485-.96.717-1.406.66-1.27%201.243-2.393%201.57-3.657.398-1.537.4-3.279-.519-5.633a3.197%203.197%200%200%200-3.486.523l-9.875%208.884a3.197%203.197%200%200%200-1.023%201.9c-.118.78-.259%201.752-.404%202.757-.25%201.72-.512%203.537-.7%204.67%202.313%201.47%205.437%203.94%207.093%207.98.226.553.424%201.132.589%201.74%202.033-.46%204.085-.765%206.098-.705Zm1.587.128c.957.129%201.901.355%202.827.701%203.307%201.238%206.25%203.964%208.665%208.966l.08-.12a139.935%20139.935%200%200%200%204.196-6.627%201.618%201.618%200%200%200-.122-1.797c-1.03-1.364-3-4.138-4.074-6.706-1.104-2.64-1.269-6.74-1.278-8.736a3.406%203.406%200%200%200-.714-2.097l-7.383-9.38a9.237%209.237%200%200%201-.165%203.912c-.371%201.437-1.062%202.763-1.744%204.075a60.2%2060.2%200%200%200-.644%201.26c-.87%201.771-1.631%203.728-1.745%206.353-.111%202.562.393%205.815%202.101%2010.196Zm4.66%2019.708c2.13.59%204.273-1.12%204.567-3.31.218-1.618.633-3.462%201.408-5.16-2.394-5.402-5.261-8.05-8.312-9.19-2.97-1.112-6.244-.85-9.698-.068.666%203.863.018%208.748-3%2014.906.*************.483.072l4.844.367c2.362.182%204.92.956%207.328%201.684.816.246%201.613.488%202.38.7Zm-16.329-3.449c3.777-7.702%203.612-13.101%202.065-16.876-1.404-3.426-3.996-5.637-6.134-7.054-.91%202.125-5.19%2011.535-5.19%2011.535a3.196%203.196%200%200%200%20.622%203.535l8.348%208.597c.***************.29.263Z%22%20clip-rule%3D%22evenodd%22%2F%3E%3C%2Fsvg%3E");
}

.new-tab-image-old .workspace-leaf-content[data-type="empty"] .empty-state::before {
    -webkit-mask-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' width='240' height='240' fill='none' viewBox='0 0 240 240'%3e%3cg fill='black' clip-path='url(%23a)'%3e%3cpath fill-opacity='.25' fill-rule='evenodd' d='M67.793 61.072A80.673 80.673 0 0 1 95.85 45.667v32.798H75.54l-7.748-17.393Zm-28.703 58.26a3.98 3.98 0 0 0 2.737-.18l4.546-2.025 7.425 52.553C44.474 156.487 39 140.377 39 123c0-1.23.03-2.453.09-3.668Zm60.322 73.005h-21.3c12.038 7.29 26.13 11.535 41.198 11.655-8.453-.24-15.81-4.852-19.898-11.655Zm62.476 0c-12.038 7.29-26.131 11.535-41.198 11.655 8.453-.24 15.81-4.852 19.898-11.655h21.3Zm28.049-28.454C196.972 151.883 201 137.91 201 123c0-1.747-.053-3.472-.165-5.19l-14.918-5.43v9.293h3.128c3.622 0 6.405 3.202 5.895 6.787l-5.003 35.423Zm-22.312-85.418H147.15v-31.8a80.945 80.945 0 0 1 26.385 15.548l-5.91 16.252Z' clip-rule='evenodd'/%3e%3cpath fill-opacity='.4' d='M185.917 112.38v9.293H100.83a5.96 5.96 0 0 1-5.895-5.115l-1.47-10.403a5.947 5.947 0 0 0-5.895-5.115h-5.31a3.954 3.954 0 0 0 1.215-4.748L75.54 78.466h20.31v17.46a3.945 3.945 0 0 0 3.945 3.945h43.41a3.945 3.945 0 0 0 3.945-3.945v-17.46h20.475l-7.237 19.882-.173.48a3.936 3.936 0 0 0 2.355 5.055l23.347 8.498Zm-67.312-57.855s-4.23-8.648-5.76-12.21c-.277-.637-.465-1.117-.525-1.35 0 0-.285-.72-1.298-.72-1.012 0-1.267.645-1.267.645-.083.3-.36.968-.765 1.86-1.74 3.833-5.745 11.775-5.745 11.775-.36.742-1.245 1.44-1.245 1.943 0 .412.217.532.817.532h2.04c.6 0 .84-.12.84-.532 0-.503-.84-1.178-.84-1.943l1.321-2.76h6.697l1.32 2.76c0 .765-.84 1.44-.84 1.943 0 .412.21.532.84.532h4.823c.6 0 .839-.12.839-.532 0-.503-.915-1.2-1.252-1.943ZM106.822 50.4l2.693-5.737 2.708 5.737h-5.401Z'/%3e%3cg fill-opacity='.1'%3e%3cpath d='M135.315 40.958V29.123h-35.52a3.945 3.945 0 0 0-3.945 3.945v62.857a3.945 3.945 0 0 0 3.945 3.945h43.41a3.945 3.945 0 0 0 3.945-3.945V40.958h-11.835Zm-32.07 13.567s4.005-7.942 5.745-11.775c.405-.892.683-1.56.765-1.86 0 0 .263-.645 1.268-.645s1.297.72 1.297.72c.06.232.248.712.525 1.35 1.53 3.563 5.76 12.21 5.76 12.21.338.742 1.253 1.44 1.253 1.943 0 .412-.24.532-.84.532h-4.823c-.63 0-.84-.12-.84-.532 0-.503.84-1.178.84-1.943l-1.32-2.76h-6.697l-1.32 2.76c0 .765.839 1.44.839 1.943 0 .412-.239.532-.839.532h-2.04c-.6 0-.818-.12-.818-.532 0-.503.885-1.2 1.245-1.943ZM141 93h-39v-2.25h39V93Zm0-9h-39v-2.25h39V84Zm0-9h-39v-2.25h39V75Zm0-9h-39v-2.25h39V66Zm0-9h-18v-2.25h18V57Zm0-9h-18v-2.25h18V48Z'/%3e%3cpath d='m109.515 44.663-2.693 5.737h5.401l-2.708-5.737Z'/%3e%3c/g%3e%3cpath fill-opacity='.3' d='m67.823 76.245-4.808-10.808-3.428-7.694a4.49 4.49 0 0 0-2.505-2.37 4.458 4.458 0 0 0-3.442.09L26.235 67.665a4.51 4.51 0 0 0-2.28 5.94l8.235 18.502a4.502 4.502 0 0 0 5.94 2.28l7.402-3.292L57 85.987l8.543-3.802a4.492 4.492 0 0 0 2.647-3.72 4.43 4.43 0 0 0-.367-2.22Zm-5.715-7.313 1.83 4.118-8.91 3.96-.66-1.485-1.17-2.625 4.957-2.205 3.945-1.762h.008Zm-2.753-6.165 1.83 4.11-8.903 3.968-1.83-4.11 8.903-3.968Zm-8.212 11.048 1.71 3.855.12.255-.405.18L43.38 82.2l-1.83-4.11 9.593-4.275Zm-10.508 2.22-1.83-4.11 9.593-4.275 1.83 4.11-9.593 4.275Zm13.92-18.517c.292-.128.608-.196.915-.196.278 0 .547.053.81.15a2.22 2.22 0 0 1 1.245 1.185l.915 2.056-8.902 3.967-1.83-4.118 6.847-3.044Zm-8.91 3.967 1.837 4.11-9.592 4.275-1.837-4.118 9.592-4.267ZM26.01 72.69a2.236 2.236 0 0 1-.045-1.718 2.258 2.258 0 0 1 1.185-1.252l6.848-3.052 1.837 4.117-8.91 3.968-.915-2.063Zm1.83 4.118 8.91-3.968 1.83 4.11-8.91 3.968-1.83-4.11Zm4.575 10.274-1.83-4.11 8.91-3.967 1.83 4.11-8.91 3.968Zm4.8 5.25a2.235 2.235 0 0 1-1.718.046 2.258 2.258 0 0 1-1.252-1.186l-.915-2.054 8.91-3.968 1.83 4.11-6.855 3.052Zm9.712-4.327-.802.36-1.83-4.11 6.12-2.722 3.473-1.553 1.83 4.117-8.79 3.908Zm18.998-9.54a2.224 2.224 0 0 1-1.297 1.665l-6.855 3.053-.383-.863-1.447-3.255 8.91-3.96.915 2.055c.187.428.232.877.157 1.305Z'/%3e%3cpath fill-opacity='.2' d='M57.525 58.657a2.22 2.22 0 0 0-1.245-1.184 2.313 2.313 0 0 0-.81-.15c-.307 0-.623.067-.915.194l-6.847 3.045 1.83 4.118 8.902-3.968-.915-2.055Zm-21.472 7.095 1.837 4.118 9.593-4.275-1.838-4.11-9.593 4.267ZM27.15 69.72c-.547.248-.967.69-1.185 1.252a2.236 2.236 0 0 0 .045 1.718l.915 2.063 8.91-3.968-1.837-4.117-6.848 3.052Z'/%3e%3cpath fill-opacity='.1' d='M83.475 96.293 75.54 78.465l-7.748-17.393-6.675-15-10.814 4.816-4.816-10.816-32.444 14.445a3.943 3.943 0 0 0-1.995 5.205l25.567 57.428a3.957 3.957 0 0 0 2.475 2.183c.87.255 1.837.217 2.737-.181l4.546-2.024-.893-6.308-4.133 1.838-.922-2.055 4.733-2.108-.098-.667c-.503-3.585 2.273-6.788 5.895-6.788h10.95l14.153-6.3.914 2.055-9.532 4.245h14.82a3.954 3.954 0 0 0 1.215-4.748Zm-65.01-35.018L41.078 51.21l.914 2.055L19.38 63.33l-.915-2.055Zm17.842 33.503a4.502 4.502 0 0 1-4.117-2.67l-8.235-18.503a4.51 4.51 0 0 1 2.28-5.94L53.64 55.462a4.458 4.458 0 0 1 3.443-.09 4.49 4.49 0 0 1 2.505 2.37l3.427 7.696 4.808 10.807a4.43 4.43 0 0 1 .367 2.22 4.492 4.492 0 0 1-2.647 3.72L57 85.987l-11.468 5.108-7.402 3.293c-.585.262-1.2.39-1.823.39ZM57 95.835l-15.443 6.877-3.877 1.726-.915-2.056 5.587-2.49L57 93.376l15.397-6.855.915 2.055L57 95.835Z'/%3e%3cpath fill-opacity='.05' d='m36.746 72.85-8.907 3.965 1.83 4.111 8.907-3.965-1.83-4.111Zm11.648-5.192L38.8 71.93l1.83 4.11 9.593-4.27-1.83-4.11Zm10.965-4.89-8.907 3.966 1.83 4.11 8.907-3.965-1.83-4.11ZM53.197 72.9l1.17 2.625.66 1.485 8.91-3.96-1.83-4.117H62.1l-3.945 1.762-4.958 2.205Zm-.629 5.205.404-.18-.12-.255-1.71-3.855-9.592 4.275 1.83 4.11 9.188-4.088v-.007Zm-13.08.91-8.907 3.966 1.83 4.11 8.908-3.965-1.83-4.11Zm2.752 6.155-8.91 3.968.915 2.054c.248.548.69.968 1.253 1.186.562.217 1.17.195 1.717-.046l6.855-3.052-1.83-4.11Zm8.175-3.645v.008l-6.12 2.722 1.83 4.11.803-.352v-.008l8.79-3.907-1.83-4.118-3.473 1.545Zm6.975.795.383.863 6.854-3.053a2.224 2.224 0 0 0 1.298-1.665c.075-.427.03-.877-.157-1.305l-.915-2.055-8.91 3.96 1.447 3.255Z'/%3e%3cpath fill-opacity='.1' d='m216.098 60.667 4.042-11.122-33.368-12.15a3.954 3.954 0 0 0-5.062 2.355l-3.78 10.388-4.395 12.075-5.91 16.252-7.237 19.882-.173.48a3.936 3.936 0 0 0 2.355 5.055l23.347 8.498 14.918 5.43 2.52.915a3.946 3.946 0 0 0 5.063-2.355l.172-.48 17.55-48.203 1.08-2.97-11.122-4.05ZM185.977 46.02l25.373 9.232-.773 2.115-25.372-9.232.772-2.115Zm17.663 65.49-3.653-1.328-14.07-5.122-18.922-6.885.765-2.115 18.157 6.607 13.62 4.951 4.875 1.777-.772 2.115Zm11.505-31.598-7.118 19.545a3.89 3.89 0 0 1-4.995 2.333l-5.407-1.965-11.708-4.267-12.202-4.44a3.884 3.884 0 0 1-2.333-4.995l2.783-7.658 4.223-11.603.105-.284a3.894 3.894 0 0 1 5.002-2.333l29.317 10.672a3.897 3.897 0 0 1 2.333 4.995Zm3.63-9.982L182.13 56.595l.765-2.115 36.653 13.335-.773 2.115Z'/%3e%3cpath fill-opacity='.25' d='m212.813 74.918-29.318-10.673a3.894 3.894 0 0 0-5.002 2.332l-.105.285-4.223 11.603-2.782 7.657a3.884 3.884 0 0 0 2.332 4.996l12.203 4.44 11.707 4.26 5.407 1.972a3.891 3.891 0 0 0 4.996-2.333l7.117-19.544a3.896 3.896 0 0 0-2.332-4.995Zm-31.763-3.953c.037-.105.083-.21.128-.308.037-.075.067-.15.112-.217.045-.082.09-.157.143-.233 0 0 .022-.03.03-.044.03-.045.06-.09.097-.128a2.55 2.55 0 0 1 .225-.255c.053-.06.113-.12.173-.165.052-.06.112-.105.172-.15.18-.135.375-.255.593-.36.825-.36 1.635-.398 2.49-.083.15.053.3.12.435.195.082.038.157.083.225.128.165.097.314.21.449.338a3.19 3.19 0 0 1 .586.712c.052.098.105.195.157.3.368.818.405 1.635.09 2.49-.308.855-.862 1.455-1.672 1.852-.053.023-.113.045-.165.068-.008.007-.015.007-.03.007-.068.03-.128.053-.196.076-.127.037-.247.075-.382.09-.09.022-.187.037-.277.044-.473.053-.945-.014-1.44-.195-.855-.307-1.455-.862-1.853-1.672-.367-.825-.405-1.635-.09-2.49Zm23.317 27.157-8.295-3.014-10.154-3.698-10.868-3.953 1.417-3.914 6.893-4.006.127-.075.593.66.593.66 2.752 3.068 2.76-1.313 10.635-5.07 6.863 11.543-3.316 9.112Z'/%3e%3cpath fill-opacity='.05' d='m207.682 89.01-3.315 9.112-8.295-3.014-10.155-3.698-10.867-3.953 1.417-3.914 6.893-4.006.127-.075.593.66.592.66 2.753 3.068 2.76-1.313 10.635-5.07 6.862 11.543Zm-20.527-15.825c-.308.855-.863 1.455-1.673 1.852-.052.023-.112.045-.165.068-.007.007-.015.007-.03.007-.067.03-.127.053-.195.076-.127.037-.247.075-.382.09-.09.022-.188.037-.278.044-.472.053-.945-.014-1.44-.195-.855-.307-1.455-.862-1.852-1.672-.368-.825-.405-1.635-.09-2.49.037-.105.082-.21.127-.308.03-.075.068-.15.113-.217.045-.082.09-.157.142-.233 0 0 .023-.03.03-.044.03-.045.06-.09.098-.128.067-.09.142-.172.225-.255.052-.06.112-.12.172-.165.053-.052.113-.105.173-.15.18-.135.375-.255.592-.36.825-.36 1.635-.398 2.49-.083.15.053.3.12.435.195.083.038.158.083.225.128.165.097.315.21.45.338.075.067.143.134.21.21.135.15.255.322.375.502.053.098.105.195.158.3.367.818.405 1.635.09 2.49Z'/%3e%3cpath fill-opacity='.3' d='M147.15 40.958h-11.835V29.13l11.835 11.828Zm-96.847 9.929 10.815-4.815-15.63-6 4.815 10.815ZM227.22 64.718l-11.122-4.05 4.042-11.123 7.08 15.172Z'/%3e%3cpath fill-opacity='.15' d='m194.94 128.46-5.002 35.422-3.3 23.333a5.949 5.949 0 0 1-5.895 5.123h-40.156c.12-.203.24-.398.353-.608A23.85 23.85 0 0 0 144 180c0-13.26-10.747-24-24-24s-24 10.74-24 24c0 4.26 1.11 8.265 3.06 11.73.112.21.233.405.352.608H62.167a5.96 5.96 0 0 1-5.895-5.123l-2.474-17.535-7.425-52.553-.893-6.307-.322-2.325-.098-.667c-.503-3.585 2.273-6.788 5.895-6.788H87.57a5.947 5.947 0 0 1 5.895 5.115l1.47 10.403a5.959 5.959 0 0 0 5.895 5.114h88.215c3.622 0 6.405 3.203 5.895 6.788Z'/%3e%3cpath fill-opacity='.4' d='M120 156c-13.253 0-24 10.74-24 24 0 4.26 1.11 8.265 3.06 11.73.112.21.233.405.352.608 4.088 6.802 11.446 11.414 19.898 11.654h1.38c8.452-.24 15.81-4.852 19.897-11.654.12-.203.24-.398.353-.608A23.85 23.85 0 0 0 144 180c0-13.26-10.747-24-24-24Zm13.65 35.73c-.173.21-.36.413-.547.608A17.928 17.928 0 0 1 120 198a17.908 17.908 0 0 1-13.095-5.662 8.574 8.574 0 0 1-.555-.608A17.935 17.935 0 0 1 102 180c0-9.945 8.062-18 18-18s18 8.055 18 18c0 4.485-1.635 8.58-4.35 11.73Z'/%3e%3cpath fill-opacity='.5' d='M120 162c-9.938 0-18 8.055-18 18 0 4.485 1.643 8.58 4.35 ************.36.413.555.608A17.92 17.92 0 0 0 120 198a17.94 17.94 0 0 0 13.103-5.662c.187-.195.374-.398.547-.608A17.893 17.893 0 0 0 138 180c0-9.945-8.055-18-18-18Zm7.5 19.5h-6v6c0 .825-.675 1.5-1.5 1.5s-1.5-.675-1.5-1.5v-6h-6c-.825 0-1.5-.675-1.5-1.5s.675-1.5 1.5-1.5h6v-6a1.5 1.5 0 1 1 3 0v6h6a1.5 1.5 0 1 1 0 3Z'/%3e%3c/g%3e%3cdefs%3e%3cclipPath id='a'%3e%3cpath fill='white' d='M0 0h240v240H0z'/%3e%3c/clipPath%3e%3c/defs%3e%3c/svg%3e");
}

.new-tab-image-customize .workspace-leaf-content[data-type="empty"] .empty-state::before {
    background-color: unset;
    background-size: contain;
    background-position: center;
    background-repeat: no-repeat;
    background-image: var(--new-tab-image);
    -webkit-mask: unset;
}

.new-tab-image-none .workspace-leaf-content[data-type="empty"] .empty-state::before {
    display: none;
}

.new-tab-image-none .workspace-leaf-content[data-type="empty"] .empty-state-title {
    display: block;
}

body {
    --new-tab-image: url("data:image/svg+xml,%3Csvg%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20width%3D%2280%22%20height%3D%2280%22%20fill%3D%22none%22%20viewBox%3D%220%200%2080%2080%22%3E%3Cg%20filter%3D%22url(%23a)%22%3E%3Cpath%20fill%3D%22%23000%22%20fill-opacity%3D%22.26%22%20d%3D%22M51.894%2060.617c-.291%202.162-2.41%203.85-4.515%203.267-3-.825-6.474-2.112-9.6-2.352l-4.79-.362a3.163%203.163%200%200%201-2.042-.95l-8.254-8.483a3.15%203.15%200%200%201-.616-3.49S27.18%2037.053%2027.37%2036.47c.19-.582.886-5.66%201.298-8.388a3.153%203.153%200%200%201%201.011-1.875l9.765-8.767a3.165%203.165%200%200%201%204.597.394l8.202%2010.4c.464.59.702%201.32.706%202.07.009%201.97.172%206.016%201.263%208.621%201.062%202.535%203.01%205.272%204.03%206.619.39.517.45%201.216.12%201.773a138.06%20138.06%200%200%201-4.15%206.54c-1.386%202.063-2.028%204.613-2.318%206.761Z%22%2F%3E%3C%2Fg%3E%3Cpath%20fill%3D%22%230E0E0E%22%20d%3D%22M51.602%2060.187c-.292%202.171-2.417%203.867-4.528%203.282-3.008-.829-6.491-2.122-9.626-2.362l-4.803-.365a3.168%203.168%200%200%201-2.048-.953l-8.277-8.523a3.168%203.168%200%200%201-.618-3.504S26.82%2036.515%2027.01%2035.93c.19-.585.888-5.686%201.301-8.427a3.17%203.17%200%200%201%201.015-1.883l9.79-8.807a3.17%203.17%200%200%201%204.61.396l8.226%2010.448c.465.59.704%201.326.707%202.078.01%201.98.173%206.043%201.268%208.66%201.064%202.547%203.018%205.296%204.04%206.65a1.6%201.6%200%200%201%20.12%201.78%20137.46%20137.46%200%200%201-4.16%206.57c-1.39%202.072-2.034%204.634-2.325%206.792Z%22%2F%3E%3Cpath%20fill%3D%22url(%23b)%22%20d%3D%22M31.489%2060.433c3.844-7.804%203.736-13.395%202.1-17.384-1.505-3.672-4.304-5.988-6.51-7.425-.047.208-.115.41-.202.605l-5.175%2011.532a3.168%203.168%200%200%200%20.618%203.505l8.277%208.523c.26.267.562.484.892.644Z%22%2F%3E%3Cpath%20fill%3D%22url(%23c)%22%20d%3D%22M47.075%2063.469c2.11.585%204.235-1.11%204.527-3.282.252-1.869.769-4.041%201.806-5.936-2.38-5.121-5.256-7.777-8.41-8.954-3.339-1.246-6.987-.835-10.684.063.826%203.76.331%208.673-2.82%2015.073.358.174.75.28%201.154.309%200%200%202.275.191%204.98.383%202.705.191%206.73%201.59%209.447%202.344Z%22%2F%3E%3Cpath%20fill%3D%22url(%23d)%22%20d%3D%22M41.95%2044.595c1.038.108%202.056.332%203.047.702%203.154%201.177%206.032%203.833%208.411%208.954.16-.292.333-.578.519-.855a138.877%20138.877%200%200%200%204.16-6.57%201.603%201.603%200%200%200-.12-1.78c-1.022-1.354-2.976-4.103-4.04-6.65-1.095-2.617-1.258-6.68-1.267-8.66a3.377%203.377%200%200%200-.708-2.079L43.727%2017.21a3.198%203.198%200%200%200-.136-.162c.603%201.978.562%203.569.19%205.015-.345%201.341-.975%202.558-1.641%203.843-.223.43-.45.87-.672%201.323-.884%201.806-1.68%203.85-1.798%206.592-.118%202.74.444%206.179%202.28%2010.774Z%22%2F%3E%3Cpath%20fill%3D%22url(%23e)%22%20d%3D%22M41.948%2044.594c-1.835-4.595-2.398-8.033-2.28-10.774.118-2.742.915-4.786%201.798-6.592.222-.454.45-.893.673-1.324.665-1.285%201.295-2.501%201.64-3.842.373-1.447.414-3.038-.19-5.017a3.17%203.17%200%200%200-4.472-.232l-9.792%208.807a3.17%203.17%200%200%200-1.014%201.883l-1.192%207.902a3.182%203.182%200%200%201-.04.218c2.206%201.438%205.006%203.754%206.511%207.426.294.717.54%201.486.72%202.31%202.6-.632%205.177-1.022%207.638-.765Z%22%2F%3E%3Cg%20fill-rule%3D%22evenodd%22%20clip-rule%3D%22evenodd%22%3E%3Cpath%20fill%3D%22url(%23f)%22%20d%3D%22M39.687%2033.638c-.118%202.72.221%205.839%202.053%2010.424l-.575-.052c-1.643-4.785-2.001-7.238-1.881-9.995.12-2.758%201.01-4.879%201.899-6.688.225-.458.75-1.319.974-1.75.665-1.28%201.108-1.955%201.488-3.124.53-1.633.416-2.406.355-3.176.422%202.783-1.178%205.202-2.388%207.667-.882%201.795-1.807%203.976-1.925%206.694Z%22%2F%3E%3Cpath%20fill%3D%22url(%23g)%22%20d%3D%22M34.131%2043.25c.217.502.422.907.552%201.529l-.48.108c-.2-.726-.354-1.242-.63-1.865-1.654-3.902-4.309-5.91-6.478-7.381%202.62%201.41%205.31%203.616%207.036%207.608Z%22%2F%3E%3Cpath%20fill%3D%22url(%23h)%22%20d%3D%22M34.71%2045.27c.917%204.264-.105%209.683-3.118%2014.95%202.518-5.22%203.74-10.234%202.723-14.866l.396-.085Z%22%2F%3E%3Cpath%20fill%3D%22url(%23i)%22%20d%3D%22M45.11%2044.888c4.94%201.848%206.842%205.908%208.264%209.294-1.756-3.546-4.197-7.462-8.441-8.915-3.23-1.106-5.957-.975-10.619.083l-.104-.462c4.948-1.128%207.535-1.26%2010.9%200Z%22%2F%3E%3C%2Fg%3E%3Cdefs%3E%3CradialGradient%20id%3D%22b%22%20cx%3D%220%22%20cy%3D%220%22%20r%3D%221%22%20gradientTransform%3D%22matrix(-5.44827%20-20.95522%2013.96424%20-3.63065%2031.076%2059.662)%22%20gradientUnits%3D%22userSpaceOnUse%22%3E%3Cstop%20stop-color%3D%22%23fff%22%20stop-opacity%3D%22.44%22%2F%3E%3Cstop%20offset%3D%221%22%20stop-color%3D%22%23333%22%20stop-opacity%3D%22.52%22%2F%3E%3C%2FradialGradient%3E%3CradialGradient%20id%3D%22c%22%20cx%3D%220%22%20cy%3D%220%22%20r%3D%221%22%20gradientTransform%3D%22matrix(-9.24927%20-16.1868%2016.18663%20-9.24917%2047.535%2064.623)%22%20gradientUnits%3D%22userSpaceOnUse%22%3E%3Cstop%20stop-color%3D%22%23565656%22%2F%3E%3Cstop%20offset%3D%221%22%20stop-color%3D%22%23151515%22%20stop-opacity%3D%22.71%22%2F%3E%3C%2FradialGradient%3E%3CradialGradient%20id%3D%22d%22%20cx%3D%220%22%20cy%3D%220%22%20r%3D%221%22%20gradientTransform%3D%22matrix(4.6468%20-35.22462%2026.02364%203.433%2049.53%2050.76)%22%20gradientUnits%3D%22userSpaceOnUse%22%3E%3Cstop%20stop-color%3D%22%237E7E7E%22%20stop-opacity%3D%22.91%22%2F%3E%3Cstop%20offset%3D%221%22%20stop-color%3D%22%231E1E1E%22%20stop-opacity%3D%22.6%22%2F%3E%3C%2FradialGradient%3E%3CradialGradient%20id%3D%22e%22%20cx%3D%220%22%20cy%3D%220%22%20r%3D%221%22%20gradientTransform%3D%22matrix(13.14708%20-30.29344%2020.65904%208.96583%2034.531%2046.293)%22%20gradientUnits%3D%22userSpaceOnUse%22%3E%3Cstop%20stop-color%3D%22%23D7D7D7%22%2F%3E%3Cstop%20offset%3D%221%22%20stop-color%3D%22%23414141%22%2F%3E%3C%2FradialGradient%3E%3CradialGradient%20id%3D%22f%22%20cx%3D%220%22%20cy%3D%220%22%20r%3D%221%22%20gradientTransform%3D%22matrix(-3.34584%2015.42817%20-10.40357%20-2.25617%2044.882%2027.895)%22%20gradientUnits%3D%22userSpaceOnUse%22%3E%3Cstop%20stop-color%3D%22%23fff%22%20stop-opacity%3D%220%22%2F%3E%3Cstop%20offset%3D%221%22%20stop-color%3D%22%23fff%22%20stop-opacity%3D%22.17%22%2F%3E%3C%2FradialGradient%3E%3CradialGradient%20id%3D%22g%22%20cx%3D%220%22%20cy%3D%220%22%20r%3D%221%22%20gradientTransform%3D%22matrix(8.17858%208.27172%20-17.61805%2017.41967%2026.387%2036.445)%22%20gradientUnits%3D%22userSpaceOnUse%22%3E%3Cstop%20stop-color%3D%22%23fff%22%20stop-opacity%3D%22.2%22%2F%3E%3Cstop%20offset%3D%221%22%20stop-color%3D%22%23fff%22%20stop-opacity%3D%22.44%22%2F%3E%3C%2FradialGradient%3E%3CradialGradient%20id%3D%22h%22%20cx%3D%220%22%20cy%3D%220%22%20r%3D%221%22%20gradientTransform%3D%22rotate(80.202%20-7.323%2041.856)%20scale(13.634%2028.9518)%22%20gradientUnits%3D%22userSpaceOnUse%22%3E%3Cstop%20stop-color%3D%22%23fff%22%20stop-opacity%3D%22.12%22%2F%3E%3Cstop%20offset%3D%221%22%20stop-color%3D%22%23fff%22%20stop-opacity%3D%22.35%22%2F%3E%3C%2FradialGradient%3E%3CradialGradient%20id%3D%22i%22%20cx%3D%220%22%20cy%3D%220%22%20r%3D%221%22%20gradientTransform%3D%22rotate(-152.296%2033.168%2019.977)%20scale(20.7745%2065.377)%22%20gradientUnits%3D%22userSpaceOnUse%22%3E%3Cstop%20stop-color%3D%22%23fff%22%20stop-opacity%3D%22.21%22%2F%3E%3Cstop%20offset%3D%22.467%22%20stop-color%3D%22%23fff%22%20stop-opacity%3D%22.19%22%2F%3E%3Cstop%20offset%3D%221%22%20stop-color%3D%22%23fff%22%20stop-opacity%3D%22.29%22%2F%3E%3C%2FradialGradient%3E%3Cfilter%20id%3D%22a%22%20width%3D%2239.273%22%20height%3D%2249.867%22%20x%3D%2220.551%22%20y%3D%2215.381%22%20color-interpolation-filters%3D%22sRGB%22%20filterUnits%3D%22userSpaceOnUse%22%3E%3CfeFlood%20flood-opacity%3D%220%22%20result%3D%22BackgroundImageFix%22%2F%3E%3CfeBlend%20in%3D%22SourceGraphic%22%20in2%3D%22BackgroundImageFix%22%20mode%3D%22normal%22%20result%3D%22shape%22%2F%3E%3CfeGaussianBlur%20result%3D%22effect1_foregroundBlur_3584_482%22%20stdDeviation%3D%22.624%22%2F%3E%3C%2Ffilter%3E%3C%2Fdefs%3E%3C%2Fsvg%3E");
}

.new-tab-image-customize .workspace-leaf-content[data-type="empty"] .empty-state::before {
    background-color: unset;
    background-size: contain;
    background-position: center;
    background-repeat: no-repeat;
    background-image: var(--new-tab-image);
    -webkit-mask: unset;
}

.workspace-leaf-content[data-type="empty"] .empty-state-container {
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
    max-height: 100%;
}

.workspace-leaf-content[data-type="empty"] .empty-state-title {
    display: none;
}

.is-mobile .workspace-leaf-content[data-type="empty"] .empty-state-action-list,
.workspace-leaf-content[data-type="empty"] .empty-state-action-list {
    margin-top: 0px;
}

.workspace-leaf-content[data-type="empty"] .empty-state-container div:has(+ .empty-state-action-list) {
    margin-bottom: 20px;
}

.is-mobile .workspace-leaf-content[data-type="empty"] .empty-state-action {
    margin: 12px 0;
    padding: 4px 24px;
}

/*surfing plugin*/

.empty-state:has(.wb-search-bar-container)::before,
.empty-state:has(.wb-search-bar-container) .empty-state-container {
    display: none;
}

.surfing-settings-icon {
    width: fit-content;
    height: fit-content;
    position: absolute;
    right: 20px;
    margin-top: var(--size-4-4);
}

input.wb-search-bar {
    --background-modifier-form-field: transparent;
}

/* ====== workspace mod side====== */
.nav-header {
    padding: var(--size-2-3) var(--size-2-3) var(--size-2-1);
}

.nav-header~.search-input-container {
    width: calc(100% - var(--size-4-6));
}

.nav-header .search-input-container {
    margin: 6px auto 4px;
}

/*nav-buttons left align*/
.nav-buttons-container {
    display: flex;
    justify-content: flex-start;
}

/*outline pane*/
/* from https://github.com/subframe7536/obsidian-theme-maple, author:@subframe7536 */

.outline-enhanced .workspace-leaf-content[data-type=outline] .tree-item-inner {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.outline-enhanced .workspace-leaf-content[data-type=outline] .view-content .collapse-icon {
    padding-inline-end: var(--size-2-3);
}

.outline-enhanced .workspace-leaf-content[data-type=outline] .view-content .collapse-icon::before {
    content: "" !important;
}

.outline-enhanced:not(.is-grabbing) .workspace-leaf-content[data-type=outline] .view-content .tree-item {
    position: relative;
}

.outline-enhanced .workspace-leaf-content[data-type=outline] .view-content .tree-item-self {
    position: relative;
    margin-bottom: 0;
    white-space: nowrap;
    margin-top: -1px;
    /* fix item gap */
}

.outline-enhanced .workspace-leaf-content[data-type=outline] .view-content .tree-item-self .tree-item-inner {
    padding-left: 16px;
    margin-left: -16px;
    overflow: hidden;
    text-overflow: ellipsis;
    height: calc(var(--nav-item-size) * 1.8);
    line-height: calc(var(--nav-item-size) * 1.8);
    position: relative;
}

.outline-enhanced .workspace-leaf-content[data-type=outline] .view-content .tree-item-self .tree-item-inner::before {
    content: "";
    width: var(--size-4-1);
    height: var(--size-4-1);
    border: 2px solid var(--color-accent);
    border-radius: 50%;
    position: absolute;
    left: 0px;
    top: 50%;
    transform: translateY(-50%);
}

/*.outline-enhanced .workspace-leaf-content[data-type=outline] .view-content .tree-item-self .tree-item-icon~.tree-item-inner {
    padding-left: 4px;
}*/

.outline-enhanced .workspace-leaf-content[data-type=outline] .view-content .tree-item-self .tree-item-icon~.tree-item-inner::before {
    content: none;
}

.outline-enhanced .workspace-leaf-content[data-type=outline] .view-content .tree-item.is-collapsed .tree-item-icon::before {
    box-shadow: 0 0 0 4px var(--background-modifier-active-hover);
}

.outline-enhanced:not(.is-grabbing) .workspace-leaf-content[data-type=outline] .view-content .tree-item::after {
    content: "";
    width: 2px;
    position: absolute;
    background-color: transparent;
    top: calc(var(--nav-item-size) * 1.8 / 2 * -1);
    left: -9px;
    height: calc(100% - var(--nav-item-size) * 1.8 + var(--size-4-8));
}

.outline-enhanced .workspace-leaf-content[data-type=outline] .view-content .tree-item-icon {
    cursor: pointer;
    top: 50%;
    transform: translateY(-50%);
    z-index: 1;
}

.outline-enhanced .workspace-leaf-content[data-type=outline] .view-content .tree-item-icon::before {
    width: var(--size-4-2);
    height: var(--size-4-2);
    background-color: var(--color-accent);
    border-radius: 50%;
    position: absolute;
    left: 4px;
    top: 50%;
    transform: translateY(-50%);
}

.outline-enhanced .workspace-leaf-content[data-type=outline] .view-content .tree-item-icon svg {
    display: block;
}

.outline-enhanced .workspace-leaf-content[data-type=outline] .view-content .tree-item-icon svg path {
    display: none;
}

.outline-enhanced .workspace-leaf-content[data-type=outline] .view-content .tree-item:hover>.tree-item-children>.tree-item::after {
    background-color: var(--color-accent);
}

.outline-enhanced .workspace-leaf-content[data-type=outline] .view-content .tree-item:hover>.tree-item-self:hover+.tree-item-children .tree-item::after {
    background-color: transparent;
}

.outline-enhanced .workspace-leaf-content[data-type=outline] .view-content .tree-item:hover>.tree-item-children>.tree-item:hover::after,
.outline-enhanced .workspace-leaf-content[data-type=outline] .view-content .tree-item:hover>.tree-item-children>.tree-item:hover~.tree-item::after {
    background-color: transparent;
}

.outline-enhanced:not(.is-grabbing) .workspace-leaf-content[data-type=outline] .view-content .tree-item:hover>.tree-item-children>.tree-item:hover::before {
    content: "";
    position: absolute;
    top: calc(var(--nav-item-size) * 1.8 / 2 * -1);
    left: -9px;
    bottom: calc(100% - (var(--nav-item-size) * 1.8 + var(--size-4-2)) / 2 - 1px);
    width: 16px;
    border-bottom-left-radius: var(--radius-m);
    border-bottom: 2px solid var(--color-accent);
    border-left: 2px solid var(--color-accent);
}

.outline-enhanced .workspace-leaf-content[data-type=outline] .view-content :is(.tree-item-children, .tree-item-self .tree-item-self) {
    padding-left: 0;
    margin-left: var(--size-4-5);
    border-left: none;
}

/* ====== file pane====== */
/*bigger cta*/
body:not(.is-mobile).CTA-BTN-enable .workspace-leaf-content[data-type="file-explorer"] .nav-header .nav-buttons-container .clickable-icon.nav-action-button:first-of-type {
    background-color: var(--interactive-accent);
    color: var(--text-on-accent);
    box-shadow: var(--input-shadow);
    border-radius: var(--button-radius);
    width: 100px;
    padding: var(--size-2-3);
}

body:not(.is-mobile).CTA-BTN-enable .workspace-leaf-content[data-type="file-explorer"] .nav-header .nav-buttons-container .clickable-icon.nav-action-button:first-of-type:hover {
    background-color: var(--interactive-accent-hover);
}

body:not(.is-mobile).CTA-BTN-enable .workspace-leaf-content[data-type="file-explorer"] .nav-header .nav-buttons-container .clickable-icon.nav-action-button:first-of-type::after {
    content: "New Note";
    font-size: var(--font-ui-small);
    margin-left: auto;
}

/* file active states */
.theme-light {
    --border-radius-activated-file-light: var(--radius-s);
    --color-activated-file-light: var(--nav-item-color-active);
    --background-activated-file-light: var(--nav-item-background-active);
    --shadow-activated-file-light: none;
}

.theme-dark {
    --border-radius-activated-file-dark: var(--radius-s);
    --color-activated-file-dark: var(--nav-item-color-active);
    --background-activated-file-dark: var(--nav-item-background-active);
    --shadow-activated-file-dark: none;
}

.activated-file-default-light.theme-light {
    --border-radius-activated-file-light: var(--radius-s) !important;
    --color-activated-file-light: var(--nav-item-color-active) !important;
    --background-activated-file-light: var(--nav-item-background-active) !important;
    --shadow-activated-file-light: none !important;
}

.activated-file-default-dark.theme-dark {
    --border-radius-activated-file-dark: var(--radius-s) !important;
    --color-activated-file-dark: var(--nav-item-color-active) !important;
    --background-activated-file-dark: var(--nav-item-background-active) !important;
    --shadow-activated-file-dark: none !important;
}

.activated-file-accent-light.theme-light {
    --border-radius-activated-file-light: var(--radius-s) !important;
    --color-activated-file-light: var(--text-on-accent) !important;
    --background-activated-file-light: var(--color-accent) !important;
    --shadow-activated-file-light: none !important;
}

.activated-file-accent-dark.theme-dark {
    --border-radius-activated-file-dark: var(--radius-s) !important;
    --color-activated-file-dark: var(--text-on-accent) !important;
    --background-activated-file-dark: var(--color-accent) !important;
    --shadow-activated-file-dark: none !important;
}

.activated-file-accent-light.theme-light .nav-file-title.is-active .nav-file-tag,
.activated-file-accent-dark.theme-dark .nav-file-title.is-active .nav-file-tag,
.activated-file-accent-light.theme-light .tree-item-self.is-active svg,
.activated-file-accent-dark.theme-dark .tree-item-self.is-active svg {
    color: var(--text-on-accent) !important;
}

.activated-file-tab-style-light.theme-light {
    --border-radius-activated-file-light: var(--border-radius-activated-tab-header-light) !important;
    --color-activated-file-light: var(--color-activated-tab-header-light) !important;
    --background-activated-file-light: var(--background-activated-tab-header-light) !important;
    --shadow-activated-file-light: var(--shadow-activated-tab-header-light) !important;
}

.activated-file-tab-style-dark.theme-dark {
    --border-radius-activated-file-dark: var(--border-radius-activated-tab-header-dark) !important;
    --color-activated-file-dark: var(--color-activated-tab-header-dark) !important;
    --background-activated-file-dark: var(--background-activated-tab-header-dark) !important;
    --shadow-activated-file-dark: var(--shadow-activated-tab-header-dark) !important;
}

.activated-file-tab-style-light.theme-light .nav-file-title.is-active .nav-file-tag,
.activated-file-tab-style-light.theme-light .tree-item-self.is-active svg {
    color: var(--color-activated-file-light) !important;
}

.activated-file-tab-style-dark.theme-dark .nav-file-title.is-active .nav-file-tag,
.activated-file-tab-style-dark.theme-dark .tree-item-self.is-active svg {
    color: var(--color-activated-file-dark) !important;
}

.activated-file-customize-light {
    --border-radius-activated-file-light: 4px;
    --color-activated-file-light: var(--nav-item-color-active);
    --background-activated-file-light: rgba(0, 0, 0, 0.05);
    --shadow-activated-file-light: inset 0 0 0 1px rgba(0, 0, 0, 0.1);
}

.activated-file-customize-dark {
    --border-radius-activated-file-dark: 4px;
    --color-activated-file-light: var(--nav-item-color-active);
    --background-activated-file-dark: rgba(255, 255, 255, 0.075);
    --shadow-activated-file-dark: inset 0 0 0 1px rgba(255, 255, 255, 0.15);
}

.activated-file-customize-light.theme-light .nav-file-title.is-active .nav-file-tag,
.activated-file-customize-light.theme-light .tree-item-self.is-active svg {
    color: var(--color-activated-file-light) !important;
}

.activated-file-customize-dark.theme-dark .nav-file-title.is-active .nav-file-tag,
.activated-file-customize-dark.theme-dark .tree-item-self.is-active svg {
    color: var(--color-activated-file-dark) !important;
}

.theme-light:not(.is-grabbing) .workspace-leaf-content[data-type="bookmarks"] .tree-item-self.is-active:hover,
.theme-light .workspace-leaf-content[data-type="bookmarks"] .tree-item-self.is-active,
.theme-light .mk-tree-item.nav-folder-title.is-active.is-selected,
.theme-light .mk-tree-item.nav-file-title.is-active.is-selected,
.theme-light:not(.is-grabbing) .nav-file-title.is-active:hover,
.theme-light:not(.is-grabbing) .nav-folder-title.is-active:hover,
.theme-light .nav-file-title.is-active,
.theme-light .nav-folder-title.is-active {
    --nav-item-color-active: var(--color-activated-file-light);
    --nav-item-color-selected: var(--color-activated-file-light);
    border-radius: var(--border-radius-activated-file-light);
    background: var(--background-activated-file-light) !important;
    box-shadow: var(--shadow-activated-file-light);
}

.theme-dark:not(.is-grabbing) .workspace-leaf-content[data-type="bookmarks"] .tree-item-self.is-active:hover,
.theme-dark .workspace-leaf-content[data-type="bookmarks"] .tree-item-self.is-active,
.theme-dark .mk-tree-item.nav-folder-title.is-active.is-selected,
.theme-dark .mk-tree-item.nav-file-title.is-active.is-selected,
.theme-dark:not(.is-grabbing) .nav-file-title.is-active:hover,
.theme-dark:not(.is-grabbing) .nav-folder-title.is-active:hover,
.theme-dark .nav-file-title.is-active,
.theme-dark .nav-folder-title.is-active {
    --nav-item-color-active: var(--color-activated-file-dark);
    --nav-item-color-selected: var(--color-activated-file-dark);
    border-radius: var(--border-radius-activated-file-dark);
    background: var(--background-activated-file-dark) !important;
    box-shadow: var(--shadow-activated-file-dark);
}

/* file layout & style */
.workspace-leaf-content .tree-item-self {
    margin-bottom: var(--size-2-2);
    position: relative;
    border-radius: var(--border-radius-activated-file-light);
    align-items: center;
}

.theme-dark .workspace-leaf-content .tree-item-self {
    border-radius: var(--border-radius-activated-file-dark);
}

.workspace-leaf-content .tree-item-self :is(.tree-item-flair, .tree-item-inner) {
    line-height: 20px;
}

.file-names-untrim .workspace-leaf-content[data-type="file-explorer"] :is(.nav-file-title-content, .nav-folder-title-content) {
    white-space: normal;
}

.folder-font-bold .nav-folder-title {
    --nav-item-weight-hover: bold;
    --nav-item-weight-active: bold;
    --nav-item-weight: bold;
}

/*file tag*/
.nav-file-tag {
    background-color: transparent;
    color: var(--text-faint);
    margin-left: 4px;
}

/*other pane*/
.workspace-leaf-content .nav-header~.node-insert-event {
    padding-top: var(--size-4-1);
}

/*icons in pane*/
.tree-item-self .tree-item-icon {
    height: var(--size-4-4);
    --icon-color: currentColor;
}

.tree-item-self .tree-item-icon .svg-icon:not(.right-triangle) {
    --icon-size: var(--icon-s);
    --icon-stroke: var(--icon-s-stroke-width);
}

body:not(.file-icon-remove) .workspace-leaf-content[data-type="tag"] .tree-item:not(:hover) .tree-item-icon.collapse-icon svg.svg-icon,
body:not(.file-icon-remove) .workspace-leaf-content:is([data-type="bookmarks"], [data-type="file-explorer"]) .tree-item-icon.collapse-icon svg.svg-icon {
    color: transparent;
}

body:not(.file-icon-remove) .workspace-leaf-content[data-type="tag"] .tree-item:not(:hover) .tree-item-icon.collapse-icon,
body:not(.file-icon-remove) .workspace-leaf-content:is([data-type="bookmarks"], [data-type="file-explorer"]) .tree-item-icon.collapse-icon {
    background-color: currentColor;
    opacity: var(--icon-opacity);
}

body:not(.file-icon-remove) .workspace-leaf-content[data-type="tag"] .tree-item-self:not(:has(>.tree-item-icon.collapse-icon)):before,
body:not(.file-icon-remove) .workspace-leaf-content[data-type="file-explorer"] :is(.nav-file-title, .nav-folder.mod-root>.nav-folder-title)::before {
    content: " ";
    position: absolute;
    margin-left: calc(-1 * var(--size-4-5));
    width: var(--size-4-4);
    height: var(--size-4-4);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: var(--icon-opacity);
    background-color: currentColor;
    flex: 0 0 auto;
}

body:not(.file-icon-remove) .workspace-leaf-content[data-type="file-explorer"] .nav-folder.mod-root>.nav-folder-title::before {
    -webkit-mask-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' fill='none' viewBox='0 0 16 16'%3e%3cg stroke='black' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.333'%3e%3cpath d='M14 12.8V3.2c0-.884-.264-1.2-1-1.2H3c-.736 0-1 .316-1 1.2v9.6c0 .884.264 1.2 1 1.2h10c.736 0 1-.316 1-1.2Z'/%3e%3cpath d='M9.978 7.04a2 2 0 1 1-3.956.587 2 2 0 0 1 3.956-.587ZM8 9.333v2m6-6.666h1m-1 6h1'/%3e%3c/g%3e%3c/svg%3e");
}

body:not(.file-icon-remove) .workspace-leaf-content[data-type="file-explorer"] .tree-item-icon.collapse-icon,
body:not(.file-icon-remove) .workspace-leaf-content[data-type="bookmarks"] .tree-item-icon.collapse-icon {
    -webkit-mask-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' fill='none' viewBox='0 0 16 16'%3e%3cpath stroke='black' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.333' d='M4 9.333 4.967 7.4a1.333 1.333 0 0 1 1.193-.733h7.173m0 0a1.333 1.333 0 0 1 1.294 1.666l-1.034 4a1.333 1.333 0 0 1-1.293 1H2.667A1.333 1.333 0 0 1 1.333 12V3.333C1.333 2.6 1.933 2 2.667 2h2.62a1.333 1.333 0 0 1 1.106.6l.547.8a1.333 1.333 0 0 0 1.107.6H12a1.333 1.333 0 0 1 1.333 1.333v1.334Z'/%3e%3c/svg%3e");
}

body:not(.file-icon-remove) .workspace-leaf-content[data-type="file-explorer"] .is-collapsed .tree-item-icon.collapse-icon,
body:not(.file-icon-remove) .workspace-leaf-content[data-type="bookmarks"] .is-collapsed .tree-item-icon.collapse-icon {
    -webkit-mask-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' fill='none' viewBox='0 0 16 16'%3e%3cpath stroke='black' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.333' d='M2.667 13.333h10.666A1.333 1.333 0 0 0 14.667 12V5.333A1.333 1.333 0 0 0 13.333 4H8.047a1.333 1.333 0 0 1-1.107-.6l-.547-.8A1.333 1.333 0 0 0 5.287 2h-2.62a1.333 1.333 0 0 0-1.334 1.333V12c0 .733.6 1.333 1.334 1.333Z'/%3e%3c/svg%3e");
}

body:not(.file-icon-remove) .workspace-leaf-content[data-type="file-explorer"] .nav-file-title::before {
    -webkit-mask-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' fill='none' viewBox='0 0 16 16'%3e%3cg stroke='black' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.333'%3e%3cpath d='M9.667 1.333H4a1.333 1.333 0 0 0-1.333 1.334v10.666A1.333 1.333 0 0 0 4 14.667h8a1.333 1.333 0 0 0 1.333-1.334V5L9.667 1.333Z'/%3e%3cpath d='M9.333 1.333v4h4'/%3e%3c/g%3e%3c/svg%3e");
}

body:not(.file-icon-remove) .workspace-leaf-content[data-type="file-explorer"] .nav-file-title[data-path$=".canvas"]::before {
    -webkit-mask-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' fill='none' viewBox='0 0 16 16'%3e%3cpath stroke='black' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.333' d='M6 2H2.667A.667.667 0 0 0 2 2.667v4.666c0 .369.298.667.667.667H6a.667.667 0 0 0 .667-.667V2.667A.667.667 0 0 0 6 2Zm7.333 0H10a.667.667 0 0 0-.667.667v2c0 .368.299.666.667.666h3.333A.667.667 0 0 0 14 4.667v-2A.667.667 0 0 0 13.333 2Zm0 6H10a.667.667 0 0 0-.667.667v4.666c0 .368.299.667.667.667h3.333a.667.667 0 0 0 .667-.667V8.667A.667.667 0 0 0 13.333 8ZM6 10.667H2.667a.667.667 0 0 0-.667.666v2c0 .368.298.667.667.667H6a.667.667 0 0 0 .667-.667v-2A.667.667 0 0 0 6 10.667Z'/%3e%3c/svg%3e");
}

body:not(.file-icon-remove) .workspace-leaf-content[data-type="file-explorer"] .is-unsupported.nav-file-title::before {
    -webkit-mask-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' fill='none' viewBox='0 0 16 16'%3e%3cg stroke='black' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.333'%3e%3cpath d='M9.667 1.333H4a1.333 1.333 0 0 0-1.333 1.334v10.666A1.333 1.333 0 0 0 4 14.667h8a1.333 1.333 0 0 0 1.333-1.334V5L9.667 1.333Z'/%3e%3cpath d='M6.667 6.867c.133-.267.333-.534.6-.667A1.4 1.4 0 0 1 9 6.467c.2.266.333.533.333.866C9.333 8.2 8 8.667 8 8.667m0 2.666h.007'/%3e%3c/g%3e%3c/svg%3e");
}

body:not(.file-icon-remove) .workspace-leaf-content[data-type="file-explorer"] .is-unsupported.nav-file-title:is([data-path$=".zip"], [data-path$=".7z"])::before {
    -webkit-mask-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' fill='none' viewBox='0 0 16 16'%3e%3cg stroke='black' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.333'%3e%3cpath d='M2.667 14.667v-12c0-.334.133-.667.4-.934.266-.266.6-.4.933-.4h5.667L13.333 5v8.333c0 .334-.133.667-.4.934-.266.266-.6.4-.933.4h-1.333'/%3e%3cpath d='M9.333 1.333v4h4m-6.666 9.334a1.333 1.333 0 1 0 0-2.667 1.333 1.333 0 0 0 0 2.667Zm0-10V4m0 4v-.667m0 4.667v-1.333'/%3e%3c/g%3e%3c/svg%3e");
}

body:not(.file-icon-remove) .workspace-leaf-content[data-type="file-explorer"] .nav-file-title[data-path$=".webm"]::before {
    -webkit-mask-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' fill='none' viewBox='0 0 16 16'%3e%3cg stroke='black' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.333'%3e%3cpath d='M2.667 14.667H12a1.333 1.333 0 0 0 1.333-1.334V5L9.667 1.333H4a1.333 1.333 0 0 0-1.333 1.334v2'/%3e%3cpath d='M9.333 1.333v4h4M4.667 6.667 2.667 8H1.333v2.667h1.334l2 1.333V6.667Zm2.666.666c.427.534.667 1.247.667 2 0 .754-.24 1.467-.667 2'/%3e%3c/g%3e%3c/svg%3e");
}

body:not(.file-icon-remove) .workspace-leaf-content[data-type="file-explorer"] .nav-file-title:is([data-path$=".svg"], [data-path$=".bmp"], [data-path$=".jpeg"], [data-path$=".jpg"], [data-path$=".png"], [data-path$=".gif"])::before {
    -webkit-mask-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' fill='none' viewBox='0 0 16 16'%3e%3cg stroke='black' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.333'%3e%3cpath d='M12.667 2H3.333C2.597 2 2 2.597 2 3.333v9.334C2 13.403 2.597 14 3.333 14h9.334c.736 0 1.333-.597 1.333-1.333V3.333C14 2.597 13.403 2 12.667 2Z'/%3e%3cpath d='M6 7.333a1.333 1.333 0 1 0 0-2.666 1.333 1.333 0 0 0 0 2.666ZM14 10l-2.057-2.057a1.333 1.333 0 0 0-1.886 0L4 14'/%3e%3c/g%3e%3c/svg%3e");
}

body:not(.file-icon-remove) .workspace-leaf-content[data-type="file-explorer"] .nav-file-title[data-path$=".pdf"]::before {
    -webkit-mask-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' fill='none' viewBox='0 0 16 16'%3e%3cg stroke='black' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.333'%3e%3cpath d='M9.667 1.333H4a1.333 1.333 0 0 0-1.333 1.334v10.666A1.333 1.333 0 0 0 4 14.667h8a1.333 1.333 0 0 0 1.333-1.334V5L9.667 1.333Z'/%3e%3cpath d='M9.333 1.333v4h4m-2.666 3.334H5.333m5.334 2.666H5.333M6.667 6H5.333'/%3e%3c/g%3e%3c/svg%3e");
}

body:not(.file-icon-remove) .workspace-leaf-content[data-type="file-explorer"] .nav-file-title[data-path$=".excalidraw.md"]::before {
    -webkit-mask-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' fill='none' viewBox='0 0 16 16'%3e%3cg stroke='black' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.333' clip-path='url(%23a)'%3e%3cpath d='M8 12.667 12.667 8l2 2L10 14.667l-2-2Z'/%3e%3cpath d='m12 8.667-1-5-9.667-2.334L3.667 11l5 1L12 8.667ZM1.333 1.333l5.058 5.058'/%3e%3cpath d='M7.333 8.667a1.333 1.333 0 1 0 0-2.667 1.333 1.333 0 0 0 0 2.667Z'/%3e%3c/g%3e%3cdefs%3e%3cclipPath id='a'%3e%3cpath fill='white' d='M0 0h16v16H0z'/%3e%3c/clipPath%3e%3c/defs%3e%3c/svg%3e");
}

body:not(.file-icon-remove) .workspace-leaf-content[data-type="tag"] .tree-item:not(:hover) .tree-item-icon.collapse-icon {
    -webkit-mask-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' fill='none' viewBox='0 0 16 16'%3e%3cg stroke='black' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.333'%3e%3cpath d='M6 3.333H1.333V8l4.194 4.193a1.617 1.617 0 0 0 2.28 0l2.386-2.386a1.617 1.617 0 0 0 0-2.28L6 3.333ZM4 6.007V6'/%3e%3cpath d='m10 3.333 4.2 4.2a1.6 1.6 0 0 1 0 2.267l-2.867 2.867'/%3e%3c/g%3e%3c/svg%3e");
}

body:not(.file-icon-remove) .workspace-leaf-content[data-type="tag"] .tree-item-self:not(:has(>.tree-item-icon.collapse-icon))::before {
    -webkit-mask-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' fill='none' viewBox='0 0 16 16'%3e%3cg clip-path='url(%23a)'%3e%3cpath stroke='black' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.333' d='M8 1.333H1.333V8l6.194 6.193a1.617 1.617 0 0 0 2.28 0l4.386-4.386a1.617 1.617 0 0 0 0-2.28L8 1.333ZM4.667 4.667h.006'/%3e%3c/g%3e%3cdefs%3e%3cclipPath id='a'%3e%3cpath fill='white' d='M0 0h16v16H0z'/%3e%3c/clipPath%3e%3c/defs%3e%3c/svg%3e");
}

body:not(.file-icon-remove) .workspace-leaf-content[data-type="bookmarks"] svg.lucide-folder {
    background-color: currentColor;
    -webkit-mask-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' fill='none' viewBox='0 0 16 16'%3e%3cg stroke='black' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.333'%3e%3cpath d='M1.333 6V3.333C1.333 2.6 1.933 2 2.667 2h2.62a1.333 1.333 0 0 1 1.106.6l.547.8a1.333 1.333 0 0 0 1.107.6h5.286a1.333 1.333 0 0 1 1.334 1.333V12a1.333 1.333 0 0 1-1.334 1.333h-12'/%3e%3cpath d='m5.333 10.667 2-2-2-2'/%3e%3cpath d='M1.333 10.667V10a1.333 1.333 0 0 1 1.334-1.333h4'/%3e%3c/g%3e%3c/svg%3e");
}

/*colorful folder icon*/

body:not(.file-icon-remove) .nav-folder:nth-child(8n+2) {
    --colorful-folder-color: var(--color-red);
}

body:not(.file-icon-remove) .nav-folder:nth-child(8n+3) {
    --colorful-folder-color: var(--color-orange);
}

body:not(.file-icon-remove) .nav-folder:nth-child(8n+4) {
    --colorful-folder-color: var(--color-yellow);
}

body:not(.file-icon-remove) .nav-folder:nth-child(8n+5) {
    --colorful-folder-color: var(--color-green);
}

body:not(.file-icon-remove) .nav-folder:nth-child(8n+6) {
    --colorful-folder-color: var(--color-cyan);
}

body:not(.file-icon-remove) .nav-folder:nth-child(8n+7) {
    --colorful-folder-color: var(--color-blue);
}

body:not(.file-icon-remove) .nav-folder:nth-child(8n+8) {
    --colorful-folder-color: var(--color-purple);
}

body:not(.file-icon-remove) .nav-folder:nth-child(8n+9) {
    --colorful-folder-color: var(--color-pink);
}

body:not(.file-icon-remove).colorful-folder .workspace-leaf-content[data-type="file-explorer"] .tree-item-icon.collapse-icon {
    background-color: var(--colorful-folder-color);
}

body:not(.file-icon-remove).colorful-folder .nav-folder.mod-root>.nav-folder-title::before {
    background-color: currentColor;
}

/*====== empty state ======*/

body {
    --empty-state-image: url("data:image/svg+xml,%3Csvg%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20width%3D%22160%22%20height%3D%22160%22%20fill%3D%22none%22%20viewBox%3D%220%200%20160%20160%22%3E%3Cg%20fill%3D%22%23000%22%20clip-path%3D%22url(%23a)%22%20opacity%3D%22.75%22%3E%3Cpath%20fill-opacity%3D%22.25%22%20d%3D%22M38.66%20123.48C27.16%20112.55%2020%2097.11%2020%2080c0-5.18.66-10.2%201.89-15l16.77%2058.48Zm70.35%209.05C100.41%20137.29%2090.52%20140%2080%20140c-10.52%200-20.47-2.73-29.08-7.52l19.24-5.51c3%201.03%206.22%201.58%209.57%201.58%203.7%200%207.24-.68%2010.5-1.91l17.97%205.15.81.74Zm29.04-67.75-16.88%2058.86C132.76%20112.71%20140%2097.2%20140%2080c0-5.26-.68-10.36-1.95-15.22Zm-15.41-26.99-39.3-11.27a5.27%205.27%200%200%200-6.51%203.61l-1.34%204.68-8.94-4.95-29.78%208.54C47.68%2027.06%2063.02%2020%2080%2020c16.98%200%2031.77%206.8%2042.64%2017.79Z%22%2F%3E%3Cpath%20fill-opacity%3D%22.05%22%20d%3D%22m70.31%2052.88-.98%203.41-38.26%2010.97-.83-2.89%2040.07-11.49ZM58.37%2078.15a28.777%2028.777%200%200%200-3.3%204.07l-18.21%205.22-.83-2.88%2022.34-6.41Zm-7.78%2014.72c-.21%201.06-.37%202.15-.47%203.25l-9.96%202.86-.82-2.88%2011.25-3.23Zm.24-43.76-23.07%206.61-.83-2.88L50%2046.22l.83%202.89Zm4.9%2067.25-.06.02-9.72%202.79-.83-2.89%208.67-2.48.23-.07c.51.91%201.09%201.79%201.71%202.63Z%22%2F%3E%3Cpath%20fill-opacity%3D%22.15%22%20d%3D%22m72.71%2044.51-1.81.52-4.35-15.17-29.78%208.54-15.74%204.51a5.27%205.27%200%200%200-3.61%206.51L21.89%2065l16.77%2058.48%201.87%206.51c.8%202.79%203.71%204.41%206.5%203.61l3.89-1.12%2019.24-5.51a29.706%2029.706%200%200%201-14.43-10.61l-.06.02-9.72%202.79-.83-2.89%208.67-2.48.23-.07c-.15-.24-.29-.48-.43-.73A29.64%2029.64%200%200%201%2050%2098.82c0-.91.04-1.81.12-2.7l-9.96%202.86-.82-2.88%2011.25-3.23c.79-3.88%202.34-7.48%204.48-10.65l-18.21%205.22-.83-2.88%2022.34-6.41a29.14%2029.14%200%200%201%206.07-4.82l4.89-17.04-38.26%2010.97-.83-2.89%2040.07-11.49%202.4-8.37ZM27.76%2055.72l-.83-2.88L50%2046.22l.83%202.89-23.07%206.61Z%22%2F%3E%3Cpath%20fill-opacity%3D%22.1%22%20d%3D%22m108.2%20131.79-17.97-5.15c2.76-1.04%205.32-2.48%207.61-4.25l10.36%209.4ZM135.84%2058l-11.33-3.25%203.19-11.14%201.16-4.04-6.22-1.78-39.3-11.27a5.27%205.27%200%200%200-6.51%203.61l-1.34%204.68-2.78%209.7-2.4%208.37-.98%203.41-4.89%2017.04c4.47-2.69%209.7-4.24%2015.29-4.24s11.22%201.66%2015.79%204.55a28.65%2028.65%200%200%201%204.74%203.68l23.71%206.8-.82%202.89-19.27-5.52c2.3%203.17%203.97%206.81%204.85%2010.75l11.94%203.42-.83%202.88-10.55-3.03c.12%201.09.18%202.19.18%203.31%200%204.7-1.09%209.14-3.03%2013.09-.22.45-.45.89-.7%201.32l.67.19%208.47%202.43-.83%202.88-6.55-1.88%2010.53%209.55%201.87%201.69%201.27-4.45%2016.88-58.86%201.63-5.68-3.84-1.1ZM82.85%2038.89l.23-.82%2023.07%206.62-.82%202.88-18.83-5.4-2.81-.81-1.43-.41.59-2.06Zm46.09%2027.93L90.4%2055.77l-9.63-2.76-1.82-.52.65-2.27.18-.62%209.64%202.77%2040.34%2011.57-.82%202.88Z%22%2F%3E%3Cpath%20fill-opacity%3D%22.35%22%20d%3D%22m75.49%2034.81-2.78%209.7-1.81.52-4.35-15.17%208.94%204.95Z%22%2F%3E%3Cpath%20fill-opacity%3D%22.3%22%20d%3D%22m128.86%2039.57-1.16%204.03v.01l-3.19%2011.14L135.84%2058l3.84%201.1-10.82-19.53Z%22%2F%3E%3Cpath%20fill-opacity%3D%22.35%22%20d%3D%22m122.01%20130.01-7.12%207.85-5.88-5.33-.81-.74-10.36-9.4c1.83-1.39%203.49-3%204.94-4.79.46-.56.9-1.13%201.3-1.73.32-.43.61-.87.88-1.32l.79.71%201.75%201.59%2010.53%209.55%201.87%201.69%202.11%201.92Z%22%2F%3E%3Cpath%20fill-opacity%3D%22.35%22%20fill-rule%3D%22evenodd%22%20d%3D%22M109.29%2095.51c-.13-1.11-.31-2.2-.56-3.27-.88-3.94-2.55-7.58-4.85-10.75-1.07-1.51-2.28-2.9-3.62-4.17a28.65%2028.65%200%200%200-4.74-3.68c-4.57-2.89-9.99-4.55-15.79-4.55-5.8%200-10.82%201.55-15.29%204.24a29.14%2029.14%200%200%200-6.07%204.82%2028.777%2028.777%200%200%200-3.3%204.07%2029.564%2029.564%200%200%200-4.48%2010.65%2030.145%2030.145%200%200%200-.59%205.95c0%205.13%201.3%209.96%203.59%2014.17.14.26.28.5.43.74.51.91%201.09%201.79%201.71%202.63a29.706%2029.706%200%200%200%2014.43%2010.61c3%201.03%206.22%201.58%209.57%201.58%203.7%200%207.24-.68%2010.5-1.91%202.76-1.04%205.32-2.48%207.61-4.25%201.83-1.39%203.49-3%204.94-4.79.46-.56.9-1.13%201.3-1.73.32-.43.61-.87.88-1.32.28-.43.54-.87.78-1.32.25-.43.48-.87.7-1.32%201.94-3.95%203.03-8.39%203.03-13.09%200-1.12-.06-2.22-.18-3.31Zm-4.98%208.78a24.97%2024.97%200%200%201-4.91%2010.24c-.31.4-.63.78-.97%201.15-.02.02-.04.05-.06.07-.27.3-.55.59-.83.87-.29.28-.58.56-.88.83-.59.55-1.21%201.06-1.86%201.54-.65.49-1.32.94-2.01%201.36l-.42.24s-.01.01-.02.01c-.55.33-1.12.63-1.7.91-1.1.53-2.25.98-3.43%201.35-2.01.62-4.14%201-6.33%201.1-.1.01-.19.01-.29.01-.29.02-.58.02-.87.02-2.61%200-5.12-.4-7.48-1.13a24.94%2024.94%200%200%201-5.57-2.51c-.21-.13-.43-.27-.64-.4-.21-.13-.41-.27-.62-.42-.26-.17-.5-.35-.75-.54-.65-.48-1.27-.99-1.86-1.54-.84-.76-1.62-1.57-2.35-2.44-.69-.81-1.32-1.68-1.9-2.58a25.1%2025.1%200%200%201-3.22-7.35c-.51-2-.78-4.1-.78-6.26%200-1.38.11-2.74.33-4.06.18-1.15.44-2.26.78-3.34%201.28-4.17%203.61-7.87%206.68-10.8.26-.26.52-.5.79-.71a24.966%2024.966%200%200%201%2016.59-6.26c1.39%200%202.74.11%204.07.33%204.28.69%208.2%202.47%2011.46%************%201.66%201.37%202.42%202.14%202.63%202.68%204.68%205.95%205.9%209.6.38%201.09.68%202.21.89%203.37a25.259%2025.259%200%200%201-.16%2010.16Z%22%20clip-rule%3D%22evenodd%22%2F%3E%3Cpath%20fill-opacity%3D%22.05%22%20fill-rule%3D%22evenodd%22%20d%3D%22M104.47%2094.13c-.21-1.16-.51-2.28-.89-3.37a25.082%2025.082%200%200%200-5.9-9.6c-.76-.77-1.57-1.49-2.42-2.14a24.907%2024.907%200%200%200-11.46-5.04c-1.33-.22-2.68-.33-4.07-.33a24.966%2024.966%200%200%200-16.59%206.26c-.27.21-.53.45-.79.71-3.07%202.93-5.4%206.63-6.68%2010.8-.34%201.08-.6%202.19-.78%203.34-.22%201.32-.33%202.68-.33%204.06%200%202.16.27%204.26.78%206.26a25.1%2025.1%200%200%200%203.22%207.35c.58.9%201.21%201.77%201.9%************%201.51%201.68%202.35%************%201.21%201.06%201.86%************.***********.***********.***********.43.27.64.4a24.94%2024.94%200%200%200%205.57%202.51c2.36.73%204.87%201.13%207.48%201.13.29%200%20.58%200%20.87-.02.1%200%20.19%200%20.29-.01%202.19-.1%204.32-.48%206.33-1.1%201.18-.37%202.33-.82%203.43-1.35.58-.28%201.15-.58%201.7-.91.01%200%20.02%200%20.02-.01l.42-.24c.69-.42%201.36-.87%202.01-1.36.65-.48%201.27-.99%201.86-1.54.3-.27.59-.55.88-.83.28-.28.56-.57.83-.87.02-.02.04-.05.06-.07.34-.37.66-.75.97-1.15.66-.82%201.27-1.68%201.83-2.59a25.174%2025.174%200%200%200%203.68-13.12c0-1.6-.15-3.17-.44-4.69Zm-14.69%209.35h.01l2.41%202.42.14.14c.64.72.98%201.65.95%202.61-.01.33-.06.65-.16.96-.17.6-.49%201.14-.94%201.59-.39.39-.86.69-1.37.87-.37.15-.77.23-1.18.24-.96.03-1.89-.3-2.61-.94l-.54-.54-2.56-2.56-1.83-1.82-2.29-2.29-1.27%201.27-1.78%201.78-.64.64-3.52%203.52c-.08.08-.16.16-.24.22-.3.27-.63.48-.99.64-.46.21-.96.32-1.46.32-.23%200-.46%200-.69-.06h-.02c-.26-.04-.51-.11-.76-.21a3.796%203.796%200%200%201-2.08-2.08c-.19-.48-.28-.97-.28-1.47.01-.51.12-1%20.32-1.47.2-.46.5-.87.86-1.22l.56-.56%202.43-2.43%204.23-4.23-7.22-7.21c-.1-.1-.2-.2-.29-.31-.23-.28-.43-.58-.57-.91-.2-.47-.31-.96-.32-1.47%200-.17.01-.34.04-.5.03-.33.11-.66.24-.97.19-.47.47-.89.83-1.25.35-.36.78-.64%201.25-.83.46-.19.96-.28%201.47-.27.5%200%201%20.11%************.21.88.5%201.23.87l.22.22%202.42%202.42%204.56%204.57%205-5%202.21-2.21c.08-.07.16-.14.24-.2.68-.56%201.53-.85%202.4-.83.99.01%201.92.41%202.62%**********.7.89.88%************.21.79.21%201.2a3.7%203.7%200%200%201-1.03%202.64l-7.2%207.21%204.64%204.66h.01Z%22%20clip-rule%3D%22evenodd%22%2F%3E%3Cpath%20fill-opacity%3D%22.5%22%20fill-rule%3D%22evenodd%22%20d%3D%22M92.34%20106.04c.64.72.98%201.65.95%202.61-.01.33-.06.65-.16.96-.17.6-.49%201.14-.94%201.59-.39.39-.86.69-1.37.87-.37.15-.77.23-1.18.24-.96.03-1.89-.3-2.61-.94l-.54-.54-2.56-2.56-1.83-1.82-2.29-2.29-1.27%201.27-1.78%201.78-.64.64-3.52%203.52c-.08.08-.16.16-.24.22-.3.27-.63.48-.99.64-.46.21-.96.32-1.46.32-.23%200-.46%200-.69-.06h-.02c-.26-.04-.51-.11-.76-.21a3.796%203.796%200%200%201-2.08-2.08c-.19-.48-.28-.97-.28-1.47.01-.51.12-1%20.32-1.47.2-.46.5-.87.86-1.22l.56-.56%202.43-2.43%204.23-4.23-7.22-7.21c-.1-.1-.2-.2-.29-.31-.23-.28-.43-.58-.57-.91-.2-.47-.31-.96-.32-1.47%200-.17.01-.34.04-.5.03-.33.11-.66.24-.97.19-.47.47-.89.83-1.25.35-.36.78-.64%201.25-.83.46-.19.96-.28%201.47-.27.5%200%201%20.11%************.21.88.5%201.23.87l.22.22%202.42%202.42%204.56%204.57%205-5%202.21-2.21c.08-.07.16-.14.24-.2.68-.56%201.53-.85%202.4-.83.99.01%201.92.41%202.62%**********.7.89.88%************.21.79.21%201.2a3.7%203.7%200%200%201-1.03%202.64l-7.2%207.21%204.64%204.66h.01l2.41%202.42.14.14h.01Z%22%20clip-rule%3D%22evenodd%22%2F%3E%3Cpath%20fill-opacity%3D%22.35%22%20d%3D%22M124.81%20134.66c.07%201.4-.42%202.77-1.36%203.81a5.29%205.29%200%200%201-3.67%201.73c-1.4.07-2.78-.42-3.82-1.36l-1.07-.98%207.12-7.84%201.07.97c1.04.94%201.66%202.26%201.73%203.67Z%22%2F%3E%3C%2Fg%3E%3Cdefs%3E%3CclipPath%20id%3D%22a%22%3E%3Cpath%20fill%3D%22%23fff%22%20d%3D%22M0%200h160v160H0z%22%2F%3E%3C%2FclipPath%3E%3C%2Fdefs%3E%3C%2Fsvg%3E");
}

.tag-pane-empty,
.bookmarks-pane-empty,
.pane-empty,
.search-empty-state {
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
    gap: 8px;
    padding-left: 0px;
}

.tag-pane-empty::before,
.bookmarks-pane-empty::before,
.pane-empty::before,
.search-empty-state::before {
    content: "";
    display: inline-block;
    width: 160px;
    height: 160px;
    background-color: var(--icon-color);
    -webkit-mask-size: contain;
    -webkit-mask-position: center;
    -webkit-mask-repeat: no-repeat;
    -webkit-mask-image: var(--empty-state-image);
}


/*====== setting modal ======*/

body {
    --modal-border-color: var(--background-modifier-border);
}

.modal-sidebar {
    --background-modifier-form-field: transparent;
}

.settings-search-container.vertical-tab-header-group {
    padding-top: 2px;
}

body:not(.setting-item-title-icon-remove):not(.is-phone) .vertical-tab-header-group .vertical-tab-header-group-title {
    display: flex;
    align-items: center;
}

body:not(.setting-item-title-icon-remove):not(.is-phone) .vertical-tab-header-group:nth-last-of-type(3) .vertical-tab-header-group-title::before {
    content: " ";
    width: 16px;
    height: 16px;
    background-color: currentColor;
    margin-right: 4px;
    -webkit-mask-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' fill='none' viewBox='0 0 16 16'%3e%3cg clip-path='url(%23a)'%3e%3cpath fill='black' d='m14.476 6.987-1.49-.445a5.177 5.177 0 0 0-.422-1.035l.73-1.365a.27.27 0 0 0-.05-.32l-1.062-1.066a.271.271 0 0 0-.32-.05l-1.355.725a5.165 5.165 0 0 0-1.05-.444l-.444-1.471a.271.271 0 0 0-.262-.183H7.25a.271.271 0 0 0-.258.191l-.444 1.467a5.169 5.169 0 0 0-1.058.445l-1.333-.72a.271.271 0 0 0-.32.048l-1.08 1.054a.271.271 0 0 0-.05.32l.72 1.333a5.169 5.169 0 0 0-.444 1.053l-1.47.445a.271.271 0 0 0-.192.258v1.502a.271.271 0 0 0 .191.258l1.48.444c.11.36.26.708.445 1.036l-.73 1.395a.271.271 0 0 0 .05.32l1.062 1.062a.271.271 0 0 0 .32.05l1.373-.734a5.25 5.25 0 0 0 1.022.418l.445 1.498a.27.27 0 0 0 .258.19h1.502a.271.271 0 0 0 .258-.19l.444-1.503a5.17 5.17 0 0 0 1.013-.417l1.383.737a.27.27 0 0 0 .32-.049l1.062-1.062a.27.27 0 0 0 .049-.32l-.738-1.378a5.17 5.17 0 0 0 .422-1.017l1.498-.445a.271.271 0 0 0 .191-.258V7.25a.271.271 0 0 0-.164-.262ZM8 10.444a2.444 2.444 0 1 1 0-4.888 2.444 2.444 0 0 1 0 4.888Z'/%3e%3c/g%3e%3cdefs%3e%3cclipPath id='a'%3e%3cpath fill='white' d='M0 0h16v16H0z'/%3e%3c/clipPath%3e%3c/defs%3e%3c/svg%3e");
}

body:not(.setting-item-title-icon-remove):not(.is-phone) .vertical-tab-header-group:nth-last-of-type(2) .vertical-tab-header-group-title::before {
    content: " ";
    width: 16px;
    height: 16px;
    background-color: currentColor;
    margin-right: 4px;
    -webkit-mask-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' fill='none' viewBox='0 0 16 16'%3e%3cg clip-path='url(%23a)'%3e%3cpath fill='black' d='M13.249 7.111h-.36V3.924A.889.889 0 0 0 12 3.036H9.333A2.284 2.284 0 0 0 7.338.889a2.222 2.222 0 0 0-2.45 2.147h-3.11a.889.889 0 0 0-.89.888v3.632h1.25A1.391 1.391 0 0 1 3.556 8.75a1.333 1.333 0 0 1-.347 1.027 1.334 1.334 0 0 1-.987.444H.89v3.925a.889.889 0 0 0 .889.889H12a.889.889 0 0 0 .889-.89v-2.59h.444a2.223 2.223 0 0 0 2.223-2.45 2.29 2.29 0 0 0-2.307-1.995Z'/%3e%3c/g%3e%3cdefs%3e%3cclipPath id='a'%3e%3cpath fill='white' d='M0 0h16v16H0z'/%3e%3c/clipPath%3e%3c/defs%3e%3c/svg%3e");
}

body:not(.setting-item-title-icon-remove):not(.is-phone) .vertical-tab-header-group:nth-last-of-type(1) .vertical-tab-header-group-title::before {
    content: " ";
    width: 16px;
    height: 16px;
    background-color: currentColor;
    margin-right: 4px;
    -webkit-mask-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' fill='none' viewBox='0 0 16 16'%3e%3cg fill='black' clip-path='url(%23a)'%3e%3cpath d='M13.249 7.111h-.36v-.267H9.88a1.636 1.636 0 0 1-1.436-2.448l.89-1.467A2.276 2.276 0 0 0 7.337.889a2.222 2.222 0 0 0-2.45 2.147h-3.11a.889.889 0 0 0-.89.888v3.632h1.25A1.391 1.391 0 0 1 3.556 8.75a1.333 1.333 0 0 1-.347 1.027 1.334 1.334 0 0 1-.987.444H.89v3.925a.889.889 0 0 0 .889.889H12a.889.889 0 0 0 .889-.89v-2.59h.444a2.223 2.223 0 0 0 2.223-2.45 2.29 2.29 0 0 0-2.307-1.995Z'/%3e%3cpath d='M11.933.507 9.391 4.889a.569.569 0 0 0 .489.889h5.089a.569.569 0 0 0 .489-.89L12.916.508a.57.57 0 0 0-.983 0Z'/%3e%3c/g%3e%3cdefs%3e%3cclipPath id='a'%3e%3cpath fill='white' d='M0 0h16v16H0z'/%3e%3c/clipPath%3e%3c/defs%3e%3c/svg%3e");
}

body:not(.is-phone) .horizontal-tab-nav-item.is-active,
body:not(.is-phone) .vertical-tab-nav-item.is-active {
    color: var(--nav-item-color-active);
    background-color: var(--nav-item-background-active);
}

/* ====== UI Components====== */
.theme-light {
    --toggle-thumb-color: var(--toggle-thumb-color-light);
    --toggle-thumb-enabled-color: var(--toggle-thumb-enabled-color-light);
    --toggle-thumb-color-light: white;
    --toggle-thumb-enabled-color-light: white;

    --toggle-thumb-shadow: var(--toggle-thumb-shadow-light);
    --toggle-thumb-enabled-shadow: var(--toggle-thumb-enabled-shadow-light);
    --toggle-thumb-shadow-light: 0px 4px 4px rgba(0, 0, 0, 0.05), 0px 1px 2px rgba(0, 0, 0, 0.2), inset 0px -1px 1px rgba(255, 255, 255, 0.4), inset 0px -4px 4px rgba(0, 0, 0, 0.05);
    --toggle-thumb-enabled-shadow-light: 0px 4px 4px rgba(0, 0, 0, 0.05), 0px 1px 2px rgba(0, 0, 0, 0.2), inset 0px -1px 1px rgba(255, 255, 255, 0.4), inset 0px -4px 4px hsla(var(--interactive-accent-hsl), 0.15);

    --toggle-track-color: var(--toggle-track-color-light);
    --toggle-track-enabled-color: var(--toggle-track-enabled-color-light);
    --toggle-track-color-light: var(--background-modifier-border-hover);
    --toggle-track-enabled-color-light: var(--interactive-accent);

    --toggle-track-shadow: var(--toggle-track-shadow-light);
    --toggle-track-hovered-shadow: var(--toggle-track-hovered-shadow-light);
    --toggle-track-shadow-light: inset 0px 1px 2px rgba(0, 0, 0, 0.25), 0px 1px 0px var(--background-primary), 0px 2px 4px rgba(0, 0, 0, 0.04);
    --toggle-track-hovered-shadow-light: inset 0px 1px 2px rgba(0, 0, 0, 0.25), 0px 1px 0px var(--background-primary), 0px 2px 4px rgba(0, 0, 0, 0.04);

    --interactive-normal: var(--color-base-05);
    --interactive-hover: var(--color-base-15);

    --input-radius: var(--input-radius-light);
    --input-shadow: var(--input-shadow-light);
    --input-shadow-hover: var(--input-shadow-hover-light);

    --input-radius-light: 5px;
    --input-shadow-light: 0px 4px 8px -4px rgba(0, 0, 0, 0.16), inset 0px -1px 0px rgba(0, 0, 0, 0.04), inset 0px 0px 0px 1px rgba(0, 0, 0, 0.12), inset 0px 2px 0.75px rgba(255, 255, 255, 0.24), inset 0px -3px 0.75px rgba(0, 0, 0, 0.04);
    --input-shadow-hover-light: 0px 4px 8px -4px rgba(0, 0, 0, 0.16), inset 0px -1px 0px rgba(0, 0, 0, 0.04), inset 0px 0px 0px 1px rgba(0, 0, 0, 0.12), inset 0px 2px 0.75px rgba(255, 255, 255, 0.24), inset 0px -3px 0.75px rgba(0, 0, 0, 0.04);
}

.theme-dark {
    --toggle-thumb-color: var(--toggle-thumb-color-dark);
    --toggle-thumb-enabled-color: var(--toggle-thumb-enabled-color-dark);
    --toggle-thumb-color-dark: white;
    --toggle-thumb-enabled-color-dark: white;

    --toggle-thumb-shadow: var(--toggle-thumb-shadow-dark);
    --toggle-thumb-enabled-shadow: var(--toggle-thumb-enabled-shadow-dark);
    --toggle-thumb-shadow-dark: 0px 4px 4px rgba(0, 0, 0, 0.3), 0px 1px 2px rgba(0, 0, 0, 0.2), inset 0px -1px 1px rgba(255, 255, 255, 0.3), inset 0px -4px 6px rgba(0, 0, 0, 0.2), inset 0px 4px 4px rgba(255, 255, 255, 0.4);
    --toggle-thumb-enabled-shadow-dark: 0px 4px 4px rgba(0, 0, 0, 0.1), 0px 1px 2px rgba(0, 0, 0, 0.1), inset 0px -1px 1px rgba(255, 255, 255, 0.3), inset 0px -4px 6px hsla(var(--interactive-accent-hsl), 0.4), inset 0px 4px 4px rgba(255, 255, 255, 0.4);

    --toggle-track-color: var(--toggle-track-color-dark);
    --toggle-track-enabled-color: var(--toggle-track-enabled-color-dark);
    --toggle-track-color-dark: var(--background-modifier-border-hover);
    --toggle-track-enabled-color-dark: var(--interactive-accent);

    --toggle-track-shadow: var(--toggle-track-shadow-dark);
    --toggle-track-hovered-shadow: var(--toggle-track-hovered-shadow-dark);
    --toggle-track-shadow-dark: inset 0px -1px 2px rgba(0, 0, 0, 0.2), inset 0px 2px 4px rgba(0, 0, 0, 0.4), 0px 0px 0px 1px var(--background-modifier-border);
    --toggle-track-hovered-shadow-dark: inset 0px -1px 2px rgba(0, 0, 0, 0.2), inset 0px 2px 4px rgba(0, 0, 0, 0.4), 0px 0px 0px 1px var(--background-modifier-border);

    --interactive-normal: hsl(var(--accent-h),
            calc(var(--accent-s) / 7),
            calc(1.25*var(--accent-l) / 3));
    --interactive-hover: hsl(var(--accent-h),
            calc(var(--accent-s) / 7),
            calc(1.25*var(--accent-l) / 2.7));

    --input-radius: var(--input-radius-dark);
    --input-shadow: var(--input-shadow-dark);
    --input-shadow-hover: var(--input-shadow-hover-dark);

    --input-radius-dark: 5px;
    --input-shadow-dark: 0px 4px 8px -4px rgba(0, 0, 0, 0.32), 0px 1px 2px rgba(0, 0, 0, 0.32), 0px 0px 0px 1px rgba(0, 0, 0, 0.32), 0px 0px 1px 1px rgba(0, 0, 0, 0.12), inset 0px 1px 0.75px rgba(255, 255, 255, 0.16), inset 0px -2px 0.75px rgba(0, 0, 0, 0.16);
    --input-shadow-hover-dark: 0px 4px 8px -4px rgba(0, 0, 0, 0.32), 0px 1px 2px rgba(0, 0, 0, 0.32), 0px 0px 0px 1px rgba(0, 0, 0, 0.32), 0px 0px 1px 1px rgba(0, 0, 0, 0.12), inset 0px 1px 0.75px rgba(255, 255, 255, 0.16), inset 0px -2px 0.75px rgba(0, 0, 0, 0.16);
}

.theme-light.is-mobile,
.theme-dark.is-mobile {
    --input-shadow: none;
    --input-shadow-hover: none;
}

/* Range slider */
input[type='range']::-webkit-slider-thumb {
    box-shadow: var(--toggle-thumb-shadow);
    transition: none;
}

input[type=range]::-webkit-slider-thumb:is(:hover, :active) {
    box-shadow: var(--toggle-thumb-shadow);
    outline-offset: 2px;
    outline: 2px solid var(--background-modifier-border-hover);
    transition: none;
    border-color: var(--slider-thumb-border-color);
}

body:not(.is-mobile) input[type=range]:is(:focus, :focus-visible)::-webkit-slider-thumb {
    box-shadow: var(--toggle-thumb-shadow);
    outline-offset: 2px;
    outline: 2px solid var(--background-modifier-border-focus);
    transition: none;
    border-color: var(--slider-thumb-border-color);
}

/* input */
textarea,
input.metadata-input-text,
input[type='date'],
input[type='datetime-local'],
input[type='text'],
input[type='search'],
input[type='email'],
input[type='password'],
input[type='number'] {
    border-color: var(--background-modifier-border-focus);
}

@media (hover: hover) {

    textarea:hover,
    input.metadata-input-text:hover,
    input[type='date']:hover,
    input[type='datetime-local']:hover,
    input[type='text']:hover,
    input[type='search']:hover,
    input[type='email']:hover,
    input[type='password']:hover,
    input[type='number']:hover {
        border-color: var(--background-modifier-border-focus);
    }
}

.theme-light input[type=checkbox],
body:not(.is-mobile).theme-light textarea:not([class]),
body:not(.is-mobile).theme-light input[type='text']:not([class]),
body:not(.is-mobile).theme-light input[type='search']:not([class]),
body:not(.is-mobile).theme-light input[type='email']:not([class]),
body:not(.is-mobile).theme-light input[type='password']:not([class]),
body:not(.is-mobile).theme-light input[type='number']:not([class]) {
    box-shadow: 0 1px 2px 0px hsla(var(--accent-h), 18%, 80%, 0.2);
}

body:not(.is-mobile) textarea:not([class]):is(:focus, :focus-visible),
body:not(.is-mobile) input[type='text']:not([class]):is(:focus, :focus-visible),
body:not(.is-mobile) input[type='search']:not([class]):is(:focus, :focus-visible),
body:not(.is-mobile) input[type='email']:not([class]):is(:focus, :focus-visible),
body:not(.is-mobile) input[type='password']:not([class]):is(:focus, :focus-visible),
body:not(.is-mobile) input[type='number']:not([class]):is(:focus, :focus-visible) {
    border: 1px solid var(--interactive-accent);
    box-shadow: 0 0 0 4px var(--background-modifier-active-hover);
}

.view-header input,
body:not(.is-mobile) input.prompt-input {
    border: none !important;
    box-shadow: none !important;
}

body:not(.is-phone) div:has(>input.prompt-input) {
    border-bottom: 2px solid var(--color-accent) !important;
}

.kanban-plugin__lane-input>textarea,
.kanban-plugin__grow-wrap>textarea {
    --background-modifier-form-field: transparent;
}

/* dropdown  */

select,
.dropdown {
    cursor: pointer;
}

/*toggle switch*/

/*track*/

.checkbox-container {
    background-color: var(--toggle-track-color);
    box-shadow: var(--toggle-track-shadow);
}

.checkbox-container.is-enabled {
    background-color: var(--toggle-track-enabled-color);
}

@media (hover: hover) {
    .checkbox-container:hover {
        box-shadow: var(--toggle-track-hovered-shadow);
    }
}

/*thumb*/

.checkbox-container:after {
    background-color: var(--toggle-thumb-color);
    box-shadow: var(--toggle-thumb-shadow);
}

.checkbox-container.is-enabled:after {
    background-color: var(--toggle-thumb-enabled-color);
    box-shadow: var(--toggle-thumb-enabled-shadow);
}

body {
    /* Buttons */
    --button-radius: var(--input-radius);
}


/* container */
.modal.mod-canvas-help,
.suggestion-container,
.popover,
.prompt,
.menu,
.suggestion-container.mod-search-suggestion {
    background-color: var(--workspace-background-translucent);
    backdrop-filter: blur(12px) saturate(190%) contrast(50%) brightness(130%);
    -webkit-backdrop-filter: blur(12px) saturate(190%) contrast(50%) brightness(130%);
}

.theme-dark .modal.mod-canvas-help,
.theme-dark .suggestion-container,
.theme-dark .popover,
.theme-dark.is-mobile .prompt,
.theme-dark .prompt,
.theme-dark .menu,
.theme-dark .suggestion-container.mod-search-suggestion {
    background-color: var(--workspace-background-translucent);
    backdrop-filter: blur(10px) saturate(190%) contrast(70%) brightness(80%);
    -webkit-backdrop-filter: blur(10px) saturate(190%) contrast(70%) brightness(80%);
}

.suggestion-container.mod-search-suggestion .suggestion {
    background-color: transparent;
}

.prompt-instructions {
    border-top: 1px solid var(--divider-color);
}

input.prompt-input {
    background-color: transparent !important;
}

/* notice */
.notice-container {
    top: 34px;
    padding-right: 16px;
}

.tab-autohide .notice-container {
    top: 6px;
}

/* Prompts */

body {
    --prompt-border-color: var(--background-modifier-border);
}

/* ====== editor ====== */
/* background pattern */
body {
    --grid-background-pattern-size: 36px;
    --grid-background-pattern-color: var(--background-modifier-border);
}

.editor-grid-background-pattren .workspace-leaf-content[data-type="markdown"],
.editor-grid-background-pattren .export-image-preview-container.markdown-rendered {
    background-image: linear-gradient(to right, var(--grid-background-pattern-color) 1px, transparent 1px),
        linear-gradient(to bottom, var(--grid-background-pattern-color) 1px, transparent 1px);
    background-size: var(--grid-background-pattern-size) var(--grid-background-pattern-size);
    background-position: center;
}


/* Paragraphs */

body.p-spacing-br :is(.markdown-preview-view, .markdown-rendered) p>br {
    content: " ";
    margin-top: var(--p-spacing);
    display: block;
}

.markdown-source-view.mod-cm6 .cm-content>div:first-of-type {
    padding-top: 0 !important;
}

/* line-height */
body {
    --line-height-normal: 1.5;
    --line-height-tight: 1.3;
    --line-height-customize: var(--line-height-normal);
}

.markdown-preview-view,
.markdown-source-view.mod-cm6 .cm-scroller {
    line-height: var(--line-height-customize);
}

/* justify text */
body.text-align-justify .markdown-source-view.mod-cm6 .cm-line,
body.text-align-justify .markdown-preview-view p {
    text-align: justify;
    text-justify: auto;
}


/* ====== title style ====== */
body {
    /* Inline title */
    --inline-title-color: var(--h1-color);
    --inline-title-font: var(--h1-font);
    --inline-title-line-height: var(--h1-line-height);
    --inline-title-size: var(--h1-size);
    --inline-title-style: var(--h1-style);
    --inline-title-variant: var(--h1-variant);
    --inline-title-weight: var(--h1-weight);
    /* Headings */
    --heading-formatting: var(--text-faint);
    --h1-color: inherit;
    --h2-color: inherit;
    --h3-color: inherit;
    --h4-color: inherit;
    --h5-color: inherit;
    --h6-color: inherit;
    --h1-accent-color: var(--color-red);
    --h2-accent-color: var(--color-orange);
    --h3-accent-color: var(--color-yellow);
    --h4-accent-color: var(--color-green);
    --h5-accent-color: var(--color-blue);
    --h6-accent-color: var(--color-purple);
    --h1-font: inherit;
    --h2-font: inherit;
    --h3-font: inherit;
    --h4-font: inherit;
    --h5-font: inherit;
    --h6-font: inherit;
    --h1-line-height: 1.2;
    --h2-line-height: 1.2;
    --h3-line-height: 1.3;
    --h4-line-height: 1.4;
    --h5-line-height: var(--line-height-normal);
    --h6-line-height: var(--line-height-normal);
    --h1-size: 1.5em;
    --h2-size: 1.425em;
    --h3-size: 1.35em;
    --h4-size: 1.275em;
    --h5-size: 1.2em;
    --h6-size: 1.125em;
    --h1-style: normal;
    --h2-style: normal;
    --h3-style: normal;
    --h4-style: normal;
    --h5-style: normal;
    --h6-style: normal;
    --h1-variant: normal;
    --h2-variant: normal;
    --h3-variant: normal;
    --h4-variant: normal;
    --h5-variant: normal;
    --h6-variant: normal;
    --h1-weight: 700;
    --h2-weight: 675;
    --h3-weight: 650;
    --h4-weight: 625;
    --h5-weight: 600;
    --h6-weight: 575;
}

body {
    /* Extra */
    --inline-title-text-transform: var(--h1-text-transform);
    --h1-text-transform: unset;
    --h2-text-transform: unset;
    --h3-text-transform: unset;
    --h4-text-transform: unset;
    --h5-text-transform: unset;
    --h6-text-transform: unset;
}

.inline-title {
    text-transform: var(--inline-title-text-transform);
}

body:not(.is-phone):not(.inline-title-divider-remove) .inline-title:not(.mk-inline-title) {
    border-bottom: 1px solid var(--divider-color);
    padding-bottom: var(--size-4-3);
    margin-bottom: var(--size-4-3);
}

.embedded-backlinks {
    border-top: 1px solid var(--divider-color);
}

.markdown-rendered>:is(h1, h2, h3, h4, h5, h6),
.markdown-preview-sizer>div>:is(h1, h2, h3, h4, h5, h6) {
    position: relative;
}

body:not(.heading-indicator-off) .markdown-preview-view .heading-collapse-indicator {
    transform: translateX(-16px);
}

body:not(.heading-indicator-off) .is-live-preview .HyperMD-header .collapse-indicator.collapse-icon {
    transform: translateX(-8px);
}

body:not(.heading-indicator-off) .markdown-rendered>:is(h1, h2, h3, h4, h5, h6),
body:not(.heading-indicator-off) .markdown-preview-sizer>div>:is(h1, h2, h3, h4, h5, h6) {
    text-indent: 9px;
}

@media print {
    :is(h1, h2, h3, h4, h5, h6) {
        position: relative;
    }

    body:not(.heading-indicator-off) :is(h1, h2, h3, h4, h5, h6) {
        text-indent: 9px;
    }
}

/* h1 */
h1,
.markdown-rendered h1,
.HyperMD-header-1,
.inline-title[data-level='1'],
.HyperMD-list-line .cm-header-1 {
    text-transform: var(--h1-text-transform);
}

.h1-color-designated {
    --h1-color: var(--h1-accent-color) !important;
}

body:not(.heading-indicator-off) .is-live-preview .HyperMD-header-1::before {
    margin-right: 6px;
    content: " ";
    display: inline-block;
    width: 3px;
    height: calc(1.2em - 8px);
    border-radius: var(--radius-m);
    background-color: var(--h1-accent-color);
    transform: translateY(4px);
}

body:not(.heading-indicator-off) .markdown-rendered>h1::before,
body:not(.heading-indicator-off) .markdown-preview-sizer>div>h1::before {
    margin-right: 6px;
    content: " ";
    display: inline-block;
    width: 3px;
    height: calc(1.2em - 8px);
    border-radius: var(--radius-m);
    background-color: var(--h1-accent-color);
    position: absolute;
    top: 4px;
    left: 0px;
}


body.h1-divider-on :is(.markdown-preview-sizer>div>h1, .markdown-rendered>h1, .HyperMD-header-1)::after {
    content: "";
    position: absolute;
    height: 1px;
    width: 100%;
    right: 0px;
    opacity: 0.5;
    bottom: 0;
    background-image: linear-gradient(to left, var(--h1-accent-color) 30%, transparent 70%);
}

@media print {
    body:not(.heading-indicator-off) h1::before {
        margin-right: 6px;
        content: " ";
        display: inline-block;
        width: 3px;
        height: calc(1.2em - 8px);
        border-radius: var(--radius-m);
        background-color: var(--h1-accent-color);
        position: absolute;
        top: 4px;
        left: 0px;
    }

    body.h1-divider-on h1::after {
        content: "";
        position: absolute;
        height: 1px;
        width: 100%;
        right: 0px;
        opacity: 0.5;
        bottom: 0;
        background-image: linear-gradient(to left, var(--h1-accent-color) 30%, transparent 70%);
    }
}

body:not(.collapse-icon-restore) .is-live-preview .HyperMD-header-1 .collapse-indicator.collapse-icon svg,
body:not(.collapse-icon-restore) :is(.markdown-rendered, .markdown-preview-view) h1 .collapse-indicator.collapse-icon svg {
    transform: translateX(0px);
    background-color: currentColor;
    -webkit-mask-image: url('data:image/svg+xml;utf8,<?xml version="1.0" encoding="UTF-8"?><svg id="svg0" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 10 10"><defs><style>.cls-1{fill:none;}</style></defs><rect class="cls-1" width="10" height="10"/><path d="M0,1H1.18v3.43H3.53V1h1.18V9h-1.18v-3.43H1.18v3.43H0V1ZM6.47,9v-1.14h1.18V2.32l-1.47,.82V1.82l1.47-.82h1.18V7.86h1.18v1.14h-3.53Z"/></svg>')
}

body:not(.collapse-icon-restore) .is-live-preview .HyperMD-header-1 .is-collapsed .collapse-indicator.collapse-icon svg,
body:not(.collapse-icon-restore) :is(.markdown-rendered, .markdown-preview-view) .is-collapsed h1 .collapse-indicator.collapse-icon svg {
    background-color: var(--h1-accent-color);
    color: var(--h1-accent-color);
}

h2,
.markdown-rendered h2,
.HyperMD-header-2,
.inline-title[data-level='2'],
.HyperMD-list-line .cm-header-2 {
    text-transform: var(--h2-text-transform);
}

.h2-color-designated {
    --h2-color: var(--h2-accent-color) !important;
}

body:not(.heading-indicator-off) .is-live-preview .HyperMD-header-2::before {
    margin-right: 6px;
    content: " ";
    display: inline-block;
    width: 3px;
    height: calc(1.2em - 8px);
    border-radius: var(--radius-m);
    background-color: var(--h2-accent-color);
    transform: translateY(4px);
}

body:not(.heading-indicator-off) .markdown-rendered>h2::before,
body:not(.heading-indicator-off) .markdown-preview-sizer>div>h2::before {
    margin-right: 6px;
    content: " ";
    display: inline-block;
    width: 3px;
    height: calc(1.2em - 8px);
    border-radius: var(--radius-m);
    background-color: var(--h2-accent-color);
    position: absolute;
    top: 4px;
    left: 0px;
}


body.h2-divider-on :is(.markdown-preview-sizer>div>h2, .markdown-rendered>h2, .HyperMD-header-2)::after {
    content: "";
    position: absolute;
    height: 1px;
    width: 100%;
    right: 0px;
    opacity: 0.5;
    bottom: 0;
    background-image: linear-gradient(to left, var(--h2-accent-color) 30%, transparent 70%);
}

@media print {
    body:not(.heading-indicator-off) h2::before {
        margin-right: 6px;
        content: " ";
        display: inline-block;
        width: 3px;
        height: calc(1.2em - 8px);
        border-radius: var(--radius-m);
        background-color: var(--h2-accent-color);
        position: absolute;
        top: 4px;
        left: 0px;
    }

    body.h2-divider-on h2::after {
        content: "";
        position: absolute;
        height: 1px;
        width: 100%;
        right: 0px;
        opacity: 0.5;
        bottom: 0;
        background-image: linear-gradient(to left, var(--h2-accent-color) 30%, transparent 70%);
    }
}

body:not(.collapse-icon-restore) .is-live-preview .HyperMD-header-2 .collapse-indicator.collapse-icon svg,
body:not(.collapse-icon-restore) :is(.markdown-rendered, .markdown-preview-view) h2 .collapse-indicator.collapse-icon svg {
    transform: translateX(0px);
    background-color: currentColor;
    -webkit-mask-image: url('data:image/svg+xml;utf8,<?xml version="1.0" encoding="UTF-8"?><svg id="svg0" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 10 10"><defs><style>.cls-1{fill:none;}</style></defs><rect class="cls-1" width="10" height="10"/><path d="M0,1H1.11v3.43H3.33V1h1.11V9h-1.11v-3.43H1.11v3.43H0V1ZM10,9h-3.33c-.29,0-.58-.12-.79-.33-.21-.21-.33-.51-.33-.81s.11-.57,.3-.78l2.71-2.99c.21-.21,.33-.49,.33-.81s-.12-.59-.33-.81c-.21-.21-.49-.33-.79-.33s-.58,.12-.79,.33c-.21,.21-.33,.51-.33,.81h-1.11c0-.61,.23-1.19,.65-1.62,.42-.43,.98-.67,1.57-.67s1.15,.24,1.57,.67c.42,.43,.65,1.01,.65,1.62s-.25,1.2-.65,1.62l-2.68,2.95h3.33v1.14Z"/></svg>')
}

body:not(.collapse-icon-restore) .is-live-preview .HyperMD-header-2 .is-collapsed .collapse-indicator.collapse-icon svg,
body:not(.collapse-icon-restore) :is(.markdown-rendered, .markdown-preview-view) .is-collapsed h2 .collapse-indicator.collapse-icon svg {
    background-color: var(--h2-accent-color);
    color: var(--h2-accent-color);
}

h3,
.markdown-rendered h3,
.HyperMD-header-3,
.inline-title[data-level='3'],
.HyperMD-list-line .cm-header-3 {
    text-transform: var(--h3-text-transform);
}

.h3-color-designated {
    --h3-color: var(--h3-accent-color) !important;
}

body:not(.heading-indicator-off) .is-live-preview .HyperMD-header-3::before {
    margin-right: 6px;
    content: " ";
    display: inline-block;
    width: 3px;
    height: calc(1.3em - 8px);
    border-radius: var(--radius-m);
    background-color: var(--h3-accent-color);
    transform: translateY(4px);
}

body:not(.heading-indicator-off) .markdown-rendered>h3::before,
body:not(.heading-indicator-off) .markdown-preview-sizer>div>h3::before {
    margin-right: 6px;
    content: " ";
    display: inline-block;
    width: 3px;
    height: calc(1.3em - 8px);
    border-radius: var(--radius-m);
    background-color: var(--h3-accent-color);
    position: absolute;
    top: 4px;
    left: 0px;
}

body.h3-divider-on :is(.markdown-preview-sizer>div>h3, .markdown-rendered>h3, .HyperMD-header-3)::after {
    content: "";
    position: absolute;
    height: 1px;
    width: 100%;
    right: 0px;
    opacity: 0.5;
    bottom: 0;
    background-image: linear-gradient(to left, var(--h3-accent-color) 30%, transparent 70%);
}

@media print {
    body:not(.heading-indicator-off) h3::before {
        margin-right: 6px;
        content: " ";
        display: inline-block;
        width: 3px;
        height: calc(1.2em - 8px);
        border-radius: var(--radius-m);
        background-color: var(--h3-accent-color);
        position: absolute;
        top: 4px;
        left: 0px;
    }

    body.h3-divider-on h3::after {
        content: "";
        position: absolute;
        height: 1px;
        width: 100%;
        right: 0px;
        opacity: 0.5;
        bottom: 0;
        background-image: linear-gradient(to left, var(--h3-accent-color) 30%, transparent 70%);
    }
}

body:not(.collapse-icon-restore) .is-live-preview .HyperMD-header-3 .collapse-indicator.collapse-icon svg,
body:not(.collapse-icon-restore) :is(.markdown-rendered, .markdown-preview-view) h3 .collapse-indicator.collapse-icon svg {
    transform: translateX(0px);
    background-color: currentColor;
    -webkit-mask-image: url('data:image/svg+xml;utf8,<?xml version="1.0" encoding="UTF-8"?><svg id="svg0" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 10 10"><defs><style>.cls-1{fill:none;}</style></defs><rect class="cls-1" width="10" height="10"/><path d="M0,1H1.11v3.43H3.33V1h1.11V9h-1.11v-3.43H1.11v3.43H0V1Zm6.67,0h2.22c.29,0,.58,.12,.79,.33,.21,.21,.33,.51,.33,.81V7.86c0,.3-.12,.59-.33,.81-.21,.21-.49,.33-.79,.33h-2.22c-.29,0-.58-.12-.79-.33-.21-.21-.33-.51-.33-.81v-.57h1.11v.57h2.22v-2.29h-2.22v-1.14h2.22V2.14h-2.22v.57h-1.11v-.57c0-.3,.12-.59,.33-.81,.21-.21,.49-.33,.79-.33Z"/></svg>')
}

body:not(.collapse-icon-restore) .is-live-preview .HyperMD-header-3 .is-collapsed .collapse-indicator.collapse-icon svg,
body:not(.collapse-icon-restore) :is(.markdown-rendered, .markdown-preview-view) .is-collapsed h3 .collapse-indicator.collapse-icon svg {
    background-color: var(--h3-accent-color);
    color: var(--h3-accent-color);
}

h4,
.markdown-rendered h4,
.HyperMD-header-4,
.inline-title[data-level='4'],
.HyperMD-list-line .cm-header-4 {
    text-transform: var(--h4-text-transform);
}

.h4-color-designated {
    --h4-color: var(--h4-accent-color) !important;
}

body:not(.heading-indicator-off) .is-live-preview .HyperMD-header-4::before {
    margin-right: 6px;
    content: " ";
    display: inline-block;
    width: 3px;
    height: calc(1.4em - 8px);
    border-radius: var(--radius-m);
    background-color: var(--h4-accent-color);
    transform: translateY(4px);
}

body:not(.heading-indicator-off) .markdown-rendered>h4::before,
body:not(.heading-indicator-off) .markdown-preview-sizer>div>h4::before {
    margin-right: 6px;
    content: " ";
    display: inline-block;
    width: 3px;
    height: calc(1.4em - 8px);
    border-radius: var(--radius-m);
    background-color: var(--h4-accent-color);
    position: absolute;
    top: 4px;
    left: 0px;
}


body.h4-divider-on :is(.markdown-rendered>h4, .markdown-preview-sizer>div>h4, .HyperMD-header-4)::after {
    content: "";
    position: absolute;
    height: 1px;
    width: 100%;
    right: 0px;
    opacity: 0.5;
    bottom: 0;
    background-image: linear-gradient(to left, var(--h4-accent-color) 30%, transparent 70%);
}

@media print {
    body:not(.heading-indicator-off) h4::before {
        margin-right: 6px;
        content: " ";
        display: inline-block;
        width: 3px;
        height: calc(1.2em - 8px);
        border-radius: var(--radius-m);
        background-color: var(--h4-accent-color);
        position: absolute;
        top: 4px;
        left: 0px;
    }

    body.h4-divider-on h4::after {
        content: "";
        position: absolute;
        height: 1px;
        width: 100%;
        right: 0px;
        opacity: 0.5;
        bottom: 0;
        background-image: linear-gradient(to left, var(--h4-accent-color) 30%, transparent 70%);
    }
}

body:not(.collapse-icon-restore) .is-live-preview .HyperMD-header-4 .collapse-indicator.collapse-icon svg,
body:not(.collapse-icon-restore) :is(.markdown-rendered, .markdown-preview-view) h4 .collapse-indicator.collapse-icon svg {
    transform: translateX(0px);
    background-color: currentColor;
    -webkit-mask-image: url('data:image/svg+xml;utf8,<?xml version="1.0" encoding="UTF-8"?><svg id="svg0" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 10 10"><defs><style>.cls-1{fill:none;}</style></defs><rect class="cls-1" width="10" height="10"/><path d="M0,1H1.11v3.43H3.33V1h1.11V9h-1.11v-3.43H1.11v3.43H0V1ZM8.33,9v-2.86h-2.78v-1.14L8.33,1h1.11V5h.56v1.14h-.56v2.86h-1.11Zm0-4V2.95l-1.42,2.05h1.42Z"/></svg>')
}

body:not(.collapse-icon-restore) .is-live-preview .HyperMD-header-4 .is-collapsed .collapse-indicator.collapse-icon svg,
body:not(.collapse-icon-restore) :is(.markdown-rendered, .markdown-preview-view) .is-collapsed h4 .collapse-indicator.collapse-icon svg {
    background-color: var(--h4-accent-color);
    color: var(--h4-accent-color);
}

h5,
.markdown-rendered h5,
.HyperMD-header-5,
.inline-title[data-level='5'],
.HyperMD-list-line .cm-header-5 {
    text-transform: var(--h5-text-transform);
}

.h5-color-designated {
    --h5-color: var(--h5-accent-color) !important;
}

body:not(.heading-indicator-off) .is-live-preview .HyperMD-header-5::before {
    margin-right: 6px;
    content: " ";
    display: inline-block;
    width: 3px;
    height: calc(1.5em - 8px);
    border-radius: var(--radius-m);
    background-color: var(--h5-accent-color);
    transform: translateY(4px);
}

body:not(.heading-indicator-off) .markdown-rendered>h5::before,
body:not(.heading-indicator-off) .markdown-preview-sizer>div>h5::before {
    margin-right: 6px;
    content: " ";
    display: inline-block;
    width: 3px;
    height: calc(1.5em - 8px);
    border-radius: var(--radius-m);
    background-color: var(--h5-accent-color);
    position: absolute;
    top: 4px;
    left: 0px;
}


body.h5-divider-on :is(.markdown-rendered>h5, .markdown-preview-sizer>div>h5, .HyperMD-header-5)::after {
    content: "";
    position: absolute;
    height: 1px;
    width: 100%;
    right: 0px;
    opacity: 0.5;
    bottom: 0;
    background-image: linear-gradient(to left, var(--h5-accent-color) 30%, transparent 70%);
}

@media print {
    body:not(.heading-indicator-off) h5::before {
        margin-right: 6px;
        content: " ";
        display: inline-block;
        width: 3px;
        height: calc(1.2em - 8px);
        border-radius: var(--radius-m);
        background-color: var(--h5-accent-color);
        position: absolute;
        top: 4px;
        left: 0px;
    }

    body.h5-divider-on h5::after {
        content: "";
        position: absolute;
        height: 1px;
        width: 100%;
        right: 0px;
        opacity: 0.5;
        bottom: 0;
        background-image: linear-gradient(to left, var(--h5-accent-color) 30%, transparent 70%);
    }
}

body:not(.collapse-icon-restore) .is-live-preview .HyperMD-header-5 .collapse-indicator.collapse-icon svg,
body:not(.collapse-icon-restore) :is(.markdown-rendered, .markdown-preview-view) h5 .collapse-indicator.collapse-icon svg {
    transform: translateX(0px);
    background-color: currentColor;
    -webkit-mask-image: url('data:image/svg+xml;utf8,<?xml version="1.0" encoding="UTF-8"?><svg id="svg0" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 10 10"><defs><style>.cls-1{fill:none;}</style></defs><rect class="cls-1" width="10" height="10"/><path d="M0,1H1.11v3.43H3.33V1h1.11V9h-1.11v-3.43H1.11v3.43H0V1Zm6.67,0h2.78v1.14h-2.78v2.29h1.11c.59,0,1.15,.24,1.57,.67,.42,.43,.65,1.01,.65,1.62s-.23,1.19-.65,1.62c-.42,.43-.98,.67-1.57,.67h-1.11c-.29,0-.58-.12-.79-.33-.21-.21-.33-.51-.33-.81v-.57h1.11v.57h1.11c.29,0,.58-.12,.79-.33,.21-.21,.33-.51,.33-.81s-.12-.59-.33-.81c-.21-.21-.49-.33-.79-.33h-1.11c-.29,0-.58-.12-.79-.33-.21-.21-.33-.51-.33-.81V2.14c0-.3,.12-.59,.33-.81,.21-.21,.49-.33,.79-.33Z"/></svg>')
}

body:not(.collapse-icon-restore) .is-live-preview .HyperMD-header-5 .is-collapsed .collapse-indicator.collapse-icon svg,
body:not(.collapse-icon-restore) :is(.markdown-rendered, .markdown-preview-view) .is-collapsed h5 .collapse-indicator.collapse-icon svg {
    background-color: var(--h5-accent-color);
    color: var(--h5-accent-color);
}

h6,
.markdown-rendered h6,
.HyperMD-header-6,
.inline-title[data-level='6'],
.HyperMD-list-line .cm-header-6 {
    text-transform: var(--h6-text-transform);
}

.h6-color-designated {
    --h6-color: var(--h6-accent-color) !important;
}

body:not(.heading-indicator-off) .is-live-preview .HyperMD-header-6::before {
    margin-right: 6px;
    content: " ";
    display: inline-block;
    width: 3px;
    height: calc(1.5em - 8px);
    border-radius: var(--radius-m);
    background-color: var(--h6-accent-color);
    transform: translateY(4px);
}

body:not(.heading-indicator-off) .markdown-rendered>h6::before,
body:not(.heading-indicator-off) .markdown-preview-sizer>div>h6::before {
    margin-right: 6px;
    content: " ";
    display: inline-block;
    width: 3px;
    height: calc(1.5em - 8px);
    border-radius: var(--radius-m);
    background-color: var(--h6-accent-color);
    position: absolute;
    top: 4px;
    left: 0px;
}


body.h6-divider-on :is(.markdown-rendered>h6, .markdown-preview-sizer>div>h6, .HyperMD-header-6)::after {
    content: "";
    position: absolute;
    height: 1px;
    width: 100%;
    right: 0px;
    opacity: 0.5;
    bottom: 0;
    background-image: linear-gradient(to left, var(--h6-accent-color) 30%, transparent 70%);
}

@media print {
    body:not(.heading-indicator-off) h6::before {
        margin-right: 6px;
        content: " ";
        display: inline-block;
        width: 3px;
        height: calc(1.2em - 8px);
        border-radius: var(--radius-m);
        background-color: var(--h6-accent-color);
        position: absolute;
        top: 4px;
        left: 0px;
    }

    body.h6-divider-on h6::after {
        content: "";
        position: absolute;
        height: 1px;
        width: 100%;
        right: 0px;
        opacity: 0.5;
        bottom: 0;
        background-image: linear-gradient(to left, var(--h6-accent-color) 30%, transparent 70%);
    }
}

body:not(.collapse-icon-restore) .is-live-preview .HyperMD-header-6 .collapse-indicator.collapse-icon svg,
body:not(.collapse-icon-restore) :is(.markdown-rendered, .markdown-preview-view) h6 .collapse-indicator.collapse-icon svg {
    transform: translateX(0px);
    background-color: currentColor;
    -webkit-mask-image: url('data:image/svg+xml;utf8,<?xml version="1.0" encoding="UTF-8"?><svg id="svg0" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 10 10"><defs><style>.cls-1{fill:none;}</style></defs><rect class="cls-1" width="10" height="10"/><path d="M0,1H1.11v3.43H3.33V1h1.11V9h-1.11v-3.43H1.11v3.43H0V1Zm6.67,0h2.22c.29,0,.58,.12,.79,.33,.21,.21,.33,.51,.33,.81v.57h-1.11v-.57h-2.22v2.29h2.22c.29,0,.58,.12,.79,.33,.21,.21,.33,.51,.33,.81v2.29c0,.3-.12,.59-.33,.81-.21,.21-.49,.33-.79,.33h-2.22c-.29,0-.58-.12-.79-.33-.21-.21-.33-.51-.33-.81V2.14c0-.3,.12-.59,.33-.81,.21-.21,.49-.33,.79-.33Zm0,4.57v2.29h2.22v-2.29h-2.22Z"/></svg>')
}

body:not(.collapse-icon-restore) .is-live-preview .HyperMD-header-6 .is-collapsed .collapse-indicator.collapse-icon svg,
body:not(.collapse-icon-restore) :is(.markdown-rendered, .markdown-preview-view) .is-collapsed h6 .collapse-indicator.collapse-icon svg {
    background-color: var(--h6-accent-color);
    color: var(--h6-accent-color);
}




/* ====== link ====== */
body {
    /* Links */
    --link-external-color: var(--color-blue);
    --link-external-color-hover: var(--color-blue);
}

.cm-s-obsidian span:is(.cm-link, .cm-url) a {
    color: var(--link-external-color);
}

a:not(.tag, .raindrop-tag, .clickable-icon, :has(img:not(.link-favicon))):hover,
.cm-s-obsidian .cm-line:not(.cm-active) span.cm-hmd-internal-link:hover,
.external-link:not(:has(img:not(.link-favicon))):hover,
.cm-s-obsidian .cm-line:not(.cm-active) span:is(.cm-link, .cm-url):hover {
    border-radius: var(--radius-s);
    background-color: var(--background-modifier-hover);
    transition: background-color var(--anim-duration-fast) var(--anim-motion-smooth);
}

/* ====== tag====== */
body {
    /* Tags */
    --tag-size: var(--font-smaller);
    --tag-color: var(--text-accent);
    --tag-color-hover: var(--text-accent);
    --tag-decoration: none;
    --tag-decoration-hover: none;
    --tag-background: hsla(var(--interactive-accent-hsl), 0.1);
    --tag-background-hover: hsla(var(--interactive-accent-hsl), 0.2);
    --tag-border-color: hsla(var(--interactive-accent-hsl), 0.15);
    --tag-border-color-hover: hsla(var(--interactive-accent-hsl), 0.15);
    --tag-border-width: 0px;
    --tag-padding-x: 0.65em;
    --tag-padding-y: 0.25em;
    --tag-radius: 2em;
}

@media (hover: hover) {

    .cm-hashtag-end:hover,
    .cm-hashtag-begin:has(+.cm-hashtag-end:hover),
    .cm-hashtag-begin:hover,
    .cm-hashtag-begin:hover+.cm-hashtag-end {
        background-color: var(--tag-background-hover);
        border: var(--tag-border-width) solid var(--tag-border-color-hover);
        color: var(--tag-color-hover);
        text-decoration: var(--tag-decoration-hover);
    }
}


/* ====== line emphasis ====== */
/* ====== highlight ====== */
body {
    --line-normal-opacity: 0.5;
}

.border-focus-mode .cm-lineWrapping>.cm-line.cm-active:not(:has(.mk-note-footer, .mk-floweditor-container)) {
    background-color: var(--line-active-bg);
    border-radius: var(--radius-s);
    opacity: 1;
}

.border-focus-mode .cm-lineWrapping>.cm-line {
    padding-left: var(--size-4-1);
    padding-right: var(--size-4-1);
    opacity: var(--line-normal-opacity);
}

.border-focus-mode .cm-lineWrapping>.cm-table-widget .cm-line {
    background-color: unset !important;
    border-radius: unset !important;
    padding-left: unset !important;
    padding-right: unset !important;
}

.border-focus-mode .markdown-source-view .cm-scroller .cm-line.cm-active.HyperMD-codeblock-bg:not(.HyperMD-codeblock-begin-bg, .HyperMD-codeblock-end-bg) {
    border-radius: 0px;
}

.border-focus-mode .markdown-source-view .cm-scroller .cm-line.cm-active.HyperMD-codeblock-begin-bg {
    border-bottom-left-radius: 0px;
    border-bottom-right-radius: 0px;
}

.border-focus-mode .markdown-source-view .cm-scroller .cm-line.cm-active.HyperMD-codeblock-end-bg {
    border-top-left-radius: 0px;
    border-top-right-radius: 0px;
}

/* ====== hover indicator ====== */

/*general*/

body {
    --hover-indicator-color: var(--color-accent);
}

.line-hover-indicator .workspace-leaf-content[data-type="markdown"] .markdown-preview-sizer>div:not([class], :has(.collapse-indicator, .mk-header)) {
    position: relative;
}

.line-hover-indicator .workspace-leaf-content[data-type="markdown"] .markdown-source-view .cm-content>div:not(:has(.mk-note-footer)):has(.cm-hmd-list-indent)::after,
.line-hover-indicator .workspace-leaf-content[data-type="markdown"] .markdown-source-view .cm-content>div:not(.image-embed, .mk-header, :has(.mk-note-footer, .collapse-indicator))::after,
.line-hover-indicator .workspace-leaf-content[data-type="markdown"] .markdown-preview-sizer>div:is(.el-ul, .el-ol)::after,
.line-hover-indicator .workspace-leaf-content[data-type="markdown"] .markdown-preview-sizer>div:not([class], :has(.collapse-indicator, .mk-header))::after {
    content: "";
    position: absolute;
    left: -12px;
    top: 3px;
    width: 3px;
    height: 0;
    border-radius: 10px;
    background: var(--hover-indicator-color);
    opacity: 1;
}

.line-hover-indicator .workspace-leaf-content[data-type="markdown"] .markdown-source-view .cm-content>div:not(:has(.mk-note-footer)):has(.cm-hmd-list-indent):hover::after,
.line-hover-indicator .workspace-leaf-content[data-type="markdown"] .markdown-source-view .cm-content>div:not(.image-embed, .mk-header, :has(.mk-note-footer, .collapse-indicator)):hover::after,
.line-hover-indicator .workspace-leaf-content[data-type="markdown"] .markdown-preview-sizer>div:is(.el-ul, .el-ol):hover::after,
.line-hover-indicator .workspace-leaf-content[data-type="markdown"] .markdown-preview-sizer>div:not([class], :has(.collapse-indicator, .mk-header)):hover::after {
    height: calc(100% - 6px);
}

/* num */

.line-hover-indicator .workspace-leaf-content[data-type="markdown"] .cm-contentContainer .cm-line.HyperMD-list-line:not(.HyperMD-list-line-1):after,
.line-hover-indicator .workspace-leaf-content[data-type="markdown"] .cm-contentContainer .cm-line.HyperMD-codeblock:not(.HyperMD-codeblock-begin):not(.HyperMD-codeblock-end)::after {
    background: unset !important;
    opacity: 0 !important;
    position: absolute !important;
    left: -12px !important;
    top: 0px !important;
    height: 100% !important;
    width: 3px !important;
    display: flex;
    align-items: center;
    justify-content: center;
    white-space: nowrap;
    color: var(--hover-indicator-color);
}

.line-hover-indicator .workspace-leaf-content[data-type="markdown"] .cm-contentContainer .cm-line.HyperMD-list-line:not(.HyperMD-list-line-1):hover::after,
.line-hover-indicator .workspace-leaf-content[data-type="markdown"] .cm-contentContainer .cm-line.HyperMD-codeblock:not(.HyperMD-codeblock-begin):not(.HyperMD-codeblock-end):hover:after {
    opacity: 1 !important;
}

/* code */
.line-hover-indicator .workspace-leaf-content[data-type="markdown"] .cm-contentContainer,
.line-hover-indicator .workspace-leaf-content[data-type="markdown"] .cm-contentContainer .cm-line.HyperMD-codeblock-end {
    counter-reset: code-line;
}

.line-hover-indicator .workspace-leaf-content[data-type="markdown"] .cm-contentContainer .cm-line.HyperMD-codeblock:not(.HyperMD-codeblock-begin):not(.HyperMD-codeblock-end)::after {
    counter-increment: code-line;
    content: counter(code-line) !important;
}

/* list */
.line-hover-indicator .workspace-leaf-content[data-type="markdown"] .cm-contentContainer .cm-line.HyperMD-list-line:not(.HyperMD-list-line-1):after {
    content: "";
    text-indent: 0px !important;
}

.line-hover-indicator .workspace-leaf-content[data-type="markdown"] .cm-contentContainer .cm-line.HyperMD-list-line.HyperMD-list-line-2::after {
    content: "2" !important;
}

.line-hover-indicator .workspace-leaf-content[data-type="markdown"] .cm-contentContainer .cm-line.HyperMD-list-line.HyperMD-list-line-3::after {
    content: "3" !important;
}

.line-hover-indicator .workspace-leaf-content[data-type="markdown"] .cm-contentContainer .cm-line.HyperMD-list-line.HyperMD-list-line-4::after {
    content: "4" !important;
}

.line-hover-indicator .workspace-leaf-content[data-type="markdown"] .cm-contentContainer .cm-line.HyperMD-list-line.HyperMD-list-line-5::after {
    content: "5" !important;
}

.line-hover-indicator .workspace-leaf-content[data-type="markdown"] .cm-contentContainer .cm-line.HyperMD-list-line.HyperMD-list-line-6::after {
    content: "6" !important;
}

.line-hover-indicator .workspace-leaf-content[data-type="markdown"] .cm-contentContainer .cm-line.HyperMD-list-line.HyperMD-list-line-7::after {
    content: "7" !important;
}

.line-hover-indicator .workspace-leaf-content[data-type="markdown"] .cm-contentContainer .cm-line.HyperMD-list-line.HyperMD-list-line-8::after {
    content: "8" !important;
}

.line-hover-indicator .workspace-leaf-content[data-type="markdown"] .cm-contentContainer .cm-line.HyperMD-list-line.HyperMD-list-line-9::after {
    content: "9" !important;
}

.line-hover-indicator .workspace-leaf-content[data-type="markdown"] .cm-contentContainer .cm-line.HyperMD-list-line.HyperMD-list-line-10::after {
    content: "10" !important;
}

/* Cursor */

body {
    --cursor: pointer;
    --cursor-link: pointer;
}


/* ====== Bold ====== */


body {
    --bold-weight: var(--font-semibold);
    --bold-color: var(--color-red);
}

.cm-s-obsidian span.cm-quote.cm-strong {
    color: var(--bold-color);
}

/* Italic */
body {
    --italic-color: var(--color-orange);
}

.cm-s-obsidian span.cm-quote.cm-em {
    color: var(--italic-color);
}

/* --- */

font[color] {
    --italic-color: inhert;
    --bold-color: inhert;
}

/* Lists */
body {
    --list-indent: 2em;
    --list-spacing: 0.075em;
    --list-bullet-border: none;
    --list-bullet-radius: 50%;
    --list-bullet-size: 0.3em;
    --list-bullet-size-alt: calc(var(--list-bullet-size) / 2);
    --list-bullet-transform: none;
    --list-numbered-style: decimal;
}

/* List all */
body:not(.ul-marker-restore) .markdown-rendered .has-list-bullet .has-list-bullet .has-list-bullet .has-list-bullet .list-bullet::after,
body:not(.ul-marker-restore) .markdown-rendered .list-bullet::after,
body:not(.ul-marker-restore) .markdown-source-view.mod-cm6 .list-bullet::after {
    --list-bullet-radius: 50%;
    --list-bullet-border: calc(var(--list-bullet-size) / 2) solid var(--list-marker-color);
    width: var(--list-bullet-size-alt);
    height: var(--list-bullet-size-alt);
    background-color: transparent;
}

/* level 1 */
body:not(.ul-marker-restore) .markdown-rendered .has-list-bullet .list-bullet::after,
body:not(.ul-marker-restore) .markdown-source-view.mod-cm6 .HyperMD-list-line.HyperMD-list-line-1.cm-line div.cm-fold-indicator~span.cm-formatting .list-bullet::after,
body:not(.ul-marker-restore) .markdown-source-view.mod-cm6 .HyperMD-list-line.HyperMD-list-line-1.cm-line>span.cm-formatting:first-of-type .list-bullet::after {
    --list-bullet-radius: 50%;
    --list-bullet-border: none;
    width: var(--list-bullet-size);
    height: var(--list-bullet-size);
    background-color: var(--list-marker-color);
}

/* level 2 */
body:not(.ul-marker-restore) .markdown-rendered .has-list-bullet .has-list-bullet .list-bullet::after,
body:not(.ul-marker-restore) .markdown-source-view.mod-cm6 .HyperMD-list-line.HyperMD-list-line-2.cm-line div.cm-fold-indicator~span.cm-formatting .list-bullet::after,
body:not(.ul-marker-restore) .markdown-source-view.mod-cm6 .HyperMD-list-line.HyperMD-list-line-2.cm-line span.cm-hmd-list-indent~span.cm-formatting .list-bullet::after {
    --list-bullet-radius: 0;
    --list-bullet-border: none;
    width: var(--list-bullet-size);
    height: var(--list-bullet-size);
    background-color: var(--list-marker-color);
}

/* level 3 */
body:not(.ul-marker-restore) .markdown-rendered .has-list-bullet .has-list-bullet .has-list-bullet .list-bullet::after,
body:not(.ul-marker-restore) .markdown-source-view.mod-cm6 .HyperMD-list-line.HyperMD-list-line-3.cm-line div.cm-fold-indicator~span.cm-formatting .list-bullet::after,
body:not(.ul-marker-restore) .markdown-source-view.mod-cm6 .HyperMD-list-line.HyperMD-list-line-3.cm-line span.cm-hmd-list-indent~span.cm-formatting .list-bullet::after {
    --list-bullet-radius: 0;
    --list-bullet-transform: rotate(45deg);
    --list-bullet-border: none;
    width: var(--list-bullet-size);
    height: var(--list-bullet-size);
    background-color: var(--list-marker-color);
}

li.is-collapsed .list-bullet:after,
.is-collapsed~.cm-formatting-list .list-bullet:after {
    background-color: var(--list-marker-color-collapsed) !important;
}

ol>li.is-collapsed::marker,
ul>li.is-collapsed::marker,
.cm-s-obsidian .is-collapsed~.cm-formatting-list {
    color: var(--list-marker-color-collapsed) !important;
}

@media (hover: hover) {

    .list-collapse-indicator:hover~.list-bullet:after,
    .cm-fold-indicator:hover~.list-bullet:after,
    .list-collapse-indicator:hover~.cm-formatting-list .list-bullet:after,
    .cm-fold-indicator:hover~.cm-formatting-list .list-bullet:after {
        background-color: var(--list-marker-color-hover) !important;
    }

    li.is-collapsed .list-collapse-indicator:hover~.list-bullet:after,
    li.is-collapsed .cm-fold-indicator:hover~.list-bullet:after,
    .list-collapse-indicator:hover.is-collapsed~.list-bullet:after,
    .cm-fold-indicator:hover.is-collapsed~.list-bullet:after,
    li.is-collapsed .list-collapse-indicator:hover~.cm-formatting-list .list-bullet:after,
    li.is-collapsed .cm-fold-indicator:hover~.cm-formatting-list .list-bullet:after,
    .list-collapse-indicator:hover.is-collapsed~.cm-formatting-list .list-bullet:after,
    .cm-fold-indicator:hover.is-collapsed~.cm-formatting-list .list-bullet:after {
        background-color: var(--list-marker-color-collapsed) !important;
    }
}


/* ======checkboxes ====== */
body {
    /* Checkboxes */
    --checkbox-radius: 6px;
    --checkbox-size: var(--font-text-size);
    --checkbox-marker-color: var(--background-primary);
    --checkbox-color: var(--color-green);
    --checkbox-color-hover: var(--color-green);
    --checkbox-border-color: var(--text-faint);
    --checkbox-border-color-hover: var(--text-faint);
    --checklist-done-decoration: line-through;
    --checklist-done-color: var(--text-faint);
}

@media (hover: hover) {
    input[type=checkbox]:checked:hover {
        filter: brightness(1.075);
    }


    input[type=checkbox]:hover {
        outline-offset: 2px;
        outline: 2px solid var(--background-modifier-border-hover);
    }
}

input[type=checkbox] {
    transition: box-shadow var(--anim-duration-moderate) var(--anim-motion-swing), filter var(--anim-duration-moderate) var(--anim-motion-smooth);
    cursor: pointer;
}

input[type=checkbox]:checked:after {
    -webkit-mask-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' fill='none' viewBox='0 0 16 16'%3e%3cg clip-path='url(%23a)'%3e%3cpath fill='black' d='M15.53 2.41a1.5 1.5 0 0 1 .06 2.12l-8.5 9a1.501 1.501 0 0 1-2.15.03l-4.5-4.5a1.5 1.5 0 0 1 2.12-2.12l3.41 3.408 7.44-7.878a1.5 1.5 0 0 1 2.12-.06Z'/%3e%3c/g%3e%3cdefs%3e%3cclipPath id='a'%3e%3cpath fill='white' d='M0 0h16v16H0z'/%3e%3c/clipPath%3e%3c/defs%3e%3c/svg%3e");
}


/* Alternate Checkboxes */

body:not(.disable-alternative-checkboxes) input[data-task="<"]:checked,
body:not(.disable-alternative-checkboxes) li[data-task="<"]>input:checked,
body:not(.disable-alternative-checkboxes) li[data-task="<"]>p>input:checked,
body:not(.disable-alternative-checkboxes) input[data-task=">"]:checked,
body:not(.disable-alternative-checkboxes) li[data-task=">"]>input:checked,
body:not(.disable-alternative-checkboxes) li[data-task=">"]>p>input:checked,
body:not(.disable-alternative-checkboxes) input[data-task="d"]:checked,
body:not(.disable-alternative-checkboxes) li[data-task="d"]>input:checked,
body:not(.disable-alternative-checkboxes) li[data-task="d"]>p>input:checked,
body:not(.disable-alternative-checkboxes) input[data-task="u"]:checked,
body:not(.disable-alternative-checkboxes) li[data-task="u"]>input:checked,
body:not(.disable-alternative-checkboxes) li[data-task="u"]>p>input:checked,
body:not(.disable-alternative-checkboxes) input[data-task="S"]:checked,
body:not(.disable-alternative-checkboxes) li[data-task="S"]>input:checked,
body:not(.disable-alternative-checkboxes) li[data-task="S"]>p>input:checked,
body:not(.disable-alternative-checkboxes) input[data-task="“"]:checked,
body:not(.disable-alternative-checkboxes) li[data-task="“"]>input:checked,
body:not(.disable-alternative-checkboxes) li[data-task="“"]>p>input:checked,
body:not(.disable-alternative-checkboxes) input[data-task="\""]:checked,
body:not(.disable-alternative-checkboxes) li[data-task="\""]>input:checked,
body:not(.disable-alternative-checkboxes) li[data-task="\""]>p>input:checked,
body:not(.disable-alternative-checkboxes) input[data-task="c"]:checked,
body:not(.disable-alternative-checkboxes) li[data-task="c"]>input:checked,
body:not(.disable-alternative-checkboxes) li[data-task="c"]>p>input:checked,
body:not(.disable-alternative-checkboxes) input[data-task="p"]:checked,
body:not(.disable-alternative-checkboxes) li[data-task="p"]>input:checked,
body:not(.disable-alternative-checkboxes) li[data-task="p"]>p>input:checked,
body:not(.disable-alternative-checkboxes) input[data-task="n"]:checked,
body:not(.disable-alternative-checkboxes) li[data-task="n"]>input:checked,
body:not(.disable-alternative-checkboxes) li[data-task="n"]>p>input:checked,
body:not(.disable-alternative-checkboxes) input[data-task="b"]:checked,
body:not(.disable-alternative-checkboxes) li[data-task="b"]>input:checked,
body:not(.disable-alternative-checkboxes) li[data-task="b"]>p>input:checked,
body:not(.disable-alternative-checkboxes) input[data-task="l"]:checked,
body:not(.disable-alternative-checkboxes) li[data-task="l"]>input:checked,
body:not(.disable-alternative-checkboxes) li[data-task="l"]>p>input:checked,
body:not(.disable-alternative-checkboxes) input[data-task="I"]:checked,
body:not(.disable-alternative-checkboxes) li[data-task="I"]>input:checked,
body:not(.disable-alternative-checkboxes) li[data-task="I"]>p>input:checked,
body:not(.disable-alternative-checkboxes) input[data-task="*"]:checked,
body:not(.disable-alternative-checkboxes) li[data-task="*"]>input:checked,
body:not(.disable-alternative-checkboxes) li[data-task="*"]>p>input:checked {
    --checkbox-marker-color: transparent;
    border: none;
    border-radius: 0;
    background-image: none;
    background-color: currentColor;
    -webkit-mask-size: var(--checkbox-icon);
    -webkit-mask-position: 50% 50%
}

body:not(.disable-alternative-checkboxes) input[data-task="!"]:checked,
body:not(.disable-alternative-checkboxes) li[data-task="!"]>input:checked,
body:not(.disable-alternative-checkboxes) li[data-task="!"]>p>input:checked {
    --checkbox-color: var(--color-orange);
    --checkbox-color-hover: var(--color-orange);
}

body:not(.disable-alternative-checkboxes) input[data-task="!"]:checked:after,
body:not(.disable-alternative-checkboxes) li[data-task="!"]>input:checked:after,
body:not(.disable-alternative-checkboxes) li[data-task="!"]>p>input:checked:after {
    -webkit-mask-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' fill='none' viewBox='0 0 16 16'%3e%3cpath fill='black' fill-rule='evenodd' d='M8 0a2.463 2.463 0 0 0-2.43 2.864v.002L6.686 9.55a1.334 1.334 0 0 0 2.63 0l1.114-6.685v-.002A2.463 2.463 0 0 0 8 0Zm0 12a2 2 0 1 0 0 4 2 2 0 0 0 0-4Z' clip-rule='evenodd'/%3e%3c/svg%3e");
}

body:not(.disable-alternative-checkboxes) input[data-task="?"]:checked,
body:not(.disable-alternative-checkboxes) li[data-task="?"]>input:checked,
body:not(.disable-alternative-checkboxes) li[data-task="?"]>p>input:checked {
    --checkbox-color: var(--color-pink);
    --checkbox-color-hover: var(--color-pink);
}

body:not(.disable-alternative-checkboxes) input[data-task="?"]:checked:after,
body:not(.disable-alternative-checkboxes) li[data-task="?"]>input:checked:after,
body:not(.disable-alternative-checkboxes) li[data-task="?"]>p>input:checked:after {
    -webkit-mask-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' fill='none' viewBox='0 0 16 16'%3e%3cpath fill='black' d='M12.871 3.692c0 3.56-3.956 3.319-3.956 5.78v.014c0 .284-.23.514-.514.514H6.243a.514.514 0 0 1-.515-.514V9.34c0-3.803 3.473-3.56 3.473-5.341 0-.77-.571-1.23-1.517-1.23-.768 0-1.554.335-2.268 1.022a.512.512 0 0 1-.67.031l-1.419-1.11a.513.513 0 0 1-.056-.76C4.457.731 5.997 0 8.036 0c3.23 0 4.835 1.736 4.835 3.692ZM9.355 14c0 1.099-.88 2-2 2-1.1 0-2-.901-2-2s.9-2 2-2c1.12 0 2 .901 2 2Z'/%3e%3c/svg%3e");
}

body:not(.disable-alternative-checkboxes) input[data-task="*"]:checked,
body:not(.disable-alternative-checkboxes) li[data-task="*"]>input:checked,
body:not(.disable-alternative-checkboxes) li[data-task="*"]>p>input:checked {
    --checkbox-color-hover: var(--color-yellow);
    color: var(--color-yellow);
    -webkit-mask-image: url('data:image/svg+xml;utf8,<?xml version="1.0" encoding="UTF-8"?><svg id="svg0" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16"><defs><style>.cls-1{fill:none;}</style></defs><rect class="cls-1" width="16" height="16"/><path d="M8.91,.58c-.08-.17-.21-.32-.37-.42C8.38,.05,8.19,0,8,0s-.38,.05-.54,.16c-.16,.1-.29,.25-.37,.42l-1.93,4.12L.85,5.36c-.18,.03-.35,.1-.49,.22-.14,.12-.25,.27-.3,.45-.06,.17-.07,.36-.03,.54,.04,.18,.13,.34,.26,.48l3.15,3.23-.75,4.57c-.03,.19,0,.38,.06,.55,.07,.17,.19,.32,.35,.43,.15,.11,.33,.17,.52,.18,.19,0,.37-.03,.54-.12l3.84-2.13,3.84,2.13c.16,.09,.35,.13,.54,.12,.19-.01,.37-.07,.52-.18,.15-.11,.27-.26,.35-.43,.07-.17,.09-.36,.06-.55l-.75-4.57,3.15-3.23c.13-.13,.22-.3,.26-.48,.04-.18,.03-.37-.03-.54-.06-.17-.16-.33-.31-.45-.14-.12-.31-.2-.49-.22l-4.31-.66L8.91,.58Z"/></svg>');
}

body:not(.disable-alternative-checkboxes) input[data-task="i"]:checked,
body:not(.disable-alternative-checkboxes) li[data-task="i"]>input:checked,
body:not(.disable-alternative-checkboxes) li[data-task="i"]>p>input:checked {
    --checkbox-color: var(--color-cyan);
    --checkbox-color-hover: var(--color-cyan);
}

body:not(.disable-alternative-checkboxes) input[data-task="i"]:checked:after,
body:not(.disable-alternative-checkboxes) li[data-task="i"]>input:checked:after,
body:not(.disable-alternative-checkboxes) li[data-task="i"]>p>input:checked:after {
    -webkit-mask-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' fill='none' viewBox='0 0 16 16'%3e%3cpath fill='black' d='M10.34 14.025c-.007.002-.56.186-1.04.186-.265 0-.372-.055-.406-.08-.168-.116-.48-.335.054-1.4l1-1.993c.593-1.184.681-2.33.245-3.226-.356-.733-1.039-1.236-1.92-1.416A5 5 0 0 0 7.315 6C5.466 6 4.221 7.08 4.17 7.125a.5.5 0 0 0 .493.848c.005-.002.559-.187 1.04-.187.262 0 .368.055.401.078.17.119.482.34-.05 1.403l-1.001 1.995c-.594 1.185-.681 2.33-.245 3.225.356.733 1.038 1.236 1.921 1.416.314.063.636.097.954.097 1.85 0 3.096-1.08 3.148-1.126a.5.5 0 0 0-.49-.85ZM9.5 5a2.5 2.5 0 1 0 0-5 2.5 2.5 0 0 0 0 5Z'/%3e%3c/svg%3e");
}

body:not(.disable-alternative-checkboxes) input[data-task="/"]:checked,
body:not(.disable-alternative-checkboxes) li[data-task="/"]>input:checked,
body:not(.disable-alternative-checkboxes) li[data-task="/"]>p>input:checked {
    --checkbox-color-hover: var(--color-yellow);
    --checkbox-color: var(--color-yellow);
    border: 2px var(--color-yellow) solid;
    background: conic-gradient(var(--color-yellow) 0% 37.5%, transparent 37.5% 100%);
}

body:not(.disable-alternative-checkboxes) input[data-task="/"]:checked:hover,
body:not(.disable-alternative-checkboxes) li[data-task="/"]>input:checked:hover,
body:not(.disable-alternative-checkboxes) li[data-task="/"]>p>input:checked:hover {
    background: conic-gradient(var(--color-yellow) 0% 37.5%, transparent 37.5% 100%);
}

body:not(.disable-alternative-checkboxes) input[data-task="/"]:checked:after,
body:not(.disable-alternative-checkboxes) li[data-task="/"]>input:checked:after,
body:not(.disable-alternative-checkboxes) li[data-task="/"]>p>input:checked:after {
    background-color: rgba(0, 0, 0, 0);
    -webkit-mask-image: none;
}

body:not(.disable-alternative-checkboxes) input[data-task="I"]:checked,
body:not(.disable-alternative-checkboxes) li[data-task="I"]>input:checked,
body:not(.disable-alternative-checkboxes) li[data-task="I"]>p>input:checked {
    --checkbox-color-hover: var(--color-orange);
    color: var(--color-orange);
    -webkit-mask-image: url('data:image/svg+xml;utf8,<?xml version="1.0" encoding="UTF-8"?><svg id="svg0" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16"><defs><style>.cls-1{fill:none;}</style></defs><rect class="cls-1" width="16" height="16"/><g><rect class="cls-1" width="16" height="16"/><path d="M9,1c0-.27-.11-.52-.29-.71-.19-.19-.44-.29-.71-.29s-.52,.11-.71,.29c-.19,.19-.29,.44-.29,.71v1c0,.27,.11,.52,.29,.71,.19,.19,.44,.29,.71,.29s.52-.11,.71-.29c.19-.19,.29-.44,.29-.71V1Zm4.66,2.76c.18-.19,.28-.44,.28-.7,0-.26-.11-.51-.29-.7s-.44-.29-.7-.29c-.26,0-.51,.1-.7,.28l-.71,.71c-.18,.19-.28,.44-.28,.7,0,.26,.11,.51,.29,.7s.44,.29,.7,.29c.26,0,.51-.1,.7-.28l.71-.71Zm2.34,4.24c0,.27-.11,.52-.29,.71-.19,.19-.44,.29-.71,.29h-1c-.27,0-.52-.11-.71-.29-.19-.19-.29-.44-.29-.71s.11-.52,.29-.71c.19-.19,.44-.29,.71-.29h1c.27,0,.52,.11,.71,.29,.19,.19,.29,.44,.29,.71ZM3.05,4.46c.09,.1,.2,.17,.32,.22,.12,.05,.25,.08,.39,.08,.13,0,.26-.02,.39-.07,.12-.05,.23-.12,.33-.22,.09-.09,.17-.21,.22-.33,.05-.12,.08-.25,.07-.39,0-.13-.03-.26-.08-.39-.05-.12-.13-.23-.22-.32l-.71-.71c-.19-.18-.44-.28-.7-.28-.26,0-.51,.11-.7,.29s-.29,.44-.29,.7c0,.26,.1,.51,.28,.7l.71,.71Zm-.05,3.54c0,.27-.11,.52-.29,.71-.19,.19-.44,.29-.71,.29H1c-.27,0-.52-.11-.71-.29-.19-.19-.29-.44-.29-.71s.11-.52,.29-.71c.19-.19,.44-.29,.71-.29h1c.27,0,.52,.11,.71,.29,.19,.19,.29,.44,.29,.71Zm3,6v-1h4v1c0,.53-.21,1.04-.59,1.41-.38,.38-.88,.59-1.41,.59s-1.04-.21-1.41-.59c-.38-.38-.59-.88-.59-1.41Zm4-2c.02-.34,.21-.65,.48-.86,.65-.51,1.13-1.22,1.36-2.02,.23-.8,.21-1.65-.06-2.43-.27-.79-.78-1.47-1.46-1.95-.68-.48-1.49-.74-2.32-.74s-1.64,.26-2.32,.74c-.68,.48-1.19,1.16-1.46,1.95-.27,.79-.29,1.64-.06,2.43,.23,.8,.71,1.5,1.36,2.02,.27,.21,.46,.52,.48,.86h4Z"/></g></svg>');
}

body:not(.disable-alternative-checkboxes) input[data-task="l"]:checked,
body:not(.disable-alternative-checkboxes) li[data-task="l"]>input:checked,
body:not(.disable-alternative-checkboxes) li[data-task="l"]>p>input:checked {
    --checkbox-color-hover: var(--color-red);
    color: var(--color-red);
    -webkit-mask-image: url('data:image/svg+xml;utf8,<?xml version="1.0" encoding="UTF-8"?><svg id="svg0" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16"><defs><style>.cls-1{fill:none;}</style></defs><rect class="cls-1" width="16" height="16"/><path d="M8,.12c-1.64,0-3.21,.65-4.37,1.81-1.16,1.16-1.81,2.73-1.81,4.37,0,1.34,.44,2.64,1.25,3.71,0,0,.17,.22,.2,.25l4.74,5.6,4.75-5.6s.19-.25,.19-.25h0c.81-1.07,1.25-2.37,1.25-3.71,0-1.64-.65-3.21-1.81-4.37C11.21,.78,9.64,.13,8,.12Zm0,8.44c-.45,0-.88-.13-1.25-.38-.37-.25-.66-.6-.83-1.01-.17-.41-.21-.86-.13-1.3,.09-.44,.3-.84,.62-1.15,.31-.31,.72-.53,1.15-.62,.44-.09,.89-.04,1.3,.13,.41,.17,.76,.46,1.01,.83,.25,.37,.38,.81,.38,1.25,0,.6-.24,1.17-.66,1.59-.42,.42-.99,.66-1.59,.66Z"/></svg>');
}

body:not(.disable-alternative-checkboxes) input[data-task="-"]:checked,
body:not(.disable-alternative-checkboxes) li[data-task="-"]>input:checked,
body:not(.disable-alternative-checkboxes) li[data-task="-"]>p>input:checked {
    --checkbox-color: var(--text-faint);
    --checkbox-color-hover: var(--text-faint);
}

body:not(.disable-alternative-checkboxes) input[data-task="-"]:checked:after,
body:not(.disable-alternative-checkboxes) li[data-task="-"]>input:checked:after,
body:not(.disable-alternative-checkboxes) li[data-task="-"]>p>input:checked:after {
    -webkit-mask-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' fill='none' viewBox='0 0 16 16'%3e%3cpath fill='black' fill-rule='evenodd' d='M0 8a1.5 1.5 0 0 1 1.5-1.5h13a1.5 1.5 0 0 1 0 3h-13A1.5 1.5 0 0 1 0 8Z' clip-rule='evenodd'/%3e%3c/svg%3e");
}

body:not(.disable-alternative-checkboxes):not(.tasks) .markdown-preview-view ul li[data-task="-"].task-list-item.is-checked,
body:not(.disable-alternative-checkboxes):not(.tasks) .markdown-source-view.mod-cm6 .HyperMD-task-line[data-task]:is([data-task="-"]),
body:not(.disable-alternative-checkboxes):not(.tasks) li[data-task="-"].task-list-item.is-checked {
    color: var(--text-faint);
    text-decoration: line-through solid var(--text-faint) 1px
}


body:not(.disable-alternative-checkboxes) input[data-task="b"]:checked,
body:not(.disable-alternative-checkboxes) li[data-task="b"]>input:checked,
body:not(.disable-alternative-checkboxes) li[data-task="b"]>p>input:checked {
    --checkbox-color-hover: var(--color-blue);
    color: var(--color-blue);
    -webkit-mask-image: url('data:image/svg+xml;utf8,<?xml version="1.0" encoding="UTF-8"?><svg id="svg0" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16"><defs><style>.cls-1{fill:none;}.cls-2{fill-rule:evenodd;}</style></defs><rect class="cls-1" width="16" height="16"/><path class="cls-2" d="M4.25,.5c-.6,0-1.17,.24-1.59,.66-.42,.42-.66,.99-.66,1.59V14.1c0,.2,.05,.39,.15,.56,.1,.17,.24,.31,.41,.41,.17,.1,.36,.15,.56,.15,.2,0,.39-.05,.56-.15l3.94-2.25c.11-.06,.24-.1,.37-.1s.26,.03,.37,.1l3.95,2.25c.17,.1,.36,.15,.56,.15,.2,0,.39-.05,.56-.15,.17-.1,.31-.24,.41-.41s.15-.36,.15-.56V2.75c0-.6-.24-1.17-.66-1.59-.42-.42-.99-.66-1.59-.66H4.25Z"/></svg>');
}

body:not(.disable-alternative-checkboxes) input[data-task="n"]:checked,
body:not(.disable-alternative-checkboxes) li[data-task="n"]>input:checked,
body:not(.disable-alternative-checkboxes) li[data-task="n"]>p>input:checked {
    --checkbox-color-hover: var(--color-cyan);
    color: var(--color-cyan);
    -webkit-mask-image: url('data:image/svg+xml;utf8,<?xml version="1.0" encoding="UTF-8"?><svg id="svg0" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16"><defs><style>.cls-1{fill:none;}</style></defs><rect class="cls-1" width="16" height="16"/><path d="M2.75,14.75c-.41,0-.77-.15-1.06-.44-.29-.29-.44-.65-.44-1.06V2.75c0-.41,.15-.77,.44-1.06,.29-.29,.65-.44,1.06-.44H13.25c.41,0,.77,.15,1.06,.44,.29,.29,.44,.65,.44,1.06v7.5l-4.5,4.5H2.75Zm4.5-5.25c.21,0,.39-.07,.53-.22,.14-.14,.22-.32,.22-.53s-.07-.39-.22-.53c-.14-.14-.32-.22-.53-.22h-2.25c-.21,0-.39,.07-.53,.22-.14,.14-.22,.32-.22,.53s.07,.39,.22,.53c.14,.14,.32,.22,.53,.22h2.25Zm3.75-3c.21,0,.39-.07,.53-.22,.14-.14,.22-.32,.22-.53s-.07-.39-.22-.53c-.14-.14-.32-.22-.53-.22H5c-.21,0-.39,.07-.53,.22-.14,.14-.22,.32-.22,.53s.07,.39,.22,.53,.32,.22,.53,.22h6Zm-1.5,6.75l3.75-3.75h-3c-.21,0-.39,.07-.53,.22s-.22,.32-.22,.53v3Z"/></svg>');
}

body:not(.disable-alternative-checkboxes) input[data-task="p"]:checked,
body:not(.disable-alternative-checkboxes) li[data-task="p"]>input:checked,
body:not(.disable-alternative-checkboxes) li[data-task="p"]>p>input:checked {
    --checkbox-color-hover: var(--color-green);
    color: var(--color-green);
    -webkit-mask-image: url('data:image/svg+xml;utf8,<?xml version="1.0" encoding="UTF-8"?><svg id="svg0" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16"><defs><style>.cls-1{fill:none;}</style></defs><path class="cls-1" d="M0,16H16V0H0V16Z"/><g><path d="M3.33,15h-.72c-.33,0-.66-.13-.9-.35-.25-.23-.4-.54-.43-.87L.73,7.11c-.02-.18,0-.37,.07-.54,.06-.18,.16-.34,.28-.47,.13-.14,.28-.25,.45-.32,.17-.07,.35-.11,.54-.11h1.27c.36,0,.69,.14,.94,.39,.25,.25,.39,.59,.39,.94v6.67c0,.35-.14,.7-.39,.94-.25,.25-.59,.39-.94,.39Z"/><path d="M15.4,7.69l-1.79,6.34c-.08,.28-.25,.53-.48,.7-.23,.17-.52,.27-.81,.27H6.67c-.36,0-.69-.14-.94-.39-.25-.25-.39-.59-.39-.94V7.13c0-.53,.32-1.02,.81-1.25,.79-.37,1.38-.82,1.62-1.22,.36-.6,.53-1.78,.57-2.65,0-.06,0-.12,.01-.18,.06-.41,.34-.72,.73-.8,.07-.01,.14-.02,.21-.02,.8,0,1.73,.83,2.12,1.48,.29,.48,.41,1.09,.36,1.84-.03,.55-.18,1.05-.33,1.55l-.04,.12h2.72c.21,0,.41,.05,.6,.14,.18,.09,.34,.23,.47,.39,.12,.17,.21,.35,.24,.55,.04,.2,.03,.41-.03,.61ZM9,1.94h0Z"/></g></svg>');
}

body:not(.disable-alternative-checkboxes) input[data-task="c"]:checked,
body:not(.disable-alternative-checkboxes) li[data-task="c"]>input:checked,
body:not(.disable-alternative-checkboxes) li[data-task="c"]>p>input:checked {
    --checkbox-color-hover: var(--color-red);
    color: var(--color-red);
    -webkit-mask-image: url('data:image/svg+xml;utf8,<?xml version="1.0" encoding="UTF-8"?><svg id="svg0" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16"><defs><style>.cls-1{fill:none;}</style></defs><path class="cls-1" d="M0,0H16V16H0V0Z"/><g><path d="M3.33,1h-.72c-.33,0-.66,.13-.9,.35-.25,.23-.4,.54-.43,.87l-.56,6.67c-.02,.18,0,.37,.07,.54,.06,.18,.16,.34,.28,.47,.13,.14,.28,.25,.45,.32,.17,.07,.35,.11,.54,.11h1.27c.36,0,.69-.14,.94-.39,.25-.25,.39-.59,.39-.94V2.33c0-.35-.14-.7-.39-.94-.25-.25-.59-.39-.94-.39Z"/><path d="M15.4,8.31l-1.79-6.34c-.08-.28-.25-.53-.48-.7-.23-.17-.52-.27-.81-.27H6.67c-.36,0-.69,.14-.94,.39-.25,.25-.39,.59-.39,.94v6.54c0,.53,.32,1.02,.81,1.25,.79,.37,1.38,.82,1.62,1.22,.36,.6,.53,1.78,.57,2.65,0,.06,0,.12,.01,.18,.06,.41,.34,.72,.73,.8,.07,.01,.14,.02,.21,.02,.8,0,1.73-.83,2.12-1.48,.29-.48,.41-1.09,.36-1.84-.03-.55-.18-1.05-.33-1.55l-.04-.12h2.72c.21,0,.41-.05,.6-.14,.18-.09,.34-.23,.47-.39,.12-.17,.21-.35,.24-.55,.04-.2,.03-.41-.03-.61Zm-6.4,5.75h0Z"/></g></svg>');
}

body:not(.disable-alternative-checkboxes) input[data-task="“"]:checked,
body:not(.disable-alternative-checkboxes) li[data-task="“"]>input:checked,
body:not(.disable-alternative-checkboxes) li[data-task="“"]>p>input:checked,
body:not(.disable-alternative-checkboxes) input[data-task="\""]:checked,
body:not(.disable-alternative-checkboxes) li[data-task="\""]>input:checked,
body:not(.disable-alternative-checkboxes) li[data-task="\""]>p>input:checked {
    --checkbox-color-hover: var(--color-purple);
    color: var(--color-purple);
    -webkit-mask-image: url('data:image/svg+xml;utf8,<?xml version="1.0" encoding="UTF-8"?><svg id="svg0" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16"><defs><style>.cls-1{fill:none;}</style></defs><rect class="cls-1" width="16" height="16"/><path d="M2.46,4.19c.94-1.01,2.35-1.53,4.21-1.53h.67v1.88l-.54,.11c-.91,.18-1.55,.54-1.89,1.07-.18,.28-.28,.61-.29,.94h2.05c.18,0,.35,.07,.47,.2,.13,.13,.2,.29,.2,.47v4.67c0,.74-.6,1.33-1.33,1.33H2c-.18,0-.35-.07-.47-.2-.13-.12-.2-.29-.2-.47V7.39c0-.07-.13-1.83,1.13-3.19ZM13.33,13.33h-4c-.18,0-.35-.07-.47-.2-.13-.12-.2-.29-.2-.47V7.39c0-.07-.13-1.83,1.13-3.19,.94-1.01,2.35-1.53,4.21-1.53h.67v1.88l-.54,.11c-.91,.18-1.55,.54-1.89,1.07-.18,.28-.28,.61-.29,.94h2.05c.18,0,.35,.07,.47,.2,.12,.13,.2,.29,.2,.47v4.67c0,.74-.6,1.33-1.33,1.33Z"/></svg>');
}

body:not(.disable-alternative-checkboxes) input[data-task="S"]:checked,
body:not(.disable-alternative-checkboxes) li[data-task="S"]>input:checked,
body:not(.disable-alternative-checkboxes) li[data-task="S"]>p>input:checked {
    --checkbox-color-hover: var(--color-green);
    color: var(--color-green);
    -webkit-mask-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' fill='none' viewBox='0 0 16 16'%3e%3cpath fill='black' d='M12.781 8.34c-.84-.665-2.146-1.247-3.354-1.715V4.396a28.48 28.48 0 0 1 2.123.772c.096.04.209.006.265-.082l1.193-1.805a.219.219 0 0 0-.079-.314c-.68-.365-2.072-.93-3.502-1.293V.366a.27.27 0 0 0-.27-.271H6.985a.271.271 0 0 0-.271.271v.973c-2.014.092-3.38.795-4.166 2.147-.616 1.06-.613 2.255.004 3.118.645.922 1.699 1.332 3.031 1.851l.172.066c.309.118.634.239.959.359v2.715c-1.41-.335-2.736-1.023-2.98-1.189a.218.218 0 0 0-.296.048l-1.444 1.883a.211.211 0 0 0-.043.163.22.22 0 0 0 .086.145c.874.648 2.145 1.266 3.403 1.654a12.4 12.4 0 0 0 1.275.316v1.018c0 .148.121.27.271.27h2.173a.27.27 0 0 0 .27-.27v-.845c1.928-.16 3.368-.997 4.192-2.45.793-1.402.49-2.858-.839-3.998ZM6.712 4.014v1.643c-.624-.2-.993-.394-.953-.872.04-.49.51-.69.954-.771Zm2.716 5.875c.458.205.806.42.927.689.069.152.063.326-.016.533-.14.364-.502.553-.912.649V9.889Z'/%3e%3c/svg%3e");
}

body:not(.disable-alternative-checkboxes) input[data-task="u"]:checked,
body:not(.disable-alternative-checkboxes) li[data-task="u"]>input:checked,
body:not(.disable-alternative-checkboxes) li[data-task="u"]>p>input:checked {
    --checkbox-color-hover: var(--color-green);
    color: var(--color-green);
    -webkit-mask-image: url('data:image/svg+xml;utf8,<?xml version="1.0" encoding="UTF-8"?><svg id="svg0" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16"><defs><style>.cls-1{fill:none;}.cls-2{fill-rule:evenodd;}</style></defs><rect class="cls-1" width="16" height="16"/><path class="cls-2" d="M10,5c-.27,0-.52-.11-.71-.29-.19-.19-.29-.44-.29-.71s.11-.52,.29-.71c.19-.19,.44-.29,.71-.29h5c.27,0,.52,.11,.71,.29,.19,.19,.29,.44,.29,.71v5c0,.27-.11,.52-.29,.71-.19,.19-.44,.29-.71,.29s-.52-.11-.71-.29c-.19-.19-.29-.44-.29-.71v-2.59l-4.29,4.29c-.19,.19-.44,.29-.71,.29s-.52-.11-.71-.29l-2.29-2.29L1.71,12.71c-.19,.18-.44,.28-.7,.28-.26,0-.51-.11-.7-.29S.01,12.26,.01,12c0-.26,.1-.51,.28-.7L5.29,6.29c.19-.19,.44-.29,.71-.29s.52,.11,.71,.29l2.29,2.29,3.59-3.59h-2.59Z"/></svg>');
}

body:not(.disable-alternative-checkboxes) input[data-task="d"]:checked,
body:not(.disable-alternative-checkboxes) li[data-task="d"]>input:checked,
body:not(.disable-alternative-checkboxes) li[data-task="d"]>p>input:checked {
    --checkbox-color-hover: var(--color-red);
    color: var(--color-red);
    -webkit-mask-image: url('data:image/svg+xml;utf8,<?xml version="1.0" encoding="UTF-8"?><svg id="svg0" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16"><defs><style>.cls-1{fill:none;}.cls-2{fill-rule:evenodd;}</style></defs><rect class="cls-1" width="16" height="16"/><path class="cls-2" d="M10,11c-.27,0-.52,.11-.71,.29-.19,.19-.29,.44-.29,.71s.11,.52,.29,.71c.19,.19,.44,.29,.71,.29h5c.27,0,.52-.11,.71-.29,.19-.19,.29-.44,.29-.71V7c0-.27-.11-.52-.29-.71-.19-.19-.44-.29-.71-.29s-.52,.11-.71,.29c-.19,.19-.29,.44-.29,.71v2.59l-4.29-4.29c-.19-.19-.44-.29-.71-.29s-.52,.11-.71,.29l-2.29,2.29L1.71,3.29c-.19-.18-.44-.28-.7-.28-.26,0-.51,.11-.7,.29S.01,3.74,.01,4c0,.26,.1,.51,.28,.7l5,5c.19,.19,.44,.29,.71,.29s.52-.11,.71-.29l2.29-2.29,3.59,3.59h-2.59Z"/></svg>');
}

body:not(.disable-alternative-checkboxes) input[data-task=">"]:checked,
body:not(.disable-alternative-checkboxes) li[data-task=">"]>input:checked,
body:not(.disable-alternative-checkboxes) li[data-task=">"]>p>input:checked {
    --checkbox-color-hover: var(--color-pink);
    color: var(--color-pink);
    -webkit-mask-image: url('data:image/svg+xml;utf8,<?xml version="1.0" encoding="UTF-8"?><svg id="svg0" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16"><defs><style>.cls-1{fill:none;}</style></defs><rect class="cls-1" width="16" height="16"/><path d="M1.72,1.05c-.08-.04-.18-.06-.27-.05-.09,0-.18,.04-.26,.1-.07,.06-.13,.13-.16,.22-.03,.09-.04,.18-.02,.27l1.4,4.85c.03,.09,.08,.17,.15,.23,.07,.06,.16,.1,.25,.12l5.69,.95c.27,.05,.27,.44,0,.49l-5.69,.95c-.09,.02-.18,.06-.25,.12s-.12,.14-.15,.23l-1.4,4.85c-.02,.09-.01,.19,.02,.27,.03,.09,.09,.16,.16,.22,.07,.06,.16,.09,.26,.1,.09,0,.19,0,.27-.05l13-6.5c.08-.04,.15-.11,.2-.18,.05-.08,.07-.17,.07-.26s-.03-.18-.07-.26c-.05-.08-.12-.14-.2-.18L1.72,1.05Z"/></svg>');
}

body:not(.disable-alternative-checkboxes) input[data-task="<"]:checked,
body:not(.disable-alternative-checkboxes) li[data-task="<"]>input:checked,
body:not(.disable-alternative-checkboxes) li[data-task="<"]>p>input:checked {
    --checkbox-color-hover: var(--color-blue);
    color: var(--color-blue);
    -webkit-mask-image: url('data:image/svg+xml;utf8,<?xml version="1.0" encoding="UTF-8"?><svg id="svg0" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16"><defs><style>.cls-1{fill:none;}</style></defs><rect class="cls-1" width="16" height="16"/><path d="M2.75,15.5H13.25c.83,0,1.5-.67,1.5-1.5V3.5c0-.83-.67-1.5-1.5-1.5h-1.5V.5h-1.5v1.5H5.75V.5h-1.5v1.5h-1.5c-.83,0-1.5,.67-1.5,1.5V14c0,.83,.67,1.5,1.5,1.5Zm0-11.25H13.25v1.5H2.75v-1.5Z"/></svg>');
}


/* ====== Callout ====== */
body {
    /* Callouts */
    --callout-border-width: 0px;
    --callout-border-opacity: 0.25;
    --callout-padding: var(--size-4-3) var(--size-4-3) var(--size-4-3) var(--size-4-6);
    --callout-radius: var(--radius-s);
    --callout-title-color: inherit;
    --callout-title-padding: 0;
    --callout-title-size: inherit;
    --callout-content-padding: 0;
    --callout-content-background: transparent;
    --callout-content-radius: 0px;
}

.theme-light .callout:is([data-callout-metadata*="style-1"], [data-callout*=style-1]),
.callout-style-1.theme-light {
    --callout-border-width: 1px;
    --callout-border-opacity: 0.25;
    --callout-padding: 0;
    --callout-radius: var(--radius-s);
    --callout-title-color: inherit;
    --callout-title-padding: 8px 16px;
    --callout-title-size: inherit;
    --callout-content-padding: 0px 16px;
    --callout-content-background: #FFFFFFBF;
    --callout-content-radius: 0px;
}

.theme-dark .callout:is([data-callout-metadata*="style-1"], [data-callout*=style-1]),
.callout-style-1.theme-dark {
    --callout-border-width: 1px;
    --callout-border-opacity: 0.25;
    --callout-padding: 0;
    --callout-radius: var(--radius-s);
    --callout-title-color: inherit;
    --callout-title-padding: 8px 16px;
    --callout-title-size: inherit;
    --callout-content-padding: 0px 16px;
    --callout-content-background: #00000040;
    --callout-content-radius: 0px;
}

.theme-light .callout:is([data-callout-metadata*="style-2"], [data-callout*=style-2]),
.callout-style-2.theme-light {
    --callout-border-width: 1px;
    --callout-border-opacity: 0.25;
    --callout-padding: 0 6px 6px;
    --callout-radius: var(--radius-s);
    --callout-title-color: inherit;
    --callout-title-padding: 8px 16px;
    --callout-title-size: inherit;
    --callout-content-padding: 0px 16px;
    --callout-content-background: #FFFFFFBF;
    --callout-content-radius: 4px;
}

.theme-dark .callout:is([data-callout-metadata*="style-2"], [data-callout*=style-2]),
.callout-style-2.theme-dark {
    --callout-border-width: 1px;
    --callout-border-opacity: 0.25;
    --callout-padding: 0 6px 6px;
    --callout-radius: var(--radius-s);
    --callout-title-color: inherit;
    --callout-title-padding: 8px 16px;
    --callout-title-size: inherit;
    --callout-content-padding: 0px 16px;
    --callout-content-background: #00000040;
    --callout-content-radius: 4px;
}

body:is(.theme-light, .theme-dark) .callout:is([data-callout-metadata*="style-3"], [data-callout*=style-3]),
.callout-style-3:is(.theme-light, .theme-dark) {
    --callout-border-width: 0 0 0 4px;
    --callout-border-opacity: 1;
    --callout-padding: var(--size-4-3) var(--size-4-3) var(--size-4-3) var(--size-4-6);
    --callout-radius: var(--radius-s);
    --callout-title-color: inherit;
    --callout-title-padding: 0;
    --callout-title-size: inherit;
    --callout-content-padding: 0;
    --callout-content-background: transparent;
    --callout-content-radius: 0px;
}

.theme-light .callout:is([data-callout-metadata*="style-4"], [data-callout*=style-4]),
.callout-style-4.theme-light {
    --callout-border-width: 0 0 0 4px;
    --callout-border-opacity: 1;
    --callout-padding: 0;
    --callout-radius: var(--radius-s);
    --callout-title-color: inherit;
    --callout-title-padding: 8px 16px;
    --callout-title-size: inherit;
    --callout-content-padding: 0px 16px;
    --callout-content-background: #FFFFFFBF;
    --callout-content-radius: 0px;
}

.theme-dark .callout:is([data-callout-metadata*="style-4"], [data-callout*=style-4]),
.callout-style-4.theme-dark {
    --callout-border-width: 0 0 0 4px;
    --callout-border-opacity: 1;
    --callout-padding: 0;
    --callout-radius: var(--radius-s);
    --callout-title-color: inherit;
    --callout-title-padding: 8px 16px;
    --callout-title-size: inherit;
    --callout-content-padding: 0px 16px;
    --callout-content-background: #00000040;
    --callout-content-radius: 0px;
}

.callout-content {
    border-radius: var(--callout-content-radius);
}

/* ======Blockquotes ====== */
body {
    --background-alt-light: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='4' height='4' viewBox='0 0 4 4'%3E%3Cpath fill='%23000000' fill-opacity='0.12' d='M1 3h1v1H1V3zm2-2h1v1H3V1z'%3E%3C/path%3E%3C/svg%3E");
    --background-alt-dark: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='4' height='4' viewBox='0 0 4 4'%3E%3Cpath fill='%23ffffff' fill-opacity='0.12' d='M1 3h1v1H1V3zm2-2h1v1H3V1z'%3E%3C/path%3E%3C/svg%3E");
}

body {
    /* Blockquotes */
    --blockquote-border-thickness: 3px;
    --blockquote-border-color: var(--interactive-accent);
    --blockquote-font-style: normal;
    --blockquote-color: inherit;
    --blockquote-background-color: transparent;
    --blockquote-background-light: var(--background-alt-light);
    --blockquote-background-dark: var(--background-alt-dark);
}

.theme-light .markdown-source-view .cm-scroller .cm-line.HyperMD-quote,
.theme-light .markdown-rendered blockquote {
    border-radius: var(--radius-s);
    background: var(--blockquote-background-light);
}

.theme-dark .markdown-source-view .cm-scroller .cm-line.HyperMD-quote,
.theme-dark .markdown-rendered blockquote {
    border-radius: var(--radius-s);
    background: var(--blockquote-background-dark);
}

.markdown-rendered blockquote,
.markdown-preview-view blockquote {
    position: relative;
}

.markdown-rendered blockquote::before,
.markdown-preview-view blockquote::before {
    content: "";
    position: absolute;
    left: 4px;
    top: 4px;
    height: calc(100% - 8px);
    width: var(--blockquote-border-thickness);
    background-color: var(--blockquote-border-color);
    border-radius: 24px;
}

.markdown-rendered blockquote {
    border-left: none;
}

.markdown-source-view.mod-cm6.is-live-preview .HyperMD-quote::before {
    content: "";
    width: var(--blockquote-border-thickness) !important;
    border-left: none !important;
    background-color: var(--blockquote-border-color);
    border-radius: 24px;
    border-top-left-radius: 0px;
    border-top-right-radius: 0px;
    color: transparent;
    position: absolute;
    left: 0px !important;
    bottom: 4px !important;
    height: calc(100% - 4px);
}

.markdown-source-view.mod-cm6.is-live-preview .HyperMD-quote.cm-line {
    border-left: 4px solid transparent;
}

.markdown-source-view.mod-cm6 div:not(.HyperMD-quote)+.HyperMD-quote::before,
.markdown-source-view.mod-cm6 .cm-content :first-child:is(.HyperMD-quote)::before {
    top: 4px !important;
    height: calc(100% - 4px);
    border-radius: 24px;
    border-bottom-left-radius: 0px;
    border-bottom-right-radius: 0px;
}

.markdown-source-view.mod-cm6 div:not(.HyperMD-quote)+.HyperMD-quote:not(:has(+ .HyperMD-quote))::before,
.markdown-source-view.mod-cm6 .cm-content :first-child:is(.HyperMD-quote):not(:has(+ .HyperMD-quote))::before {
    bottom: 4px !important;
    top: 4px !important;
    height: calc(100% - 8px);
    border-radius: 24px;
}

.markdown-source-view.mod-cm6 .HyperMD-quote:has(+ .HyperMD-quote)::before {
    bottom: 0px !important;
    height: 100%;
    border-radius: 0px;
}

/*---*/

.markdown-source-view.mod-cm6 .HyperMD-quote {
    border-top-left-radius: 0 !important;
    border-top-right-radius: 0 !important;
    border-bottom-left-radius: var(--radius-s) !important;
    border-bottom-right-radius: var(--radius-s) !important;
}

.markdown-source-view.mod-cm6 div:not(.HyperMD-quote)+.HyperMD-quote,
.markdown-source-view.mod-cm6 .cm-content :first-child:is(.HyperMD-quote) {
    border-top-left-radius: var(--radius-s) !important;
    border-top-right-radius: var(--radius-s) !important;
}

.markdown-source-view.mod-cm6 .HyperMD-quote:has(+ .HyperMD-quote) {
    border-bottom-left-radius: 0 !important;
    border-bottom-right-radius: 0 !important;
}

/*kbd*/
kbd {
    background-color: var(--background-modifier-hover);
    color: var(--text-muted);
}

/*inline code*/
body {
    --inline-code-normal: var(--color-pink);
    --inline-code-background-light: var(--background-alt-light);
    --inline-code-background-dark: var(--background-alt-dark);
}

.markdown-rendered :not(pre)>code,
.cm-s-obsidian span.cm-inline-code {
    color: var(--inline-code-normal)
}

.theme-light .cm-s-obsidian span.cm-inline-code:not(.cm-formatting):not(.cm-hmd-indented-code):not(.obsidian-search-match-highlight),
.theme-light .cm-s-obsidian span.cm-inline-code,
.theme-light :not(pre)>code {
    background: var(--inline-code-background-light);
}

.theme-dark .cm-s-obsidian span.cm-inline-code:not(.cm-formatting):not(.cm-hmd-indented-code):not(.obsidian-search-match-highlight),
.theme-dark .cm-s-obsidian span.cm-inline-code,
.theme-dark :not(pre)>code {
    background: var(--inline-code-background-dark);
}

.theme-light .cm-s-obsidian span.cm-inline-code span.cm-inline-code.cm-hmd-indented-code,
.theme-dark .cm-s-obsidian span.cm-inline-code span.cm-inline-code.cm-hmd-indented-code {
    background: unset;
}

/*codeblock*/
body {
    /* Code */
    --code-white-space: pre-wrap;
    --code-radius: var(--radius-s);
    --code-size: var(--font-smaller);
    --code-background-light: var(--background-alt-light);
    --code-background-dark: var(--background-alt-dark);
    --code-border-light: 1px dashed var(--background-modifier-border);
    --code-border-dark: 1px dashed var(--background-modifier-border);
    --code-normal: var(--text-muted);
    --code-comment: var(--text-faint);
    --code-function: var(--color-yellow);
    --code-important: var(--color-orange);
    --code-keyword: var(--color-pink);
    --code-operator: var(--color-red);
    --code-property: var(--color-cyan);
    --code-punctuation: var(--text-muted);
    --code-string: var(--color-green);
    --code-tag: var(--color-red);
    --code-value: var(--color-purple);
}

.theme-light {
    --code-background: var(--code-background-light);
    --code-border: var(--code-border-light);
}

.theme-dark {
    --code-background: var(--code-background-dark);
    --code-border: var(--code-border-dark);
}

.cm-s-obsidian div.HyperMD-codeblock-begin-bg,
.cm-s-obsidian div.HyperMD-codeblock-end-bg,
.cm-s-obsidian div.HyperMD-codeblock-bg,
.markdown-rendered pre {
    background: var(--code-background);
}

.cm-s-obsidian div.HyperMD-codeblock-begin-bg {
    border-top: var(--code-border);
}

.cm-s-obsidian div.HyperMD-codeblock-end-bg {
    border-bottom: var(--code-border);
}

.cm-s-obsidian div.HyperMD-codeblock-bg {
    border-left: var(--code-border);
    border-right: var(--code-border);
}

.markdown-rendered pre {
    border: var(--code-border);
}

.cm-s-obsidian .HyperMD-codeblock {
    caret-color: auto;
}

.codeblock-style-dracula {
    --code-background-light: #282A36;
    --code-background-dark: #282A36;
    --code-border-light: none;
    --code-border-dark: none;
    --code-normal: #f8f8f2;
    --code-comment: #6272a4;
    --code-function: #f1fa8c;
    --code-important: #ffb86c;
    --code-keyword: #ff79c6;
    --code-operator: #ff5555;
    --code-property: #8be9fd;
    --code-punctuation: #f8f8f2;
    --code-string: #50fa7b;
    --code-tag: #ff5555;
    --code-value: #bd93f9;
}

.codeblock-style-solarized-light {
    --code-background-light: #fdf6e3;
    --code-background-dark: #fdf6e3;
    --code-border-light: none;
    --code-border-dark: none;
    --code-normal: #657b83;
    --code-comment: #93a1a1;
    --code-function: #b58900;
    --code-important: #cb4b16;
    --code-keyword: #d33682;
    --code-operator: #dc322f;
    --code-property: #2aa198;
    --code-punctuation: #657b83;
    --code-string: #859900;
    --code-tag: #dc322f;
    --code-value: #6c71c4;
}

.codeblock-style-solarized-dark {
    --code-background-light: #002b36;
    --code-background-dark: #002b36;
    --code-border-light: none;
    --code-border-dark: none;
    --code-normal: #839496;
    --code-comment: #586e75;
    --code-function: #b58900;
    --code-important: #cb4b16;
    --code-keyword: #d33682;
    --code-operator: #dc322f;
    --code-property: #2aa198;
    --code-punctuation: #839496;
    --code-string: #859900;
    --code-tag: #dc322f;
    --code-value: #6c71c4;
}

.codeblock-style-one-dark {
    --code-background-light: #282C34;
    --code-background-dark: #282C34;
    --code-border-light: none;
    --code-border-dark: none;
    --code-normal: #ABB2BF;
    --code-comment: #5C6370;
    --code-function: #E5C07B;
    --code-important: #D19A66;
    --code-keyword: #E06C75;
    --code-operator: #BE5046;
    --code-property: #56B6C2;
    --code-punctuation: #ABB2BF;
    --code-string: #98C379;
    --code-tag: #BE5046;
    --code-value: #C678DD;
}

.css-settings-manager:not(.codeblock-style-customize) .cm-s-obsidian .HyperMD-codeblock ::selection,
.css-settings-manager:not(.codeblock-style-customize) .markdown-rendered pre ::selection,
.css-settings-manager:not(.codeblock-style-customize) .markdown-source-view.mod-cm6 .code-block-flair:hover,
.css-settings-manager:not(.codeblock-style-customize) .markdown-rendered button.copy-code-button:hover {
    background-color: hsl(from var(--code-normal) h s l / 20%);
}

.markdown-source-view.mod-cm6 .code-block-flair,
.markdown-rendered button.copy-code-button {
    color: var(--code-normal);
}

/* ======table ====== */
body {
    --table-header-background-light: var(--background-alt-light);
    --table-header-background-dark: var(--background-alt-dark);
    --table-width: 88cqw;
}

.theme-light .markdown-rendered thead tr {
    background: var(--table-header-background-light);
}

.theme-dark .markdown-rendered thead tr {
    background: var(--table-header-background-dark);
}

body:not(.table-width-obsidian-default) .markdown-source-view.mod-cm6 .cm-table-widget .table-wrapper,
body:not(.table-width-obsidian-default) :is(.markdown-rendered, .markdown-source-view.mod-cm6.is-live-preview) table {
    width: 100% !important;
}

.markdown-source-view,
.markdown-reading-view {
    container-type: inline-size;
}

.table-width-customized .markdown-source-view.mod-cm6.is-live-preview .cm-content>.cm-table-widget,
.table-width-customized .markdown-rendered .markdown-preview-sizer>div:has(>table) {
    overflow: auto;
    width: var(--table-width) !important;
    margin-left: calc((var(--table-width) - 100%) / -2) !important;
}

/* ===== embeds ====== */
body {
    /* Embeds */
    --embed-border-radius: 0px;
}

.seamless-embeds {
    --embed-border-left: none;
}

.seamless-embeds .markdown-embed {
    --embed-border-start: none;
}

.markdown-embed {
    border-radius: var(--embed-border-radius);
}

/* ===== image ====== */

/*  image center align */
.img-center-align .print :is(.markdown-preview-view, .markdown-rendered) img:not([class]),
.img-center-align .markdown-preview-view img:not([class]),
.img-center-align .markdown-source-view img:not([class]) {
    display: block;
    margin-left: auto !important;
    margin-right: auto !important;
}

.img-center-align .print :is(.markdown-preview-view, .markdown-rendered) img:is([class=""], .image-ready-resize, .image-ready-click-view),
.img-center-align .markdown-preview-view img:is([class=""], .image-ready-resize, .image-ready-click-view),
.img-center-align .markdown-source-view img:is([class=""], .image-ready-resize, .image-ready-click-view) {
    display: block;
    margin-left: auto !important;
    margin-right: auto !important;
}

/*  image hover effect */
.markdown-source-view.mod-cm6 .cm-content>.internal-embed {
    contain: unset !important;
}

@media (hover: hover) {

    .workspace-leaf-content[data-type="markdown"] .markdown-source-view .cm-content>img:hover,
    .workspace-leaf-content[data-type="markdown"] .view-content .cm-content .image-embed img:hover {
        outline-offset: 2px;
        outline: 2px solid var(--background-modifier-border-hover);
        border-radius: var(--radius-s);
    }
}

.theme-dark.img-darken .markdown-preview-view img,
.theme-dark.img-darken .markdown-source-view img {
    opacity: 0.75;
    transition: opacity var(--anim-duration-fast) linear;
}

.theme-dark.img-darken .markdown-preview-view img:hover,
.theme-dark.img-darken .markdown-source-view img:hover {
    opacity: 1;
    transition: opacity var(--anim-duration-fast) linear;
}

/* image zoom */
/* from https://github.com/kepano/obsidian-minimal, author:@kepano */

.image-embed.is-loaded {
    line-height: 0
}

.image-embed.is-loaded img {
    background-color: transparent
}

.image-embed.is-loaded img:active {
    background-color: transparent
}

body:not(.zoom-off) .view-content div:not(.canvas-node-content) img:not([class]) {
    max-width: 100%;
    cursor: zoom-in
}

body:not(.zoom-off) .view-content img:not([class]):active {
    cursor: zoom-out;
}

body:not(.zoom-off) .view-content .markdown-preview-view img:not([class])[referrerpolicy=no-referrer]:active {
    background-color: var(--background-primary);
    padding: 10px;
}

body:not(.zoom-off) .view-content .image-embed:not(.canvas-node-content):active,
body:not(.zoom-off) .view-content .markdown-preview-view img:not([class])[referrerpolicy=no-referrer]:active {
    aspect-ratio: unset;
    cursor: zoom-out;
    display: block;
    z-index: 200;
    position: fixed;
    max-height: calc(100% + 1px);
    max-width: 100%;
    height: calc(100% + 1px);
    width: 100%;
    object-fit: contain;
    margin: -.5px auto 0 !important;
    text-align: center;
    padding: 0;
    left: 0;
    right: 0;
    bottom: 0;
}

body:not(.zoom-off) .view-content .image-embed:not(.canvas-node-content):active:after {
    background-color: var(--background-primary);
    opacity: .9;
    content: " ";
    height: calc(100% + 1px);
    width: 100%;
    position: fixed;
    left: 0;
    right: 1px;
    z-index: 0;
}

body:not(.zoom-off) .view-content .image-embed:not(.canvas-node-content):active img:not([class]) {
    aspect-ratio: unset;
    top: 50%;
    z-index: 99;
    transform: translateY(-50%);
    padding: 0;
    margin: 0 auto;
    width: calc(100% - 20px);
    max-height: 95vh;
    object-fit: contain;
    left: 0;
    right: 0;
    bottom: 0;
    position: absolute;
    opacity: 1;
    outline: unset !important;
    outline-offset: 0px !important;
    border-radius: 0 !important;
}

/* ====== pdf ====== */
.pdf-toolbar,
.pdf-container,
.pdf-sidebar-container {
    background-color: transparent;
}

/* ====== icon ====== */
body {
    --clickable-icon-radius: 6px;
}

.workspace-tab-header-inner-icon {
    align-items: center;
    justify-content: center;
}

.workspace-tab-header-tab-list,
.workspace-tab-header-new-tab {
    padding-top: 6px;
}

.clickable-icon.document-search-button,
.clickable-icon.document-search-close-button,
body:not(.is-mobile) .workspace-split.mod-left-split .workspace-sidedock-vault-profile .workspace-drawer-vault-actions .clickable-icon,
.mod-left-split .workspace-tab-header .workspace-tab-header-inner,
.mod-right-split .workspace-tab-header .workspace-tab-header-inner,
.clickable-icon.nav-action-button,
.clickable-icon.side-dock-ribbon-action,
.sidebar-toggle-button .clickable-icon,
.workspace-tab-header-new-tab .clickable-icon,
.workspace-tab-header-tab-list .clickable-icon,
.view-header .clickable-icon {
    padding: 7px;
    height: 32px;
    width: 32px;
}

.workspace-leaf.has-pane-relief-label .view-header .view-header-nav-buttons .clickable-icon {
    width: unset;
}

.mod-left-split .workspace-tab-header .workspace-tab-header-inner,
.mod-right-split .workspace-tab-header .workspace-tab-header-inner {
    /*pin icon*/
    width: fit-content;
    gap: var(--size-2-2);
}

.is-mobile .clickable-icon.nav-action-button,
.is-mobile .clickable-icon.side-dock-ribbon-action,
.is-mobile .sidebar-toggle-button .clickable-icon,
.is-mobile .view-header .clickable-icon {
    padding: var(--size-2-2);
}

.titlebar .workspace-tab-header-tab-list,
.titlebar .workspace-tab-header-new-tab,
.mod-root .workspace-tab-header-tab-list,
.mod-root .workspace-tab-header-new-tab {
    display: unset;
}

.side-dock-settings,
.side-dock-actions {
    gap: var(--size-2-2);
}

.workspace-tab-header-status-icon,
.mod-root .workspace-tab-header-status-icon,
.mod-root .workspace-tab-header-inner-icon {
    --icon-size: var(--icon-s);
}


/* ====== icon replace ====== */
svg {
    -webkit-mask-size: contain;
    -webkit-mask-position: center;
    -webkit-mask-repeat: no-repeat;
}

svg.sidebar-right {
    background-color: currentColor;
    -webkit-mask-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' fill='none' viewBox='0 0 24 24'%3e%3cpath fill='black' fill-rule='evenodd' d='M22.875 5.778c0-1.484-1.224-2.653-2.693-2.653H3.818c-1.469 0-2.693 1.17-2.693 2.653v12.444c0 1.484 1.224 2.653 2.693 2.653h16.364c1.469 0 2.693-1.17 2.693-2.653V5.778Zm-10.75 13.347H3.818c-.54 0-.943-.423-.943-.903V5.778c0-.48.404-.903.943-.903h8.307v14.25Zm1.75-14.25v14.25h6.307c.54 0 .943-.423.943-.903V5.778c0-.48-.404-.903-.943-.903h-6.307Zm6 2.125A.875.875 0 0 0 19 6.125h-3a.875.875 0 0 0 0 1.75h3A.875.875 0 0 0 19.875 7Zm0 3A.875.875 0 0 0 19 9.125h-3a.875.875 0 0 0 0 1.75h3a.875.875 0 0 0 .875-.875Zm0 3a.875.875 0 0 0-.875-.875h-3a.875.875 0 0 0 0 1.75h3a.875.875 0 0 0 .875-.875Z' clip-rule='evenodd'/%3e%3c/svg%3e");
}

svg.sidebar-left {
    background-color: currentColor;
    -webkit-mask-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' fill='none' viewBox='0 0 24 24'%3e%3cpath fill='black' fill-rule='evenodd' d='M1.125 5.778c0-1.484 1.224-2.653 2.693-2.653h16.364c1.469 0 2.693 1.17 2.693 2.653v12.444c0 1.484-1.224 2.653-2.693 2.653H3.818c-1.469 0-2.693-1.17-2.693-2.653V5.778Zm10.75 13.347h8.307c.54 0 .943-.423.943-.903V5.778c0-.48-.404-.903-.943-.903h-8.307v14.25Zm-1.75-14.25v14.25H3.818c-.54 0-.943-.423-.943-.903V5.778c0-.48.404-.903.943-.903h6.307ZM4.125 7c0-.483.392-.875.875-.875h3a.875.875 0 1 1 0 1.75H5A.875.875 0 0 1 4.125 7Zm0 3c0-.483.392-.875.875-.875h3a.875.875 0 1 1 0 1.75H5A.875.875 0 0 1 4.125 10Zm0 3c0-.483.392-.875.875-.875h3a.875.875 0 0 1 0 1.75H5A.875.875 0 0 1 4.125 13Z' clip-rule='evenodd'/%3e%3c/svg%3e");
}

/* ====== MOBILE ====== */
.is-phone {
    --file-margins: var(--size-4-2) var(--size-4-4);
}

.is-mobile {
    --background-modifier-form-field: var(--background-modifier-hover);
}

.is-mobile.theme-light {
    --interactive-normal: rgba(var(--mono-rgb-100), 0.05);
    --interactive-hover: rgba(var(--mono-rgb-100), 0.075);
}

.is-mobile.theme-dark {
    --search-result-background: var(--background-tertiary);
    --background-modifier-form-field: var(--background-modifier-hover);
    --background-modifier-hover: rgba(var(--mono-rgb-100), 0.08);
    --interactive-normal: var(--background-modifier-border);
    --interactive-hover: var(--background-modifier-border-hover);
}

.is-mobile .workspace-drawer.mod-right {
    --mobile-sidebar-background: var(--background-primary);
}

.is-phone .menu-scroll {
    background-color: transparent;
}

.is-tablet.theme-dark .vertical-tab-content,
.is-tablet .workspace-tab-header-container,
.is-mobile .workspace-split.mod-root .view-header,
.is-mobile .workspace-split.mod-root .view-content,
.workspace-drawer.mod-right .workspace-drawer-header,
.workspace-drawer.mod-right .view-header,
.workspace-drawer.mod-right .view-content,
.workspace-drawer.mod-right,
.workspace-drawer.mod-right .workspace-drawer-inner,
.workspace-drawer.mod-right .workspace-drawer-inner .workspace-drawer-tab-container,
.workspace-drawer.mod-right .workspace-drawer-inner .workspace-drawer-tab-container .workspace-leaf-content {
    background-color: var(--background-primary);
}

.mobile-navbar {
    box-shadow: 0px 0px 8px hsla(var(--accent-h), 18%, 80%, 0.2), 0px 0px 6px hsla(var(--accent-h), 18%, 80%, 0.1);
}

.theme-dark .mobile-navbar {
    box-shadow: 0px -1px 0px var(--divider-color);
}

.is-tablet .workspace-tab-container {
    border-top: 1px solid var(--divider-color);
}

.is-tablet .workspace-drawer-ribbon {
    border-right: 1px solid var(--divider-color);
}

.is-tablet .workspace-drawer {
    padding-top: 0;
}

.is-mobile.theme-light .mk-flow-bar button {
    background-color: var(--background-modifier-form-field);
}

/* tablet card layout */
body.card-layout-pad-open.is-tablet {
    --titlebar-background: var(--background-underlying);
    --titlebar-background-focused: var(--background-underlying);
}

body.card-layout-pad-open.is-tablet,
body.card-layout-pad-open.is-tablet .app-container,
body.card-layout-pad-open.is-tablet .workspace,
body.card-layout-pad-open.is-tablet .horizontal-main-container {
    background-color: var(--background-underlying) !important;
}

body.card-layout-pad-open.is-tablet .workspace {
    gap: 16px;
    padding: 16px;
    padding-bottom: 24px;
    padding-top: 8px;
}

body.card-layout-pad-open.is-tablet .app-container:has(.mobile-toolbar) .workspace {
    padding-bottom: 8px;
}

body.card-layout-pad-open.is-tablet .workspace-drawer {
    background-color: var(--background-primary);
    border-radius: var(--card-border-radius-light, 8px) !important;
    overflow: hidden !important;
    margin: 16px;
    margin-bottom: 24px;
    margin-top: 32px;
}

body.card-layout-pad-open.is-tablet.theme-dark .workspace-drawer {
    border-radius: var(--card-border-radius-dark, 8px) !important;
}

body.card-layout-pad-open.is-tablet .workspace-split.mod-root {
    border-radius: var(--card-border-radius-light, 8px) !important;
    overflow: hidden !important;
    background-color: var(--background-primary) !important;
    box-shadow: 0px 0px 6px hsla(var(--accent-h), 18%, 80%, 0.4), 0px 0px 2px hsla(var(--accent-h), 18%, 80%, 0.2), 0 0 0 1px var(--background-modifier-border);
}

body.card-layout-pad-open.is-tablet.theme-dark .workspace-split.mod-root {
    box-shadow: 0px 0px 0px 1px var(--background-modifier-border);
    border-radius: var(--card-border-radius-dark, 8px);
}

body.card-layout-pad-open.is-tablet .workspace-drawer.is-pinned {
    border-right: none;
    border-left: none;
    border-radius: var(--card-border-radius-light, 8px) !important;
    margin: 0px;
    box-shadow: 0px 0px 6px hsla(var(--accent-h), 18%, 80%, 0.4), 0px 0px 2px hsla(var(--accent-h), 18%, 80%, 0.2), 0 0 0 1px var(--background-modifier-border);
}

body.card-layout-pad-open.is-tablet.theme-dark .workspace-drawer.is-pinned {
    box-shadow: 0px 0px 0px 1px var(--background-modifier-border);
    border-radius: var(--card-border-radius-dark, 8px) !important;
}

body.card-layout-pad-open.is-tablet .workspace-drawer-inner {
    padding-top: 8px;
}


/* drawer-phone-full-width */

body.is-phone.drawer-phone-full-width .workspace-drawer {
    width: 100vw;
    border-radius: 0px;
}


/* ======== Plugin support ========*/

/*DB folder*/
body:not(.DB-table-full-width-off) .database-plugin__table {
    width: 100% !important;
}

body.DB-table-bg-color-unify .database-plugin__navbar,
body.DB-table-bg-color-unify .database-plugin__table {
    --background-primary: transparent;
    --background-secondary: transparent;
}

body.DB-table-bg-color-adapt .database-plugin__navbar,
body.DB-table-bg-color-adapt .database-plugin__table {
    --background-primary: transparent;
    --background-secondary: rgba(var(--mono-rgb-100), 0.025);
}

body.DB-table-bg-color-unify .database-plugin__th:hover,
body.DB-table-bg-color-unify .data-input:hover,
body.DB-table-bg-color-adapt .database-plugin__th:hover,
body.DB-table-bg-color-adapt .data-input:hover {
    --background-modifier-hover: rgba(var(--mono-rgb-100), 0.025);
    --background-secondary: var(--background-modifier-hover);
}

.database-plugin__pagination-button:hover {
    background-color: var(--interactive-hover);
}

/* Projects */
body.Projects-bg-color-unify .projects-container.svelte-gr1f73 {
    --background-primary: transparent;
    --background-secondary: transparent;
    --tab-background-active: var(--background-primary);
}

body.Projects-bg-color-adapt .projects-container.svelte-gr1f73 {
    --background-primary: transparent;
    --background-secondary: rgba(var(--mono-rgb-100), 0.025);
    --tab-background-active: var(--background-primary);
}

/*style settings*/

body:not(.is-mobile) .workspace-leaf-content[data-type="style-settings"] .view-content {
    padding: 12px !important;
}

.style-settings-container {
    padding-left: var(--size-4-8);
    margin-left: var(--size-4-1);
    border-left: 1px solid var(--divider-color);
}

.style-settings-heading,
.style-settings-heading[data-level="0"] {
    margin-bottom: 12px;
}

.style-settings-heading.is-collapsed[data-level="0"] {
    margin-bottom: 0;
}

.setting-item+div>.setting-item-heading,
.setting-item+.setting-item-heading,
.themed-color-wrapper>div+div {
    margin-top: 0px;
}

.themed-color-wrapper>div {
    box-shadow: 0px 0px 0px 1px var(--background-modifier-border);
}

.themed-color-wrapper {
    display: flex;
    align-items: center;
    flex-direction: row;
    gap: 8px;
}

.style-settings-container {
    padding-bottom: 8px;
}

.setting-item.style-settings-heading:is([data-id=Info], [data-id=Components], [data-id=Appearance-light], [data-id=Appearance-dark], [data-id=Editor], [data-id=Mobile], [data-id=Plugin]) .style-settings-collapse-indicator::after {
    content: "";
    margin-left: var(--size-4-2);
    display: inline-block;
    position: relative;
    top: 4px;
    width: 18px;
    height: 18px;
    background-color: var(--text-muted);
}

.setting-item.style-settings-heading[data-id=Info] .style-settings-collapse-indicator::after {
    -webkit-mask-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' width='18' height='18' fill='none' viewBox='0 0 18 18'%3e%3cg clip-path='url(%23a)'%3e%3cpath fill='%2309244B' d='M9 1.5a7.5 7.5 0 1 1 0 15 7.5 7.5 0 0 1 0-15ZM9 3a6 6 0 1 0 0 12A6 6 0 0 0 9 3Zm-.008 4.5c.419 0 .758.34.758.758V12.1a.75.75 0 0 1-.375 1.4h-.367a.757.757 0 0 1-.758-.758V9a.75.75 0 1 1 0-1.5h.742ZM9 5.25a.75.75 0 1 1 0 ********* 0 0 1 0-1.5Z'/%3e%3c/g%3e%3cdefs%3e%3cclipPath id='a'%3e%3cpath fill='white' d='M0 0h18v18H0z'/%3e%3c/clipPath%3e%3c/defs%3e%3c/svg%3e");
}

.setting-item.style-settings-heading[data-id=Components] .style-settings-collapse-indicator::after {
    -webkit-mask-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' width='18' height='18' fill='none' viewBox='0 0 18 18'%3e%3cg clip-path='url(%23a)'%3e%3cpath stroke='black' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='M4.125 6.375 6.75 9l-2.625 2.625L1.5 9l2.625-2.625ZM9 1.5l2.625 2.625L9 6.75 6.375 4.125 9 1.5Zm4.875 4.875L16.5 9l-2.625 2.625L11.25 9l2.625-2.625ZM9 11.25l2.625 2.625L9 16.5l-2.625-2.625L9 11.25Z'/%3e%3c/g%3e%3cdefs%3e%3cclipPath id='a'%3e%3cpath fill='white' d='M0 0h18v18H0z'/%3e%3c/clipPath%3e%3c/defs%3e%3c/svg%3e");
}

.setting-item.style-settings-heading[data-id=Appearance-light] .style-settings-collapse-indicator::after {
    -webkit-mask-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' width='18' height='18' fill='none' viewBox='0 0 18 18'%3e%3cg clip-path='url(%23a)'%3e%3cpath fill='black' d='M9 3a.726.726 0 0 1-.535-.216.723.723 0 0 1-.215-.534V1.5c0-.213.072-.391.216-.535A.723.723 0 0 1 9 .75a.73.73 0 0 1 .535.216.723.723 0 0 1 .215.534v.75c0 .212-.072.39-.216.534A.723.723 0 0 1 9 3Zm0 14.25a.726.726 0 0 1-.535-.216.723.723 0 0 1-.215-.534v-.75c0-.213.072-.391.216-.535A.723.723 0 0 1 9 15a.73.73 0 0 1 .535.216.723.723 0 0 1 .215.534v.75a.73.73 0 0 1-.216.535.723.723 0 0 1-.534.215Zm6.75-7.5a.726.726 0 0 1-.535-.216A.723.723 0 0 1 15 9c0-.213.072-.391.216-.535a.723.723 0 0 1 .534-.215h.75a.73.73 0 0 1 .535.216.723.723 0 0 1 .215.534c0 .212-.072.39-.216.534a.723.723 0 0 1-.534.216h-.75Zm-14.25 0a.726.726 0 0 1-.535-.216A.723.723 0 0 1 .75 9c0-.213.072-.391.216-.535A.723.723 0 0 1 1.5 8.25h.75a.73.73 0 0 1 .535.216A.723.723 0 0 1 3 9c0 .212-.072.39-.216.534a.723.723 0 0 1-.534.216H1.5Zm12-5.25a.688.688 0 0 1-.206-.507c0-.2.068-.38.206-.543l.262-.282a.68.68 0 0 1 .525-.225.74.74 0 0 1 .544.225.79.79 0 0 1 .235.554.638.638 0 0 1-.235.534l-.281.262a.699.699 0 0 1-.515.207.732.732 0 0 1-.535-.225ZM3.169 14.83a.784.784 0 0 1-.234-.553.645.645 0 0 1 .234-.535l.28-.262a.7.7 0 0 1 .517-.206.73.73 0 0 1 .534.225.7.7 0 0 1 .206.516.762.762 0 0 1-.206.534l-.263.281a.68.68 0 0 1-.525.225.74.74 0 0 1-.543-.225Zm10.575 0-.263-.281a.7.7 0 0 1-.206-.516.73.73 0 0 1 .225-.534.7.7 0 0 1 .516-.207c.206 0 .384.07.534.207l.281.262a.68.68 0 0 1 .225.525.74.74 0 0 1-.225.544.787.787 0 0 1-.553.235.641.641 0 0 1-.534-.235ZM3.45 4.5l-.281-.263a.68.68 0 0 1-.225-.525.74.74 0 0 1 .225-.544.787.787 0 0 1 .553-.234.638.638 0 0 1 .534.234l.263.282a.7.7 0 0 1 .206.516.728.728 0 0 1-.225.534.688.688 0 0 1-.506.206.821.821 0 0 1-.544-.206Zm5.55 9c-1.25 0-2.313-.438-3.188-1.313S4.5 10.25 4.5 9c0-1.25.437-2.313 1.312-3.188S7.75 4.5 9 4.5c1.25 0 2.312.437 3.187 1.312S13.5 7.75 13.5 9c0 1.25-.438 2.312-1.313 3.187S10.25 13.5 9 13.5ZM9 12c.837 0 1.547-.291 2.128-.873C11.71 10.546 12 9.837 12 9c0-.838-.29-1.547-.872-2.129C10.546 6.29 9.837 6 9 6c-.838 0-1.547.29-2.129.872C6.29 7.453 6 8.162 6 9c0 .837.29 1.547.872 2.128C7.454 11.71 8.162 12 9 12Z'/%3e%3c/g%3e%3cdefs%3e%3cclipPath id='a'%3e%3cpath fill='white' d='M0 0h18v18H0z'/%3e%3c/clipPath%3e%3c/defs%3e%3c/svg%3e");
}

.setting-item.style-settings-heading[data-id=Appearance-dark] .style-settings-collapse-indicator::after {
    -webkit-mask-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' width='18' height='18' fill='none' viewBox='0 0 18 18'%3e%3cg clip-path='url(%23a)'%3e%3cpath fill='%2309244B' fill-rule='evenodd' d='M9.358 3.41a.758.758 0 0 1 .822-1.057A6.752 6.752 0 0 1 9 15.75a6.752 6.752 0 0 1-6.647-5.57.758.758 0 0 1 1.056-.822 4.5 4.5 0 0 0 5.949-5.949Zm1.803.803a6 6 0 0 1-6.947 6.947A5.252 5.252 0 0 0 14.25 9a5.252 5.252 0 0 0-3.09-4.787ZM4.174 5.787l.047.104c.205.423.527.779.929 1.023l.074.044a.05.05 0 0 1 0 .089l-.074.043c-.402.245-.724.6-.929 1.024l-.047.104a.053.053 0 0 1-.098 0l-.047-.104A2.445 2.445 0 0 0 3.1 7.09l-.074-.043a.05.05 0 0 1 0-.089l.074-.044c.402-.244.724-.6.929-1.023l.047-.104a.053.053 0 0 1 .098 0Zm2.123-3.644a.085.085 0 0 1 .155 0l.076.166c.326.674.84 1.24 1.478 1.629l.118.07a.081.081 0 0 1 0 .14l-.118.07a3.891 3.891 0 0 0-1.554 1.795.085.085 0 0 1-.155 0l-.076-.166a3.892 3.892 0 0 0-1.477-1.629l-.118-.07a.081.081 0 0 1 0-.14l.118-.07a3.892 3.892 0 0 0 1.477-1.629l.076-.166Z' clip-rule='evenodd'/%3e%3c/g%3e%3cdefs%3e%3cclipPath id='a'%3e%3cpath fill='white' d='M0 0h18v18H0z'/%3e%3c/clipPath%3e%3c/defs%3e%3c/svg%3e");
}

.setting-item.style-settings-heading[data-id=Editor] .style-settings-collapse-indicator::after {
    -webkit-mask-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' width='18' height='18' fill='none' viewBox='0 0 18 18'%3e%3cg clip-path='url(%23a)'%3e%3cpath fill='%2309244B' d='M9.75 2.25a.75.75 0 0 1 .087 1.495l-.087.005h-6v10.5h10.5v-6a.75.75 0 0 1 1.495-.087l.005.087v6a1.5 1.5 0 0 1-1.388 1.496l-.112.004H3.75a1.5 1.5 0 0 1-1.496-1.388l-.004-.112V3.75a1.5 1.5 0 0 1 1.388-1.496l.112-.004h6Zm4.682.257a.75.75 0 0 1 1.123.99l-.062.071-7.425 7.425a.75.75 0 0 1-1.123-.99l.062-.07 7.425-7.425Z'/%3e%3c/g%3e%3cdefs%3e%3cclipPath id='a'%3e%3cpath fill='white' d='M0 0h18v18H0z'/%3e%3c/clipPath%3e%3c/defs%3e%3c/svg%3e");
}

.setting-item.style-settings-heading[data-id=Mobile] .style-settings-collapse-indicator::after {
    -webkit-mask-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' width='18' height='18' fill='none' viewBox='0 0 18 18'%3e%3cg clip-path='url(%23a)'%3e%3cpath fill='%2309244B' d='M12.75 1.5a1.5 1.5 0 0 1 1.496 1.388L14.25 3v12a1.5 1.5 0 0 1-1.388 1.496l-.112.004h-7.5a1.5 1.5 0 0 1-1.496-1.388L3.75 15V3a1.5 1.5 0 0 1 1.388-1.496L5.25 1.5h7.5Zm0 1.5h-7.5v12h7.5V3Zm-3.375 9c.184 0 .337.133.369.308l.006.067v.75a.375.375 0 0 1-.308.369l-.067.006h-.75a.375.375 0 0 1-.369-.308l-.006-.067v-.75c0-.184.133-.337.308-.369L8.625 12h.75Z'/%3e%3c/g%3e%3cdefs%3e%3cclipPath id='a'%3e%3cpath fill='white' d='M0 0h18v18H0z'/%3e%3c/clipPath%3e%3c/defs%3e%3c/svg%3e");
}

.setting-item.style-settings-heading[data-id=Plugin] .style-settings-collapse-indicator::after {
    -webkit-mask-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' width='18' height='18' fill='none' viewBox='0 0 18 18'%3e%3cg clip-path='url(%23a)'%3e%3cpath fill='black' fill-rule='evenodd' d='M7.875 3a1.104 1.104 0 0 0-.988 1.597l.122.244a.8.8 0 0 1-.717 1.158H3.75a.75.75 0 0 0-.75.75v.896c1.365-.082 2.625.998 2.625 2.48 0 1.48-1.26 2.56-2.625 2.479v1.645a.75.75 0 0 0 .75.75h1.645c-.081-1.365.999-2.625 2.48-2.625 1.481 0 2.561 1.26 2.48 2.625h.895a.75.75 0 0 0 .75-.75v-2.542a.801.801 0 0 1 1.159-.717l.243.122a1.105 1.105 0 1 0 0-1.975l-.243.122a.8.8 0 0 1-1.16-.717V6.749a.75.75 0 0 0-.75-.75H9.458a.801.801 0 0 1-.717-1.158l.123-.244a1.104 1.104 0 0 0-.988-1.598ZM5.298 4.5c-.237-1.513.922-3 2.577-3 1.654 0 2.814 1.487 2.577 3h.798a2.25 2.25 0 0 1 2.25 2.25v.797c1.513-.237 3 .923 3 2.577s-1.487 2.814-3 2.577v1.548a2.25 2.25 0 0 1-2.25 2.25H9.445a.794.794 0 0 1-.732-1.11l.063-.147a.981.981 0 1 0-1.803 0l.064.148a.796.796 0 0 1-.732 1.11H3.75a2.25 2.25 0 0 1-2.25-2.25v-2.556a.796.796 0 0 1 1.11-.732l.148.064a.982.982 0 0 0 1.25-1.367.981.981 0 0 0-1.25-.436l-.149.064a.796.796 0 0 1-1.11-.732V6.749A2.25 2.25 0 0 1 3.75 4.5h1.549Z' clip-rule='evenodd'/%3e%3c/g%3e%3cdefs%3e%3cclipPath id='a'%3e%3cpath fill='white' d='M0 0h18v18H0z'/%3e%3c/clipPath%3e%3c/defs%3e%3c/svg%3e");
}


.setting-item.style-settings-heading:is([data-id=Extended-colors-light], [data-id=Global-background-header-light], [data-id=Foreground-header-light], [data-id=interactive-color-light], [data-id=Miscellaneous-light])+.style-settings-container .setting-item .setting-item-control div.theme-dark,
.setting-item.style-settings-heading:is([data-id=Extended-colors-dark], [data-id=Global-background-header-dark], [data-id=Foreground-header-dark], [data-id=interactive-color-dark], [data-id=Miscellaneous-dark])+.style-settings-container .setting-item .setting-item-control div.theme-light {
    display: none;
}

/*calendar*/
.day.svelte-q3wqg9:active .filled.svelte-1widvzq,
.active.svelte-q3wqg9 .filled.svelte-1widvzq,
.active.today.svelte-q3wqg9 .filled.svelte-1widvzq {
    fill: var(--text-on-accent);
}

.today.svelte-q3wqg9 {
    box-shadow: inset 0 0 0 1px var(--color-accent);
}

.reset-button.svelte-1vwr9dd.svelte-1vwr9dd {
    line-height: 16px;
}

.workspace-leaf-content[data-type=calendar] .view-content {
    padding: 8px 0 0;
}

#calendar-container {
    padding: 0 var(--size-4-4);
}

/*surfing*/

body.Surfing-bookmark-bar-hide .workspace-leaf-content[data-type="surfing-view"] .wb-bookmark-bar {
    display: none;
}

body.Surfing-bookmark-bar-hide .workspace-leaf-content[data-type="surfing-view"] .wb-view-content:has(.wb-bookmark-bar) .wb-frame {
    height: calc(100%);
}


/* make.md */
.mk-blink-preview {
    border-color: var(--divider-color);
}

.mk-blink-modal .mk-options-menu__search input {
    border-bottom: 2px solid var(--color-accent);
}

/* checklist */
.compact.svelte-sx5ktw>.content.svelte-sx5ktw {
    --checklist-contentPadding--compact: 4px 2px;
}

li.svelte-sx5ktw.svelte-sx5ktw {
    --checklist-listItemMargin: 0 0 4px;
    --checklist-listItemBackground: transparent;
    --checklist-listItemBackground--hover: var(--background-modifier-hover);
}

svg.settings.button.svelte-9fjno5 {
    background-color: currentColor;
    -webkit-mask-image: url('data:image/svg+xml;utf8,<?xml version="1.0" encoding="UTF-8"?><svg id="a" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><defs><style>.b{fill:none;}.c{fill-rule:evenodd;}</style></defs><rect class="b" width="24" height="24"/><path d="M2,12c0-.86,.11-1.7,.32-2.5,.55,.03,1.1-.1,1.59-.36,.49-.26,.89-.66,1.17-1.14,.28-.48,.42-1.03,.4-1.58-.01-.55-.18-1.09-.48-1.56,1.21-1.19,2.7-2.05,4.34-2.51,.25,.49,.63,.91,1.11,1.2,.47,.29,1.01,.44,1.57,.44s1.1-.15,1.57-.44c.47-.29,.85-.7,1.11-1.2,1.63,.45,3.13,1.32,4.33,2.51-.3,.46-.47,1-.48,1.56-.01,.55,.12,1.1,.4,1.58,.28,.48,.68,.87,1.17,1.14,.49,.26,1.04,.39,1.59,.36,.42,1.64,.42,3.36,0,5.01-.55-.03-1.1,.09-1.59,.36-.49,.26-.89,.66-1.17,1.14-.28,.48-.42,1.03-.4,1.58,.01,.55,.18,1.09,.48,1.56-1.21,1.19-2.7,2.05-4.33,2.51-.25-.49-.63-.91-1.11-1.2-.47-.29-1.01-.44-1.57-.44s-1.1,.15-1.57,.44c-.47,.29-.85,.7-1.11,1.2-1.63-.45-3.13-1.32-4.34-2.51,.3-.46,.47-1,.48-1.56,.01-.55-.12-1.1-.4-1.58-.28-.48-.68-.87-1.17-1.14-.49-.26-1.04-.39-1.59-.36-.21-.82-.32-1.66-.32-2.5Zm4.8,3c.63,1.09,.81,2.35,.56,3.52,.41,.29,.84,.54,1.3,.75,.92-.82,2.1-1.27,3.34-1.27,1.26,0,2.44,.47,3.34,1.27,.45-.21,.89-.46,1.3-.75-.25-1.2-.05-2.46,.56-3.52,.61-1.07,1.6-1.87,2.77-2.25,.05-.5,.05-1,0-1.5-1.17-.38-2.16-1.18-2.77-2.25-.62-1.07-.82-2.32-.56-3.52-.41-.29-.84-.54-1.3-.75-.92,.82-2.1,1.27-3.33,1.27-1.23,0-2.42-.45-3.34-1.27-.45,.21-.89,.46-1.3,.75,.25,1.2,.05,2.46-.56,3.52-.61,1.07-1.6,1.87-2.77,2.25-.05,.5-.05,1,0,1.5,1.17,.38,2.16,1.18,2.77,2.25Z"/><path class="c" d="M12,14c1.1,0,2-.9,2-2s-.9-2-2-2-2,.9-2,2,.9,2,2,2Zm0,2c2.21,0,4-1.79,4-4s-1.79-4-4-4-4,1.79-4,4,1.79,4,4,4Z"/></svg>')
}

.checkbox.svelte-1wagsqu {
    --checklist-checkboxSize: var(--checkbox-size);
    --checklist-checkboxBorder: 2px solid var(--text-muted);
    --checklist-checkboxCheckedSize: 8px;
    --checklist-listItemBorderRadius: var(--radius-s);
}

body.colorful-checkbox li.svelte-sx5ktw.svelte-sx5ktw:nth-child(8n+1) .checkbox.svelte-1wagsqu {
    --checklist-checkboxBorder: 2px solid var(--color-red);
}

body.colorful-checkbox li.svelte-sx5ktw.svelte-sx5ktw:nth-child(8n+2) .checkbox.svelte-1wagsqu {
    --checklist-checkboxBorder: 2px solid var(--color-green);
}

body.colorful-checkbox li.svelte-sx5ktw.svelte-sx5ktw:nth-child(8n+3) .checkbox.svelte-1wagsqu {
    --checklist-checkboxBorder: 2px solid var(--color-orange);
}

body.colorful-checkbox li.svelte-sx5ktw.svelte-sx5ktw:nth-child(8n+4) .checkbox.svelte-1wagsqu {
    --checklist-checkboxBorder: 2px solid var(--color-yellow);
}

body.colorful-checkbox li.svelte-sx5ktw.svelte-sx5ktw:nth-child(8n+5) .checkbox.svelte-1wagsqu {
    --checklist-checkboxBorder: 2px solid var(--color-cyan);
}

body.colorful-checkbox li.svelte-sx5ktw.svelte-sx5ktw:nth-child(8n+6) .checkbox.svelte-1wagsqu {
    --checklist-checkboxBorder: 2px solid var(--color-blue);
}

body.colorful-checkbox li.svelte-sx5ktw.svelte-sx5ktw:nth-child(8n+7) .checkbox.svelte-1wagsqu {
    --checklist-checkboxBorder: 2px solid var(--color-purple);
}

body.colorful-checkbox li.svelte-sx5ktw.svelte-sx5ktw:nth-child(8n+8) .checkbox.svelte-1wagsqu {
    --checklist-checkboxBorder: 2px solid var(--color-pink);
}

.checked.svelte-1wagsqu {
    top: calc(calc(var(--checklist-checkboxSize) - var(--checklist-checkboxCheckedSize) - 2) / 2) !important;
    left: calc(calc(var(--checklist-checkboxSize) - var(--checklist-checkboxCheckedSize) - 2) / 2) !important;
}

.checklist-plugin-main .title {
    font-weight: 400;
    font-size: 14px;
}

.search.svelte-rdace4.svelte-rdace4 {
    box-shadow: 0 0 0 1px var(--background-modifier-border);
}

.count.svelte-1tzpg3c {
    --checklist-countBackground: transparent;
    --checklist-countPadding: 0px
}

.svg.svelte-9fjno5 {
    --checklist-iconSize: 20px
}

.search.svelte-rdace4.svelte-rdace4 {
    --checklist-searchBackground: transparent;
    --checklist-listItemBorderRadius: var(--input-radius);
}

.toggle.svelte-sx5ktw.svelte-sx5ktw:hover {
    opacity: 1 !important;
}

/* Text Generator */
.modelTitle {
    display: none;
}

/* callout manager */
body:not(.is-mobile):not(.mod-macos):not(.mod-windows):not(.mod-linux) .workspace::before,
body:not(.is-mobile):not(.mod-macos):not(.mod-windows):not(.mod-linux) .app-container::before {
    display: none !important;
}