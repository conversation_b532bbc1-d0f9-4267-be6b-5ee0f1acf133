  (int)(long)&((struct stringpool2_t *)0)->stringpool_aix_0,
  (int)(long)&((struct stringpool2_t *)0)->stringpool_aix_1,
  (int)(long)&((struct stringpool2_t *)0)->stringpool_aix_2,
  (int)(long)&((struct stringpool2_t *)0)->stringpool_aix_3,
  (int)(long)&((struct stringpool2_t *)0)->stringpool_aix_4,
  (int)(long)&((struct stringpool2_t *)0)->stringpool_aix_5,
  (int)(long)&((struct stringpool2_t *)0)->stringpool_aix_6,
  (int)(long)&((struct stringpool2_t *)0)->stringpool_aix_10,
  (int)(long)&((struct stringpool2_t *)0)->stringpool_aix_14,
