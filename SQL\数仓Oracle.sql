--- 查询当日融资行权委托
SELECT FUNDID, OR<PERSON>RQ<PERSON>, ORDERSNO, ORDERDATE, ORDERTIM<PERSON>, TAXAMT, OR<PERSON>RPRICE
FROM (SELECT *
      FROM (SELECT FUNDID, OR<PERSON>RQTY, ORDERSNO, OR<PERSON>RDAT<PERSON>, ORDERTIME, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>TUS, TAXAMT, OR<PERSON>RPRICE
            FROM RUN1.FJY_ORDERREC
            UNION ALL
            SELECT FUNDID, ORDERQTY, ORDERSNO, ORDERDATE, ORDERTIME, <PERSON><PERSON><PERSON>FLAG, ORDERSTATUS, TAXAMT, ORDERPRICE
            FROM RUN2.FJY_ORDERREC
            UNION ALL
            SELECT FUNDID, ORDERQTY, ORDERSNO, OR<PERSON>RDAT<PERSON>, OR<PERSON>RT<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, ORDERSTATUS, TAXAMT, ORDERPRICE
            FROM RUN3.FJY_ORDERREC
            UNION ALL
            SELECT FUNDID, ORDERQTY, ORDERSNO, ORDERDATE, ORDERTIME, CANCELFLAG, ORDERSTATUS, <PERSON>A<PERSON>AM<PERSON>, ORDERPRICE
            FROM RUN4.FJY_ORDERREC
            UNION ALL
            SELECT FUNDID, ORDERQTY, ORDERSNO, ORDERDATE, ORDERTIME, CANCELFLAG, ORDERSTATUS, TAXAMT, ORDERPRICE
            FROM RUN5.FJY_ORDERREC
            UNION ALL
            SELECT FUNDID, ORDERQTY, ORDERSNO, ORDERDATE, ORDERTIME, CANCELFLAG, ORDERSTATUS, TAXAMT, ORDERPRICE
            FROM RZRQ.FJY_ORDERREC
            UNION ALL
            SELECT FUNDID, ORDERQTY, ORDERSNO, ORDERDATE, ORDERTIME, CANCELFLAG, ORDERSTATUS, TAXAMT, ORDERPRICE
            FROM RUN7.FJY_ORDERREC
            UNION ALL
            SELECT FUNDID, ORDERQTY, ORDERSNO, ORDERDATE, ORDERTIME, CANCELFLAG, ORDERSTATUS, TAXAMT, ORDERPRICE
            FROM RUN8.FJY_ORDERREC
            UNION ALL
            SELECT FUNDID, ORDERQTY, ORDERSNO, ORDERDATE, ORDERTIME, CANCELFLAG, ORDERSTATUS, TAXAMT, ORDERPRICE
            FROM RUN9.FJY_ORDERREC) T
      WHERE T.CANCELFLAG = 'F'
        AND T.ORDERSTATUS = '1'
        AND T.ORDERSNO = '7'
        AND T.ORDERDATE = #{orderDate}
        AND T.FUNDID = #{fundId}) A
;