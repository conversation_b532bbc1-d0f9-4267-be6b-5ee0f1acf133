--数仓tidb ，getOrderQtyByTidbChannel
SELECT fundid, orderqty, ordersno, orderdate, ordertime, taxamt, orderprice,orderid
FROM (SELECT *
      FROM (SELECT fundid, orderqty, ordersno, orderdate, ordertime, cancelflag, orderstatus, taxamt, orderprice,orderid
            FROM run1_his.h_fjy_orderrec
            UNION ALL
            SELECT fundid, orderqty, ordersno, orderdate, ordertime, cancelflag, orderstatus, taxamt, orderprice,orderid
            FROM run2_his.h_fjy_orderrec
            UNION ALL
            SELECT fundid, orderqty, ordersno, orderdate, ordertime, cancelflag, orderstatus, taxamt, orderprice,orderid
            FROM run3_his.h_fjy_orderrec
            UNION ALL
            SELECT fundid, orderqty, ordersno, orderdate, ordertime, cancelflag, orderstatus, taxamt, orderprice,orderid
            FROM run4_his.h_fjy_orderrec
            UNION ALL
            SELECT fundid, orderqty, ordersno, orderdate, ordertime, cancelflag, orderstatus, taxamt, orderprice,orderid
            FROM run5_his.h_fjy_orderrec
            UNION ALL
            SELECT fundid, orderqty, ordersno, orderdate, ordertime, cancelflag, orderstatus, taxamt, orderprice,orderid
            FROM rzrq_his.h_fjy_orderrec
            UNION ALL
            SELECT fundid, orderqty, ordersno, orderdate, ordertime, cancelflag, orderstatus, taxamt, orderprice,orderid
            FROM run7_his.h_fjy_orderrec
            UNION ALL
            SELECT fundid, orderqty, ordersno, orderdate, ordertime, cancelflag, orderstatus, taxamt, orderprice,orderid
            FROM run8_his.h_fjy_orderrec
            UNION ALL
            SELECT fundid, orderqty, ordersno, orderdate, ordertime, cancelflag, orderstatus, taxamt, orderprice,orderid
            FROM run9_his.h_fjy_orderrec) t
      WHERE t.cancelflag = 'F'
        AND t.orderstatus = '1'
        AND t.ordersno =  #{orderSno}
        AND t.orderdate = #{orderDate}
        AND t.fundid = #{fundId}
     ) a
;

-- 数仓Oracle,getOrderQtyByOracleChannel
SELECT FUNDID, ORDERQTY, ORDERSNO, ORDERDATE, ORDERTIME, TAXAMT, ORDERPRICE,ORDERID
FROM (SELECT *
      FROM (SELECT FUNDID, ORDERQTY, ORDERSNO, ORDERDATE, ORDERTIME, CANCELFLAG, ORDERSTATUS, TAXAMT, ORDERPRICE,ORDERID
            FROM RUN1.FJY_ORDERREC
            UNION ALL
            SELECT FUNDID, ORDERQTY, ORDERSNO, ORDERDATE, ORDERTIME, CANCELFLAG, ORDERSTATUS, TAXAMT, ORDERPRICE,ORDERID
            FROM RUN2.FJY_ORDERREC
            UNION ALL
            SELECT FUNDID, ORDERQTY, ORDERSNO, ORDERDATE, ORDERTIME, CANCELFLAG, ORDERSTATUS, TAXAMT, ORDERPRICE,ORDERID
            FROM RUN3.FJY_ORDERREC
            UNION ALL
            SELECT FUNDID, ORDERQTY, ORDERSNO, ORDERDATE, ORDERTIME, CANCELFLAG, ORDERSTATUS, TAXAMT, ORDERPRICE,ORDERID
            FROM RUN4.FJY_ORDERREC
            UNION ALL
            SELECT FUNDID, ORDERQTY, ORDERSNO, ORDERDATE, ORDERTIME, CANCELFLAG, ORDERSTATUS, TAXAMT, ORDERPRICE,ORDERID
            FROM RUN5.FJY_ORDERREC
            UNION ALL
            SELECT FUNDID, ORDERQTY, ORDERSNO, ORDERDATE, ORDERTIME, CANCELFLAG, ORDERSTATUS, TAXAMT, ORDERPRICE,ORDERID
            FROM RZRQ.FJY_ORDERREC
            UNION ALL
            SELECT FUNDID, ORDERQTY, ORDERSNO, ORDERDATE, ORDERTIME, CANCELFLAG, ORDERSTATUS, TAXAMT, ORDERPRICE,ORDERID
            FROM RUN7.FJY_ORDERREC
            UNION ALL
            SELECT FUNDID, ORDERQTY, ORDERSNO, ORDERDATE, ORDERTIME, CANCELFLAG, ORDERSTATUS, TAXAMT, ORDERPRICE,ORDERID
            FROM RUN8.FJY_ORDERREC
            UNION ALL
            SELECT FUNDID, ORDERQTY, ORDERSNO, ORDERDATE, ORDERTIME, CANCELFLAG, ORDERSTATUS, TAXAMT, ORDERPRICE,ORDERID
            FROM RUN9.FJY_ORDERREC) T
      WHERE T.CANCELFLAG = 'F'
        AND T.ORDERSTATUS = '1'
        AND T.ORDERSNO = #{orderSno}
        AND T.ORDERDATE = #{orderDate}
        AND T.FUNDID = #{fundId}) A
;

----集中交易 getTodayExerciseData
SELECT SUM(orderqty) as orderqty, stkcode
FROM run..fjy_orderrec
WHERE custid = #{custid}
  AND cancelflag = 'F'
  AND orderstatus = '1'
  AND bsflag = '10'
  AND bsflag_sub IN ('A0','A1')
GROUP BY stkcode