{"remoteType": "", "useCustomRequestHandler": false, "couchDB_URI": "", "couchDB_USER": "", "couchDB_PASSWORD": "", "couchDB_DBNAME": "", "liveSync": false, "syncOnSave": false, "syncOnStart": false, "savingDelay": 200, "lessInformationInLog": false, "gcDelay": 0, "versionUpFlash": "", "minimumChunkSize": 20, "longLineThreshold": 250, "showVerboseLog": true, "suspendFileWatching": false, "trashInsteadDelete": true, "periodicReplication": false, "periodicReplicationInterval": 60, "syncOnFileOpen": false, "encrypt": true, "passphrase": "", "usePathObfuscation": true, "doNotDeleteFolder": true, "resolveConflictsByNewerFile": false, "batchSave": false, "batchSaveMinimumDelay": 5, "batchSaveMaximumDelay": 60, "deviceAndVaultName": "", "usePluginSettings": false, "showOwnPlugins": false, "showStatusOnEditor": true, "showStatusOnStatusbar": true, "showOnlyIconsOnEditor": false, "usePluginSync": false, "autoSweepPlugins": false, "autoSweepPluginsPeriodic": false, "notifyPluginOrSettingUpdated": true, "checkIntegrityOnSave": false, "batch_size": 25, "batches_limit": 25, "useHistory": true, "disableRequestURI": true, "skipOlderFilesOnSync": true, "checkConflictOnlyOnOpen": false, "showMergeDialogOnlyOnActive": false, "syncInternalFiles": false, "syncInternalFilesBeforeReplication": false, "syncInternalFilesIgnorePatterns": "\\/node_modules\\/, \\/\\.git\\/, \\/obsidian-livesync\\/, \\/.code-links\\/", "syncInternalFilesInterval": 60, "additionalSuffixOfDatabaseName": "17c613afa76fc7b6", "ignoreVersionCheck": false, "lastReadUpdates": 24, "deleteMetadataOfDeletedFiles": false, "syncIgnoreRegEx": "", "syncOnlyRegEx": "", "customChunkSize": 50, "readChunksOnline": true, "watchInternalFileChanges": true, "automaticallyDeleteMetadataOfDeletedFiles": 0, "disableMarkdownAutoMerge": false, "writeDocumentsIfConflicted": false, "useDynamicIterationCount": true, "syncAfterMerge": false, "configPassphraseStore": "", "encryptedPassphrase": "%7ebaa3b9a3a335c7f524fdf6040000009f0876ffeec99af4ce61493da3e7adb7+wpU4SOUGJkaWGABfOprEBKRk+ll+A==", "encryptedCouchDBConnection": "%7ebaa3b9a3a335c7f524fdf6030000009f0876ffeec99af4ce61493da3e7adb7/NPb0dsJicOZhlal3WS4ck8ROh2Oy3JhEUYEPCjS9kMcx+3sjgdZ277d1A6OM6XVe6rpS4A3VCTQntB10/XFpY5cB5loaqAPSIIDd1UdIh0CGXd5YZ7j778n/vtw+ZPw+9npYSkITlS6/7uN3pm9zNsz0A+0wZ78JnzppWm5TOV5Ky2GuFRM6MeMlka9Mp9hhBAx205WpRI0jYswo9whV/xuYAoDVnGwMriSzbk7G+jEw9BHkj7eB+kVZpsgSfsDhZzypzeZ5Jp1O5U/u98cSsg+GyyMYvWa2ceqFAn+MdDRBuuuih5PWIFcKPbDs9yrb4L/G/wWxMvSCb+75hb947qUECgBUNHxbhS9saEqZflFGAWnBnLlmU9iPoIKVy5+Ea5asHPbDggZErs1Re+QMx2FJH97syCz2Q4+a9ZUdDeXN+58vAFdX7d/fdh903ngozdNCZuZznvCaDuplqeHx1MQUl5XOdz2W0v8Cs9a080Rc/wv8r+YW+oea2KhfwwWDSbaLn+EKNPapw0tGW4/UcSvbyGe7yUrU4fGYPsEOKIMCtiuP6MfnIO9vjd/WpmJoy5PXaYbC4zgUHJXzB83niDYHcqu4rOXKAbXbtvNleL9XIxX/4aZxtda/6gBkOxnw33xJ1efolabPJ7lt9KfdnhM8HZ3Iq3OOiswmBpJFE8g4pju72N8GllULhDfMVoYaRALHBkrKedu6Ln/XHzQ1qoTWgf0YlV4zw==", "permitEmptyPassphrase": false, "useIndexedDBAdapter": true, "useTimeouts": false, "writeLogToTheFile": false, "doNotPaceReplication": false, "hashCacheMaxCount": 300, "hashCacheMaxAmount": 50, "concurrencyOfReadChunksOnline": 30, "minimumIntervalOfReadChunksOnline": 25, "hashAlg": "xxhash64", "suspendParseReplicationResult": false, "doNotSuspendOnFetching": false, "useIgnoreFiles": false, "ignoreFiles": ".giti<PERSON>re", "syncOnEditorSave": false, "pluginSyncExtendedSetting": {}, "syncMaxSizeInMB": 50, "settingSyncFile": "", "writeCredentialsForSettingSync": false, "notifyAllSettingSyncFile": false, "isConfigured": true, "settingVersion": 10, "enableCompression": false, "accessKey": "", "bucket": "", "endpoint": "", "region": "", "secretKey": "", "useEden": false, "maxChunksInEden": 10, "maxTotalLengthInEden": 1024, "maxAgeInEden": 10, "disableCheckingConfigMismatch": false, "displayLanguage": "zh", "enableChunkSplitterV2": false, "disableWorkerForGeneratingChunks": false, "processSmallFilesInUIThread": false, "notifyThresholdOfRemoteStorageSize": 2000, "usePluginSyncV2": true, "usePluginEtc": false, "handleFilenameCaseSensitive": false, "doNotUseFixedRevisionForChunks": true, "showLongerLogInsideEditor": false, "sendChunksBulk": false, "sendChunksBulkMaxSize": 1, "useSegmenter": true, "useAdvancedMode": true, "usePowerUserMode": true, "useEdgeCaseMode": true, "enableDebugTools": false, "suppressNotifyHiddenFilesChange": false, "syncMinimumInterval": 2000, "P2P_Enabled": false, "P2P_AutoAccepting": 0, "P2P_AppID": "self-hosted-livesync", "P2P_roomID": "", "P2P_passphrase": "", "P2P_relays": "wss://exp-relay.vrtmrz.net/", "P2P_AutoBroadcast": false, "P2P_AutoStart": false, "P2P_AutoSyncPeers": "", "P2P_AutoWatchPeers": "", "P2P_SyncOnReplication": "", "P2P_RebuildFrom": "", "P2P_AutoAcceptingPeers": "", "P2P_AutoDenyingPeers": "", "P2P_IsHeadless": false, "doctorProcessedVersion": "0.24.16", "bucketCustomHeaders": "", "couchDB_CustomHeaders": "", "useJWT": false, "jwtAlgorithm": "", "jwtKey": "", "jwtKid": "", "jwtSub": "", "jwtExpDuration": 5}