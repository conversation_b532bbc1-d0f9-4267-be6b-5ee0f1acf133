---
created: 2024-10-29 09:25
updated: 2024-10-30 14:03
---

# 1. 流程图

![[../../../attachments/融资行权委托/期权持仓查询 2.png|../../../../../attachs/期权持仓查询 2.png]]![[../../../attachments/融资行权委托/行权信息查询.png]]

![[../../../../../attachs/融资行权委托 1.png]]

```
title 期权持仓查询

participant APP
participant 股权激励中台服务
participant 股权激励数据库
participant 股权激励前置机通讯服务
participant 柜台

APP->股权激励中台服务: 查询期权持仓信息
activate APP

activate 股权激励中台服务
股权激励中台服务->股权激励数据库: 查询用户股票期权激励计划
activate 股权激励数据库

股权激励中台服务<-股权激励数据库: return
deactivate 股权激励数据库

股权激励中台服务->股权激励前置机通讯服务: 查询持仓明细
activate 股权激励前置机通讯服务

股权激励前置机通讯服务->柜台: 查询303[410503]-持股明细
activate 柜台

股权激励前置机通讯服务<-柜台: return
deactivate 柜台

股权激励中台服务<-股权激励前置机通讯服务: return
deactivate 股权激励前置机通讯服务

股权激励中台服务->股权激励中台服务: 筛选证券类型为Q权证的持仓

loop 持仓明细信息
    股权激励中台服务->股权激励前置机通讯服务: 查询权证信息
    activate 股权激励前置机通讯服务
    
    股权激励前置机通讯服务->柜台: 查询275[410231]-取权证信息
    activate 柜台
    
    股权激励前置机通讯服务<-柜台: return
    deactivate 柜台
    
    股权激励中台服务<-股权激励前置机通讯服务: return
    deactivate 股权激励前置机通讯服务

    股权激励中台服务->股权激励中台服务: 将权证信息设置到响应结果
    股权激励中台服务->股权激励中台服务: 根据行权代码匹配对应的计划批次信息，并将计划信息设置到响应结果
end

APP<-股权激励中台服务: return
deactivate 股权激励中台服务
deactivate APP


```

```
title 行权信息查询

participant APP
participant 股权激励中台服务
participant 股权激励前置机通讯服务
participant 柜台

APP->股权激励中台服务: 查询行权信息
activate APP

activate 股权激励中台服务
股权激励中台服务->股权激励前置机通讯服务: 查询可用额度
activate 股权激励前置机通讯服务

股权激励前置机通讯服务->柜台: 调用420539融资行权客户权证额度信息查询
activate 柜台

股权激励前置机通讯服务<-柜台: return
deactivate 柜台

股权激励中台服务<-股权激励前置机通讯服务: return
deactivate 股权激励前置机通讯服务

股权激励中台服务->股权激励前置机通讯服务: 查询行权名称和行权价格信息
activate 股权激励前置机通讯服务

股权激励前置机通讯服务->柜台: 查询275[410231]-取权证信息
activate 柜台

股权激励前置机通讯服务<-柜台: return
deactivate 柜台

股权激励中台服务<-股权激励前置机通讯服务: return
deactivate 股权激励前置机通讯服务

股权激励中台服务->股权激励前置机通讯服务: 查询最大可行权量
activate 股权激励前置机通讯服务

股权激励前置机通讯服务->柜台: 调用283[410986]-取自主行权可委托数量
activate 柜台

股权激励前置机通讯服务<-柜台: return
deactivate 柜台

股权激励中台服务<-股权激励前置机通讯服务: return
deactivate 股权激励前置机通讯服务

股权激励中台服务->股权激励前置机通讯服务: 查股权激励人员信息，判断是否处理所得税
activate 股权激励前置机通讯服务

股权激励前置机通讯服务->柜台: 调用281[410990]-股权激励人员信息查询
activate 柜台

股权激励前置机通讯服务<-柜台: return
deactivate 柜台

股权激励中台服务<-股权激励前置机通讯服务: return
deactivate 股权激励前置机通讯服务

股权激励中台服务->股权激励中台服务: 封装响应结果，设置行权信息

APP<-股权激励中台服务: return
deactivate 股权激励中台服务
deactivate APP

```

``

```
title 融资行权委托

participant APP
participant 股权激励中台服务
participant 股权激励数据库
participant 股权激励前置机通讯服务
participant 柜台

group 期权持仓查询
    activate APP
    APP->股权激励中台服务: 查询期权持仓信息
    activate 股权激励中台服务
    
    股权激励中台服务->股权激励数据库: 查询用户股票期权激励计划
    activate 股权激励数据库

    股权激励中台服务<-股权激励数据库: return
    deactivate 股权激励数据库

    股权激励中台服务->股权激励前置机通讯服务: 查询持仓明细
    activate 股权激励前置机通讯服务

    股权激励前置机通讯服务->柜台: 查询303[410503]-持股明细
    activate 柜台

    股权激励前置机通讯服务<-柜台: return
    deactivate 柜台

    股权激励中台服务<-股权激励前置机通讯服务: return
    deactivate 股权激励前置机通讯服务

    股权激励中台服务->股权激励中台服务: 筛选证券类型为Q权证的持仓

    loop 持仓明细信息
        股权激励中台服务->股权激励前置机通讯服务: 查询权证信息
        activate 股权激励前置机通讯服务
        
        股权激励前置机通讯服务->柜台: 查询275[410231]-取权证信息
        activate 柜台
        
        股权激励前置机通讯服务<-柜台: return
        deactivate 柜台
        
        股权激励中台服务<-股权激励前置机通讯服务: return
        deactivate 股权激励前置机通讯服务

        股权激励中台服务->股权激励中台服务: 将权证信息设置到响应结果
        股权激励中台服务->股权激励中台服务: 根据行权代码匹配对应的计划批次信息，并将计划信息设置到响应结果
    end

    APP<-股权激励中台服务: return
    deactivate 股权激励中台服务
    deactivate APP
end

APP->APP: 输入行权代码
APP->APP: 校验行权代码是否在期权持仓接口信息中

alt 行权代码不在期权持仓信息中
    APP->APP: 提示行权代码输入错误

else 行权代码在期权持仓信息中
    group 查询行权信息
    activate APP
    APP->股权激励中台服务: 查询行权信息
    activate 股权激励中台服务

    股权激励中台服务->股权激励前置机通讯服务: 查询可用额度
    activate 股权激励前置机通讯服务

    股权激励前置机通讯服务->柜台: 调用420539融资行权客户权证额度信息查询
    activate 柜台

    股权激励前置机通讯服务<-柜台: return
    deactivate 柜台

    股权激励中台服务<-股权激励前置机通讯服务: return
    deactivate 股权激励前置机通讯服务

    股权激励中台服务->股权激励前置机通讯服务: 查询行权名称和行权价格信息
    activate 股权激励前置机通讯服务

    股权激励前置机通讯服务->柜台: 查询275[410231]-取权证信息
    activate 柜台

    股权激励前置机通讯服务<-柜台: return
    deactivate 柜台

    股权激励中台服务<-股权激励前置机通讯服务: return
    deactivate 股权激励前置机通讯服务

    股权激励中台服务->股权激励前置机通讯服务: 查询最大可行权量
    activate 股权激励前置机通讯服务

    股权激励前置机通讯服务->柜台: 调用283[410986]-取自主行权可委托数量
    activate 柜台

    股权激励前置机通讯服务<-柜台: return
    deactivate 柜台

    股权激励中台服务<-股权激励前置机通讯服务: return
    deactivate 股权激励前置机通讯服务

    股权激励中台服务->股权激励前置机通讯服务: 查股权激励人员信息，判断是否处理所得税
    activate 股权激励前置机通讯服务

    股权激励前置机通讯服务->柜台: 调用281[410990]-股权激励人员信息查询
    activate 柜台

    股权激励前置机通讯服务<-柜台: return
    deactivate 柜台

    股权激励中台服务<-股权激励前置机通讯服务: return
    deactivate 股权激励前置机通讯服务

    股权激励中台服务->股权激励中台服务: 封装响应结果，设置行权信息

    APP<-股权激励中台服务: return
    deactivate 股权激励中台服务
    deactivate APP
    end
    APP->APP: 填写行权数量
    group 融资行权委托
    APP->股权激励中台服务: 融资行权委托
    activate APP
    activate 股权激励中台服务

    股权激励中台服务->股权激励前置机通讯服务: 融资行权委托
    activate 股权激励前置机通讯服务

    股权激励前置机通讯服务->柜台: 调用282[410985]-自主行权业务
    activate 柜台

    股权激励前置机通讯服务<-柜台: return
    deactivate 柜台

    股权激励中台服务<-股权激励前置机通讯服务: return
    deactivate 股权激励前置机通讯服务

    股权激励中台服务->股权激励数据库: 插入行权记录
    activate 股权激励数据库

    股权激励中台服务<-股权激励数据库: return
    deactivate 股权激励数据库

    APP<-股权激励中台服务: return
    deactivate 股权激励中台服务
    deactivate APP
end
end



```

# 2. 涉及的数据库、Redis、Kafka 等中间件的修改/新增图或者说明

股权激励LCIMS新增表-行权记录表

```sql
create table LCIMS.INCENT_EXERCISE_LOG  
(  
    EID        NUMBER(18) not null  
        constraint PK_INCENT_EXERCISE_LOG  
            primary key,  
    EITIME     DATE default sysdate,  
    EUTIME     DATE default sysdate,  
    PLANID     NUMBER,  
    VERSIONID  NUMBER,  
    COMPANYID  VARCHAR2(100),  
    FUNDID     NUMBER,  
    ORDERSNO   NUMBER,  
    OPTIONCODE VARCHAR2(255)  
)  
/  
  
comment on table LCIMS.INCENT_EXERCISE_LOG is '股权激励行权外围流水表'  
/  
  
comment on column LCIMS.INCENT_EXERCISE_LOG.EID is '系统物理主键'  
/  
  
comment on column LCIMS.INCENT_EXERCISE_LOG.EITIME is '数据入库时间'  
/  
  
comment on column LCIMS.INCENT_EXERCISE_LOG.EUTIME is '最近一次修改时间'  
/  
  
comment on column LCIMS.INCENT_EXERCISE_LOG.PLANID is '计划ID'  
/  
  
comment on column LCIMS.INCENT_EXERCISE_LOG.VERSIONID is '计划批次'  
/  
  
comment on column LCIMS.INCENT_EXERCISE_LOG.COMPANYID is '公司id'  
/  
  
comment on column LCIMS.INCENT_EXERCISE_LOG.FUNDID is '客户资金账号'  
/  
  
comment on column LCIMS.INCENT_EXERCISE_LOG.ORDERSNO is '委托序号'  
/  
  
comment on column LCIMS.INCENT_EXERCISE_LOG.OPTIONCODE is '行权代码'  
/  
  
create index LCIMS.IDX_INCENT_EXERCISE_LOG  
    on LCIMS.INCENT_EXERCISE_LOG (FUNDID)  
/
```

# 3. 前后端交互接口信息

## 3.1. 期权持仓查询

### 3.1.1. 接口路径

POST:financingOption/queryOptionPositions

### 3.1.2. 接口入参

无

### 3.1.3. 3. 接口出参

| 参数名称    | 参数含义   | 参数类型   | 是否必填 | 备注  |
| ------- | ------ | ------ | ---- | --- |
| Bdzqdm  | 标的证券代码 | String | 是    |     |
| Bdzqmc  | 标的证券名称 | String | 是    |     |
| Gddm    | 股东代码   | String | 是    |     |
| Market  | 交易市场   | String | 是    |     |
| Gfye    | 股份余额   | String | 是    |     |
| Kysl    | 可用数量   | String | 是    |     |
| Xqdm    | 行权代码   | String | 是    |     |
| Xqdmmc  | 行权代码名称 | String | 是    |     |
| Xqjg    | 行权价格   | String | 是    |     |
| planid  | 计划ID   | String | 是    |     |
| version | 批次ID   | String | 是    |     |

## 3.2. 行权信息查询

### 3.2.1. 接口路径

POST:financingOption/queryOptionInfo

### 3.2.2. 接口入参

| 参数名称   | 参数含义   | 参数类型   | 是否必填 | 备注    |
| ------ | ------ | ------ | ---- | ----- |
| Xqdm   | 行权代码   | String | 是    |       |
| Zqdm   | 标的证券代码 | String | 是    |       |
| Market | 交易市场   | String | 是    | HA/SA |
| Gddm   | 股东代码   | String | 是    |       |

### 3.2.3. 接口出参

| 参数名称    | 参数含义    | 参数类型   | 是否必填 | 备注          |
|---------|---------|--------|------|-------------|
| Xqmc    | 行权名称    | String | 是    |             |
| Xqjg    | 行权价格    | String | 是    |             |
| Kyed    | 可用额度    | String | 是    |             |
| Gfsl    | 最大可行权数量 | String | 是    |             |
| Sfclsds | 是否处理所得税 | String | 是    | 0:不处理, 1:处理 |

## 3.3. 融资行权委托

### 3.3.1. 接口路径

POST:financingOption/exerciseWithLog

### 3.3.2. 接口入参

| 参数名称      | 参数含义 | 参数类型   | 是否必填 | 备注          |
| --------- | ---- | ------ | ---- | ----------- |
| planid    | 计划ID | String | 否    |             |
| versionid | 批次ID | String | 否    |             |
| Jysc      | 交易市场 | String | 是    | 0: 深市，1: 沪市 |
| Gddm      | 股东代码 | String | 是    |             |
| Zqdm      | 行权代码 | String | 是    |             |
| Sl        | 数量   | String | 是    |             |
| companyId | 公司ID | String | 否    |             |

### 3.3.3. 接口出参

| 参数名称 | 参数含义 | 参数类型   | 是否必填 | 备注  |
| ---- | ---- | ------ | ---- | --- |
| Wtxh | 委托序号 | String | 是    |     |
| Htxh | 合同序号 | String | 是    |     |
| Wtph | 委托批号 | String | 是    |     |

# 4. 配置&部署修改信息

不涉及

# 5. 新增技术组件说明

不涉及

# 6. 影响范围

不涉及

# 7. 外部依赖项

柜台服务
