/*
THIS IS A GENERATED/BUNDLED FILE BY ESBUILD
if you want to view the source visit the plugins github repository
*/

/*
License obsidian-tasks:
MIT License

Copyright (c) 2021 <PERSON>, <PERSON><PERSON> and <PERSON>

Permission is hereby granted, free of charge, to any person obtaining a copy
of this software and associated documentation files (the "Software"), to deal
in the Software without restriction, including without limitation the rights
to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
copies of the Software, and to permit persons to whom the Software is
furnished to do so, subject to the following conditions:

The above copyright notice and this permission notice shall be included in all
copies or substantial portions of the Software.

THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE
SOFTWARE.
*/

/*
License rrule (included library):
rrule.js: Library for working with recurrence rules for calendar dates.
=======================================================================

Copyright 2010, Jakub Roztocil <<EMAIL>> and Lars Schöning

Redistribution and use in source and binary forms, with or without
modification, are permitted provided that the following conditions are met:

    1. Redistributions of source code must retain the above copyright notice,
       this list of conditions and the following disclaimer.

    2. Redistributions in binary form must reproduce the above copyright
       notice, this list of conditions and the following disclaimer in the
       documentation and/or other materials provided with the distribution.

    3. Neither the name of The author nor the names of its contributors may
       be used to endorse or promote products derived from this software
       without specific prior written permission.

THIS SOFTWARE IS PROVIDED BY THE AUTHOR AND CONTRIBUTORS "AS IS" AND
ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
DISCLAIMED. IN NO EVENT SHALL THE AUTHOR AND CONTRIBUTORS BE LIABLE FOR
ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
(INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON
ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
(INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.



./rrule.js and ./test/tests.js is based on python-dateutil. LICENCE:

python-dateutil - Extensions to the standard Python datetime module.
====================================================================

Copyright (c) 2003-2011 - Gustavo Niemeyer <<EMAIL>>
Copyright (c) 2012 - Tomi Pieviläinen <<EMAIL>>

All rights reserved.

Redistribution and use in source and binary forms, with or without
modification, are permitted provided that the following conditions are met:

    * Redistributions of source code must retain the above copyright notice,
      this list of conditions and the following disclaimer.
    * Redistributions in binary form must reproduce the above copyright notice,
      this list of conditions and the following disclaimer in the documentation
      and/or other materials provided with the distribution.
    * Neither the name of the copyright holder nor the names of its
      contributors may be used to endorse or promote products derived from
      this software without specific prior written permission.

THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS
"AS IS" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT
LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR
A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT OWNER OR
CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL,
EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO,
PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR
PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF
LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING
NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
*/

/*
License chrono-node (included library):
The MIT License

Copyright (c) 2014, Wanasit Tanakitrungruang

Permission is hereby granted, free of charge, to any person obtaining a copy
of this software and associated documentation files (the "Software"), to deal
in the Software without restriction, including without limitation the rights
to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
copies of the Software, and to permit persons to whom the Software is
furnished to do so, subject to the following conditions:

The above copyright notice and this permission notice shall be included in
all copies or substantial portions of the Software.

THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN
THE SOFTWARE.
*/

/*
License flatpickr (included library):
The MIT License (MIT)

Copyright (c) 2017 Gregory Petrosyan

Permission is hereby granted, free of charge, to any person obtaining a copy
of this software and associated documentation files (the "Software"), to deal
in the Software without restriction, including without limitation the rights
to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
copies of the Software, and to permit persons to whom the Software is
furnished to do so, subject to the following conditions:

The above copyright notice and this permission notice shall be included in all
copies or substantial portions of the Software.

THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE
SOFTWARE.
*/

"use strict";var _E=Object.create;var pa=Object.defineProperty,vE=Object.defineProperties,wE=Object.getOwnPropertyDescriptor,kE=Object.getOwnPropertyDescriptors,EE=Object.getOwnPropertyNames,Xo=Object.getOwnPropertySymbols,SE=Object.getPrototypeOf,Nc=Object.prototype.hasOwnProperty,og=Object.prototype.propertyIsEnumerable;var ag=(r,e,t)=>e in r?pa(r,e,{enumerable:!0,configurable:!0,writable:!0,value:t}):r[e]=t,K=(r,e)=>{for(var t in e||(e={}))Nc.call(e,t)&&ag(r,t,e[t]);if(Xo)for(var t of Xo(e))og.call(e,t)&&ag(r,t,e[t]);return r},de=(r,e)=>vE(r,kE(e));var Zo=(r,e)=>{var t={};for(var n in r)Nc.call(r,n)&&e.indexOf(n)<0&&(t[n]=r[n]);if(r!=null&&Xo)for(var n of Xo(r))e.indexOf(n)<0&&og.call(r,n)&&(t[n]=r[n]);return t};var E=(r,e)=>()=>(e||r((e={exports:{}}).exports,e),e.exports),OE=(r,e)=>{for(var t in e)pa(r,t,{get:e[t],enumerable:!0})},ug=(r,e,t,n)=>{if(e&&typeof e=="object"||typeof e=="function")for(let i of EE(e))!Nc.call(r,i)&&i!==t&&pa(r,i,{get:()=>e[i],enumerable:!(n=wE(e,i))||n.enumerable});return r};var ma=(r,e,t)=>(t=r!=null?_E(SE(r)):{},ug(e||!r||!r.__esModule?pa(t,"default",{value:r,enumerable:!0}):t,r)),DE=r=>ug(pa({},"__esModule",{value:!0}),r);var A=(r,e,t)=>new Promise((n,i)=>{var s=u=>{try{o(t.next(u))}catch(l){i(l)}},a=u=>{try{o(t.throw(u))}catch(l){i(l)}},o=u=>u.done?n(u.value):Promise.resolve(u.value).then(s,a);o((t=t.apply(r,e)).next())});var Re=E(Gn=>{"use strict";Object.defineProperty(Gn,"__esModule",{value:!0});Gn.matchAnyPattern=Gn.extractTerms=Gn.repeatedTimeunitPattern=void 0;function CE(r,e){let t=e.replace(/\((?!\?)/g,"(?:");return`${r}${t}\\s{0,5}(?:,?\\s{0,5}${t}){0,10}`}Gn.repeatedTimeunitPattern=CE;function lg(r){let e;return r instanceof Array?e=[...r]:r instanceof Map?e=Array.from(r.keys()):e=Object.keys(r),e}Gn.extractTerms=lg;function AE(r){return`(?:${lg(r).sort((t,n)=>n.length-t.length).join("|").replace(/\./g,"\\.")})`}Gn.matchAnyPattern=AE});var we=E((Fc,Lc)=>{(function(r,e){typeof Fc=="object"&&typeof Lc!="undefined"?Lc.exports=e():typeof define=="function"&&define.amd?define(e):(r=typeof globalThis!="undefined"?globalThis:r||self).dayjs=e()})(Fc,function(){"use strict";var r=1e3,e=6e4,t=36e5,n="millisecond",i="second",s="minute",a="hour",o="day",u="week",l="month",c="quarter",d="year",f="date",m="Invalid Date",y=/^(\d{4})[-/]?(\d{1,2})?[-/]?(\d{0,2})[Tt\s]*(\d{1,2})?:?(\d{1,2})?:?(\d{1,2})?[.:]?(\d+)?$/,b=/\[([^\]]+)]|Y{1,4}|M{1,4}|D{1,2}|d{1,4}|H{1,2}|h{1,2}|a|A|m{1,2}|s{1,2}|Z{1,2}|SSS/g,k={name:"en",weekdays:"Sunday_Monday_Tuesday_Wednesday_Thursday_Friday_Saturday".split("_"),months:"January_February_March_April_May_June_July_August_September_October_November_December".split("_")},_=function(j,$,D){var Y=String(j);return!Y||Y.length>=$?j:""+Array($+1-Y.length).join(D)+j},R={s:_,z:function(j){var $=-j.utcOffset(),D=Math.abs($),Y=Math.floor(D/60),I=D%60;return($<=0?"+":"-")+_(Y,2,"0")+":"+_(I,2,"0")},m:function j($,D){if($.date()<D.date())return-j(D,$);var Y=12*(D.year()-$.year())+(D.month()-$.month()),I=$.clone().add(Y,l),J=D-I<0,p=$.clone().add(Y+(J?-1:1),l);return+(-(Y+(D-I)/(J?I-p:p-I))||0)},a:function(j){return j<0?Math.ceil(j)||0:Math.floor(j)},p:function(j){return{M:l,y:d,w:u,d:o,D:f,h:a,m:s,s:i,ms:n,Q:c}[j]||String(j||"").toLowerCase().replace(/s$/,"")},u:function(j){return j===void 0}},S="en",F={};F[S]=k;var q=function(j){return j instanceof ge},ne=function(j,$,D){var Y;if(!j)return S;if(typeof j=="string")F[j]&&(Y=j),$&&(F[j]=$,Y=j);else{var I=j.name;F[I]=j,Y=I}return!D&&Y&&(S=Y),Y||!D&&S},G=function(j,$){if(q(j))return j.clone();var D=typeof $=="object"?$:{};return D.date=j,D.args=arguments,new ge(D)},B=R;B.l=ne,B.i=q,B.w=function(j,$){return G(j,{locale:$.$L,utc:$.$u,x:$.$x,$offset:$.$offset})};var ge=function(){function j(D){this.$L=ne(D.locale,null,!0),this.parse(D)}var $=j.prototype;return $.parse=function(D){this.$d=function(Y){var I=Y.date,J=Y.utc;if(I===null)return new Date(NaN);if(B.u(I))return new Date;if(I instanceof Date)return new Date(I);if(typeof I=="string"&&!/Z$/i.test(I)){var p=I.match(y);if(p){var h=p[2]-1||0,g=(p[7]||"0").substring(0,3);return J?new Date(Date.UTC(p[1],h,p[3]||1,p[4]||0,p[5]||0,p[6]||0,g)):new Date(p[1],h,p[3]||1,p[4]||0,p[5]||0,p[6]||0,g)}}return new Date(I)}(D),this.$x=D.x||{},this.init()},$.init=function(){var D=this.$d;this.$y=D.getFullYear(),this.$M=D.getMonth(),this.$D=D.getDate(),this.$W=D.getDay(),this.$H=D.getHours(),this.$m=D.getMinutes(),this.$s=D.getSeconds(),this.$ms=D.getMilliseconds()},$.$utils=function(){return B},$.isValid=function(){return this.$d.toString()!==m},$.isSame=function(D,Y){var I=G(D);return this.startOf(Y)<=I&&I<=this.endOf(Y)},$.isAfter=function(D,Y){return G(D)<this.startOf(Y)},$.isBefore=function(D,Y){return this.endOf(Y)<G(D)},$.$g=function(D,Y,I){return B.u(D)?this[Y]:this.set(I,D)},$.unix=function(){return Math.floor(this.valueOf()/1e3)},$.valueOf=function(){return this.$d.getTime()},$.startOf=function(D,Y){var I=this,J=!!B.u(Y)||Y,p=B.p(D),h=function(x,N){var ie=B.w(I.$u?Date.UTC(I.$y,N,x):new Date(I.$y,N,x),I);return J?ie:ie.endOf(o)},g=function(x,N){return B.w(I.toDate()[x].apply(I.toDate("s"),(J?[0,0,0,0]:[23,59,59,999]).slice(N)),I)},T=this.$W,w=this.$M,O=this.$D,M="set"+(this.$u?"UTC":"");switch(p){case d:return J?h(1,0):h(31,11);case l:return J?h(1,w):h(0,w+1);case u:var P=this.$locale().weekStart||0,v=(T<P?T+7:T)-P;return h(J?O-v:O+(6-v),w);case o:case f:return g(M+"Hours",0);case a:return g(M+"Minutes",1);case s:return g(M+"Seconds",2);case i:return g(M+"Milliseconds",3);default:return this.clone()}},$.endOf=function(D){return this.startOf(D,!1)},$.$set=function(D,Y){var I,J=B.p(D),p="set"+(this.$u?"UTC":""),h=(I={},I[o]=p+"Date",I[f]=p+"Date",I[l]=p+"Month",I[d]=p+"FullYear",I[a]=p+"Hours",I[s]=p+"Minutes",I[i]=p+"Seconds",I[n]=p+"Milliseconds",I)[J],g=J===o?this.$D+(Y-this.$W):Y;if(J===l||J===d){var T=this.clone().set(f,1);T.$d[h](g),T.init(),this.$d=T.set(f,Math.min(this.$D,T.daysInMonth())).$d}else h&&this.$d[h](g);return this.init(),this},$.set=function(D,Y){return this.clone().$set(D,Y)},$.get=function(D){return this[B.p(D)]()},$.add=function(D,Y){var I,J=this;D=Number(D);var p=B.p(Y),h=function(w){var O=G(J);return B.w(O.date(O.date()+Math.round(w*D)),J)};if(p===l)return this.set(l,this.$M+D);if(p===d)return this.set(d,this.$y+D);if(p===o)return h(1);if(p===u)return h(7);var g=(I={},I[s]=e,I[a]=t,I[i]=r,I)[p]||1,T=this.$d.getTime()+D*g;return B.w(T,this)},$.subtract=function(D,Y){return this.add(-1*D,Y)},$.format=function(D){var Y=this,I=this.$locale();if(!this.isValid())return I.invalidDate||m;var J=D||"YYYY-MM-DDTHH:mm:ssZ",p=B.z(this),h=this.$H,g=this.$m,T=this.$M,w=I.weekdays,O=I.months,M=function(N,ie,ce,me){return N&&(N[ie]||N(Y,J))||ce[ie].substr(0,me)},P=function(N){return B.s(h%12||12,N,"0")},v=I.meridiem||function(N,ie,ce){var me=N<12?"AM":"PM";return ce?me.toLowerCase():me},x={YY:String(this.$y).slice(-2),YYYY:this.$y,M:T+1,MM:B.s(T+1,2,"0"),MMM:M(I.monthsShort,T,O,3),MMMM:M(O,T),D:this.$D,DD:B.s(this.$D,2,"0"),d:String(this.$W),dd:M(I.weekdaysMin,this.$W,w,2),ddd:M(I.weekdaysShort,this.$W,w,3),dddd:w[this.$W],H:String(h),HH:B.s(h,2,"0"),h:P(1),hh:P(2),a:v(h,g,!0),A:v(h,g,!1),m:String(g),mm:B.s(g,2,"0"),s:String(this.$s),ss:B.s(this.$s,2,"0"),SSS:B.s(this.$ms,3,"0"),Z:p};return J.replace(b,function(N,ie){return ie||x[N]||p.replace(":","")})},$.utcOffset=function(){return 15*-Math.round(this.$d.getTimezoneOffset()/15)},$.diff=function(D,Y,I){var J,p=B.p(Y),h=G(D),g=(h.utcOffset()-this.utcOffset())*e,T=this-h,w=B.m(this,h);return w=(J={},J[d]=w/12,J[l]=w,J[c]=w/3,J[u]=(T-g)/6048e5,J[o]=(T-g)/864e5,J[a]=T/t,J[s]=T/e,J[i]=T/r,J)[p]||T,I?w:B.a(w)},$.daysInMonth=function(){return this.endOf(l).$D},$.$locale=function(){return F[this.$L]},$.locale=function(D,Y){if(!D)return this.$L;var I=this.clone(),J=ne(D,Y,!0);return J&&(I.$L=J),I},$.clone=function(){return B.w(this.$d,this)},$.toDate=function(){return new Date(this.valueOf())},$.toJSON=function(){return this.isValid()?this.toISOString():null},$.toISOString=function(){return this.$d.toISOString()},$.toString=function(){return this.$d.toUTCString()},j}(),Pe=ge.prototype;return G.prototype=Pe,[["$ms",n],["$s",i],["$m",s],["$H",a],["$W",o],["$M",l],["$y",d],["$D",f]].forEach(function(j){Pe[j[1]]=function($){return this.$g($,j[0],j[1])}}),G.extend=function(j,$){return j.$i||(j($,ge,G),j.$i=!0),G},G.locale=ne,G.isDayjs=q,G.unix=function(j){return G(1e3*j)},G.en=F[S],G.Ls=F,G.p={},G})});var lt=E(Yn=>{"use strict";var PE=Yn&&Yn.__importDefault||function(r){return r&&r.__esModule?r:{default:r}};Object.defineProperty(Yn,"__esModule",{value:!0});Yn.findYearClosestToRef=Yn.findMostLikelyADYear=void 0;var NE=PE(we());function IE(r){return r<100&&(r>50?r=r+1900:r=r+2e3),r}Yn.findMostLikelyADYear=IE;function FE(r,e,t){let n=NE.default(r),i=n;i=i.month(t-1),i=i.date(e),i=i.year(n.year());let s=i.add(1,"y"),a=i.add(-1,"y");return Math.abs(s.diff(n))<Math.abs(i.diff(n))?i=s:Math.abs(a.diff(n))<Math.abs(i.diff(n))&&(i=a),i.year()}Yn.findYearClosestToRef=FE});var ct=E(fe=>{"use strict";Object.defineProperty(fe,"__esModule",{value:!0});fe.parseTimeUnits=fe.TIME_UNITS_PATTERN=fe.parseYear=fe.YEAR_PATTERN=fe.parseOrdinalNumberPattern=fe.ORDINAL_NUMBER_PATTERN=fe.parseNumberPattern=fe.NUMBER_PATTERN=fe.TIME_UNIT_DICTIONARY=fe.ORDINAL_WORD_DICTIONARY=fe.INTEGER_WORD_DICTIONARY=fe.MONTH_DICTIONARY=fe.FULL_MONTH_NAME_DICTIONARY=fe.WEEKDAY_DICTIONARY=void 0;var tu=Re(),LE=lt();fe.WEEKDAY_DICTIONARY={sunday:0,sun:0,"sun.":0,monday:1,mon:1,"mon.":1,tuesday:2,tue:2,"tue.":2,wednesday:3,wed:3,"wed.":3,thursday:4,thurs:4,"thurs.":4,thur:4,"thur.":4,thu:4,"thu.":4,friday:5,fri:5,"fri.":5,saturday:6,sat:6,"sat.":6};fe.FULL_MONTH_NAME_DICTIONARY={january:1,february:2,march:3,april:4,may:5,june:6,july:7,august:8,september:9,october:10,november:11,december:12};fe.MONTH_DICTIONARY=Object.assign(Object.assign({},fe.FULL_MONTH_NAME_DICTIONARY),{jan:1,"jan.":1,feb:2,"feb.":2,mar:3,"mar.":3,apr:4,"apr.":4,jun:6,"jun.":6,jul:7,"jul.":7,aug:8,"aug.":8,sep:9,"sep.":9,sept:9,"sept.":9,oct:10,"oct.":10,nov:11,"nov.":11,dec:12,"dec.":12});fe.INTEGER_WORD_DICTIONARY={one:1,two:2,three:3,four:4,five:5,six:6,seven:7,eight:8,nine:9,ten:10,eleven:11,twelve:12};fe.ORDINAL_WORD_DICTIONARY={first:1,second:2,third:3,fourth:4,fifth:5,sixth:6,seventh:7,eighth:8,ninth:9,tenth:10,eleventh:11,twelfth:12,thirteenth:13,fourteenth:14,fifteenth:15,sixteenth:16,seventeenth:17,eighteenth:18,nineteenth:19,twentieth:20,"twenty first":21,"twenty-first":21,"twenty second":22,"twenty-second":22,"twenty third":23,"twenty-third":23,"twenty fourth":24,"twenty-fourth":24,"twenty fifth":25,"twenty-fifth":25,"twenty sixth":26,"twenty-sixth":26,"twenty seventh":27,"twenty-seventh":27,"twenty eighth":28,"twenty-eighth":28,"twenty ninth":29,"twenty-ninth":29,thirtieth:30,"thirty first":31,"thirty-first":31};fe.TIME_UNIT_DICTIONARY={sec:"second",second:"second",seconds:"second",min:"minute",mins:"minute",minute:"minute",minutes:"minute",h:"hour",hr:"hour",hrs:"hour",hour:"hour",hours:"hour",day:"d",days:"d",week:"week",weeks:"week",month:"month",months:"month",qtr:"quarter",quarter:"quarter",quarters:"quarter",y:"year",yr:"year",year:"year",years:"year"};fe.NUMBER_PATTERN=`(?:${tu.matchAnyPattern(fe.INTEGER_WORD_DICTIONARY)}|[0-9]+|[0-9]+\\.[0-9]+|half(?:\\s{0,2}an?)?|an?\\b(?:\\s{0,2}few)?|few|several|a?\\s{0,2}couple\\s{0,2}(?:of)?)`;function dg(r){let e=r.toLowerCase();return fe.INTEGER_WORD_DICTIONARY[e]!==void 0?fe.INTEGER_WORD_DICTIONARY[e]:e==="a"||e==="an"?1:e.match(/few/)?3:e.match(/half/)?.5:e.match(/couple/)?2:e.match(/several/)?7:parseFloat(e)}fe.parseNumberPattern=dg;fe.ORDINAL_NUMBER_PATTERN=`(?:${tu.matchAnyPattern(fe.ORDINAL_WORD_DICTIONARY)}|[0-9]{1,2}(?:st|nd|rd|th)?)`;function UE(r){let e=r.toLowerCase();return fe.ORDINAL_WORD_DICTIONARY[e]!==void 0?fe.ORDINAL_WORD_DICTIONARY[e]:(e=e.replace(/(?:st|nd|rd|th)$/i,""),parseInt(e))}fe.parseOrdinalNumberPattern=UE;fe.YEAR_PATTERN="(?:[1-9][0-9]{0,3}\\s{0,2}(?:BE|AD|BC|BCE|CE)|[1-2][0-9]{3}|[5-9][0-9])";function WE(r){if(/BE/i.test(r))return r=r.replace(/BE/i,""),parseInt(r)-543;if(/BCE?/i.test(r))return r=r.replace(/BCE?/i,""),-parseInt(r);if(/(AD|CE)/i.test(r))return r=r.replace(/(AD|CE)/i,""),parseInt(r);let e=parseInt(r);return LE.findMostLikelyADYear(e)}fe.parseYear=WE;var fg=`(${fe.NUMBER_PATTERN})\\s{0,3}(${tu.matchAnyPattern(fe.TIME_UNIT_DICTIONARY)})`,cg=new RegExp(fg,"i");fe.TIME_UNITS_PATTERN=tu.repeatedTimeunitPattern("(?:(?:about|around)\\s{0,3})?",fg);function qE(r){let e={},t=r,n=cg.exec(t);for(;n;)$E(e,n),t=t.substring(n[0].length).trim(),n=cg.exec(t);return e}fe.parseTimeUnits=qE;function $E(r,e){let t=dg(e[1]),n=fe.TIME_UNIT_DICTIONARY[e[2].toLowerCase()];r[n]=t}});var pg=E((Uc,Wc)=>{(function(r,e){typeof Uc=="object"&&typeof Wc!="undefined"?Wc.exports=e():typeof define=="function"&&define.amd?define(e):(r=typeof globalThis!="undefined"?globalThis:r||self).dayjs_plugin_quarterOfYear=e()})(Uc,function(){"use strict";var r="month",e="quarter";return function(t,n){var i=n.prototype;i.quarter=function(o){return this.$utils().u(o)?Math.ceil((this.month()+1)/3):this.month(this.month()%3+3*(o-1))};var s=i.add;i.add=function(o,u){return o=Number(o),this.$utils().p(u)===e?this.add(3*o,r):s.bind(this)(o,u)};var a=i.startOf;i.startOf=function(o,u){var l=this.$utils(),c=!!l.u(u)||u;if(l.p(o)===e){var d=this.quarter()-1;return c?this.month(3*d).startOf(r).startOf("day"):this.month(3*d+2).endOf(r).endOf("day")}return a.bind(this)(o,u)}}})});var ir=E(Vr=>{"use strict";Object.defineProperty(Vr,"__esModule",{value:!0});Vr.implySimilarTime=Vr.assignSimilarTime=Vr.assignSimilarDate=Vr.assignTheNextDay=void 0;var mg=Ke();function jE(r,e){e=e.add(1,"day"),hg(r,e),gg(r,e)}Vr.assignTheNextDay=jE;function hg(r,e){r.assign("day",e.date()),r.assign("month",e.month()+1),r.assign("year",e.year())}Vr.assignSimilarDate=hg;function GE(r,e){r.assign("hour",e.hour()),r.assign("minute",e.minute()),r.assign("second",e.second()),r.assign("millisecond",e.millisecond()),r.get("hour")<12?r.assign("meridiem",mg.Meridiem.AM):r.assign("meridiem",mg.Meridiem.PM)}Vr.assignSimilarTime=GE;function gg(r,e){r.imply("hour",e.hour()),r.imply("minute",e.minute()),r.imply("second",e.second()),r.imply("millisecond",e.millisecond())}Vr.implySimilarTime=gg});var yg=E(mi=>{"use strict";Object.defineProperty(mi,"__esModule",{value:!0});mi.toTimezoneOffset=mi.TIMEZONE_ABBR_MAP=void 0;mi.TIMEZONE_ABBR_MAP={ACDT:630,ACST:570,ADT:-180,AEDT:660,AEST:600,AFT:270,AKDT:-480,AKST:-540,ALMT:360,AMST:-180,AMT:-240,ANAST:720,ANAT:720,AQTT:300,ART:-180,AST:-240,AWDT:540,AWST:480,AZOST:0,AZOT:-60,AZST:300,AZT:240,BNT:480,BOT:-240,BRST:-120,BRT:-180,BST:60,BTT:360,CAST:480,CAT:120,CCT:390,CDT:-300,CEST:120,CET:60,CHADT:825,CHAST:765,CKT:-600,CLST:-180,CLT:-240,COT:-300,CST:-360,CVT:-60,CXT:420,ChST:600,DAVT:420,EASST:-300,EAST:-360,EAT:180,ECT:-300,EDT:-240,EEST:180,EET:120,EGST:0,EGT:-60,EST:-300,ET:-300,FJST:780,FJT:720,FKST:-180,FKT:-240,FNT:-120,GALT:-360,GAMT:-540,GET:240,GFT:-180,GILT:720,GMT:0,GST:240,GYT:-240,HAA:-180,HAC:-300,HADT:-540,HAE:-240,HAP:-420,HAR:-360,HAST:-600,HAT:-90,HAY:-480,HKT:480,HLV:-210,HNA:-240,HNC:-360,HNE:-300,HNP:-480,HNR:-420,HNT:-150,HNY:-540,HOVT:420,ICT:420,IDT:180,IOT:360,IRDT:270,IRKST:540,IRKT:540,IRST:210,IST:330,JST:540,KGT:360,KRAST:480,KRAT:480,KST:540,KUYT:240,LHDT:660,LHST:630,LINT:840,MAGST:720,MAGT:720,MART:-510,MAWT:300,MDT:-360,MESZ:120,MEZ:60,MHT:720,MMT:390,MSD:240,MSK:180,MST:-420,MUT:240,MVT:300,MYT:480,NCT:660,NDT:-90,NFT:690,NOVST:420,NOVT:360,NPT:345,NST:-150,NUT:-660,NZDT:780,NZST:720,OMSST:420,OMST:420,PDT:-420,PET:-300,PETST:720,PETT:720,PGT:600,PHOT:780,PHT:480,PKT:300,PMDT:-120,PMST:-180,PONT:660,PST:-480,PT:-480,PWT:540,PYST:-180,PYT:-240,RET:240,SAMT:240,SAST:120,SBT:660,SCT:240,SGT:480,SRT:-180,SST:-660,TAHT:-600,TFT:300,TJT:300,TKT:780,TLT:540,TMT:300,TVT:720,ULAT:480,UTC:0,UYST:-120,UYT:-180,UZT:300,VET:-210,VLAST:660,VLAT:660,VUT:660,WAST:120,WAT:60,WEST:60,WESZ:60,WET:0,WEZ:0,WFT:720,WGST:-120,WGT:-180,WIB:420,WIT:540,WITA:480,WST:780,WT:0,YAKST:600,YAKT:600,YAPT:600,YEKST:360,YEKT:360};function YE(r){var e;return r==null?null:typeof r=="number"?r:(e=mi.TIMEZONE_ABBR_MAP[r])!==null&&e!==void 0?e:null}mi.toTimezoneOffset=YE});var We=E(zr=>{"use strict";var Tg=zr&&zr.__importDefault||function(r){return r&&r.__esModule?r:{default:r}};Object.defineProperty(zr,"__esModule",{value:!0});zr.ParsingResult=zr.ParsingComponents=zr.ReferenceWithTimezone=void 0;var BE=Tg(pg()),ru=Tg(we()),qc=ir(),HE=yg();ru.default.extend(BE.default);var $c=class{constructor(e){var t;e=e!=null?e:new Date,e instanceof Date?this.instant=e:(this.instant=(t=e.instant)!==null&&t!==void 0?t:new Date,this.timezoneOffset=HE.toTimezoneOffset(e.timezone))}};zr.ReferenceWithTimezone=$c;var hi=class{constructor(e,t){if(this.reference=e,this.knownValues={},this.impliedValues={},t)for(let i in t)this.knownValues[i]=t[i];let n=ru.default(e.instant);this.imply("day",n.date()),this.imply("month",n.month()+1),this.imply("year",n.year()),this.imply("hour",12),this.imply("minute",0),this.imply("second",0),this.imply("millisecond",0)}get(e){return e in this.knownValues?this.knownValues[e]:e in this.impliedValues?this.impliedValues[e]:null}isCertain(e){return e in this.knownValues}getCertainComponents(){return Object.keys(this.knownValues)}imply(e,t){return e in this.knownValues?this:(this.impliedValues[e]=t,this)}assign(e,t){return this.knownValues[e]=t,delete this.impliedValues[e],this}delete(e){delete this.knownValues[e],delete this.impliedValues[e]}clone(){let e=new hi(this.reference);e.knownValues={},e.impliedValues={};for(let t in this.knownValues)e.knownValues[t]=this.knownValues[t];for(let t in this.impliedValues)e.impliedValues[t]=this.impliedValues[t];return e}isOnlyDate(){return!this.isCertain("hour")&&!this.isCertain("minute")&&!this.isCertain("second")}isOnlyTime(){return!this.isCertain("weekday")&&!this.isCertain("day")&&!this.isCertain("month")}isOnlyWeekdayComponent(){return this.isCertain("weekday")&&!this.isCertain("day")&&!this.isCertain("month")}isOnlyDayMonthComponent(){return this.isCertain("day")&&this.isCertain("month")&&!this.isCertain("year")}isValidDate(){let e=this.dateWithoutTimezoneAdjustment();return!(e.getFullYear()!==this.get("year")||e.getMonth()!==this.get("month")-1||e.getDate()!==this.get("day")||this.get("hour")!=null&&e.getHours()!=this.get("hour")||this.get("minute")!=null&&e.getMinutes()!=this.get("minute"))}toString(){return`[ParsingComponents {knownValues: ${JSON.stringify(this.knownValues)}, impliedValues: ${JSON.stringify(this.impliedValues)}}, reference: ${JSON.stringify(this.reference)}]`}dayjs(){return ru.default(this.date())}date(){let e=this.dateWithoutTimezoneAdjustment();return new Date(e.getTime()+this.getSystemTimezoneAdjustmentMinute(e)*6e4)}dateWithoutTimezoneAdjustment(){let e=new Date(this.get("year"),this.get("month")-1,this.get("day"),this.get("hour"),this.get("minute"),this.get("second"),this.get("millisecond"));return e.setFullYear(this.get("year")),e}getSystemTimezoneAdjustmentMinute(e){var t,n;(!e||e.getTime()<0)&&(e=new Date);let i=-e.getTimezoneOffset(),s=(n=(t=this.get("timezoneOffset"))!==null&&t!==void 0?t:this.reference.timezoneOffset)!==null&&n!==void 0?n:i;return i-s}static createRelativeFromReference(e,t){let n=ru.default(e.instant);for(let s in t)n=n.add(t[s],s);let i=new hi(e);return t.hour||t.minute||t.second?(qc.assignSimilarTime(i,n),qc.assignSimilarDate(i,n),e.timezoneOffset!==null&&i.assign("timezoneOffset",-e.instant.getTimezoneOffset())):(qc.implySimilarTime(i,n),e.timezoneOffset!==null&&i.imply("timezoneOffset",-e.instant.getTimezoneOffset()),t.d?(i.assign("day",n.date()),i.assign("month",n.month()+1),i.assign("year",n.year())):(t.week&&i.imply("weekday",n.day()),i.imply("day",n.date()),t.month?(i.assign("month",n.month()+1),i.assign("year",n.year())):(i.imply("month",n.month()+1),t.year?i.assign("year",n.year()):i.imply("year",n.year())))),i}};zr.ParsingComponents=hi;var ha=class{constructor(e,t,n,i,s){this.reference=e,this.refDate=e.instant,this.index=t,this.text=n,this.start=i||new hi(e),this.end=s}clone(){let e=new ha(this.reference,this.index,this.text);return e.start=this.start?this.start.clone():null,e.end=this.end?this.end.clone():null,e}date(){return this.start.date()}toString(){return`[ParsingResult {index: ${this.index}, text: '${this.text}', ...}]`}};zr.ParsingResult=ha});var H=E(nu=>{"use strict";Object.defineProperty(nu,"__esModule",{value:!0});nu.AbstractParserWithWordBoundaryChecking=void 0;var jc=class{constructor(){this.cachedInnerPattern=null,this.cachedPattern=null}patternLeftBoundary(){return"(\\W|^)"}pattern(e){let t=this.innerPattern(e);return t==this.cachedInnerPattern?this.cachedPattern:(this.cachedPattern=new RegExp(`${this.patternLeftBoundary()}${t.source}`,t.flags),this.cachedInnerPattern=t,this.cachedPattern)}extract(e,t){var n;let i=(n=t[1])!==null&&n!==void 0?n:"";t.index=t.index+i.length,t[0]=t[0].substring(i.length);for(let s=2;s<t.length;s++)t[s-1]=t[s];return this.innerExtract(e,t)}};nu.AbstractParserWithWordBoundaryChecking=jc});var bg=E(Bc=>{"use strict";Object.defineProperty(Bc,"__esModule",{value:!0});var Yc=ct(),VE=We(),zE=H(),KE=new RegExp(`(?:within|in|for)\\s*(?:(?:about|around|roughly|approximately|just)\\s*(?:~\\s*)?)?(${Yc.TIME_UNITS_PATTERN})(?=\\W|$)`,"i"),QE=new RegExp(`(?:(?:about|around|roughly|approximately|just)\\s*(?:~\\s*)?)?(${Yc.TIME_UNITS_PATTERN})(?=\\W|$)`,"i"),Gc=class extends zE.AbstractParserWithWordBoundaryChecking{innerPattern(e){return e.option.forwardDate?QE:KE}innerExtract(e,t){let n=Yc.parseTimeUnits(t[1]);return VE.ParsingComponents.createRelativeFromReference(e.reference,n)}};Bc.default=Gc});var Sg=E(Vc=>{"use strict";Object.defineProperty(Vc,"__esModule",{value:!0});var XE=lt(),kg=ct(),Eg=ct(),iu=ct(),ZE=Re(),JE=H(),eS=new RegExp(`(?:on\\s{0,3})?(${iu.ORDINAL_NUMBER_PATTERN})(?:\\s{0,3}(?:to|\\-|\\\u2013|until|through|till)?\\s{0,3}(${iu.ORDINAL_NUMBER_PATTERN}))?(?:-|/|\\s{0,3}(?:of)?\\s{0,3})(${ZE.matchAnyPattern(kg.MONTH_DICTIONARY)})(?:(?:-|/|,?\\s{0,3})(${Eg.YEAR_PATTERN}(?![^\\s]\\d)))?(?=\\W|$)`,"i"),_g=1,vg=2,tS=3,wg=4,Hc=class extends JE.AbstractParserWithWordBoundaryChecking{innerPattern(){return eS}innerExtract(e,t){let n=e.createParsingResult(t.index,t[0]),i=kg.MONTH_DICTIONARY[t[tS].toLowerCase()],s=iu.parseOrdinalNumberPattern(t[_g]);if(s>31)return t.index=t.index+t[_g].length,null;if(n.start.assign("month",i),n.start.assign("day",s),t[wg]){let a=Eg.parseYear(t[wg]);n.start.assign("year",a)}else{let a=XE.findYearClosestToRef(e.refDate,s,i);n.start.imply("year",a)}if(t[vg]){let a=iu.parseOrdinalNumberPattern(t[vg]);n.end=n.start.clone(),n.end.assign("day",a)}return n}};Vc.default=Hc});var Mg=E(Kc=>{"use strict";Object.defineProperty(Kc,"__esModule",{value:!0});var rS=lt(),xg=ct(),su=ct(),Rg=ct(),nS=Re(),iS=H(),sS=new RegExp(`(${nS.matchAnyPattern(xg.MONTH_DICTIONARY)})(?:-|/|\\s*,?\\s*)(${su.ORDINAL_NUMBER_PATTERN})(?!\\s*(?:am|pm))\\s*(?:(?:to|\\-)\\s*(${su.ORDINAL_NUMBER_PATTERN})\\s*)?(?:(?:-|/|\\s*,?\\s*)(${Rg.YEAR_PATTERN}))?(?=\\W|$)(?!\\:\\d)`,"i"),aS=1,oS=2,Og=3,Dg=4,zc=class extends iS.AbstractParserWithWordBoundaryChecking{innerPattern(){return sS}innerExtract(e,t){let n=xg.MONTH_DICTIONARY[t[aS].toLowerCase()],i=su.parseOrdinalNumberPattern(t[oS]);if(i>31)return null;let s=e.createParsingComponents({day:i,month:n});if(t[Dg]){let u=Rg.parseYear(t[Dg]);s.assign("year",u)}else{let u=rS.findYearClosestToRef(e.refDate,i,n);s.imply("year",u)}if(!t[Og])return s;let a=su.parseOrdinalNumberPattern(t[Og]),o=e.createParsingResult(t.index,t[0]);return o.start=s,o.end=s.clone(),o.end.assign("day",a),o}};Kc.default=zc});var Pg=E(Zc=>{"use strict";Object.defineProperty(Zc,"__esModule",{value:!0});var Qc=ct(),uS=lt(),lS=Re(),Ag=ct(),cS=H(),dS=new RegExp(`((?:in)\\s*)?(${lS.matchAnyPattern(Qc.MONTH_DICTIONARY)})\\s*(?:[,-]?\\s*(${Ag.YEAR_PATTERN})?)?(?=[^\\s\\w]|\\s+[^0-9]|\\s+$|$)`,"i"),fS=1,pS=2,Cg=3,Xc=class extends cS.AbstractParserWithWordBoundaryChecking{innerPattern(){return dS}innerExtract(e,t){let n=t[pS].toLowerCase();if(t[0].length<=3&&!Qc.FULL_MONTH_NAME_DICTIONARY[n])return null;let i=e.createParsingResult(t.index+(t[fS]||"").length,t.index+t[0].length);i.start.imply("day",1);let s=Qc.MONTH_DICTIONARY[n];if(i.start.assign("month",s),t[Cg]){let a=Ag.parseYear(t[Cg]);i.start.assign("year",a)}else{let a=uS.findYearClosestToRef(e.refDate,1,s);i.start.imply("year",a)}return i}};Zc.default=Xc});var Fg=E(ed=>{"use strict";Object.defineProperty(ed,"__esModule",{value:!0});var Ig=ct(),mS=Re(),hS=H(),gS=new RegExp(`([0-9]{4})[\\.\\/\\s](?:(${mS.matchAnyPattern(Ig.MONTH_DICTIONARY)})|([0-9]{1,2}))[\\.\\/\\s]([0-9]{1,2})(?=\\W|$)`,"i"),yS=1,TS=2,Ng=3,bS=4,Jc=class extends hS.AbstractParserWithWordBoundaryChecking{innerPattern(){return gS}innerExtract(e,t){let n=t[Ng]?parseInt(t[Ng]):Ig.MONTH_DICTIONARY[t[TS].toLowerCase()];if(n<1||n>12)return null;let i=parseInt(t[yS]);return{day:parseInt(t[bS]),month:n,year:i}}};ed.default=Jc});var Lg=E(rd=>{"use strict";Object.defineProperty(rd,"__esModule",{value:!0});var _S=H(),vS=new RegExp("([0-9]|0[1-9]|1[012])/([0-9]{4})","i"),wS=1,kS=2,td=class extends _S.AbstractParserWithWordBoundaryChecking{innerPattern(){return vS}innerExtract(e,t){let n=parseInt(t[kS]),i=parseInt(t[wS]);return e.createParsingComponents().imply("day",1).assign("month",i).assign("year",n)}};rd.default=td});var gi=E(uu=>{"use strict";Object.defineProperty(uu,"__esModule",{value:!0});uu.AbstractTimeExpressionParser=void 0;var Ot=Ke();function ES(r,e,t,n){return new RegExp(`${r}${e}(\\d{1,4})(?:(?:\\.|:|\uFF1A)(\\d{1,2})(?:(?::|\uFF1A)(\\d{2})(?:\\.(\\d{1,6}))?)?)?(?:\\s*(a\\.m\\.|p\\.m\\.|am?|pm?))?${t}`,n)}function SS(r,e){return new RegExp(`^(${r})(\\d{1,4})(?:(?:\\.|\\:|\\\uFF1A)(\\d{1,2})(?:(?:\\.|\\:|\\\uFF1A)(\\d{1,2})(?:\\.(\\d{1,6}))?)?)?(?:\\s*(a\\.m\\.|p\\.m\\.|am?|pm?))?${e}`,"i")}var Ug=2,Ji=3,au=4,ou=5,ga=6,nd=class{constructor(e=!1){this.cachedPrimaryPrefix=null,this.cachedPrimarySuffix=null,this.cachedPrimaryTimePattern=null,this.cachedFollowingPhase=null,this.cachedFollowingSuffix=null,this.cachedFollowingTimePatten=null,this.strictMode=e}patternFlags(){return"i"}primaryPatternLeftBoundary(){return"(^|\\s|T|\\b)"}primarySuffix(){return"(?=\\W|$)"}followingSuffix(){return"(?=\\W|$)"}pattern(e){return this.getPrimaryTimePatternThroughCache()}extract(e,t){let n=this.extractPrimaryTimeComponents(e,t);if(!n)return t.index+=t[0].length,null;let i=t.index+t[1].length,s=t[0].substring(t[1].length),a=e.createParsingResult(i,s,n);t.index+=t[0].length;let o=e.text.substring(t.index),l=this.getFollowingTimePatternThroughCache().exec(o);return s.match(/^\d{3,4}/)&&l&&l[0].match(/^\s*([+-])\s*\d{2,4}$/)?null:!l||l[0].match(/^\s*([+-])\s*\d{3,4}$/)?this.checkAndReturnWithoutFollowingPattern(a):(a.end=this.extractFollowingTimeComponents(e,l,a),a.end&&(a.text+=l[0]),this.checkAndReturnWithFollowingPattern(a))}extractPrimaryTimeComponents(e,t,n=!1){let i=e.createParsingComponents(),s=0,a=null,o=parseInt(t[Ug]);if(o>100){if(this.strictMode||t[Ji]!=null)return null;s=o%100,o=Math.floor(o/100)}if(o>24)return null;if(t[Ji]!=null){if(t[Ji].length==1&&!t[ga])return null;s=parseInt(t[Ji])}if(s>=60)return null;if(o>12&&(a=Ot.Meridiem.PM),t[ga]!=null){if(o>12)return null;let u=t[ga][0].toLowerCase();u=="a"&&(a=Ot.Meridiem.AM,o==12&&(o=0)),u=="p"&&(a=Ot.Meridiem.PM,o!=12&&(o+=12))}if(i.assign("hour",o),i.assign("minute",s),a!==null?i.assign("meridiem",a):o<12?i.imply("meridiem",Ot.Meridiem.AM):i.imply("meridiem",Ot.Meridiem.PM),t[ou]!=null){let u=parseInt(t[ou].substring(0,3));if(u>=1e3)return null;i.assign("millisecond",u)}if(t[au]!=null){let u=parseInt(t[au]);if(u>=60)return null;i.assign("second",u)}return i}extractFollowingTimeComponents(e,t,n){let i=e.createParsingComponents();if(t[ou]!=null){let u=parseInt(t[ou].substring(0,3));if(u>=1e3)return null;i.assign("millisecond",u)}if(t[au]!=null){let u=parseInt(t[au]);if(u>=60)return null;i.assign("second",u)}let s=parseInt(t[Ug]),a=0,o=-1;if(t[Ji]!=null?a=parseInt(t[Ji]):s>100&&(a=s%100,s=Math.floor(s/100)),a>=60||s>24)return null;if(s>=12&&(o=Ot.Meridiem.PM),t[ga]!=null){if(s>12)return null;let u=t[ga][0].toLowerCase();u=="a"&&(o=Ot.Meridiem.AM,s==12&&(s=0,i.isCertain("day")||i.imply("day",i.get("day")+1))),u=="p"&&(o=Ot.Meridiem.PM,s!=12&&(s+=12)),n.start.isCertain("meridiem")||(o==Ot.Meridiem.AM?(n.start.imply("meridiem",Ot.Meridiem.AM),n.start.get("hour")==12&&n.start.assign("hour",0)):(n.start.imply("meridiem",Ot.Meridiem.PM),n.start.get("hour")!=12&&n.start.assign("hour",n.start.get("hour")+12)))}return i.assign("hour",s),i.assign("minute",a),o>=0?i.assign("meridiem",o):n.start.isCertain("meridiem")&&n.start.get("hour")>12?n.start.get("hour")-12>s?i.imply("meridiem",Ot.Meridiem.AM):s<=12&&(i.assign("hour",s+12),i.assign("meridiem",Ot.Meridiem.PM)):s>12?i.imply("meridiem",Ot.Meridiem.PM):s<=12&&i.imply("meridiem",Ot.Meridiem.AM),i.date().getTime()<n.start.date().getTime()&&i.imply("day",i.get("day")+1),i}checkAndReturnWithoutFollowingPattern(e){if(e.text.match(/^\d$/)||e.text.match(/^\d\d\d+$/)||e.text.match(/\d[apAP]$/))return null;let t=e.text.match(/[^\d:.](\d[\d.]+)$/);if(t){let n=t[1];if(this.strictMode||n.includes(".")&&!n.match(/\d(\.\d{2})+$/)||parseInt(n)>24)return null}return e}checkAndReturnWithFollowingPattern(e){if(e.text.match(/^\d+-\d+$/))return null;let t=e.text.match(/[^\d:.](\d[\d.]+)\s*-\s*(\d[\d.]+)$/);if(t){if(this.strictMode)return null;let n=t[1],i=t[2];if(i.includes(".")&&!i.match(/\d(\.\d{2})+$/))return null;let s=parseInt(i),a=parseInt(n);if(s>24||a>24)return null}return e}getPrimaryTimePatternThroughCache(){let e=this.primaryPrefix(),t=this.primarySuffix();return this.cachedPrimaryPrefix===e&&this.cachedPrimarySuffix===t?this.cachedPrimaryTimePattern:(this.cachedPrimaryTimePattern=ES(this.primaryPatternLeftBoundary(),e,t,this.patternFlags()),this.cachedPrimaryPrefix=e,this.cachedPrimarySuffix=t,this.cachedPrimaryTimePattern)}getFollowingTimePatternThroughCache(){let e=this.followingPhase(),t=this.followingSuffix();return this.cachedFollowingPhase===e&&this.cachedFollowingSuffix===t?this.cachedFollowingTimePatten:(this.cachedFollowingTimePatten=SS(e,t),this.cachedFollowingPhase=e,this.cachedFollowingSuffix=t,this.cachedFollowingTimePatten)}};uu.AbstractTimeExpressionParser=nd});var Wg=E(sd=>{"use strict";Object.defineProperty(sd,"__esModule",{value:!0});var lu=Ke(),OS=gi(),id=class extends OS.AbstractTimeExpressionParser{constructor(e){super(e)}followingPhase(){return"\\s*(?:\\-|\\\u2013|\\~|\\\u301C|to|\\?)\\s*"}primaryPrefix(){return"(?:(?:at|from)\\s*)??"}primarySuffix(){return"(?:\\s*(?:o\\W*clock|at\\s*night|in\\s*the\\s*(?:morning|afternoon)))?(?!/)(?=\\W|$)"}extractPrimaryTimeComponents(e,t){let n=super.extractPrimaryTimeComponents(e,t);if(n){if(t[0].endsWith("night")){let i=n.get("hour");i>=6&&i<12?(n.assign("hour",n.get("hour")+12),n.assign("meridiem",lu.Meridiem.PM)):i<6&&n.assign("meridiem",lu.Meridiem.AM)}if(t[0].endsWith("afternoon")){n.assign("meridiem",lu.Meridiem.PM);let i=n.get("hour");i>=0&&i<=6&&n.assign("hour",n.get("hour")+12)}t[0].endsWith("morning")&&(n.assign("meridiem",lu.Meridiem.AM),n.get("hour")<12&&n.assign("hour",n.get("hour")))}return n}};sd.default=id});var sr=E(es=>{"use strict";Object.defineProperty(es,"__esModule",{value:!0});es.addImpliedTimeUnits=es.reverseTimeUnits=void 0;function DS(r){let e={};for(let t in r)e[t]=-r[t];return e}es.reverseTimeUnits=DS;function xS(r,e){let t=r.clone(),n=r.dayjs();for(let i in e)n=n.add(e[i],i);return("day"in e||"d"in e||"week"in e||"month"in e||"year"in e)&&(t.imply("day",n.date()),t.imply("month",n.month()+1),t.imply("year",n.year())),("second"in e||"minute"in e||"hour"in e)&&(t.imply("second",n.second()),t.imply("minute",n.minute()),t.imply("hour",n.hour())),t}es.addImpliedTimeUnits=xS});var qg=E(ud=>{"use strict";Object.defineProperty(ud,"__esModule",{value:!0});var od=ct(),RS=We(),MS=H(),CS=sr(),AS=new RegExp(`(${od.TIME_UNITS_PATTERN})\\s{0,5}(?:ago|before|earlier)(?=(?:\\W|$))`,"i"),PS=new RegExp(`(${od.TIME_UNITS_PATTERN})\\s{0,5}ago(?=(?:\\W|$))`,"i"),ad=class extends MS.AbstractParserWithWordBoundaryChecking{constructor(e){super(),this.strictMode=e}innerPattern(){return this.strictMode?PS:AS}innerExtract(e,t){let n=od.parseTimeUnits(t[1]),i=CS.reverseTimeUnits(n);return RS.ParsingComponents.createRelativeFromReference(e.reference,i)}};ud.default=ad});var $g=E(dd=>{"use strict";Object.defineProperty(dd,"__esModule",{value:!0});var cd=ct(),NS=We(),IS=H(),FS=new RegExp(`(${cd.TIME_UNITS_PATTERN})\\s{0,5}(?:later|after|from now|henceforth|forward|out)(?=(?:\\W|$))`,"i"),LS=new RegExp("("+cd.TIME_UNITS_PATTERN+")(later|from now)(?=(?:\\W|$))","i"),US=1,ld=class extends IS.AbstractParserWithWordBoundaryChecking{constructor(e){super(),this.strictMode=e}innerPattern(){return this.strictMode?LS:FS}innerExtract(e,t){let n=cd.parseTimeUnits(t[US]);return NS.ParsingComponents.createRelativeFromReference(e.reference,n)}};dd.default=ld});var rs=E(ts=>{"use strict";Object.defineProperty(ts,"__esModule",{value:!0});ts.MergingRefiner=ts.Filter=void 0;var fd=class{refine(e,t){return t.filter(n=>this.isValid(e,n))}};ts.Filter=fd;var pd=class{refine(e,t){if(t.length<2)return t;let n=[],i=t[0],s=null;for(let a=1;a<t.length;a++){s=t[a];let o=e.text.substring(i.index+i.text.length,s.index);if(!this.shouldMergeResults(o,i,s,e))n.push(i),i=s;else{let u=i,l=s,c=this.mergeResults(o,u,l,e);e.debug(()=>{console.log(`${this.constructor.name} merged ${u} and ${l} into ${c}`)}),i=c}}return i!=null&&n.push(i),n}};ts.MergingRefiner=pd});var Kr=E(hd=>{"use strict";Object.defineProperty(hd,"__esModule",{value:!0});var WS=rs(),md=class extends WS.MergingRefiner{shouldMergeResults(e,t,n){return!t.end&&!n.end&&e.match(this.patternBetween())!=null}mergeResults(e,t,n){if(!t.start.isOnlyWeekdayComponent()&&!n.start.isOnlyWeekdayComponent()&&(n.start.getCertainComponents().forEach(s=>{t.start.isCertain(s)||t.start.assign(s,n.start.get(s))}),t.start.getCertainComponents().forEach(s=>{n.start.isCertain(s)||n.start.assign(s,t.start.get(s))})),t.start.date().getTime()>n.start.date().getTime()){let s=t.start.dayjs(),a=n.start.dayjs();t.start.isOnlyWeekdayComponent()&&s.add(-7,"days").isBefore(a)?(s=s.add(-7,"days"),t.start.imply("day",s.date()),t.start.imply("month",s.month()+1),t.start.imply("year",s.year())):n.start.isOnlyWeekdayComponent()&&a.add(7,"days").isAfter(s)?(a=a.add(7,"days"),n.start.imply("day",a.date()),n.start.imply("month",a.month()+1),n.start.imply("year",a.year())):[n,t]=[t,n]}let i=t.clone();return i.start=t.start,i.end=n.start,i.index=Math.min(t.index,n.index),t.index<n.index?i.text=t.text+e+n.text:i.text=n.text+e+t.text,i}};hd.default=md});var jg=E(ya=>{"use strict";var qS=ya&&ya.__importDefault||function(r){return r&&r.__esModule?r:{default:r}};Object.defineProperty(ya,"__esModule",{value:!0});var $S=qS(Kr()),gd=class extends $S.default{patternBetween(){return/^\s*(to|-)\s*$/i}};ya.default=gd});var Gg=E(ns=>{"use strict";Object.defineProperty(ns,"__esModule",{value:!0});ns.mergeDateTimeComponent=ns.mergeDateTimeResult=void 0;var jS=Ke();function GS(r,e){let t=r.clone(),n=r.start,i=e.start;if(t.start=yd(n,i),r.end!=null||e.end!=null){let s=r.end==null?r.start:r.end,a=e.end==null?e.start:e.end,o=yd(s,a);r.end==null&&o.date().getTime()<t.start.date().getTime()&&(o.isCertain("day")?o.assign("day",o.get("day")+1):o.imply("day",o.get("day")+1)),t.end=o}return t}ns.mergeDateTimeResult=GS;function yd(r,e){let t=r.clone();return e.isCertain("hour")?(t.assign("hour",e.get("hour")),t.assign("minute",e.get("minute")),e.isCertain("second")?(t.assign("second",e.get("second")),e.isCertain("millisecond")?t.assign("millisecond",e.get("millisecond")):t.imply("millisecond",e.get("millisecond"))):(t.imply("second",e.get("second")),t.imply("millisecond",e.get("millisecond")))):(t.imply("hour",e.get("hour")),t.imply("minute",e.get("minute")),t.imply("second",e.get("second")),t.imply("millisecond",e.get("millisecond"))),e.isCertain("timezoneOffset")&&t.assign("timezoneOffset",e.get("timezoneOffset")),e.isCertain("meridiem")?t.assign("meridiem",e.get("meridiem")):e.get("meridiem")!=null&&t.get("meridiem")==null&&t.imply("meridiem",e.get("meridiem")),t.get("meridiem")==jS.Meridiem.PM&&t.get("hour")<12&&(e.isCertain("hour")?t.assign("hour",t.get("hour")+12):t.imply("hour",t.get("hour")+12)),t}ns.mergeDateTimeComponent=yd});var hn=E(bd=>{"use strict";Object.defineProperty(bd,"__esModule",{value:!0});var YS=rs(),Yg=Gg(),Td=class extends YS.MergingRefiner{shouldMergeResults(e,t,n){return(t.start.isOnlyDate()&&n.start.isOnlyTime()||n.start.isOnlyDate()&&t.start.isOnlyTime())&&e.match(this.patternBetween())!=null}mergeResults(e,t,n){let i=t.start.isOnlyDate()?Yg.mergeDateTimeResult(t,n):Yg.mergeDateTimeResult(n,t);return i.index=t.index,i.text=t.text+e+n.text,i}};bd.default=Td});var Bg=E(Ta=>{"use strict";var BS=Ta&&Ta.__importDefault||function(r){return r&&r.__esModule?r:{default:r}};Object.defineProperty(Ta,"__esModule",{value:!0});var HS=BS(hn()),_d=class extends HS.default{patternBetween(){return new RegExp("^\\s*(T|at|after|before|on|of|,|-)?\\s*$")}};Ta.default=_d});var Hg=E(wd=>{"use strict";Object.defineProperty(wd,"__esModule",{value:!0});var VS=new RegExp("^\\s*,?\\s*\\(?([A-Z]{2,4})\\)?(?=\\W|$)","i"),zS={ACDT:630,ACST:570,ADT:-180,AEDT:660,AEST:600,AFT:270,AKDT:-480,AKST:-540,ALMT:360,AMST:-180,AMT:-240,ANAST:720,ANAT:720,AQTT:300,ART:-180,AST:-240,AWDT:540,AWST:480,AZOST:0,AZOT:-60,AZST:300,AZT:240,BNT:480,BOT:-240,BRST:-120,BRT:-180,BST:60,BTT:360,CAST:480,CAT:120,CCT:390,CDT:-300,CEST:120,CET:60,CHADT:825,CHAST:765,CKT:-600,CLST:-180,CLT:-240,COT:-300,CST:-360,CVT:-60,CXT:420,ChST:600,DAVT:420,EASST:-300,EAST:-360,EAT:180,ECT:-300,EDT:-240,EEST:180,EET:120,EGST:0,EGT:-60,EST:-300,ET:-300,FJST:780,FJT:720,FKST:-180,FKT:-240,FNT:-120,GALT:-360,GAMT:-540,GET:240,GFT:-180,GILT:720,GMT:0,GST:240,GYT:-240,HAA:-180,HAC:-300,HADT:-540,HAE:-240,HAP:-420,HAR:-360,HAST:-600,HAT:-90,HAY:-480,HKT:480,HLV:-210,HNA:-240,HNC:-360,HNE:-300,HNP:-480,HNR:-420,HNT:-150,HNY:-540,HOVT:420,ICT:420,IDT:180,IOT:360,IRDT:270,IRKST:540,IRKT:540,IRST:210,IST:330,JST:540,KGT:360,KRAST:480,KRAT:480,KST:540,KUYT:240,LHDT:660,LHST:630,LINT:840,MAGST:720,MAGT:720,MART:-510,MAWT:300,MDT:-360,MESZ:120,MEZ:60,MHT:720,MMT:390,MSD:240,MSK:240,MST:-420,MUT:240,MVT:300,MYT:480,NCT:660,NDT:-90,NFT:690,NOVST:420,NOVT:360,NPT:345,NST:-150,NUT:-660,NZDT:780,NZST:720,OMSST:420,OMST:420,PDT:-420,PET:-300,PETST:720,PETT:720,PGT:600,PHOT:780,PHT:480,PKT:300,PMDT:-120,PMST:-180,PONT:660,PST:-480,PT:-480,PWT:540,PYST:-180,PYT:-240,RET:240,SAMT:240,SAST:120,SBT:660,SCT:240,SGT:480,SRT:-180,SST:-660,TAHT:-600,TFT:300,TJT:300,TKT:780,TLT:540,TMT:300,TVT:720,ULAT:480,UTC:0,UYST:-120,UYT:-180,UZT:300,VET:-210,VLAST:660,VLAT:660,VUT:660,WAST:120,WAT:60,WEST:60,WESZ:60,WET:0,WEZ:0,WFT:720,WGST:-120,WGT:-180,WIB:420,WIT:540,WITA:480,WST:780,WT:0,YAKST:600,YAKT:600,YAPT:600,YEKST:360,YEKT:360},vd=class{constructor(e){this.timezone=Object.assign(Object.assign({},zS),e)}refine(e,t){var n;let i=(n=e.option.timezones)!==null&&n!==void 0?n:{};return t.forEach(s=>{var a,o;let u=e.text.substring(s.index+s.text.length),l=VS.exec(u);if(!l)return;let c=l[1].toUpperCase(),d=(o=(a=i[c])!==null&&a!==void 0?a:this.timezone[c])!==null&&o!==void 0?o:null;if(d===null)return;e.debug(()=>{console.log(`Extracting timezone: '${c}' into: ${d} for: ${s.start}`)});let f=s.start.get("timezoneOffset");f!==null&&d!=f&&(s.start.isCertain("timezoneOffset")||c!=l[1])||s.start.isOnlyDate()&&c!=l[1]||(s.text+=l[0],s.start.isCertain("timezoneOffset")||s.start.assign("timezoneOffset",d),s.end!=null&&!s.end.isCertain("timezoneOffset")&&s.end.assign("timezoneOffset",d))}),t}};wd.default=vd});var cu=E(Ed=>{"use strict";Object.defineProperty(Ed,"__esModule",{value:!0});var KS=new RegExp("^\\s*(?:\\(?(?:GMT|UTC)\\s?)?([+-])(\\d{1,2})(?::?(\\d{2}))?\\)?","i"),QS=1,XS=2,ZS=3,kd=class{refine(e,t){return t.forEach(function(n){if(n.start.isCertain("timezoneOffset"))return;let i=e.text.substring(n.index+n.text.length),s=KS.exec(i);if(!s)return;e.debug(()=>{console.log(`Extracting timezone: '${s[0]}' into : ${n}`)});let a=parseInt(s[XS]),o=parseInt(s[ZS]||"0"),u=a*60+o;u>14*60||(s[QS]==="-"&&(u=-u),n.end!=null&&n.end.assign("timezoneOffset",u),n.start.assign("timezoneOffset",u),n.text+=s[0])}),t}};Ed.default=kd});var Vg=E(Od=>{"use strict";Object.defineProperty(Od,"__esModule",{value:!0});var Sd=class{refine(e,t){if(t.length<2)return t;let n=[],i=t[0];for(let s=1;s<t.length;s++){let a=t[s];a.index<i.index+i.text.length?a.text.length>i.text.length&&(i=a):(n.push(i),i=a)}return i!=null&&n.push(i),n}};Od.default=Sd});var zg=E(ba=>{"use strict";var JS=ba&&ba.__importDefault||function(r){return r&&r.__esModule?r:{default:r}};Object.defineProperty(ba,"__esModule",{value:!0});var eO=JS(we()),Dd=class{refine(e,t){return e.option.forwardDate&&t.forEach(function(n){let i=eO.default(e.refDate);if(n.start.isOnlyDayMonthComponent()&&i.isAfter(n.start.dayjs()))for(let s=0;s<3&&i.isAfter(n.start.dayjs());s++)n.start.imply("year",n.start.get("year")+1),e.debug(()=>{console.log(`Forward yearly adjusted for ${n} (${n.start})`)}),n.end&&!n.end.isCertain("year")&&(n.end.imply("year",n.end.get("year")+1),e.debug(()=>{console.log(`Forward yearly adjusted for ${n} (${n.end})`)}));n.start.isOnlyWeekdayComponent()&&i.isAfter(n.start.dayjs())&&(i.day()>=n.start.get("weekday")?i=i.day(n.start.get("weekday")+7):i=i.day(n.start.get("weekday")),n.start.imply("day",i.date()),n.start.imply("month",i.month()+1),n.start.imply("year",i.year()),e.debug(()=>{console.log(`Forward weekly adjusted for ${n} (${n.start})`)}),n.end&&n.end.isOnlyWeekdayComponent()&&(i.day()>n.end.get("weekday")?i=i.day(n.end.get("weekday")+7):i=i.day(n.end.get("weekday")),n.end.imply("day",i.date()),n.end.imply("month",i.month()+1),n.end.imply("year",i.year()),e.debug(()=>{console.log(`Forward weekly adjusted for ${n} (${n.end})`)})))}),t}};ba.default=Dd});var Kg=E(Rd=>{"use strict";Object.defineProperty(Rd,"__esModule",{value:!0});var tO=rs(),xd=class extends tO.Filter{constructor(e){super(),this.strictMode=e}isValid(e,t){return t.text.replace(" ","").match(/^\d*(\.\d*)?$/)?(e.debug(()=>{console.log(`Removing unlikely result '${t.text}'`)}),!1):t.start.isValidDate()?t.end&&!t.end.isValidDate()?(e.debug(()=>{console.log(`Removing invalid result: ${t} (${t.end})`)}),!1):this.strictMode?this.isStrictModeValid(e,t):!0:(e.debug(()=>{console.log(`Removing invalid result: ${t} (${t.start})`)}),!1)}isStrictModeValid(e,t){return t.start.isOnlyWeekdayComponent()?(e.debug(()=>{console.log(`(Strict) Removing weekday only component: ${t} (${t.end})`)}),!1):t.start.isOnlyTime()&&(!t.start.isCertain("hour")||!t.start.isCertain("minute"))?(e.debug(()=>{console.log(`(Strict) Removing uncertain time component: ${t} (${t.end})`)}),!1):!0}};Rd.default=xd});var Ad=E(Cd=>{"use strict";Object.defineProperty(Cd,"__esModule",{value:!0});var rO=H(),nO=new RegExp("([0-9]{4})\\-([0-9]{1,2})\\-([0-9]{1,2})(?:T([0-9]{1,2}):([0-9]{1,2})(?::([0-9]{1,2})(?:\\.(\\d{1,4}))?)?(?:Z|([+-]\\d{2}):?(\\d{2})?)?)?(?=\\W|$)","i"),iO=1,sO=2,aO=3,Qg=4,oO=5,Xg=6,Zg=7,Jg=8,ey=9,Md=class extends rO.AbstractParserWithWordBoundaryChecking{innerPattern(){return nO}innerExtract(e,t){let n={};if(n.year=parseInt(t[iO]),n.month=parseInt(t[sO]),n.day=parseInt(t[aO]),t[Qg]!=null)if(n.hour=parseInt(t[Qg]),n.minute=parseInt(t[oO]),t[Xg]!=null&&(n.second=parseInt(t[Xg])),t[Zg]!=null&&(n.millisecond=parseInt(t[Zg])),t[Jg]==null)n.timezoneOffset=0;else{let i=parseInt(t[Jg]),s=0;t[ey]!=null&&(s=parseInt(t[ey]));let a=i*60;a<0?a-=s:a+=s,n.timezoneOffset=a}return n}};Cd.default=Md});var ty=E(Nd=>{"use strict";Object.defineProperty(Nd,"__esModule",{value:!0});var uO=rs(),Pd=class extends uO.MergingRefiner{mergeResults(e,t,n){let i=n.clone();return i.index=t.index,i.text=t.text+e+i.text,i.start.assign("weekday",t.start.get("weekday")),i.end&&i.end.assign("weekday",t.start.get("weekday")),i}shouldMergeResults(e,t,n){return t.start.isOnlyWeekdayComponent()&&!t.start.isCertain("hour")&&n.start.isCertain("day")&&e.match(/^,?\s*$/)!=null}};Nd.default=Pd});var gn=E(is=>{"use strict";var yi=is&&is.__importDefault||function(r){return r&&r.__esModule?r:{default:r}};Object.defineProperty(is,"__esModule",{value:!0});is.includeCommonConfiguration=void 0;var lO=yi(Hg()),cO=yi(cu()),ry=yi(Vg()),dO=yi(zg()),fO=yi(Kg()),pO=yi(Ad()),mO=yi(ty());function hO(r,e=!1){return r.parsers.unshift(new pO.default),r.refiners.unshift(new mO.default),r.refiners.unshift(new lO.default),r.refiners.unshift(new cO.default),r.refiners.unshift(new ry.default),r.refiners.push(new ry.default),r.refiners.push(new dO.default),r.refiners.push(new fO.default(e)),r}is.includeCommonConfiguration=hO});var Tn=E(Oe=>{"use strict";var gO=Oe&&Oe.__importDefault||function(r){return r&&r.__esModule?r:{default:r}};Object.defineProperty(Oe,"__esModule",{value:!0});Oe.noon=Oe.morning=Oe.midnight=Oe.yesterdayEvening=Oe.evening=Oe.lastNight=Oe.tonight=Oe.theDayAfter=Oe.tomorrow=Oe.theDayBefore=Oe.yesterday=Oe.today=Oe.now=void 0;var Qr=We(),ss=gO(we()),yn=ir(),_a=Ke();function yO(r){let e=ss.default(r.instant),t=new Qr.ParsingComponents(r,{});return yn.assignSimilarDate(t,e),yn.assignSimilarTime(t,e),r.timezoneOffset!==null&&t.assign("timezoneOffset",e.utcOffset()),t}Oe.now=yO;function TO(r){let e=ss.default(r.instant),t=new Qr.ParsingComponents(r,{});return yn.assignSimilarDate(t,e),yn.implySimilarTime(t,e),t}Oe.today=TO;function bO(r){return ny(r,1)}Oe.yesterday=bO;function ny(r,e){return Id(r,-e)}Oe.theDayBefore=ny;function _O(r){return Id(r,1)}Oe.tomorrow=_O;function Id(r,e){let t=ss.default(r.instant),n=new Qr.ParsingComponents(r,{});return t=t.add(e,"day"),yn.assignSimilarDate(n,t),yn.implySimilarTime(n,t),n}Oe.theDayAfter=Id;function vO(r,e=22){let t=ss.default(r.instant),n=new Qr.ParsingComponents(r,{});return n.imply("hour",e),n.imply("meridiem",_a.Meridiem.PM),yn.assignSimilarDate(n,t),n}Oe.tonight=vO;function wO(r,e=0){let t=ss.default(r.instant),n=new Qr.ParsingComponents(r,{});return t.hour()<6&&(t=t.add(-1,"day")),yn.assignSimilarDate(n,t),n.imply("hour",e),n}Oe.lastNight=wO;function kO(r,e=20){let t=new Qr.ParsingComponents(r,{});return t.imply("meridiem",_a.Meridiem.PM),t.imply("hour",e),t}Oe.evening=kO;function EO(r,e=20){let t=ss.default(r.instant),n=new Qr.ParsingComponents(r,{});return t=t.add(-1,"day"),yn.assignSimilarDate(n,t),n.imply("hour",e),n.imply("meridiem",_a.Meridiem.PM),n}Oe.yesterdayEvening=EO;function SO(r){let e=new Qr.ParsingComponents(r,{});return e.imply("hour",0),e.imply("minute",0),e.imply("second",0),e}Oe.midnight=SO;function OO(r,e=6){let t=new Qr.ParsingComponents(r,{});return t.imply("meridiem",_a.Meridiem.AM),t.imply("hour",e),t}Oe.morning=OO;function DO(r){let e=new Qr.ParsingComponents(r,{});return e.imply("meridiem",_a.Meridiem.AM),e.imply("hour",12),e}Oe.noon=DO});var iy=E(Or=>{"use strict";var xO=Or&&Or.__createBinding||(Object.create?function(r,e,t,n){n===void 0&&(n=t),Object.defineProperty(r,n,{enumerable:!0,get:function(){return e[t]}})}:function(r,e,t,n){n===void 0&&(n=t),r[n]=e[t]}),RO=Or&&Or.__setModuleDefault||(Object.create?function(r,e){Object.defineProperty(r,"default",{enumerable:!0,value:e})}:function(r,e){r.default=e}),MO=Or&&Or.__importStar||function(r){if(r&&r.__esModule)return r;var e={};if(r!=null)for(var t in r)t!=="default"&&Object.prototype.hasOwnProperty.call(r,t)&&xO(e,r,t);return RO(e,r),e},CO=Or&&Or.__importDefault||function(r){return r&&r.__esModule?r:{default:r}};Object.defineProperty(Or,"__esModule",{value:!0});var AO=CO(we()),PO=H(),NO=ir(),va=MO(Tn()),IO=/(now|today|tonight|tomorrow|tmr|tmrw|yesterday|last\s*night)(?=\W|$)/i,Fd=class extends PO.AbstractParserWithWordBoundaryChecking{innerPattern(e){return IO}innerExtract(e,t){let n=AO.default(e.refDate),i=t[0].toLowerCase(),s=e.createParsingComponents();switch(i){case"now":return va.now(e.reference);case"today":return va.today(e.reference);case"yesterday":return va.yesterday(e.reference);case"tomorrow":case"tmr":case"tmrw":return va.tomorrow(e.reference);case"tonight":return va.tonight(e.reference);default:i.match(/last\s*night/)&&(n.hour()>6&&(n=n.add(-1,"day")),NO.assignSimilarDate(s,n),s.imply("hour",0));break}return s}};Or.default=Fd});var sy=E(wa=>{"use strict";var FO=wa&&wa.__importDefault||function(r){return r&&r.__esModule?r:{default:r}};Object.defineProperty(wa,"__esModule",{value:!0});var du=Ke(),LO=H(),UO=FO(we()),WO=ir(),qO=/(?:this)?\s{0,3}(morning|afternoon|evening|night|midnight|noon)(?=\W|$)/i,Ld=class extends LO.AbstractParserWithWordBoundaryChecking{innerPattern(){return qO}innerExtract(e,t){let n=UO.default(e.refDate),i=e.createParsingComponents();switch(t[1].toLowerCase()){case"afternoon":i.imply("meridiem",du.Meridiem.PM),i.imply("hour",15);break;case"evening":case"night":i.imply("meridiem",du.Meridiem.PM),i.imply("hour",20);break;case"midnight":WO.assignTheNextDay(i,n),i.imply("hour",0),i.imply("minute",0),i.imply("second",0);break;case"morning":i.imply("meridiem",du.Meridiem.AM),i.imply("hour",6);break;case"noon":i.imply("meridiem",du.Meridiem.AM),i.imply("hour",12);break}return i}};wa.default=Ld});var Ti=E(Bn=>{"use strict";var $O=Bn&&Bn.__importDefault||function(r){return r&&r.__esModule?r:{default:r}};Object.defineProperty(Bn,"__esModule",{value:!0});Bn.toDayJSClosestWeekday=Bn.toDayJSWeekday=void 0;var ay=$O(we());function jO(r,e,t){if(!t)return oy(r,e);let n=ay.default(r);switch(t){case"this":n=n.day(e);break;case"next":n=n.day(e+7);break;case"last":n=n.day(e-7);break}return n}Bn.toDayJSWeekday=jO;function oy(r,e){let t=ay.default(r),n=t.day();return Math.abs(e-7-n)<Math.abs(e-n)?t=t.day(e-7):Math.abs(e+7-n)<Math.abs(e-n)?t=t.day(e+7):t=t.day(e),t}Bn.toDayJSClosestWeekday=oy});var ly=E(Wd=>{"use strict";Object.defineProperty(Wd,"__esModule",{value:!0});var uy=ct(),GO=Re(),YO=H(),BO=Ti(),HO=new RegExp(`(?:(?:\\,|\\(|\\\uFF08)\\s*)?(?:on\\s*?)?(?:(this|last|past|next)\\s*)?(${GO.matchAnyPattern(uy.WEEKDAY_DICTIONARY)})(?:\\s*(?:\\,|\\)|\\\uFF09))?(?:\\s*(this|last|past|next)\\s*week)?(?=\\W|$)`,"i"),VO=1,zO=2,KO=3,Ud=class extends YO.AbstractParserWithWordBoundaryChecking{innerPattern(){return HO}innerExtract(e,t){let n=t[zO].toLowerCase(),i=uy.WEEKDAY_DICTIONARY[n],s=t[VO],a=t[KO],o=s||a;o=o||"",o=o.toLowerCase();let u=null;o=="last"||o=="past"?u="last":o=="next"?u="next":o=="this"&&(u="this");let l=BO.toDayJSWeekday(e.refDate,i,u);return e.createParsingComponents().assign("weekday",i).imply("day",l.date()).imply("month",l.month()+1).imply("year",l.year())}};Wd.default=Ud});var fy=E(ka=>{"use strict";var QO=ka&&ka.__importDefault||function(r){return r&&r.__esModule?r:{default:r}};Object.defineProperty(ka,"__esModule",{value:!0});var dy=ct(),cy=We(),XO=QO(we()),ZO=H(),JO=Re(),eD=new RegExp(`(this|last|past|next|after\\s*this)\\s*(${JO.matchAnyPattern(dy.TIME_UNIT_DICTIONARY)})(?=\\s*)(?=\\W|$)`,"i"),tD=1,rD=2,qd=class extends ZO.AbstractParserWithWordBoundaryChecking{innerPattern(){return eD}innerExtract(e,t){let n=t[tD].toLowerCase(),i=t[rD].toLowerCase(),s=dy.TIME_UNIT_DICTIONARY[i];if(n=="next"||n.startsWith("after")){let u={};return u[s]=1,cy.ParsingComponents.createRelativeFromReference(e.reference,u)}if(n=="last"||n=="past"){let u={};return u[s]=-1,cy.ParsingComponents.createRelativeFromReference(e.reference,u)}let a=e.createParsingComponents(),o=XO.default(e.reference.instant);return i.match(/week/i)?(o=o.add(-o.get("d"),"d"),a.imply("day",o.date()),a.imply("month",o.month()+1),a.imply("year",o.year())):i.match(/month/i)?(o=o.add(-o.date()+1,"d"),a.imply("day",o.date()),a.assign("year",o.year()),a.assign("month",o.month()+1)):i.match(/year/i)&&(o=o.add(-o.date()+1,"d"),o=o.add(-o.month(),"month"),a.imply("day",o.date()),a.imply("month",o.month()+1),a.assign("year",o.year())),a}};ka.default=qd});var Dr=E(us=>{"use strict";Object.defineProperty(us,"__esModule",{value:!0});us.ParsingContext=us.Chrono=void 0;var as=We(),nD=$d(),os=class{constructor(e){e=e||nD.createCasualConfiguration(),this.parsers=[...e.parsers],this.refiners=[...e.refiners]}clone(){return new os({parsers:[...this.parsers],refiners:[...this.refiners]})}parseDate(e,t,n){let i=this.parse(e,t,n);return i.length>0?i[0].start.date():null}parse(e,t,n){let i=new fu(e,t,n),s=[];return this.parsers.forEach(a=>{let o=os.executeParser(i,a);s=s.concat(o)}),s.sort((a,o)=>a.index-o.index),this.refiners.forEach(function(a){s=a.refine(i,s)}),s}static executeParser(e,t){let n=[],i=t.pattern(e),s=e.text,a=e.text,o=i.exec(a);for(;o;){let u=o.index+s.length-a.length;o.index=u;let l=t.extract(e,o);if(!l){a=s.substring(o.index+1),o=i.exec(a);continue}let c=null;l instanceof as.ParsingResult?c=l:l instanceof as.ParsingComponents?(c=e.createParsingResult(o.index,o[0]),c.start=l):c=e.createParsingResult(o.index,o[0],l),e.debug(()=>console.log(`${t.constructor.name} extracted result ${c}`)),n.push(c),a=s.substring(u+c.text.length),o=i.exec(a)}return n}};us.Chrono=os;var fu=class{constructor(e,t,n){this.text=e,this.reference=new as.ReferenceWithTimezone(t),this.option=n!=null?n:{},this.refDate=this.reference.instant}createParsingComponents(e){return e instanceof as.ParsingComponents?e:new as.ParsingComponents(this.reference,e)}createParsingResult(e,t,n,i){let s=typeof t=="string"?t:this.text.substring(e,t),a=n?this.createParsingComponents(n):null,o=i?this.createParsingComponents(i):null;return new as.ParsingResult(this.reference,e,s,a,o)}debug(e){this.option.debug&&(this.option.debug instanceof Function?this.option.debug(e):this.option.debug.debug(e))}};us.ParsingContext=fu});var bi=E(Yd=>{"use strict";Object.defineProperty(Yd,"__esModule",{value:!0});var py=lt(),iD=new RegExp("([^\\d]|^)([0-3]{0,1}[0-9]{1})[\\/\\.\\-]([0-3]{0,1}[0-9]{1})(?:[\\/\\.\\-]([0-9]{4}|[0-9]{2}))?(\\W|$)","i"),pu=1,my=5,hy=2,gy=3,jd=4,Gd=class{constructor(e){this.groupNumberMonth=e?gy:hy,this.groupNumberDay=e?hy:gy}pattern(){return iD}extract(e,t){if(t[pu]=="/"||t[my]=="/"){t.index+=t[0].length;return}let n=t.index+t[pu].length,i=t[0].substr(t[pu].length,t[0].length-t[pu].length-t[my].length);if(i.match(/^\d\.\d$/)||i.match(/^\d\.\d{1,2}\.\d{1,2}\s*$/)||!t[jd]&&t[0].indexOf("/")<0)return;let s=e.createParsingResult(n,i),a=parseInt(t[this.groupNumberMonth]),o=parseInt(t[this.groupNumberDay]);if((a<1||a>12)&&a>12)if(o>=1&&o<=12&&a<=31)[o,a]=[a,o];else return null;if(o<1||o>31)return null;if(s.start.assign("day",o),s.start.assign("month",a),t[jd]){let u=parseInt(t[jd]),l=py.findMostLikelyADYear(u);s.start.assign("year",l)}else{let u=py.findYearClosestToRef(e.refDate,o,a);s.start.imply("year",u)}return s}};Yd.default=Gd});var Ty=E(Hd=>{"use strict";Object.defineProperty(Hd,"__esModule",{value:!0});var yy=ct(),sD=We(),aD=H(),oD=sr(),uD=new RegExp(`(this|last|past|next|after|\\+|-)\\s*(${yy.TIME_UNITS_PATTERN})(?=\\W|$)`,"i"),Bd=class extends aD.AbstractParserWithWordBoundaryChecking{innerPattern(){return uD}innerExtract(e,t){let n=t[1].toLowerCase(),i=yy.parseTimeUnits(t[2]);switch(n){case"last":case"past":case"-":i=oD.reverseTimeUnits(i);break}return sD.ParsingComponents.createRelativeFromReference(e.reference,i)}};Hd.default=Bd});var _y=E(Kd=>{"use strict";Object.defineProperty(Kd,"__esModule",{value:!0});var lD=rs(),Vd=We(),cD=ct(),dD=sr();function by(r){return r.text.match(/\s+(before|from)$/i)!=null}function fD(r){return r.text.match(/\s+(after|since)$/i)!=null}var zd=class extends lD.MergingRefiner{patternBetween(){return/^\s*$/i}shouldMergeResults(e,t,n){return!e.match(this.patternBetween())||!by(t)&&!fD(t)?!1:!!n.start.get("day")&&!!n.start.get("month")&&!!n.start.get("year")}mergeResults(e,t,n){let i=cD.parseTimeUnits(t.text);by(t)&&(i=dD.reverseTimeUnits(i));let s=Vd.ParsingComponents.createRelativeFromReference(new Vd.ReferenceWithTimezone(n.start.date()),i);return new Vd.ParsingResult(n.reference,t.index,`${t.text}${e}${n.text}`,s)}};Kd.default=zd});var $d=E(Ye=>{"use strict";var Ze=Ye&&Ye.__importDefault||function(r){return r&&r.__esModule?r:{default:r}};Object.defineProperty(Ye,"__esModule",{value:!0});Ye.createConfiguration=Ye.createCasualConfiguration=Ye.parseDate=Ye.parse=Ye.GB=Ye.strict=Ye.casual=void 0;var pD=Ze(bg()),mD=Ze(Sg()),hD=Ze(Mg()),gD=Ze(Pg()),yD=Ze(Fg()),TD=Ze(Lg()),bD=Ze(Wg()),_D=Ze(qg()),vD=Ze($g()),wD=Ze(jg()),kD=Ze(Bg()),ED=gn(),SD=Ze(iy()),OD=Ze(sy()),DD=Ze(ly()),xD=Ze(fy()),Qd=Dr(),RD=Ze(bi()),MD=Ze(Ty()),CD=Ze(_y());Ye.casual=new Qd.Chrono(vy(!1));Ye.strict=new Qd.Chrono(mu(!0,!1));Ye.GB=new Qd.Chrono(mu(!1,!0));function AD(r,e,t){return Ye.casual.parse(r,e,t)}Ye.parse=AD;function PD(r,e,t){return Ye.casual.parseDate(r,e,t)}Ye.parseDate=PD;function vy(r=!1){let e=mu(!1,r);return e.parsers.unshift(new SD.default),e.parsers.unshift(new OD.default),e.parsers.unshift(new gD.default),e.parsers.unshift(new xD.default),e.parsers.unshift(new MD.default),e}Ye.createCasualConfiguration=vy;function mu(r=!0,e=!1){return ED.includeCommonConfiguration({parsers:[new RD.default(e),new pD.default,new mD.default,new hD.default,new DD.default,new yD.default,new TD.default,new bD.default(r),new _D.default(r),new vD.default(r)],refiners:[new CD.default,new kD.default,new wD.default]},r)}Ye.createConfiguration=mu});var wy=E(Zd=>{"use strict";Object.defineProperty(Zd,"__esModule",{value:!0});var ND=gi(),Xd=class extends ND.AbstractTimeExpressionParser{primaryPrefix(){return"(?:(?:um|von)\\s*)?"}followingPhase(){return"\\s*(?:\\-|\\\u2013|\\~|\\\u301C|bis)\\s*"}extractPrimaryTimeComponents(e,t){return t[0].match(/^\s*\d{4}\s*$/)?null:super.extractPrimaryTimeComponents(e,t)}};Zd.default=Xd});var Ea=E(Ae=>{"use strict";Object.defineProperty(Ae,"__esModule",{value:!0});Ae.parseTimeUnits=Ae.TIME_UNITS_PATTERN=Ae.parseYear=Ae.YEAR_PATTERN=Ae.parseNumberPattern=Ae.NUMBER_PATTERN=Ae.TIME_UNIT_DICTIONARY=Ae.INTEGER_WORD_DICTIONARY=Ae.MONTH_DICTIONARY=Ae.WEEKDAY_DICTIONARY=void 0;var Jd=Re(),ID=lt();Ae.WEEKDAY_DICTIONARY={sonntag:0,so:0,montag:1,mo:1,dienstag:2,di:2,mittwoch:3,mi:3,donnerstag:4,do:4,freitag:5,fr:5,samstag:6,sa:6};Ae.MONTH_DICTIONARY={januar:1,j\u00E4nner:1,janner:1,jan:1,"jan.":1,februar:2,feber:2,feb:2,"feb.":2,m\u00E4rz:3,maerz:3,m\u00E4r:3,"m\xE4r.":3,mrz:3,"mrz.":3,april:4,apr:4,"apr.":4,mai:5,juni:6,jun:6,"jun.":6,juli:7,jul:7,"jul.":7,august:8,aug:8,"aug.":8,september:9,sep:9,"sep.":9,sept:9,"sept.":9,oktober:10,okt:10,"okt.":10,november:11,nov:11,"nov.":11,dezember:12,dez:12,"dez.":12};Ae.INTEGER_WORD_DICTIONARY={eins:1,eine:1,einem:1,einen:1,einer:1,zwei:2,drei:3,vier:4,f\u00FCnf:5,fuenf:5,sechs:6,sieben:7,acht:8,neun:9,zehn:10,elf:11,zw\u00F6lf:12,zwoelf:12};Ae.TIME_UNIT_DICTIONARY={sek:"second",sekunde:"second",sekunden:"second",min:"minute",minute:"minute",minuten:"minute",h:"hour",std:"hour",stunde:"hour",stunden:"hour",tag:"d",tage:"d",tagen:"d",woche:"week",wochen:"week",monat:"month",monate:"month",monaten:"month",monats:"month",quartal:"quarter",quartals:"quarter",quartale:"quarter",quartalen:"quarter",a:"year",j:"year",jr:"year",jahr:"year",jahre:"year",jahren:"year",jahres:"year"};Ae.NUMBER_PATTERN=`(?:${Jd.matchAnyPattern(Ae.INTEGER_WORD_DICTIONARY)}|[0-9]+|[0-9]+\\.[0-9]+|half(?:\\s*an?)?|an?\\b(?:\\s*few)?|few|several|a?\\s*couple\\s*(?:of)?)`;function Ey(r){let e=r.toLowerCase();return Ae.INTEGER_WORD_DICTIONARY[e]!==void 0?Ae.INTEGER_WORD_DICTIONARY[e]:e==="a"||e==="an"?1:e.match(/few/)?3:e.match(/half/)?.5:e.match(/couple/)?2:e.match(/several/)?7:parseFloat(e)}Ae.parseNumberPattern=Ey;Ae.YEAR_PATTERN="(?:[0-9]{1,4}(?:\\s*[vn]\\.?\\s*(?:C(?:hr)?|(?:u\\.?|d\\.?(?:\\s*g\\.?)?)?\\s*Z)\\.?|\\s*(?:u\\.?|d\\.?(?:\\s*g\\.)?)\\s*Z\\.?)?)";function FD(r){if(/v/i.test(r))return-parseInt(r.replace(/[^0-9]+/gi,""));if(/n/i.test(r))return parseInt(r.replace(/[^0-9]+/gi,""));if(/z/i.test(r))return parseInt(r.replace(/[^0-9]+/gi,""));let e=parseInt(r);return ID.findMostLikelyADYear(e)}Ae.parseYear=FD;var Sy=`(${Ae.NUMBER_PATTERN})\\s{0,5}(${Jd.matchAnyPattern(Ae.TIME_UNIT_DICTIONARY)})\\s{0,5}`,ky=new RegExp(Sy,"i");Ae.TIME_UNITS_PATTERN=Jd.repeatedTimeunitPattern("",Sy);function LD(r){let e={},t=r,n=ky.exec(t);for(;n;)UD(e,n),t=t.substring(n[0].length),n=ky.exec(t);return e}Ae.parseTimeUnits=LD;function UD(r,e){let t=Ey(e[1]),n=Ae.TIME_UNIT_DICTIONARY[e[2].toLowerCase()];r[n]=t}});var Dy=E(tf=>{"use strict";Object.defineProperty(tf,"__esModule",{value:!0});var Oy=Ea(),WD=Re(),qD=H(),$D=Ti(),jD=new RegExp(`(?:(?:\\,|\\(|\\\uFF08)\\s*)?(?:a[mn]\\s*?)?(?:(diese[mn]|letzte[mn]|n(?:\xE4|ae)chste[mn])\\s*)?(${WD.matchAnyPattern(Oy.WEEKDAY_DICTIONARY)})(?:\\s*(?:\\,|\\)|\\\uFF09))?(?:\\s*(diese|letzte|n(?:\xE4|ae)chste)\\s*woche)?(?=\\W|$)`,"i"),GD=1,YD=3,BD=2,ef=class extends qD.AbstractParserWithWordBoundaryChecking{innerPattern(){return jD}innerExtract(e,t){let n=t[BD].toLowerCase(),i=Oy.WEEKDAY_DICTIONARY[n],s=t[GD],a=t[YD],o=s||a;o=o||"",o=o.toLowerCase();let u=null;o.match(/letzte/)?u="last":o.match(/chste/)?u="next":o.match(/diese/)&&(u="this");let l=$D.toDayJSWeekday(e.refDate,i,u);return e.createParsingComponents().assign("weekday",i).imply("day",l.date()).imply("month",l.month()+1).imply("year",l.year())}};tf.default=ef});var Cy=E(rf=>{"use strict";Object.defineProperty(rf,"__esModule",{value:!0});var Hn=Ke(),HD=new RegExp("(^|\\s|T)(?:(?:um|von)\\s*)?(\\d{1,2})(?:h|:)?(?:(\\d{1,2})(?:m|:)?)?(?:(\\d{1,2})(?:s)?)?(?:\\s*Uhr)?(?:\\s*(morgens|vormittags|nachmittags|abends|nachts|am\\s+(?:Morgen|Vormittag|Nachmittag|Abend)|in\\s+der\\s+Nacht))?(?=\\W|$)","i"),VD=new RegExp("^\\s*(\\-|\\\u2013|\\~|\\\u301C|bis(?:\\s+um)?|\\?)\\s*(\\d{1,2})(?:h|:)?(?:(\\d{1,2})(?:m|:)?)?(?:(\\d{1,2})(?:s)?)?(?:\\s*Uhr)?(?:\\s*(morgens|vormittags|nachmittags|abends|nachts|am\\s+(?:Morgen|Vormittag|Nachmittag|Abend)|in\\s+der\\s+Nacht))?(?=\\W|$)","i"),zD=2,xy=3,Ry=4,My=5,ls=class{pattern(e){return HD}extract(e,t){let n=e.createParsingResult(t.index+t[1].length,t[0].substring(t[1].length));if(n.text.match(/^\d{4}$/)||(n.start=ls.extractTimeComponent(n.start.clone(),t),!n.start))return t.index+=t[0].length,null;let i=e.text.substring(t.index+t[0].length),s=VD.exec(i);return s&&(n.end=ls.extractTimeComponent(n.start.clone(),s),n.end&&(n.text+=s[0])),n}static extractTimeComponent(e,t){let n=0,i=0,s=null;if(n=parseInt(t[zD]),t[xy]!=null&&(i=parseInt(t[xy])),i>=60||n>24)return null;if(n>=12&&(s=Hn.Meridiem.PM),t[My]!=null){if(n>12)return null;let a=t[My].toLowerCase();a.match(/morgen|vormittag/)&&(s=Hn.Meridiem.AM,n==12&&(n=0)),a.match(/nachmittag|abend/)&&(s=Hn.Meridiem.PM,n!=12&&(n+=12)),a.match(/nacht/)&&(n==12?(s=Hn.Meridiem.AM,n=0):n<6?s=Hn.Meridiem.AM:(s=Hn.Meridiem.PM,n+=12))}if(e.assign("hour",n),e.assign("minute",i),s!==null?e.assign("meridiem",s):n<12?e.imply("meridiem",Hn.Meridiem.AM):e.imply("meridiem",Hn.Meridiem.PM),t[Ry]!=null){let a=parseInt(t[Ry]);if(a>=60)return null;e.assign("second",a)}return e}};rf.default=ls});var Ay=E(Sa=>{"use strict";var KD=Sa&&Sa.__importDefault||function(r){return r&&r.__esModule?r:{default:r}};Object.defineProperty(Sa,"__esModule",{value:!0});var QD=KD(Kr()),nf=class extends QD.default{patternBetween(){return/^\s*(bis(?:\s*(?:am|zum))?|-)\s*$/i}};Sa.default=nf});var Py=E(Oa=>{"use strict";var XD=Oa&&Oa.__importDefault||function(r){return r&&r.__esModule?r:{default:r}};Object.defineProperty(Oa,"__esModule",{value:!0});var ZD=XD(hn()),sf=class extends ZD.default{patternBetween(){return new RegExp("^\\s*(T|um|am|,|-)?\\s*$")}};Oa.default=sf});var af=E(xa=>{"use strict";var JD=xa&&xa.__importDefault||function(r){return r&&r.__esModule?r:{default:r}};Object.defineProperty(xa,"__esModule",{value:!0});var e0=JD(we()),_i=Ke(),t0=H(),r0=ir(),n0=sr(),Da=class extends t0.AbstractParserWithWordBoundaryChecking{innerPattern(e){return/(diesen)?\s*(morgen|vormittag|mittags?|nachmittag|abend|nacht|mitternacht)(?=\W|$)/i}innerExtract(e,t){let n=e0.default(e.refDate),i=t[2].toLowerCase(),s=e.createParsingComponents();return r0.implySimilarTime(s,n),Da.extractTimeComponents(s,i)}static extractTimeComponents(e,t){switch(t){case"morgen":e.imply("hour",6),e.imply("minute",0),e.imply("second",0),e.imply("meridiem",_i.Meridiem.AM);break;case"vormittag":e.imply("hour",9),e.imply("minute",0),e.imply("second",0),e.imply("meridiem",_i.Meridiem.AM);break;case"mittag":case"mittags":e.imply("hour",12),e.imply("minute",0),e.imply("second",0),e.imply("meridiem",_i.Meridiem.AM);break;case"nachmittag":e.imply("hour",15),e.imply("minute",0),e.imply("second",0),e.imply("meridiem",_i.Meridiem.PM);break;case"abend":e.imply("hour",18),e.imply("minute",0),e.imply("second",0),e.imply("meridiem",_i.Meridiem.PM);break;case"nacht":e.imply("hour",22),e.imply("minute",0),e.imply("second",0),e.imply("meridiem",_i.Meridiem.PM);break;case"mitternacht":e.get("hour")>1&&(e=n0.addImpliedTimeUnits(e,{day:1})),e.imply("hour",0),e.imply("minute",0),e.imply("second",0),e.imply("meridiem",_i.Meridiem.AM);break}return e}};xa.default=Da});var Fy=E(xr=>{"use strict";var i0=xr&&xr.__createBinding||(Object.create?function(r,e,t,n){n===void 0&&(n=t),Object.defineProperty(r,n,{enumerable:!0,get:function(){return e[t]}})}:function(r,e,t,n){n===void 0&&(n=t),r[n]=e[t]}),s0=xr&&xr.__setModuleDefault||(Object.create?function(r,e){Object.defineProperty(r,"default",{enumerable:!0,value:e})}:function(r,e){r.default=e}),a0=xr&&xr.__importStar||function(r){if(r&&r.__esModule)return r;var e={};if(r!=null)for(var t in r)t!=="default"&&Object.prototype.hasOwnProperty.call(r,t)&&i0(e,r,t);return s0(e,r),e},Iy=xr&&xr.__importDefault||function(r){return r&&r.__esModule?r:{default:r}};Object.defineProperty(xr,"__esModule",{value:!0});var o0=Iy(we()),u0=H(),vi=ir(),l0=Iy(af()),Ny=a0(Tn()),c0=new RegExp("(jetzt|heute|morgen|\xFCbermorgen|uebermorgen|gestern|vorgestern|letzte\\s*nacht)(?:\\s*(morgen|vormittag|mittags?|nachmittag|abend|nacht|mitternacht))?(?=\\W|$)","i"),d0=1,f0=2,of=class extends u0.AbstractParserWithWordBoundaryChecking{innerPattern(e){return c0}innerExtract(e,t){let n=o0.default(e.refDate),i=(t[d0]||"").toLowerCase(),s=(t[f0]||"").toLowerCase(),a=e.createParsingComponents();switch(i){case"jetzt":a=Ny.now(e.reference);break;case"heute":a=Ny.today(e.reference);break;case"morgen":vi.assignTheNextDay(a,n);break;case"\xFCbermorgen":case"uebermorgen":n=n.add(1,"day"),vi.assignTheNextDay(a,n);break;case"gestern":n=n.add(-1,"day"),vi.assignSimilarDate(a,n),vi.implySimilarTime(a,n);break;case"vorgestern":n=n.add(-2,"day"),vi.assignSimilarDate(a,n),vi.implySimilarTime(a,n);break;default:i.match(/letzte\s*nacht/)&&(n.hour()>6&&(n=n.add(-1,"day")),vi.assignSimilarDate(a,n),a.imply("hour",0));break}return s&&(a=l0.default.extractTimeComponents(a,s)),a}};xr.default=of});var jy=E(lf=>{"use strict";Object.defineProperty(lf,"__esModule",{value:!0});var p0=lt(),qy=Ea(),$y=Ea(),m0=Re(),h0=H(),g0=new RegExp(`(?:am\\s*?)?(?:den\\s*?)?([0-9]{1,2})\\.(?:\\s*(?:bis(?:\\s*(?:am|zum))?|\\-|\\\u2013|\\s)\\s*([0-9]{1,2})\\.?)?\\s*(${m0.matchAnyPattern(qy.MONTH_DICTIONARY)})(?:(?:-|/|,?\\s*)(${$y.YEAR_PATTERN}(?![^\\s]\\d)))?(?=\\W|$)`,"i"),Ly=1,Uy=2,y0=3,Wy=4,uf=class extends h0.AbstractParserWithWordBoundaryChecking{innerPattern(){return g0}innerExtract(e,t){let n=e.createParsingResult(t.index,t[0]),i=qy.MONTH_DICTIONARY[t[y0].toLowerCase()],s=parseInt(t[Ly]);if(s>31)return t.index=t.index+t[Ly].length,null;if(n.start.assign("month",i),n.start.assign("day",s),t[Wy]){let a=$y.parseYear(t[Wy]);n.start.assign("year",a)}else{let a=p0.findYearClosestToRef(e.refDate,s,i);n.start.imply("year",a)}if(t[Uy]){let a=parseInt(t[Uy]);n.end=n.start.clone(),n.end.assign("day",a)}return n}};lf.default=uf});var Gy=E(df=>{"use strict";Object.defineProperty(df,"__esModule",{value:!0});var hu=Ea(),T0=We(),b0=H(),_0=sr(),v0=Re(),cf=class extends b0.AbstractParserWithWordBoundaryChecking{constructor(){super()}innerPattern(){return new RegExp(`(?:\\s*((?:n\xE4chste|kommende|folgende|letzte|vergangene|vorige|vor(?:her|an)gegangene)(?:s|n|m|r)?|vor|in)\\s*)?(${hu.NUMBER_PATTERN})?(?:\\s*(n\xE4chste|kommende|folgende|letzte|vergangene|vorige|vor(?:her|an)gegangene)(?:s|n|m|r)?)?\\s*(${v0.matchAnyPattern(hu.TIME_UNIT_DICTIONARY)})`,"i")}innerExtract(e,t){let n=t[2]?hu.parseNumberPattern(t[2]):1,i=hu.TIME_UNIT_DICTIONARY[t[4].toLowerCase()],s={};s[i]=n;let a=t[1]||t[3]||"";if(a=a.toLowerCase(),!!a)return(/vor/.test(a)||/letzte/.test(a)||/vergangen/.test(a))&&(s=_0.reverseTimeUnits(s)),T0.ParsingComponents.createRelativeFromReference(e.reference,s)}};df.default=cf});var Hy=E(Je=>{"use strict";var Rr=Je&&Je.__importDefault||function(r){return r&&r.__esModule?r:{default:r}};Object.defineProperty(Je,"__esModule",{value:!0});Je.createConfiguration=Je.createCasualConfiguration=Je.parseDate=Je.parse=Je.strict=Je.casual=void 0;var w0=gn(),Yy=Dr(),k0=Rr(bi()),E0=Rr(Ad()),S0=Rr(wy()),O0=Rr(Dy()),D0=Rr(Cy()),x0=Rr(Ay()),R0=Rr(Py()),M0=Rr(Fy()),C0=Rr(af()),A0=Rr(jy()),P0=Rr(Gy());Je.casual=new Yy.Chrono(By());Je.strict=new Yy.Chrono(ff(!0));function N0(r,e,t){return Je.casual.parse(r,e,t)}Je.parse=N0;function I0(r,e,t){return Je.casual.parseDate(r,e,t)}Je.parseDate=I0;function By(r=!0){let e=ff(!1,r);return e.parsers.unshift(new C0.default),e.parsers.unshift(new M0.default),e.parsers.unshift(new P0.default),e}Je.createCasualConfiguration=By;function ff(r=!0,e=!0){return w0.includeCommonConfiguration({parsers:[new E0.default,new k0.default(e),new S0.default,new D0.default,new A0.default,new O0.default],refiners:[new x0.default,new R0.default]},r)}Je.createConfiguration=ff});var zy=E(Mr=>{"use strict";var F0=Mr&&Mr.__createBinding||(Object.create?function(r,e,t,n){n===void 0&&(n=t),Object.defineProperty(r,n,{enumerable:!0,get:function(){return e[t]}})}:function(r,e,t,n){n===void 0&&(n=t),r[n]=e[t]}),L0=Mr&&Mr.__setModuleDefault||(Object.create?function(r,e){Object.defineProperty(r,"default",{enumerable:!0,value:e})}:function(r,e){r.default=e}),U0=Mr&&Mr.__importStar||function(r){if(r&&r.__esModule)return r;var e={};if(r!=null)for(var t in r)t!=="default"&&Object.prototype.hasOwnProperty.call(r,t)&&F0(e,r,t);return L0(e,r),e},W0=Mr&&Mr.__importDefault||function(r){return r&&r.__esModule?r:{default:r}};Object.defineProperty(Mr,"__esModule",{value:!0});var q0=W0(we()),$0=Ke(),j0=H(),Vy=ir(),gu=U0(Tn()),pf=class extends j0.AbstractParserWithWordBoundaryChecking{innerPattern(e){return/(maintenant|aujourd'hui|demain|hier|cette\s*nuit|la\s*veille)(?=\W|$)/i}innerExtract(e,t){let n=q0.default(e.refDate),i=t[0].toLowerCase(),s=e.createParsingComponents();switch(i){case"maintenant":return gu.now(e.reference);case"aujourd'hui":return gu.today(e.reference);case"hier":return gu.yesterday(e.reference);case"demain":return gu.tomorrow(e.reference);default:i.match(/cette\s*nuit/)?(Vy.assignSimilarDate(s,n),s.imply("hour",22),s.imply("meridiem",$0.Meridiem.PM)):i.match(/la\s*veille/)&&(n=n.add(-1,"day"),Vy.assignSimilarDate(s,n),s.imply("hour",0))}return s}};Mr.default=pf});var Ky=E(hf=>{"use strict";Object.defineProperty(hf,"__esModule",{value:!0});var Ra=Ke(),G0=H(),mf=class extends G0.AbstractParserWithWordBoundaryChecking{innerPattern(e){return/(cet?)?\s*(matin|soir|après-midi|aprem|a midi|à minuit)(?=\W|$)/i}innerExtract(e,t){let n=t[2].toLowerCase(),i=e.createParsingComponents();switch(n){case"apr\xE8s-midi":case"aprem":i.imply("hour",14),i.imply("minute",0),i.imply("meridiem",Ra.Meridiem.PM);break;case"soir":i.imply("hour",18),i.imply("minute",0),i.imply("meridiem",Ra.Meridiem.PM);break;case"matin":i.imply("hour",8),i.imply("minute",0),i.imply("meridiem",Ra.Meridiem.AM);break;case"a midi":i.imply("hour",12),i.imply("minute",0),i.imply("meridiem",Ra.Meridiem.AM);break;case"\xE0 minuit":i.imply("hour",0),i.imply("meridiem",Ra.Meridiem.AM);break}return i}};hf.default=mf});var Qy=E(yf=>{"use strict";Object.defineProperty(yf,"__esModule",{value:!0});var Y0=gi(),gf=class extends Y0.AbstractTimeExpressionParser{primaryPrefix(){return"(?:(?:[\xE0a])\\s*)?"}followingPhase(){return"\\s*(?:\\-|\\\u2013|\\~|\\\u301C|[\xE0a]|\\?)\\s*"}extractPrimaryTimeComponents(e,t){return t[0].match(/^\s*\d{4}\s*$/)?null:super.extractPrimaryTimeComponents(e,t)}};yf.default=gf});var Xy=E(Ma=>{"use strict";var B0=Ma&&Ma.__importDefault||function(r){return r&&r.__esModule?r:{default:r}};Object.defineProperty(Ma,"__esModule",{value:!0});var H0=B0(hn()),Tf=class extends H0.default{patternBetween(){return new RegExp("^\\s*(T|\xE0|a|vers|de|,|-)?\\s*$")}};Ma.default=Tf});var Zy=E(Ca=>{"use strict";var V0=Ca&&Ca.__importDefault||function(r){return r&&r.__esModule?r:{default:r}};Object.defineProperty(Ca,"__esModule",{value:!0});var z0=V0(Kr()),bf=class extends z0.default{patternBetween(){return/^\s*(à|a|-)\s*$/i}};Ca.default=bf});var Vn=E(ke=>{"use strict";Object.defineProperty(ke,"__esModule",{value:!0});ke.parseTimeUnits=ke.TIME_UNITS_PATTERN=ke.parseYear=ke.YEAR_PATTERN=ke.parseOrdinalNumberPattern=ke.ORDINAL_NUMBER_PATTERN=ke.parseNumberPattern=ke.NUMBER_PATTERN=ke.TIME_UNIT_DICTIONARY=ke.INTEGER_WORD_DICTIONARY=ke.MONTH_DICTIONARY=ke.WEEKDAY_DICTIONARY=void 0;var _f=Re();ke.WEEKDAY_DICTIONARY={dimanche:0,dim:0,lundi:1,lun:1,mardi:2,mar:2,mercredi:3,mer:3,jeudi:4,jeu:4,vendredi:5,ven:5,samedi:6,sam:6};ke.MONTH_DICTIONARY={janvier:1,jan:1,"jan.":1,f\u00E9vrier:2,f\u00E9v:2,"f\xE9v.":2,fevrier:2,fev:2,"fev.":2,mars:3,mar:3,"mar.":3,avril:4,avr:4,"avr.":4,mai:5,juin:6,jun:6,juillet:7,juil:7,jul:7,"jul.":7,ao\u00FBt:8,aout:8,septembre:9,sep:9,"sep.":9,sept:9,"sept.":9,octobre:10,oct:10,"oct.":10,novembre:11,nov:11,"nov.":11,d\u00E9cembre:12,decembre:12,dec:12,"dec.":12};ke.INTEGER_WORD_DICTIONARY={un:1,deux:2,trois:3,quatre:4,cinq:5,six:6,sept:7,huit:8,neuf:9,dix:10,onze:11,douze:12,treize:13};ke.TIME_UNIT_DICTIONARY={sec:"second",seconde:"second",secondes:"second",min:"minute",mins:"minute",minute:"minute",minutes:"minute",h:"hour",hr:"hour",hrs:"hour",heure:"hour",heures:"hour",jour:"d",jours:"d",semaine:"week",semaines:"week",mois:"month",trimestre:"quarter",trimestres:"quarter",ans:"year",ann\u00E9e:"year",ann\u00E9es:"year"};ke.NUMBER_PATTERN=`(?:${_f.matchAnyPattern(ke.INTEGER_WORD_DICTIONARY)}|[0-9]+|[0-9]+\\.[0-9]+|une?\\b|quelques?|demi-?)`;function eT(r){let e=r.toLowerCase();return ke.INTEGER_WORD_DICTIONARY[e]!==void 0?ke.INTEGER_WORD_DICTIONARY[e]:e==="une"||e==="un"?1:e.match(/quelques?/)?3:e.match(/demi-?/)?.5:parseFloat(e)}ke.parseNumberPattern=eT;ke.ORDINAL_NUMBER_PATTERN="(?:[0-9]{1,2}(?:er)?)";function K0(r){let e=r.toLowerCase();return e=e.replace(/(?:er)$/i,""),parseInt(e)}ke.parseOrdinalNumberPattern=K0;ke.YEAR_PATTERN="(?:[1-9][0-9]{0,3}\\s*(?:AC|AD|p\\.\\s*C(?:hr?)?\\.\\s*n\\.)|[1-2][0-9]{3}|[5-9][0-9])";function Q0(r){if(/AC/i.test(r))return r=r.replace(/BC/i,""),-parseInt(r);if(/AD/i.test(r)||/C/i.test(r))return r=r.replace(/[^\d]+/i,""),parseInt(r);let e=parseInt(r);return e<100&&(e>50?e=e+1900:e=e+2e3),e}ke.parseYear=Q0;var tT=`(${ke.NUMBER_PATTERN})\\s{0,5}(${_f.matchAnyPattern(ke.TIME_UNIT_DICTIONARY)})\\s{0,5}`,Jy=new RegExp(tT,"i");ke.TIME_UNITS_PATTERN=_f.repeatedTimeunitPattern("",tT);function X0(r){let e={},t=r,n=Jy.exec(t);for(;n;)Z0(e,n),t=t.substring(n[0].length),n=Jy.exec(t);return e}ke.parseTimeUnits=X0;function Z0(r,e){let t=eT(e[1]),n=ke.TIME_UNIT_DICTIONARY[e[2].toLowerCase()];r[n]=t}});var nT=E(wf=>{"use strict";Object.defineProperty(wf,"__esModule",{value:!0});var rT=Vn(),J0=Re(),e1=H(),t1=Ti(),r1=new RegExp(`(?:(?:\\,|\\(|\\\uFF08)\\s*)?(?:(?:ce)\\s*)?(${J0.matchAnyPattern(rT.WEEKDAY_DICTIONARY)})(?:\\s*(?:\\,|\\)|\\\uFF09))?(?:\\s*(dernier|prochain)\\s*)?(?=\\W|\\d|$)`,"i"),n1=1,i1=2,vf=class extends e1.AbstractParserWithWordBoundaryChecking{innerPattern(){return r1}innerExtract(e,t){let n=t[n1].toLowerCase(),i=rT.WEEKDAY_DICTIONARY[n];if(i===void 0)return null;let s=t[i1];s=s||"",s=s.toLowerCase();let a=null;s=="dernier"?a="last":s=="prochain"&&(a="next");let o=t1.toDayJSWeekday(e.refDate,i,a);return e.createParsingComponents().assign("weekday",i).imply("day",o.date()).imply("month",o.month()+1).imply("year",o.year())}};wf.default=vf});var oT=E(kf=>{"use strict";Object.defineProperty(kf,"__esModule",{value:!0});var Aa=Ke(),s1=new RegExp("(^|\\s|T)(?:(?:[\xE0a])\\s*)?(\\d{1,2})(?:h|:)?(?:(\\d{1,2})(?:m|:)?)?(?:(\\d{1,2})(?:s|:)?)?(?:\\s*(A\\.M\\.|P\\.M\\.|AM?|PM?))?(?=\\W|$)","i"),a1=new RegExp("^\\s*(\\-|\\\u2013|\\~|\\\u301C|[\xE0a]|\\?)\\s*(\\d{1,2})(?:h|:)?(?:(\\d{1,2})(?:m|:)?)?(?:(\\d{1,2})(?:s|:)?)?(?:\\s*(A\\.M\\.|P\\.M\\.|AM?|PM?))?(?=\\W|$)","i"),o1=2,iT=3,sT=4,aT=5,cs=class{pattern(e){return s1}extract(e,t){let n=e.createParsingResult(t.index+t[1].length,t[0].substring(t[1].length));if(n.text.match(/^\d{4}$/)||(n.start=cs.extractTimeComponent(n.start.clone(),t),!n.start))return t.index+=t[0].length,null;let i=e.text.substring(t.index+t[0].length),s=a1.exec(i);return s&&(n.end=cs.extractTimeComponent(n.start.clone(),s),n.end&&(n.text+=s[0])),n}static extractTimeComponent(e,t){let n=0,i=0,s=null;if(n=parseInt(t[o1]),t[iT]!=null&&(i=parseInt(t[iT])),i>=60||n>24)return null;if(n>=12&&(s=Aa.Meridiem.PM),t[aT]!=null){if(n>12)return null;let a=t[aT][0].toLowerCase();a=="a"&&(s=Aa.Meridiem.AM,n==12&&(n=0)),a=="p"&&(s=Aa.Meridiem.PM,n!=12&&(n+=12))}if(e.assign("hour",n),e.assign("minute",i),s!==null?e.assign("meridiem",s):n<12?e.imply("meridiem",Aa.Meridiem.AM):e.imply("meridiem",Aa.Meridiem.PM),t[sT]!=null){let a=parseInt(t[sT]);if(a>=60)return null;e.assign("second",a)}return e}};kf.default=cs});var pT=E(Sf=>{"use strict";Object.defineProperty(Sf,"__esModule",{value:!0});var u1=lt(),dT=Vn(),fT=Vn(),yu=Vn(),l1=Re(),c1=H(),d1=new RegExp(`(?:on\\s*?)?(${yu.ORDINAL_NUMBER_PATTERN})(?:\\s*(?:au|\\-|\\\u2013|jusqu'au?|\\s)\\s*(${yu.ORDINAL_NUMBER_PATTERN}))?(?:-|/|\\s*(?:de)?\\s*)(${l1.matchAnyPattern(dT.MONTH_DICTIONARY)})(?:(?:-|/|,?\\s*)(${fT.YEAR_PATTERN}(?![^\\s]\\d)))?(?=\\W|$)`,"i"),uT=1,lT=2,f1=3,cT=4,Ef=class extends c1.AbstractParserWithWordBoundaryChecking{innerPattern(){return d1}innerExtract(e,t){let n=e.createParsingResult(t.index,t[0]),i=dT.MONTH_DICTIONARY[t[f1].toLowerCase()],s=yu.parseOrdinalNumberPattern(t[uT]);if(s>31)return t.index=t.index+t[uT].length,null;if(n.start.assign("month",i),n.start.assign("day",s),t[cT]){let a=fT.parseYear(t[cT]);n.start.assign("year",a)}else{let a=u1.findYearClosestToRef(e.refDate,s,i);n.start.imply("year",a)}if(t[lT]){let a=yu.parseOrdinalNumberPattern(t[lT]);n.end=n.start.clone(),n.end.assign("day",a)}return n}};Sf.default=Ef});var hT=E(Df=>{"use strict";Object.defineProperty(Df,"__esModule",{value:!0});var mT=Vn(),p1=We(),m1=H(),h1=sr(),Of=class extends m1.AbstractParserWithWordBoundaryChecking{constructor(){super()}innerPattern(){return new RegExp(`il y a\\s*(${mT.TIME_UNITS_PATTERN})(?=(?:\\W|$))`,"i")}innerExtract(e,t){let n=mT.parseTimeUnits(t[1]),i=h1.reverseTimeUnits(n);return p1.ParsingComponents.createRelativeFromReference(e.reference,i)}};Df.default=Of});var yT=E(Rf=>{"use strict";Object.defineProperty(Rf,"__esModule",{value:!0});var gT=Vn(),g1=We(),y1=H(),xf=class extends y1.AbstractParserWithWordBoundaryChecking{innerPattern(){return new RegExp(`(?:dans|en|pour|pendant|de)\\s*(${gT.TIME_UNITS_PATTERN})(?=\\W|$)`,"i")}innerExtract(e,t){let n=gT.parseTimeUnits(t[1]);return g1.ParsingComponents.createRelativeFromReference(e.reference,n)}};Rf.default=xf});var TT=E(Cf=>{"use strict";Object.defineProperty(Cf,"__esModule",{value:!0});var Tu=Vn(),T1=We(),b1=H(),_1=sr(),v1=Re(),Mf=class extends b1.AbstractParserWithWordBoundaryChecking{constructor(){super()}innerPattern(){return new RegExp(`(?:les?|la|l'|du|des?)\\s*(${Tu.NUMBER_PATTERN})?(?:\\s*(prochaine?s?|derni[e\xE8]re?s?|pass[\xE9e]e?s?|pr[\xE9e]c[\xE9e]dents?|suivante?s?))?\\s*(${v1.matchAnyPattern(Tu.TIME_UNIT_DICTIONARY)})(?:\\s*(prochaine?s?|derni[e\xE8]re?s?|pass[\xE9e]e?s?|pr[\xE9e]c[\xE9e]dents?|suivante?s?))?`,"i")}innerExtract(e,t){let n=t[1]?Tu.parseNumberPattern(t[1]):1,i=Tu.TIME_UNIT_DICTIONARY[t[3].toLowerCase()],s={};s[i]=n;let a=t[2]||t[4]||"";if(a=a.toLowerCase(),!!a)return(/derni[eè]re?s?/.test(a)||/pass[ée]e?s?/.test(a)||/pr[ée]c[ée]dents?/.test(a))&&(s=_1.reverseTimeUnits(s)),T1.ParsingComponents.createRelativeFromReference(e.reference,s)}};Cf.default=Mf});var vT=E(et=>{"use strict";var ar=et&&et.__importDefault||function(r){return r&&r.__esModule?r:{default:r}};Object.defineProperty(et,"__esModule",{value:!0});et.createConfiguration=et.createCasualConfiguration=et.parseDate=et.parse=et.strict=et.casual=void 0;var w1=gn(),bT=Dr(),k1=ar(zy()),E1=ar(Ky()),S1=ar(bi()),O1=ar(Qy()),D1=ar(Xy()),x1=ar(Zy()),R1=ar(nT()),M1=ar(oT()),C1=ar(pT()),A1=ar(hT()),P1=ar(yT()),N1=ar(TT());et.casual=new bT.Chrono(_T());et.strict=new bT.Chrono(Af(!0));function I1(r,e,t){return et.casual.parse(r,e,t)}et.parse=I1;function F1(r,e,t){return et.casual.parseDate(r,e,t)}et.parseDate=F1;function _T(r=!0){let e=Af(!1,r);return e.parsers.unshift(new k1.default),e.parsers.unshift(new E1.default),e.parsers.unshift(new N1.default),e}et.createCasualConfiguration=_T;function Af(r=!0,e=!0){return w1.includeCommonConfiguration({parsers:[new S1.default(e),new C1.default,new O1.default,new M1.default,new A1.default,new P1.default,new R1.default],refiners:[new D1.default,new x1.default]},r)}et.createConfiguration=Af});var wT=E(bu=>{"use strict";Object.defineProperty(bu,"__esModule",{value:!0});bu.toHankaku=void 0;function L1(r){return String(r).replace(/\u2019/g,"'").replace(/\u201D/g,'"').replace(/\u3000/g," ").replace(/\uFFE5/g,"\xA5").replace(/[\uFF01\uFF03-\uFF06\uFF08\uFF09\uFF0C-\uFF19\uFF1C-\uFF1F\uFF21-\uFF3B\uFF3D\uFF3F\uFF41-\uFF5B\uFF5D\uFF5E]/g,U1)}bu.toHankaku=L1;function U1(r){return String.fromCharCode(r.charCodeAt(0)-65248)}});var ET=E(Pa=>{"use strict";var W1=Pa&&Pa.__importDefault||function(r){return r&&r.__esModule?r:{default:r}};Object.defineProperty(Pa,"__esModule",{value:!0});var Pf=wT(),q1=lt(),$1=W1(we()),j1=/(?:(?:([同今本])|((昭和|平成|令和)?([0-9０-９]{1,4}|元)))年\s*)?([0-9０-９]{1,2})月\s*([0-9０-９]{1,2})日/i,kT=1,G1=2,Nf=3,Y1=4,B1=5,H1=6,If=class{pattern(){return j1}extract(e,t){let n=parseInt(Pf.toHankaku(t[B1])),i=parseInt(Pf.toHankaku(t[H1])),s=e.createParsingComponents({day:i,month:n});if(t[kT]&&t[kT].match("\u540C|\u4ECA|\u672C")){let a=$1.default(e.refDate);s.assign("year",a.year())}if(t[G1]){let a=t[Y1],o=a=="\u5143"?1:parseInt(Pf.toHankaku(a));t[Nf]=="\u4EE4\u548C"?o+=2018:t[Nf]=="\u5E73\u6210"?o+=1988:t[Nf]=="\u662D\u548C"&&(o+=1925),s.assign("year",o)}else{let a=q1.findYearClosestToRef(e.refDate,i,n);s.imply("year",a)}return s}};Pa.default=If});var ST=E(Na=>{"use strict";var V1=Na&&Na.__importDefault||function(r){return r&&r.__esModule?r:{default:r}};Object.defineProperty(Na,"__esModule",{value:!0});var z1=V1(Kr()),Ff=class extends z1.default{patternBetween(){return/^\s*(から|ー|-)\s*$/i}};Na.default=Ff});var DT=E(Cr=>{"use strict";var K1=Cr&&Cr.__createBinding||(Object.create?function(r,e,t,n){n===void 0&&(n=t),Object.defineProperty(r,n,{enumerable:!0,get:function(){return e[t]}})}:function(r,e,t,n){n===void 0&&(n=t),r[n]=e[t]}),Q1=Cr&&Cr.__setModuleDefault||(Object.create?function(r,e){Object.defineProperty(r,"default",{enumerable:!0,value:e})}:function(r,e){r.default=e}),X1=Cr&&Cr.__importStar||function(r){if(r&&r.__esModule)return r;var e={};if(r!=null)for(var t in r)t!=="default"&&Object.prototype.hasOwnProperty.call(r,t)&&K1(e,r,t);return Q1(e,r),e},Z1=Cr&&Cr.__importDefault||function(r){return r&&r.__esModule?r:{default:r}};Object.defineProperty(Cr,"__esModule",{value:!0});var J1=Z1(we()),OT=Ke(),Lf=X1(Tn()),ex=/今日|当日|昨日|明日|今夜|今夕|今晩|今朝/i,Uf=class{pattern(){return ex}extract(e,t){let n=t[0],i=J1.default(e.refDate),s=e.createParsingComponents();switch(n){case"\u6628\u65E5":return Lf.yesterday(e.reference);case"\u660E\u65E5":return Lf.tomorrow(e.reference);case"\u4ECA\u65E5":case"\u5F53\u65E5":return Lf.today(e.reference)}return n=="\u4ECA\u591C"||n=="\u4ECA\u5915"||n=="\u4ECA\u6669"?(s.imply("hour",22),s.assign("meridiem",OT.Meridiem.PM)):n.match("\u4ECA\u671D")&&(s.imply("hour",6),s.assign("meridiem",OT.Meridiem.AM)),s.assign("day",i.date()),s.assign("month",i.month()+1),s.assign("year",i.year()),s}};Cr.default=Uf});var MT=E(tt=>{"use strict";var Wf=tt&&tt.__importDefault||function(r){return r&&r.__esModule?r:{default:r}};Object.defineProperty(tt,"__esModule",{value:!0});tt.createConfiguration=tt.createCasualConfiguration=tt.parseDate=tt.parse=tt.strict=tt.casual=void 0;var tx=Wf(ET()),rx=Wf(ST()),nx=Wf(DT()),xT=Dr();tt.casual=new xT.Chrono(RT());tt.strict=new xT.Chrono(qf());function ix(r,e,t){return tt.casual.parse(r,e,t)}tt.parse=ix;function sx(r,e,t){return tt.casual.parseDate(r,e,t)}tt.parseDate=sx;function RT(){let r=qf();return r.parsers.unshift(new nx.default),r}tt.createCasualConfiguration=RT;function qf(){return{parsers:[new tx.default],refiners:[new rx.default]}}tt.createConfiguration=qf});var _u=E(Xr=>{"use strict";Object.defineProperty(Xr,"__esModule",{value:!0});Xr.parseYear=Xr.YEAR_PATTERN=Xr.MONTH_DICTIONARY=Xr.WEEKDAY_DICTIONARY=void 0;Xr.WEEKDAY_DICTIONARY={domingo:0,dom:0,segunda:1,"segunda-feira":1,seg:1,ter\u00E7a:2,"ter\xE7a-feira":2,ter:2,quarta:3,"quarta-feira":3,qua:3,quinta:4,"quinta-feira":4,qui:4,sexta:5,"sexta-feira":5,sex:5,s\u00E1bado:6,sabado:6,sab:6};Xr.MONTH_DICTIONARY={janeiro:1,jan:1,"jan.":1,fevereiro:2,fev:2,"fev.":2,mar\u00E7o:3,mar:3,"mar.":3,abril:4,abr:4,"abr.":4,maio:5,mai:5,"mai.":5,junho:6,jun:6,"jun.":6,julho:7,jul:7,"jul.":7,agosto:8,ago:8,"ago.":8,setembro:9,set:9,"set.":9,outubro:10,out:10,"out.":10,novembro:11,nov:11,"nov.":11,dezembro:12,dez:12,"dez.":12};Xr.YEAR_PATTERN="[0-9]{1,4}(?![^\\s]\\d)(?:\\s*[a|d]\\.?\\s*c\\.?|\\s*a\\.?\\s*d\\.?)?";function ax(r){if(r.match(/^[0-9]{1,4}$/)){let e=parseInt(r);return e<100&&(e>50?e=e+1900:e=e+2e3),e}return r.match(/a\.?\s*c\.?/i)?(r=r.replace(/a\.?\s*c\.?/i,""),-parseInt(r)):parseInt(r)}Xr.parseYear=ax});var AT=E(jf=>{"use strict";Object.defineProperty(jf,"__esModule",{value:!0});var CT=_u(),ox=Re(),ux=H(),lx=Ti(),cx=new RegExp(`(?:(?:\\,|\\(|\\\uFF08)\\s*)?(?:(este|esta|passado|pr[o\xF3]ximo)\\s*)?(${ox.matchAnyPattern(CT.WEEKDAY_DICTIONARY)})(?:\\s*(?:\\,|\\)|\\\uFF09))?(?:\\s*(este|esta|passado|pr[\xF3o]ximo)\\s*semana)?(?=\\W|\\d|$)`,"i"),dx=1,fx=2,px=3,$f=class extends ux.AbstractParserWithWordBoundaryChecking{innerPattern(){return cx}innerExtract(e,t){let n=t[fx].toLowerCase(),i=CT.WEEKDAY_DICTIONARY[n];if(i===void 0)return null;let s=t[dx],a=t[px],o=s||a||"";o=o.toLowerCase();let u=null;o=="passado"?u="this":o=="pr\xF3ximo"||o=="proximo"?u="next":o=="este"&&(u="this");let l=lx.toDayJSWeekday(e.refDate,i,u);return e.createParsingComponents().assign("weekday",i).imply("day",l.date()).imply("month",l.month()+1).imply("year",l.year())}};jf.default=$f});var PT=E(Yf=>{"use strict";Object.defineProperty(Yf,"__esModule",{value:!0});var mx=gi(),Gf=class extends mx.AbstractTimeExpressionParser{primaryPrefix(){return"(?:(?:ao?|\xE0s?|das|da|de|do)\\s*)?"}followingPhase(){return"\\s*(?:\\-|\\\u2013|\\~|\\\u301C|a(?:o)?|\\?)\\s*"}};Yf.default=Gf});var NT=E(Ia=>{"use strict";var hx=Ia&&Ia.__importDefault||function(r){return r&&r.__esModule?r:{default:r}};Object.defineProperty(Ia,"__esModule",{value:!0});var gx=hx(hn()),Bf=class extends gx.default{patternBetween(){return new RegExp("^\\s*(?:,|\xE0)?\\s*$")}};Ia.default=Bf});var IT=E(Fa=>{"use strict";var yx=Fa&&Fa.__importDefault||function(r){return r&&r.__esModule?r:{default:r}};Object.defineProperty(Fa,"__esModule",{value:!0});var Tx=yx(Kr()),Hf=class extends Tx.default{patternBetween(){return/^\s*(?:-)\s*$/i}};Fa.default=Hf});var $T=E(zf=>{"use strict";Object.defineProperty(zf,"__esModule",{value:!0});var bx=lt(),WT=_u(),qT=_u(),_x=Re(),vx=H(),wx=new RegExp(`([0-9]{1,2})(?:\xBA|\xAA|\xB0)?(?:\\s*(?:desde|de|\\-|\\\u2013|ao?|\\s)\\s*([0-9]{1,2})(?:\xBA|\xAA|\xB0)?)?\\s*(?:de)?\\s*(?:-|/|\\s*(?:de|,)?\\s*)(${_x.matchAnyPattern(WT.MONTH_DICTIONARY)})(?:\\s*(?:de|,)?\\s*(${qT.YEAR_PATTERN}))?(?=\\W|$)`,"i"),FT=1,LT=2,kx=3,UT=4,Vf=class extends vx.AbstractParserWithWordBoundaryChecking{innerPattern(){return wx}innerExtract(e,t){let n=e.createParsingResult(t.index,t[0]),i=WT.MONTH_DICTIONARY[t[kx].toLowerCase()],s=parseInt(t[FT]);if(s>31)return t.index=t.index+t[FT].length,null;if(n.start.assign("month",i),n.start.assign("day",s),t[UT]){let a=qT.parseYear(t[UT]);n.start.assign("year",a)}else{let a=bx.findYearClosestToRef(e.refDate,s,i);n.start.imply("year",a)}if(t[LT]){let a=parseInt(t[LT]);n.end=n.start.clone(),n.end.assign("day",a)}return n}};zf.default=Vf});var jT=E(bn=>{"use strict";var Ex=bn&&bn.__createBinding||(Object.create?function(r,e,t,n){n===void 0&&(n=t),Object.defineProperty(r,n,{enumerable:!0,get:function(){return e[t]}})}:function(r,e,t,n){n===void 0&&(n=t),r[n]=e[t]}),Sx=bn&&bn.__setModuleDefault||(Object.create?function(r,e){Object.defineProperty(r,"default",{enumerable:!0,value:e})}:function(r,e){r.default=e}),Ox=bn&&bn.__importStar||function(r){if(r&&r.__esModule)return r;var e={};if(r!=null)for(var t in r)t!=="default"&&Object.prototype.hasOwnProperty.call(r,t)&&Ex(e,r,t);return Sx(e,r),e};Object.defineProperty(bn,"__esModule",{value:!0});var Dx=H(),vu=Ox(Tn()),Kf=class extends Dx.AbstractParserWithWordBoundaryChecking{innerPattern(e){return/(agora|hoje|amanha|amanhã|ontem)(?=\W|$)/i}innerExtract(e,t){let n=t[0].toLowerCase(),i=e.createParsingComponents();switch(n){case"agora":return vu.now(e.reference);case"hoje":return vu.today(e.reference);case"amanha":case"amanh\xE3":return vu.tomorrow(e.reference);case"ontem":return vu.yesterday(e.reference)}return i}};bn.default=Kf});var GT=E(La=>{"use strict";var xx=La&&La.__importDefault||function(r){return r&&r.__esModule?r:{default:r}};Object.defineProperty(La,"__esModule",{value:!0});var wu=Ke(),Rx=H(),Mx=ir(),Cx=xx(we()),Qf=class extends Rx.AbstractParserWithWordBoundaryChecking{innerPattern(){return/(?:esta\s*)?(manha|manhã|tarde|meia-noite|meio-dia|noite)(?=\W|$)/i}innerExtract(e,t){let n=Cx.default(e.refDate),i=e.createParsingComponents();switch(t[1].toLowerCase()){case"tarde":i.imply("meridiem",wu.Meridiem.PM),i.imply("hour",15);break;case"noite":i.imply("meridiem",wu.Meridiem.PM),i.imply("hour",22);break;case"manha":case"manh\xE3":i.imply("meridiem",wu.Meridiem.AM),i.imply("hour",6);break;case"meia-noite":Mx.assignTheNextDay(i,n),i.imply("hour",0),i.imply("minute",0),i.imply("second",0);break;case"meio-dia":i.imply("meridiem",wu.Meridiem.AM),i.imply("hour",12);break}return i}};La.default=Qf});var HT=E(rt=>{"use strict";var zn=rt&&rt.__importDefault||function(r){return r&&r.__esModule?r:{default:r}};Object.defineProperty(rt,"__esModule",{value:!0});rt.createConfiguration=rt.createCasualConfiguration=rt.parseDate=rt.parse=rt.strict=rt.casual=void 0;var Ax=gn(),YT=Dr(),Px=zn(bi()),Nx=zn(AT()),Ix=zn(PT()),Fx=zn(NT()),Lx=zn(IT()),Ux=zn($T()),Wx=zn(jT()),qx=zn(GT());rt.casual=new YT.Chrono(BT());rt.strict=new YT.Chrono(Xf(!0));function $x(r,e,t){return rt.casual.parse(r,e,t)}rt.parse=$x;function jx(r,e,t){return rt.casual.parseDate(r,e,t)}rt.parseDate=jx;function BT(r=!0){let e=Xf(!1,r);return e.parsers.push(new Wx.default),e.parsers.push(new qx.default),e}rt.createCasualConfiguration=BT;function Xf(r=!0,e=!0){return Ax.includeCommonConfiguration({parsers:[new Px.default(e),new Nx.default,new Ix.default,new Ux.default],refiners:[new Fx.default,new Lx.default]},r)}rt.createConfiguration=Xf});var VT=E(Ua=>{"use strict";var Gx=Ua&&Ua.__importDefault||function(r){return r&&r.__esModule?r:{default:r}};Object.defineProperty(Ua,"__esModule",{value:!0});var Yx=Gx(Kr()),Zf=class extends Yx.default{patternBetween(){return/^\s*(tot|-)\s*$/i}};Ua.default=Zf});var zT=E(Wa=>{"use strict";var Bx=Wa&&Wa.__importDefault||function(r){return r&&r.__esModule?r:{default:r}};Object.defineProperty(Wa,"__esModule",{value:!0});var Hx=Bx(hn()),Jf=class extends Hx.default{patternBetween(){return new RegExp("^\\s*(om|na|voor|in de|,|-)?\\s*$")}};Wa.default=Jf});var KT=E(_n=>{"use strict";var Vx=_n&&_n.__createBinding||(Object.create?function(r,e,t,n){n===void 0&&(n=t),Object.defineProperty(r,n,{enumerable:!0,get:function(){return e[t]}})}:function(r,e,t,n){n===void 0&&(n=t),r[n]=e[t]}),zx=_n&&_n.__setModuleDefault||(Object.create?function(r,e){Object.defineProperty(r,"default",{enumerable:!0,value:e})}:function(r,e){r.default=e}),Kx=_n&&_n.__importStar||function(r){if(r&&r.__esModule)return r;var e={};if(r!=null)for(var t in r)t!=="default"&&Object.prototype.hasOwnProperty.call(r,t)&&Vx(e,r,t);return zx(e,r),e};Object.defineProperty(_n,"__esModule",{value:!0});var Qx=H(),ku=Kx(Tn()),ep=class extends Qx.AbstractParserWithWordBoundaryChecking{innerPattern(e){return/(nu|vandaag|morgen|morgend|gisteren)(?=\W|$)/i}innerExtract(e,t){let n=t[0].toLowerCase(),i=e.createParsingComponents();switch(n){case"nu":return ku.now(e.reference);case"vandaag":return ku.today(e.reference);case"morgen":case"morgend":return ku.tomorrow(e.reference);case"gisteren":return ku.yesterday(e.reference)}return i}};_n.default=ep});var QT=E(qa=>{"use strict";var Xx=qa&&qa.__importDefault||function(r){return r&&r.__esModule?r:{default:r}};Object.defineProperty(qa,"__esModule",{value:!0});var Eu=Ke(),Zx=H(),Jx=Xx(we()),eR=ir(),tR=1,rR=2,tp=class extends Zx.AbstractParserWithWordBoundaryChecking{innerPattern(){return/(deze)?\s*(namiddag|avond|middernacht|ochtend|middag|'s middags|'s avonds|'s ochtends)(?=\W|$)/i}innerExtract(e,t){let n=Jx.default(e.refDate),i=e.createParsingComponents();switch(t[tR]==="deze"&&(i.assign("day",e.refDate.getDate()),i.assign("month",e.refDate.getMonth()+1),i.assign("year",e.refDate.getFullYear())),t[rR].toLowerCase()){case"namiddag":case"'s namiddags":i.imply("meridiem",Eu.Meridiem.PM),i.imply("hour",15);break;case"avond":case"'s avonds'":i.imply("meridiem",Eu.Meridiem.PM),i.imply("hour",20);break;case"middernacht":eR.assignTheNextDay(i,n),i.imply("hour",0),i.imply("minute",0),i.imply("second",0);break;case"ochtend":case"'s ochtends":i.imply("meridiem",Eu.Meridiem.AM),i.imply("hour",6);break;case"middag":case"'s middags":i.imply("meridiem",Eu.Meridiem.AM),i.imply("hour",12);break}return i}};qa.default=tp});var Yt=E(Te=>{"use strict";Object.defineProperty(Te,"__esModule",{value:!0});Te.parseTimeUnits=Te.TIME_UNITS_PATTERN=Te.parseYear=Te.YEAR_PATTERN=Te.parseOrdinalNumberPattern=Te.ORDINAL_NUMBER_PATTERN=Te.parseNumberPattern=Te.NUMBER_PATTERN=Te.TIME_UNIT_DICTIONARY=Te.ORDINAL_WORD_DICTIONARY=Te.INTEGER_WORD_DICTIONARY=Te.MONTH_DICTIONARY=Te.WEEKDAY_DICTIONARY=void 0;var Su=Re(),nR=lt();Te.WEEKDAY_DICTIONARY={zondag:0,zon:0,"zon.":0,zo:0,"zo.":0,maandag:1,ma:1,"ma.":1,dinsdag:2,din:2,"din.":2,di:2,"di.":2,woensdag:3,woe:3,"woe.":3,wo:3,"wo.":3,donderdag:4,dond:4,"dond.":4,do:4,"do.":4,vrijdag:5,vrij:5,"vrij.":5,vr:5,"vr.":5,zaterdag:6,zat:6,"zat.":6,za:6,"za.":6};Te.MONTH_DICTIONARY={januari:1,jan:1,"jan.":1,februari:2,feb:2,"feb.":2,maart:3,mar:3,"mar.":3,april:4,apr:4,"apr.":4,mei:5,juni:6,jun:6,"jun.":6,juli:7,jul:7,"jul.":7,augustus:8,aug:8,"aug.":8,september:9,sep:9,"sep.":9,sept:9,"sept.":9,oktober:10,okt:10,"okt.":10,november:11,nov:11,"nov.":11,december:12,dec:12,"dec.":12};Te.INTEGER_WORD_DICTIONARY={een:1,twee:2,drie:3,vier:4,vijf:5,zes:6,zeven:7,acht:8,negen:9,tien:10,elf:11,twaalf:12};Te.ORDINAL_WORD_DICTIONARY={eerste:1,tweede:2,derde:3,vierde:4,vijfde:5,zesde:6,zevende:7,achtste:8,negende:9,tiende:10,elfde:11,twaalfde:12,dertiende:13,veertiende:14,vijftiende:15,zestiende:16,zeventiende:17,achttiende:18,negentiende:19,twintigste:20,eenentwintigste:21,twee\u00EBntwintigste:22,drieentwintigste:23,vierentwintigste:24,vijfentwintigste:25,zesentwintigste:26,zevenentwintigste:27,achtentwintig:28,negenentwintig:29,dertigste:30,eenendertigste:31};Te.TIME_UNIT_DICTIONARY={sec:"second",second:"second",seconden:"second",min:"minute",mins:"minute",minute:"minute",minuut:"minute",minuten:"minute",minuutje:"minute",h:"hour",hr:"hour",hrs:"hour",uur:"hour",u:"hour",uren:"hour",dag:"d",dagen:"d",week:"week",weken:"week",maand:"month",maanden:"month",jaar:"year",jr:"year",jaren:"year"};Te.NUMBER_PATTERN=`(?:${Su.matchAnyPattern(Te.INTEGER_WORD_DICTIONARY)}|[0-9]+|[0-9]+[\\.,][0-9]+|halve?|half|paar)`;function ZT(r){let e=r.toLowerCase();return Te.INTEGER_WORD_DICTIONARY[e]!==void 0?Te.INTEGER_WORD_DICTIONARY[e]:e==="paar"?2:e==="half"||e.match(/halve?/)?.5:parseFloat(e.replace(",","."))}Te.parseNumberPattern=ZT;Te.ORDINAL_NUMBER_PATTERN=`(?:${Su.matchAnyPattern(Te.ORDINAL_WORD_DICTIONARY)}|[0-9]{1,2}(?:ste|de)?)`;function iR(r){let e=r.toLowerCase();return Te.ORDINAL_WORD_DICTIONARY[e]!==void 0?Te.ORDINAL_WORD_DICTIONARY[e]:(e=e.replace(/(?:ste|de)$/i,""),parseInt(e))}Te.parseOrdinalNumberPattern=iR;Te.YEAR_PATTERN="(?:[1-9][0-9]{0,3}\\s*(?:voor Christus|na Christus)|[1-2][0-9]{3}|[5-9][0-9])";function sR(r){if(/voor Christus/i.test(r))return r=r.replace(/voor Christus/i,""),-parseInt(r);if(/na Christus/i.test(r))return r=r.replace(/na Christus/i,""),parseInt(r);let e=parseInt(r);return nR.findMostLikelyADYear(e)}Te.parseYear=sR;var JT=`(${Te.NUMBER_PATTERN})\\s{0,5}(${Su.matchAnyPattern(Te.TIME_UNIT_DICTIONARY)})\\s{0,5}`,XT=new RegExp(JT,"i");Te.TIME_UNITS_PATTERN=Su.repeatedTimeunitPattern("(?:(?:binnen|in)\\s*)?",JT);function aR(r){let e={},t=r,n=XT.exec(t);for(;n;)oR(e,n),t=t.substring(n[0].length),n=XT.exec(t);return e}Te.parseTimeUnits=aR;function oR(r,e){let t=ZT(e[1]),n=Te.TIME_UNIT_DICTIONARY[e[2].toLowerCase()];r[n]=t}});var tb=E(np=>{"use strict";Object.defineProperty(np,"__esModule",{value:!0});var eb=Yt(),uR=We(),lR=H(),rp=class extends lR.AbstractParserWithWordBoundaryChecking{innerPattern(){return new RegExp("(?:binnen|in|binnen de|voor)\\s*("+eb.TIME_UNITS_PATTERN+")(?=\\W|$)","i")}innerExtract(e,t){let n=eb.parseTimeUnits(t[1]);return uR.ParsingComponents.createRelativeFromReference(e.reference,n)}};np.default=rp});var nb=E(sp=>{"use strict";Object.defineProperty(sp,"__esModule",{value:!0});var rb=Yt(),cR=Re(),dR=H(),fR=Ti(),pR=new RegExp(`(?:(?:\\,|\\(|\\\uFF08)\\s*)?(?:op\\s*?)?(?:(deze|vorige|volgende)\\s*(?:week\\s*)?)?(${cR.matchAnyPattern(rb.WEEKDAY_DICTIONARY)})(?=\\W|$)`,"i"),mR=1,hR=2,gR=3,ip=class extends dR.AbstractParserWithWordBoundaryChecking{innerPattern(){return pR}innerExtract(e,t){let n=t[hR].toLowerCase(),i=rb.WEEKDAY_DICTIONARY[n],s=t[mR],a=t[gR],o=s||a;o=o||"",o=o.toLowerCase();let u=null;o=="vorige"?u="last":o=="volgende"?u="next":o=="deze"&&(u="this");let l=fR.toDayJSWeekday(e.refDate,i,u);return e.createParsingComponents().assign("weekday",i).imply("day",l.date()).imply("month",l.month()+1).imply("year",l.year())}};sp.default=ip});var lb=E(op=>{"use strict";Object.defineProperty(op,"__esModule",{value:!0});var yR=lt(),ob=Yt(),Ou=Yt(),ub=Yt(),TR=Re(),bR=H(),_R=new RegExp(`(?:on\\s*?)?(${Ou.ORDINAL_NUMBER_PATTERN})(?:\\s*(?:tot|\\-|\\\u2013|until|through|till|\\s)\\s*(${Ou.ORDINAL_NUMBER_PATTERN}))?(?:-|/|\\s*(?:of)?\\s*)(`+TR.matchAnyPattern(ob.MONTH_DICTIONARY)+`)(?:(?:-|/|,?\\s*)(${ub.YEAR_PATTERN}(?![^\\s]\\d)))?(?=\\W|$)`,"i"),vR=3,ib=1,sb=2,ab=4,ap=class extends bR.AbstractParserWithWordBoundaryChecking{innerPattern(){return _R}innerExtract(e,t){let n=ob.MONTH_DICTIONARY[t[vR].toLowerCase()],i=Ou.parseOrdinalNumberPattern(t[ib]);if(i>31)return t.index=t.index+t[ib].length,null;let s=e.createParsingComponents({day:i,month:n});if(t[ab]){let u=ub.parseYear(t[ab]);s.assign("year",u)}else{let u=yR.findYearClosestToRef(e.refDate,i,n);s.imply("year",u)}if(!t[sb])return s;let a=Ou.parseOrdinalNumberPattern(t[sb]),o=e.createParsingResult(t.index,t[0]);return o.start=s,o.end=s.clone(),o.end.assign("day",a),o}};op.default=ap});var pb=E(lp=>{"use strict";Object.defineProperty(lp,"__esModule",{value:!0});var db=Yt(),wR=lt(),kR=Re(),fb=Yt(),ER=H(),SR=new RegExp(`(${kR.matchAnyPattern(db.MONTH_DICTIONARY)})\\s*(?:[,-]?\\s*(${fb.YEAR_PATTERN})?)?(?=[^\\s\\w]|\\s+[^0-9]|\\s+$|$)`,"i"),OR=1,cb=2,up=class extends ER.AbstractParserWithWordBoundaryChecking{innerPattern(){return SR}innerExtract(e,t){let n=e.createParsingComponents();n.imply("day",1);let i=t[OR],s=db.MONTH_DICTIONARY[i.toLowerCase()];if(n.assign("month",s),t[cb]){let a=fb.parseYear(t[cb]);n.assign("year",a)}else{let a=wR.findYearClosestToRef(e.refDate,1,s);n.imply("year",a)}return n}};lp.default=up});var mb=E(dp=>{"use strict";Object.defineProperty(dp,"__esModule",{value:!0});var DR=H(),xR=new RegExp("([0-9]|0[1-9]|1[012])/([0-9]{4})","i"),RR=1,MR=2,cp=class extends DR.AbstractParserWithWordBoundaryChecking{innerPattern(){return xR}innerExtract(e,t){let n=parseInt(t[MR]),i=parseInt(t[RR]);return e.createParsingComponents().imply("day",1).assign("month",i).assign("year",n)}};dp.default=cp});var hb=E(pp=>{"use strict";Object.defineProperty(pp,"__esModule",{value:!0});var CR=gi(),fp=class extends CR.AbstractTimeExpressionParser{primaryPrefix(){return"(?:(?:om)\\s*)?"}followingPhase(){return"\\s*(?:\\-|\\\u2013|\\~|\\\u301C|om|\\?)\\s*"}primarySuffix(){return"(?:\\s*(?:uur))?(?!/)(?=\\W|$)"}extractPrimaryTimeComponents(e,t){return t[0].match(/^\s*\d{4}\s*$/)?null:super.extractPrimaryTimeComponents(e,t)}};pp.default=fp});var Tb=E(hp=>{"use strict";Object.defineProperty(hp,"__esModule",{value:!0});var yb=Yt(),AR=Re(),PR=H(),NR=new RegExp(`([0-9]{4})[\\.\\/\\s](?:(${AR.matchAnyPattern(yb.MONTH_DICTIONARY)})|([0-9]{1,2}))[\\.\\/\\s]([0-9]{1,2})(?=\\W|$)`,"i"),IR=1,FR=2,gb=3,LR=4,mp=class extends PR.AbstractParserWithWordBoundaryChecking{innerPattern(){return NR}innerExtract(e,t){let n=t[gb]?parseInt(t[gb]):yb.MONTH_DICTIONARY[t[FR].toLowerCase()];if(n<1||n>12)return null;let i=parseInt(t[IR]);return{day:parseInt(t[LR]),month:n,year:i}}};hp.default=mp});var bb=E($a=>{"use strict";var UR=$a&&$a.__importDefault||function(r){return r&&r.__esModule?r:{default:r}};Object.defineProperty($a,"__esModule",{value:!0});var WR=H(),Du=Ke(),gp=ir(),qR=UR(we()),$R=1,jR=2,yp=class extends WR.AbstractParserWithWordBoundaryChecking{innerPattern(e){return/(gisteren|morgen|van)(ochtend|middag|namiddag|avond|nacht)(?=\W|$)/i}innerExtract(e,t){let n=t[$R].toLowerCase(),i=t[jR].toLowerCase(),s=e.createParsingComponents(),a=qR.default(e.refDate);switch(n){case"gisteren":gp.assignSimilarDate(s,a.add(-1,"day"));break;case"van":gp.assignSimilarDate(s,a);break;case"morgen":gp.assignTheNextDay(s,a);break}switch(i){case"ochtend":s.imply("meridiem",Du.Meridiem.AM),s.imply("hour",6);break;case"middag":s.imply("meridiem",Du.Meridiem.AM),s.imply("hour",12);break;case"namiddag":s.imply("meridiem",Du.Meridiem.PM),s.imply("hour",15);break;case"avond":s.imply("meridiem",Du.Meridiem.PM),s.imply("hour",20);break}return s}};$a.default=yp});var vb=E(bp=>{"use strict";Object.defineProperty(bp,"__esModule",{value:!0});var _b=Yt(),GR=We(),YR=H(),BR=sr(),HR=new RegExp(`(deze|vorige|afgelopen|komende|over|\\+|-)\\s*(${_b.TIME_UNITS_PATTERN})(?=\\W|$)`,"i"),Tp=class extends YR.AbstractParserWithWordBoundaryChecking{innerPattern(){return HR}innerExtract(e,t){let n=t[1].toLowerCase(),i=_b.parseTimeUnits(t[2]);switch(n){case"vorige":case"afgelopen":case"-":i=BR.reverseTimeUnits(i);break}return GR.ParsingComponents.createRelativeFromReference(e.reference,i)}};bp.default=Tp});var Eb=E(ja=>{"use strict";var VR=ja&&ja.__importDefault||function(r){return r&&r.__esModule?r:{default:r}};Object.defineProperty(ja,"__esModule",{value:!0});var kb=Yt(),wb=We(),zR=VR(we()),KR=H(),QR=Re(),XR=new RegExp(`(dit|deze|komende|volgend|volgende|afgelopen|vorige)\\s*(${QR.matchAnyPattern(kb.TIME_UNIT_DICTIONARY)})(?=\\s*)(?=\\W|$)`,"i"),ZR=1,JR=2,_p=class extends KR.AbstractParserWithWordBoundaryChecking{innerPattern(){return XR}innerExtract(e,t){let n=t[ZR].toLowerCase(),i=t[JR].toLowerCase(),s=kb.TIME_UNIT_DICTIONARY[i];if(n=="volgend"||n=="volgende"||n=="komende"){let u={};return u[s]=1,wb.ParsingComponents.createRelativeFromReference(e.reference,u)}if(n=="afgelopen"||n=="vorige"){let u={};return u[s]=-1,wb.ParsingComponents.createRelativeFromReference(e.reference,u)}let a=e.createParsingComponents(),o=zR.default(e.reference.instant);return i.match(/week/i)?(o=o.add(-o.get("d"),"d"),a.imply("day",o.date()),a.imply("month",o.month()+1),a.imply("year",o.year())):i.match(/maand/i)?(o=o.add(-o.date()+1,"d"),a.imply("day",o.date()),a.assign("year",o.year()),a.assign("month",o.month()+1)):i.match(/jaar/i)&&(o=o.add(-o.date()+1,"d"),o=o.add(-o.month(),"month"),a.imply("day",o.date()),a.imply("month",o.month()+1),a.assign("year",o.year())),a}};ja.default=_p});var Sb=E(kp=>{"use strict";Object.defineProperty(kp,"__esModule",{value:!0});var wp=Yt(),eM=We(),tM=H(),rM=sr(),nM=new RegExp("("+wp.TIME_UNITS_PATTERN+")(?:geleden|voor|eerder)(?=(?:\\W|$))","i"),iM=new RegExp("("+wp.TIME_UNITS_PATTERN+")geleden(?=(?:\\W|$))","i"),vp=class extends tM.AbstractParserWithWordBoundaryChecking{constructor(e){super(),this.strictMode=e}innerPattern(){return this.strictMode?iM:nM}innerExtract(e,t){let n=wp.parseTimeUnits(t[1]),i=rM.reverseTimeUnits(n);return eM.ParsingComponents.createRelativeFromReference(e.reference,i)}};kp.default=vp});var Ob=E(Op=>{"use strict";Object.defineProperty(Op,"__esModule",{value:!0});var Sp=Yt(),sM=We(),aM=H(),oM=new RegExp("("+Sp.TIME_UNITS_PATTERN+")(later|na|vanaf nu|voortaan|vooruit|uit)(?=(?:\\W|$))","i"),uM=new RegExp("("+Sp.TIME_UNITS_PATTERN+")(later|vanaf nu)(?=(?:\\W|$))","i"),lM=1,Ep=class extends aM.AbstractParserWithWordBoundaryChecking{constructor(e){super(),this.strictMode=e}innerPattern(){return this.strictMode?uM:oM}innerExtract(e,t){let n=Sp.parseTimeUnits(t[lM]);return sM.ParsingComponents.createRelativeFromReference(e.reference,n)}};Op.default=Ep});var Mb=E(nt=>{"use strict";var dt=nt&&nt.__importDefault||function(r){return r&&r.__esModule?r:{default:r}};Object.defineProperty(nt,"__esModule",{value:!0});nt.createConfiguration=nt.createCasualConfiguration=nt.parseDate=nt.parse=nt.strict=nt.casual=void 0;var cM=gn(),Db=Dr(),dM=dt(VT()),fM=dt(zT()),pM=dt(KT()),mM=dt(QT()),hM=dt(bi()),gM=dt(tb()),yM=dt(nb()),TM=dt(lb()),xb=dt(pb()),bM=dt(mb()),_M=dt(hb()),vM=dt(Tb()),wM=dt(bb()),kM=dt(vb()),EM=dt(Eb()),SM=dt(Sb()),OM=dt(Ob());nt.casual=new Db.Chrono(Rb());nt.strict=new Db.Chrono(Dp(!0));function DM(r,e,t){return nt.casual.parse(r,e,t)}nt.parse=DM;function xM(r,e,t){return nt.casual.parseDate(r,e,t)}nt.parseDate=xM;function Rb(r=!0){let e=Dp(!1,r);return e.parsers.unshift(new pM.default),e.parsers.unshift(new mM.default),e.parsers.unshift(new wM.default),e.parsers.unshift(new xb.default),e.parsers.unshift(new EM.default),e.parsers.unshift(new kM.default),e}nt.createCasualConfiguration=Rb;function Dp(r=!0,e=!0){return cM.includeCommonConfiguration({parsers:[new hM.default(e),new gM.default,new TM.default,new xb.default,new yM.default,new vM.default,new bM.default,new _M.default(r),new SM.default(r),new OM.default(r)],refiners:[new fM.default,new dM.default]},r)}nt.createConfiguration=Dp});var Nb=E(Ga=>{"use strict";var RM=Ga&&Ga.__importDefault||function(r){return r&&r.__esModule?r:{default:r}};Object.defineProperty(Ga,"__esModule",{value:!0});var MM=RM(we()),CM=H(),AM=1,Cb=2,PM=3,Ab=4,Pb=5,NM=6,xp=class extends CM.AbstractParserWithWordBoundaryChecking{innerPattern(e){return new RegExp("(\u800C\u5BB6|\u7ACB(?:\u523B|\u5373)|\u5373\u523B)|(\u4ECA|\u660E|\u524D|\u5927\u524D|\u5F8C|\u5927\u5F8C|\u807D|\u6628|\u5C0B|\u7434)(\u65E9|\u671D|\u665A)|(\u4E0A(?:\u5348|\u665D)|\u671D(?:\u65E9)|\u65E9(?:\u4E0A)|\u4E0B(?:\u5348|\u665D)|\u664F(?:\u665D)|\u665A(?:\u4E0A)|\u591C(?:\u665A)?|\u4E2D(?:\u5348)|\u51CC(?:\u6668))|(\u4ECA|\u660E|\u524D|\u5927\u524D|\u5F8C|\u5927\u5F8C|\u807D|\u6628|\u5C0B|\u7434)(?:\u65E5|\u5929)(?:[\\s|,|\uFF0C]*)(?:(\u4E0A(?:\u5348|\u665D)|\u671D(?:\u65E9)|\u65E9(?:\u4E0A)|\u4E0B(?:\u5348|\u665D)|\u664F(?:\u665D)|\u665A(?:\u4E0A)|\u591C(?:\u665A)?|\u4E2D(?:\u5348)|\u51CC(?:\u6668)))?","i")}innerExtract(e,t){let n=t.index,i=e.createParsingResult(n,t[0]),s=MM.default(e.refDate),a=s;if(t[AM])i.start.imply("hour",s.hour()),i.start.imply("minute",s.minute()),i.start.imply("second",s.second()),i.start.imply("millisecond",s.millisecond());else if(t[Cb]){let o=t[Cb],u=t[PM];o=="\u660E"||o=="\u807D"?s.hour()>1&&(a=a.add(1,"day")):o=="\u6628"||o=="\u5C0B"||o=="\u7434"?a=a.add(-1,"day"):o=="\u524D"?a=a.add(-2,"day"):o=="\u5927\u524D"?a=a.add(-3,"day"):o=="\u5F8C"?a=a.add(2,"day"):o=="\u5927\u5F8C"&&(a=a.add(3,"day")),u=="\u65E9"||u=="\u671D"?i.start.imply("hour",6):u=="\u665A"&&(i.start.imply("hour",22),i.start.imply("meridiem",1))}else if(t[Ab]){let u=t[Ab][0];u=="\u65E9"||u=="\u671D"||u=="\u4E0A"?i.start.imply("hour",6):u=="\u4E0B"||u=="\u664F"?(i.start.imply("hour",15),i.start.imply("meridiem",1)):u=="\u4E2D"?(i.start.imply("hour",12),i.start.imply("meridiem",1)):u=="\u591C"||u=="\u665A"?(i.start.imply("hour",22),i.start.imply("meridiem",1)):u=="\u51CC"&&i.start.imply("hour",0)}else if(t[Pb]){let o=t[Pb];o=="\u660E"||o=="\u807D"?s.hour()>1&&(a=a.add(1,"day")):o=="\u6628"||o=="\u5C0B"||o=="\u7434"?a=a.add(-1,"day"):o=="\u524D"?a=a.add(-2,"day"):o=="\u5927\u524D"?a=a.add(-3,"day"):o=="\u5F8C"?a=a.add(2,"day"):o=="\u5927\u5F8C"&&(a=a.add(3,"day"));let u=t[NM];if(u){let l=u[0];l=="\u65E9"||l=="\u671D"||l=="\u4E0A"?i.start.imply("hour",6):l=="\u4E0B"||l=="\u664F"?(i.start.imply("hour",15),i.start.imply("meridiem",1)):l=="\u4E2D"?(i.start.imply("hour",12),i.start.imply("meridiem",1)):l=="\u591C"||l=="\u665A"?(i.start.imply("hour",22),i.start.imply("meridiem",1)):l=="\u51CC"&&i.start.imply("hour",0)}}return i.start.assign("day",a.date()),i.start.assign("month",a.month()+1),i.start.assign("year",a.year()),i}};Ga.default=xp});var ds=E(It=>{"use strict";Object.defineProperty(It,"__esModule",{value:!0});It.zhStringToYear=It.zhStringToNumber=It.WEEKDAY_OFFSET=It.NUMBER=void 0;It.NUMBER={\u96F6:0,\u4E00:1,\u4E8C:2,\u5169:2,\u4E09:3,\u56DB:4,\u4E94:5,\u516D:6,\u4E03:7,\u516B:8,\u4E5D:9,\u5341:10,\u5EFF:20,\u5345:30};It.WEEKDAY_OFFSET={\u5929:0,\u65E5:0,\u4E00:1,\u4E8C:2,\u4E09:3,\u56DB:4,\u4E94:5,\u516D:6};function IM(r){let e=0;for(let t=0;t<r.length;t++){let n=r[t];n==="\u5341"?e=e===0?It.NUMBER[n]:e*It.NUMBER[n]:e+=It.NUMBER[n]}return e}It.zhStringToNumber=IM;function FM(r){let e="";for(let t=0;t<r.length;t++){let n=r[t];e=e+It.NUMBER[n]}return parseInt(e)}It.zhStringToYear=FM});var Fb=E(Ya=>{"use strict";var LM=Ya&&Ya.__importDefault||function(r){return r&&r.__esModule?r:{default:r}};Object.defineProperty(Ya,"__esModule",{value:!0});var UM=LM(we()),WM=H(),wi=ds(),Rp=1,Ib=2,Mp=3,Cp=class extends WM.AbstractParserWithWordBoundaryChecking{innerPattern(){return new RegExp("(\\d{2,4}|["+Object.keys(wi.NUMBER).join("")+"]{4}|["+Object.keys(wi.NUMBER).join("")+"]{2})?(?:\\s*)(?:\u5E74)?(?:[\\s|,|\uFF0C]*)(\\d{1,2}|["+Object.keys(wi.NUMBER).join("")+"]{1,2})(?:\\s*)(?:\u6708)(?:\\s*)(\\d{1,2}|["+Object.keys(wi.NUMBER).join("")+"]{1,2})?(?:\\s*)(?:\u65E5|\u865F)?")}innerExtract(e,t){let n=UM.default(e.refDate),i=e.createParsingResult(t.index,t[0]),s=parseInt(t[Ib]);if(isNaN(s)&&(s=wi.zhStringToNumber(t[Ib])),i.start.assign("month",s),t[Mp]){let a=parseInt(t[Mp]);isNaN(a)&&(a=wi.zhStringToNumber(t[Mp])),i.start.assign("day",a)}else i.start.imply("day",n.date());if(t[Rp]){let a=parseInt(t[Rp]);isNaN(a)&&(a=wi.zhStringToYear(t[Rp])),i.start.assign("year",a)}else i.start.imply("year",n.year());return i}};Ya.default=Cp});var Ub=E(Ba=>{"use strict";var qM=Ba&&Ba.__importDefault||function(r){return r&&r.__esModule?r:{default:r}};Object.defineProperty(Ba,"__esModule",{value:!0});var $M=qM(we()),jM=H(),Lb=ds(),GM=new RegExp("(\\d+|["+Object.keys(Lb.NUMBER).join("")+"]+|\u534A|\u5E7E)(?:\\s*)(?:\u500B)?(\u79D2(?:\u9418)?|\u5206\u9418|\u5C0F\u6642|\u9418|\u65E5|\u5929|\u661F\u671F|\u79AE\u62DC|\u6708|\u5E74)(?:(?:\u4E4B|\u904E)?\u5F8C|(?:\u4E4B)?\u5167)","i"),Ap=1,YM=2,Pp=class extends jM.AbstractParserWithWordBoundaryChecking{innerPattern(){return GM}innerExtract(e,t){let n=e.createParsingResult(t.index,t[0]),i=parseInt(t[Ap]);if(isNaN(i)&&(i=Lb.zhStringToNumber(t[Ap])),isNaN(i)){let u=t[Ap];if(u==="\u5E7E")i=3;else if(u==="\u534A")i=.5;else return null}let s=$M.default(e.refDate),o=t[YM][0];return o.match(/[日天星禮月年]/)?(o=="\u65E5"||o=="\u5929"?s=s.add(i,"d"):o=="\u661F"||o=="\u79AE"?s=s.add(i*7,"d"):o=="\u6708"?s=s.add(i,"month"):o=="\u5E74"&&(s=s.add(i,"year")),n.start.assign("year",s.year()),n.start.assign("month",s.month()+1),n.start.assign("day",s.date()),n):(o=="\u79D2"?s=s.add(i,"second"):o=="\u5206"?s=s.add(i,"minute"):(o=="\u5C0F"||o=="\u9418")&&(s=s.add(i,"hour")),n.start.imply("year",s.year()),n.start.imply("month",s.month()+1),n.start.imply("day",s.date()),n.start.assign("hour",s.hour()),n.start.assign("minute",s.minute()),n.start.assign("second",s.second()),n)}};Ba.default=Pp});var qb=E(Ha=>{"use strict";var BM=Ha&&Ha.__importDefault||function(r){return r&&r.__esModule?r:{default:r}};Object.defineProperty(Ha,"__esModule",{value:!0});var HM=BM(we()),VM=H(),Wb=ds(),zM=new RegExp("(?<prefix>\u4E0A|\u4ECA|\u4E0B|\u9019|\u5462)(?:\u500B)?(?:\u661F\u671F|\u79AE\u62DC|\u9031)(?<weekday>"+Object.keys(Wb.WEEKDAY_OFFSET).join("|")+")"),Np=class extends VM.AbstractParserWithWordBoundaryChecking{innerPattern(){return zM}innerExtract(e,t){let n=e.createParsingResult(t.index,t[0]),i=t.groups.weekday,s=Wb.WEEKDAY_OFFSET[i];if(s===void 0)return null;let a=null,o=t.groups.prefix;o=="\u4E0A"?a="last":o=="\u4E0B"?a="next":(o=="\u4ECA"||o=="\u9019"||o=="\u5462")&&(a="this");let u=HM.default(e.refDate),l=!1,c=u.day();return a=="last"||a=="past"?(u=u.day(s-7),l=!0):a=="next"?(u=u.day(s+7),l=!0):a=="this"?u=u.day(s):Math.abs(s-7-c)<Math.abs(s-c)?u=u.day(s-7):Math.abs(s+7-c)<Math.abs(s-c)?u=u.day(s+7):u=u.day(s),n.start.assign("weekday",s),l?(n.start.assign("day",u.date()),n.start.assign("month",u.month()+1),n.start.assign("year",u.year())):(n.start.imply("day",u.date()),n.start.imply("month",u.month()+1),n.start.imply("year",u.year())),n}};Ha.default=Np});var $b=E(Va=>{"use strict";var KM=Va&&Va.__importDefault||function(r){return r&&r.__esModule?r:{default:r}};Object.defineProperty(Va,"__esModule",{value:!0});var QM=KM(we()),XM=H(),ur=ds(),ZM=new RegExp("(?:\u7531|\u5F9E|\u81EA)?(?:(\u4ECA|\u660E|\u524D|\u5927\u524D|\u5F8C|\u5927\u5F8C|\u807D|\u6628|\u5C0B|\u7434)(\u65E9|\u671D|\u665A)|(\u4E0A(?:\u5348|\u665D)|\u671D(?:\u65E9)|\u65E9(?:\u4E0A)|\u4E0B(?:\u5348|\u665D)|\u664F(?:\u665D)|\u665A(?:\u4E0A)|\u591C(?:\u665A)?|\u4E2D(?:\u5348)|\u51CC(?:\u6668))|(\u4ECA|\u660E|\u524D|\u5927\u524D|\u5F8C|\u5927\u5F8C|\u807D|\u6628|\u5C0B|\u7434)(?:\u65E5|\u5929)(?:[\\s,\uFF0C]*)(?:(\u4E0A(?:\u5348|\u665D)|\u671D(?:\u65E9)|\u65E9(?:\u4E0A)|\u4E0B(?:\u5348|\u665D)|\u664F(?:\u665D)|\u665A(?:\u4E0A)|\u591C(?:\u665A)?|\u4E2D(?:\u5348)|\u51CC(?:\u6668)))?)?(?:[\\s,\uFF0C]*)(?:(\\d+|["+Object.keys(ur.NUMBER).join("")+"]+)(?:\\s*)(?:\u9EDE|\u6642|:|\uFF1A)(?:\\s*)(\\d+|\u534A|\u6B63|\u6574|["+Object.keys(ur.NUMBER).join("")+"]+)?(?:\\s*)(?:\u5206|:|\uFF1A)?(?:\\s*)(\\d+|["+Object.keys(ur.NUMBER).join("")+"]+)?(?:\\s*)(?:\u79D2)?)(?:\\s*(A.M.|P.M.|AM?|PM?))?","i"),JM=new RegExp("(?:^\\s*(?:\u5230|\u81F3|\\-|\\\u2013|\\~|\\\u301C)\\s*)(?:(\u4ECA|\u660E|\u524D|\u5927\u524D|\u5F8C|\u5927\u5F8C|\u807D|\u6628|\u5C0B|\u7434)(\u65E9|\u671D|\u665A)|(\u4E0A(?:\u5348|\u665D)|\u671D(?:\u65E9)|\u65E9(?:\u4E0A)|\u4E0B(?:\u5348|\u665D)|\u664F(?:\u665D)|\u665A(?:\u4E0A)|\u591C(?:\u665A)?|\u4E2D(?:\u5348)|\u51CC(?:\u6668))|(\u4ECA|\u660E|\u524D|\u5927\u524D|\u5F8C|\u5927\u5F8C|\u807D|\u6628|\u5C0B|\u7434)(?:\u65E5|\u5929)(?:[\\s,\uFF0C]*)(?:(\u4E0A(?:\u5348|\u665D)|\u671D(?:\u65E9)|\u65E9(?:\u4E0A)|\u4E0B(?:\u5348|\u665D)|\u664F(?:\u665D)|\u665A(?:\u4E0A)|\u591C(?:\u665A)?|\u4E2D(?:\u5348)|\u51CC(?:\u6668)))?)?(?:[\\s,\uFF0C]*)(?:(\\d+|["+Object.keys(ur.NUMBER).join("")+"]+)(?:\\s*)(?:\u9EDE|\u6642|:|\uFF1A)(?:\\s*)(\\d+|\u534A|\u6B63|\u6574|["+Object.keys(ur.NUMBER).join("")+"]+)?(?:\\s*)(?:\u5206|:|\uFF1A)?(?:\\s*)(\\d+|["+Object.keys(ur.NUMBER).join("")+"]+)?(?:\\s*)(?:\u79D2)?)(?:\\s*(A.M.|P.M.|AM?|PM?))?","i"),xu=1,Ru=2,Mu=3,Cu=4,Au=5,Pu=6,or=7,fs=8,Nu=9,Ip=class extends XM.AbstractParserWithWordBoundaryChecking{innerPattern(){return ZM}innerExtract(e,t){if(t.index>0&&e.text[t.index-1].match(/\w/))return null;let n=QM.default(e.refDate),i=e.createParsingResult(t.index,t[0]),s=n.clone();if(t[xu]){var a=t[xu];a=="\u660E"||a=="\u807D"?n.hour()>1&&s.add(1,"day"):a=="\u6628"||a=="\u5C0B"||a=="\u7434"?s.add(-1,"day"):a=="\u524D"?s.add(-2,"day"):a=="\u5927\u524D"?s.add(-3,"day"):a=="\u5F8C"?s.add(2,"day"):a=="\u5927\u5F8C"&&s.add(3,"day"),i.start.assign("day",s.date()),i.start.assign("month",s.month()+1),i.start.assign("year",s.year())}else if(t[Cu]){var o=t[Cu];o=="\u660E"||o=="\u807D"?s.add(1,"day"):o=="\u6628"||o=="\u5C0B"||o=="\u7434"?s.add(-1,"day"):o=="\u524D"?s.add(-2,"day"):o=="\u5927\u524D"?s.add(-3,"day"):o=="\u5F8C"?s.add(2,"day"):o=="\u5927\u5F8C"&&s.add(3,"day"),i.start.assign("day",s.date()),i.start.assign("month",s.month()+1),i.start.assign("year",s.year())}else i.start.imply("day",s.date()),i.start.imply("month",s.month()+1),i.start.imply("year",s.year());let u=0,l=0,c=-1;if(t[fs]){var d=parseInt(t[fs]);if(isNaN(d)&&(d=ur.zhStringToNumber(t[fs])),d>=60)return null;i.start.assign("second",d)}if(u=parseInt(t[Pu]),isNaN(u)&&(u=ur.zhStringToNumber(t[Pu])),t[or]?t[or]=="\u534A"?l=30:t[or]=="\u6B63"||t[or]=="\u6574"?l=0:(l=parseInt(t[or]),isNaN(l)&&(l=ur.zhStringToNumber(t[or]))):u>100&&(l=u%100,u=Math.floor(u/100)),l>=60||u>24)return null;if(u>=12&&(c=1),t[Nu]){if(u>12)return null;var f=t[Nu][0].toLowerCase();f=="a"&&(c=0,u==12&&(u=0)),f=="p"&&(c=1,u!=12&&(u+=12))}else if(t[Ru]){var m=t[Ru],y=m[0];y=="\u671D"||y=="\u65E9"?(c=0,u==12&&(u=0)):y=="\u665A"&&(c=1,u!=12&&(u+=12))}else if(t[Mu]){var b=t[Mu],k=b[0];k=="\u4E0A"||k=="\u671D"||k=="\u65E9"||k=="\u51CC"?(c=0,u==12&&(u=0)):(k=="\u4E0B"||k=="\u664F"||k=="\u665A")&&(c=1,u!=12&&(u+=12))}else if(t[Au]){var _=t[Au],R=_[0];R=="\u4E0A"||R=="\u671D"||R=="\u65E9"||R=="\u51CC"?(c=0,u==12&&(u=0)):(R=="\u4E0B"||R=="\u664F"||R=="\u665A")&&(c=1,u!=12&&(u+=12))}if(i.start.assign("hour",u),i.start.assign("minute",l),c>=0?i.start.assign("meridiem",c):u<12?i.start.imply("meridiem",0):i.start.imply("meridiem",1),t=JM.exec(e.text.substring(i.index+i.text.length)),!t)return i.text.match(/^\d+$/)?null:i;let S=s.clone();if(i.end=e.createParsingComponents(),t[xu]){var a=t[xu];a=="\u660E"||a=="\u807D"?n.hour()>1&&S.add(1,"day"):a=="\u6628"||a=="\u5C0B"||a=="\u7434"?S.add(-1,"day"):a=="\u524D"?S.add(-2,"day"):a=="\u5927\u524D"?S.add(-3,"day"):a=="\u5F8C"?S.add(2,"day"):a=="\u5927\u5F8C"&&S.add(3,"day"),i.end.assign("day",S.date()),i.end.assign("month",S.month()+1),i.end.assign("year",S.year())}else if(t[Cu]){var o=t[Cu];o=="\u660E"||o=="\u807D"?S.add(1,"day"):o=="\u6628"||o=="\u5C0B"||o=="\u7434"?S.add(-1,"day"):o=="\u524D"?S.add(-2,"day"):o=="\u5927\u524D"?S.add(-3,"day"):o=="\u5F8C"?S.add(2,"day"):o=="\u5927\u5F8C"&&S.add(3,"day"),i.end.assign("day",S.date()),i.end.assign("month",S.month()+1),i.end.assign("year",S.year())}else i.end.imply("day",S.date()),i.end.imply("month",S.month()+1),i.end.imply("year",S.year());if(u=0,l=0,c=-1,t[fs]){var d=parseInt(t[fs]);if(isNaN(d)&&(d=ur.zhStringToNumber(t[fs])),d>=60)return null;i.end.assign("second",d)}if(u=parseInt(t[Pu]),isNaN(u)&&(u=ur.zhStringToNumber(t[Pu])),t[or]?t[or]=="\u534A"?l=30:t[or]=="\u6B63"||t[or]=="\u6574"?l=0:(l=parseInt(t[or]),isNaN(l)&&(l=ur.zhStringToNumber(t[or]))):u>100&&(l=u%100,u=Math.floor(u/100)),l>=60||u>24)return null;if(u>=12&&(c=1),t[Nu]){if(u>12)return null;var f=t[Nu][0].toLowerCase();f=="a"&&(c=0,u==12&&(u=0)),f=="p"&&(c=1,u!=12&&(u+=12)),i.start.isCertain("meridiem")||(c==0?(i.start.imply("meridiem",0),i.start.get("hour")==12&&i.start.assign("hour",0)):(i.start.imply("meridiem",1),i.start.get("hour")!=12&&i.start.assign("hour",i.start.get("hour")+12)))}else if(t[Ru]){var m=t[Ru],y=m[0];y=="\u671D"||y=="\u65E9"?(c=0,u==12&&(u=0)):y=="\u665A"&&(c=1,u!=12&&(u+=12))}else if(t[Mu]){var b=t[Mu],k=b[0];k=="\u4E0A"||k=="\u671D"||k=="\u65E9"||k=="\u51CC"?(c=0,u==12&&(u=0)):(k=="\u4E0B"||k=="\u664F"||k=="\u665A")&&(c=1,u!=12&&(u+=12))}else if(t[Au]){var _=t[Au],R=_[0];R=="\u4E0A"||R=="\u671D"||R=="\u65E9"||R=="\u51CC"?(c=0,u==12&&(u=0)):(R=="\u4E0B"||R=="\u664F"||R=="\u665A")&&(c=1,u!=12&&(u+=12))}return i.text=i.text+t[0],i.end.assign("hour",u),i.end.assign("minute",l),c>=0?i.end.assign("meridiem",c):i.start.isCertain("meridiem")&&i.start.get("meridiem")==1&&i.start.get("hour")>u?i.end.imply("meridiem",0):u>12&&i.end.imply("meridiem",1),i.end.date().getTime()<i.start.date().getTime()&&i.end.imply("day",i.end.get("day")+1),i}};Va.default=Ip});var Gb=E(za=>{"use strict";var eC=za&&za.__importDefault||function(r){return r&&r.__esModule?r:{default:r}};Object.defineProperty(za,"__esModule",{value:!0});var tC=eC(we()),rC=H(),jb=ds(),nC=new RegExp("(?:\u661F\u671F|\u79AE\u62DC|\u9031)(?<weekday>"+Object.keys(jb.WEEKDAY_OFFSET).join("|")+")"),Fp=class extends rC.AbstractParserWithWordBoundaryChecking{innerPattern(){return nC}innerExtract(e,t){let n=e.createParsingResult(t.index,t[0]),i=t.groups.weekday,s=jb.WEEKDAY_OFFSET[i];if(s===void 0)return null;let a=tC.default(e.refDate),o=!1,u=a.day();return Math.abs(s-7-u)<Math.abs(s-u)?a=a.day(s-7):Math.abs(s+7-u)<Math.abs(s-u)?a=a.day(s+7):a=a.day(s),n.start.assign("weekday",s),o?(n.start.assign("day",a.date()),n.start.assign("month",a.month()+1),n.start.assign("year",a.year())):(n.start.imply("day",a.date()),n.start.imply("month",a.month()+1),n.start.imply("year",a.year())),n}};za.default=Fp});var Yb=E(Ka=>{"use strict";var iC=Ka&&Ka.__importDefault||function(r){return r&&r.__esModule?r:{default:r}};Object.defineProperty(Ka,"__esModule",{value:!0});var sC=iC(Kr()),Lp=class extends sC.default{patternBetween(){return/^\s*(至|到|\-|\~|～|－|ー)\s*$/i}};Ka.default=Lp});var Bb=E(Qa=>{"use strict";var aC=Qa&&Qa.__importDefault||function(r){return r&&r.__esModule?r:{default:r}};Object.defineProperty(Qa,"__esModule",{value:!0});var oC=aC(hn()),Up=class extends oC.default{patternBetween(){return/^\s*$/i}};Qa.default=Up});var Hb=E(Be=>{"use strict";var vn=Be&&Be.__importDefault||function(r){return r&&r.__esModule?r:{default:r}};Object.defineProperty(Be,"__esModule",{value:!0});Be.createConfiguration=Be.createCasualConfiguration=Be.parseDate=Be.parse=Be.strict=Be.casual=Be.hant=void 0;var Wp=Dr(),uC=vn(cu()),lC=gn(),cC=vn(Nb()),dC=vn(Fb()),fC=vn(Ub()),pC=vn(qb()),mC=vn($b()),hC=vn(Gb()),gC=vn(Yb()),yC=vn(Bb());Be.hant=new Wp.Chrono(qp());Be.casual=new Wp.Chrono(qp());Be.strict=new Wp.Chrono($p());function TC(r,e,t){return Be.casual.parse(r,e,t)}Be.parse=TC;function bC(r,e,t){return Be.casual.parseDate(r,e,t)}Be.parseDate=bC;function qp(){let r=$p();return r.parsers.unshift(new cC.default),r}Be.createCasualConfiguration=qp;function $p(){let r=lC.includeCommonConfiguration({parsers:[new dC.default,new pC.default,new hC.default,new mC.default,new fC.default],refiners:[new gC.default,new yC.default]});return r.refiners=r.refiners.filter(e=>!(e instanceof uC.default)),r}Be.createConfiguration=$p});var Qb=E(Xa=>{"use strict";var _C=Xa&&Xa.__importDefault||function(r){return r&&r.__esModule?r:{default:r}};Object.defineProperty(Xa,"__esModule",{value:!0});var vC=_C(we()),wC=H(),kC=1,Vb=2,EC=3,zb=4,Kb=5,SC=6,jp=class extends wC.AbstractParserWithWordBoundaryChecking{innerPattern(e){return new RegExp("(\u73B0\u5728|\u7ACB(?:\u523B|\u5373)|\u5373\u523B)|(\u4ECA|\u660E|\u524D|\u5927\u524D|\u540E|\u5927\u540E|\u6628)(\u65E9|\u665A)|(\u4E0A(?:\u5348)|\u65E9(?:\u4E0A)|\u4E0B(?:\u5348)|\u665A(?:\u4E0A)|\u591C(?:\u665A)?|\u4E2D(?:\u5348)|\u51CC(?:\u6668))|(\u4ECA|\u660E|\u524D|\u5927\u524D|\u540E|\u5927\u540E|\u6628)(?:\u65E5|\u5929)(?:[\\s|,|\uFF0C]*)(?:(\u4E0A(?:\u5348)|\u65E9(?:\u4E0A)|\u4E0B(?:\u5348)|\u665A(?:\u4E0A)|\u591C(?:\u665A)?|\u4E2D(?:\u5348)|\u51CC(?:\u6668)))?","i")}innerExtract(e,t){let n=t.index,i=e.createParsingResult(n,t[0]),s=vC.default(e.refDate),a=s;if(t[kC])i.start.imply("hour",s.hour()),i.start.imply("minute",s.minute()),i.start.imply("second",s.second()),i.start.imply("millisecond",s.millisecond());else if(t[Vb]){let o=t[Vb],u=t[EC];o=="\u660E"?s.hour()>1&&(a=a.add(1,"day")):o=="\u6628"?a=a.add(-1,"day"):o=="\u524D"?a=a.add(-2,"day"):o=="\u5927\u524D"?a=a.add(-3,"day"):o=="\u540E"?a=a.add(2,"day"):o=="\u5927\u540E"&&(a=a.add(3,"day")),u=="\u65E9"?i.start.imply("hour",6):u=="\u665A"&&(i.start.imply("hour",22),i.start.imply("meridiem",1))}else if(t[zb]){let u=t[zb][0];u=="\u65E9"||u=="\u4E0A"?i.start.imply("hour",6):u=="\u4E0B"?(i.start.imply("hour",15),i.start.imply("meridiem",1)):u=="\u4E2D"?(i.start.imply("hour",12),i.start.imply("meridiem",1)):u=="\u591C"||u=="\u665A"?(i.start.imply("hour",22),i.start.imply("meridiem",1)):u=="\u51CC"&&i.start.imply("hour",0)}else if(t[Kb]){let o=t[Kb];o=="\u660E"?s.hour()>1&&(a=a.add(1,"day")):o=="\u6628"?a=a.add(-1,"day"):o=="\u524D"?a=a.add(-2,"day"):o=="\u5927\u524D"?a=a.add(-3,"day"):o=="\u540E"?a=a.add(2,"day"):o=="\u5927\u540E"&&(a=a.add(3,"day"));let u=t[SC];if(u){let l=u[0];l=="\u65E9"||l=="\u4E0A"?i.start.imply("hour",6):l=="\u4E0B"?(i.start.imply("hour",15),i.start.imply("meridiem",1)):l=="\u4E2D"?(i.start.imply("hour",12),i.start.imply("meridiem",1)):l=="\u591C"||l=="\u665A"?(i.start.imply("hour",22),i.start.imply("meridiem",1)):l=="\u51CC"&&i.start.imply("hour",0)}}return i.start.assign("day",a.date()),i.start.assign("month",a.month()+1),i.start.assign("year",a.year()),i}};Xa.default=jp});var ps=E(Ft=>{"use strict";Object.defineProperty(Ft,"__esModule",{value:!0});Ft.zhStringToYear=Ft.zhStringToNumber=Ft.WEEKDAY_OFFSET=Ft.NUMBER=void 0;Ft.NUMBER={\u96F6:0,"\u3007":0,\u4E00:1,\u4E8C:2,\u4E24:2,\u4E09:3,\u56DB:4,\u4E94:5,\u516D:6,\u4E03:7,\u516B:8,\u4E5D:9,\u5341:10};Ft.WEEKDAY_OFFSET={\u5929:0,\u65E5:0,\u4E00:1,\u4E8C:2,\u4E09:3,\u56DB:4,\u4E94:5,\u516D:6};function OC(r){let e=0;for(let t=0;t<r.length;t++){let n=r[t];n==="\u5341"?e=e===0?Ft.NUMBER[n]:e*Ft.NUMBER[n]:e+=Ft.NUMBER[n]}return e}Ft.zhStringToNumber=OC;function DC(r){let e="";for(let t=0;t<r.length;t++){let n=r[t];e=e+Ft.NUMBER[n]}return parseInt(e)}Ft.zhStringToYear=DC});var Zb=E(Za=>{"use strict";var xC=Za&&Za.__importDefault||function(r){return r&&r.__esModule?r:{default:r}};Object.defineProperty(Za,"__esModule",{value:!0});var RC=xC(we()),MC=H(),ki=ps(),Gp=1,Xb=2,Yp=3,Bp=class extends MC.AbstractParserWithWordBoundaryChecking{innerPattern(){return new RegExp("(\\d{2,4}|["+Object.keys(ki.NUMBER).join("")+"]{4}|["+Object.keys(ki.NUMBER).join("")+"]{2})?(?:\\s*)(?:\u5E74)?(?:[\\s|,|\uFF0C]*)(\\d{1,2}|["+Object.keys(ki.NUMBER).join("")+"]{1,3})(?:\\s*)(?:\u6708)(?:\\s*)(\\d{1,2}|["+Object.keys(ki.NUMBER).join("")+"]{1,3})?(?:\\s*)(?:\u65E5|\u53F7)?")}innerExtract(e,t){let n=RC.default(e.refDate),i=e.createParsingResult(t.index,t[0]),s=parseInt(t[Xb]);if(isNaN(s)&&(s=ki.zhStringToNumber(t[Xb])),i.start.assign("month",s),t[Yp]){let a=parseInt(t[Yp]);isNaN(a)&&(a=ki.zhStringToNumber(t[Yp])),i.start.assign("day",a)}else i.start.imply("day",n.date());if(t[Gp]){let a=parseInt(t[Gp]);isNaN(a)&&(a=ki.zhStringToYear(t[Gp])),i.start.assign("year",a)}else i.start.imply("year",n.year());return i}};Za.default=Bp});var e_=E(Ja=>{"use strict";var CC=Ja&&Ja.__importDefault||function(r){return r&&r.__esModule?r:{default:r}};Object.defineProperty(Ja,"__esModule",{value:!0});var AC=CC(we()),PC=H(),Jb=ps(),NC=new RegExp("(\\d+|["+Object.keys(Jb.NUMBER).join("")+"]+|\u534A|\u51E0)(?:\\s*)(?:\u4E2A)?(\u79D2(?:\u949F)?|\u5206\u949F|\u5C0F\u65F6|\u949F|\u65E5|\u5929|\u661F\u671F|\u793C\u62DC|\u6708|\u5E74)(?:(?:\u4E4B|\u8FC7)?\u540E|(?:\u4E4B)?\u5185)","i"),Hp=1,IC=2,Vp=class extends PC.AbstractParserWithWordBoundaryChecking{innerPattern(){return NC}innerExtract(e,t){let n=e.createParsingResult(t.index,t[0]),i=parseInt(t[Hp]);if(isNaN(i)&&(i=Jb.zhStringToNumber(t[Hp])),isNaN(i)){let u=t[Hp];if(u==="\u51E0")i=3;else if(u==="\u534A")i=.5;else return null}let s=AC.default(e.refDate),o=t[IC][0];return o.match(/[日天星礼月年]/)?(o=="\u65E5"||o=="\u5929"?s=s.add(i,"d"):o=="\u661F"||o=="\u793C"?s=s.add(i*7,"d"):o=="\u6708"?s=s.add(i,"month"):o=="\u5E74"&&(s=s.add(i,"year")),n.start.assign("year",s.year()),n.start.assign("month",s.month()+1),n.start.assign("day",s.date()),n):(o=="\u79D2"?s=s.add(i,"second"):o=="\u5206"?s=s.add(i,"minute"):(o=="\u5C0F"||o=="\u949F")&&(s=s.add(i,"hour")),n.start.imply("year",s.year()),n.start.imply("month",s.month()+1),n.start.imply("day",s.date()),n.start.assign("hour",s.hour()),n.start.assign("minute",s.minute()),n.start.assign("second",s.second()),n)}};Ja.default=Vp});var r_=E(eo=>{"use strict";var FC=eo&&eo.__importDefault||function(r){return r&&r.__esModule?r:{default:r}};Object.defineProperty(eo,"__esModule",{value:!0});var LC=FC(we()),UC=H(),t_=ps(),WC=new RegExp("(?<prefix>\u4E0A|\u4E0B|\u8FD9)(?:\u4E2A)?(?:\u661F\u671F|\u793C\u62DC|\u5468)(?<weekday>"+Object.keys(t_.WEEKDAY_OFFSET).join("|")+")"),zp=class extends UC.AbstractParserWithWordBoundaryChecking{innerPattern(){return WC}innerExtract(e,t){let n=e.createParsingResult(t.index,t[0]),i=t.groups.weekday,s=t_.WEEKDAY_OFFSET[i];if(s===void 0)return null;let a=null,o=t.groups.prefix;o=="\u4E0A"?a="last":o=="\u4E0B"?a="next":o=="\u8FD9"&&(a="this");let u=LC.default(e.refDate),l=!1,c=u.day();return a=="last"||a=="past"?(u=u.day(s-7),l=!0):a=="next"?(u=u.day(s+7),l=!0):a=="this"?u=u.day(s):Math.abs(s-7-c)<Math.abs(s-c)?u=u.day(s-7):Math.abs(s+7-c)<Math.abs(s-c)?u=u.day(s+7):u=u.day(s),n.start.assign("weekday",s),l?(n.start.assign("day",u.date()),n.start.assign("month",u.month()+1),n.start.assign("year",u.year())):(n.start.imply("day",u.date()),n.start.imply("month",u.month()+1),n.start.imply("year",u.year())),n}};eo.default=zp});var n_=E(to=>{"use strict";var qC=to&&to.__importDefault||function(r){return r&&r.__esModule?r:{default:r}};Object.defineProperty(to,"__esModule",{value:!0});var $C=qC(we()),jC=H(),cr=ps(),GC=new RegExp("(?:\u4ECE|\u81EA)?(?:(\u4ECA|\u660E|\u524D|\u5927\u524D|\u540E|\u5927\u540E|\u6628)(\u65E9|\u671D|\u665A)|(\u4E0A(?:\u5348)|\u65E9(?:\u4E0A)|\u4E0B(?:\u5348)|\u665A(?:\u4E0A)|\u591C(?:\u665A)?|\u4E2D(?:\u5348)|\u51CC(?:\u6668))|(\u4ECA|\u660E|\u524D|\u5927\u524D|\u540E|\u5927\u540E|\u6628)(?:\u65E5|\u5929)(?:[\\s,\uFF0C]*)(?:(\u4E0A(?:\u5348)|\u65E9(?:\u4E0A)|\u4E0B(?:\u5348)|\u665A(?:\u4E0A)|\u591C(?:\u665A)?|\u4E2D(?:\u5348)|\u51CC(?:\u6668)))?)?(?:[\\s,\uFF0C]*)(?:(\\d+|["+Object.keys(cr.NUMBER).join("")+"]+)(?:\\s*)(?:\u70B9|\u65F6|:|\uFF1A)(?:\\s*)(\\d+|\u534A|\u6B63|\u6574|["+Object.keys(cr.NUMBER).join("")+"]+)?(?:\\s*)(?:\u5206|:|\uFF1A)?(?:\\s*)(\\d+|["+Object.keys(cr.NUMBER).join("")+"]+)?(?:\\s*)(?:\u79D2)?)(?:\\s*(A.M.|P.M.|AM?|PM?))?","i"),YC=new RegExp("(?:^\\s*(?:\u5230|\u81F3|\\-|\\\u2013|\\~|\\\u301C)\\s*)(?:(\u4ECA|\u660E|\u524D|\u5927\u524D|\u540E|\u5927\u540E|\u6628)(\u65E9|\u671D|\u665A)|(\u4E0A(?:\u5348)|\u65E9(?:\u4E0A)|\u4E0B(?:\u5348)|\u665A(?:\u4E0A)|\u591C(?:\u665A)?|\u4E2D(?:\u5348)|\u51CC(?:\u6668))|(\u4ECA|\u660E|\u524D|\u5927\u524D|\u540E|\u5927\u540E|\u6628)(?:\u65E5|\u5929)(?:[\\s,\uFF0C]*)(?:(\u4E0A(?:\u5348)|\u65E9(?:\u4E0A)|\u4E0B(?:\u5348)|\u665A(?:\u4E0A)|\u591C(?:\u665A)?|\u4E2D(?:\u5348)|\u51CC(?:\u6668)))?)?(?:[\\s,\uFF0C]*)(?:(\\d+|["+Object.keys(cr.NUMBER).join("")+"]+)(?:\\s*)(?:\u70B9|\u65F6|:|\uFF1A)(?:\\s*)(\\d+|\u534A|\u6B63|\u6574|["+Object.keys(cr.NUMBER).join("")+"]+)?(?:\\s*)(?:\u5206|:|\uFF1A)?(?:\\s*)(\\d+|["+Object.keys(cr.NUMBER).join("")+"]+)?(?:\\s*)(?:\u79D2)?)(?:\\s*(A.M.|P.M.|AM?|PM?))?","i"),Iu=1,Fu=2,Lu=3,Uu=4,Wu=5,qu=6,lr=7,ms=8,$u=9,Kp=class extends jC.AbstractParserWithWordBoundaryChecking{innerPattern(){return GC}innerExtract(e,t){if(t.index>0&&e.text[t.index-1].match(/\w/))return null;let n=$C.default(e.refDate),i=e.createParsingResult(t.index,t[0]),s=n.clone();if(t[Iu]){let c=t[Iu];c=="\u660E"?n.hour()>1&&s.add(1,"day"):c=="\u6628"?s.add(-1,"day"):c=="\u524D"?s.add(-2,"day"):c=="\u5927\u524D"?s.add(-3,"day"):c=="\u540E"?s.add(2,"day"):c=="\u5927\u540E"&&s.add(3,"day"),i.start.assign("day",s.date()),i.start.assign("month",s.month()+1),i.start.assign("year",s.year())}else if(t[Uu]){let c=t[Uu];c=="\u660E"?s.add(1,"day"):c=="\u6628"?s.add(-1,"day"):c=="\u524D"?s.add(-2,"day"):c=="\u5927\u524D"?s.add(-3,"day"):c=="\u540E"?s.add(2,"day"):c=="\u5927\u540E"&&s.add(3,"day"),i.start.assign("day",s.date()),i.start.assign("month",s.month()+1),i.start.assign("year",s.year())}else i.start.imply("day",s.date()),i.start.imply("month",s.month()+1),i.start.imply("year",s.year());let a=0,o=0,u=-1;if(t[ms]){let c=parseInt(t[ms]);if(isNaN(c)&&(c=cr.zhStringToNumber(t[ms])),c>=60)return null;i.start.assign("second",c)}if(a=parseInt(t[qu]),isNaN(a)&&(a=cr.zhStringToNumber(t[qu])),t[lr]?t[lr]=="\u534A"?o=30:t[lr]=="\u6B63"||t[lr]=="\u6574"?o=0:(o=parseInt(t[lr]),isNaN(o)&&(o=cr.zhStringToNumber(t[lr]))):a>100&&(o=a%100,a=Math.floor(a/100)),o>=60||a>24)return null;if(a>=12&&(u=1),t[$u]){if(a>12)return null;let c=t[$u][0].toLowerCase();c=="a"&&(u=0,a==12&&(a=0)),c=="p"&&(u=1,a!=12&&(a+=12))}else if(t[Fu]){let d=t[Fu][0];d=="\u65E9"?(u=0,a==12&&(a=0)):d=="\u665A"&&(u=1,a!=12&&(a+=12))}else if(t[Lu]){let d=t[Lu][0];d=="\u4E0A"||d=="\u65E9"||d=="\u51CC"?(u=0,a==12&&(a=0)):(d=="\u4E0B"||d=="\u665A")&&(u=1,a!=12&&(a+=12))}else if(t[Wu]){let d=t[Wu][0];d=="\u4E0A"||d=="\u65E9"||d=="\u51CC"?(u=0,a==12&&(a=0)):(d=="\u4E0B"||d=="\u665A")&&(u=1,a!=12&&(a+=12))}if(i.start.assign("hour",a),i.start.assign("minute",o),u>=0?i.start.assign("meridiem",u):a<12?i.start.imply("meridiem",0):i.start.imply("meridiem",1),t=YC.exec(e.text.substring(i.index+i.text.length)),!t)return i.text.match(/^\d+$/)?null:i;let l=s.clone();if(i.end=e.createParsingComponents(),t[Iu]){let c=t[Iu];c=="\u660E"?n.hour()>1&&l.add(1,"day"):c=="\u6628"?l.add(-1,"day"):c=="\u524D"?l.add(-2,"day"):c=="\u5927\u524D"?l.add(-3,"day"):c=="\u540E"?l.add(2,"day"):c=="\u5927\u540E"&&l.add(3,"day"),i.end.assign("day",l.date()),i.end.assign("month",l.month()+1),i.end.assign("year",l.year())}else if(t[Uu]){let c=t[Uu];c=="\u660E"?l.add(1,"day"):c=="\u6628"?l.add(-1,"day"):c=="\u524D"?l.add(-2,"day"):c=="\u5927\u524D"?l.add(-3,"day"):c=="\u540E"?l.add(2,"day"):c=="\u5927\u540E"&&l.add(3,"day"),i.end.assign("day",l.date()),i.end.assign("month",l.month()+1),i.end.assign("year",l.year())}else i.end.imply("day",l.date()),i.end.imply("month",l.month()+1),i.end.imply("year",l.year());if(a=0,o=0,u=-1,t[ms]){let c=parseInt(t[ms]);if(isNaN(c)&&(c=cr.zhStringToNumber(t[ms])),c>=60)return null;i.end.assign("second",c)}if(a=parseInt(t[qu]),isNaN(a)&&(a=cr.zhStringToNumber(t[qu])),t[lr]?t[lr]=="\u534A"?o=30:t[lr]=="\u6B63"||t[lr]=="\u6574"?o=0:(o=parseInt(t[lr]),isNaN(o)&&(o=cr.zhStringToNumber(t[lr]))):a>100&&(o=a%100,a=Math.floor(a/100)),o>=60||a>24)return null;if(a>=12&&(u=1),t[$u]){if(a>12)return null;let c=t[$u][0].toLowerCase();c=="a"&&(u=0,a==12&&(a=0)),c=="p"&&(u=1,a!=12&&(a+=12)),i.start.isCertain("meridiem")||(u==0?(i.start.imply("meridiem",0),i.start.get("hour")==12&&i.start.assign("hour",0)):(i.start.imply("meridiem",1),i.start.get("hour")!=12&&i.start.assign("hour",i.start.get("hour")+12)))}else if(t[Fu]){let d=t[Fu][0];d=="\u65E9"?(u=0,a==12&&(a=0)):d=="\u665A"&&(u=1,a!=12&&(a+=12))}else if(t[Lu]){let d=t[Lu][0];d=="\u4E0A"||d=="\u65E9"||d=="\u51CC"?(u=0,a==12&&(a=0)):(d=="\u4E0B"||d=="\u665A")&&(u=1,a!=12&&(a+=12))}else if(t[Wu]){let d=t[Wu][0];d=="\u4E0A"||d=="\u65E9"||d=="\u51CC"?(u=0,a==12&&(a=0)):(d=="\u4E0B"||d=="\u665A")&&(u=1,a!=12&&(a+=12))}return i.text=i.text+t[0],i.end.assign("hour",a),i.end.assign("minute",o),u>=0?i.end.assign("meridiem",u):i.start.isCertain("meridiem")&&i.start.get("meridiem")==1&&i.start.get("hour")>a?i.end.imply("meridiem",0):a>12&&i.end.imply("meridiem",1),i.end.date().getTime()<i.start.date().getTime()&&i.end.imply("day",i.end.get("day")+1),i}};to.default=Kp});var s_=E(ro=>{"use strict";var BC=ro&&ro.__importDefault||function(r){return r&&r.__esModule?r:{default:r}};Object.defineProperty(ro,"__esModule",{value:!0});var HC=BC(we()),VC=H(),i_=ps(),zC=new RegExp("(?:\u661F\u671F|\u793C\u62DC|\u5468)(?<weekday>"+Object.keys(i_.WEEKDAY_OFFSET).join("|")+")"),Qp=class extends VC.AbstractParserWithWordBoundaryChecking{innerPattern(){return zC}innerExtract(e,t){let n=e.createParsingResult(t.index,t[0]),i=t.groups.weekday,s=i_.WEEKDAY_OFFSET[i];if(s===void 0)return null;let a=HC.default(e.refDate),o=!1,u=a.day();return Math.abs(s-7-u)<Math.abs(s-u)?a=a.day(s-7):Math.abs(s+7-u)<Math.abs(s-u)?a=a.day(s+7):a=a.day(s),n.start.assign("weekday",s),o?(n.start.assign("day",a.date()),n.start.assign("month",a.month()+1),n.start.assign("year",a.year())):(n.start.imply("day",a.date()),n.start.imply("month",a.month()+1),n.start.imply("year",a.year())),n}};ro.default=Qp});var a_=E(no=>{"use strict";var KC=no&&no.__importDefault||function(r){return r&&r.__esModule?r:{default:r}};Object.defineProperty(no,"__esModule",{value:!0});var QC=KC(Kr()),Xp=class extends QC.default{patternBetween(){return/^\s*(至|到|-|~|～|－|ー)\s*$/i}};no.default=Xp});var o_=E(io=>{"use strict";var XC=io&&io.__importDefault||function(r){return r&&r.__esModule?r:{default:r}};Object.defineProperty(io,"__esModule",{value:!0});var ZC=XC(hn()),Zp=class extends ZC.default{patternBetween(){return/^\s*$/i}};io.default=Zp});var u_=E(He=>{"use strict";var wn=He&&He.__importDefault||function(r){return r&&r.__esModule?r:{default:r}};Object.defineProperty(He,"__esModule",{value:!0});He.createConfiguration=He.createCasualConfiguration=He.parseDate=He.parse=He.strict=He.casual=He.hans=void 0;var Jp=Dr(),JC=wn(cu()),eA=gn(),tA=wn(Qb()),rA=wn(Zb()),nA=wn(e_()),iA=wn(r_()),sA=wn(n_()),aA=wn(s_()),oA=wn(a_()),uA=wn(o_());He.hans=new Jp.Chrono(em());He.casual=new Jp.Chrono(em());He.strict=new Jp.Chrono(tm());function lA(r,e,t){return He.casual.parse(r,e,t)}He.parse=lA;function cA(r,e,t){return He.casual.parseDate(r,e,t)}He.parseDate=cA;function em(){let r=tm();return r.parsers.unshift(new tA.default),r}He.createCasualConfiguration=em;function tm(){let r=eA.includeCommonConfiguration({parsers:[new rA.default,new iA.default,new aA.default,new sA.default,new nA.default],refiners:[new oA.default,new uA.default]});return r.refiners=r.refiners.filter(e=>!(e instanceof JC.default)),r}He.createConfiguration=tm});var c_=E(Bt=>{"use strict";var l_=Bt&&Bt.__createBinding||(Object.create?function(r,e,t,n){n===void 0&&(n=t),Object.defineProperty(r,n,{enumerable:!0,get:function(){return e[t]}})}:function(r,e,t,n){n===void 0&&(n=t),r[n]=e[t]}),dA=Bt&&Bt.__setModuleDefault||(Object.create?function(r,e){Object.defineProperty(r,"default",{enumerable:!0,value:e})}:function(r,e){r.default=e}),fA=Bt&&Bt.__exportStar||function(r,e){for(var t in r)t!=="default"&&!Object.prototype.hasOwnProperty.call(e,t)&&l_(e,r,t)},pA=Bt&&Bt.__importStar||function(r){if(r&&r.__esModule)return r;var e={};if(r!=null)for(var t in r)t!=="default"&&Object.prototype.hasOwnProperty.call(r,t)&&l_(e,r,t);return dA(e,r),e};Object.defineProperty(Bt,"__esModule",{value:!0});Bt.hans=void 0;fA(Hb(),Bt);Bt.hans=pA(u_())});var Lt=E(ae=>{"use strict";Object.defineProperty(ae,"__esModule",{value:!0});ae.parseTimeUnits=ae.TIME_UNITS_PATTERN=ae.parseYear=ae.YEAR_PATTERN=ae.parseOrdinalNumberPattern=ae.ORDINAL_NUMBER_PATTERN=ae.parseNumberPattern=ae.NUMBER_PATTERN=ae.TIME_UNIT_DICTIONARY=ae.ORDINAL_WORD_DICTIONARY=ae.INTEGER_WORD_DICTIONARY=ae.MONTH_DICTIONARY=ae.FULL_MONTH_NAME_DICTIONARY=ae.WEEKDAY_DICTIONARY=ae.REGEX_PARTS=void 0;var ju=Re(),mA=lt();ae.REGEX_PARTS={leftBoundary:"([^\\p{L}\\p{N}_]|^)",rightBoundary:"(?=[^\\p{L}\\p{N}_]|$)",flags:"iu"};ae.WEEKDAY_DICTIONARY={\u0432\u043E\u0441\u043A\u0440\u0435\u0441\u0435\u043D\u044C\u0435:0,\u0432\u043E\u0441\u043A\u0440\u0435\u0441\u0435\u043D\u044C\u044F:0,\u0432\u0441\u043A:0,"\u0432\u0441\u043A.":0,\u043F\u043E\u043D\u0435\u0434\u0435\u043B\u044C\u043D\u0438\u043A:1,\u043F\u043E\u043D\u0435\u0434\u0435\u043B\u044C\u043D\u0438\u043A\u0430:1,\u043F\u043D:1,"\u043F\u043D.":1,\u0432\u0442\u043E\u0440\u043D\u0438\u043A:2,\u0432\u0442\u043E\u0440\u043D\u0438\u043A\u0430:2,\u0432\u0442:2,"\u0432\u0442.":2,\u0441\u0440\u0435\u0434\u0430:3,\u0441\u0440\u0435\u0434\u044B:3,\u0441\u0440\u0435\u0434\u0443:3,\u0441\u0440:3,"\u0441\u0440.":3,\u0447\u0435\u0442\u0432\u0435\u0440\u0433:4,\u0447\u0435\u0442\u0432\u0435\u0440\u0433\u0430:4,\u0447\u0442:4,"\u0447\u0442.":4,\u043F\u044F\u0442\u043D\u0438\u0446\u0430:5,\u043F\u044F\u0442\u043D\u0438\u0446\u0443:5,\u043F\u044F\u0442\u043D\u0438\u0446\u044B:5,\u043F\u0442:5,"\u043F\u0442.":5,\u0441\u0443\u0431\u0431\u043E\u0442\u0430:6,\u0441\u0443\u0431\u0431\u043E\u0442\u0443:6,\u0441\u0443\u0431\u0431\u043E\u0442\u044B:6,\u0441\u0431:6,"\u0441\u0431.":6};ae.FULL_MONTH_NAME_DICTIONARY={\u044F\u043D\u0432\u0430\u0440\u044C:1,\u044F\u043D\u0432\u0430\u0440\u044F:1,\u044F\u043D\u0432\u0430\u0440\u0435:1,\u0444\u0435\u0432\u0440\u044F\u043B\u044C:2,\u0444\u0435\u0432\u0440\u044F\u043B\u044F:2,\u0444\u0435\u0432\u0440\u044F\u043B\u0435:2,\u043C\u0430\u0440\u0442:3,\u043C\u0430\u0440\u0442\u0430:3,\u043C\u0430\u0440\u0442\u0435:3,\u0430\u043F\u0440\u0435\u043B\u044C:4,\u0430\u043F\u0440\u0435\u043B\u044F:4,\u0430\u043F\u0440\u0435\u043B\u0435:4,\u043C\u0430\u0439:5,\u043C\u0430\u044F:5,\u043C\u0430\u0435:5,\u0438\u044E\u043D\u044C:6,\u0438\u044E\u043D\u044F:6,\u0438\u044E\u043D\u0435:6,\u0438\u044E\u043B\u044C:7,\u0438\u044E\u043B\u044F:7,\u0438\u044E\u043B\u0435:7,\u0430\u0432\u0433\u0443\u0441\u0442:8,\u0430\u0432\u0433\u0443\u0441\u0442\u0430:8,\u0430\u0432\u0433\u0443\u0441\u0442\u0435:8,\u0441\u0435\u043D\u0442\u044F\u0431\u0440\u044C:9,\u0441\u0435\u043D\u0442\u044F\u0431\u0440\u044F:9,\u0441\u0435\u043D\u0442\u044F\u0431\u0440\u0435:9,\u043E\u043A\u0442\u044F\u0431\u0440\u044C:10,\u043E\u043A\u0442\u044F\u0431\u0440\u044F:10,\u043E\u043A\u0442\u044F\u0431\u0440\u0435:10,\u043D\u043E\u044F\u0431\u0440\u044C:11,\u043D\u043E\u044F\u0431\u0440\u044F:11,\u043D\u043E\u044F\u0431\u0440\u0435:11,\u0434\u0435\u043A\u0430\u0431\u0440\u044C:12,\u0434\u0435\u043A\u0430\u0431\u0440\u044F:12,\u0434\u0435\u043A\u0430\u0431\u0440\u0435:12};ae.MONTH_DICTIONARY=Object.assign(Object.assign({},ae.FULL_MONTH_NAME_DICTIONARY),{\u044F\u043D\u0432:1,"\u044F\u043D\u0432.":1,\u0444\u0435\u0432:2,"\u0444\u0435\u0432.":2,\u043C\u0430\u0440:3,"\u043C\u0430\u0440.":3,\u0430\u043F\u0440:4,"\u0430\u043F\u0440.":4,\u0430\u0432\u0433:8,"\u0430\u0432\u0433.":8,\u0441\u0435\u043D:9,"\u0441\u0435\u043D.":9,\u043E\u043A\u0442:10,"\u043E\u043A\u0442.":10,\u043D\u043E\u044F:11,"\u043D\u043E\u044F.":11,\u0434\u0435\u043A:12,"\u0434\u0435\u043A.":12});ae.INTEGER_WORD_DICTIONARY={\u043E\u0434\u0438\u043D:1,\u043E\u0434\u043D\u0430:1,\u043E\u0434\u043D\u043E\u0439:1,\u043E\u0434\u043D\u0443:1,\u0434\u0432\u0435:2,\u0434\u0432\u0430:2,\u0434\u0432\u0443\u0445:2,\u0442\u0440\u0438:3,\u0442\u0440\u0435\u0445:3,\u0442\u0440\u0451\u0445:3,\u0447\u0435\u0442\u044B\u0440\u0435:4,\u0447\u0435\u0442\u044B\u0440\u0435\u0445:4,\u0447\u0435\u0442\u044B\u0440\u0451\u0445:4,\u043F\u044F\u0442\u044C:5,\u043F\u044F\u0442\u0438:5,\u0448\u0435\u0441\u0442\u044C:6,\u0448\u0435\u0441\u0442\u0438:6,\u0441\u0435\u043C\u044C:7,\u0441\u0435\u043C\u0438:7,\u0432\u043E\u0441\u0435\u043C\u044C:8,\u0432\u043E\u0441\u0435\u043C\u044C\u043C\u0438:8,\u0434\u0435\u0432\u044F\u0442\u044C:9,\u0434\u0435\u0432\u044F\u0442\u0438:9,\u0434\u0435\u0441\u044F\u0442\u044C:10,\u0434\u0435\u0441\u044F\u0442\u0438:10,\u043E\u0434\u0438\u043D\u043D\u0430\u0434\u0446\u0430\u0442\u044C:11,\u043E\u0434\u0438\u043D\u043D\u0430\u0434\u0446\u0430\u0442\u0438:11,\u0434\u0432\u0435\u043D\u0430\u0434\u0446\u0430\u0442\u044C:12,\u0434\u0432\u0435\u043D\u0430\u0434\u0446\u0430\u0442\u0438:12};ae.ORDINAL_WORD_DICTIONARY={\u043F\u0435\u0440\u0432\u043E\u0435:1,\u043F\u0435\u0440\u0432\u043E\u0433\u043E:1,\u0432\u0442\u043E\u0440\u043E\u0435:2,\u0432\u0442\u043E\u0440\u043E\u0433\u043E:2,\u0442\u0440\u0435\u0442\u044C\u0435:3,\u0442\u0440\u0435\u0442\u044C\u0435\u0433\u043E:3,\u0447\u0435\u0442\u0432\u0435\u0440\u0442\u043E\u0435:4,\u0447\u0435\u0442\u0432\u0435\u0440\u0442\u043E\u0433\u043E:4,\u043F\u044F\u0442\u043E\u0435:5,\u043F\u044F\u0442\u043E\u0433\u043E:5,\u0448\u0435\u0441\u0442\u043E\u0435:6,\u0448\u0435\u0441\u0442\u043E\u0433\u043E:6,\u0441\u0435\u0434\u044C\u043C\u043E\u0435:7,\u0441\u0435\u0434\u044C\u043C\u043E\u0433\u043E:7,\u0432\u043E\u0441\u044C\u043C\u043E\u0435:8,\u0432\u043E\u0441\u044C\u043C\u043E\u0433\u043E:8,\u0434\u0435\u0432\u044F\u0442\u043E\u0435:9,\u0434\u0435\u0432\u044F\u0442\u043E\u0433\u043E:9,\u0434\u0435\u0441\u044F\u0442\u043E\u0435:10,\u0434\u0435\u0441\u044F\u0442\u043E\u0433\u043E:10,\u043E\u0434\u0438\u043D\u043D\u0430\u0434\u0446\u0430\u0442\u043E\u0435:11,\u043E\u0434\u0438\u043D\u043D\u0430\u0434\u0446\u0430\u0442\u043E\u0433\u043E:11,\u0434\u0432\u0435\u043D\u0430\u0434\u0446\u0430\u0442\u043E\u0435:12,\u0434\u0432\u0435\u043D\u0430\u0434\u0446\u0430\u0442\u043E\u0433\u043E:12,\u0442\u0440\u0438\u043D\u0430\u0434\u0446\u0430\u0442\u043E\u0435:13,\u0442\u0440\u0438\u043D\u0430\u0434\u0446\u0430\u0442\u043E\u0433\u043E:13,\u0447\u0435\u0442\u044B\u0440\u043D\u0430\u0434\u0446\u0430\u0442\u043E\u0435:14,\u0447\u0435\u0442\u044B\u0440\u043D\u0430\u0434\u0446\u0430\u0442\u043E\u0433\u043E:14,\u043F\u044F\u0442\u043D\u0430\u0434\u0446\u0430\u0442\u043E\u0435:15,\u043F\u044F\u0442\u043D\u0430\u0434\u0446\u0430\u0442\u043E\u0433\u043E:15,\u0448\u0435\u0441\u0442\u043D\u0430\u0434\u0446\u0430\u0442\u043E\u0435:16,\u0448\u0435\u0441\u0442\u043D\u0430\u0434\u0446\u0430\u0442\u043E\u0433\u043E:16,\u0441\u0435\u043C\u043D\u0430\u0434\u0446\u0430\u0442\u043E\u0435:17,\u0441\u0435\u043C\u043D\u0430\u0434\u0446\u0430\u0442\u043E\u0433\u043E:17,\u0432\u043E\u0441\u0435\u043C\u043D\u0430\u0434\u0446\u0430\u0442\u043E\u0435:18,\u0432\u043E\u0441\u0435\u043C\u043D\u0430\u0434\u0446\u0430\u0442\u043E\u0433\u043E:18,\u0434\u0435\u0432\u044F\u0442\u043D\u0430\u0434\u0446\u0430\u0442\u043E\u0435:19,\u0434\u0435\u0432\u044F\u0442\u043D\u0430\u0434\u0446\u0430\u0442\u043E\u0433\u043E:19,\u0434\u0432\u0430\u0434\u0446\u0430\u0442\u043E\u0435:20,\u0434\u0432\u0430\u0434\u0446\u0430\u0442\u043E\u0433\u043E:20,"\u0434\u0432\u0430\u0434\u0446\u0430\u0442\u044C \u043F\u0435\u0440\u0432\u043E\u0435":21,"\u0434\u0432\u0430\u0434\u0446\u0430\u0442\u044C \u043F\u0435\u0440\u0432\u043E\u0433\u043E":21,"\u0434\u0432\u0430\u0434\u0446\u0430\u0442\u044C \u0432\u0442\u043E\u0440\u043E\u0435":22,"\u0434\u0432\u0430\u0434\u0446\u0430\u0442\u044C \u0432\u0442\u043E\u0440\u043E\u0433\u043E":22,"\u0434\u0432\u0430\u0434\u0446\u0430\u0442\u044C \u0442\u0440\u0435\u0442\u044C\u0435":23,"\u0434\u0432\u0430\u0434\u0446\u0430\u0442\u044C \u0442\u0440\u0435\u0442\u044C\u0435\u0433\u043E":23,"\u0434\u0432\u0430\u0434\u0446\u0430\u0442\u044C \u0447\u0435\u0442\u0432\u0435\u0440\u0442\u043E\u0435":24,"\u0434\u0432\u0430\u0434\u0446\u0430\u0442\u044C \u0447\u0435\u0442\u0432\u0435\u0440\u0442\u043E\u0433\u043E":24,"\u0434\u0432\u0430\u0434\u0446\u0430\u0442\u044C \u043F\u044F\u0442\u043E\u0435":25,"\u0434\u0432\u0430\u0434\u0446\u0430\u0442\u044C \u043F\u044F\u0442\u043E\u0433\u043E":25,"\u0434\u0432\u0430\u0434\u0446\u0430\u0442\u044C \u0448\u0435\u0441\u0442\u043E\u0435":26,"\u0434\u0432\u0430\u0434\u0446\u0430\u0442\u044C \u0448\u0435\u0441\u0442\u043E\u0433\u043E":26,"\u0434\u0432\u0430\u0434\u0446\u0430\u0442\u044C \u0441\u0435\u0434\u044C\u043C\u043E\u0435":27,"\u0434\u0432\u0430\u0434\u0446\u0430\u0442\u044C \u0441\u0435\u0434\u044C\u043C\u043E\u0433\u043E":27,"\u0434\u0432\u0430\u0434\u0446\u0430\u0442\u044C \u0432\u043E\u0441\u044C\u043C\u043E\u0435":28,"\u0434\u0432\u0430\u0434\u0446\u0430\u0442\u044C \u0432\u043E\u0441\u044C\u043C\u043E\u0433\u043E":28,"\u0434\u0432\u0430\u0434\u0446\u0430\u0442\u044C \u0434\u0435\u0432\u044F\u0442\u043E\u0435":29,"\u0434\u0432\u0430\u0434\u0446\u0430\u0442\u044C \u0434\u0435\u0432\u044F\u0442\u043E\u0433\u043E":29,\u0442\u0440\u0438\u0434\u0446\u0430\u0442\u043E\u0435:30,\u0442\u0440\u0438\u0434\u0446\u0430\u0442\u043E\u0433\u043E:30,"\u0442\u0440\u0438\u0434\u0446\u0430\u0442\u044C \u043F\u0435\u0440\u0432\u043E\u0435":31,"\u0442\u0440\u0438\u0434\u0446\u0430\u0442\u044C \u043F\u0435\u0440\u0432\u043E\u0433\u043E":31};ae.TIME_UNIT_DICTIONARY={\u0441\u0435\u043A:"second",\u0441\u0435\u043A\u0443\u043D\u0434\u0430:"second",\u0441\u0435\u043A\u0443\u043D\u0434:"second",\u0441\u0435\u043A\u0443\u043D\u0434\u044B:"second",\u0441\u0435\u043A\u0443\u043D\u0434\u0443:"second",\u0441\u0435\u043A\u0443\u043D\u0434\u043E\u0447\u043A\u0430:"second",\u0441\u0435\u043A\u0443\u043D\u0434\u043E\u0447\u043A\u0438:"second",\u0441\u0435\u043A\u0443\u043D\u0434\u043E\u0447\u0435\u043A:"second",\u0441\u0435\u043A\u0443\u043D\u0434\u043E\u0447\u043A\u0443:"second",\u043C\u0438\u043D:"minute",\u043C\u0438\u043D\u0443\u0442\u0430:"minute",\u043C\u0438\u043D\u0443\u0442:"minute",\u043C\u0438\u043D\u0443\u0442\u044B:"minute",\u043C\u0438\u043D\u0443\u0442\u0443:"minute",\u043C\u0438\u043D\u0443\u0442\u043E\u043A:"minute",\u043C\u0438\u043D\u0443\u0442\u043A\u0438:"minute",\u043C\u0438\u043D\u0443\u0442\u043A\u0443:"minute",\u0447\u0430\u0441:"hour",\u0447\u0430\u0441\u043E\u0432:"hour",\u0447\u0430\u0441\u0430:"hour",\u0447\u0430\u0441\u0443:"hour",\u0447\u0430\u0441\u0438\u043A\u043E\u0432:"hour",\u0447\u0430\u0441\u0438\u043A\u0430:"hour",\u0447\u0430\u0441\u0438\u043A\u0435:"hour",\u0447\u0430\u0441\u0438\u043A:"hour",\u0434\u0435\u043D\u044C:"d",\u0434\u043D\u044F:"d",\u0434\u043D\u0435\u0439:"d",\u0441\u0443\u0442\u043E\u043A:"d",\u0441\u0443\u0442\u043A\u0438:"d",\u043D\u0435\u0434\u0435\u043B\u044F:"week",\u043D\u0435\u0434\u0435\u043B\u0435:"week",\u043D\u0435\u0434\u0435\u043B\u0438:"week",\u043D\u0435\u0434\u0435\u043B\u044E:"week",\u043D\u0435\u0434\u0435\u043B\u044C:"week",\u043D\u0435\u0434\u0435\u043B\u044C\u043A\u0435:"week",\u043D\u0435\u0434\u0435\u043B\u044C\u043A\u0438:"week",\u043D\u0435\u0434\u0435\u043B\u0435\u043A:"week",\u043C\u0435\u0441\u044F\u0446:"month",\u043C\u0435\u0441\u044F\u0446\u0435:"month",\u043C\u0435\u0441\u044F\u0446\u0435\u0432:"month",\u043C\u0435\u0441\u044F\u0446\u0430:"month",\u043A\u0432\u0430\u0440\u0442\u0430\u043B:"quarter",\u043A\u0432\u0430\u0440\u0442\u0430\u043B\u0435:"quarter",\u043A\u0432\u0430\u0440\u0442\u0430\u043B\u043E\u0432:"quarter",\u0433\u043E\u0434:"year",\u0433\u043E\u0434\u0430:"year",\u0433\u043E\u0434\u0443:"year",\u0433\u043E\u0434\u043E\u0432:"year",\u043B\u0435\u0442:"year",\u0433\u043E\u0434\u0438\u043A:"year",\u0433\u043E\u0434\u0438\u043A\u0430:"year",\u0433\u043E\u0434\u0438\u043A\u043E\u0432:"year"};ae.NUMBER_PATTERN=`(?:${ju.matchAnyPattern(ae.INTEGER_WORD_DICTIONARY)}|[0-9]+|[0-9]+\\.[0-9]+|\u043F\u043E\u043B|\u043D\u0435\u0441\u043A\u043E\u043B\u044C\u043A\u043E|\u043F\u0430\u0440(?:\u044B|\u0443)|\\s{0,3})`;function f_(r){let e=r.toLowerCase();return ae.INTEGER_WORD_DICTIONARY[e]!==void 0?ae.INTEGER_WORD_DICTIONARY[e]:e.match(/несколько/)?3:e.match(/пол/)?.5:e.match(/пар/)?2:e===""?1:parseFloat(e)}ae.parseNumberPattern=f_;ae.ORDINAL_NUMBER_PATTERN=`(?:${ju.matchAnyPattern(ae.ORDINAL_WORD_DICTIONARY)}|[0-9]{1,2}(?:\u0433\u043E|\u043E\u0433\u043E|\u0435|\u043E\u0435)?)`;function hA(r){let e=r.toLowerCase();return ae.ORDINAL_WORD_DICTIONARY[e]!==void 0?ae.ORDINAL_WORD_DICTIONARY[e]:(e=e.replace(/(?:st|nd|rd|th)$/i,""),parseInt(e))}ae.parseOrdinalNumberPattern=hA;var rm="(?:\\s+(?:\u0433\u043E\u0434\u0443|\u0433\u043E\u0434\u0430|\u0433\u043E\u0434|\u0433|\u0433.))?";ae.YEAR_PATTERN=`(?:[1-9][0-9]{0,3}${rm}\\s*(?:\u043D.\u044D.|\u0434\u043E \u043D.\u044D.|\u043D. \u044D.|\u0434\u043E \u043D. \u044D.)|[1-2][0-9]{3}${rm}|[5-9][0-9]${rm})`;function gA(r){if(/(год|года|г|г.)/i.test(r)&&(r=r.replace(/(год|года|г|г.)/i,"")),/(до н.э.|до н. э.)/i.test(r))return r=r.replace(/(до н.э.|до н. э.)/i,""),-parseInt(r);if(/(н. э.|н.э.)/i.test(r))return r=r.replace(/(н. э.|н.э.)/i,""),parseInt(r);let e=parseInt(r);return mA.findMostLikelyADYear(e)}ae.parseYear=gA;var p_=`(${ae.NUMBER_PATTERN})\\s{0,3}(${ju.matchAnyPattern(ae.TIME_UNIT_DICTIONARY)})`,d_=new RegExp(p_,"i");ae.TIME_UNITS_PATTERN=ju.repeatedTimeunitPattern("(?:(?:\u043E\u043A\u043E\u043B\u043E|\u043F\u0440\u0438\u043C\u0435\u0440\u043D\u043E)\\s{0,3})?",p_);function yA(r){let e={},t=r,n=d_.exec(t);for(;n;)TA(e,n),t=t.substring(n[0].length).trim(),n=d_.exec(t);return e}ae.parseTimeUnits=yA;function TA(r,e){let t=f_(e[1]),n=ae.TIME_UNIT_DICTIONARY[e[2].toLowerCase()];r[n]=t}});var h_=E(im=>{"use strict";Object.defineProperty(im,"__esModule",{value:!0});var so=Lt(),bA=We(),_A=H(),m_=`(?:(?:\u043E\u043A\u043E\u043B\u043E|\u043F\u0440\u0438\u043C\u0435\u0440\u043D\u043E)\\s*(?:~\\s*)?)?(${so.TIME_UNITS_PATTERN})${so.REGEX_PARTS.rightBoundary}`,vA=new RegExp(`(?:\u0432 \u0442\u0435\u0447\u0435\u043D\u0438\u0435|\u0432 \u0442\u0435\u0447\u0435\u043D\u0438\u0438)\\s*${m_}`,so.REGEX_PARTS.flags),wA=new RegExp(m_,"i"),nm=class extends _A.AbstractParserWithWordBoundaryChecking{patternLeftBoundary(){return so.REGEX_PARTS.leftBoundary}innerPattern(e){return e.option.forwardDate?wA:vA}innerExtract(e,t){let n=so.parseTimeUnits(t[1]);return bA.ParsingComponents.createRelativeFromReference(e.reference,n)}};im.default=nm});var __=E(am=>{"use strict";Object.defineProperty(am,"__esModule",{value:!0});var kA=lt(),ao=Lt(),b_=Lt(),Gu=Lt(),EA=Re(),SA=H(),OA=new RegExp(`(?:\u0441)?\\s*(${Gu.ORDINAL_NUMBER_PATTERN})(?:\\s{0,3}(?:\u043F\u043E|-|\u2013|\u0434\u043E)?\\s{0,3}(${Gu.ORDINAL_NUMBER_PATTERN}))?(?:-|\\/|\\s{0,3}(?:of)?\\s{0,3})(${EA.matchAnyPattern(ao.MONTH_DICTIONARY)})(?:(?:-|\\/|,?\\s{0,3})(${b_.YEAR_PATTERN}(?![^\\s]\\d)))?${ao.REGEX_PARTS.rightBoundary}`,ao.REGEX_PARTS.flags),g_=1,y_=2,DA=3,T_=4,sm=class extends SA.AbstractParserWithWordBoundaryChecking{patternLeftBoundary(){return ao.REGEX_PARTS.leftBoundary}innerPattern(){return OA}innerExtract(e,t){let n=e.createParsingResult(t.index,t[0]),i=ao.MONTH_DICTIONARY[t[DA].toLowerCase()],s=Gu.parseOrdinalNumberPattern(t[g_]);if(s>31)return t.index=t.index+t[g_].length,null;if(n.start.assign("month",i),n.start.assign("day",s),t[T_]){let a=b_.parseYear(t[T_]);n.start.assign("year",a)}else{let a=kA.findYearClosestToRef(e.refDate,s,i);n.start.imply("year",a)}if(t[y_]){let a=Gu.parseOrdinalNumberPattern(t[y_]);n.end=n.start.clone(),n.end.assign("day",a)}return n}};am.default=sm});var k_=E(um=>{"use strict";Object.defineProperty(um,"__esModule",{value:!0});var oo=Lt(),xA=lt(),RA=Re(),w_=Lt(),MA=H(),CA=new RegExp(`((?:\u0432)\\s*)?(${RA.matchAnyPattern(oo.MONTH_DICTIONARY)})\\s*(?:[,-]?\\s*(${w_.YEAR_PATTERN})?)?(?=[^\\s\\w]|\\s+[^0-9]|\\s+$|$)`,oo.REGEX_PARTS.flags),AA=2,v_=3,om=class extends MA.AbstractParserWithWordBoundaryChecking{patternLeftBoundary(){return oo.REGEX_PARTS.leftBoundary}innerPattern(){return CA}innerExtract(e,t){let n=t[AA].toLowerCase();if(t[0].length<=3&&!oo.FULL_MONTH_NAME_DICTIONARY[n])return null;let i=e.createParsingResult(t.index,t.index+t[0].length);i.start.imply("day",1);let s=oo.MONTH_DICTIONARY[n];if(i.start.assign("month",s),t[v_]){let a=w_.parseYear(t[v_]);i.start.assign("year",a)}else{let a=xA.findYearClosestToRef(e.refDate,1,s);i.start.imply("year",a)}return i}};um.default=om});var S_=E(cm=>{"use strict";Object.defineProperty(cm,"__esModule",{value:!0});var Yu=Ke(),PA=gi(),E_=Lt(),lm=class extends PA.AbstractTimeExpressionParser{constructor(e){super(e)}patternFlags(){return E_.REGEX_PARTS.flags}primaryPatternLeftBoundary(){return"(^|\\s|T|(?:[^\\p{L}\\p{N}_]))"}followingPhase(){return"\\s*(?:\\-|\\\u2013|\\~|\\\u301C|\u0434\u043E|\u0438|\u043F\u043E|\\?)\\s*"}primaryPrefix(){return"(?:(?:\u0432|\u0441)\\s*)??"}primarySuffix(){return`(?:\\s*(?:\u0443\u0442\u0440\u0430|\u0432\u0435\u0447\u0435\u0440\u0430|\u043F\u043E\u0441\u043B\u0435 \u043F\u043E\u043B\u0443\u0434\u043D\u044F))?(?!\\/)${E_.REGEX_PARTS.rightBoundary}`}extractPrimaryTimeComponents(e,t){let n=super.extractPrimaryTimeComponents(e,t);if(n){if(t[0].endsWith("\u0432\u0435\u0447\u0435\u0440\u0430")){let i=n.get("hour");i>=6&&i<12?(n.assign("hour",n.get("hour")+12),n.assign("meridiem",Yu.Meridiem.PM)):i<6&&n.assign("meridiem",Yu.Meridiem.AM)}if(t[0].endsWith("\u043F\u043E\u0441\u043B\u0435 \u043F\u043E\u043B\u0443\u0434\u043D\u044F")){n.assign("meridiem",Yu.Meridiem.PM);let i=n.get("hour");i>=0&&i<=6&&n.assign("hour",n.get("hour")+12)}t[0].endsWith("\u0443\u0442\u0440\u0430")&&(n.assign("meridiem",Yu.Meridiem.AM),n.get("hour")<12&&n.assign("hour",n.get("hour")))}return n}};cm.default=lm});var O_=E(fm=>{"use strict";Object.defineProperty(fm,"__esModule",{value:!0});var Bu=Lt(),NA=We(),IA=H(),FA=sr(),LA=new RegExp(`(${Bu.TIME_UNITS_PATTERN})\\s{0,5}\u043D\u0430\u0437\u0430\u0434(?=(?:\\W|$))`,Bu.REGEX_PARTS.flags),dm=class extends IA.AbstractParserWithWordBoundaryChecking{patternLeftBoundary(){return Bu.REGEX_PARTS.leftBoundary}innerPattern(){return LA}innerExtract(e,t){let n=Bu.parseTimeUnits(t[1]),i=FA.reverseTimeUnits(n);return NA.ParsingComponents.createRelativeFromReference(e.reference,i)}};fm.default=dm});var D_=E(uo=>{"use strict";var UA=uo&&uo.__importDefault||function(r){return r&&r.__esModule?r:{default:r}};Object.defineProperty(uo,"__esModule",{value:!0});var WA=UA(Kr()),pm=class extends WA.default{patternBetween(){return/^\s*(и до|и по|до|по|-)\s*$/i}};uo.default=pm});var x_=E(lo=>{"use strict";var qA=lo&&lo.__importDefault||function(r){return r&&r.__esModule?r:{default:r}};Object.defineProperty(lo,"__esModule",{value:!0});var $A=qA(hn()),mm=class extends $A.default{patternBetween(){return new RegExp("^\\s*(T|\u0432|,|-)?\\s*$")}};lo.default=mm});var R_=E(kn=>{"use strict";var jA=kn&&kn.__createBinding||(Object.create?function(r,e,t,n){n===void 0&&(n=t),Object.defineProperty(r,n,{enumerable:!0,get:function(){return e[t]}})}:function(r,e,t,n){n===void 0&&(n=t),r[n]=e[t]}),GA=kn&&kn.__setModuleDefault||(Object.create?function(r,e){Object.defineProperty(r,"default",{enumerable:!0,value:e})}:function(r,e){r.default=e}),YA=kn&&kn.__importStar||function(r){if(r&&r.__esModule)return r;var e={};if(r!=null)for(var t in r)t!=="default"&&Object.prototype.hasOwnProperty.call(r,t)&&jA(e,r,t);return GA(e,r),e};Object.defineProperty(kn,"__esModule",{value:!0});var BA=H(),co=YA(Tn()),hm=Lt(),HA=new RegExp(`(?:\u0441|\u0441\u043E)?\\s*(\u0441\u0435\u0433\u043E\u0434\u043D\u044F|\u0432\u0447\u0435\u0440\u0430|\u0437\u0430\u0432\u0442\u0440\u0430|\u043F\u043E\u0441\u043B\u0435\u0437\u0430\u0432\u0442\u0440\u0430|\u043F\u043E\u0437\u0430\u0432\u0447\u0435\u0440\u0430)${hm.REGEX_PARTS.rightBoundary}`,hm.REGEX_PARTS.flags),gm=class extends BA.AbstractParserWithWordBoundaryChecking{patternLeftBoundary(){return hm.REGEX_PARTS.leftBoundary}innerPattern(e){return HA}innerExtract(e,t){let n=t[1].toLowerCase(),i=e.createParsingComponents();switch(n){case"\u0441\u0435\u0433\u043E\u0434\u043D\u044F":return co.today(e.reference);case"\u0432\u0447\u0435\u0440\u0430":return co.yesterday(e.reference);case"\u0437\u0430\u0432\u0442\u0440\u0430":return co.tomorrow(e.reference);case"\u043F\u043E\u0441\u043B\u0435\u0437\u0430\u0432\u0442\u0440\u0430":return co.theDayAfter(e.reference,2);case"\u043F\u043E\u0437\u0430\u0432\u0447\u0435\u0440\u0430":return co.theDayBefore(e.reference,2)}return i}};kn.default=gm});var M_=E(Ar=>{"use strict";var VA=Ar&&Ar.__createBinding||(Object.create?function(r,e,t,n){n===void 0&&(n=t),Object.defineProperty(r,n,{enumerable:!0,get:function(){return e[t]}})}:function(r,e,t,n){n===void 0&&(n=t),r[n]=e[t]}),zA=Ar&&Ar.__setModuleDefault||(Object.create?function(r,e){Object.defineProperty(r,"default",{enumerable:!0,value:e})}:function(r,e){r.default=e}),KA=Ar&&Ar.__importStar||function(r){if(r&&r.__esModule)return r;var e={};if(r!=null)for(var t in r)t!=="default"&&Object.prototype.hasOwnProperty.call(r,t)&&VA(e,r,t);return zA(e,r),e},QA=Ar&&Ar.__importDefault||function(r){return r&&r.__esModule?r:{default:r}};Object.defineProperty(Ar,"__esModule",{value:!0});var XA=H(),Ei=KA(Tn()),ZA=ir(),JA=QA(we()),ym=Lt(),eP=new RegExp(`(\u0441\u0435\u0439\u0447\u0430\u0441|\u043F\u0440\u043E\u0448\u043B\u044B\u043C\\s*\u0432\u0435\u0447\u0435\u0440\u043E\u043C|\u043F\u0440\u043E\u0448\u043B\u043E\u0439\\s*\u043D\u043E\u0447\u044C\u044E|\u0441\u043B\u0435\u0434\u0443\u044E\u0449\u0435\u0439\\s*\u043D\u043E\u0447\u044C\u044E|\u0441\u0435\u0433\u043E\u0434\u043D\u044F\\s*\u043D\u043E\u0447\u044C\u044E|\u044D\u0442\u043E\u0439\\s*\u043D\u043E\u0447\u044C\u044E|\u043D\u043E\u0447\u044C\u044E|\u044D\u0442\u0438\u043C \u0443\u0442\u0440\u043E\u043C|\u0443\u0442\u0440\u043E\u043C|\u0443\u0442\u0440\u0430|\u0432\\s*\u043F\u043E\u043B\u0434\u0435\u043D\u044C|\u0432\u0435\u0447\u0435\u0440\u043E\u043C|\u0432\u0435\u0447\u0435\u0440\u0430|\u0432\\s*\u043F\u043E\u043B\u043D\u043E\u0447\u044C)${ym.REGEX_PARTS.rightBoundary}`,ym.REGEX_PARTS.flags),Tm=class extends XA.AbstractParserWithWordBoundaryChecking{patternLeftBoundary(){return ym.REGEX_PARTS.leftBoundary}innerPattern(){return eP}innerExtract(e,t){let n=JA.default(e.refDate),i=t[0].toLowerCase(),s=e.createParsingComponents();if(i==="\u0441\u0435\u0439\u0447\u0430\u0441")return Ei.now(e.reference);if(i==="\u0432\u0435\u0447\u0435\u0440\u043E\u043C"||i==="\u0432\u0435\u0447\u0435\u0440\u0430")return Ei.evening(e.reference);if(i.endsWith("\u0443\u0442\u0440\u043E\u043C")||i.endsWith("\u0443\u0442\u0440\u0430"))return Ei.morning(e.reference);if(i.match(/в\s*полдень/))return Ei.noon(e.reference);if(i.match(/прошлой\s*ночью/))return Ei.lastNight(e.reference);if(i.match(/прошлым\s*вечером/))return Ei.yesterdayEvening(e.reference);if(i.match(/следующей\s*ночью/)){let a=n.hour()<22?1:2;n=n.add(a,"day"),ZA.assignSimilarDate(s,n),s.imply("hour",0)}return i.match(/в\s*полночь/)||i.endsWith("\u043D\u043E\u0447\u044C\u044E")?Ei.midnight(e.reference):s}};Ar.default=Tm});var C_=E(_m=>{"use strict";Object.defineProperty(_m,"__esModule",{value:!0});var fo=Lt(),tP=Re(),rP=H(),nP=Ti(),iP=new RegExp(`(?:(?:,|\\(|\uFF08)\\s*)?(?:\u0432\\s*?)?(?:(\u044D\u0442\u0443|\u044D\u0442\u043E\u0442|\u043F\u0440\u043E\u0448\u043B\u044B\u0439|\u043F\u0440\u043E\u0448\u043B\u0443\u044E|\u0441\u043B\u0435\u0434\u0443\u044E\u0449\u0438\u0439|\u0441\u043B\u0435\u0434\u0443\u044E\u0449\u0443\u044E|\u0441\u043B\u0435\u0434\u0443\u044E\u0449\u0435\u0433\u043E)\\s*)?(${tP.matchAnyPattern(fo.WEEKDAY_DICTIONARY)})(?:\\s*(?:,|\\)|\uFF09))?(?:\\s*\u043D\u0430\\s*(\u044D\u0442\u043E\u0439|\u043F\u0440\u043E\u0448\u043B\u043E\u0439|\u0441\u043B\u0435\u0434\u0443\u044E\u0449\u0435\u0439)\\s*\u043D\u0435\u0434\u0435\u043B\u0435)?${fo.REGEX_PARTS.rightBoundary}`,fo.REGEX_PARTS.flags),sP=1,aP=2,oP=3,bm=class extends rP.AbstractParserWithWordBoundaryChecking{innerPattern(){return iP}patternLeftBoundary(){return fo.REGEX_PARTS.leftBoundary}innerExtract(e,t){let n=t[aP].toLowerCase(),i=fo.WEEKDAY_DICTIONARY[n],s=t[sP],a=t[oP],o=s||a;o=o||"",o=o.toLowerCase();let u=null;o=="\u043F\u0440\u043E\u0448\u043B\u044B\u0439"||o=="\u043F\u0440\u043E\u0448\u043B\u0443\u044E"||o=="\u043F\u0440\u043E\u0448\u043B\u043E\u0439"?u="last":o=="\u0441\u043B\u0435\u0434\u0443\u044E\u0449\u0438\u0439"||o=="\u0441\u043B\u0435\u0434\u0443\u044E\u0449\u0443\u044E"||o=="\u0441\u043B\u0435\u0434\u0443\u044E\u0449\u0435\u0439"||o=="\u0441\u043B\u0435\u0434\u0443\u044E\u0449\u0435\u0433\u043E"?u="next":(o=="\u044D\u0442\u043E\u0442"||o=="\u044D\u0442\u0443"||o=="\u044D\u0442\u043E\u0439")&&(u="this");let l=nP.toDayJSWeekday(e.refDate,i,u);return e.createParsingComponents().assign("weekday",i).imply("day",l.date()).imply("month",l.month()+1).imply("year",l.year())}};_m.default=bm});var P_=E(mo=>{"use strict";var uP=mo&&mo.__importDefault||function(r){return r&&r.__esModule?r:{default:r}};Object.defineProperty(mo,"__esModule",{value:!0});var po=Lt(),A_=We(),lP=uP(we()),cP=H(),dP=Re(),fP=new RegExp(`(\u0432 \u043F\u0440\u043E\u0448\u043B\u043E\u043C|\u043D\u0430 \u043F\u0440\u043E\u0448\u043B\u043E\u0439|\u043D\u0430 \u0441\u043B\u0435\u0434\u0443\u044E\u0449\u0435\u0439|\u0432 \u0441\u043B\u0435\u0434\u0443\u044E\u0449\u0435\u043C|\u043D\u0430 \u044D\u0442\u043E\u0439|\u0432 \u044D\u0442\u043E\u043C)\\s*(${dP.matchAnyPattern(po.TIME_UNIT_DICTIONARY)})(?=\\s*)${po.REGEX_PARTS.rightBoundary}`,po.REGEX_PARTS.flags),pP=1,mP=2,vm=class extends cP.AbstractParserWithWordBoundaryChecking{patternLeftBoundary(){return po.REGEX_PARTS.leftBoundary}innerPattern(){return fP}innerExtract(e,t){let n=t[pP].toLowerCase(),i=t[mP].toLowerCase(),s=po.TIME_UNIT_DICTIONARY[i];if(n=="\u043D\u0430 \u0441\u043B\u0435\u0434\u0443\u044E\u0449\u0435\u0439"||n=="\u0432 \u0441\u043B\u0435\u0434\u0443\u044E\u0449\u0435\u043C"){let u={};return u[s]=1,A_.ParsingComponents.createRelativeFromReference(e.reference,u)}if(n=="\u0432 \u043F\u0440\u043E\u0448\u043B\u043E\u043C"||n=="\u043D\u0430 \u043F\u0440\u043E\u0448\u043B\u043E\u0439"){let u={};return u[s]=-1,A_.ParsingComponents.createRelativeFromReference(e.reference,u)}let a=e.createParsingComponents(),o=lP.default(e.reference.instant);return s.match(/week/i)?(o=o.add(-o.get("d"),"d"),a.imply("day",o.date()),a.imply("month",o.month()+1),a.imply("year",o.year())):s.match(/month/i)?(o=o.add(-o.date()+1,"d"),a.imply("day",o.date()),a.assign("year",o.year()),a.assign("month",o.month()+1)):s.match(/year/i)&&(o=o.add(-o.date()+1,"d"),o=o.add(-o.month(),"month"),a.imply("day",o.date()),a.imply("month",o.month()+1),a.assign("year",o.year())),a}};mo.default=vm});var N_=E(km=>{"use strict";Object.defineProperty(km,"__esModule",{value:!0});var ho=Lt(),hP=We(),gP=H(),yP=sr(),TP=new RegExp(`(\u044D\u0442\u0438|\u043F\u043E\u0441\u043B\u0435\u0434\u043D\u0438\u0435|\u043F\u0440\u043E\u0448\u043B\u044B\u0435|\u0441\u043B\u0435\u0434\u0443\u044E\u0449\u0438\u0435|\u043F\u043E\u0441\u043B\u0435|\u0447\u0435\u0440\u0435\u0437|\\+|-)\\s*(${ho.TIME_UNITS_PATTERN})${ho.REGEX_PARTS.rightBoundary}`,ho.REGEX_PARTS.flags),wm=class extends gP.AbstractParserWithWordBoundaryChecking{patternLeftBoundary(){return ho.REGEX_PARTS.leftBoundary}innerPattern(){return TP}innerExtract(e,t){let n=t[1].toLowerCase(),i=ho.parseTimeUnits(t[2]);switch(n){case"\u043F\u043E\u0441\u043B\u0435\u0434\u043D\u0438\u0435":case"\u043F\u0440\u043E\u0448\u043B\u044B\u0435":case"-":i=yP.reverseTimeUnits(i);break}return hP.ParsingComponents.createRelativeFromReference(e.reference,i)}};km.default=wm});var L_=E(it=>{"use strict";var Ht=it&&it.__importDefault||function(r){return r&&r.__esModule?r:{default:r}};Object.defineProperty(it,"__esModule",{value:!0});it.createConfiguration=it.createCasualConfiguration=it.parseDate=it.parse=it.strict=it.casual=void 0;var bP=Ht(h_()),_P=Ht(__()),vP=Ht(k_()),wP=Ht(S_()),kP=Ht(O_()),EP=Ht(D_()),SP=Ht(x_()),OP=gn(),DP=Ht(R_()),xP=Ht(M_()),RP=Ht(C_()),MP=Ht(P_()),I_=Dr(),CP=Ht(bi()),AP=Ht(N_());it.casual=new I_.Chrono(F_());it.strict=new I_.Chrono(Em(!0));function PP(r,e,t){return it.casual.parse(r,e,t)}it.parse=PP;function NP(r,e,t){return it.casual.parseDate(r,e,t)}it.parseDate=NP;function F_(){let r=Em(!1);return r.parsers.unshift(new DP.default),r.parsers.unshift(new xP.default),r.parsers.unshift(new vP.default),r.parsers.unshift(new MP.default),r.parsers.unshift(new AP.default),r}it.createCasualConfiguration=F_;function Em(r=!0){return OP.includeCommonConfiguration({parsers:[new CP.default(!0),new bP.default,new _P.default,new RP.default,new wP.default(r),new kP.default],refiners:[new SP.default,new EP.default]},r)}it.createConfiguration=Em});var Ke=E(he=>{"use strict";var IP=he&&he.__createBinding||(Object.create?function(r,e,t,n){n===void 0&&(n=t),Object.defineProperty(r,n,{enumerable:!0,get:function(){return e[t]}})}:function(r,e,t,n){n===void 0&&(n=t),r[n]=e[t]}),FP=he&&he.__setModuleDefault||(Object.create?function(r,e){Object.defineProperty(r,"default",{enumerable:!0,value:e})}:function(r,e){r.default=e}),Kn=he&&he.__importStar||function(r){if(r&&r.__esModule)return r;var e={};if(r!=null)for(var t in r)t!=="default"&&Object.prototype.hasOwnProperty.call(r,t)&&IP(e,r,t);return FP(e,r),e};Object.defineProperty(he,"__esModule",{value:!0});he.parseDate=he.parse=he.casual=he.strict=he.ru=he.zh=he.nl=he.pt=he.ja=he.fr=he.de=he.Meridiem=he.Chrono=he.en=void 0;var Sm=Kn($d());he.en=Sm;var LP=Dr();Object.defineProperty(he,"Chrono",{enumerable:!0,get:function(){return LP.Chrono}});var UP;(function(r){r[r.AM=0]="AM",r[r.PM=1]="PM"})(UP=he.Meridiem||(he.Meridiem={}));var WP=Kn(Hy());he.de=WP;var qP=Kn(vT());he.fr=qP;var $P=Kn(MT());he.ja=$P;var jP=Kn(HT());he.pt=jP;var GP=Kn(Mb());he.nl=GP;var YP=Kn(c_());he.zh=YP;var BP=Kn(L_());he.ru=BP;he.strict=Sm.strict;he.casual=Sm.casual;function HP(r,e,t){return he.casual.parse(r,e,t)}he.parse=HP;function VP(r,e,t){return he.casual.parseDate(r,e,t)}he.parseDate=VP});var $v=E((Wv,qv)=>{(function(r){var e=Object.hasOwnProperty,t=Array.isArray?Array.isArray:function(h){return Object.prototype.toString.call(h)==="[object Array]"},n=10,i=typeof process=="object"&&typeof process.nextTick=="function",s=typeof Symbol=="function",a=typeof Reflect=="object",o=typeof setImmediate=="function",u=o?setImmediate:setTimeout,l=s?a&&typeof Reflect.ownKeys=="function"?Reflect.ownKeys:function(p){var h=Object.getOwnPropertyNames(p);return h.push.apply(h,Object.getOwnPropertySymbols(p)),h}:Object.keys;function c(){this._events={},this._conf&&d.call(this,this._conf)}function d(p){p&&(this._conf=p,p.delimiter&&(this.delimiter=p.delimiter),p.maxListeners!==r&&(this._maxListeners=p.maxListeners),p.wildcard&&(this.wildcard=p.wildcard),p.newListener&&(this._newListener=p.newListener),p.removeListener&&(this._removeListener=p.removeListener),p.verboseMemoryLeak&&(this.verboseMemoryLeak=p.verboseMemoryLeak),p.ignoreErrors&&(this.ignoreErrors=p.ignoreErrors),this.wildcard&&(this.listenerTree={}))}function f(p,h){var g="(node) warning: possible EventEmitter memory leak detected. "+p+" listeners added. Use emitter.setMaxListeners() to increase limit.";if(this.verboseMemoryLeak&&(g+=" Event name: "+h+"."),typeof process!="undefined"&&process.emitWarning){var T=new Error(g);T.name="MaxListenersExceededWarning",T.emitter=this,T.count=p,process.emitWarning(T)}else console.error(g),console.trace&&console.trace()}var m=function(p,h,g){var T=arguments.length;switch(T){case 0:return[];case 1:return[p];case 2:return[p,h];case 3:return[p,h,g];default:for(var w=new Array(T);T--;)w[T]=arguments[T];return w}};function y(p,h){for(var g={},T,w=p.length,O=h?h.length:0,M=0;M<w;M++)T=p[M],g[T]=M<O?h[M]:r;return g}function b(p,h,g){this._emitter=p,this._target=h,this._listeners={},this._listenersCount=0;var T,w;if((g.on||g.off)&&(T=g.on,w=g.off),h.addEventListener?(T=h.addEventListener,w=h.removeEventListener):h.addListener?(T=h.addListener,w=h.removeListener):h.on&&(T=h.on,w=h.off),!T&&!w)throw Error("target does not implement any known event API");if(typeof T!="function")throw TypeError("on method must be a function");if(typeof w!="function")throw TypeError("off method must be a function");this._on=T,this._off=w;var O=p._observers;O?O.push(this):p._observers=[this]}Object.assign(b.prototype,{subscribe:function(p,h,g){var T=this,w=this._target,O=this._emitter,M=this._listeners,P=function(){var v=m.apply(null,arguments),x={data:v,name:h,original:p};if(g){var N=g.call(w,x);N!==!1&&O.emit.apply(O,[x.name].concat(v));return}O.emit.apply(O,[h].concat(v))};if(M[p])throw Error("Event '"+p+"' is already listening");this._listenersCount++,O._newListener&&O._removeListener&&!T._onNewListener?(this._onNewListener=function(v){v===h&&M[p]===null&&(M[p]=P,T._on.call(w,p,P))},O.on("newListener",this._onNewListener),this._onRemoveListener=function(v){v===h&&!O.hasListeners(v)&&M[p]&&(M[p]=null,T._off.call(w,p,P))},M[p]=null,O.on("removeListener",this._onRemoveListener)):(M[p]=P,T._on.call(w,p,P))},unsubscribe:function(p){var h=this,g=this._listeners,T=this._emitter,w,O,M=this._off,P=this._target,v;if(p&&typeof p!="string")throw TypeError("event must be a string");function x(){h._onNewListener&&(T.off("newListener",h._onNewListener),T.off("removeListener",h._onRemoveListener),h._onNewListener=null,h._onRemoveListener=null);var N=ne.call(T,h);T._observers.splice(N,1)}if(p){if(w=g[p],!w)return;M.call(P,p,w),delete g[p],--this._listenersCount||x()}else{for(O=l(g),v=O.length;v-- >0;)p=O[v],M.call(P,p,g[p]);this._listeners={},this._listenersCount=0,x()}}});function k(p,h,g,T){var w=Object.assign({},h);if(!p)return w;if(typeof p!="object")throw TypeError("options must be an object");var O=Object.keys(p),M=O.length,P,v,x;function N(ce){throw Error('Invalid "'+P+'" option value'+(ce?". Reason: "+ce:""))}for(var ie=0;ie<M;ie++){if(P=O[ie],!T&&!e.call(h,P))throw Error('Unknown "'+P+'" option');v=p[P],v!==r&&(x=g[P],w[P]=x?x(v,N):v)}return w}function _(p,h){return(typeof p!="function"||!p.hasOwnProperty("prototype"))&&h("value must be a constructor"),p}function R(p){var h="value must be type of "+p.join("|"),g=p.length,T=p[0],w=p[1];return g===1?function(O,M){if(typeof O===T)return O;M(h)}:g===2?function(O,M){var P=typeof O;if(P===T||P===w)return O;M(h)}:function(O,M){for(var P=typeof O,v=g;v-- >0;)if(P===p[v])return O;M(h)}}var S=R(["function"]),F=R(["object","function"]);function q(p,h,g){var T,w,O=0,M,P=new p(function(v,x,N){g=k(g,{timeout:0,overload:!1},{timeout:function(Ne,Ce){return Ne*=1,(typeof Ne!="number"||Ne<0||!Number.isFinite(Ne))&&Ce("timeout must be a positive number"),Ne}}),T=!g.overload&&typeof p.prototype.cancel=="function"&&typeof N=="function";function ie(){w&&(w=null),O&&(clearTimeout(O),O=0)}var ce=function(Ne){ie(),v(Ne)},me=function(Ne){ie(),x(Ne)};T?h(ce,me,N):(w=[function(Ne){me(Ne||Error("canceled"))}],h(ce,me,function(Ne){if(M)throw Error("Unable to subscribe on cancel event asynchronously");if(typeof Ne!="function")throw TypeError("onCancel callback must be a function");w.push(Ne)}),M=!0),g.timeout>0&&(O=setTimeout(function(){var Ne=Error("timeout");Ne.code="ETIMEDOUT",O=0,P.cancel(Ne),x(Ne)},g.timeout))});return T||(P.cancel=function(v){if(!!w){for(var x=w.length,N=1;N<x;N++)w[N](v);w[0](v),w=null}}),P}function ne(p){var h=this._observers;if(!h)return-1;for(var g=h.length,T=0;T<g;T++)if(h[T]._target===p)return T;return-1}function G(p,h,g,T,w){if(!g)return null;if(T===0){var O=typeof h;if(O==="string"){var M,P,v=0,x=0,N=this.delimiter,ie=N.length;if((P=h.indexOf(N))!==-1){M=new Array(5);do M[v++]=h.slice(x,P),x=P+ie;while((P=h.indexOf(N,x))!==-1);M[v++]=h.slice(x),h=M,w=v}else h=[h],w=1}else O==="object"?w=h.length:(h=[h],w=1)}var ce=null,me,Ne,Ce,Nt,fn,Br=h[T],ot=h[T+1],Et,Ge;if(T===w)g._listeners&&(typeof g._listeners=="function"?(p&&p.push(g._listeners),ce=[g]):(p&&p.push.apply(p,g._listeners),ce=[g]));else if(Br==="*"){for(Et=l(g),P=Et.length;P-- >0;)me=Et[P],me!=="_listeners"&&(Ge=G(p,h,g[me],T+1,w),Ge&&(ce?ce.push.apply(ce,Ge):ce=Ge));return ce}else if(Br==="**"){for(fn=T+1===w||T+2===w&&ot==="*",fn&&g._listeners&&(ce=G(p,h,g,w,w)),Et=l(g),P=Et.length;P-- >0;)me=Et[P],me!=="_listeners"&&(me==="*"||me==="**"?(g[me]._listeners&&!fn&&(Ge=G(p,h,g[me],w,w),Ge&&(ce?ce.push.apply(ce,Ge):ce=Ge)),Ge=G(p,h,g[me],T,w)):me===ot?Ge=G(p,h,g[me],T+2,w):Ge=G(p,h,g[me],T,w),Ge&&(ce?ce.push.apply(ce,Ge):ce=Ge));return ce}else g[Br]&&(ce=G(p,h,g[Br],T+1,w));if(Ne=g["*"],Ne&&G(p,h,Ne,T+1,w),Ce=g["**"],Ce)if(T<w)for(Ce._listeners&&G(p,h,Ce,w,w),Et=l(Ce),P=Et.length;P-- >0;)me=Et[P],me!=="_listeners"&&(me===ot?G(p,h,Ce[me],T+2,w):me===Br?G(p,h,Ce[me],T+1,w):(Nt={},Nt[me]=Ce[me],G(p,h,{"**":Nt},T+1,w)));else Ce._listeners?G(p,h,Ce,w,w):Ce["*"]&&Ce["*"]._listeners&&G(p,h,Ce["*"],w,w);return ce}function B(p,h,g){var T=0,w=0,O,M=this.delimiter,P=M.length,v;if(typeof p=="string")if((O=p.indexOf(M))!==-1){v=new Array(5);do v[T++]=p.slice(w,O),w=O+P;while((O=p.indexOf(M,w))!==-1);v[T++]=p.slice(w)}else v=[p],T=1;else v=p,T=p.length;if(T>1){for(O=0;O+1<T;O++)if(v[O]==="**"&&v[O+1]==="**")return}var x=this.listenerTree,N;for(O=0;O<T;O++)if(N=v[O],x=x[N]||(x[N]={}),O===T-1)return x._listeners?(typeof x._listeners=="function"&&(x._listeners=[x._listeners]),g?x._listeners.unshift(h):x._listeners.push(h),!x._listeners.warned&&this._maxListeners>0&&x._listeners.length>this._maxListeners&&(x._listeners.warned=!0,f.call(this,x._listeners.length,N))):x._listeners=h,!0;return!0}function ge(p,h,g,T){for(var w=l(p),O=w.length,M,P,v,x=p._listeners,N;O-- >0;)P=w[O],M=p[P],P==="_listeners"?v=g:v=g?g.concat(P):[P],N=T||typeof P=="symbol",x&&h.push(N?v:v.join(this.delimiter)),typeof M=="object"&&ge.call(this,M,h,v,N);return h}function Pe(p){for(var h=l(p),g=h.length,T,w,O;g-- >0;)w=h[g],T=p[w],T&&(O=!0,w!=="_listeners"&&!Pe(T)&&delete p[w]);return O}function j(p,h,g){this.emitter=p,this.event=h,this.listener=g}j.prototype.off=function(){return this.emitter.off(this.event,this.listener),this};function $(p,h,g){if(g===!0)w=!0;else if(g===!1)T=!0;else{if(!g||typeof g!="object")throw TypeError("options should be an object or true");var T=g.async,w=g.promisify,O=g.nextTick,M=g.objectify}if(T||O||w){var P=h,v=h._origin||h;if(O&&!i)throw Error("process.nextTick is not supported");w===r&&(w=h.constructor.name==="AsyncFunction"),h=function(){var x=arguments,N=this,ie=this.event;return w?O?Promise.resolve():new Promise(function(ce){u(ce)}).then(function(){return N.event=ie,P.apply(N,x)}):(O?process.nextTick:u)(function(){N.event=ie,P.apply(N,x)})},h._async=!0,h._origin=v}return[h,M?new j(this,p,h):this]}function D(p){this._events={},this._newListener=!1,this._removeListener=!1,this.verboseMemoryLeak=!1,d.call(this,p)}D.EventEmitter2=D,D.prototype.listenTo=function(p,h,g){if(typeof p!="object")throw TypeError("target musts be an object");var T=this;g=k(g,{on:r,off:r,reducers:r},{on:S,off:S,reducers:F});function w(O){if(typeof O!="object")throw TypeError("events must be an object");var M=g.reducers,P=ne.call(T,p),v;P===-1?v=new b(T,p,g):v=T._observers[P];for(var x=l(O),N=x.length,ie,ce=typeof M=="function",me=0;me<N;me++)ie=x[me],v.subscribe(ie,O[ie]||ie,ce?M:M&&M[ie])}return t(h)?w(y(h)):w(typeof h=="string"?y(h.split(/\s+/)):h),this},D.prototype.stopListeningTo=function(p,h){var g=this._observers;if(!g)return!1;var T=g.length,w,O=!1;if(p&&typeof p!="object")throw TypeError("target should be an object");for(;T-- >0;)w=g[T],(!p||w._target===p)&&(w.unsubscribe(h),O=!0);return O},D.prototype.delimiter=".",D.prototype.setMaxListeners=function(p){p!==r&&(this._maxListeners=p,this._conf||(this._conf={}),this._conf.maxListeners=p)},D.prototype.getMaxListeners=function(){return this._maxListeners},D.prototype.event="",D.prototype.once=function(p,h,g){return this._once(p,h,!1,g)},D.prototype.prependOnceListener=function(p,h,g){return this._once(p,h,!0,g)},D.prototype._once=function(p,h,g,T){return this._many(p,1,h,g,T)},D.prototype.many=function(p,h,g,T){return this._many(p,h,g,!1,T)},D.prototype.prependMany=function(p,h,g,T){return this._many(p,h,g,!0,T)},D.prototype._many=function(p,h,g,T,w){var O=this;if(typeof g!="function")throw new Error("many only accepts instances of Function");function M(){return--h===0&&O.off(p,M),g.apply(this,arguments)}return M._origin=g,this._on(p,M,T,w)},D.prototype.emit=function(){if(!this._events&&!this._all)return!1;this._events||c.call(this);var p=arguments[0],h,g=this.wildcard,T,w,O,M,P;if(p==="newListener"&&!this._newListener&&!this._events.newListener)return!1;if(g&&(h=p,p!=="newListener"&&p!=="removeListener"&&typeof p=="object")){if(w=p.length,s){for(O=0;O<w;O++)if(typeof p[O]=="symbol"){P=!0;break}}P||(p=p.join(this.delimiter))}var v=arguments.length,x;if(this._all&&this._all.length)for(x=this._all.slice(),O=0,w=x.length;O<w;O++)switch(this.event=p,v){case 1:x[O].call(this,p);break;case 2:x[O].call(this,p,arguments[1]);break;case 3:x[O].call(this,p,arguments[1],arguments[2]);break;default:x[O].apply(this,arguments)}if(g)x=[],G.call(this,x,h,this.listenerTree,0,w);else if(x=this._events[p],typeof x=="function"){switch(this.event=p,v){case 1:x.call(this);break;case 2:x.call(this,arguments[1]);break;case 3:x.call(this,arguments[1],arguments[2]);break;default:for(T=new Array(v-1),M=1;M<v;M++)T[M-1]=arguments[M];x.apply(this,T)}return!0}else x&&(x=x.slice());if(x&&x.length){if(v>3)for(T=new Array(v-1),M=1;M<v;M++)T[M-1]=arguments[M];for(O=0,w=x.length;O<w;O++)switch(this.event=p,v){case 1:x[O].call(this);break;case 2:x[O].call(this,arguments[1]);break;case 3:x[O].call(this,arguments[1],arguments[2]);break;default:x[O].apply(this,T)}return!0}else if(!this.ignoreErrors&&!this._all&&p==="error")throw arguments[1]instanceof Error?arguments[1]:new Error("Uncaught, unspecified 'error' event.");return!!this._all},D.prototype.emitAsync=function(){if(!this._events&&!this._all)return!1;this._events||c.call(this);var p=arguments[0],h=this.wildcard,g,T,w,O,M,P;if(p==="newListener"&&!this._newListener&&!this._events.newListener)return Promise.resolve([!1]);if(h&&(g=p,p!=="newListener"&&p!=="removeListener"&&typeof p=="object")){if(O=p.length,s){for(M=0;M<O;M++)if(typeof p[M]=="symbol"){T=!0;break}}T||(p=p.join(this.delimiter))}var v=[],x=arguments.length,N;if(this._all)for(M=0,O=this._all.length;M<O;M++)switch(this.event=p,x){case 1:v.push(this._all[M].call(this,p));break;case 2:v.push(this._all[M].call(this,p,arguments[1]));break;case 3:v.push(this._all[M].call(this,p,arguments[1],arguments[2]));break;default:v.push(this._all[M].apply(this,arguments))}if(h?(N=[],G.call(this,N,g,this.listenerTree,0)):N=this._events[p],typeof N=="function")switch(this.event=p,x){case 1:v.push(N.call(this));break;case 2:v.push(N.call(this,arguments[1]));break;case 3:v.push(N.call(this,arguments[1],arguments[2]));break;default:for(w=new Array(x-1),P=1;P<x;P++)w[P-1]=arguments[P];v.push(N.apply(this,w))}else if(N&&N.length){if(N=N.slice(),x>3)for(w=new Array(x-1),P=1;P<x;P++)w[P-1]=arguments[P];for(M=0,O=N.length;M<O;M++)switch(this.event=p,x){case 1:v.push(N[M].call(this));break;case 2:v.push(N[M].call(this,arguments[1]));break;case 3:v.push(N[M].call(this,arguments[1],arguments[2]));break;default:v.push(N[M].apply(this,w))}}else if(!this.ignoreErrors&&!this._all&&p==="error")return arguments[1]instanceof Error?Promise.reject(arguments[1]):Promise.reject("Uncaught, unspecified 'error' event.");return Promise.all(v)},D.prototype.on=function(p,h,g){return this._on(p,h,!1,g)},D.prototype.prependListener=function(p,h,g){return this._on(p,h,!0,g)},D.prototype.onAny=function(p){return this._onAny(p,!1)},D.prototype.prependAny=function(p){return this._onAny(p,!0)},D.prototype.addListener=D.prototype.on,D.prototype._onAny=function(p,h){if(typeof p!="function")throw new Error("onAny only accepts instances of Function");return this._all||(this._all=[]),h?this._all.unshift(p):this._all.push(p),this},D.prototype._on=function(p,h,g,T){if(typeof p=="function")return this._onAny(p,h),this;if(typeof h!="function")throw new Error("on only accepts instances of Function");this._events||c.call(this);var w=this,O;return T!==r&&(O=$.call(this,p,h,T),h=O[0],w=O[1]),this._newListener&&this.emit("newListener",p,h),this.wildcard?(B.call(this,p,h,g),w):(this._events[p]?(typeof this._events[p]=="function"&&(this._events[p]=[this._events[p]]),g?this._events[p].unshift(h):this._events[p].push(h),!this._events[p].warned&&this._maxListeners>0&&this._events[p].length>this._maxListeners&&(this._events[p].warned=!0,f.call(this,this._events[p].length,p))):this._events[p]=h,w)},D.prototype.off=function(p,h){if(typeof h!="function")throw new Error("removeListener only takes instances of Function");var g,T=[];if(this.wildcard){var w=typeof p=="string"?p.split(this.delimiter):p.slice();if(T=G.call(this,null,w,this.listenerTree,0),!T)return this}else{if(!this._events[p])return this;g=this._events[p],T.push({_listeners:g})}for(var O=0;O<T.length;O++){var M=T[O];if(g=M._listeners,t(g)){for(var P=-1,v=0,x=g.length;v<x;v++)if(g[v]===h||g[v].listener&&g[v].listener===h||g[v]._origin&&g[v]._origin===h){P=v;break}if(P<0)continue;return this.wildcard?M._listeners.splice(P,1):this._events[p].splice(P,1),g.length===0&&(this.wildcard?delete M._listeners:delete this._events[p]),this._removeListener&&this.emit("removeListener",p,h),this}else(g===h||g.listener&&g.listener===h||g._origin&&g._origin===h)&&(this.wildcard?delete M._listeners:delete this._events[p],this._removeListener&&this.emit("removeListener",p,h))}return this.listenerTree&&Pe(this.listenerTree),this},D.prototype.offAny=function(p){var h=0,g=0,T;if(p&&this._all&&this._all.length>0){for(T=this._all,h=0,g=T.length;h<g;h++)if(p===T[h])return T.splice(h,1),this._removeListener&&this.emit("removeListenerAny",p),this}else{if(T=this._all,this._removeListener)for(h=0,g=T.length;h<g;h++)this.emit("removeListenerAny",T[h]);this._all=[]}return this},D.prototype.removeListener=D.prototype.off,D.prototype.removeAllListeners=function(p){if(p===r)return!this._events||c.call(this),this;if(this.wildcard){var h=G.call(this,null,p,this.listenerTree,0),g,T;if(!h)return this;for(T=0;T<h.length;T++)g=h[T],g._listeners=null;this.listenerTree&&Pe(this.listenerTree)}else this._events&&(this._events[p]=null);return this},D.prototype.listeners=function(p){var h=this._events,g,T,w,O,M;if(p===r){if(this.wildcard)throw Error("event name required for wildcard emitter");if(!h)return[];for(g=l(h),O=g.length,w=[];O-- >0;)T=h[g[O]],typeof T=="function"?w.push(T):w.push.apply(w,T);return w}else{if(this.wildcard){if(M=this.listenerTree,!M)return[];var P=[],v=typeof p=="string"?p.split(this.delimiter):p.slice();return G.call(this,P,v,M,0),P}return h?(T=h[p],T?typeof T=="function"?[T]:T:[]):[]}},D.prototype.eventNames=function(p){var h=this._events;return this.wildcard?ge.call(this,this.listenerTree,[],null,p):h?l(h):[]},D.prototype.listenerCount=function(p){return this.listeners(p).length},D.prototype.hasListeners=function(p){if(this.wildcard){var h=[],g=typeof p=="string"?p.split(this.delimiter):p.slice();return G.call(this,h,g,this.listenerTree,0),h.length>0}var T=this._events,w=this._all;return!!(w&&w.length||T&&(p===r?l(T).length:T[p]))},D.prototype.listenersAny=function(){return this._all?this._all:[]},D.prototype.waitFor=function(p,h){var g=this,T=typeof h;return T==="number"?h={timeout:h}:T==="function"&&(h={filter:h}),h=k(h,{timeout:0,filter:r,handleError:!1,Promise,overload:!1},{filter:S,Promise:_}),q(h.Promise,function(w,O,M){function P(){var v=h.filter;if(!(v&&!v.apply(g,arguments)))if(g.off(p,P),h.handleError){var x=arguments[0];x?O(x):w(m.apply(null,arguments).slice(1))}else w(m.apply(null,arguments))}M(function(){g.off(p,P)}),g._on(p,P,!1)},{timeout:h.timeout,overload:h.overload})};function Y(p,h,g){g=k(g,{Promise,timeout:0,overload:!1},{Promise:_});var T=g.Promise;return q(T,function(w,O,M){var P;if(typeof p.addEventListener=="function"){P=function(){w(m.apply(null,arguments))},M(function(){p.removeEventListener(h,P)}),p.addEventListener(h,P,{once:!0});return}var v=function(){x&&p.removeListener("error",x),w(m.apply(null,arguments))},x;h!=="error"&&(x=function(N){p.removeListener(h,v),O(N)},p.once("error",x)),M(function(){x&&p.removeListener("error",x),p.removeListener(h,v)}),p.once(h,v)},{timeout:g.timeout,overload:g.overload})}var I=D.prototype;if(Object.defineProperties(D,{defaultMaxListeners:{get:function(){return I._maxListeners},set:function(p){if(typeof p!="number"||p<0||Number.isNaN(p))throw TypeError("n must be a non-negative number");I._maxListeners=p},enumerable:!0},once:{value:Y,writable:!0,configurable:!0}}),Object.defineProperties(I,{_maxListeners:{value:n,writable:!0,configurable:!0},_observers:{value:null,writable:!0,configurable:!0}}),typeof define=="function"&&define.amd)define(function(){return D});else if(typeof Wv=="object")qv.exports=D;else{var J=new Function("","return this")();J.EventEmitter2=D}})()});var Kw=E(wh=>{"use strict";Object.defineProperty(wh,"__esModule",{value:!0});var Vw=Symbol("MustacheDataPath");function Hw({target:r,propertyName:e}){return[...r[Vw]||[],e]}function zw(r,e){return typeof r!="object"?r:new Proxy(r,{get(t,n){let i=t[n];if(i===void 0&&!(n in t)){let s=Hw({target:t,propertyName:n});if(e!=null&&e.handleError)return e.handleError(s),i;throw Error(`Missing Mustache data property: ${s.join(" > ")}`)}return i&&typeof i=="object"?(i[Vw]=Hw({target:t,propertyName:n}),zw(i,e)):i}})}wh.default=zw});var on=E(qr=>{"use strict";qr.__esModule=!0;qr.Tokens=qr.StructuralCharacters=qr.Operators=void 0;var kF;(function(r){r.AND="AND",r.OR="OR",r.XOR="XOR",r.NOT="NOT"})(kF=qr.Operators||(qr.Operators={}));var EF;(function(r){r.OPEN_PARENTHESIS="(",r.CLOSE_PARENTHESIS=")"})(EF=qr.StructuralCharacters||(qr.StructuralCharacters={}));var SF;(function(r){r.IDENTIFIER="IDENTIFIER",r.OPERATOR="OPERATOR",r.STRUCTURAL_CHARACTER="STRUCTURAL_CHARACTER",r.EOF="EOF",r.COMMENT="COMMENT"})(SF=qr.Tokens||(qr.Tokens={}))});var Rh=E(Gs=>{"use strict";Gs.__esModule=!0;Gs.VALID_TOKENS=Gs.OPERATOR_PRECEDENCE=void 0;var qe=on();Gs.OPERATOR_PRECEDENCE={NOT:0,XOR:1,AND:2,OR:3};Gs.VALID_TOKENS={identifierOnly:[{name:qe.Tokens.IDENTIFIER},{name:qe.Tokens.STRUCTURAL_CHARACTER,value:qe.StructuralCharacters.OPEN_PARENTHESIS}],identifierOrNot:[{name:qe.Tokens.IDENTIFIER},{name:qe.Tokens.STRUCTURAL_CHARACTER,value:qe.StructuralCharacters.OPEN_PARENTHESIS},{name:qe.Tokens.OPERATOR,value:qe.Operators.NOT}],binaryOperator:[{name:qe.Tokens.OPERATOR,value:qe.Operators.AND},{name:qe.Tokens.OPERATOR,value:qe.Operators.OR},{name:qe.Tokens.OPERATOR,value:qe.Operators.XOR}],binaryOperatorOrClose:[{name:qe.Tokens.OPERATOR,value:qe.Operators.AND},{name:qe.Tokens.OPERATOR,value:qe.Operators.OR},{name:qe.Tokens.OPERATOR,value:qe.Operators.XOR},{name:qe.Tokens.STRUCTURAL_CHARACTER,value:qe.StructuralCharacters.CLOSE_PARENTHESIS}]}});var Mh=E(wt=>{"use strict";wt.__esModule=!0;wt.ESCAPE_CHARACTER=wt.EOL=wt.COMMENT_DELIMITER=wt.QUOTED_IDENTIFIER_DELIMITER=wt.SEPARATORS=wt.OPERATORS=wt.STRUCTURAL_CHARACTERS=void 0;var Ys=on();wt.STRUCTURAL_CHARACTERS={"(":Ys.StructuralCharacters.OPEN_PARENTHESIS,")":Ys.StructuralCharacters.CLOSE_PARENTHESIS};wt.OPERATORS={AND:Ys.Operators.AND,OR:Ys.Operators.OR,XOR:Ys.Operators.XOR,NOT:Ys.Operators.NOT};wt.SEPARATORS=new Set([32,9,10,13].map(function(r){return String.fromCodePoint(r)}));wt.QUOTED_IDENTIFIER_DELIMITER=String.fromCodePoint(34);wt.COMMENT_DELIMITER=String.fromCodePoint(35);wt.EOL=String.fromCodePoint(10);wt.ESCAPE_CHARACTER=String.fromCodePoint(92)});var tk=E(wr=>{"use strict";var Ch=wr&&wr.__assign||function(){return Ch=Object.assign||function(r){for(var e,t=1,n=arguments.length;t<n;t++){e=arguments[t];for(var i in e)Object.prototype.hasOwnProperty.call(e,i)&&(r[i]=e[i])}return r},Ch.apply(this,arguments)};wr.__esModule=!0;wr.getQuotedIdentifier=wr.getComment=wr.createResult=void 0;var ek=on(),Hi=Mh(),OF=function(r,e,t){return{token:Ch({name:r},e!==null?{value:e}:{}),remainingString:t}};wr.createResult=OF;var DF=function(r){for(var e=r.length,t=0;t<r.length;t+=1){var n=r[t];if(n===Hi.EOL){e=t;break}}return(0,wr.createResult)(ek.Tokens.COMMENT,r.slice(0,e),r.slice(e+1))};wr.getComment=DF;var xF=function(r){for(var e=!1,t="",n=null,i=0;i<r.length;i+=1){var s=r[i];if(n===null)s===Hi.QUOTED_IDENTIFIER_DELIMITER?e?(t=t.slice(0,-1)+Hi.QUOTED_IDENTIFIER_DELIMITER,e=!1):n=i:(s===Hi.ESCAPE_CHARACTER?e=!0:e=!1,t=t+=s);else{if(!Hi.SEPARATORS.has(s)&&!Hi.STRUCTURAL_CHARACTERS[s])throw new Error("Unexpected character: ".concat(s," Expected ) character or separator"));break}}if(n===null)throw new Error("Unexpected end of expression: expected ".concat(Hi.QUOTED_IDENTIFIER_DELIMITER," character"));return(0,wr.createResult)(ek.Tokens.IDENTIFIER,t,r.slice(n+1))};wr.getQuotedIdentifier=xF});var rk=E(ql=>{"use strict";ql.__esModule=!0;ql.lex=void 0;var Bs=on(),Jt=Mh(),Hs=tk(),RF=function(r){for(var e=null,t=null,n=null,i=0;i<r.length;i+=1){var s=r[i];if(e===null){if(!Jt.SEPARATORS.has(s)){var a=Jt.STRUCTURAL_CHARACTERS[s];if(a){var o=r[i+1];if(a===Bs.StructuralCharacters.CLOSE_PARENTHESIS&&o&&!Jt.SEPARATORS.has(o)&&o!==Bs.StructuralCharacters.CLOSE_PARENTHESIS)throw new Error("Unexpected character: ".concat(o,". A closing parenthesis should be followed by another closing parenthesis or whitespace"));return(0,Hs.createResult)(Bs.Tokens.STRUCTURAL_CHARACTER,Jt.STRUCTURAL_CHARACTERS[s],r.slice(i+1))}if(s===Jt.QUOTED_IDENTIFIER_DELIMITER)return(0,Hs.getQuotedIdentifier)(r.slice(i+1));if(s===Jt.COMMENT_DELIMITER)return(0,Hs.getComment)(r.slice(i+1));e=i}}else if(Jt.SEPARATORS.has(s)||Jt.STRUCTURAL_CHARACTERS[s]){t=i,n=s;break}else if(s===Jt.QUOTED_IDENTIFIER_DELIMITER||s===Jt.COMMENT_DELIMITER)throw new Error("Unexpected character: ".concat(s))}if(e!==null){t=t!=null?t:r.length;var u=r.slice(e,t),l=r.slice(t);if(Jt.OPERATORS[u]){if(n&&!Jt.SEPARATORS.has(n))throw new Error("Unexpected character: ".concat(n,". Operators should be separated using whitespace"));return(0,Hs.createResult)(Bs.Tokens.OPERATOR,Jt.OPERATORS[u],l)}else return(0,Hs.createResult)(Bs.Tokens.IDENTIFIER,u,l)}return(0,Hs.createResult)(Bs.Tokens.EOF,null,"")};ql.lex=RF});var ik=E(er=>{"use strict";var nk=er&&er.__spreadArray||function(r,e,t){if(t||arguments.length===2)for(var n=0,i=e.length,s;n<i;n++)(s||!(n in e))&&(s||(s=Array.prototype.slice.call(e,0,n)),s[n]=e[n]);return r.concat(s||Array.prototype.slice.call(e))};er.__esModule=!0;er.validateToken=er.previousOperatorTakesPrecedent=er.getValue=er.newTokenGenerator=void 0;var MF=rk(),Vs=on(),$l=Rh(),CF=function(r){var e=r;return function(t,n){for(n===void 0&&(n=!1);;){var i=(0,MF.lex)(e),s=i.token,a=i.remainingString;if(e=a,s.name!==Vs.Tokens.COMMENT)return(0,er.validateToken)(s,t,n),s}}};er.newTokenGenerator=CF;var AF=function(r,e){var t=r($l.VALID_TOKENS.identifierOrNot),n=t.value===Vs.Operators.NOT;n&&(t=r($l.VALID_TOKENS.identifierOnly));var i=t.name===Vs.Tokens.STRUCTURAL_CHARACTER?e(r,!0):[t];return n?nk(nk([],i,!0),[{name:Vs.Tokens.OPERATOR,value:Vs.Operators.NOT}],!1):i};er.getValue=AF;var PF=function(r,e){return $l.OPERATOR_PRECEDENCE[r]<=$l.OPERATOR_PRECEDENCE[e]};er.previousOperatorTakesPrecedent=PF;var NF=function(r,e,t){if(t===void 0&&(t=!1),r.name===Vs.Tokens.EOF){if(t)return;throw new Error("Unexpected end of expression")}for(var n=0,i=e;n<i.length;n++){var s=i[n];if(s.name===r.name&&(!s.value||s.value===r.value))return}throw new TypeError("Invalid token")};er.validateToken=NF});var Ph=E(zs=>{"use strict";var un=zs&&zs.__spreadArray||function(r,e,t){if(t||arguments.length===2)for(var n=0,i=e.length,s;n<i;n++)(s||!(n in e))&&(s||(s=Array.prototype.slice.call(e,0,n)),s[n]=e[n]);return r.concat(s||Array.prototype.slice.call(e))};zs.__esModule=!0;zs.parse=void 0;var sk=on(),ak=Rh(),jl=ik(),IF=function(r){if(typeof r!="string")throw new Error("Expected string but received ".concat(typeof r));var e=(0,jl.newTokenGenerator)(r);return Ah(e)};zs.parse=IF;var Ah=function(r,e){e===void 0&&(e=!1);for(var t=un([],(0,jl.getValue)(r,Ah),!0),n=[];;){var i=e?ak.VALID_TOKENS.binaryOperatorOrClose:ak.VALID_TOKENS.binaryOperator,s=r(i,!e);if(s.name===sk.Tokens.EOF||s.name===sk.Tokens.STRUCTURAL_CHARACTER)return un(un([],t,!0),un([],n,!0).reverse(),!0);for(;n.length;){var a=n[n.length-1]||null;if(a&&(0,jl.previousOperatorTakesPrecedent)(a.value,s.value))t=un(un([],t,!0),[a],!1),n=n.slice(0,-1);else break}n=un(un([],n,!0),[s],!1),t=un(un([],t,!0),(0,jl.getValue)(r,Ah),!0)}}});var Nh=E(kt=>{"use strict";kt.__esModule=!0;kt.throwInvalidExpression=kt.isOperator=kt.isIdentifier=kt.notUtil=kt.xorUtil=kt.orUtil=kt.andUtil=void 0;var ok=on(),FF=function(r,e){return r&&e};kt.andUtil=FF;var LF=function(r,e){return r||e};kt.orUtil=LF;var UF=function(r,e){return r!==e};kt.xorUtil=UF;var WF=function(r){return!r};kt.notUtil=WF;var qF=function(r){var e=r.name,t=r.value;return e===ok.Tokens.IDENTIFIER&&typeof t=="string"};kt.isIdentifier=qF;var $F=function(r){var e=r.name,t=r.value;return e===ok.Tokens.OPERATOR&&typeof t=="string"};kt.isOperator=$F;var jF=function(r){throw new TypeError("Invalid postfix expression: ".concat(r))};kt.throwInvalidExpression=jF});var uk=E(Gl=>{"use strict";var Go;Gl.__esModule=!0;Gl.OPERATOR_MAP=void 0;var Ih=on(),Fh=Nh();Gl.OPERATOR_MAP=(Go={},Go[Ih.Operators.AND]=Fh.andUtil,Go[Ih.Operators.OR]=Fh.orUtil,Go[Ih.Operators.XOR]=Fh.xorUtil,Go)});var ck=E(kr=>{"use strict";var Ks=kr&&kr.__spreadArray||function(r,e,t){if(t||arguments.length===2)for(var n=0,i=e.length,s;n<i;n++)(s||!(n in e))&&(s||(s=Array.prototype.slice.call(e,0,n)),s[n]=e[n]);return r.concat(s||Array.prototype.slice.call(e))};kr.__esModule=!0;kr.evaluateExpression=kr.evaluate=kr.getEvaluator=void 0;var GF=Ph(),lk=on(),YF=uk(),Vi=Nh(),BF=function(r){var e=(0,GF.parse)(r);return function(t){return(0,kr.evaluate)(e,t)}};kr.getEvaluator=BF;var HF=function(r,e){if(!Array.isArray(r))throw new Error("".concat(r," should be an array. evaluate takes in a parsed expression. Use in combination with parse or use getEvaluator"));var t=r.reduce(function(n,i,s){if(!(i&&((0,Vi.isIdentifier)(i)||(0,Vi.isOperator)(i))))throw new Error("Invalid token: ".concat(i,". Found in parsed expression at index ").concat(s));if(i.name===lk.Tokens.IDENTIFIER)return Ks(Ks([],n,!0),[Boolean(e[i.value])],!1);var a=n[n.length-2],o=n[n.length-1];if(i.value===lk.Operators.NOT)return o===void 0&&(0,Vi.throwInvalidExpression)("missing identifier"),Ks(Ks([],n.slice(0,-1),!0),[(0,Vi.notUtil)(o)],!1);(o===void 0||a===void 0)&&(0,Vi.throwInvalidExpression)("missing identifier");var u=YF.OPERATOR_MAP[i.value];return u||(0,Vi.throwInvalidExpression)("unknown operator"),Ks(Ks([],n.slice(0,-2),!0),[u(a,o)],!1)},[]);return t.length!==1&&(0,Vi.throwInvalidExpression)("too many identifiers after evaluation"),t[0]};kr.evaluate=HF;var VF=function(r,e){return(0,kr.getEvaluator)(r)(e)};kr.evaluateExpression=VF});var fk=E(ln=>{"use strict";var Lh=ln&&ln.__createBinding||(Object.create?function(r,e,t,n){n===void 0&&(n=t);var i=Object.getOwnPropertyDescriptor(e,t);(!i||("get"in i?!e.__esModule:i.writable||i.configurable))&&(i={enumerable:!0,get:function(){return e[t]}}),Object.defineProperty(r,n,i)}:function(r,e,t,n){n===void 0&&(n=t),r[n]=e[t]});ln.__esModule=!0;ln.parse=ln.evaluate=ln.getEvaluator=void 0;var dk=ck();Lh(ln,dk,"getEvaluator");Lh(ln,dk,"evaluate");var zF=Ph();Lh(ln,zF,"parse")});var pL={};OE(pL,{default:()=>vc});module.exports=DE(pL);var sE=require("obsidian");var ks=require("obsidian");var hL=new Error("timeout while waiting for mutex to become available"),gL=new Error("mutex already locked"),xE=new Error("request for lock canceled"),RE=function(r,e,t,n){function i(s){return s instanceof t?s:new t(function(a){a(s)})}return new(t||(t=Promise))(function(s,a){function o(c){try{l(n.next(c))}catch(d){a(d)}}function u(c){try{l(n.throw(c))}catch(d){a(d)}}function l(c){c.done?s(c.value):i(c.value).then(o,u)}l((n=n.apply(r,e||[])).next())})},Ic=class{constructor(e,t=xE){this._value=e,this._cancelError=t,this._weightedQueues=[],this._weightedWaiters=[]}acquire(e=1){if(e<=0)throw new Error(`invalid weight ${e}: must be positive`);return new Promise((t,n)=>{this._weightedQueues[e-1]||(this._weightedQueues[e-1]=[]),this._weightedQueues[e-1].push({resolve:t,reject:n}),this._dispatch()})}runExclusive(e,t=1){return RE(this,void 0,void 0,function*(){let[n,i]=yield this.acquire(t);try{return yield e(n)}finally{i()}})}waitForUnlock(e=1){if(e<=0)throw new Error(`invalid weight ${e}: must be positive`);return new Promise(t=>{this._weightedWaiters[e-1]||(this._weightedWaiters[e-1]=[]),this._weightedWaiters[e-1].push(t),this._dispatch()})}isLocked(){return this._value<=0}getValue(){return this._value}setValue(e){this._value=e,this._dispatch()}release(e=1){if(e<=0)throw new Error(`invalid weight ${e}: must be positive`);this._value+=e,this._dispatch()}cancel(){this._weightedQueues.forEach(e=>e.forEach(t=>t.reject(this._cancelError))),this._weightedQueues=[]}_dispatch(){var e;for(let t=this._value;t>0;t--){let n=(e=this._weightedQueues[t-1])===null||e===void 0?void 0:e.shift();if(!n)continue;let i=this._value,s=t;this._value-=t,t=this._value+1,n.resolve([i,this._newReleaser(s)])}this._drainUnlockWaiters()}_newReleaser(e){let t=!1;return()=>{t||(t=!0,this.release(e))}}_drainUnlockWaiters(){for(let e=this._value;e>0;e--)!this._weightedWaiters[e-1]||(this._weightedWaiters[e-1].forEach(t=>t()),this._weightedWaiters[e-1]=[])}},ME=function(r,e,t,n){function i(s){return s instanceof t?s:new t(function(a){a(s)})}return new(t||(t=Promise))(function(s,a){function o(c){try{l(n.next(c))}catch(d){a(d)}}function u(c){try{l(n.throw(c))}catch(d){a(d)}}function l(c){c.done?s(c.value):i(c.value).then(o,u)}l((n=n.apply(r,e||[])).next())})},Jo=class{constructor(e){this._semaphore=new Ic(1,e)}acquire(){return ME(this,void 0,void 0,function*(){let[,e]=yield this._semaphore.acquire();return e})}runExclusive(e){return this._semaphore.runExclusive(()=>e())}isLocked(){return this._semaphore.isLocked()}waitForUnlock(){return this._semaphore.waitForUnlock()}release(){this._semaphore.isLocked()&&this._semaphore.release()}cancel(){return this._semaphore.cancel()}};var eu=require("obsidian"),ut=class{constructor(e,t={}){this._frontmatter={tags:[]};this._tags=[];var i,s;this._path=e,this._cachedMetadata=t;let n=t.frontmatter;if(n!==void 0&&(this._frontmatter=JSON.parse(JSON.stringify(n)),this._frontmatter.tags=(i=(0,eu.parseFrontMatterTags)(n))!=null?i:[]),Object.keys(t).length!==0){let a=(s=(0,eu.getAllTags)(this.cachedMetadata))!=null?s:[];this._tags=[...new Set(a)]}}get path(){return this._path}get tags(){return this._tags}get cachedMetadata(){return this._cachedMetadata}get frontmatter(){return this._frontmatter}rawFrontmatterIdenticalTo(e){let t=this.cachedMetadata.frontmatter,n=e.cachedMetadata.frontmatter;return t===n?!0:!t||!n?!1:JSON.stringify(t)===JSON.stringify(n)}get pathWithoutExtension(){return this.withoutExtension(this.path)}withoutExtension(e){return e.replace(/\.md$/,"")}get root(){let e=this.path.replace(/\\/g,"/");e.charAt(0)==="/"&&(e=e.substring(1));let t=e.indexOf("/");return t==-1?"/":e.substring(0,t+1)}get folder(){let e=this.path,t=this.filename,n=e.substring(0,e.lastIndexOf(t));return n===""?"/":n}get filename(){let e=this.path.match(/([^/]+)$/);return e!==null?e[1]:""}get filenameWithoutExtension(){return this.withoutExtension(this.filename)}hasProperty(e){let t=this.findKeyInFrontmatter(e);if(t===void 0)return!1;let n=this.frontmatter[t];return!(n===null||n===void 0)}property(e){let t=this.findKeyInFrontmatter(e);if(t===void 0)return null;let n=this.frontmatter[t];return n===void 0?null:Array.isArray(n)?n.filter(i=>i!==null):n}findKeyInFrontmatter(e){let t=e.toLowerCase();return Object.keys(this.frontmatter).find(n=>n.toLowerCase()===t)}};var Gt=class{},Z=Gt;Z.dateFormat="YYYY-MM-DD",Z.dateTimeFormat="YYYY-MM-DD HH:mm",Z.indentationRegex=/^([\s\t>]*)/,Z.listMarkerRegex=/([-*+]|[0-9]+\.)/,Z.checkboxRegex=/\[(.)\]/u,Z.afterCheckboxRegex=/ *(.*)/u,Z.taskRegex=new RegExp(Gt.indentationRegex.source+Gt.listMarkerRegex.source+" +"+Gt.checkboxRegex.source+Gt.afterCheckboxRegex.source,"u"),Z.nonTaskRegex=new RegExp(Gt.indentationRegex.source+Gt.listMarkerRegex.source+"? *("+Gt.checkboxRegex.source+")?"+Gt.afterCheckboxRegex.source,"u"),Z.listItemRegex=new RegExp(Gt.indentationRegex.source+Gt.listMarkerRegex.source),Z.blockLinkRegex=/ \^[a-zA-Z0-9-]+$/u,Z.hashTags=/(^|\s)#[^ !@#$%^&*(),.?":{}|<>]+/g,Z.hashTagsFromEnd=new RegExp(Gt.hashTags.source+"$");var mn=class{constructor(e,t){this.parent=null;this.children=[];this.description=e.replace(Z.listItemRegex,"").trim(),this.originalMarkdown=e,this.parent=t,t!==null&&t.children.push(this)}get root(){return this.parent===null?this:this.parent.root}get isRoot(){return this.parent===null}identicalTo(e){return this.constructor.name!==e.constructor.name||this.originalMarkdown!==e.originalMarkdown?!1:mn.listsAreIdentical(this.children,e.children)}static listsAreIdentical(e,t){return e.length!==t.length?!1:e.every((n,i)=>n.identicalTo(t[i]))}};var Hu=ma(Ke());var Dt=class{constructor(e,t){this.start=e,this.end=t,t.isBefore(e)&&(this.start=t,this.end=e),this.start=this.start.startOf("day"),this.end=this.end.startOf("day")}static buildRelative(e){let t=e==="week"?"isoWeek":e;return new Dt(window.moment().startOf(t).startOf("day"),window.moment().endOf(t).startOf("day"))}static buildInvalid(){return new Dt(window.moment.invalid(),window.moment.invalid())}isValid(){return this.start.isValid()&&this.end.isValid()}moveToPrevious(e){let t=window.moment.duration(1,e);this.start.subtract(t),this.end.subtract(t),(e==="month"||e==="quarter")&&(this.end=this.end.endOf(e).startOf("day"))}moveToNext(e){let t=window.moment.duration(1,e);this.start.add(t),this.end.add(t),(e==="month"||e==="quarter")&&(this.end=this.end.endOf(e).startOf("day"))}};var Vt=class{static parseDate(e,t=!1){return window.moment(Hu.parseDate(e,void 0,{forwardDate:t})).startOf("day")}static parseDateRange(e,t=!1){let n=[Vt.parseRelativeDateRange,Vt.parseNumberedDateRange,Vt.parseAbsoluteDateRange];for(let i of n){let s=i(e,t);if(s.isValid())return s}return Dt.buildInvalid()}static parseAbsoluteDateRange(e,t){let n=Hu.parse(e,void 0,{forwardDate:t});if(n.length===0)return Dt.buildInvalid();let i=n[0].start,s=n[1]&&n[1].start?n[1].start:i,a=window.moment(i.date()),o=window.moment(s.date());return new Dt(a,o)}static parseRelativeDateRange(e,t){let n=/(last|this|next) (week|month|quarter|year)/,i=e.match(n);if(i&&i.length===3){let s=i[1],a=i[2],o=Dt.buildRelative(a);switch(s){case"last":o.moveToPrevious(a);break;case"next":o.moveToNext(a);break}return o}return Dt.buildInvalid()}static parseNumberedDateRange(e,t){let n=[[/^\s*[0-9]{4}\s*$/,"YYYY","year"],[/^\s*[0-9]{4}-Q[1-4]\s*$/,"YYYY-Q","quarter"],[/^\s*[0-9]{4}-[0-9]{2}\s*$/,"YYYY-MM","month"],[/^\s*[0-9]{4}-W[0-9]{2}\s*$/,"YYYY-WW","isoWeek"]];for(let[i,s,a]of n){let o=e.match(i);if(o){let u=o[0].trim();return new Dt(window.moment(u,s).startOf(a),window.moment(u,s).endOf(a))}}return Dt.buildInvalid()}};var zP={td:"today",tm:"tomorrow",yd:"yesterday",tw:"this week",nw:"next week",weekend:"sat",we:"sat"};function Vu(r){for(let[e,t]of Object.entries(zP))r=r.replace(RegExp(`\\b${e}\\s`,"i"),t);return r}var Om=ma(Ke());function dr(r,e){return r!==null&&e===null?-1:r===null&&e!==null?1:r!==null&&e!==null?r.isValid()&&!e.isValid()?1:!r.isValid()&&e.isValid()?-1:r.isAfter(e)?1:r.isBefore(e)?-1:0:0}function KP(r,e,t=void 0){if(!e)return`<i>no ${r} date</i>`;let n=Om.parseDate(e,t,{forwardDate:t!=null});return n!==null?window.moment(n).format("YYYY-MM-DD"):`<i>invalid ${r} date</i>`}function U_(r,e,t){return KP(r,e,t?new Date:void 0)}function Si(r,e){let t=null,n=Om.parseDate(r,new Date,{forwardDate:e});return n!==null&&(t=window.moment(n)),t}var fr=class{constructor({startDate:e=null,scheduledDate:t=null,dueDate:n=null}){this.startDate=e!=null?e:null,this.scheduledDate=t!=null?t:null,this.dueDate=n!=null?n:null,this.referenceDate=this.getReferenceDate()}getReferenceDate(){return this.dueDate?window.moment(this.dueDate):this.scheduledDate?window.moment(this.scheduledDate):this.startDate?window.moment(this.startDate):null}isIdenticalTo(e){return!(dr(this.startDate,e.startDate)!==0||dr(this.scheduledDate,e.scheduledDate)!==0||dr(this.dueDate,e.dueDate)!==0)}next(e){return this.referenceDate===null?new fr({startDate:null,scheduledDate:null,dueDate:null}):new fr({startDate:this.nextOccurrenceDate(this.startDate,e),scheduledDate:this.nextOccurrenceDate(this.scheduledDate,e),dueDate:this.nextOccurrenceDate(this.dueDate,e)})}nextOccurrenceDate(e,t){if(e===null)return null;let n=window.moment.duration(e.diff(this.referenceDate)),i=window.moment(t);return i.add(Math.round(n.asDays()),"days"),i}};var zu=["MO","TU","WE","TH","FR","SA","SU"],Ve=function(){function r(e,t){if(t===0)throw new Error("Can't create weekday with n == 0");this.weekday=e,this.n=t}return r.fromStr=function(e){return new r(zu.indexOf(e))},r.prototype.nth=function(e){return this.n===e?this:new r(this.weekday,e)},r.prototype.equals=function(e){return this.weekday===e.weekday&&this.n===e.n},r.prototype.toString=function(){var e=zu[this.weekday];return this.n&&(e=(this.n>0?"+":"")+String(this.n)+e),e},r.prototype.getJsWeekday=function(){return this.weekday===6?0:this.weekday+1},r}();var Fe=function(r){return r!=null},zt=function(r){return typeof r=="number"},Dm=function(r){return typeof r=="string"&&zu.includes(r)},ft=Array.isArray,pr=function(r,e){e===void 0&&(e=r),arguments.length===1&&(e=r,r=0);for(var t=[],n=r;n<e;n++)t.push(n);return t};var be=function(r,e){var t=0,n=[];if(ft(r))for(;t<e;t++)n[t]=[].concat(r);else for(;t<e;t++)n[t]=r;return n},W_=function(r){return ft(r)?r:[r]};function Oi(r,e,t){t===void 0&&(t=" ");var n=String(r);return e=e>>0,n.length>e?String(n):(e=e-n.length,e>t.length&&(t+=be(t,e/t.length)),t.slice(0,e)+String(n))}var q_=function(r,e,t){var n=r.split(e);return t?n.slice(0,t).concat([n.slice(t).join(e)]):n},yt=function(r,e){var t=r%e;return t*e<0?t+e:t},Ku=function(r,e){return{div:Math.floor(r/e),mod:yt(r,e)}},Kt=function(r){return!Fe(r)||r.length===0},$e=function(r){return!Kt(r)},_e=function(r,e){return $e(r)&&r.indexOf(e)!==-1};var Zr=function(r,e,t,n,i,s){return n===void 0&&(n=0),i===void 0&&(i=0),s===void 0&&(s=0),new Date(Date.UTC(r,e-1,t,n,i,s))},QP=[31,28,31,30,31,30,31,31,30,31,30,31],G_=1e3*60*60*24,Qu=9999,Y_=Zr(1970,1,1),XP=[6,0,1,2,3,4,5];var hs=function(r){return r%4===0&&r%100!==0||r%400===0},xm=function(r){return r instanceof Date},Di=function(r){return xm(r)&&!isNaN(r.getTime())},$_=function(r){return r.getTimezoneOffset()*60*1e3},ZP=function(r,e){var t=r.getTime()-$_(r),n=e.getTime()-$_(e),i=t-n;return Math.round(i/G_)},go=function(r){return ZP(r,Y_)},Xu=function(r){return new Date(Y_.getTime()+r*G_)},JP=function(r){var e=r.getUTCMonth();return e===1&&hs(r.getUTCFullYear())?29:QP[e]},En=function(r){return XP[r.getUTCDay()]},Rm=function(r,e){var t=Zr(r,e+1,1);return[En(t),JP(t)]},Zu=function(r,e){return e=e||r,new Date(Date.UTC(r.getUTCFullYear(),r.getUTCMonth(),r.getUTCDate(),e.getHours(),e.getMinutes(),e.getSeconds(),e.getMilliseconds()))},Ju=function(r){var e=new Date(r.getTime());return e},Mm=function(r){for(var e=[],t=0;t<r.length;t++)e.push(Ju(r[t]));return e},Sn=function(r){r.sort(function(e,t){return e.getTime()-t.getTime()})},gs=function(r,e){e===void 0&&(e=!0);var t=new Date(r);return[Oi(t.getUTCFullYear().toString(),4,"0"),Oi(t.getUTCMonth()+1,2,"0"),Oi(t.getUTCDate(),2,"0"),"T",Oi(t.getUTCHours(),2,"0"),Oi(t.getUTCMinutes(),2,"0"),Oi(t.getUTCSeconds(),2,"0"),e?"Z":""].join("")},yo=function(r){var e=/^(\d{4})(\d{2})(\d{2})(T(\d{2})(\d{2})(\d{2})Z?)?$/,t=e.exec(r);if(!t)throw new Error("Invalid UNTIL value: ".concat(r));return new Date(Date.UTC(parseInt(t[1],10),parseInt(t[2],10)-1,parseInt(t[3],10),parseInt(t[5],10)||0,parseInt(t[6],10)||0,parseInt(t[7],10)||0))},j_=function(r,e){var t=r.toLocaleString("sv-SE",{timeZone:e});return t.replace(" ","T")+"Z"},B_=function(r,e){var t=Intl.DateTimeFormat().resolvedOptions().timeZone,n=new Date(j_(r,t)),i=new Date(j_(r,e!=null?e:"UTC")),s=i.getTime()-n.getTime();return new Date(r.getTime()-s)};var eN=function(){function r(e,t){this.minDate=null,this.maxDate=null,this._result=[],this.total=0,this.method=e,this.args=t,e==="between"?(this.maxDate=t.inc?t.before:new Date(t.before.getTime()-1),this.minDate=t.inc?t.after:new Date(t.after.getTime()+1)):e==="before"?this.maxDate=t.inc?t.dt:new Date(t.dt.getTime()-1):e==="after"&&(this.minDate=t.inc?t.dt:new Date(t.dt.getTime()+1))}return r.prototype.accept=function(e){++this.total;var t=this.minDate&&e<this.minDate,n=this.maxDate&&e>this.maxDate;if(this.method==="between"){if(t)return!0;if(n)return!1}else if(this.method==="before"){if(n)return!1}else if(this.method==="after")return t?!0:(this.add(e),!1);return this.add(e)},r.prototype.add=function(e){return this._result.push(e),!0},r.prototype.getValue=function(){var e=this._result;switch(this.method){case"all":case"between":return e;case"before":case"after":default:return e.length?e[e.length-1]:null}},r.prototype.clone=function(){return new r(this.method,this.args)},r}(),On=eN;var Cm=function(r,e){return Cm=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,n){t.__proto__=n}||function(t,n){for(var i in n)Object.prototype.hasOwnProperty.call(n,i)&&(t[i]=n[i])},Cm(r,e)};function ys(r,e){if(typeof e!="function"&&e!==null)throw new TypeError("Class extends value "+String(e)+" is not a constructor or null");Cm(r,e);function t(){this.constructor=r}r.prototype=e===null?Object.create(e):(t.prototype=e.prototype,new t)}var pt=function(){return pt=Object.assign||function(e){for(var t,n=1,i=arguments.length;n<i;n++){t=arguments[n];for(var s in t)Object.prototype.hasOwnProperty.call(t,s)&&(e[s]=t[s])}return e},pt.apply(this,arguments)};function L(r,e,t){if(t||arguments.length===2)for(var n=0,i=e.length,s;n<i;n++)(s||!(n in e))&&(s||(s=Array.prototype.slice.call(e,0,n)),s[n]=e[n]);return r.concat(s||Array.prototype.slice.call(e))}var tN=function(r){ys(e,r);function e(t,n,i){var s=r.call(this,t,n)||this;return s.iterator=i,s}return e.prototype.add=function(t){return this.iterator(t,this._result.length)?(this._result.push(t),!0):!1},e}(On),Am=tN;var rN={dayNames:["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"],monthNames:["January","February","March","April","May","June","July","August","September","October","November","December"],tokens:{SKIP:/^[ \r\n\t]+|^\.$/,number:/^[1-9][0-9]*/,numberAsText:/^(one|two|three)/i,every:/^every/i,"day(s)":/^days?/i,"weekday(s)":/^weekdays?/i,"week(s)":/^weeks?/i,"hour(s)":/^hours?/i,"minute(s)":/^minutes?/i,"month(s)":/^months?/i,"year(s)":/^years?/i,on:/^(on|in)/i,at:/^(at)/i,the:/^the/i,first:/^first/i,second:/^second/i,third:/^third/i,nth:/^([1-9][0-9]*)(\.|th|nd|rd|st)/i,last:/^last/i,for:/^for/i,"time(s)":/^times?/i,until:/^(un)?til/i,monday:/^mo(n(day)?)?/i,tuesday:/^tu(e(s(day)?)?)?/i,wednesday:/^we(d(n(esday)?)?)?/i,thursday:/^th(u(r(sday)?)?)?/i,friday:/^fr(i(day)?)?/i,saturday:/^sa(t(urday)?)?/i,sunday:/^su(n(day)?)?/i,january:/^jan(uary)?/i,february:/^feb(ruary)?/i,march:/^mar(ch)?/i,april:/^apr(il)?/i,may:/^may/i,june:/^june?/i,july:/^july?/i,august:/^aug(ust)?/i,september:/^sep(t(ember)?)?/i,october:/^oct(ober)?/i,november:/^nov(ember)?/i,december:/^dec(ember)?/i,comma:/^(,\s*|(and|or)\s*)+/i}},xi=rN;var H_=function(r,e){return r.indexOf(e)!==-1},nN=function(r){return r.toString()},iN=function(r,e,t){return"".concat(e," ").concat(t,", ").concat(r)},sN=function(){function r(e,t,n,i){if(t===void 0&&(t=nN),n===void 0&&(n=xi),i===void 0&&(i=iN),this.text=[],this.language=n||xi,this.gettext=t,this.dateFormatter=i,this.rrule=e,this.options=e.options,this.origOptions=e.origOptions,this.origOptions.bymonthday){var s=[].concat(this.options.bymonthday),a=[].concat(this.options.bynmonthday);s.sort(function(c,d){return c-d}),a.sort(function(c,d){return d-c}),this.bymonthday=s.concat(a),this.bymonthday.length||(this.bymonthday=null)}if(Fe(this.origOptions.byweekday)){var o=ft(this.origOptions.byweekday)?this.origOptions.byweekday:[this.origOptions.byweekday],u=String(o);this.byweekday={allWeeks:o.filter(function(c){return!c.n}),someWeeks:o.filter(function(c){return Boolean(c.n)}),isWeekdays:u.indexOf("MO")!==-1&&u.indexOf("TU")!==-1&&u.indexOf("WE")!==-1&&u.indexOf("TH")!==-1&&u.indexOf("FR")!==-1&&u.indexOf("SA")===-1&&u.indexOf("SU")===-1,isEveryDay:u.indexOf("MO")!==-1&&u.indexOf("TU")!==-1&&u.indexOf("WE")!==-1&&u.indexOf("TH")!==-1&&u.indexOf("FR")!==-1&&u.indexOf("SA")!==-1&&u.indexOf("SU")!==-1};var l=function(c,d){return c.weekday-d.weekday};this.byweekday.allWeeks.sort(l),this.byweekday.someWeeks.sort(l),this.byweekday.allWeeks.length||(this.byweekday.allWeeks=null),this.byweekday.someWeeks.length||(this.byweekday.someWeeks=null)}else this.byweekday=null}return r.isFullyConvertible=function(e){var t=!0;if(!(e.options.freq in r.IMPLEMENTED)||e.origOptions.until&&e.origOptions.count)return!1;for(var n in e.origOptions){if(H_(["dtstart","wkst","freq"],n))return!0;if(!H_(r.IMPLEMENTED[e.options.freq],n))return!1}return t},r.prototype.isFullyConvertible=function(){return r.isFullyConvertible(this.rrule)},r.prototype.toString=function(){var e=this.gettext;if(!(this.options.freq in r.IMPLEMENTED))return e("RRule error: Unable to fully convert this rrule to text");if(this.text=[e("every")],this[V.FREQUENCIES[this.options.freq]](),this.options.until){this.add(e("until"));var t=this.options.until;this.add(this.dateFormatter(t.getUTCFullYear(),this.language.monthNames[t.getUTCMonth()],t.getUTCDate()))}else this.options.count&&this.add(e("for")).add(this.options.count.toString()).add(this.plural(this.options.count)?e("times"):e("time"));return this.isFullyConvertible()||this.add(e("(~ approximate)")),this.text.join("")},r.prototype.HOURLY=function(){var e=this.gettext;this.options.interval!==1&&this.add(this.options.interval.toString()),this.add(this.plural(this.options.interval)?e("hours"):e("hour"))},r.prototype.MINUTELY=function(){var e=this.gettext;this.options.interval!==1&&this.add(this.options.interval.toString()),this.add(this.plural(this.options.interval)?e("minutes"):e("minute"))},r.prototype.DAILY=function(){var e=this.gettext;this.options.interval!==1&&this.add(this.options.interval.toString()),this.byweekday&&this.byweekday.isWeekdays?this.add(this.plural(this.options.interval)?e("weekdays"):e("weekday")):this.add(this.plural(this.options.interval)?e("days"):e("day")),this.origOptions.bymonth&&(this.add(e("in")),this._bymonth()),this.bymonthday?this._bymonthday():this.byweekday?this._byweekday():this.origOptions.byhour&&this._byhour()},r.prototype.WEEKLY=function(){var e=this.gettext;this.options.interval!==1&&this.add(this.options.interval.toString()).add(this.plural(this.options.interval)?e("weeks"):e("week")),this.byweekday&&this.byweekday.isWeekdays?this.options.interval===1?this.add(this.plural(this.options.interval)?e("weekdays"):e("weekday")):this.add(e("on")).add(e("weekdays")):this.byweekday&&this.byweekday.isEveryDay?this.add(this.plural(this.options.interval)?e("days"):e("day")):(this.options.interval===1&&this.add(e("week")),this.origOptions.bymonth&&(this.add(e("in")),this._bymonth()),this.bymonthday?this._bymonthday():this.byweekday&&this._byweekday())},r.prototype.MONTHLY=function(){var e=this.gettext;this.origOptions.bymonth?(this.options.interval!==1&&(this.add(this.options.interval.toString()).add(e("months")),this.plural(this.options.interval)&&this.add(e("in"))),this._bymonth()):(this.options.interval!==1&&this.add(this.options.interval.toString()),this.add(this.plural(this.options.interval)?e("months"):e("month"))),this.bymonthday?this._bymonthday():this.byweekday&&this.byweekday.isWeekdays?this.add(e("on")).add(e("weekdays")):this.byweekday&&this._byweekday()},r.prototype.YEARLY=function(){var e=this.gettext;this.origOptions.bymonth?(this.options.interval!==1&&(this.add(this.options.interval.toString()),this.add(e("years"))),this._bymonth()):(this.options.interval!==1&&this.add(this.options.interval.toString()),this.add(this.plural(this.options.interval)?e("years"):e("year"))),this.bymonthday?this._bymonthday():this.byweekday&&this._byweekday(),this.options.byyearday&&this.add(e("on the")).add(this.list(this.options.byyearday,this.nth,e("and"))).add(e("day")),this.options.byweekno&&this.add(e("in")).add(this.plural(this.options.byweekno.length)?e("weeks"):e("week")).add(this.list(this.options.byweekno,void 0,e("and")))},r.prototype._bymonthday=function(){var e=this.gettext;this.byweekday&&this.byweekday.allWeeks?this.add(e("on")).add(this.list(this.byweekday.allWeeks,this.weekdaytext,e("or"))).add(e("the")).add(this.list(this.bymonthday,this.nth,e("or"))):this.add(e("on the")).add(this.list(this.bymonthday,this.nth,e("and")))},r.prototype._byweekday=function(){var e=this.gettext;this.byweekday.allWeeks&&!this.byweekday.isWeekdays&&this.add(e("on")).add(this.list(this.byweekday.allWeeks,this.weekdaytext)),this.byweekday.someWeeks&&(this.byweekday.allWeeks&&this.add(e("and")),this.add(e("on the")).add(this.list(this.byweekday.someWeeks,this.weekdaytext,e("and"))))},r.prototype._byhour=function(){var e=this.gettext;this.add(e("at")).add(this.list(this.origOptions.byhour,void 0,e("and")))},r.prototype._bymonth=function(){this.add(this.list(this.options.bymonth,this.monthtext,this.gettext("and")))},r.prototype.nth=function(e){e=parseInt(e.toString(),10);var t,n=this.gettext;if(e===-1)return n("last");var i=Math.abs(e);switch(i){case 1:case 21:case 31:t=i+n("st");break;case 2:case 22:t=i+n("nd");break;case 3:case 23:t=i+n("rd");break;default:t=i+n("th")}return e<0?t+" "+n("last"):t},r.prototype.monthtext=function(e){return this.language.monthNames[e-1]},r.prototype.weekdaytext=function(e){var t=zt(e)?(e+1)%7:e.getJsWeekday();return(e.n?this.nth(e.n)+" ":"")+this.language.dayNames[t]},r.prototype.plural=function(e){return e%100!==1},r.prototype.add=function(e){return this.text.push(" "),this.text.push(e),this},r.prototype.list=function(e,t,n,i){var s=this;i===void 0&&(i=","),ft(e)||(e=[e]);var a=function(u,l,c){for(var d="",f=0;f<u.length;f++)f!==0&&(f===u.length-1?d+=" "+c+" ":d+=l+" "),d+=u[f];return d};t=t||function(u){return u.toString()};var o=function(u){return t&&t.call(s,u)};return n?a(e.map(o),i,n):e.map(o).join(i+" ")},r}(),Jr=sN;var aN=function(){function r(e){this.done=!0,this.rules=e}return r.prototype.start=function(e){return this.text=e,this.done=!1,this.nextSymbol()},r.prototype.isDone=function(){return this.done&&this.symbol===null},r.prototype.nextSymbol=function(){var e,t;this.symbol=null,this.value=null;do{if(this.done)return!1;var n=void 0;e=null;for(var i in this.rules){n=this.rules[i];var s=n.exec(this.text);s&&(e===null||s[0].length>e[0].length)&&(e=s,t=i)}if(e!=null&&(this.text=this.text.substr(e[0].length),this.text===""&&(this.done=!0)),e==null){this.done=!0,this.symbol=null,this.value=null;return}}while(t==="SKIP");return this.symbol=t,this.value=e,!0},r.prototype.accept=function(e){if(this.symbol===e){if(this.value){var t=this.value;return this.nextSymbol(),t}return this.nextSymbol(),!0}return!1},r.prototype.acceptNumber=function(){return this.accept("number")},r.prototype.expect=function(e){if(this.accept(e))return!0;throw new Error("expected "+e+" but found "+this.symbol)},r}();function To(r,e){e===void 0&&(e=xi);var t={},n=new aN(e.tokens);if(!n.start(r))return null;return i(),t;function i(){n.expect("every");var f=n.acceptNumber();if(f&&(t.interval=parseInt(f[0],10)),n.isDone())throw new Error("Unexpected end");switch(n.symbol){case"day(s)":t.freq=V.DAILY,n.nextSymbol()&&(a(),d());break;case"weekday(s)":t.freq=V.WEEKLY,t.byweekday=[V.MO,V.TU,V.WE,V.TH,V.FR],n.nextSymbol(),d();break;case"week(s)":t.freq=V.WEEKLY,n.nextSymbol()&&(s(),d());break;case"hour(s)":t.freq=V.HOURLY,n.nextSymbol()&&(s(),d());break;case"minute(s)":t.freq=V.MINUTELY,n.nextSymbol()&&(s(),d());break;case"month(s)":t.freq=V.MONTHLY,n.nextSymbol()&&(s(),d());break;case"year(s)":t.freq=V.YEARLY,n.nextSymbol()&&(s(),d());break;case"monday":case"tuesday":case"wednesday":case"thursday":case"friday":case"saturday":case"sunday":t.freq=V.WEEKLY;var m=n.symbol.substr(0,2).toUpperCase();if(t.byweekday=[V[m]],!n.nextSymbol())return;for(;n.accept("comma");){if(n.isDone())throw new Error("Unexpected end");var y=u();if(!y)throw new Error("Unexpected symbol "+n.symbol+", expected weekday");t.byweekday.push(V[y]),n.nextSymbol()}c(),d();break;case"january":case"february":case"march":case"april":case"may":case"june":case"july":case"august":case"september":case"october":case"november":case"december":if(t.freq=V.YEARLY,t.bymonth=[o()],!n.nextSymbol())return;for(;n.accept("comma");){if(n.isDone())throw new Error("Unexpected end");var b=o();if(!b)throw new Error("Unexpected symbol "+n.symbol+", expected month");t.bymonth.push(b),n.nextSymbol()}s(),d();break;default:throw new Error("Unknown symbol")}}function s(){var f=n.accept("on"),m=n.accept("the");if(!!(f||m))do{var y=l(),b=u(),k=o();if(y)b?(n.nextSymbol(),t.byweekday||(t.byweekday=[]),t.byweekday.push(V[b].nth(y))):(t.bymonthday||(t.bymonthday=[]),t.bymonthday.push(y),n.accept("day(s)"));else if(b)n.nextSymbol(),t.byweekday||(t.byweekday=[]),t.byweekday.push(V[b]);else if(n.symbol==="weekday(s)")n.nextSymbol(),t.byweekday||(t.byweekday=[V.MO,V.TU,V.WE,V.TH,V.FR]);else if(n.symbol==="week(s)"){n.nextSymbol();var _=n.acceptNumber();if(!_)throw new Error("Unexpected symbol "+n.symbol+", expected week number");for(t.byweekno=[parseInt(_[0],10)];n.accept("comma");){if(_=n.acceptNumber(),!_)throw new Error("Unexpected symbol "+n.symbol+"; expected monthday");t.byweekno.push(parseInt(_[0],10))}}else if(k)n.nextSymbol(),t.bymonth||(t.bymonth=[]),t.bymonth.push(k);else return}while(n.accept("comma")||n.accept("the")||n.accept("on"))}function a(){var f=n.accept("at");if(!!f)do{var m=n.acceptNumber();if(!m)throw new Error("Unexpected symbol "+n.symbol+", expected hour");for(t.byhour=[parseInt(m[0],10)];n.accept("comma");){if(m=n.acceptNumber(),!m)throw new Error("Unexpected symbol "+n.symbol+"; expected hour");t.byhour.push(parseInt(m[0],10))}}while(n.accept("comma")||n.accept("at"))}function o(){switch(n.symbol){case"january":return 1;case"february":return 2;case"march":return 3;case"april":return 4;case"may":return 5;case"june":return 6;case"july":return 7;case"august":return 8;case"september":return 9;case"october":return 10;case"november":return 11;case"december":return 12;default:return!1}}function u(){switch(n.symbol){case"monday":case"tuesday":case"wednesday":case"thursday":case"friday":case"saturday":case"sunday":return n.symbol.substr(0,2).toUpperCase();default:return!1}}function l(){switch(n.symbol){case"last":return n.nextSymbol(),-1;case"first":return n.nextSymbol(),1;case"second":return n.nextSymbol(),n.accept("last")?-2:2;case"third":return n.nextSymbol(),n.accept("last")?-3:3;case"nth":var f=parseInt(n.value[1],10);if(f<-366||f>366)throw new Error("Nth out of range: "+f);return n.nextSymbol(),n.accept("last")?-f:f;default:return!1}}function c(){n.accept("on"),n.accept("the");var f=l();if(!!f)for(t.bymonthday=[f],n.nextSymbol();n.accept("comma");){if(f=l(),!f)throw new Error("Unexpected symbol "+n.symbol+"; expected monthday");t.bymonthday.push(f),n.nextSymbol()}}function d(){if(n.symbol==="until"){var f=Date.parse(n.text);if(!f)throw new Error("Cannot parse until date:"+n.text);t.until=new Date(f)}else n.accept("for")&&(t.count=parseInt(n.value[0],10),n.expect("number"))}}var ye;(function(r){r[r.YEARLY=0]="YEARLY",r[r.MONTHLY=1]="MONTHLY",r[r.WEEKLY=2]="WEEKLY",r[r.DAILY=3]="DAILY",r[r.HOURLY=4]="HOURLY",r[r.MINUTELY=5]="MINUTELY",r[r.SECONDLY=6]="SECONDLY"})(ye||(ye={}));function bo(r){return r<ye.HOURLY}var V_=function(r,e){return e===void 0&&(e=xi),new V(To(r,e)||void 0)},Ts=["count","until","interval","byweekday","bymonthday","bymonth"];Jr.IMPLEMENTED=[];Jr.IMPLEMENTED[ye.HOURLY]=Ts;Jr.IMPLEMENTED[ye.MINUTELY]=Ts;Jr.IMPLEMENTED[ye.DAILY]=["byhour"].concat(Ts);Jr.IMPLEMENTED[ye.WEEKLY]=Ts;Jr.IMPLEMENTED[ye.MONTHLY]=Ts;Jr.IMPLEMENTED[ye.YEARLY]=["byweekno","byyearday"].concat(Ts);var z_=function(r,e,t,n){return new Jr(r,e,t,n).toString()},K_=Jr.isFullyConvertible;var bs=function(){function r(e,t,n,i){this.hour=e,this.minute=t,this.second=n,this.millisecond=i||0}return r.prototype.getHours=function(){return this.hour},r.prototype.getMinutes=function(){return this.minute},r.prototype.getSeconds=function(){return this.second},r.prototype.getMilliseconds=function(){return this.millisecond},r.prototype.getTime=function(){return(this.hour*60*60+this.minute*60+this.second)*1e3+this.millisecond},r}();var Q_=function(r){ys(e,r);function e(t,n,i,s,a,o,u){var l=r.call(this,s,a,o,u)||this;return l.year=t,l.month=n,l.day=i,l}return e.fromDate=function(t){return new this(t.getUTCFullYear(),t.getUTCMonth()+1,t.getUTCDate(),t.getUTCHours(),t.getUTCMinutes(),t.getUTCSeconds(),t.valueOf()%1e3)},e.prototype.getWeekday=function(){return En(new Date(this.getTime()))},e.prototype.getTime=function(){return new Date(Date.UTC(this.year,this.month-1,this.day,this.hour,this.minute,this.second,this.millisecond)).getTime()},e.prototype.getDay=function(){return this.day},e.prototype.getMonth=function(){return this.month},e.prototype.getYear=function(){return this.year},e.prototype.addYears=function(t){this.year+=t},e.prototype.addMonths=function(t){if(this.month+=t,this.month>12){var n=Math.floor(this.month/12),i=yt(this.month,12);this.month=i,this.year+=n,this.month===0&&(this.month=12,--this.year)}},e.prototype.addWeekly=function(t,n){n>this.getWeekday()?this.day+=-(this.getWeekday()+1+(6-n))+t*7:this.day+=-(this.getWeekday()-n)+t*7,this.fixDay()},e.prototype.addDaily=function(t){this.day+=t,this.fixDay()},e.prototype.addHours=function(t,n,i){for(n&&(this.hour+=Math.floor((23-this.hour)/t)*t);;){this.hour+=t;var s=Ku(this.hour,24),a=s.div,o=s.mod;if(a&&(this.hour=o,this.addDaily(a)),Kt(i)||_e(i,this.hour))break}},e.prototype.addMinutes=function(t,n,i,s){for(n&&(this.minute+=Math.floor((1439-(this.hour*60+this.minute))/t)*t);;){this.minute+=t;var a=Ku(this.minute,60),o=a.div,u=a.mod;if(o&&(this.minute=u,this.addHours(o,!1,i)),(Kt(i)||_e(i,this.hour))&&(Kt(s)||_e(s,this.minute)))break}},e.prototype.addSeconds=function(t,n,i,s,a){for(n&&(this.second+=Math.floor((86399-(this.hour*3600+this.minute*60+this.second))/t)*t);;){this.second+=t;var o=Ku(this.second,60),u=o.div,l=o.mod;if(u&&(this.second=l,this.addMinutes(u,!1,i,s)),(Kt(i)||_e(i,this.hour))&&(Kt(s)||_e(s,this.minute))&&(Kt(a)||_e(a,this.second)))break}},e.prototype.fixDay=function(){if(!(this.day<=28)){var t=Rm(this.year,this.month-1)[1];if(!(this.day<=t))for(;this.day>t;){if(this.day-=t,++this.month,this.month===13&&(this.month=1,++this.year,this.year>Qu))return;t=Rm(this.year,this.month-1)[1]}}},e.prototype.add=function(t,n){var i=t.freq,s=t.interval,a=t.wkst,o=t.byhour,u=t.byminute,l=t.bysecond;switch(i){case ye.YEARLY:return this.addYears(s);case ye.MONTHLY:return this.addMonths(s);case ye.WEEKLY:return this.addWeekly(s,a);case ye.DAILY:return this.addDaily(s);case ye.HOURLY:return this.addHours(s,n,o);case ye.MINUTELY:return this.addMinutes(s,n,o,u);case ye.SECONDLY:return this.addSeconds(s,n,o,u,l)}},e}(bs);function Pm(r){for(var e=[],t=Object.keys(r),n=0,i=t;n<i.length;n++){var s=i[n];_e(J_,s)||e.push(s),xm(r[s])&&!Di(r[s])&&e.push(s)}if(e.length)throw new Error("Invalid options: "+e.join(", "));return pt({},r)}function X_(r){var e=pt(pt({},_o),Pm(r));if(Fe(e.byeaster)&&(e.freq=V.YEARLY),!(Fe(e.freq)&&V.FREQUENCIES[e.freq]))throw new Error("Invalid frequency: ".concat(e.freq," ").concat(r.freq));if(e.dtstart||(e.dtstart=new Date(new Date().setMilliseconds(0))),Fe(e.wkst)?zt(e.wkst)||(e.wkst=e.wkst.weekday):e.wkst=V.MO.weekday,Fe(e.bysetpos)){zt(e.bysetpos)&&(e.bysetpos=[e.bysetpos]);for(var t=0;t<e.bysetpos.length;t++){var n=e.bysetpos[t];if(n===0||!(n>=-366&&n<=366))throw new Error("bysetpos must be between 1 and 366, or between -366 and -1")}}if(!(Boolean(e.byweekno)||$e(e.byweekno)||$e(e.byyearday)||Boolean(e.bymonthday)||$e(e.bymonthday)||Fe(e.byweekday)||Fe(e.byeaster)))switch(e.freq){case V.YEARLY:e.bymonth||(e.bymonth=e.dtstart.getUTCMonth()+1),e.bymonthday=e.dtstart.getUTCDate();break;case V.MONTHLY:e.bymonthday=e.dtstart.getUTCDate();break;case V.WEEKLY:e.byweekday=[En(e.dtstart)];break}if(Fe(e.bymonth)&&!ft(e.bymonth)&&(e.bymonth=[e.bymonth]),Fe(e.byyearday)&&!ft(e.byyearday)&&zt(e.byyearday)&&(e.byyearday=[e.byyearday]),!Fe(e.bymonthday))e.bymonthday=[],e.bynmonthday=[];else if(ft(e.bymonthday)){for(var i=[],s=[],t=0;t<e.bymonthday.length;t++){var n=e.bymonthday[t];n>0?i.push(n):n<0&&s.push(n)}e.bymonthday=i,e.bynmonthday=s}else e.bymonthday<0?(e.bynmonthday=[e.bymonthday],e.bymonthday=[]):(e.bynmonthday=[],e.bymonthday=[e.bymonthday]);if(Fe(e.byweekno)&&!ft(e.byweekno)&&(e.byweekno=[e.byweekno]),!Fe(e.byweekday))e.bynweekday=null;else if(zt(e.byweekday))e.byweekday=[e.byweekday],e.bynweekday=null;else if(Dm(e.byweekday))e.byweekday=[Ve.fromStr(e.byweekday).weekday],e.bynweekday=null;else if(e.byweekday instanceof Ve)!e.byweekday.n||e.freq>V.MONTHLY?(e.byweekday=[e.byweekday.weekday],e.bynweekday=null):(e.bynweekday=[[e.byweekday.weekday,e.byweekday.n]],e.byweekday=null);else{for(var a=[],o=[],t=0;t<e.byweekday.length;t++){var u=e.byweekday[t];if(zt(u)){a.push(u);continue}else if(Dm(u)){a.push(Ve.fromStr(u).weekday);continue}!u.n||e.freq>V.MONTHLY?a.push(u.weekday):o.push([u.weekday,u.n])}e.byweekday=$e(a)?a:null,e.bynweekday=$e(o)?o:null}return Fe(e.byhour)?zt(e.byhour)&&(e.byhour=[e.byhour]):e.byhour=e.freq<V.HOURLY?[e.dtstart.getUTCHours()]:null,Fe(e.byminute)?zt(e.byminute)&&(e.byminute=[e.byminute]):e.byminute=e.freq<V.MINUTELY?[e.dtstart.getUTCMinutes()]:null,Fe(e.bysecond)?zt(e.bysecond)&&(e.bysecond=[e.bysecond]):e.bysecond=e.freq<V.SECONDLY?[e.dtstart.getUTCSeconds()]:null,{parsedOptions:e}}function Z_(r){var e=r.dtstart.getTime()%1e3;if(!bo(r.freq))return[];var t=[];return r.byhour.forEach(function(n){r.byminute.forEach(function(i){r.bysecond.forEach(function(s){t.push(new bs(n,i,s,e))})})}),t}function wo(r){var e=r.split(`
`).map(oN).filter(function(t){return t!==null});return pt(pt({},e[0]),e[1])}function vo(r){var e={},t=/DTSTART(?:;TZID=([^:=]+?))?(?::|=)([^;\s]+)/i.exec(r);if(!t)return e;var n=t[1],i=t[2];return n&&(e.tzid=n),e.dtstart=yo(i),e}function oN(r){if(r=r.replace(/^\s+|\s+$/,""),!r.length)return null;var e=/^([A-Z]+?)[:;]/.exec(r.toUpperCase());if(!e)return ev(r);var t=e[1];switch(t.toUpperCase()){case"RRULE":case"EXRULE":return ev(r);case"DTSTART":return vo(r);default:throw new Error("Unsupported RFC prop ".concat(t," in ").concat(r))}}function ev(r){var e=r.replace(/^RRULE:/i,""),t=vo(e),n=r.replace(/^(?:RRULE|EXRULE):/i,"").split(";");return n.forEach(function(i){var s=i.split("="),a=s[0],o=s[1];switch(a.toUpperCase()){case"FREQ":t.freq=ye[o.toUpperCase()];break;case"WKST":t.wkst=mr[o.toUpperCase()];break;case"COUNT":case"INTERVAL":case"BYSETPOS":case"BYMONTH":case"BYMONTHDAY":case"BYYEARDAY":case"BYWEEKNO":case"BYHOUR":case"BYMINUTE":case"BYSECOND":var u=uN(o),l=a.toLowerCase();t[l]=u;break;case"BYWEEKDAY":case"BYDAY":t.byweekday=lN(o);break;case"DTSTART":case"TZID":var c=vo(r);t.tzid=c.tzid,t.dtstart=c.dtstart;break;case"UNTIL":t.until=yo(o);break;case"BYEASTER":t.byeaster=Number(o);break;default:throw new Error("Unknown RRULE property '"+a+"'")}}),t}function uN(r){if(r.indexOf(",")!==-1){var e=r.split(",");return e.map(tv)}return tv(r)}function tv(r){return/^[+-]?\d+$/.test(r)?Number(r):r}function lN(r){var e=r.split(",");return e.map(function(t){if(t.length===2)return mr[t];var n=t.match(/^([+-]?\d{1,2})([A-Z]{2})$/);if(!n||n.length<3)throw new SyntaxError("Invalid weekday string: ".concat(t));var i=Number(n[1]),s=n[2],a=mr[s].weekday;return new Ve(a,i)})}var Ri=function(){function r(e,t){if(isNaN(e.getTime()))throw new RangeError("Invalid date passed to DateWithZone");this.date=e,this.tzid=t}return Object.defineProperty(r.prototype,"isUTC",{get:function(){return!this.tzid||this.tzid.toUpperCase()==="UTC"},enumerable:!1,configurable:!0}),r.prototype.toString=function(){var e=gs(this.date.getTime(),this.isUTC);return this.isUTC?":".concat(e):";TZID=".concat(this.tzid,":").concat(e)},r.prototype.getTime=function(){return this.date.getTime()},r.prototype.rezonedDate=function(){return this.isUTC?this.date:B_(this.date,this.tzid)},r}();function ko(r){for(var e=[],t="",n=Object.keys(r),i=Object.keys(_o),s=0;s<n.length;s++)if(n[s]!=="tzid"&&!!_e(i,n[s])){var a=n[s].toUpperCase(),o=r[n[s]],u="";if(!(!Fe(o)||ft(o)&&!o.length)){switch(a){case"FREQ":u=V.FREQUENCIES[r.freq];break;case"WKST":zt(o)?u=new Ve(o).toString():u=o.toString();break;case"BYWEEKDAY":a="BYDAY",u=W_(o).map(function(m){return m instanceof Ve?m:ft(m)?new Ve(m[0],m[1]):new Ve(m)}).toString();break;case"DTSTART":t=cN(o,r.tzid);break;case"UNTIL":u=gs(o,!r.tzid);break;default:if(ft(o)){for(var l=[],c=0;c<o.length;c++)l[c]=String(o[c]);u=l.toString()}else u=String(o)}u&&e.push([a,u])}}var d=e.map(function(m){var y=m[0],b=m[1];return"".concat(y,"=").concat(b.toString())}).join(";"),f="";return d!==""&&(f="RRULE:".concat(d)),[t,f].filter(function(m){return!!m}).join(`
`)}function cN(r,e){return r?"DTSTART"+new Ri(new Date(r),e).toString():""}function dN(r,e){return Array.isArray(r)?!Array.isArray(e)||r.length!==e.length?!1:r.every(function(t,n){return t.getTime()===e[n].getTime()}):r instanceof Date?e instanceof Date&&r.getTime()===e.getTime():r===e}var rv=function(){function r(){this.all=!1,this.before=[],this.after=[],this.between=[]}return r.prototype._cacheAdd=function(e,t,n){t&&(t=t instanceof Date?Ju(t):Mm(t)),e==="all"?this.all=t:(n._value=t,this[e].push(n))},r.prototype._cacheGet=function(e,t){var n=!1,i=t?Object.keys(t):[],s=function(c){for(var d=0;d<i.length;d++){var f=i[d];if(!dN(t[f],c[f]))return!0}return!1},a=this[e];if(e==="all")n=this.all;else if(ft(a))for(var o=0;o<a.length;o++){var u=a[o];if(!(i.length&&s(u))){n=u._value;break}}if(!n&&this.all){for(var l=new On(e,t),o=0;o<this.all.length&&l.accept(this.all[o]);o++);n=l.getValue(),this._cacheAdd(e,n,t)}return ft(n)?Mm(n):n instanceof Date?Ju(n):n},r}();var nv=L(L(L(L(L(L(L(L(L(L(L(L(L([],be(1,31),!0),be(2,28),!0),be(3,31),!0),be(4,30),!0),be(5,31),!0),be(6,30),!0),be(7,31),!0),be(8,31),!0),be(9,30),!0),be(10,31),!0),be(11,30),!0),be(12,31),!0),be(1,7),!0),iv=L(L(L(L(L(L(L(L(L(L(L(L(L([],be(1,31),!0),be(2,29),!0),be(3,31),!0),be(4,30),!0),be(5,31),!0),be(6,30),!0),be(7,31),!0),be(8,31),!0),be(9,30),!0),be(10,31),!0),be(11,30),!0),be(12,31),!0),be(1,7),!0),fN=pr(1,29),pN=pr(1,30),Qn=pr(1,31),Tt=pr(1,32),sv=L(L(L(L(L(L(L(L(L(L(L(L(L([],Tt,!0),pN,!0),Tt,!0),Qn,!0),Tt,!0),Qn,!0),Tt,!0),Tt,!0),Qn,!0),Tt,!0),Qn,!0),Tt,!0),Tt.slice(0,7),!0),av=L(L(L(L(L(L(L(L(L(L(L(L(L([],Tt,!0),fN,!0),Tt,!0),Qn,!0),Tt,!0),Qn,!0),Tt,!0),Tt,!0),Qn,!0),Tt,!0),Qn,!0),Tt,!0),Tt.slice(0,7),!0),mN=pr(-28,0),hN=pr(-29,0),Xn=pr(-30,0),bt=pr(-31,0),ov=L(L(L(L(L(L(L(L(L(L(L(L(L([],bt,!0),hN,!0),bt,!0),Xn,!0),bt,!0),Xn,!0),bt,!0),bt,!0),Xn,!0),bt,!0),Xn,!0),bt,!0),bt.slice(0,7),!0),uv=L(L(L(L(L(L(L(L(L(L(L(L(L([],bt,!0),mN,!0),bt,!0),Xn,!0),bt,!0),Xn,!0),bt,!0),bt,!0),Xn,!0),bt,!0),Xn,!0),bt,!0),bt.slice(0,7),!0),lv=[0,31,60,91,121,152,182,213,244,274,305,335,366],cv=[0,31,59,90,120,151,181,212,243,273,304,334,365],Nm=function(){for(var r=[],e=0;e<55;e++)r=r.concat(pr(7));return r}();function dv(r,e){var t=Zr(r,1,1),n=hs(r)?366:365,i=hs(r+1)?366:365,s=go(t),a=En(t),o=pt(pt({yearlen:n,nextyearlen:i,yearordinal:s,yearweekday:a},gN(r)),{wnomask:null});if(Kt(e.byweekno))return o;o.wnomask=be(0,n+7);var u,l,c=u=yt(7-a+e.wkst,7);c>=4?(c=0,l=o.yearlen+yt(a-e.wkst,7)):l=n-c;for(var d=Math.floor(l/7),f=yt(l,7),m=Math.floor(d+f/4),y=0;y<e.byweekno.length;y++){var b=e.byweekno[y];if(b<0&&(b+=m+1),b>0&&b<=m){var k=void 0;b>1?(k=c+(b-1)*7,c!==u&&(k-=7-u)):k=c;for(var _=0;_<7&&(o.wnomask[k]=1,k++,o.wdaymask[k]!==e.wkst);_++);}}if(_e(e.byweekno,1)){var k=c+m*7;if(c!==u&&(k-=7-u),k<n)for(var y=0;y<7&&(o.wnomask[k]=1,k+=1,o.wdaymask[k]!==e.wkst);y++);}if(c){var R=void 0;if(_e(e.byweekno,-1))R=-1;else{var S=En(Zr(r-1,1,1)),F=yt(7-S.valueOf()+e.wkst,7),q=hs(r-1)?366:365,ne=void 0;F>=4?(F=0,ne=q+yt(S-e.wkst,7)):ne=n-c,R=Math.floor(52+yt(ne,7)/4)}if(_e(e.byweekno,R))for(var k=0;k<c;k++)o.wnomask[k]=1}return o}function gN(r){var e=hs(r)?366:365,t=Zr(r,1,1),n=En(t);return e===365?{mmask:nv,mdaymask:av,nmdaymask:uv,wdaymask:Nm.slice(n),mrange:cv}:{mmask:iv,mdaymask:sv,nmdaymask:ov,wdaymask:Nm.slice(n),mrange:lv}}function fv(r,e,t,n,i,s){var a={lastyear:r,lastmonth:e,nwdaymask:[]},o=[];if(s.freq===V.YEARLY)if(Kt(s.bymonth))o=[[0,t]];else for(var u=0;u<s.bymonth.length;u++)e=s.bymonth[u],o.push(n.slice(e-1,e+1));else s.freq===V.MONTHLY&&(o=[n.slice(e-1,e+1)]);if(Kt(o))return a;a.nwdaymask=be(0,t);for(var u=0;u<o.length;u++)for(var l=o[u],c=l[0],d=l[1]-1,f=0;f<s.bynweekday.length;f++){var m=void 0,y=s.bynweekday[f],b=y[0],k=y[1];k<0?(m=d+(k+1)*7,m-=yt(i[m]-b,7)):(m=c+(k-1)*7,m+=yt(7-i[m]+b,7)),c<=m&&m<=d&&(a.nwdaymask[m]=1)}return a}function pv(r,e){e===void 0&&(e=0);var t=r%19,n=Math.floor(r/100),i=r%100,s=Math.floor(n/4),a=n%4,o=Math.floor((n+8)/25),u=Math.floor((n-o+1)/3),l=Math.floor(19*t+n-s-u+15)%30,c=Math.floor(i/4),d=i%4,f=Math.floor(32+2*a+2*c-l-d)%7,m=Math.floor((t+11*l+22*f)/451),y=Math.floor((l+f-7*m+114)/31),b=(l+f-7*m+114)%31+1,k=Date.UTC(r,y-1,b+e),_=Date.UTC(r,0,1);return[Math.ceil((k-_)/(1e3*60*60*24))]}var yN=function(){function r(e){this.options=e}return r.prototype.rebuild=function(e,t){var n=this.options;if(e!==this.lastyear&&(this.yearinfo=dv(e,n)),$e(n.bynweekday)&&(t!==this.lastmonth||e!==this.lastyear)){var i=this.yearinfo,s=i.yearlen,a=i.mrange,o=i.wdaymask;this.monthinfo=fv(e,t,s,a,o,n)}Fe(n.byeaster)&&(this.eastermask=pv(e,n.byeaster))},Object.defineProperty(r.prototype,"lastyear",{get:function(){return this.monthinfo?this.monthinfo.lastyear:null},enumerable:!1,configurable:!0}),Object.defineProperty(r.prototype,"lastmonth",{get:function(){return this.monthinfo?this.monthinfo.lastmonth:null},enumerable:!1,configurable:!0}),Object.defineProperty(r.prototype,"yearlen",{get:function(){return this.yearinfo.yearlen},enumerable:!1,configurable:!0}),Object.defineProperty(r.prototype,"yearordinal",{get:function(){return this.yearinfo.yearordinal},enumerable:!1,configurable:!0}),Object.defineProperty(r.prototype,"mrange",{get:function(){return this.yearinfo.mrange},enumerable:!1,configurable:!0}),Object.defineProperty(r.prototype,"wdaymask",{get:function(){return this.yearinfo.wdaymask},enumerable:!1,configurable:!0}),Object.defineProperty(r.prototype,"mmask",{get:function(){return this.yearinfo.mmask},enumerable:!1,configurable:!0}),Object.defineProperty(r.prototype,"wnomask",{get:function(){return this.yearinfo.wnomask},enumerable:!1,configurable:!0}),Object.defineProperty(r.prototype,"nwdaymask",{get:function(){return this.monthinfo?this.monthinfo.nwdaymask:[]},enumerable:!1,configurable:!0}),Object.defineProperty(r.prototype,"nextyearlen",{get:function(){return this.yearinfo.nextyearlen},enumerable:!1,configurable:!0}),Object.defineProperty(r.prototype,"mdaymask",{get:function(){return this.yearinfo.mdaymask},enumerable:!1,configurable:!0}),Object.defineProperty(r.prototype,"nmdaymask",{get:function(){return this.yearinfo.nmdaymask},enumerable:!1,configurable:!0}),r.prototype.ydayset=function(){return[pr(this.yearlen),0,this.yearlen]},r.prototype.mdayset=function(e,t){for(var n=this.mrange[t-1],i=this.mrange[t],s=be(null,this.yearlen),a=n;a<i;a++)s[a]=a;return[s,n,i]},r.prototype.wdayset=function(e,t,n){for(var i=be(null,this.yearlen+7),s=go(Zr(e,t,n))-this.yearordinal,a=s,o=0;o<7&&(i[s]=s,++s,this.wdaymask[s]!==this.options.wkst);o++);return[i,a,s]},r.prototype.ddayset=function(e,t,n){var i=be(null,this.yearlen),s=go(Zr(e,t,n))-this.yearordinal;return i[s]=s,[i,s,s+1]},r.prototype.htimeset=function(e,t,n,i){var s=this,a=[];return this.options.byminute.forEach(function(o){a=a.concat(s.mtimeset(e,o,n,i))}),Sn(a),a},r.prototype.mtimeset=function(e,t,n,i){var s=this.options.bysecond.map(function(a){return new bs(e,t,a,i)});return Sn(s),s},r.prototype.stimeset=function(e,t,n,i){return[new bs(e,t,n,i)]},r.prototype.getdayset=function(e){switch(e){case ye.YEARLY:return this.ydayset.bind(this);case ye.MONTHLY:return this.mdayset.bind(this);case ye.WEEKLY:return this.wdayset.bind(this);case ye.DAILY:return this.ddayset.bind(this);default:return this.ddayset.bind(this)}},r.prototype.gettimeset=function(e){switch(e){case ye.HOURLY:return this.htimeset.bind(this);case ye.MINUTELY:return this.mtimeset.bind(this);case ye.SECONDLY:return this.stimeset.bind(this)}},r}(),mv=yN;function hv(r,e,t,n,i,s){for(var a=[],o=0;o<r.length;o++){var u=void 0,l=void 0,c=r[o];c<0?(u=Math.floor(c/e.length),l=yt(c,e.length)):(u=Math.floor((c-1)/e.length),l=yt(c-1,e.length));for(var d=[],f=t;f<n;f++){var m=s[f];!Fe(m)||d.push(m)}var y=void 0;u<0?y=d.slice(u)[0]:y=d[u];var b=e[l],k=Xu(i.yearordinal+y),_=Zu(k,b);_e(a,_)||a.push(_)}return Sn(a),a}function el(r,e){var t=e.dtstart,n=e.freq,i=e.interval,s=e.until,a=e.bysetpos,o=e.count;if(o===0||i===0)return Dn(r);var u=Q_.fromDate(t),l=new mv(e);l.rebuild(u.year,u.month);for(var c=_N(l,u,e);;){var d=l.getdayset(n)(u.year,u.month,u.day),f=d[0],m=d[1],y=d[2],b=bN(f,m,y,l,e);if($e(a))for(var k=hv(a,c,m,y,l,f),_=0;_<k.length;_++){var R=k[_];if(s&&R>s)return Dn(r);if(R>=t){var S=gv(R,e);if(!r.accept(S)||o&&(--o,!o))return Dn(r)}}else for(var _=m;_<y;_++){var F=f[_];if(!!Fe(F))for(var q=Xu(l.yearordinal+F),ne=0;ne<c.length;ne++){var G=c[ne],R=Zu(q,G);if(s&&R>s)return Dn(r);if(R>=t){var S=gv(R,e);if(!r.accept(S)||o&&(--o,!o))return Dn(r)}}}if(e.interval===0||(u.add(e,b),u.year>Qu))return Dn(r);bo(n)||(c=l.gettimeset(n)(u.hour,u.minute,u.second,0)),l.rebuild(u.year,u.month)}}function TN(r,e,t){var n=t.bymonth,i=t.byweekno,s=t.byweekday,a=t.byeaster,o=t.bymonthday,u=t.bynmonthday,l=t.byyearday;return $e(n)&&!_e(n,r.mmask[e])||$e(i)&&!r.wnomask[e]||$e(s)&&!_e(s,r.wdaymask[e])||$e(r.nwdaymask)&&!r.nwdaymask[e]||a!==null&&!_e(r.eastermask,e)||($e(o)||$e(u))&&!_e(o,r.mdaymask[e])&&!_e(u,r.nmdaymask[e])||$e(l)&&(e<r.yearlen&&!_e(l,e+1)&&!_e(l,-r.yearlen+e)||e>=r.yearlen&&!_e(l,e+1-r.yearlen)&&!_e(l,-r.nextyearlen+e-r.yearlen))}function gv(r,e){return new Ri(r,e.tzid).rezonedDate()}function Dn(r){return r.getValue()}function bN(r,e,t,n,i){for(var s=!1,a=e;a<t;a++){var o=r[a];s=TN(n,o,i),s&&(r[o]=null)}return s}function _N(r,e,t){var n=t.freq,i=t.byhour,s=t.byminute,a=t.bysecond;return bo(n)?Z_(t):n>=V.HOURLY&&$e(i)&&!_e(i,e.hour)||n>=V.MINUTELY&&$e(s)&&!_e(s,e.minute)||n>=V.SECONDLY&&$e(a)&&!_e(a,e.second)?[]:r.gettimeset(n)(e.hour,e.minute,e.second,e.millisecond)}var mr={MO:new Ve(0),TU:new Ve(1),WE:new Ve(2),TH:new Ve(3),FR:new Ve(4),SA:new Ve(5),SU:new Ve(6)},_o={freq:ye.YEARLY,dtstart:null,interval:1,wkst:mr.MO,count:null,until:null,tzid:null,bysetpos:null,bymonth:null,bymonthday:null,bynmonthday:null,byyearday:null,byweekno:null,byweekday:null,bynweekday:null,byhour:null,byminute:null,bysecond:null,byeaster:null},J_=Object.keys(_o),V=function(){function r(e,t){e===void 0&&(e={}),t===void 0&&(t=!1),this._cache=t?null:new rv,this.origOptions=Pm(e);var n=X_(e).parsedOptions;this.options=n}return r.parseText=function(e,t){return To(e,t)},r.fromText=function(e,t){return V_(e,t)},r.fromString=function(e){return new r(r.parseString(e)||void 0)},r.prototype._iter=function(e){return el(e,this.options)},r.prototype._cacheGet=function(e,t){return this._cache?this._cache._cacheGet(e,t):!1},r.prototype._cacheAdd=function(e,t,n){if(!!this._cache)return this._cache._cacheAdd(e,t,n)},r.prototype.all=function(e){if(e)return this._iter(new Am("all",{},e));var t=this._cacheGet("all");return t===!1&&(t=this._iter(new On("all",{})),this._cacheAdd("all",t)),t},r.prototype.between=function(e,t,n,i){if(n===void 0&&(n=!1),!Di(e)||!Di(t))throw new Error("Invalid date passed in to RRule.between");var s={before:t,after:e,inc:n};if(i)return this._iter(new Am("between",s,i));var a=this._cacheGet("between",s);return a===!1&&(a=this._iter(new On("between",s)),this._cacheAdd("between",a,s)),a},r.prototype.before=function(e,t){if(t===void 0&&(t=!1),!Di(e))throw new Error("Invalid date passed in to RRule.before");var n={dt:e,inc:t},i=this._cacheGet("before",n);return i===!1&&(i=this._iter(new On("before",n)),this._cacheAdd("before",i,n)),i},r.prototype.after=function(e,t){if(t===void 0&&(t=!1),!Di(e))throw new Error("Invalid date passed in to RRule.after");var n={dt:e,inc:t},i=this._cacheGet("after",n);return i===!1&&(i=this._iter(new On("after",n)),this._cacheAdd("after",i,n)),i},r.prototype.count=function(){return this.all().length},r.prototype.toString=function(){return ko(this.origOptions)},r.prototype.toText=function(e,t,n){return z_(this,e,t,n)},r.prototype.isFullyConvertibleToText=function(){return K_(this)},r.prototype.clone=function(){return new r(this.origOptions)},r.FREQUENCIES=["YEARLY","MONTHLY","WEEKLY","DAILY","HOURLY","MINUTELY","SECONDLY"],r.YEARLY=ye.YEARLY,r.MONTHLY=ye.MONTHLY,r.WEEKLY=ye.WEEKLY,r.DAILY=ye.DAILY,r.HOURLY=ye.HOURLY,r.MINUTELY=ye.MINUTELY,r.SECONDLY=ye.SECONDLY,r.MO=mr.MO,r.TU=mr.TU,r.WE=mr.WE,r.TH=mr.TH,r.FR=mr.FR,r.SA=mr.SA,r.SU=mr.SU,r.parseString=wo,r.optionsToString=ko,r}();function yv(r,e,t,n,i,s){var a={},o=r.accept;function u(f,m){t.forEach(function(y){y.between(f,m,!0).forEach(function(b){a[Number(b)]=!0})})}i.forEach(function(f){var m=new Ri(f,s).rezonedDate();a[Number(m)]=!0}),r.accept=function(f){var m=Number(f);return isNaN(m)?o.call(this,f):!a[m]&&(u(new Date(m-1),new Date(m+1)),!a[m])?(a[m]=!0,o.call(this,f)):!0},r.method==="between"&&(u(r.args.after,r.args.before),r.accept=function(f){var m=Number(f);return a[m]?!0:(a[m]=!0,o.call(this,f))});for(var l=0;l<n.length;l++){var c=new Ri(n[l],s).rezonedDate();if(!r.accept(new Date(c.getTime())))break}e.forEach(function(f){el(r,f.options)});var d=r._result;switch(Sn(d),r.method){case"all":case"between":return d;case"before":return d.length&&d[d.length-1]||null;case"after":default:return d.length&&d[0]||null}}var Tv={dtstart:null,cache:!1,unfold:!1,forceset:!1,compatible:!1,tzid:null};function vN(r,e){var t=[],n=[],i=[],s=[],a=vo(r),o=a.dtstart,u=a.tzid,l=ON(r,e.unfold);return l.forEach(function(c){var d;if(!!c){var f=SN(c),m=f.name,y=f.parms,b=f.value;switch(m.toUpperCase()){case"RRULE":if(y.length)throw new Error("unsupported RRULE parm: ".concat(y.join(",")));t.push(wo(c));break;case"RDATE":var k=(d=/RDATE(?:;TZID=([^:=]+))?/i.exec(c))!==null&&d!==void 0?d:[],_=k[1];_&&!u&&(u=_),n=n.concat(bv(b,y));break;case"EXRULE":if(y.length)throw new Error("unsupported EXRULE parm: ".concat(y.join(",")));i.push(wo(b));break;case"EXDATE":s=s.concat(bv(b,y));break;case"DTSTART":break;default:throw new Error("unsupported property: "+m)}}}),{dtstart:o,tzid:u,rrulevals:t,rdatevals:n,exrulevals:i,exdatevals:s}}function wN(r,e){var t=vN(r,e),n=t.rrulevals,i=t.rdatevals,s=t.exrulevals,a=t.exdatevals,o=t.dtstart,u=t.tzid,l=e.cache===!1;if(e.compatible&&(e.forceset=!0,e.unfold=!0),e.forceset||n.length>1||i.length||s.length||a.length){var c=new Fm(l);return c.dtstart(o),c.tzid(u||void 0),n.forEach(function(f){c.rrule(new V(Im(f,o,u),l))}),i.forEach(function(f){c.rdate(f)}),s.forEach(function(f){c.exrule(new V(Im(f,o,u),l))}),a.forEach(function(f){c.exdate(f)}),e.compatible&&e.dtstart&&c.rdate(o),c}var d=n[0]||{};return new V(Im(d,d.dtstart||e.dtstart||o,d.tzid||e.tzid||u),l)}function tl(r,e){return e===void 0&&(e={}),wN(r,kN(e))}function Im(r,e,t){return pt(pt({},r),{dtstart:e,tzid:t})}function kN(r){var e=[],t=Object.keys(r),n=Object.keys(Tv);if(t.forEach(function(i){_e(n,i)||e.push(i)}),e.length)throw new Error("Invalid options: "+e.join(", "));return pt(pt({},Tv),r)}function EN(r){if(r.indexOf(":")===-1)return{name:"RRULE",value:r};var e=q_(r,":",1),t=e[0],n=e[1];return{name:t,value:n}}function SN(r){var e=EN(r),t=e.name,n=e.value,i=t.split(";");if(!i)throw new Error("empty property name");return{name:i[0].toUpperCase(),parms:i.slice(1),value:n}}function ON(r,e){if(e===void 0&&(e=!1),r=r&&r.trim(),!r)throw new Error("Invalid empty string");if(!e)return r.split(/\s/);for(var t=r.split(`
`),n=0;n<t.length;){var i=t[n]=t[n].replace(/\s+$/g,"");i?n>0&&i[0]===" "?(t[n-1]+=i.slice(1),t.splice(n,1)):n+=1:t.splice(n,1)}return t}function DN(r){r.forEach(function(e){if(!/(VALUE=DATE(-TIME)?)|(TZID=)/.test(e))throw new Error("unsupported RDATE/EXDATE parm: "+e)})}function bv(r,e){return DN(e),r.split(",").map(function(t){return yo(t)})}function _v(r){var e=this;return function(t){if(t!==void 0&&(e["_".concat(r)]=t),e["_".concat(r)]!==void 0)return e["_".concat(r)];for(var n=0;n<e._rrule.length;n++){var i=e._rrule[n].origOptions[r];if(i)return i}}}var Fm=function(r){ys(e,r);function e(t){t===void 0&&(t=!1);var n=r.call(this,{},t)||this;return n.dtstart=_v.apply(n,["dtstart"]),n.tzid=_v.apply(n,["tzid"]),n._rrule=[],n._rdate=[],n._exrule=[],n._exdate=[],n}return e.prototype._iter=function(t){return yv(t,this._rrule,this._exrule,this._rdate,this._exdate,this.tzid())},e.prototype.rrule=function(t){vv(t,this._rrule)},e.prototype.exrule=function(t){vv(t,this._exrule)},e.prototype.rdate=function(t){wv(t,this._rdate)},e.prototype.exdate=function(t){wv(t,this._exdate)},e.prototype.rrules=function(){return this._rrule.map(function(t){return tl(t.toString())})},e.prototype.exrules=function(){return this._exrule.map(function(t){return tl(t.toString())})},e.prototype.rdates=function(){return this._rdate.map(function(t){return new Date(t.getTime())})},e.prototype.exdates=function(){return this._exdate.map(function(t){return new Date(t.getTime())})},e.prototype.valueOf=function(){var t=[];return!this._rrule.length&&this._dtstart&&(t=t.concat(ko({dtstart:this._dtstart}))),this._rrule.forEach(function(n){t=t.concat(n.toString().split(`
`))}),this._exrule.forEach(function(n){t=t.concat(n.toString().split(`
`).map(function(i){return i.replace(/^RRULE:/,"EXRULE:")}).filter(function(i){return!/^DTSTART/.test(i)}))}),this._rdate.length&&t.push(kv("RDATE",this._rdate,this.tzid())),this._exdate.length&&t.push(kv("EXDATE",this._exdate,this.tzid())),t},e.prototype.toString=function(){return this.valueOf().join(`
`)},e.prototype.clone=function(){var t=new e(!!this._cache);return this._rrule.forEach(function(n){return t.rrule(n.clone())}),this._exrule.forEach(function(n){return t.exrule(n.clone())}),this._rdate.forEach(function(n){return t.rdate(new Date(n.getTime()))}),this._exdate.forEach(function(n){return t.exdate(new Date(n.getTime()))}),t},e}(V);function vv(r,e){if(!(r instanceof V))throw new TypeError(String(r)+" is not RRule instance");_e(e.map(String),String(r))||e.push(r)}function wv(r,e){if(!(r instanceof Date))throw new TypeError(String(r)+" is not Date instance");_e(e.map(Number),Number(r))||(e.push(r),Sn(e))}function kv(r,e,t){var n=!t||t.toUpperCase()==="UTC",i=n?"".concat(r,":"):"".concat(r,";TZID=").concat(t,":"),s=e.map(function(a){return gs(a.valueOf(),n)}).join(",");return"".concat(i).concat(s)}var st=class{constructor({rrule:e,baseOnToday:t,occurrence:n}){this.rrule=e,this.baseOnToday=t,this.occurrence=n}static fromText({recurrenceRuleText:e,occurrence:t}){try{let n=e.match(/^([a-zA-Z0-9, !]+?)( when done)?$/i);if(n==null)return null;let i=n[1].trim(),s=n[2]!==void 0,a=V.parseText(i);if(a!==null){let o=t.referenceDate;!s&&o!==null?a.dtstart=window.moment(o).startOf("day").utc(!0).toDate():a.dtstart=window.moment().startOf("day").utc(!0).toDate();let u=new V(a);return new st({rrule:u,baseOnToday:s,occurrence:t})}}catch(n){n instanceof Error&&console.log(n.message)}return null}toText(){let e=this.rrule.toText();return this.baseOnToday&&(e+=" when done"),e}next(e=window.moment()){let t=this.nextReferenceDate(e);return t===null?null:this.occurrence.next(t)}identicalTo(e){return this.baseOnToday!==e.baseOnToday||!this.occurrence.isIdenticalTo(e.occurrence)?!1:this.toText()===e.toText()}nextReferenceDate(e){return this.baseOnToday?this.nextReferenceDateFromToday(e.clone()).toDate():this.nextReferenceDateFromOriginalReferenceDate().toDate()}nextReferenceDateFromToday(e){let t=new V(de(K({},this.rrule.origOptions),{dtstart:e.startOf("day").utc(!0).toDate()}));return this.nextAfter(e.endOf("day"),t)}nextReferenceDateFromOriginalReferenceDate(){var t;let e=window.moment((t=this.occurrence.referenceDate)!=null?t:void 0).endOf("day");return this.nextAfter(e,this.rrule)}nextAfter(e,t){e.utc(!0);let n=window.moment.utc(t.after(e.toDate())),i=this.toText(),s=i.match(/every( \d+)? month(s)?(.*)?/);s!==null&&(i.includes(" on ")||(n=st.nextAfterMonths(e,n,t,s[1])));let a=i.match(/every( \d+)? year(s)?(.*)?/);return a!==null&&(n=st.nextAfterYears(e,n,t,a[1])),st.addTimezone(n)}static nextAfterMonths(e,t,n,i){let s=1;for(i!==void 0&&(s=Number.parseInt(i.trim(),10));st.isSkippingTooManyMonths(e,t,s);)t=st.fromOneDayEarlier(e,n);return t}static isSkippingTooManyMonths(e,t,n){let i=t.month()-e.month();return i+=(t.year()-e.year())*12,i>n}static nextAfterYears(e,t,n,i){let s=1;for(i!==void 0&&(s=Number.parseInt(i.trim(),10));st.isSkippingTooManyYears(e,t,s);)t=st.fromOneDayEarlier(e,n);return t}static isSkippingTooManyYears(e,t,n){return t.year()-e.year()>n}static fromOneDayEarlier(e,t){e.subtract(1,"days").endOf("day");let n=t.origOptions;return n.dtstart=e.startOf("day").toDate(),t=new V(n),window.moment.utc(t.after(e.toDate()))}static addTimezone(e){return window.moment.utc(e).set({hour:12,minute:0,second:0,millisecond:0}).local(!0).startOf("day")}};var _s=(m=>(m.Description="description",m.Id="id",m.DependsOn="dependsOn",m.Priority="priority",m.RecurrenceRule="recurrenceRule",m.OnCompletion="onCompletion",m.CreatedDate="createdDate",m.StartDate="startDate",m.ScheduledDate="scheduledDate",m.DueDate="dueDate",m.CancelledDate="cancelledDate",m.DoneDate="doneDate",m.BlockLink="blockLink",m))(_s||{}),rl=Object.values(_s),en=class{constructor(){this.visible={};this.tagsVisible=!0;rl.forEach(e=>{this.visible[e]=!0})}isShown(e){return this.visible[e]}areTagsShown(){return this.tagsVisible}hide(e){this.visible[e]=!1}setVisibility(e,t){this.visible[e]=t}setTagsVisibility(e){this.tagsVisible=e}get shownComponents(){return rl.filter(e=>this.visible[e])}get hiddenComponents(){return rl.filter(e=>!this.visible[e])}get toggleableComponents(){return rl.filter(e=>e!=="description"&&e!=="blockLink")}toggleVisibilityExceptDescriptionAndBlockLink(){this.toggleableComponents.forEach(e=>{this.visible[e]=!this.visible[e]}),this.setTagsVisibility(!this.areTagsShown())}};var xt=(a=>(a.TODO="TODO",a.DONE="DONE",a.IN_PROGRESS="IN_PROGRESS",a.CANCELLED="CANCELLED",a.NON_TASK="NON_TASK",a.EMPTY="EMPTY",a))(xt||{}),Qe=class{constructor(e,t,n,i,s="TODO"){this.symbol=e,this.name=t,this.nextStatusSymbol=n,this.availableAsCommand=i,this.type=s}};function Ev(r){let e=r.trim().toLowerCase();return e==="delete"?"delete":e==="keep"?"keep":""}function xN(r,e){return r.filter(t=>t!==e)}function RN(r,e){let t=r.status,n=e.status,i=n.type===t.type;return n.type!=="DONE"||i}function Sv(r,e){let t=e.length;if(r.onCompletion===""||r.onCompletion==="keep"||t===0)return e;let n=e[t-1];if(RN(r,n))return e;let s=r.onCompletion;return s==="delete"?xN(e,n):(console.log(`OnCompletion action ${s} not yet implemented.`),e)}var Mi=/[a-zA-Z0-9-_]+/,Lm=new RegExp(Mi.source+"( *, *"+Mi.source+" *)*"),Eo={prioritySymbols:{Highest:"\u{1F53A}",High:"\u23EB",Medium:"\u{1F53C}",Low:"\u{1F53D}",Lowest:"\u23EC",None:""},startDateSymbol:"\u{1F6EB}",createdDateSymbol:"\u2795",scheduledDateSymbol:"\u23F3",dueDateSymbol:"\u{1F4C5}",doneDateSymbol:"\u2705",cancelledDateSymbol:"\u274C",recurrenceSymbol:"\u{1F501}",onCompletionSymbol:"\u{1F3C1}",dependsOnSymbol:"\u26D4",idSymbol:"\u{1F194}",TaskFormatRegularExpressions:{priorityRegex:/([🔺⏫🔼🔽⏬])\uFE0F?$/u,startDateRegex:/🛫 *(\d{4}-\d{2}-\d{2})$/u,createdDateRegex:/➕ *(\d{4}-\d{2}-\d{2})$/u,scheduledDateRegex:/[⏳⌛] *(\d{4}-\d{2}-\d{2})$/u,dueDateRegex:/[📅📆🗓] *(\d{4}-\d{2}-\d{2})$/u,doneDateRegex:/✅ *(\d{4}-\d{2}-\d{2})$/u,cancelledDateRegex:/❌ *(\d{4}-\d{2}-\d{2})$/u,recurrenceRegex:/🔁 ?([a-zA-Z0-9, !]+)$/iu,onCompletionRegex:/🏁 ?([a-zA-Z]+)$/iu,dependsOnRegex:new RegExp("\u26D4\uFE0F? *("+Lm.source+")$","iu"),idRegex:new RegExp("\u{1F194} *("+Mi.source+")$","iu")}};function nl(r,e,t){return t?r?" "+e:` ${e} ${t}`:""}function vs(r,e,t){return t?r?" "+e:` ${e} ${t.format(Z.dateFormat)}`:""}function Ov(){let r=[];return Object.values(Eo.prioritySymbols).forEach(e=>{e.length>0&&r.push(e)}),Object.values(Eo).forEach(e=>{typeof e=="string"&&r.push(e)}),r}var Ci=class{constructor(e){this.symbols=e}serialize(e){let t=new en,n="",i=!1;for(let s of t.shownComponents)n+=this.componentToString(e,i,s);return n}componentToString(e,t,n){var b;let{prioritySymbols:i,startDateSymbol:s,createdDateSymbol:a,scheduledDateSymbol:o,doneDateSymbol:u,cancelledDateSymbol:l,recurrenceSymbol:c,onCompletionSymbol:d,dueDateSymbol:f,dependsOnSymbol:m,idSymbol:y}=this.symbols;switch(n){case"description":return e.description;case"priority":{let k="";return e.priority==="0"?k=" "+i.Highest:e.priority==="1"?k=" "+i.High:e.priority==="2"?k=" "+i.Medium:e.priority==="4"?k=" "+i.Low:e.priority==="5"&&(k=" "+i.Lowest),k}case"startDate":return vs(t,s,e.startDate);case"createdDate":return vs(t,a,e.createdDate);case"scheduledDate":return e.scheduledDateIsInferred?"":vs(t,o,e.scheduledDate);case"doneDate":return vs(t,u,e.doneDate);case"cancelledDate":return vs(t,l,e.cancelledDate);case"dueDate":return vs(t,f,e.dueDate);case"recurrenceRule":return e.recurrence?nl(t,c,e.recurrence.toText()):"";case"onCompletion":return e.onCompletion===""?"":nl(t,d,e.onCompletion);case"dependsOn":return e.dependsOn.length===0?"":nl(t,m,e.dependsOn.join(","));case"id":return nl(t,y,e.id);case"blockLink":return(b=e.blockLink)!=null?b:"";default:throw new Error(`Don't know how to render task component of type '${n}'`)}}parsePriority(e){let{prioritySymbols:t}=this.symbols;switch(e){case t.Lowest:return"5";case t.Low:return"4";case t.Medium:return"2";case t.High:return"1";case t.Highest:return"0";default:return"3"}}deserialize(e){let{TaskFormatRegularExpressions:t}=this.symbols,n,i="3",s=null,a=null,o=null,u=null,l=null,c=null,d="",f=null,m="",y="",b=[],k="",_=20,R=0;do{n=!1;let S=e.match(t.priorityRegex);S!==null&&(i=this.parsePriority(S[1]),e=e.replace(t.priorityRegex,"").trim(),n=!0);let F=e.match(t.doneDateRegex);F!==null&&(u=window.moment(F[1],Z.dateFormat),e=e.replace(t.doneDateRegex,"").trim(),n=!0);let q=e.match(t.cancelledDateRegex);q!==null&&(l=window.moment(q[1],Z.dateFormat),e=e.replace(t.cancelledDateRegex,"").trim(),n=!0);let ne=e.match(t.dueDateRegex);ne!==null&&(o=window.moment(ne[1],Z.dateFormat),e=e.replace(t.dueDateRegex,"").trim(),n=!0);let G=e.match(t.scheduledDateRegex);G!==null&&(a=window.moment(G[1],Z.dateFormat),e=e.replace(t.scheduledDateRegex,"").trim(),n=!0);let B=e.match(t.startDateRegex);B!==null&&(s=window.moment(B[1],Z.dateFormat),e=e.replace(t.startDateRegex,"").trim(),n=!0);let ge=e.match(t.createdDateRegex);ge!==null&&(c=window.moment(ge[1],Z.dateFormat),e=e.replace(t.createdDateRegex,"").trim(),n=!0);let Pe=e.match(t.recurrenceRegex);Pe!==null&&(d=Pe[1].trim(),e=e.replace(t.recurrenceRegex,"").trim(),n=!0);let j=e.match(t.onCompletionRegex);if(j!=null){e=e.replace(t.onCompletionRegex,"").trim();let I=j[1];m=Ev(I),n=!0}let $=e.match(Z.hashTagsFromEnd);if($!=null){e=e.replace(Z.hashTagsFromEnd,"").trim(),n=!0;let I=$[0].trim();k=k.length>0?[I,k].join(" "):I}let D=e.match(t.idRegex);D!=null&&(e=e.replace(t.idRegex,"").trim(),y=D[1].trim(),n=!0);let Y=e.match(t.dependsOnRegex);Y!=null&&(e=e.replace(t.dependsOnRegex,"").trim(),b=Y[1].replace(/ /g,"").split(",").filter(I=>I!==""),n=!0),R++}while(n&&R<=_);return d.length>0&&(f=st.fromText({recurrenceRuleText:d,occurrence:new fr({startDate:s,scheduledDate:a,dueDate:o})})),k.length>0&&(e+=" "+k),{description:e,priority:i,startDate:s,createdDate:c,scheduledDate:a,dueDate:o,doneDate:u,cancelledDate:l,recurrence:f,onCompletion:m,id:y,dependsOn:b,tags:ee.extractHashtags(e)}}};function Oo(r){let e="",t=!0;for(;t;)e=Math.random().toString(36).substring(2,6+2),r.includes(e)||(t=!1);return e}function il(r,e){return r.id!==""?r:new ee(de(K({},r),{id:Oo(e)}))}function Dv(r,e){let t=r;if(!r.dependsOn.includes(e.id)){let n=[...r.dependsOn,e.id];t=new ee(de(K({},r),{dependsOn:n}))}return t}function xv(r,e){let t=r;if(r.dependsOn.includes(e.id)){let n=r.dependsOn.filter(i=>i!==e.id);t=new ee(de(K({},r),{dependsOn:n}))}return t}function Zn(r){return r.replace(/([.*+?^${}()|[\]/\\])/g,"\\$1")}var Jn=class{constructor(){this._globalFilter="";this._removeGlobalFilter=!1}static getInstance(){return Jn.instance||(Jn.instance=new Jn),Jn.instance}get(){return this._globalFilter}set(e){this._globalFilter=e}reset(){this.set(Jn.empty)}isEmpty(){return this.get()===Jn.empty}equals(e){return this.get()===e}includedIn(e){let t=this.get();return e.includes(t)}prependTo(e){return this.get()+" "+e}removeAsWordFromDependingOnSettings(e){return this.getRemoveGlobalFilter()?this.removeAsWordFrom(e):e}getRemoveGlobalFilter(){return this._removeGlobalFilter}setRemoveGlobalFilter(e){this._removeGlobalFilter=e}removeAsWordFrom(e){if(this.isEmpty())return e;let t=RegExp("(^|\\s)"+Zn(this.get())+"($|\\s)","ug");return e.search(t)>-1&&(e=e.replace(t,"$1$2").replace("  "," ").trim()),e}removeAsSubstringFrom(e){let t=this.get();return e.replace(t,"").trim()}},ve=Jn;ve.empty="";var Mv=require("obsidian");var CN=20;function Ai(r){return ve.getInstance().removeAsWordFrom(r.description)}function AN(r,e){if(r==="")return e;let t=(0,Mv.prepareSimpleSearch)(r),n=-4;return e.map(a=>{let o=t(Ai(a));return o&&o.score>n?{item:a,match:o}:null}).filter(Boolean).sort((a,o)=>o.match.score-a.match.score).map(a=>a.item)}function sl(r,e,t,n,i){let s=AN(r,e);return s=s.filter(a=>!(a.isDone||a.description.includes("<%")&&a.description.includes("%>")||a.description===(t==null?void 0:t.description)&&a.taskLocation.path===(t==null?void 0:t.taskLocation.path)&&a.originalMarkdown===(t==null?void 0:t.originalMarkdown)||(n==null?void 0:n.includes(a))||(i==null?void 0:i.includes(a)))),t&&s.sort((a,o)=>{let u=a.taskLocation.path===t.taskLocation.path,l=o.taskLocation.path===t.taskLocation.path;return u&&l?Math.abs(a.taskLocation.lineNumber-t.taskLocation.lineNumber)-Math.abs(o.taskLocation.lineNumber-t.taskLocation.lineNumber):u?-1:l?1:0}),s.slice(0,CN)}var Um=5,PN=!0;globalThis.SHOW_DEPENDENCY_SUGGESTIONS=PN;function Cv(r){return globalThis.SHOW_DEPENDENCY_SUGGESTIONS&&r}function Wm(r,e,t){let n=[r.startDateSymbol,r.scheduledDateSymbol,r.dueDateSymbol].join("|");return(i,s,a,o,u,l)=>{let c=[],{postfix:d,insertSkip:f}=NN(t,i,s),m={line:i,cursorPos:s,settings:a,dataviewMode:t,postfix:d,insertSkip:f};return c=c.concat(qN(n,e,m)),c=c.concat($N(r.recurrenceSymbol,m)),Cv(u)&&(c=c.concat(GN(r.idSymbol,o,m)),c=c.concat(YN(r.dependsOnSymbol,o,m,l))),c=c.concat(jN(r.onCompletionSymbol,e,m)),c=c.concat(IN(r,u,m)),c.length>0&&!c.some(y=>y.suggestionType==="match")&&(t||c.unshift({suggestionType:"empty",displayText:"\u23CE",appendText:`
`})),c=c.slice(0,a.autoSuggestMaxItems),c}}function NN(r,e,t){let n=HN(e.substring(0,t),[["(",")"],["[","]"]])=="("?")":"]",i=r?n+" ":" ",s=r&&e.length>t&&e.charAt(t)===n?1:0;return{postfix:i,insertSkip:s}}function IN(r,e,t){let n=[],i=t.line;Pi(n,i,r.dueDateSymbol,"due date"),Pi(n,i,r.startDateSymbol,"start date"),Pi(n,i,r.scheduledDateSymbol,"scheduled date"),FN(n,r,t),Pi(n,i,r.recurrenceSymbol,"recurring (repeat)"),LN(n,r,t),Cv(e)&&(Pi(n,i,r.idSymbol,"id"),Pi(n,i,r.dependsOnSymbol,"depends on id")),Pi(n,i,r.onCompletionSymbol,"on completion");let s=UN(n,t);return s.length===0&&t.settings.autoSuggestMinMatch===0?n:s}function Pi(r,e,t,n){e.includes(t)||r.push({displayText:`${t} ${n}`,appendText:`${t} `})}function FN(r,e,t){if(!(i=>Object.values(e.prioritySymbols).some(s=>s.length>0&&i.includes(s)))(t.line)){let i=e.prioritySymbols,s=["High","Medium","Low","Highest","Lowest"];for(let a of s){let o=i[a];r.push({displayText:t.dataviewMode?`${o} priority`:`${o} ${a.toLowerCase()} priority`,appendText:`${o}${t.postfix}`,insertSkip:t.dataviewMode?t.insertSkip:void 0})}}}function LN(r,e,t){if(!t.line.includes(e.createdDateSymbol)){let i=Vt.parseDate("today",!0).format(Z.dateFormat);r.push({textToMatch:`${e.createdDateSymbol} created`,displayText:`${e.createdDateSymbol} created today (${i})`,appendText:`${e.createdDateSymbol} ${i}`+t.postfix,insertSkip:t.dataviewMode?t.insertSkip:void 0})}}function UN(r,e){let t=ws(/([a-zA-Z'_-]*)/g,e),n=[];if(t&&t.length>0){let i=t[0];if(i.length>=Math.max(1,e.settings.autoSuggestMinMatch)){let s=r.filter(a=>{var u;return((u=a.textToMatch)!=null?u:a.displayText).toLowerCase().includes(i.toLowerCase())});for(let a of s){let o=e.dataviewMode&&(a.displayText.includes("priority")||a.displayText.includes("created"))?i.length+e.insertSkip:i.length;n.push({suggestionType:"match",displayText:a.displayText,appendText:a.appendText,insertAt:t.index,insertSkip:o})}}}return n}function Av(r,e){let t=`${e}`,n=`${r} ${e}`;return{displayText:t,appendText:n}}function WN(r,e){let n=`${Vt.parseDate(e,!0).format(Z.dateFormat)}`,i=`${e} (${n})`,s=`${r} ${n}`;return{displayText:i,appendText:s}}function qN(r,e,t){let n=["today","tomorrow","Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday","next week","next month","next year"],i=[],s=new RegExp(`(${r})\\s*([0-9a-zA-Z ]*)`,"ug"),a=ws(s,t);if(a&&a.length>=2){let o=a[2];if(o.length<t.settings.autoSuggestMinMatch)return[];let u=o&&o.length>1?Vt.parseDate(Vu(o),!0):null;if(u!=null&&u.isValid()){let c=u.format(Z.dateFormat);al(t,a,[c],Av,i)}let l=qm(n,o,e,!0);al(t,a,l,WN,i)}return i}function $N(r,e){var a;let t=["every","every day","every week","every month","every month on the","every year","every week on Sunday","every week on Monday","every week on Tuesday","every week on Wednesday","every week on Thursday","every week on Friday","every week on Saturday"],n=[],i=new RegExp(`(${r})\\s*([0-9a-zA-Z ]*)`,"ug"),s=ws(i,e);if(s&&s.length>=2){let o=s[1],u=s[2];if(u.length<e.settings.autoSuggestMinMatch)return[];if(u.length>0){let f=(a=st.fromText({recurrenceRuleText:u,occurrence:new fr({startDate:null,scheduledDate:null,dueDate:null})}))==null?void 0:a.toText();if(f){let m=`${o} ${f}`+e.postfix;if(n.push({suggestionType:"match",displayText:`\u2705 ${f}`,appendText:m,insertAt:s.index,insertSkip:$m(s[0],e)}),s[0]==m)return[]}}let l=e.settings.autoSuggestMaxItems/2,c=qm(t,u,l,!1);c.length===0&&u.trim().length===0&&(c=t.slice(0,l)),al(e,s,c,(f,m)=>{let y=`${m}`,b=`${f} ${m}`;return{displayText:y,appendText:b}},n)}return n}function jN(r,e,t){let n=["delete","keep"],i=[],s=new RegExp(`(${r})\\s*([0-9a-zA-Z ]*)`,"ug"),a=ws(s,t);if(a&&a.length>=2){let o=a[2];if(o.length<t.settings.autoSuggestMinMatch)return[];let u=qm(n,o,e,!0);al(t,a,u,Av,i)}return i}function GN(r,e,t){let n=[],i=new RegExp(`(${r})\\s*(${Mi.source})?`,"ug"),s=ws(i,t);if(s&&s[0].trim().length<=r.length){let a=Oo(e.map(o=>o.id));n.push({suggestionType:"match",displayText:"generate unique id",appendText:`${r} ${a}`+t.postfix,insertAt:s.index,insertSkip:$m(s[0],t)})}return n}function YN(r,e,t,n){let i=[],s=t.dataviewMode?Zn("()[]"):Ov(),a=new RegExp(`(${r})([0-9a-zA-Z-_ ^,]*,)*([^,${s}]*)`,"ug"),o=ws(a,t);if(o&&o.length>=1){let u=o[2]||"",l=o[3],c=[];if(u){let d=u.split(",").map(f=>f.trim());c=e.filter(f=>f.id&&d.includes(f.id))}if(l.length>=t.settings.autoSuggestMinMatch){let d=sl(l.trim(),e,n,[],c);for(let f of d)i.push({suggestionType:"match",displayText:`${f.descriptionWithoutTags} - From: ${f.filename}.md`,appendText:`${r}${u}`,insertAt:o.index,insertSkip:r.length+u.length+l.length,taskItDependsOn:f})}}return i}function qm(r,e,t,n){let s=r.filter(a=>e&&e.length>=1&&a.toLowerCase().includes(e.toLowerCase())).slice(0,t);return n&&s.length===0&&(s=r.slice(0,t)),s}function al(r,e,t,n,i){let s=e[1];for(let a of t){let{displayText:o,appendText:u}=n(s,a);i.push({suggestionType:"match",displayText:o,appendText:u+r.postfix,insertAt:e.index,insertSkip:$m(e[0],r)})}}function ws(r,e){let t=e.line.matchAll(r),n=e.cursorPos;for(let i of t)if((i==null?void 0:i.index)&&i.index<n&&n<=i.index+i[0].length)return i}function BN(r,e){if(e.length===0)return!1;let t=Object.fromEntries(e.map(([i,s])=>[i,0])),n=Object.fromEntries(e.map(([i,s])=>[s,i]));for(let i of r)i in t?t[i]++:i in n&&(t[n[i]]=Math.max(0,t[n[i]]-1));return Object.values(t).some(i=>i>0)}function HN(r,e){if(e.length===0)return null;let t=Object.fromEntries(e.map(([s,a])=>[s,0])),n=Object.fromEntries(e.map(([s,a])=>[a,s])),i=[];for(let s=0;s<r.length;s++){let a=r[s];if(a in t)t[a]++,i.push({bracket:a,idx:s});else if(a in n){if(t[n[a]]>=1){for(let o=i.length-1;o>=0;o--)if(i[o].bracket==n[a]){i.splice(o,1);break}}t[n[a]]=Math.max(0,t[n[a]]-1)}}return i.length>0?i[i.length-1].bracket:null}function Pv(r,e){return(t,n,i,s,a)=>BN(t.slice(0,n),e)?r(t,n,i,s,a):[]}function Nv(r,e,t){let n=ve.getInstance().includedIn(r),i=VN(t,e,n);return typeof i=="boolean"?i:n&&zN(r,e.ch)}function VN(r,e,t){var n,i;return(i=(n=r==null?void 0:r.editorComponent)==null?void 0:n.showTasksPluginAutoSuggest)==null?void 0:i.call(n,e,r,t)}function zN(r,e){if(r.length===0)return!1;let t=ee.extractTaskComponents(r);if(!t)return!1;let n=t.indentation+t.listMarker+" ["+t.status.symbol+"] ";return e>=n.length}function $m(r,e){return e.dataviewMode?r.length+e.insertSkip:r.length}function Pr(r){let e=["(?:",/(?=[^\]]+\])\[/,"|",/(?=[^)]+\))\(/,")",/ */,r,/ */,/[)\]]/,/(?: *,)?/,/$/].map(t=>t instanceof RegExp?t.source:t).join("");return new RegExp(e,r.flags)}var jm={prioritySymbols:{Highest:"priority:: highest",High:"priority:: high",Medium:"priority:: medium",Low:"priority:: low",Lowest:"priority:: lowest",None:""},startDateSymbol:"start::",createdDateSymbol:"created::",scheduledDateSymbol:"scheduled::",dueDateSymbol:"due::",doneDateSymbol:"completion::",cancelledDateSymbol:"cancelled::",recurrenceSymbol:"repeat::",onCompletionSymbol:"onCompletion::",idSymbol:"id::",dependsOnSymbol:"dependsOn::",TaskFormatRegularExpressions:{priorityRegex:Pr(/priority:: *(highest|high|medium|low|lowest)/),startDateRegex:Pr(/start:: *(\d{4}-\d{2}-\d{2})/),createdDateRegex:Pr(/created:: *(\d{4}-\d{2}-\d{2})/),scheduledDateRegex:Pr(/scheduled:: *(\d{4}-\d{2}-\d{2})/),dueDateRegex:Pr(/due:: *(\d{4}-\d{2}-\d{2})/),doneDateRegex:Pr(/completion:: *(\d{4}-\d{2}-\d{2})/),cancelledDateRegex:Pr(/cancelled:: *(\d{4}-\d{2}-\d{2})/),recurrenceRegex:Pr(/repeat:: *([a-zA-Z0-9, !]+)/),onCompletionRegex:Pr(/onCompletion:: *([a-zA-Z]+)/),dependsOnRegex:Pr(new RegExp("dependsOn:: *("+Lm.source+")")),idRegex:Pr(new RegExp("id:: *("+Mi.source+")"))}},ol=class extends Ci{constructor(){super(jm)}parsePriority(e){switch(e){case"highest":return"0";case"high":return"1";case"medium":return"2";case"low":return"4";case"lowest":return"5";default:return"3"}}componentToString(e,t,n){let i=super.componentToString(e,t,n),s=["blockLink","description"];return i!==""&&!s.includes(n)?`  [${i.trim()}]`:i}};var hr=class{get symbol(){return this.configuration.symbol}get name(){return this.configuration.name}get nextStatusSymbol(){return this.configuration.nextStatusSymbol}get nextSymbol(){return this.configuration.nextStatusSymbol}get availableAsCommand(){return this.configuration.availableAsCommand}get type(){return this.configuration.type}get typeGroupText(){let e=this.type,t;switch(e){case"IN_PROGRESS":t="1";break;case"TODO":t="2";break;case"DONE":t="3";break;case"CANCELLED":t="4";break;case"NON_TASK":t="5";break;case"EMPTY":t="6";break}return`%%${t}%%${e}`}constructor(e){this.configuration=e}static getTypeForUnknownSymbol(e){switch(e){case"x":case"X":return"DONE";case"/":return"IN_PROGRESS";case"-":return"CANCELLED";case"":return"EMPTY";case" ":default:return"TODO"}}static getTypeFromStatusTypeString(e){return xt[e]||"TODO"}static createUnknownStatus(e){return new hr(new Qe(e,"Unknown","x",!1,"TODO"))}static createFromImportedValue(e){let t=e[0],n=hr.getTypeFromStatusTypeString(e[3]);return new hr(new Qe(t,e[1],e[2],!1,n))}isCompleted(){return this.type==="DONE"}isCancelled(){return this.type==="CANCELLED"}identicalTo(e){let t=["symbol","name","nextStatusSymbol","availableAsCommand","type"];for(let n of t)if(this[n]!==e[n])return!1;return!0}previewText(){let e="";return hr.tasksPluginCanCreateCommandsForStatuses()&&this.availableAsCommand&&(e=" Available as a command."),`- [${this.symbol}] => [${this.nextStatusSymbol}], name: '${this.name}', type: '${this.configuration.type}'.${e}`}static tasksPluginCanCreateCommandsForStatuses(){return!1}},re=hr;re.DONE=new hr(new Qe("x","Done"," ",!0,"DONE")),re.EMPTY=new hr(new Qe("","EMPTY","",!0,"EMPTY")),re.TODO=new hr(new Qe(" ","Todo","x",!0,"TODO")),re.CANCELLED=new hr(new Qe("-","Cancelled"," ",!0,"CANCELLED")),re.IN_PROGRESS=new hr(new Qe("/","In Progress","x",!0,"IN_PROGRESS")),re.NON_TASK=new hr(new Qe("Q","Non-Task","A",!0,"NON_TASK"));var ul=class{constructor(e=!1,t=!1,n=!1){this.ignoreSortInstructions=e,this.showTaskHiddenData=t,this.recordTimings=n}};var Le=class{constructor(){this.coreStatuses=[re.TODO.configuration,re.DONE.configuration],this.customStatuses=[re.IN_PROGRESS.configuration,re.CANCELLED.configuration]}static addStatus(e,t){e.push(t)}static replaceStatus(e,t,n){let i=this.findStatusIndex(t,e);return i<=-1?!1:(e.splice(i,1,n),!0)}static findStatusIndex(e,t){let n=new re(e);return t.findIndex(i=>new re(i).previewText()==n.previewText())}static deleteStatus(e,t){let n=this.findStatusIndex(t,e);return n<=-1?!1:(e.splice(n,1),!0)}static deleteAllCustomStatuses(e){e.customStatuses.splice(0)}static resetAllCustomStatuses(e){Le.deleteAllCustomStatuses(e),new Le().customStatuses.forEach(n=>{Le.addStatus(e.customStatuses,n)})}static bulkAddStatusCollection(e,t){let n=[];return t.forEach(i=>{e.customStatuses.find(a=>a.symbol==i[0]&&a.name==i[1]&&a.nextStatusSymbol==i[2])?n.push(`The status ${i[1]} (${i[0]}) is already added.`):Le.addStatus(e.customStatuses,re.createFromImportedValue(i))}),n}static allStatuses(e){return e.coreStatuses.concat(e.customStatuses)}static applyToStatusRegistry(e,t){t.clearStatuses(),Le.allStatuses(e).forEach(n=>{t.add(n)})}};var Iv=[{index:9999,internalName:"INTERNAL_TESTING_ENABLED_BY_DEFAULT",displayName:"Test Item. Used to validate the Feature Framework.",description:"Description",enabledByDefault:!0,stable:!1}];var xn=class{constructor(e,t,n,i,s,a){this.internalName=e;this.index=t;this.description=n;this.displayName=i;this.enabledByDefault=s;this.stable=a}static get values(){let e=[];return Iv.forEach(t=>{e=[...e,new xn(t.internalName,t.index,t.description,t.displayName,t.enabledByDefault,t.stable)]}),e}static get settingsFlags(){let e={};return xn.values.forEach(t=>{e[t.internalName]=t.enabledByDefault}),e}static fromString(e){for(let t of xn.values)if(e===t.internalName)return t;throw new RangeError(`Illegal argument passed to fromString(): ${e} does not correspond to any available Feature ${this.prototype.constructor.name}`)}};var Nr={tasksPluginEmoji:{displayName:"Tasks Emoji Format",taskSerializer:new Ci(Eo),buildSuggestions:Wm(Eo,Um,!1)},dataview:{displayName:"Dataview",taskSerializer:new ol,buildSuggestions:Pv(Wm(jm,Um,!0),[["(",")"],["[","]"]])}},Ym={globalQuery:"",globalFilter:"",removeGlobalFilter:!1,taskFormat:"tasksPluginEmoji",setCreatedDate:!1,setDoneDate:!0,setCancelledDate:!0,autoSuggestInEditor:!0,autoSuggestMinMatch:0,autoSuggestMaxItems:20,provideAccessKeys:!0,useFilenameAsScheduledDate:!1,filenameAsScheduledDateFormat:"",filenameAsDateFolders:[],recurrenceOnNextLine:!1,statusSettings:new Le,features:xn.settingsFlags,generalSettings:{},headingOpened:{},debugSettings:new ul,loggingOptions:{minLevels:{"":"info",tasks:"info","tasks.Cache":"info","tasks.Events":"info","tasks.File":"info","tasks.Query":"info","tasks.Task":"info"}}},Rn=K({},Ym);function Gm(r,e){for(let t in r)e[t]===void 0&&(e[t]=r[t])}var Q=()=>(Gm(xn.settingsFlags,Rn.features),Gm(Ym.loggingOptions.minLevels,Rn.loggingOptions.minLevels),Gm(Ym.debugSettings,Rn.debugSettings),Rn.statusSettings.customStatuses.forEach((r,e,t)=>{var i,s;let n=re.getTypeFromStatusTypeString(r.type);t[e]=new Qe((i=r.symbol)!=null?i:" ",r.name,(s=r.nextStatusSymbol)!=null?s:"x",r.availableAsCommand,n)}),K({},Rn)),ze=r=>(Rn=K(K({},Rn),r),Q());var Ni=(r,e)=>(Rn.generalSettings[r]=e,Q()),Fv=r=>{var e;return(e=Rn.features[r])!=null?e:!1};function Do(){return Nr[Q().taskFormat]}function ll(r){let t={"<":"&lt;",">":"&gt;","&":"&amp;",'"':"&quot;"}[r];return t!==void 0?t:r}function Lv(r){let e=[...r],t="";return e.forEach(n=>{t+=ll(n)}),t}var De=class{constructor(){this._registeredStatuses=[];this.addDefaultStatusTypes()}set(e){this.clearStatuses(),e.forEach(t=>{this.add(t)})}get registeredStatuses(){return this._registeredStatuses.filter(({symbol:e})=>e!==re.EMPTY.symbol)}static getInstance(){return De.instance||(De.instance=new De),De.instance}add(e){this.hasSymbol(e.symbol)||(e instanceof re?this._registeredStatuses.push(e):this._registeredStatuses.push(new re(e)))}bySymbol(e){return this.hasSymbol(e)?this.getSymbol(e):re.EMPTY}bySymbolOrCreate(e){return this.hasSymbol(e)?this.getSymbol(e):re.createUnknownStatus(e)}byName(e){return this._registeredStatuses.filter(({name:t})=>t===e).length>0?this._registeredStatuses.filter(({name:t})=>t===e)[0]:re.EMPTY}resetToDefaultStatuses(){this.clearStatuses(),this.addDefaultStatusTypes()}clearStatuses(){this._registeredStatuses=[]}getNextStatus(e){if(e.nextStatusSymbol!==""){let t=this.bySymbol(e.nextStatusSymbol);if(t!==null)return t}return re.EMPTY}getNextStatusOrCreate(e){let t=this.getNextStatus(e);return t.type!=="EMPTY"?t:re.createUnknownStatus(e.nextStatusSymbol)}getNextRecurrenceStatusOrCreate(e){let t=this.getNextStatusOrCreate(e),n=this.getNextRecurrenceStatusOfType(t,"TODO");if(n)return n;let i=this.getNextRecurrenceStatusOfType(t,"IN_PROGRESS");return i||this.bySymbolOrCreate(" ")}getNextRecurrenceStatusOfType(e,t){if(e.type===t)return e;let n=e;for(let i=0;i<this.registeredStatuses.length-1;i++)if(n=this.getNextStatusOrCreate(n),n.type===t)return n}findUnknownStatuses(e){let t=e.filter(s=>!this.hasSymbol(s.symbol)),n=new De,i=[];return t.forEach(s=>{if(n.hasSymbol(s.symbol))return;let a=De.copyStatusWithNewName(s,`Unknown (${s.symbol})`);i.push(a),n.add(a)}),i.sort((s,a)=>s.symbol.localeCompare(a.symbol,void 0,{numeric:!0}))}static copyStatusWithNewName(e,t){let n=new Qe(e.symbol,t,e.nextStatusSymbol,e.availableAsCommand,e.type);return new re(n)}getSymbol(e){return this._registeredStatuses.filter(({symbol:t})=>t===e)[0]}hasSymbol(e){return this._registeredStatuses.find(t=>t.symbol===e)!==void 0}addDefaultStatusTypes(){[re.TODO,re.IN_PROGRESS,re.DONE,re.CANCELLED].forEach(t=>{this.add(t)})}mermaidDiagram(e=!1){let t=this.registeredStatuses,n="mermaid",i=[],s=[];return t.forEach((a,o)=>{let u=this.getMermaidNodeLabel(a,e);i.push(`${o+1}${u}`);let l=this.getNextStatus(a);if(this.addEdgeIfNotToInternal(t,l,s,o,!1),a.type==="DONE"){let c=this.getNextRecurrenceStatusOrCreate(a);c.symbol!==l.symbol&&this.addEdgeIfNotToInternal(t,c,s,o,!0)}}),`
\`\`\`${n}
flowchart LR

classDef TODO        stroke:#f33,stroke-width:3px;
classDef DONE        stroke:#0c0,stroke-width:3px;
classDef IN_PROGRESS stroke:#fa0,stroke-width:3px;
classDef CANCELLED   stroke:#ddd,stroke-width:3px;
classDef NON_TASK    stroke:#99e,stroke-width:3px;

${i.join(`
`)}
${s.join(`
`)}

linkStyle default stroke:gray
\`\`\`
`}addEdgeIfNotToInternal(e,t,n,i,s){let a=e.findIndex(l=>l.symbol===t.symbol),o=a!==-1,u=t.type!=="EMPTY";if(o&&u){let l;s?l='-. "\u{1F501}" .-> ':l=" --> ";let c=`${i+1}${l}${a+1}`;n.push(c)}}getMermaidNodeLabel(e,t){let n=Lv(e.name),i=e.type;if(t){let s=ll(e.symbol),a=ll(e.nextStatusSymbol),o=`[${s}] -> [${a}]`,u=`'${n}'`,l=`(${i})`;return`["${u}<br>${o}<br>${l}"]:::${i}`}else return`["${n}"]:::${i}`}};var Uv=require("obsidian");var tn=class{constructor(e,t){this.name=e,this.sortOrder=t}get groupText(){return this.name!==""?`%%${this.sortOrder}%% ${this.name}`:""}};var mt=class{constructor(e){this._date=null;this._date=e}get moment(){return this._date}formatAsDate(e=""){return this.format(Z.dateFormat,e)}formatAsDateAndTime(e=""){return this.format(Z.dateTimeFormat,e)}format(e,t=""){return this._date?this._date.format(e):t}toISOString(e){return this._date?this._date.toISOString(e):""}get category(){let e=window.moment(),t=this.moment;return t?t.isBefore(e,"day")?new tn("Overdue",1):t.isSame(e,"day")?new tn("Today",2):t.isValid()?new tn("Future",3):new tn("Invalid date",0):new tn("Undated",4)}get fromNow(){let e=this.moment;if(!e)return new tn("",0);let t=this.fromNowOrder(e);return new tn(e.fromNow(),t)}fromNowOrder(e){if(!e.isValid())return 0;let t=window.moment(),n=e.isSameOrBefore(t,"day"),i=this.fromNowStartDateOfGroup(e,n,t);return Number((n?1:3)+i.format("YYYYMMDD"))}fromNowStartDateOfGroup(e,t,n){let i=e.fromNow(!0).split(" "),s,a=Number(i[0]);isNaN(a)?s=1:s=a;let o=i[1];return t?n.subtract(s,o):n.add(s,o)}postpone(e="days",t=1){if(!this._date)throw new Uv.Notice("Cannot postpone a null date");let n=window.moment().startOf("day");return this._date.isSameOrAfter(n,"day")?this._date.clone().add(t,e):n.add(t,e)}};var Mn=class{static priorityNameUsingNone(e){let t="ERROR";switch(e){case"1":t="High";break;case"0":t="Highest";break;case"2":t="Medium";break;case"3":t="None";break;case"4":t="Low";break;case"5":t="Lowest";break}return t}static priorityNameUsingNormal(e){return Mn.priorityNameUsingNone(e).replace("None","Normal")}};var QN=require("obsidian"),jv=ma($v());var Bm=class extends jv.EventEmitter2{constructor(){super(...arguments);this.options={minLevels:{"":"info",tasks:"info"}};this.consoleLoggerRegistered=!1;this.arrAvg=t=>t.reduce((n,i)=>n+i,0)/t.length}configure(t){return this.options=Object.assign({},this.options,t),this}getLogger(t){let n="none",i="";for(let s in this.options.minLevels)t.startsWith(s)&&s.length>=i.length&&(n=this.options.minLevels[s],i=s);return new Hm(this,t,n)}onLogEntry(t){return this.on("log",t),this}registerConsoleLogger(){return this.consoleLoggerRegistered?this:(this.onLogEntry(t=>{let n=`[${window.moment().format("YYYY-MM-DD-HH:mm:ss.SSS")}][${t.level}][${t.module}]`;switch(t.traceId&&(n+=`[${t.traceId}]`),n+=` ${t.message}`,t.objects===void 0&&(t.objects=""),t.level){case"trace":console.trace(n,t.objects);break;case"debug":console.debug(n,t.objects);break;case"info":console.info(n,t.objects);break;case"warn":console.warn(n,t.objects);break;case"error":console.error(n,t.objects);break;default:console.log(`{${t.level}} ${n}`,t.objects)}}),this.consoleLoggerRegistered=!0,this)}},Rt=new Bm,Hm=class{constructor(e,t,n){this.levels={trace:1,debug:2,info:3,warn:4,error:5};this.logManager=e,this.module=t,this.minLevel=this.levelToInt(n)}levelToInt(e){return e.toLowerCase()in this.levels?this.levels[e.toLowerCase()]:99}log(e,t,n){if(this.levelToInt(e)<this.minLevel)return;let s={level:e,module:this.module,message:t,objects:n,traceId:void 0};this.logManager.emit("log",s)}trace(e,t){this.log("trace",e,t)}debug(e,t){this.log("debug",e,t)}info(e,t){this.log("info",e,t)}warn(e,t){this.log("warn",e,t)}error(e,t){this.log("error",e,t)}logWithId(e,t,n,i){if(this.levelToInt(e)<this.minLevel)return;let a={level:e,module:this.module,message:n,objects:i,traceId:t};this.logManager.emit("log",a)}traceWithId(e,t,n){this.logWithId("trace",e,t,n)}debugWithId(e,t,n){this.logWithId("debug",e,t,n)}infoWithId(e,t,n){this.logWithId("info",e,t,n)}warnWithId(e,t,n){this.logWithId("warn",e,t,n)}errorWithId(e,t,n){this.logWithId("error",e,t,n)}};function Vm(r,e){let t=Rt.getLogger("tasks");switch(r){case"trace":t.trace(e);break;case"debug":t.debug(e);break;case"info":t.info(e);break;case"warn":t.warn(e);break;case"error":t.error(e);break;default:break}}function cl(r,e,t){r.debug(`${e}: task line number: ${t.taskLocation.lineNumber}. file path: "${t.path}"`),r.debug(`${e} original: ${t.originalMarkdown}`)}function dl(r,e,t){t.map((n,i)=>{r.debug(`${e} ==> ${i+1}   : ${n.toFileLineString()}`)})}var _t=class{static fromPath(e){let{useFilenameAsScheduledDate:t,filenameAsDateFolders:n}=Q();return!t||!this.matchesAnyFolder(n,e)?null:this.extractDateFromPath(e)}static matchesAnyFolder(e,t){return e.length===0?!0:e.some(n=>t.startsWith(n+"/"))}static extractDateFromPath(e){let t=Math.max(0,e.lastIndexOf("/")+1),n=e.lastIndexOf("."),i=e.substring(t,n),{filenameAsScheduledDateFormat:s}=Q();if(s!==""){let o=window.moment(i,s,!0);if(o.isValid())return o}let a=/(\d{4})-(\d{2})-(\d{2})/.exec(i);if(a||(a=/(\d{4})(\d{2})(\d{2})/.exec(i)),a){let o=window.moment([parseInt(a[1]),parseInt(a[2])-1,parseInt(a[3])]);if(o.isValid())return o}return null}static canApplyFallback({startDate:e,scheduledDate:t,dueDate:n}){return e===null&&n===null&&t===null}static updateTaskPath(e,t,n){let i=e.scheduledDate,s=e.scheduledDateIsInferred;return n===null?s&&(s=!1,i=null):s?i=n:this.canApplyFallback(e)&&(i=n,s=!0),new ee(de(K({},e),{taskLocation:t,scheduledDate:i,scheduledDateIsInferred:s}))}static removeInferredStatusIfNeeded(e,t){let n=e.scheduledDateIsInferred?e.scheduledDate:null;return t.map(i=>(n!==null&&!n.isSame(i.scheduledDate,"day")&&(i=new ee(de(K({},i),{scheduledDateIsInferred:!1}))),i))}};var Ir=class{static calculate(e){var n,i,s;let t=0;if((n=e.dueDate)!=null&&n.isValid()){let a=window.moment().startOf("day"),o=Math.round(a.diff(e.dueDate)/Ir.milliSecondsPerDay),u;o>=7?u=1:o>=-14?u=(o+14)*.8/21+.2:u=.2,t+=u*Ir.dueCoefficient}switch((i=e.scheduledDate)!=null&&i.isValid()&&window.moment().isSameOrAfter(e.scheduledDate)&&(t+=1*Ir.scheduledCoefficient),(s=e.startDate)!=null&&s.isValid()&&window.moment().isBefore(e.startDate)&&(t+=1*Ir.startedCoefficient),e.priority){case"0":t+=1.5*Ir.priorityCoefficient;break;case"1":t+=1*Ir.priorityCoefficient;break;case"2":t+=.65*Ir.priorityCoefficient;break;case"3":t+=.325*Ir.priorityCoefficient;break;case"5":t-=.3*Ir.priorityCoefficient;break}return t}},Cn=Ir;Cn.dueCoefficient=12,Cn.scheduledCoefficient=5,Cn.startedCoefficient=-3,Cn.priorityCoefficient=6,Cn.milliSecondsPerDay=1e3*60*60*24;var ee=class extends mn{constructor({status:t,description:n,taskLocation:i,indentation:s,listMarker:a,priority:o,createdDate:u,startDate:l,scheduledDate:c,dueDate:d,doneDate:f,cancelledDate:m,recurrence:y,onCompletion:b,dependsOn:k,id:_,blockLink:R,tags:S,originalMarkdown:F,scheduledDateIsInferred:q,parent:ne=null}){super(F,ne);this._urgency=null;this.status=t,this.description=n,this.indentation=s,this.listMarker=a,this.taskLocation=i,this.tags=S,this.priority=o,this.createdDate=u,this.startDate=l,this.scheduledDate=c,this.dueDate=d,this.doneDate=f,this.cancelledDate=m,this.recurrence=y,this.onCompletion=b,this.dependsOn=k,this.id=_,this.blockLink=R,this.scheduledDateIsInferred=q}static fromLine({line:t,taskLocation:n,fallbackDate:i}){let s=ee.extractTaskComponents(t);return s===null||!ve.getInstance().includedIn(s.body)?null:ee.parseTaskSignifiers(t,n,i)}static parseTaskSignifiers(t,n,i){let s=ee.extractTaskComponents(t);if(s===null)return null;let{taskSerializer:a}=Do(),o=a.deserialize(s.body),u=!1;return _t.canApplyFallback(o)&&i!==null&&(o.scheduledDate=i,u=!0),o.tags=o.tags.map(l=>l.trim()),o.tags=o.tags.filter(l=>!ve.getInstance().equals(l)),new ee(de(K(K({},s),o),{taskLocation:n,originalMarkdown:t,scheduledDateIsInferred:u}))}static extractTaskComponents(t){let n=t.match(Z.taskRegex);if(n===null)return null;let i=n[1],s=n[2],a=n[3],o=De.getInstance().bySymbolOrCreate(a),u=n[4].trim(),l=u.match(Z.blockLinkRegex),c=l!==null?l[0]:"";return c!==""&&(u=u.replace(Z.blockLinkRegex,"").trim()),{indentation:i,listMarker:s,status:o,body:u,blockLink:c}}toString(){return Do().taskSerializer.serialize(this)}toFileLineString(){return`${this.indentation}${this.listMarker} [${this.status.symbol}] ${this.toString()}`}toggle(){let t=Rt.getLogger("tasks.Task"),n="toggle()";cl(t,n,this);let i=De.getInstance().getNextStatusOrCreate(this.status),s=this.handleNewStatus(i);return dl(t,n,s),s}handleNewStatus(t,n=window.moment()){if(t.identicalTo(this.status))return[this];let{setDoneDate:i}=Q(),s=this.newDate(t,"DONE",this.doneDate,i,n),{setCancelledDate:a}=Q(),o=this.newDate(t,"CANCELLED",this.cancelledDate,a,n),u=new ee(de(K({},this),{status:t,doneDate:s,cancelledDate:o})),l=!t.isCompleted(),c=this.status.isCompleted(),d=this.recurrence===null;if(l||c||d)return[u];let m=this.recurrence.next(n);return m===null?[u]:[this.createNextOccurrence(t,m),u]}newDate(t,n,i,s,a){let o=null;return t.type===n&&(this.status.type!==n?s&&(o=a):o=i),o}createNextOccurrence(t,n){let{setCreatedDate:i}=Q(),s=null;i&&(s=window.moment());let a=null,o=null,l=De.getInstance().getNextRecurrenceStatusOrCreate(t);return new ee(de(K(K({},this),n),{status:l,blockLink:"",id:"",dependsOn:[],createdDate:s,cancelledDate:a,doneDate:o}))}toggleWithRecurrenceInUsersOrder(){let t=this.toggle();return this.putRecurrenceInUsersOrder(t)}handleNewStatusWithRecurrenceInUsersOrder(t,n=window.moment()){Rt.getLogger("tasks.Task").debug(`changed task ${this.taskLocation.path} ${this.taskLocation.lineNumber} ${this.originalMarkdown} status to '${t.symbol}'`);let s=this.handleNewStatus(t,n);return this.putRecurrenceInUsersOrder(s)}putRecurrenceInUsersOrder(t){let n=Sv(this,t),{recurrenceOnNextLine:i}=Q();return i?n.reverse():n}get isDone(){return this.status.type==="DONE"||this.status.type==="CANCELLED"||this.status.type==="NON_TASK"}isBlocked(t){if(this.dependsOn.length===0||this.isDone)return!1;for(let n of this.dependsOn)if(!!t.find(s=>s.id===n&&!s.isDone))return!0;return!1}isBlocking(t){return this.id===""||this.isDone?!1:t.some(n=>n.isDone?!1:n.dependsOn.includes(this.id))}get priorityNumber(){return Number.parseInt(this.priority)}get priorityNameGroupText(){let t=Mn.priorityNameUsingNormal(this.priority);return`%%${this.priority}%%${t} priority`}get descriptionWithoutTags(){return this.description.replace(Z.hashTags,"").trim()}get priorityName(){return Mn.priorityNameUsingNormal(this.priority)}get urgency(){return this._urgency===null&&(this._urgency=Cn.calculate(this)),this._urgency}get path(){return this.taskLocation.path}get cancelled(){return new mt(this.cancelledDate)}get created(){return new mt(this.createdDate)}get done(){return new mt(this.doneDate)}get due(){return new mt(this.dueDate)}get scheduled(){return new mt(this.scheduledDate)}get start(){return new mt(this.startDate)}get happensDates(){return Array.of(this.startDate,this.scheduledDate,this.dueDate)}get happens(){let t=this.happensDates,n=Array.from(t).sort(dr);for(let i of n)if(i!=null&&i.isValid())return new mt(i);return new mt(null)}get isRecurring(){return this.recurrence!==null}get recurrenceRule(){return this.recurrence?this.recurrence.toText():""}get heading(){return this.precedingHeader}get hasHeading(){return this.precedingHeader!==null}get file(){return this.taskLocation.tasksFile}get filename(){let t=this.path.match(/([^/]+)\.md$/);return t!==null?t[1]:null}get lineNumber(){return this.taskLocation.lineNumber}get sectionStart(){return this.taskLocation.sectionStart}get sectionIndex(){return this.taskLocation.sectionIndex}get precedingHeader(){return this.taskLocation.precedingHeader}getLinkText({isFilenameUnique:t}){let n;return t?n=this.filename:n="/"+this.path,n===null?null:(this.precedingHeader!==null&&this.precedingHeader!==n&&(n=n+" > "+this.precedingHeader),n)}identicalTo(t){var i,s;if(!super.identicalTo(t))return!1;let n=["description","path","indentation","listMarker","lineNumber","sectionStart","sectionIndex","precedingHeader","priority","blockLink","scheduledDateIsInferred","id","dependsOn","onCompletion"];for(let a of n)if(((i=this[a])==null?void 0:i.toString())!==((s=t[a])==null?void 0:s.toString()))return!1;if(!this.status.identicalTo(t.status)||this.tags.length!==t.tags.length||!this.tags.every(function(a,o){return a===t.tags[o]}))return!1;n=ee.allDateFields();for(let a of n){let o=this[a],u=t[a];if(dr(o,u)!==0)return!1}return this.recurrenceIdenticalTo(t)?this.file.rawFrontmatterIdenticalTo(t.file):!1}recurrenceIdenticalTo(t){let n=this.recurrence,i=t.recurrence;return!(n===null&&i!==null||n!==null&&i===null||n&&i&&!n.identicalTo(i))}static allDateFields(){return["createdDate","startDate","scheduledDate","dueDate","doneDate","cancelledDate"]}static extractHashtags(t){var n,i;return(i=(n=t.match(Z.hashTags))==null?void 0:n.map(s=>s.trim()))!=null?i:[]}};var xo=class{constructor(e){this.fetch=e;this._value=void 0}get value(){return this._value===void 0&&(this._value=this.fetch()),this._value}};var ht=class{constructor(e,t,n,i,s){this._tasksFile=e,this._lineNumber=t,this._sectionStart=n,this._sectionIndex=i,this._precedingHeader=s}static fromUnknownPosition(e){return new ht(e,0,0,0,null)}fromRenamedFile(e){return new ht(e,this.lineNumber,this.sectionStart,this.sectionIndex,this.precedingHeader)}get tasksFile(){return this._tasksFile}get path(){return this._tasksFile.path}get lineNumber(){return this._lineNumber}get sectionStart(){return this._sectionStart}get sectionIndex(){return this._sectionIndex}get precedingHeader(){return this._precedingHeader}get hasKnownPath(){return this.path!==""}};function XN(r,e,t,n,i,s){var y,b;let a=new ut(r,i),o=[],u=e.split(`
`),l=u.length,c=new xo(()=>_t.fromPath(r)),d=null,f=0,m=new Map;for(let k of t){let _=k.position.start.line;if(_>=l)return n.debug(`${r} Obsidian gave us a line number ${_} past the end of the file. ${l}.`),o;if((d===null||d.position.end.line<_)&&(d=Es.getSection(_,i.sections),f=0),d===null)continue;let R=u[_];if(R===void 0){n.debug(`${r}: line ${_} - ignoring 'undefined' line.`);continue}if(k.task!==void 0){let S;try{if(S=ee.fromLine({line:R,taskLocation:new ht(a,_,d.position.start.line,f,Es.getPrecedingHeader(_,i.headings)),fallbackDate:c.value}),S!==null){let F=(y=m.get(k.parent))!=null?y:null;F!==null&&(S=new ee(de(K({},S),{parent:F}))),m.set(_,S)}}catch(F){s(F,r,k,R);continue}S!==null&&(f++,o.push(S))}else{let S=k.position.start.line,F=(b=m.get(k.parent))!=null?b:null;m.set(S,new mn(u[S],F))}}return o}var Es=class{constructor({metadataCache:e,vault:t,workspace:n,events:i}){this.logger=Rt.getLogger("tasks.Cache");this.logger.debug("Creating Cache object"),this.metadataCache=e,this.metadataCacheEventReferences=[],this.vault=t,this.workspace=n,this.vaultEventReferences=[],this.events=i,this.eventsEventReferences=[],this.tasksMutex=new Jo,this.state="Cold",this.logger.debug("Cache.constructor(): state = Cold"),this.tasks=[],this.loadedAfterFirstResolve=!1,this.subscribeToCache(),this.workspace.onLayoutReady(()=>{this.subscribeToVault(),this.loadVault()}),this.subscribeToEvents()}unload(){this.logger.info("Unloading Cache");for(let e of this.metadataCacheEventReferences)this.metadataCache.offref(e);for(let e of this.vaultEventReferences)this.vault.offref(e);for(let e of this.eventsEventReferences)this.events.off(e)}getTasks(){return this.tasks}getState(){return this.state}notifySubscribers(){this.logger.debug("Cache.notifySubscribers()"),this.events.triggerCacheUpdate({tasks:this.tasks,state:this.state})}subscribeToCache(){this.logger.debug("Cache.subscribeToCache()");let e=this.metadataCache.on("resolved",()=>A(this,null,function*(){this.loadedAfterFirstResolve||(this.loadedAfterFirstResolve=!0,this.loadVault())}));this.metadataCacheEventReferences.push(e);let t=this.metadataCache.on("changed",n=>{this.tasksMutex.runExclusive(()=>{this.indexFile(n)})});this.metadataCacheEventReferences.push(t)}subscribeToVault(){this.logger.debug("Cache.subscribeToVault()");let{useFilenameAsScheduledDate:e}=Q(),t=this.vault.on("create",s=>{s instanceof ks.TFile&&(this.logger.debug(`Cache.subscribeToVault.createdEventReference() ${s.path}`),this.tasksMutex.runExclusive(()=>{this.indexFile(s)}))});this.vaultEventReferences.push(t);let n=this.vault.on("delete",s=>{s instanceof ks.TFile&&(this.logger.debug(`Cache.subscribeToVault.deletedEventReference() ${s.path}`),this.tasksMutex.runExclusive(()=>{this.tasks=this.tasks.filter(a=>a.path!==s.path),this.notifySubscribers()}))});this.vaultEventReferences.push(n);let i=this.vault.on("rename",(s,a)=>{s instanceof ks.TFile&&(this.logger.debug(`Cache.subscribeToVault.renamedEventReference() ${s.path}`),this.tasksMutex.runExclusive(()=>{let o=this.metadataCache.getFileCache(s),u=new ut(s.path,o!=null?o:void 0),l=new xo(()=>_t.fromPath(s.path));this.tasks=this.tasks.map(c=>{if(c.path!==a)return c;let d=c.taskLocation.fromRenamedFile(u);return e?_t.updateTaskPath(c,d,l.value):new ee(de(K({},c),{taskLocation:d}))}),this.notifySubscribers()}))});this.vaultEventReferences.push(i)}subscribeToEvents(){this.logger.debug("Cache.subscribeToEvents()");let e=this.events.onRequestCacheUpdate(t=>{t({tasks:this.tasks,state:this.state})});this.eventsEventReferences.push(e)}loadVault(){return this.logger.debug("Cache.loadVault()"),this.tasksMutex.runExclusive(()=>A(this,null,function*(){this.state="Initializing",this.logger.debug("Cache.loadVault(): state = Initializing"),yield Promise.all(this.vault.getMarkdownFiles().map(e=>this.indexFile(e))),this.state="Warm",this.logger.debug("Cache.loadVault(): state = Warm"),this.notifySubscribers()}))}indexFile(e){return A(this,null,function*(){let t=this.metadataCache.getFileCache(e);if(t==null)return;if(!e.path.endsWith(".md")){this.logger.debug("indexFile: skipping non-markdown file: "+e.path);return}this.logger.debug("Cache.indexFile: "+e.path);let n=this.tasks.filter(a=>a.path===e.path),i=t.listItems,s=[];if(i!==void 0){let a=yield this.vault.cachedRead(e);s=this.getTasksFromFileContent(a,i,t,e.path,this.reportTaskParsingErrorToUser,this.logger)}mn.listsAreIdentical(n,s)||(this.tasks=this.tasks.filter(a=>a.path!==e.path),this.tasks.push(...s),this.logger.debug("Cache.indexFile: "+e.path+`: read ${s.length} task(s)`),this.notifySubscribers())})}getTasksFromFileContent(e,t,n,i,s,a){return XN(i,e,t,a,n,s)}reportTaskParsingErrorToUser(e,t,n,i){let s=`There was an error reading one of the tasks in this vault.
The following task has been ignored, to prevent Tasks queries getting stuck with 'Loading Tasks ...'
Error: ${e}
File: ${t}
Line number: ${n.position.start.line}
Task line: ${i}

Please create a bug report for this message at
https://github.com/obsidian-tasks-group/obsidian-tasks/issues/new/choose
to help us find and fix the underlying issue.

Include:
- either a screenshot of the error popup, or copy the text from the console, if on a desktop machine.
- the output from running the Obsidian command 'Show debug info'

The error popup will only be shown when Tasks is starting up, but if the error persists,
it will be shown in the console every time this file is edited during the Obsidian
session.
`;this.logger.error(s),e instanceof Error&&this.logger.error(e.stack?e.stack:"Cannot determine stack"),this.state==="Initializing"&&new ks.Notice(s,1e4)}static getSection(e,t){if(t===void 0)return null;for(let n of t)if(n.position.start.line<=e&&n.position.end.line>=e)return n;return null}static getPrecedingHeader(e,t){if(t===void 0)return null;let n=null;for(let i of t){if(i.position.start.line>e)return n;n=i.heading}return n}};var gh=require("obsidian");var $w=require("obsidian");function Ue(){}function Xm(r){return r()}function Gv(){return Object.create(null)}function Ut(r){r.forEach(Xm)}function ml(r){return typeof r=="function"}function rn(r,e){return r!=r?e==e:r!==e||r&&typeof r=="object"||typeof r=="function"}function Yv(r){return Object.keys(r).length===0}var Bv=typeof window!="undefined"?window:typeof globalThis!="undefined"?globalThis:global,Mo=class{constructor(e){this.options=e,this._listeners="WeakMap"in Bv?new WeakMap:void 0}observe(e,t){return this._listeners.set(e,t),this._getObserver().observe(e,this.options),()=>{this._listeners.delete(e),this._observer.unobserve(e)}}_getObserver(){var e;return(e=this._observer)!==null&&e!==void 0?e:this._observer=new ResizeObserver(t=>{var n;for(let i of t)Mo.entries.set(i.target,i),(n=this._listeners.get(i.target))===null||n===void 0||n(i)})}};Mo.entries="WeakMap"in Bv?new WeakMap:void 0;var Hv=!1;function ZN(){Hv=!0}function JN(){Hv=!1}function U(r,e){r.appendChild(e)}function pe(r,e,t){r.insertBefore(e,t||null)}function oe(r){r.parentNode&&r.parentNode.removeChild(r)}function Li(r,e){for(let t=0;t<r.length;t+=1)r[t]&&r[t].d(e)}function X(r){return document.createElement(r)}function eI(r){return document.createElementNS("http://www.w3.org/2000/svg",r)}function Me(r){return document.createTextNode(r)}function ue(){return Me(" ")}function Vv(){return Me("")}function xe(r,e,t,n){return r.addEventListener(e,t,n),()=>r.removeEventListener(e,t,n)}function zv(r){return function(e){return e.preventDefault(),r.call(this,e)}}function W(r,e,t){t==null?r.removeAttribute(e):r.getAttribute(e)!==t&&r.setAttribute(e,t)}function Kv(r){let e;return{p(...t){e=t,e.forEach(n=>r.push(n))},r(){e.forEach(t=>r.splice(r.indexOf(t),1))}}}function tI(r){return Array.from(r.childNodes)}function Fr(r,e){e=""+e,r.data!==e&&(r.data=e)}function yr(r,e){r.value=e==null?"":e}function Zm(r,e,t){for(let n=0;n<r.options.length;n+=1){let i=r.options[n];if(i.__value===e){i.selected=!0;return}}(!t||e!==void 0)&&(r.selectedIndex=-1)}function Qv(r){let e=r.querySelector(":checked");return e&&e.__value}var fl;function rI(){if(fl===void 0){fl=!1;try{typeof window!="undefined"&&window.parent&&window.parent.document}catch(r){fl=!0}}return fl}function Xv(r,e){getComputedStyle(r).position==="static"&&(r.style.position="relative");let n=X("iframe");n.setAttribute("style","display: block; position: absolute; top: 0; left: 0; width: 100%; height: 100%; overflow: hidden; border: 0; opacity: 0; pointer-events: none; z-index: -1;"),n.setAttribute("aria-hidden","true"),n.tabIndex=-1;let i=rI(),s;return i?(n.src="data:text/html,<script>onresize=function(){parent.postMessage(0,'*')}<\/script>",s=xe(window,"message",a=>{a.source===n.contentWindow&&e()})):(n.src="about:blank",n.onload=()=>{s=xe(n.contentWindow,"resize",e),e()}),U(r,n),()=>{(i||s&&n.contentWindow)&&s(),oe(n)}}function An(r,e,t){r.classList[t?"add":"remove"](e)}var xs=class{constructor(e=!1){this.is_svg=!1,this.is_svg=e,this.e=this.n=null}c(e){this.h(e)}m(e,t,n=null){this.e||(this.is_svg?this.e=eI(t.nodeName):this.e=X(t.nodeType===11?"TEMPLATE":t.nodeName),this.t=t.tagName!=="TEMPLATE"?t:t.content,this.c(e)),this.i(n)}h(e){this.e.innerHTML=e,this.n=Array.from(this.e.nodeName==="TEMPLATE"?this.e.content.childNodes:this.e.childNodes)}i(e){for(let t=0;t<this.n.length;t+=1)pe(this.t,this.n[t],e)}p(e){this.d(),this.h(e),this.i(this.a)}d(){this.n.forEach(oe)}};var Co;function Ro(r){Co=r}function nI(){if(!Co)throw new Error("Function called outside component initialization");return Co}function Jm(r){nI().$$.on_mount.push(r)}var Os=[];var je=[],Ds=[],Km=[],iI=Promise.resolve(),Qm=!1;function sI(){Qm||(Qm=!0,iI.then(Zv))}function Fi(r){Ds.push(r)}function Mt(r){Km.push(r)}var zm=new Set,Ss=0;function Zv(){if(Ss!==0)return;let r=Co;do{try{for(;Ss<Os.length;){let e=Os[Ss];Ss++,Ro(e),aI(e.$$)}}catch(e){throw Os.length=0,Ss=0,e}for(Ro(null),Os.length=0,Ss=0;je.length;)je.pop()();for(let e=0;e<Ds.length;e+=1){let t=Ds[e];zm.has(t)||(zm.add(t),t())}Ds.length=0}while(Os.length);for(;Km.length;)Km.pop()();Qm=!1,zm.clear(),Ro(r)}function aI(r){if(r.fragment!==null){r.update(),Ut(r.before_update);let e=r.dirty;r.dirty=[-1],r.fragment&&r.fragment.p(r.ctx,e),r.after_update.forEach(Fi)}}function oI(r){let e=[],t=[];Ds.forEach(n=>r.indexOf(n)===-1?e.push(n):t.push(n)),t.forEach(n=>n()),Ds=e}var pl=new Set,Ii;function Jv(){Ii={r:0,c:[],p:Ii}}function ew(){Ii.r||Ut(Ii.c),Ii=Ii.p}function Wt(r,e){r&&r.i&&(pl.delete(r),r.i(e))}function Qt(r,e,t,n){if(r&&r.o){if(pl.has(r))return;pl.add(r),Ii.c.push(()=>{pl.delete(r),n&&(t&&r.d(1),n())}),r.o(e)}else n&&n()}var uI=["allowfullscreen","allowpaymentrequest","async","autofocus","autoplay","checked","controls","default","defer","disabled","formnovalidate","hidden","inert","ismap","loop","multiple","muted","nomodule","novalidate","open","playsinline","readonly","required","reversed","selected"],_G=new Set([...uI]);function Ct(r,e,t){let n=r.$$.props[e];n!==void 0&&(r.$$.bound[n]=t,t(r.$$.ctx[n]))}function Lr(r){r&&r.c()}function Tr(r,e,t,n){let{fragment:i,after_update:s}=r.$$;i&&i.m(e,t),n||Fi(()=>{let a=r.$$.on_mount.map(Xm).filter(ml);r.$$.on_destroy?r.$$.on_destroy.push(...a):Ut(a),r.$$.on_mount=[]}),s.forEach(Fi)}function Xt(r,e){let t=r.$$;t.fragment!==null&&(oI(t.after_update),Ut(t.on_destroy),t.fragment&&t.fragment.d(e),t.on_destroy=t.fragment=null,t.ctx=[])}function lI(r,e){r.$$.dirty[0]===-1&&(Os.push(r),sI(),r.$$.dirty.fill(0)),r.$$.dirty[e/31|0]|=1<<e%31}function nn(r,e,t,n,i,s,a,o=[-1]){let u=Co;Ro(r);let l=r.$$={fragment:null,ctx:[],props:s,update:Ue,not_equal:i,bound:Gv(),on_mount:[],on_destroy:[],on_disconnect:[],before_update:[],after_update:[],context:new Map(e.context||(u?u.$$.context:[])),callbacks:Gv(),dirty:o,skip_bound:!1,root:e.target||u.$$.root};a&&a(l.root);let c=!1;if(l.ctx=t?t(r,e.props||{},(d,f,...m)=>{let y=m.length?m[0]:f;return l.ctx&&i(l.ctx[d],l.ctx[d]=y)&&(!l.skip_bound&&l.bound[d]&&l.bound[d](y),c&&lI(r,d)),f}):[],l.update(),c=!0,Ut(l.before_update),l.fragment=n?n(l.ctx):!1,e.target){if(e.hydrate){ZN();let d=tI(e.target);l.fragment&&l.fragment.l(d),d.forEach(oe)}else l.fragment&&l.fragment.c();e.intro&&Wt(r.$$.fragment),Tr(r,e.target,e.anchor,e.customElement),JN(),Zv()}Ro(u)}var cI;typeof HTMLElement=="function"&&(cI=class extends HTMLElement{constructor(){super(),this.attachShadow({mode:"open"})}connectedCallback(){let{on_mount:r}=this.$$;this.$$.on_disconnect=r.map(Xm).filter(ml);for(let e in this.$$.slotted)this.appendChild(this.$$.slotted[e])}attributeChangedCallback(r,e,t){this[r]=t}disconnectedCallback(){Ut(this.$$.on_disconnect)}$destroy(){Xt(this,1),this.$destroy=Ue}$on(r,e){if(!ml(e))return Ue;let t=this.$$.callbacks[r]||(this.$$.callbacks[r]=[]);return t.push(e),()=>{let n=t.indexOf(e);n!==-1&&t.splice(n,1)}}$set(r){this.$$set&&!Yv(r)&&(this.$$.skip_bound=!0,this.$$set(r),this.$$.skip_bound=!1)}});var gr=class{$destroy(){Xt(this,1),this.$destroy=Ue}$on(e,t){if(!ml(t))return Ue;let n=this.$$.callbacks[e]||(this.$$.callbacks[e]=[]);return n.push(t),()=>{let i=n.indexOf(t);i!==-1&&n.splice(i,1)}}$set(e){this.$$set&&!Yv(e)&&(this.$$.skip_bound=!0,this.$$set(e),this.$$.skip_bound=!1)}};function tw(r,e,t,n){function i(s){return s instanceof t?s:new t(function(a){a(s)})}return new(t||(t=Promise))(function(s,a){function o(c){try{l(n.next(c))}catch(d){a(d)}}function u(c){try{l(n.throw(c))}catch(d){a(d)}}function l(c){c.done?s(c.value):i(c.value).then(o,u)}l((n=n.apply(r,e||[])).next())})}function Rs(r){return r.charAt(0).toUpperCase()+r.slice(1)}function gt(r,e){if(e===null)return Rs(r);let t=r.toLowerCase().indexOf(e.toLowerCase());if(t===-1)return`${Rs(r)} (<span class="accesskey">${e.toLowerCase()}</span>)`;let n=r.substring(0,t);return n+='<span class="accesskey">',t===0?n+=r.substring(t,t+1).toUpperCase():n+=r.substring(t,t+1),n+="</span>",n+=r.substring(t+1),n=Rs(n),n}function dI(r){let e,t=gt(r[2],r[4])+"",n,i,s,a,o,u,l,c,d;return{c(){e=X("label"),n=ue(),i=X("input"),s=ue(),a=X("code"),o=Me(r[3]),u=ue(),l=new xs(!1),W(e,"for",r[2]),W(i,"id",r[2]),W(i,"type","text"),W(i,"class","tasks-modal-date-input"),W(i,"placeholder",fI),W(i,"accesskey",r[4]),An(i,"tasks-modal-error",!r[1]),l.a=null,W(a,"class","tasks-modal-parsed-date")},m(f,m){pe(f,e,m),e.innerHTML=t,pe(f,n,m),pe(f,i,m),yr(i,r[0]),pe(f,s,m),pe(f,a,m),U(a,o),U(a,u),l.m(r[5],a),c||(d=xe(i,"input",r[7]),c=!0)},p(f,[m]){m&20&&t!==(t=gt(f[2],f[4])+"")&&(e.innerHTML=t),m&4&&W(e,"for",f[2]),m&4&&W(i,"id",f[2]),m&16&&W(i,"accesskey",f[4]),m&1&&i.value!==f[0]&&yr(i,f[0]),m&2&&An(i,"tasks-modal-error",!f[1]),m&8&&Fr(o,f[3]),m&32&&l.p(f[5])},i:Ue,o:Ue,d(f){f&&oe(e),f&&oe(n),f&&oe(i),f&&oe(s),f&&oe(a),c=!1,d()}}}var fI="Try 'Mon' or 'tm' then space";function pI(r,e,t){let{id:n}=e,{dateSymbol:i}=e,{date:s}=e,{isDateValid:a}=e,{forwardOnly:o}=e,{accesskey:u}=e,l;function c(){s=this.value,t(0,s),t(2,n),t(6,o),t(5,l)}return r.$$set=d=>{"id"in d&&t(2,n=d.id),"dateSymbol"in d&&t(3,i=d.dateSymbol),"date"in d&&t(0,s=d.date),"isDateValid"in d&&t(1,a=d.isDateValid),"forwardOnly"in d&&t(6,o=d.forwardOnly),"accesskey"in d&&t(4,u=d.accesskey)},r.$$.update=()=>{if(r.$$.dirty&101){e:t(0,s=Vu(s)),t(5,l=U_(n,s,o)),t(1,a=!l.includes("invalid"))}},[s,a,n,i,u,l,o,c]}var eh=class extends gr{constructor(e){super(),nn(this,e,pI,dI,rn,{id:2,dateSymbol:3,date:0,isDateValid:1,forwardOnly:6,accesskey:4})}},Ui=eh;var Wi=Math.min,qt=Math.max,Po=Math.round;var Pn=r=>({x:r,y:r}),mI={left:"right",right:"left",bottom:"top",top:"bottom"},hI={start:"end",end:"start"};function th(r,e,t){return qt(r,Wi(e,t))}function Ms(r,e){return typeof r=="function"?r(e):r}function Nn(r){return r.split("-")[0]}function Cs(r){return r.split("-")[1]}function rh(r){return r==="x"?"y":"x"}function nh(r){return r==="y"?"height":"width"}function As(r){return["top","bottom"].includes(Nn(r))?"y":"x"}function ih(r){return rh(As(r))}function rw(r,e,t){t===void 0&&(t=!1);let n=Cs(r),i=ih(r),s=nh(i),a=i==="x"?n===(t?"end":"start")?"right":"left":n==="start"?"bottom":"top";return e.reference[s]>e.floating[s]&&(a=Ao(a)),[a,Ao(a)]}function nw(r){let e=Ao(r);return[hl(r),e,hl(e)]}function hl(r){return r.replace(/start|end/g,e=>hI[e])}function gI(r,e,t){let n=["left","right"],i=["right","left"],s=["top","bottom"],a=["bottom","top"];switch(r){case"top":case"bottom":return t?e?i:n:e?n:i;case"left":case"right":return e?s:a;default:return[]}}function iw(r,e,t,n){let i=Cs(r),s=gI(Nn(r),t==="start",n);return i&&(s=s.map(a=>a+"-"+i),e&&(s=s.concat(s.map(hl)))),s}function Ao(r){return r.replace(/left|right|bottom|top/g,e=>mI[e])}function yI(r){return K({top:0,right:0,bottom:0,left:0},r)}function sw(r){return typeof r!="number"?yI(r):{top:r,right:r,bottom:r,left:r}}function qi(r){return de(K({},r),{top:r.y,left:r.x,right:r.x+r.width,bottom:r.y+r.height})}function aw(r,e,t){let{reference:n,floating:i}=r,s=As(e),a=ih(e),o=nh(a),u=Nn(e),l=s==="y",c=n.x+n.width/2-i.width/2,d=n.y+n.height/2-i.height/2,f=n[o]/2-i[o]/2,m;switch(u){case"top":m={x:c,y:n.y-i.height};break;case"bottom":m={x:c,y:n.y+n.height};break;case"right":m={x:n.x+n.width,y:d};break;case"left":m={x:n.x-i.width,y:d};break;default:m={x:n.x,y:n.y}}switch(Cs(e)){case"start":m[a]-=f*(t&&l?-1:1);break;case"end":m[a]+=f*(t&&l?-1:1);break}return m}var ow=(r,e,t)=>A(void 0,null,function*(){let{placement:n="bottom",strategy:i="absolute",middleware:s=[],platform:a}=t,o=s.filter(Boolean),u=yield a.isRTL==null?void 0:a.isRTL(e),l=yield a.getElementRects({reference:r,floating:e,strategy:i}),{x:c,y:d}=aw(l,n,u),f=n,m={},y=0;for(let b=0;b<o.length;b++){let{name:k,fn:_}=o[b],{x:R,y:S,data:F,reset:q}=yield _({x:c,y:d,initialPlacement:n,placement:f,strategy:i,middlewareData:m,rects:l,platform:a,elements:{reference:r,floating:e}});if(c=R!=null?R:c,d=S!=null?S:d,m=de(K({},m),{[k]:K(K({},m[k]),F)}),q&&y<=50){y++,typeof q=="object"&&(q.placement&&(f=q.placement),q.rects&&(l=q.rects===!0?yield a.getElementRects({reference:r,floating:e,strategy:i}):q.rects),{x:c,y:d}=aw(l,f,u)),b=-1;continue}}return{x:c,y:d,placement:f,strategy:i,middlewareData:m}});function gl(r,e){return A(this,null,function*(){var t;e===void 0&&(e={});let{x:n,y:i,platform:s,rects:a,elements:o,strategy:u}=r,{boundary:l="clippingAncestors",rootBoundary:c="viewport",elementContext:d="floating",altBoundary:f=!1,padding:m=0}=Ms(e,r),y=sw(m),k=o[f?d==="floating"?"reference":"floating":d],_=qi(yield s.getClippingRect({element:(t=yield s.isElement==null?void 0:s.isElement(k))==null||t?k:k.contextElement||(yield s.getDocumentElement==null?void 0:s.getDocumentElement(o.floating)),boundary:l,rootBoundary:c,strategy:u})),R=d==="floating"?de(K({},a.floating),{x:n,y:i}):a.reference,S=yield s.getOffsetParent==null?void 0:s.getOffsetParent(o.floating),F=(yield s.isElement==null?void 0:s.isElement(S))?(yield s.getScale==null?void 0:s.getScale(S))||{x:1,y:1}:{x:1,y:1},q=qi(s.convertOffsetParentRelativeRectToViewportRelativeRect?yield s.convertOffsetParentRelativeRectToViewportRelativeRect({rect:R,offsetParent:S,strategy:u}):R);return{top:(_.top-q.top+y.top)/F.y,bottom:(q.bottom-_.bottom+y.bottom)/F.y,left:(_.left-q.left+y.left)/F.x,right:(q.right-_.right+y.right)/F.x}})}var sh=function(r){return r===void 0&&(r={}),{name:"flip",options:r,fn(t){return A(this,null,function*(){var n,i;let{placement:s,middlewareData:a,rects:o,initialPlacement:u,platform:l,elements:c}=t,D=Ms(r,t),{mainAxis:d=!0,crossAxis:f=!0,fallbackPlacements:m,fallbackStrategy:y="bestFit",fallbackAxisSideDirection:b="none",flipAlignment:k=!0}=D,_=Zo(D,["mainAxis","crossAxis","fallbackPlacements","fallbackStrategy","fallbackAxisSideDirection","flipAlignment"]);if((n=a.arrow)!=null&&n.alignmentOffset)return{};let R=Nn(s),S=Nn(u)===u,F=yield l.isRTL==null?void 0:l.isRTL(c.floating),q=m||(S||!k?[Ao(u)]:nw(u));!m&&b!=="none"&&q.push(...iw(u,k,b,F));let ne=[u,...q],G=yield gl(t,_),B=[],ge=((i=a.flip)==null?void 0:i.overflows)||[];if(d&&B.push(G[R]),f){let Y=rw(s,o,F);B.push(G[Y[0]],G[Y[1]])}if(ge=[...ge,{placement:s,overflows:B}],!B.every(Y=>Y<=0)){var Pe,j;let Y=(((Pe=a.flip)==null?void 0:Pe.index)||0)+1,I=ne[Y];if(I)return{data:{index:Y,overflows:ge},reset:{placement:I}};let J=(j=ge.filter(p=>p.overflows[0]<=0).sort((p,h)=>p.overflows[1]-h.overflows[1])[0])==null?void 0:j.placement;if(!J)switch(y){case"bestFit":{var $;let p=($=ge.map(h=>[h.placement,h.overflows.filter(g=>g>0).reduce((g,T)=>g+T,0)]).sort((h,g)=>h[1]-g[1])[0])==null?void 0:$[0];p&&(J=p);break}case"initialPlacement":J=u;break}if(s!==J)return{reset:{placement:J}}}return{}})}}};function TI(r,e){return A(this,null,function*(){let{placement:t,platform:n,elements:i}=r,s=yield n.isRTL==null?void 0:n.isRTL(i.floating),a=Nn(t),o=Cs(t),u=As(t)==="y",l=["left","top"].includes(a)?-1:1,c=s&&u?-1:1,d=Ms(e,r),{mainAxis:f,crossAxis:m,alignmentAxis:y}=typeof d=="number"?{mainAxis:d,crossAxis:0,alignmentAxis:null}:K({mainAxis:0,crossAxis:0,alignmentAxis:null},d);return o&&typeof y=="number"&&(m=o==="end"?y*-1:y),u?{x:m*c,y:f*l}:{x:f*l,y:m*c}})}var yl=function(r){return r===void 0&&(r=0),{name:"offset",options:r,fn(t){return A(this,null,function*(){var n,i;let{x:s,y:a,placement:o,middlewareData:u}=t,l=yield TI(t,r);return o===((n=u.offset)==null?void 0:n.placement)&&(i=u.arrow)!=null&&i.alignmentOffset?{}:{x:s+l.x,y:a+l.y,data:de(K({},l),{placement:o})}})}}},Tl=function(r){return r===void 0&&(r={}),{name:"shift",options:r,fn(t){return A(this,null,function*(){let{x:n,y:i,placement:s}=t,_=Ms(r,t),{mainAxis:a=!0,crossAxis:o=!1,limiter:u={fn:R=>{let{x:S,y:F}=R;return{x:S,y:F}}}}=_,l=Zo(_,["mainAxis","crossAxis","limiter"]),c={x:n,y:i},d=yield gl(t,l),f=As(Nn(s)),m=rh(f),y=c[m],b=c[f];if(a){let R=m==="y"?"top":"left",S=m==="y"?"bottom":"right",F=y+d[R],q=y-d[S];y=th(F,y,q)}if(o){let R=f==="y"?"top":"left",S=f==="y"?"bottom":"right",F=b+d[R],q=b-d[S];b=th(F,b,q)}let k=u.fn(de(K({},t),{[m]:y,[f]:b}));return de(K({},k),{data:{x:k.x-n,y:k.y-i}})})}}};var ah=function(r){return r===void 0&&(r={}),{name:"size",options:r,fn(t){return A(this,null,function*(){let{placement:n,rects:i,platform:s,elements:a}=t,G=Ms(r,t),{apply:o=()=>{}}=G,u=Zo(G,["apply"]),l=yield gl(t,u),c=Nn(n),d=Cs(n),f=As(n)==="y",{width:m,height:y}=i.floating,b,k;c==="top"||c==="bottom"?(b=c,k=d===((yield s.isRTL==null?void 0:s.isRTL(a.floating))?"start":"end")?"left":"right"):(k=c,b=d==="end"?"top":"bottom");let _=y-l[b],R=m-l[k],S=!t.middlewareData.shift,F=_,q=R;if(f){let B=m-l.left-l.right;q=d||S?Wi(R,B):B}else{let B=y-l.top-l.bottom;F=d||S?Wi(_,B):B}if(S&&!d){let B=qt(l.left,0),ge=qt(l.right,0),Pe=qt(l.top,0),j=qt(l.bottom,0);f?q=m-2*(B!==0||ge!==0?B+ge:qt(l.left,l.right)):F=y-2*(Pe!==0||j!==0?Pe+j:qt(l.top,l.bottom))}yield o(de(K({},t),{availableWidth:q,availableHeight:F}));let ne=yield s.getDimensions(a.floating);return m!==ne.width||y!==ne.height?{reset:{rects:!0}}:{}})}}};function In(r){return lw(r)?(r.nodeName||"").toLowerCase():"#document"}function $t(r){var e;return(r==null||(e=r.ownerDocument)==null?void 0:e.defaultView)||window}function Fn(r){var e;return(e=(lw(r)?r.ownerDocument:r.document)||window.document)==null?void 0:e.documentElement}function lw(r){return r instanceof Node||r instanceof $t(r).Node}function sn(r){return r instanceof Element||r instanceof $t(r).Element}function Ur(r){return r instanceof HTMLElement||r instanceof $t(r).HTMLElement}function uw(r){return typeof ShadowRoot=="undefined"?!1:r instanceof ShadowRoot||r instanceof $t(r).ShadowRoot}function Ps(r){let{overflow:e,overflowX:t,overflowY:n,display:i}=Zt(r);return/auto|scroll|overlay|hidden|clip/.test(e+n+t)&&!["inline","contents"].includes(i)}function cw(r){return["table","td","th"].includes(In(r))}function _l(r){let e=vl(),t=Zt(r);return t.transform!=="none"||t.perspective!=="none"||(t.containerType?t.containerType!=="normal":!1)||!e&&(t.backdropFilter?t.backdropFilter!=="none":!1)||!e&&(t.filter?t.filter!=="none":!1)||["transform","perspective","filter"].some(n=>(t.willChange||"").includes(n))||["paint","layout","strict","content"].some(n=>(t.contain||"").includes(n))}function dw(r){let e=$i(r);for(;Ur(e)&&!No(e);){if(_l(e))return e;e=$i(e)}return null}function vl(){return typeof CSS=="undefined"||!CSS.supports?!1:CSS.supports("-webkit-backdrop-filter","none")}function No(r){return["html","body","#document"].includes(In(r))}function Zt(r){return $t(r).getComputedStyle(r)}function Io(r){return sn(r)?{scrollLeft:r.scrollLeft,scrollTop:r.scrollTop}:{scrollLeft:r.pageXOffset,scrollTop:r.pageYOffset}}function $i(r){if(In(r)==="html")return r;let e=r.assignedSlot||r.parentNode||uw(r)&&r.host||Fn(r);return uw(e)?e.host:e}function fw(r){let e=$i(r);return No(e)?r.ownerDocument?r.ownerDocument.body:r.body:Ur(e)&&Ps(e)?e:fw(e)}function bl(r,e,t){var n;e===void 0&&(e=[]),t===void 0&&(t=!0);let i=fw(r),s=i===((n=r.ownerDocument)==null?void 0:n.body),a=$t(i);return s?e.concat(a,a.visualViewport||[],Ps(i)?i:[],a.frameElement&&t?bl(a.frameElement):[]):e.concat(i,bl(i,[],t))}function hw(r){let e=Zt(r),t=parseFloat(e.width)||0,n=parseFloat(e.height)||0,i=Ur(r),s=i?r.offsetWidth:t,a=i?r.offsetHeight:n,o=Po(t)!==s||Po(n)!==a;return o&&(t=s,n=a),{width:t,height:n,$:o}}function gw(r){return sn(r)?r:r.contextElement}function Ns(r){let e=gw(r);if(!Ur(e))return Pn(1);let t=e.getBoundingClientRect(),{width:n,height:i,$:s}=hw(e),a=(s?Po(t.width):t.width)/n,o=(s?Po(t.height):t.height)/i;return(!a||!Number.isFinite(a))&&(a=1),(!o||!Number.isFinite(o))&&(o=1),{x:a,y:o}}var bI=Pn(0);function yw(r){let e=$t(r);return!vl()||!e.visualViewport?bI:{x:e.visualViewport.offsetLeft,y:e.visualViewport.offsetTop}}function _I(r,e,t){return e===void 0&&(e=!1),!t||e&&t!==$t(r)?!1:e}function Fo(r,e,t,n){e===void 0&&(e=!1),t===void 0&&(t=!1);let i=r.getBoundingClientRect(),s=gw(r),a=Pn(1);e&&(n?sn(n)&&(a=Ns(n)):a=Ns(r));let o=_I(s,t,n)?yw(s):Pn(0),u=(i.left+o.x)/a.x,l=(i.top+o.y)/a.y,c=i.width/a.x,d=i.height/a.y;if(s){let f=$t(s),m=n&&sn(n)?$t(n):n,y=f.frameElement;for(;y&&n&&m!==f;){let b=Ns(y),k=y.getBoundingClientRect(),_=Zt(y),R=k.left+(y.clientLeft+parseFloat(_.paddingLeft))*b.x,S=k.top+(y.clientTop+parseFloat(_.paddingTop))*b.y;u*=b.x,l*=b.y,c*=b.x,d*=b.y,u+=R,l+=S,y=$t(y).frameElement}}return qi({width:c,height:d,x:u,y:l})}function vI(r){let{rect:e,offsetParent:t,strategy:n}=r,i=Ur(t),s=Fn(t);if(t===s)return e;let a={scrollLeft:0,scrollTop:0},o=Pn(1),u=Pn(0);if((i||!i&&n!=="fixed")&&((In(t)!=="body"||Ps(s))&&(a=Io(t)),Ur(t))){let l=Fo(t);o=Ns(t),u.x=l.x+t.clientLeft,u.y=l.y+t.clientTop}return{width:e.width*o.x,height:e.height*o.y,x:e.x*o.x-a.scrollLeft*o.x+u.x,y:e.y*o.y-a.scrollTop*o.y+u.y}}function wI(r){return Array.from(r.getClientRects())}function Tw(r){return Fo(Fn(r)).left+Io(r).scrollLeft}function kI(r){let e=Fn(r),t=Io(r),n=r.ownerDocument.body,i=qt(e.scrollWidth,e.clientWidth,n.scrollWidth,n.clientWidth),s=qt(e.scrollHeight,e.clientHeight,n.scrollHeight,n.clientHeight),a=-t.scrollLeft+Tw(r),o=-t.scrollTop;return Zt(n).direction==="rtl"&&(a+=qt(e.clientWidth,n.clientWidth)-i),{width:i,height:s,x:a,y:o}}function EI(r,e){let t=$t(r),n=Fn(r),i=t.visualViewport,s=n.clientWidth,a=n.clientHeight,o=0,u=0;if(i){s=i.width,a=i.height;let l=vl();(!l||l&&e==="fixed")&&(o=i.offsetLeft,u=i.offsetTop)}return{width:s,height:a,x:o,y:u}}function SI(r,e){let t=Fo(r,!0,e==="fixed"),n=t.top+r.clientTop,i=t.left+r.clientLeft,s=Ur(r)?Ns(r):Pn(1),a=r.clientWidth*s.x,o=r.clientHeight*s.y,u=i*s.x,l=n*s.y;return{width:a,height:o,x:u,y:l}}function pw(r,e,t){let n;if(e==="viewport")n=EI(r,t);else if(e==="document")n=kI(Fn(r));else if(sn(e))n=SI(e,t);else{let i=yw(r);n=de(K({},e),{x:e.x-i.x,y:e.y-i.y})}return qi(n)}function bw(r,e){let t=$i(r);return t===e||!sn(t)||No(t)?!1:Zt(t).position==="fixed"||bw(t,e)}function OI(r,e){let t=e.get(r);if(t)return t;let n=bl(r,[],!1).filter(o=>sn(o)&&In(o)!=="body"),i=null,s=Zt(r).position==="fixed",a=s?$i(r):r;for(;sn(a)&&!No(a);){let o=Zt(a),u=_l(a);!u&&o.position==="fixed"&&(i=null),(s?!u&&!i:!u&&o.position==="static"&&!!i&&["absolute","fixed"].includes(i.position)||Ps(a)&&!u&&bw(r,a))?n=n.filter(c=>c!==a):i=o,a=$i(a)}return e.set(r,n),n}function DI(r){let{element:e,boundary:t,rootBoundary:n,strategy:i}=r,a=[...t==="clippingAncestors"?OI(e,this._c):[].concat(t),n],o=a[0],u=a.reduce((l,c)=>{let d=pw(e,c,i);return l.top=qt(d.top,l.top),l.right=Wi(d.right,l.right),l.bottom=Wi(d.bottom,l.bottom),l.left=qt(d.left,l.left),l},pw(e,o,i));return{width:u.right-u.left,height:u.bottom-u.top,x:u.left,y:u.top}}function xI(r){return hw(r)}function RI(r,e,t){let n=Ur(e),i=Fn(e),s=t==="fixed",a=Fo(r,!0,s,e),o={scrollLeft:0,scrollTop:0},u=Pn(0);if(n||!n&&!s)if((In(e)!=="body"||Ps(i))&&(o=Io(e)),n){let l=Fo(e,!0,s,e);u.x=l.x+e.clientLeft,u.y=l.y+e.clientTop}else i&&(u.x=Tw(i));return{x:a.left+o.scrollLeft-u.x,y:a.top+o.scrollTop-u.y,width:a.width,height:a.height}}function mw(r,e){return!Ur(r)||Zt(r).position==="fixed"?null:e?e(r):r.offsetParent}function _w(r,e){let t=$t(r);if(!Ur(r))return t;let n=mw(r,e);for(;n&&cw(n)&&Zt(n).position==="static";)n=mw(n,e);return n&&(In(n)==="html"||In(n)==="body"&&Zt(n).position==="static"&&!_l(n))?t:n||dw(r)||t}var MI=function(r){return A(this,null,function*(){let{reference:e,floating:t,strategy:n}=r,i=this.getOffsetParent||_w,s=this.getDimensions;return{reference:RI(e,yield i(t),n),floating:K({x:0,y:0},yield s(t))}})};function CI(r){return Zt(r).direction==="rtl"}var AI={convertOffsetParentRelativeRectToViewportRelativeRect:vI,getDocumentElement:Fn,getClippingRect:DI,getOffsetParent:_w,getElementRects:MI,getClientRects:wI,getDimensions:xI,getScale:Ns,isElement:sn,isRTL:CI};var oh=(r,e,t)=>{let n=new Map,i=K({platform:AI},t),s=de(K({},i.platform),{_c:n});return ow(r,e,de(K({},i),{platform:s}))};function vw(r,e,t){let n=r.slice();return n[5]=e[t],n}function ww(r,e,t){let n=r.slice();n[40]=e[t],n[43]=t;let i=n[17](n[40].taskLocation.path);return n[41]=i,n}function kw(r){let e,t,n,i=r[10],s=[];for(let a=0;a<i.length;a+=1)s[a]=Sw(ww(r,i,a));return{c(){e=X("ul");for(let a=0;a<s.length;a+=1)s[a].c();W(e,"class","task-dependency-dropdown")},m(a,o){pe(a,e,o);for(let u=0;u<s.length;u+=1)s[u]&&s[u].m(e,null);r[31](e),t||(n=xe(e,"mouseleave",r[32]),t=!0)},p(a,o){if(o[0]&928832){i=a[10];let u;for(u=0;u<i.length;u+=1){let l=ww(a,i,u);s[u]?s[u].p(l,o):(s[u]=Sw(l),s[u].c(),s[u].m(e,null))}for(;u<s.length;u+=1)s[u].d(1);s.length=i.length}},d(a){a&&oe(e),Li(s,a),r[31](null),t=!1,n()}}}function Ew(r){let e,t=r[41]+"",n,i,s;function a(...o){return r[28](r[41],...o)}return{c(){e=X("div"),n=Me(t),W(e,"class","dependency-path")},m(o,u){pe(o,e,u),U(e,n),i||(s=xe(e,"mouseenter",a),i=!0)},p(o,u){r=o,u[0]&1024&&t!==(t=r[41]+"")&&Fr(n,t)},d(o){o&&oe(e),i=!1,s()}}}function Sw(r){let e,t,n,i=r[40].status.symbol+"",s,a,o=Ai(r[40])+"",u,l,c,d,f,m;function y(...R){return r[27](r[40],...R)}let b=r[41]&&Ew(r);function k(){return r[29](r[40])}function _(){return r[30](r[43])}return{c(){e=X("li"),t=X("div"),n=Me("["),s=Me(i),a=Me("] "),u=Me(o),c=ue(),b&&b.c(),d=ue(),W(t,"class",l=r[41]?"dependency-name-shared":"dependency-name"),An(e,"selected",r[6]!==null&&r[43]===r[11])},m(R,S){pe(R,e,S),U(e,t),U(t,n),U(t,s),U(t,a),U(t,u),U(e,c),b&&b.m(e,null),U(e,d),f||(m=[xe(t,"mouseenter",y),xe(e,"mousedown",k),xe(e,"mouseenter",_)],f=!0)},p(R,S){r=R,S[0]&1024&&i!==(i=r[40].status.symbol+"")&&Fr(s,i),S[0]&1024&&o!==(o=Ai(r[40])+"")&&Fr(u,o),S[0]&1024&&l!==(l=r[41]?"dependency-name-shared":"dependency-name")&&W(t,"class",l),r[41]?b?b.p(r,S):(b=Ew(r),b.c(),b.m(e,d)):b&&(b.d(1),b=null),S[0]&2112&&An(e,"selected",r[6]!==null&&r[43]===r[11])},d(R){R&&oe(e),b&&b.d(),f=!1,Ut(m)}}}function Ow(r){let e,t=r[0][r[1]],n=[];for(let i=0;i<t.length;i+=1)n[i]=Dw(vw(r,t,i));return{c(){e=X("div");for(let i=0;i<n.length;i+=1)n[i].c();W(e,"class","task-dependencies-container results-dependency")},m(i,s){pe(i,e,s);for(let a=0;a<n.length;a+=1)n[a]&&n[a].m(e,null)},p(i,s){if(s[0]&802819){t=i[0][i[1]];let a;for(a=0;a<t.length;a+=1){let o=vw(i,t,a);n[a]?n[a].p(o,s):(n[a]=Dw(o),n[a].c(),n[a].m(e,null))}for(;a<n.length;a+=1)n[a].d(1);n.length=t.length}},d(i){i&&oe(e),Li(n,i)}}}function Dw(r){let e,t,n,i=r[5].status.symbol+"",s,a,o=Ai(r[5])+"",u,l,c,d,f,m;function y(){return r[33](r[5])}function b(...k){return r[34](r[5],...k)}return{c(){e=X("div"),t=X("span"),n=Me("["),s=Me(i),a=Me("] "),u=Me(o),l=ue(),c=X("button"),c.innerHTML='<svg style="display: block; margin: auto;" xmlns="http://www.w3.org/2000/svg" width="12" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="4" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-x"><path d="M18 6 6 18"></path><path d="m6 6 12 12"></path></svg>',d=ue(),W(t,"class","task-dependency-name"),W(c,"type","button"),W(c,"class","task-dependency-delete"),W(e,"class","task-dependency")},m(k,_){pe(k,e,_),U(e,t),U(t,n),U(t,s),U(t,a),U(t,u),U(e,l),U(e,c),U(e,d),f||(m=[xe(c,"click",y),xe(e,"mouseenter",b)],f=!0)},p(k,_){r=k,_[0]&3&&i!==(i=r[5].status.symbol+"")&&Fr(s,i),_[0]&3&&o!==(o=Ai(r[5])+"")&&Fr(u,o)},d(k){k&&oe(e),f=!1,Ut(m)}}}function PI(r){let e,t=gt(r[2],r[3])+"",n,i,s,a,o,u,l,c,d,f=r[10]&&r[10].length!==0&&kw(r),m=r[0][r[1]].length!==0&&Ow(r);return{c(){e=X("label"),n=ue(),i=X("span"),s=X("input"),o=ue(),f&&f.c(),u=ue(),m&&m.c(),l=Vv(),W(e,"for",r[1]),W(s,"accesskey",r[3]),W(s,"id",r[1]),W(s,"class","tasks-modal-dependency-input"),W(s,"type","text"),W(s,"placeholder",r[4]),Fi(()=>r[26].call(i))},m(y,b){pe(y,e,b),e.innerHTML=t,pe(y,n,b),pe(y,i,b),U(i,s),r[22](s),yr(s,r[6]),a=Xv(i,r[26].bind(i)),pe(y,o,b),f&&f.m(y,b),pe(y,u,b),m&&m.m(y,b),pe(y,l,b),c||(d=[xe(s,"input",r[23]),xe(s,"keydown",r[24]),xe(s,"focus",r[16]),xe(s,"blur",r[25])],c=!0)},p(y,b){b[0]&12&&t!==(t=gt(y[2],y[3])+"")&&(e.innerHTML=t),b[0]&2&&W(e,"for",y[1]),b[0]&8&&W(s,"accesskey",y[3]),b[0]&2&&W(s,"id",y[1]),b[0]&16&&W(s,"placeholder",y[4]),b[0]&64&&s.value!==y[6]&&yr(s,y[6]),y[10]&&y[10].length!==0?f?f.p(y,b):(f=kw(y),f.c(),f.m(u.parentNode,u)):f&&(f.d(1),f=null),y[0][y[1]].length!==0?m?m.p(y,b):(m=Ow(y),m.c(),m.m(l.parentNode,l)):m&&(m.d(1),m=null)},i:Ue,o:Ue,d(y){y&&oe(e),y&&oe(n),y&&oe(i),r[22](null),a(),y&&oe(o),f&&f.d(y),y&&oe(u),m&&m.d(y),y&&oe(l),c=!1,Ut(d)}}}function NI(r,e,t){let{task:n}=e,{editableTask:i}=e,{allTasks:s}=e,{_onDescriptionKeyDown:a}=e,{type:o}=e,{labelText:u}=e,{accesskey:l}=e,{placeholder:c="Type to search..."}=e,d="",f=null,m=0,y,b=!1,k=!1,_,R;function S(v){t(0,i[o]=[...i[o],v],i),t(6,d=""),t(7,b=!1)}function F(v){t(0,i[o]=i[o].filter(x=>x!==v),i)}function q(v){var x;if(f!==null){switch(v.key){case"ArrowUp":v.preventDefault(),!!m&&m>0?t(11,m-=1):t(11,m=f.length-1);break;case"ArrowDown":v.preventDefault(),!!m&&m<f.length-1?t(11,m+=1):t(11,m=0);break;case"Enter":m!==null?(v.preventDefault(),S(f[m]),t(11,m=null),t(7,b=!1)):a(v);break;default:t(11,m=0);break}m&&((x=R==null?void 0:R.getElementsByTagName("li")[m])===null||x===void 0||x.scrollIntoView({block:"nearest"}))}}function ne(v){return!v&&!k?[]:(k=!1,sl(v,s,n,i.blockedBy,i.blocking))}function G(){t(7,b=!0),k=!0}function B(v,x){!v||!x||oh(v,x,{middleware:[yl(6),Tl(),sh(),ah({apply(){x&&Object.assign(x.style,{width:`${y}px`})}})]}).then(({x:N,y:ie})=>{x.style.left=`${N}px`,x.style.top=`${ie}px`})}function ge(v){return v===n.taskLocation.path?"":v}function Pe(v){return Ai(v)}function j(v,x){let N=v.createDiv();N.addClasses(["tooltip","pop-up"]),N.innerText=x,oh(v,N,{placement:"top",middleware:[yl(-18),Tl()]}).then(({x:ie,y:ce})=>{N.style.left=`${ie}px`,N.style.top=`${ce}px`}),v.addEventListener("mouseleave",()=>N.remove())}function $(v){je[v?"unshift":"push"](()=>{_=v,t(8,_)})}function D(){d=this.value,t(6,d)}let Y=v=>q(v),I=()=>t(7,b=!1);function J(){y=this.clientWidth,t(12,y)}let p=(v,x)=>j(x.currentTarget,Pe(v)),h=(v,x)=>j(x.currentTarget,v),g=v=>S(v),T=v=>t(11,m=v);function w(v){je[v?"unshift":"push"](()=>{R=v,t(9,R)})}let O=()=>t(11,m=null),M=v=>F(v),P=(v,x)=>j(x.currentTarget,Pe(v));return r.$$set=v=>{"task"in v&&t(5,n=v.task),"editableTask"in v&&t(0,i=v.editableTask),"allTasks"in v&&t(20,s=v.allTasks),"_onDescriptionKeyDown"in v&&t(21,a=v._onDescriptionKeyDown),"type"in v&&t(1,o=v.type),"labelText"in v&&t(2,u=v.labelText),"accesskey"in v&&t(3,l=v.accesskey),"placeholder"in v&&t(4,c=v.placeholder)},r.$$.update=()=>{if(r.$$.dirty[0]&768){e:B(_,R)}if(r.$$.dirty[0]&192){e:t(10,f=b?ne(d):null)}},[i,o,u,l,c,n,d,b,_,R,f,m,y,S,F,q,G,ge,Pe,j,s,a,$,D,Y,I,J,p,h,g,T,w,O,M,P]}var uh=class extends gr{constructor(e){super(),nn(this,e,NI,PI,rn,{task:5,editableTask:0,allTasks:20,_onDescriptionKeyDown:21,type:1,labelText:2,accesskey:3,placeholder:4},null,[-1,-1])}},lh=uh;var Uo=require("obsidian");var Lo,ch,dh,II=["md"];function El(){return Rt.getLogger("tasks.File")}var xw=({metadataCache:r,vault:e,workspace:t})=>{Lo=r,ch=e,dh=t},br=t=>A(void 0,[t],function*({originalTask:r,newTasks:e}){if(ch===void 0||Lo===void 0||dh===void 0){wl("Tasks: cannot use File before initializing it.");return}Array.isArray(e)||(e=[e]);let n=El(),i="replaceTaskWithTasks()";cl(n,i,r),dl(n,i,e),yield Mw({originalTask:r,newTasks:e,vault:ch,metadataCache:Lo,workspace:dh,previousTries:0})});function wl(r){console.error(r),new Uo.Notice(r,15e3)}function Rw(r){console.warn(r),new Uo.Notice(r,1e4)}function FI(r){El().debug(r)}var ei=class extends Error{},kl=class extends Error{},Mw=a=>A(void 0,[a],function*({originalTask:r,newTasks:e,vault:t,metadataCache:n,workspace:i,previousTries:s}){let o=El();o.debug(`tryRepetitive after ${s} previous tries`);let u=()=>A(void 0,null,function*(){if(s>10){let c=`Tasks: Could not find the correct task line to update.

The task line not updated is:
${r.originalMarkdown}

In this markdown file:
"${r.taskLocation.path}"

Note: further clicks on this checkbox will usually now be ignored until the file is opened (or certain, specific edits are made - it's complicated).

Recommendations:

1. Close all panes that have the above file open, and then re-open the file.

2. Check for exactly identical copies of the task line, in this file, and see if you can make them different.
`;wl(c);return}let l=Math.min(Math.pow(10,s),100);o.debug(`timeout = ${l}`),setTimeout(()=>A(void 0,null,function*(){yield Mw({originalTask:r,newTasks:e,vault:t,metadataCache:n,workspace:i,previousTries:s+1})}),l)});try{let[l,c,d]=yield Cw(r,t),f=[...d.slice(0,l),...e.map(m=>m.toFileLineString()),...d.slice(l+1)];yield t.modify(c,f.join(`
`))}catch(l){if(l instanceof ei){l.message&&Rw(l.message),yield u();return}else if(l instanceof kl){yield u();return}else l instanceof Error&&wl(l.message)}});function Cw(r,e){return A(this,null,function*(){if(Lo===void 0)throw new ei;let t=e.getAbstractFileByPath(r.path);if(!(t instanceof Uo.TFile))throw new ei(`Tasks: No file found for task ${r.description}. Retrying ...`);if(!II.includes(t.extension))throw new Error(`Tasks: Does not support files with the ${t.extension} file extension.`);let n=Lo.getFileCache(t);if(n==null||n===null)throw new ei(`Tasks: No file cache found for file ${t.path}. Retrying ...`);let i=n.listItems;if(i===void 0||i.length===0)throw new ei(`Tasks: No list items found in file cache of ${t.path}. Retrying ...`);let a=(yield e.read(t)).split(`
`),o=LI(r,a,i,FI);if(o===void 0)throw new kl;return[o,t,a]})}function fh(r,e){return A(this,null,function*(){try{let[t,n,i]=yield Cw(r,e);return[t,n]}catch(t){t instanceof ei?t.message&&Rw(t.message):t instanceof Error&&wl(t.message)}})}function Aw(r,e){return r<e.length}function LI(r,e,t,n){let i=UI(r,e);return i!==void 0||(i=WI(r,e),i!==void 0)?i:qI(r,e,t,n)}function UI(r,e){let t=r.taskLocation.lineNumber;if(Aw(t,e)&&e[t]===r.originalMarkdown)return El().debug(`Found original markdown at original line number ${t}`),t}function WI(r,e){let t=[];for(let n=0;n<e.length;n++)e[n]===r.originalMarkdown&&t.push(n);if(t.length===1)return t[0]}function qI(r,e,t,n){let i,s=0;for(let a of t){let o=a.position.start.line;if(!Aw(o,e))return;if(o<r.taskLocation.sectionStart||a.task===void 0)continue;let u=e[o];if(ve.getInstance().includedIn(u)){if(s===r.taskLocation.sectionIndex){if(u===r.originalMarkdown)i=o;else{n(`Tasks: Unable to find task in file ${r.taskLocation.path}.
Expected task:
${r.originalMarkdown}
Found task:
${u}`);return}break}s++}}return i}var Is=class{constructor(e){this.addGlobalFilterOnSave=e.addGlobalFilterOnSave,this.originalBlocking=e.originalBlocking,this.description=e.description,this.status=e.status,this.priority=e.priority,this.onCompletion=e.onCompletion,this.recurrenceRule=e.recurrenceRule,this.createdDate=e.createdDate,this.startDate=e.startDate,this.scheduledDate=e.scheduledDate,this.dueDate=e.dueDate,this.doneDate=e.doneDate,this.cancelledDate=e.cancelledDate,this.forwardOnly=e.forwardOnly,this.blockedBy=e.blockedBy,this.blocking=e.blocking}static fromTask(e,t){let n=ve.getInstance().removeAsWordFrom(e.description),i=n!=e.description||!ve.getInstance().includedIn(e.description),s="none";e.priority==="5"?s="lowest":e.priority==="4"?s="low":e.priority==="2"?s="medium":e.priority==="1"?s="high":e.priority==="0"&&(s="highest");let a=[];for(let u of e.dependsOn){let l=t.find(c=>c.id===u);!l||a.push(l)}let o=t.filter(u=>u.dependsOn.includes(e.id));return new Is({addGlobalFilterOnSave:i,originalBlocking:o,description:n,status:e.status,priority:s,recurrenceRule:e.recurrence?e.recurrence.toText():"",onCompletion:e.onCompletion,createdDate:e.created.formatAsDate(),startDate:e.start.formatAsDate(),scheduledDate:e.scheduled.formatAsDate(),dueDate:e.due.formatAsDate(),doneDate:e.done.formatAsDate(),cancelledDate:e.cancelled.formatAsDate(),forwardOnly:!0,blockedBy:a,blocking:o})}applyEdits(e,t){return A(this,null,function*(){let n=this.description.trim();this.addGlobalFilterOnSave&&(n=ve.getInstance().prependTo(n));let i=Si(this.startDate,this.forwardOnly),s=Si(this.scheduledDate,this.forwardOnly),a=Si(this.dueDate,this.forwardOnly),o=Si(this.cancelledDate,this.forwardOnly),u=Si(this.createdDate,this.forwardOnly),l=Si(this.doneDate,this.forwardOnly),c=null;this.recurrenceRule&&(c=st.fromText({recurrenceRuleText:this.recurrenceRule,occurrence:new fr({startDate:i,scheduledDate:s,dueDate:a})}));let d;switch(this.priority){case"lowest":d="5";break;case"low":d="4";break;case"medium":d="2";break;case"high":d="1";break;case"highest":d="0";break;default:d="3"}let f=this.onCompletion,m=[];for(let S of this.blockedBy){let F=yield $I(S,t);m.push(F)}let y=e.id,b=[],k=[];(this.blocking.toString()!==this.originalBlocking.toString()||this.blocking.length!==0)&&(e.id===""&&(y=Oo(t.filter(S=>S.id!=="").map(S=>S.id))),b=this.originalBlocking.filter(S=>!this.blocking.includes(S)),k=this.blocking.filter(S=>!this.originalBlocking.includes(S)));let _=new ee(de(K({},e),{description:n,status:e.status,priority:d,onCompletion:f,recurrence:c,startDate:i,scheduledDate:s,dueDate:a,doneDate:l,createdDate:u,cancelledDate:o,dependsOn:m.map(S=>S.id),id:y}));for(let S of b){let F=xv(S,_);yield br({originalTask:S,newTasks:F})}for(let S of k){let F=Dv(S,_);yield br({originalTask:S,newTasks:F})}let R=this.inferTodaysDate(this.status.type,l,o);return _.handleNewStatusWithRecurrenceInUsersOrder(this.status,R)})}inferTodaysDate(e,t,n){return e==="DONE"&&t!==null?t:e==="CANCELLED"&&n!==null?n:window.moment()}parseAndValidateRecurrence(){var t;if(!this.recurrenceRule)return{parsedRecurrence:"<i>not recurring</>",isRecurrenceValid:!0};let e=(t=st.fromText({recurrenceRuleText:this.recurrenceRule,occurrence:new fr({startDate:null,scheduledDate:null,dueDate:null})}))==null?void 0:t.toText();return e?this.startDate||this.scheduledDate||this.dueDate?{parsedRecurrence:e,isRecurrenceValid:!0}:{parsedRecurrence:"<i>due, scheduled or start date required</i>",isRecurrenceValid:!1}:{parsedRecurrence:"<i>invalid recurrence rule</i>",isRecurrenceValid:!1}}};function $I(r,e){return A(this,null,function*(){if(r.id!=="")return r;let t=e.filter(i=>i.id!==""),n=il(r,t.map(i=>i.id));return yield br({originalTask:r,newTasks:n}),n})}function jI(r){let e,t=gt("Recurs",r[2])+"",n,i,s,a,o,u,l,c,d;return{c(){e=X("label"),n=ue(),i=X("input"),s=ue(),a=X("code"),o=Me(r[4]),u=ue(),l=new xs(!1),W(e,"for","recurrence"),W(i,"id","recurrence"),W(i,"type","text"),W(i,"class","tasks-modal-date-input"),W(i,"placeholder","Try 'every day when done'"),W(i,"accesskey",r[2]),An(i,"tasks-modal-error",!r[1]),l.a=null,W(a,"class","tasks-modal-parsed-date")},m(f,m){pe(f,e,m),e.innerHTML=t,pe(f,n,m),pe(f,i,m),yr(i,r[0].recurrenceRule),pe(f,s,m),pe(f,a,m),U(a,o),U(a,u),l.m(r[3],a),c||(d=xe(i,"input",r[5]),c=!0)},p(f,[m]){m&4&&t!==(t=gt("Recurs",f[2])+"")&&(e.innerHTML=t),m&4&&W(i,"accesskey",f[2]),m&1&&i.value!==f[0].recurrenceRule&&yr(i,f[0].recurrenceRule),m&2&&An(i,"tasks-modal-error",!f[1]),m&8&&l.p(f[3])},i:Ue,o:Ue,d(f){f&&oe(e),f&&oe(n),f&&oe(i),f&&oe(s),f&&oe(a),c=!1,d()}}}function GI(r,e,t){let{editableTask:n}=e,{isRecurrenceValid:i}=e,{accesskey:s}=e,a,{recurrenceSymbol:o}=Nr.tasksPluginEmoji.taskSerializer.symbols;function u(){n.recurrenceRule=this.value,t(0,n)}return r.$$set=l=>{"editableTask"in l&&t(0,n=l.editableTask),"isRecurrenceValid"in l&&t(1,i=l.isRecurrenceValid),"accesskey"in l&&t(2,s=l.accesskey)},r.$$.update=()=>{if(r.$$.dirty&1){e:t(3,{parsedRecurrence:a,isRecurrenceValid:i}=n.parseAndValidateRecurrence(),a,(t(1,i),t(0,n)))}},[n,i,s,a,o,u]}var ph=class extends gr{constructor(e){super(),nn(this,e,GI,jI,rn,{editableTask:0,isRecurrenceValid:1,accesskey:2})}},Pw=ph;function Nw(r,e,t){let n=r.slice();return n[7]=e[t],n}function Iw(r){let e,t=r[7].name+"",n,i,s=r[7].symbol+"",a,o,u;return{c(){e=X("option"),n=Me(t),i=Me(" ["),a=Me(s),o=Me("]"),e.__value=u=r[7].symbol,e.value=e.__value},m(l,c){pe(l,e,c),U(e,n),U(e,i),U(e,a),U(e,o)},p(l,c){c&1&&t!==(t=l[7].name+"")&&Fr(n,t),c&1&&s!==(s=l[7].symbol+"")&&Fr(a,s),c&1&&u!==(u=l[7].symbol)&&(e.__value=u,e.value=e.__value)},d(l){l&&oe(e)}}}function YI(r){let e,t=gt("Status",r[1])+"",n,i,s,a,o=r[0],u=[];for(let l=0;l<o.length;l+=1)u[l]=Iw(Nw(r,o,l));return{c(){e=X("label"),n=ue(),i=X("select");for(let l=0;l<u.length;l+=1)u[l].c();W(e,"for","status"),W(i,"id","status-type"),W(i,"class","status-editor-status-selector"),W(i,"accesskey",r[1]),r[2]===void 0&&Fi(()=>r[6].call(i))},m(l,c){pe(l,e,c),e.innerHTML=t,pe(l,n,c),pe(l,i,c);for(let d=0;d<u.length;d+=1)u[d]&&u[d].m(i,null);Zm(i,r[2],!0),s||(a=[xe(i,"change",r[6]),xe(i,"change",r[3])],s=!0)},p(l,[c]){if(c&2&&t!==(t=gt("Status",l[1])+"")&&(e.innerHTML=t),c&1){o=l[0];let d;for(d=0;d<o.length;d+=1){let f=Nw(l,o,d);u[d]?u[d].p(f,c):(u[d]=Iw(f),u[d].c(),u[d].m(i,null))}for(;d<u.length;d+=1)u[d].d(1);u.length=o.length}c&2&&W(i,"accesskey",l[1]),c&5&&Zm(i,l[2])},i:Ue,o:Ue,d(l){l&&oe(e),l&&oe(n),l&&oe(i),Li(u,l),s=!1,Ut(a)}}}function Fw(r,e,t){let n=r==="";return e&&n?t.formatAsDate():!e&&!n?"":r}function BI(r,e,t){let{task:n}=e,{editableTask:i}=e,{statusOptions:s}=e,{accesskey:a}=e,o=n.status.symbol,u=()=>{let c=s.find(f=>f.symbol===o);if(c)t(4,i.status=c,i);else{console.log(`Error in EditTask: cannot find status with symbol ${o}`);return}let d=n.handleNewStatus(c).pop();d&&(t(4,i.doneDate=Fw(i.doneDate,c.isCompleted(),d.done),i),t(4,i.cancelledDate=Fw(i.cancelledDate,c.isCancelled(),d.cancelled),i))};function l(){o=Qv(this),t(2,o),t(0,s)}return r.$$set=c=>{"task"in c&&t(5,n=c.task),"editableTask"in c&&t(4,i=c.editableTask),"statusOptions"in c&&t(0,s=c.statusOptions),"accesskey"in c&&t(1,a=c.accesskey)},[s,a,o,u,i,n,l]}var mh=class extends gr{constructor(e){super(),nn(this,e,BI,YI,rn,{task:5,editableTask:4,statusOptions:0,accesskey:1})}},Lw=mh;function Uw(r,e,t){let n=r.slice();return n[52]=e[t].value,n[53]=e[t].label,n[54]=e[t].symbol,n[55]=e[t].accessKey,n[56]=e[t].accessKeyIndex,n}function HI(r){let e,t=r[53]+"",n;return{c(){e=X("span"),n=Me(t)},m(i,s){pe(i,e,s),U(e,n)},p:Ue,d(i){i&&oe(e)}}}function VI(r){let e,t=r[53].substring(0,r[56])+"",n,i,s=r[53].substring(r[56],r[56]+1)+"",a,o,u=r[53].substring(r[56]+1)+"",l;return{c(){e=X("span"),n=Me(t),i=X("span"),a=Me(s),o=X("span"),l=Me(u),W(i,"class","accesskey")},m(c,d){pe(c,e,d),U(e,n),pe(c,i,d),U(i,a),pe(c,o,d),U(o,l)},p:Ue,d(c){c&&oe(e),c&&oe(i),c&&oe(o)}}}function zI(r){let e,t=r[54]+"",n;return{c(){e=X("span"),n=Me(t)},m(i,s){pe(i,e,s),U(e,n)},p:Ue,d(i){i&&oe(e)}}}function Ww(r){let e,t,n,i,s,a,o,u,l=r[54]&&r[54].charCodeAt(0)>=256,c,d,f,m,y;function b(S,F){return S[11]?VI:HI}let k=b(r,[-1,-1]),_=k(r),R=l&&zI(r);return f=Kv(r[35][0]),{c(){e=X("div"),t=X("input"),a=ue(),o=X("label"),_.c(),u=ue(),R&&R.c(),d=ue(),W(t,"type","radio"),W(t,"id",n="priority-"+r[52]),t.__value=i=r[52],t.value=t.__value,W(t,"accesskey",s=r[15](r[55])),W(o,"for",c="priority-"+r[52]),W(e,"class","task-modal-priority-option-container"),f.p(t)},m(S,F){pe(S,e,F),U(e,t),t.checked=t.__value===r[3].priority,U(e,a),U(e,o),_.m(o,null),U(o,u),R&&R.m(o,null),U(e,d),m||(y=xe(t,"change",r[34]),m=!0)},p(S,F){F[0]&32768&&s!==(s=S[15](S[55]))&&W(t,"accesskey",s),F[0]&8&&(t.checked=t.__value===S[3].priority),k===(k=b(S,F))&&_?_.p(S,F):(_.d(1),_=k(S),_&&(_.c(),_.m(o,u))),l&&R.p(S,F)},d(S){S&&oe(e),_.d(),R&&R.d(),f.r(),m=!1,y()}}}function KI(r){let e;return{c(){e=X("div"),e.innerHTML="<i>Blocking and blocked by fields are disabled when vault tasks is empty</i>"},m(t,n){pe(t,e,n)},p:Ue,i:Ue,o:Ue,d(t){t&&oe(e)}}}function QI(r){let e,t,n,i;return e=new lh({props:{type:"blockedBy",labelText:"Before this",task:r[0],editableTask:r[3],allTasks:r[2],_onDescriptionKeyDown:r[24],accesskey:r[15]("b"),placeholder:"Search for tasks that the task being edited depends on..."}}),n=new lh({props:{type:"blocking",labelText:"After this",task:r[0],editableTask:r[3],allTasks:r[2],_onDescriptionKeyDown:r[24],accesskey:r[15]("e"),placeholder:"Search for tasks that depend on this task being done..."}}),{c(){Lr(e.$$.fragment),t=ue(),Lr(n.$$.fragment)},m(s,a){Tr(e,s,a),pe(s,t,a),Tr(n,s,a),i=!0},p(s,a){let o={};a[0]&1&&(o.task=s[0]),a[0]&8&&(o.editableTask=s[3]),a[0]&4&&(o.allTasks=s[2]),a[0]&32768&&(o.accesskey=s[15]("b")),e.$set(o);let u={};a[0]&1&&(u.task=s[0]),a[0]&8&&(u.editableTask=s[3]),a[0]&4&&(u.allTasks=s[2]),a[0]&32768&&(u.accesskey=s[15]("e")),n.$set(u)},i(s){i||(Wt(e.$$.fragment,s),Wt(n.$$.fragment,s),i=!0)},o(s){Qt(e.$$.fragment,s),Qt(n.$$.fragment,s),i=!1},d(s){Xt(e,s),s&&oe(t),Xt(n,s)}}}function XI(r){let e,t,n,i=gt("Description",r[15]("t"))+"",s,a,o,u,l,c,d,f,m,y,b,k,_,R,S,F,q,ne,G,B,ge,Pe,j,$,D,Y,I,J,p,h,g=gt("Only future dates:",r[15]("f"))+"",T,w,O,M,P,v,x,N,ie,ce,me,Ne,Ce,Nt,fn,Br,ot,Et,Ge,z,Er,wc,kc,Qh,Sr,Ec,Sc,Xh,Xi,pi,Zh,Qo,Jh,sa,Hr,Oc,eg,Zi=r[22],St=[];for(let C=0;C<Zi.length;C+=1)St[C]=Ww(Uw(r,Zi,C));function aE(C){r[36](C)}let tg={editableTask:r[3],accesskey:r[15]("r")};r[10]!==void 0&&(tg.isRecurrenceValid=r[10]),R=new Pw({props:tg}),je.push(()=>Ct(R,"isRecurrenceValid",aE));function oE(C){r[37](C)}function uE(C){r[38](C)}let Dc={id:"due",dateSymbol:r[18],forwardOnly:r[3].forwardOnly,accesskey:r[15]("d")};r[3].dueDate!==void 0&&(Dc.date=r[3].dueDate),r[7]!==void 0&&(Dc.isDateValid=r[7]),q=new Ui({props:Dc}),je.push(()=>Ct(q,"date",oE)),je.push(()=>Ct(q,"isDateValid",uE)),q.$on("open",r[27]),q.$on("close",r[28]);function lE(C){r[39](C)}function cE(C){r[40](C)}let xc={id:"scheduled",dateSymbol:r[17],forwardOnly:r[3].forwardOnly,accesskey:r[15]("s")};r[3].scheduledDate!==void 0&&(xc.date=r[3].scheduledDate),r[8]!==void 0&&(xc.isDateValid=r[8]),ge=new Ui({props:xc}),je.push(()=>Ct(ge,"date",lE)),je.push(()=>Ct(ge,"isDateValid",cE)),ge.$on("open",r[27]),ge.$on("close",r[28]);function dE(C){r[41](C)}function fE(C){r[42](C)}let Rc={id:"start",dateSymbol:r[16],forwardOnly:r[3].forwardOnly,accesskey:r[15]("a")};r[3].startDate!==void 0&&(Rc.date=r[3].startDate),r[9]!==void 0&&(Rc.isDateValid=r[9]),D=new Ui({props:Rc}),je.push(()=>Ct(D,"date",dE)),je.push(()=>Ct(D,"isDateValid",fE)),D.$on("open",r[27]),D.$on("close",r[28]);let rg=[QI,KI],jn=[];function ng(C,te){return C[2].length>0&&C[14]?0:1}N=ng(r,[-1,-1]),ie=jn[N]=rg[N](r);function pE(C){r[44](C)}let ig={task:r[0],statusOptions:r[1],accesskey:r[15]("u")};r[3]!==void 0&&(ig.editableTask=r[3]),Nt=new Lw({props:ig}),je.push(()=>Ct(Nt,"editableTask",pE));function mE(C){r[45](C)}function hE(C){r[46](C)}let Mc={id:"created",dateSymbol:r[20],forwardOnly:r[3].forwardOnly,accesskey:r[15]("c")};r[3].createdDate!==void 0&&(Mc.date=r[3].createdDate),r[5]!==void 0&&(Mc.isDateValid=r[5]),ot=new Ui({props:Mc}),je.push(()=>Ct(ot,"date",mE)),je.push(()=>Ct(ot,"isDateValid",hE)),ot.$on("open",r[27]),ot.$on("close",r[28]);function gE(C){r[47](C)}function yE(C){r[48](C)}let Cc={id:"done",dateSymbol:r[21],forwardOnly:r[3].forwardOnly,accesskey:r[15]("x")};r[3].doneDate!==void 0&&(Cc.date=r[3].doneDate),r[6]!==void 0&&(Cc.isDateValid=r[6]),Er=new Ui({props:Cc}),je.push(()=>Ct(Er,"date",gE)),je.push(()=>Ct(Er,"isDateValid",yE)),Er.$on("open",r[27]),Er.$on("close",r[28]);function TE(C){r[49](C)}function bE(C){r[50](C)}let Ac={id:"cancelled",dateSymbol:r[19],forwardOnly:r[3].forwardOnly,accesskey:r[15]("-")};return r[3].cancelledDate!==void 0&&(Ac.date=r[3].cancelledDate),r[4]!==void 0&&(Ac.isDateValid=r[4]),Sr=new Ui({props:Ac}),je.push(()=>Ct(Sr,"date",TE)),je.push(()=>Ct(Sr,"isDateValid",bE)),Sr.$on("open",r[27]),Sr.$on("close",r[28]),{c(){e=X("form"),t=X("section"),n=X("label"),s=ue(),a=X("textarea"),u=ue(),l=X("section"),c=X("label"),d=Me("Priority"),m=ue();for(let C=0;C<St.length;C+=1)St[C].c();y=ue(),b=X("hr"),k=ue(),_=X("section"),Lr(R.$$.fragment),F=ue(),Lr(q.$$.fragment),B=ue(),Lr(ge.$$.fragment),$=ue(),Lr(D.$$.fragment),J=ue(),p=X("div"),h=X("label"),T=ue(),w=X("input"),M=ue(),P=X("hr"),v=ue(),x=X("section"),ie.c(),ce=ue(),me=X("hr"),Ne=ue(),Ce=X("section"),Lr(Nt.$$.fragment),Br=ue(),Lr(ot.$$.fragment),z=ue(),Lr(Er.$$.fragment),Qh=ue(),Lr(Sr.$$.fragment),Xh=ue(),Xi=X("section"),pi=X("button"),Zh=Me("Apply"),Jh=ue(),sa=X("button"),sa.textContent="Cancel",W(n,"for","description"),W(a,"id","description"),W(a,"class","tasks-modal-description"),W(a,"placeholder","Take out the trash"),W(a,"accesskey",o=r[15]("t")),W(t,"class","tasks-modal-description-section"),W(c,"for",f="priority-"+r[3].priority),W(l,"class","tasks-modal-priority-section"),W(h,"for","forwardOnly"),W(w,"id","forwardOnly"),W(w,"type","checkbox"),W(w,"class","task-list-item-checkbox tasks-modal-checkbox"),W(w,"accesskey",O=r[15]("f")),W(p,"class","future-dates-only"),W(_,"class","tasks-modal-dates-section"),W(x,"class","tasks-modal-dependencies-section"),W(Ce,"class","tasks-modal-dates-section"),pi.disabled=Qo=!r[13],W(pi,"type","submit"),W(pi,"class","mod-cta"),W(sa,"type","button"),W(Xi,"class","tasks-modal-button-section"),W(e,"class","tasks-modal")},m(C,te){pe(C,e,te),U(e,t),U(t,n),n.innerHTML=i,U(t,s),U(t,a),yr(a,r[3].description),r[33](a),U(e,u),U(e,l),U(l,c),U(c,d),U(l,m);for(let pn=0;pn<St.length;pn+=1)St[pn]&&St[pn].m(l,null);U(e,y),U(e,b),U(e,k),U(e,_),Tr(R,_,null),U(_,F),Tr(q,_,null),U(_,B),Tr(ge,_,null),U(_,$),Tr(D,_,null),U(_,J),U(_,p),U(p,h),h.innerHTML=g,U(p,T),U(p,w),w.checked=r[3].forwardOnly,U(e,M),U(e,P),U(e,v),U(e,x),jn[N].m(x,null),U(e,ce),U(e,me),U(e,Ne),U(e,Ce),Tr(Nt,Ce,null),U(Ce,Br),Tr(ot,Ce,null),U(Ce,z),Tr(Er,Ce,null),U(Ce,Qh),Tr(Sr,Ce,null),U(e,Xh),U(e,Xi),U(Xi,pi),U(pi,Zh),U(Xi,Jh),U(Xi,sa),Hr=!0,Oc||(eg=[xe(a,"input",r[32]),xe(a,"keydown",r[24]),xe(a,"paste",r[25]),xe(a,"drop",r[25]),xe(w,"change",r[43]),xe(sa,"click",r[23]),xe(e,"submit",zv(r[26]))],Oc=!0)},p(C,te){if((!Hr||te[0]&32768)&&i!==(i=gt("Description",C[15]("t"))+"")&&(n.innerHTML=i),(!Hr||te[0]&32768&&o!==(o=C[15]("t")))&&W(a,"accesskey",o),te[0]&8&&yr(a,C[3].description),(!Hr||te[0]&8&&f!==(f="priority-"+C[3].priority))&&W(c,"for",f),te[0]&4229128){Zi=C[22];let nr;for(nr=0;nr<Zi.length;nr+=1){let sg=Uw(C,Zi,nr);St[nr]?St[nr].p(sg,te):(St[nr]=Ww(sg),St[nr].c(),St[nr].m(l,null))}for(;nr<St.length;nr+=1)St[nr].d(1);St.length=Zi.length}let pn={};te[0]&8&&(pn.editableTask=C[3]),te[0]&32768&&(pn.accesskey=C[15]("r")),!S&&te[0]&1024&&(S=!0,pn.isRecurrenceValid=C[10],Mt(()=>S=!1)),R.$set(pn);let aa={};te[0]&8&&(aa.forwardOnly=C[3].forwardOnly),te[0]&32768&&(aa.accesskey=C[15]("d")),!ne&&te[0]&8&&(ne=!0,aa.date=C[3].dueDate,Mt(()=>ne=!1)),!G&&te[0]&128&&(G=!0,aa.isDateValid=C[7],Mt(()=>G=!1)),q.$set(aa);let oa={};te[0]&8&&(oa.forwardOnly=C[3].forwardOnly),te[0]&32768&&(oa.accesskey=C[15]("s")),!Pe&&te[0]&8&&(Pe=!0,oa.date=C[3].scheduledDate,Mt(()=>Pe=!1)),!j&&te[0]&256&&(j=!0,oa.isDateValid=C[8],Mt(()=>j=!1)),ge.$set(oa);let ua={};te[0]&8&&(ua.forwardOnly=C[3].forwardOnly),te[0]&32768&&(ua.accesskey=C[15]("a")),!Y&&te[0]&8&&(Y=!0,ua.date=C[3].startDate,Mt(()=>Y=!1)),!I&&te[0]&512&&(I=!0,ua.isDateValid=C[9],Mt(()=>I=!1)),D.$set(ua),(!Hr||te[0]&32768)&&g!==(g=gt("Only future dates:",C[15]("f"))+"")&&(h.innerHTML=g),(!Hr||te[0]&32768&&O!==(O=C[15]("f")))&&W(w,"accesskey",O),te[0]&8&&(w.checked=C[3].forwardOnly);let Pc=N;N=ng(C,te),N===Pc?jn[N].p(C,te):(Jv(),Qt(jn[Pc],1,1,()=>{jn[Pc]=null}),ew(),ie=jn[N],ie?ie.p(C,te):(ie=jn[N]=rg[N](C),ie.c()),Wt(ie,1),ie.m(x,null));let la={};te[0]&1&&(la.task=C[0]),te[0]&2&&(la.statusOptions=C[1]),te[0]&32768&&(la.accesskey=C[15]("u")),!fn&&te[0]&8&&(fn=!0,la.editableTask=C[3],Mt(()=>fn=!1)),Nt.$set(la);let ca={};te[0]&8&&(ca.forwardOnly=C[3].forwardOnly),te[0]&32768&&(ca.accesskey=C[15]("c")),!Et&&te[0]&8&&(Et=!0,ca.date=C[3].createdDate,Mt(()=>Et=!1)),!Ge&&te[0]&32&&(Ge=!0,ca.isDateValid=C[5],Mt(()=>Ge=!1)),ot.$set(ca);let da={};te[0]&8&&(da.forwardOnly=C[3].forwardOnly),te[0]&32768&&(da.accesskey=C[15]("x")),!wc&&te[0]&8&&(wc=!0,da.date=C[3].doneDate,Mt(()=>wc=!1)),!kc&&te[0]&64&&(kc=!0,da.isDateValid=C[6],Mt(()=>kc=!1)),Er.$set(da);let fa={};te[0]&8&&(fa.forwardOnly=C[3].forwardOnly),te[0]&32768&&(fa.accesskey=C[15]("-")),!Ec&&te[0]&8&&(Ec=!0,fa.date=C[3].cancelledDate,Mt(()=>Ec=!1)),!Sc&&te[0]&16&&(Sc=!0,fa.isDateValid=C[4],Mt(()=>Sc=!1)),Sr.$set(fa),(!Hr||te[0]&8192&&Qo!==(Qo=!C[13]))&&(pi.disabled=Qo)},i(C){Hr||(Wt(R.$$.fragment,C),Wt(q.$$.fragment,C),Wt(ge.$$.fragment,C),Wt(D.$$.fragment,C),Wt(ie),Wt(Nt.$$.fragment,C),Wt(ot.$$.fragment,C),Wt(Er.$$.fragment,C),Wt(Sr.$$.fragment,C),Hr=!0)},o(C){Qt(R.$$.fragment,C),Qt(q.$$.fragment,C),Qt(ge.$$.fragment,C),Qt(D.$$.fragment,C),Qt(ie),Qt(Nt.$$.fragment,C),Qt(ot.$$.fragment,C),Qt(Er.$$.fragment,C),Qt(Sr.$$.fragment,C),Hr=!1},d(C){C&&oe(e),r[33](null),Li(St,C),Xt(R),Xt(q),Xt(ge),Xt(D),jn[N].d(),Xt(Nt),Xt(ot),Xt(Er),Xt(Sr),Oc=!1,Ut(eg)}}}function ZI(r,e,t){let n,{task:i}=e,{onSubmit:s}=e,{statusOptions:a}=e,{allTasks:o}=e,{modal:u=null}=e,{prioritySymbols:l,startDateSymbol:c,scheduledDateSymbol:d,dueDateSymbol:f,cancelledDateSymbol:m,createdDateSymbol:y,doneDateSymbol:b}=Nr.tasksPluginEmoji.taskSerializer.symbols,k,_=Is.fromTask(i,o),R=!0,S=!0,F=!0,q=!0,ne=!0,G=!0,B=!0,ge=!0,Pe=!0,j=!0,$=!1,D=[{value:"lowest",label:"Lowest",symbol:l.Lowest,accessKey:"o",accessKeyIndex:1},{value:"low",label:"Low",symbol:l.Low,accessKey:"l",accessKeyIndex:0},{value:"none",label:"Normal",symbol:l.None,accessKey:"n",accessKeyIndex:0},{value:"medium",label:"Medium",symbol:l.Medium,accessKey:"m",accessKeyIndex:0},{value:"high",label:"High",symbol:l.High,accessKey:"h",accessKeyIndex:0},{value:"highest",label:"Highest",symbol:l.Highest,accessKey:"i",accessKeyIndex:1}];Jm(()=>{let{provideAccessKeys:z}=Q();t(11,Pe=z),t(14,$=!0),setTimeout(()=>{k.focus()},10)});let Y=()=>{s([])},I=z=>{z.key==="Enter"&&(z.preventDefault(),j&&p())},J=()=>{setTimeout(()=>{t(3,_.description=_.description.replace(/[\r\n]+/g," "),_)},0)},p=()=>tw(void 0,void 0,void 0,function*(){let z=yield _.applyEdits(i,o);s(z)});function h(z){u&&u.setActiveFlatpickrInstance(z.detail.instance)}function g(){u&&u.clearActiveFlatpickrInstance()}let T=[[]];function w(){_.description=this.value,t(3,_)}function O(z){je[z?"unshift":"push"](()=>{k=z,t(12,k)})}function M(){_.priority=this.__value,t(3,_)}function P(z){ge=z,t(10,ge)}function v(z){r.$$.not_equal(_.dueDate,z)&&(_.dueDate=z,t(3,_))}function x(z){ne=z,t(7,ne)}function N(z){r.$$.not_equal(_.scheduledDate,z)&&(_.scheduledDate=z,t(3,_))}function ie(z){G=z,t(8,G)}function ce(z){r.$$.not_equal(_.startDate,z)&&(_.startDate=z,t(3,_))}function me(z){B=z,t(9,B)}function Ne(){_.forwardOnly=this.checked,t(3,_)}function Ce(z){_=z,t(3,_)}function Nt(z){r.$$.not_equal(_.createdDate,z)&&(_.createdDate=z,t(3,_))}function fn(z){F=z,t(5,F)}function Br(z){r.$$.not_equal(_.doneDate,z)&&(_.doneDate=z,t(3,_))}function ot(z){q=z,t(6,q)}function Et(z){r.$$.not_equal(_.cancelledDate,z)&&(_.cancelledDate=z,t(3,_))}function Ge(z){S=z,t(4,S)}return r.$$set=z=>{"task"in z&&t(0,i=z.task),"onSubmit"in z&&t(29,s=z.onSubmit),"statusOptions"in z&&t(1,a=z.statusOptions),"allTasks"in z&&t(2,o=z.allTasks),"modal"in z&&t(30,u=z.modal)},r.$$.update=()=>{if(r.$$.dirty[0]&2048){e:t(15,n=z=>Pe?z:null)}if(r.$$.dirty[0]&8){e:t(31,R=_.description.trim()!=="")}if(r.$$.dirty[0]&2032|r.$$.dirty[1]&1){e:t(13,j=ne&&ge&&G&&B&&R&&S&&F&&q)}},[i,a,o,_,S,F,q,ne,G,B,ge,Pe,k,j,$,n,c,d,f,m,y,b,D,Y,I,J,p,h,g,s,u,R,w,O,M,T,P,v,x,N,ie,ce,me,Ne,Ce,Nt,fn,Br,ot,Et,Ge]}var hh=class extends gr{constructor(e){super(),nn(this,e,ZI,XI,rn,{task:0,onSubmit:29,statusOptions:1,allTasks:2,modal:30},null,[-1,-1])}},qw=hh;var ti=class extends $w.Modal{constructor({app:t,task:n,onSubmit:i,allTasks:s}){super(t);this.activeFlatpickrInstance=null;this.task=n,this.allTasks=s,this.onSubmit=a=>{this.activeFlatpickrInstance||(a.length&&i(a),this.close())}}onOpen(){this.titleEl.setText("Create or edit Task"),this.modalEl.style.paddingBottom="0";let{contentEl:t}=this;this.contentEl.style.paddingBottom="0";let n=this.getKnownStatusesAndCurrentTaskStatusIfNotKnown();new qw({target:t,props:{task:this.task,statusOptions:n,onSubmit:this.onSubmit,allTasks:this.allTasks,modal:this}})}getKnownStatusesAndCurrentTaskStatusIfNotKnown(){let t=De.getInstance().registeredStatuses;return De.getInstance().bySymbol(this.task.status.symbol)===re.EMPTY&&t.push(this.task.status),t}onClose(){let{contentEl:t}=this;t.empty()}onEscapeKey(){var t;(t=this.activeFlatpickrInstance)!=null&&t.isOpen?(this.activeFlatpickrInstance.close(),this.activeFlatpickrInstance=null):this.close()}setActiveFlatpickrInstance(t){this.activeFlatpickrInstance=t}clearActiveFlatpickrInstance(){this.activeFlatpickrInstance=null}};function JI(){let{setCreatedDate:r}=Q();return r?window.moment():null}function eF(r){let{setCreatedDate:e}=Q();if(!e||r.createdDate!==null)return!1;let t=r.description==="",n=!ve.getInstance().isEmpty(),i=!ve.getInstance().includedIn(r.description);return t||n&&i}var Sl=({line:r,path:e})=>{var f,m;let t=ee.parseTaskSignifiers(r,ht.fromUnknownPosition(new ut(e)),_t.fromPath(e)),n=JI();if(t!==null)return eF(t)?new ee(de(K({},t),{createdDate:n})):t;let i=r.match(Z.nonTaskRegex);if(i===null)return console.error("Tasks: Cannot create task on line:",r),new ee({status:re.TODO,description:"",taskLocation:ht.fromUnknownPosition(new ut(e)),indentation:"",listMarker:"-",priority:"3",createdDate:n,startDate:null,scheduledDate:null,dueDate:null,doneDate:null,cancelledDate:null,recurrence:null,onCompletion:"",dependsOn:[],id:"",blockLink:"",tags:[],originalMarkdown:"",scheduledDateIsInferred:!1});let s=i[1],a=(f=i[2])!=null?f:"-",o=(m=i[4])!=null?m:" ",u=De.getInstance().bySymbolOrCreate(o),l=i[5],c=r.match(Z.blockLinkRegex),d=c!==null?c[0]:"";return d!==""&&(l=l.replace(Z.blockLinkRegex,"")),new ee({status:u,description:l,taskLocation:ht.fromUnknownPosition(new ut(e)),indentation:s,listMarker:a,blockLink:d,priority:"3",createdDate:n,startDate:null,scheduledDate:null,dueDate:null,doneDate:null,cancelledDate:null,recurrence:null,onCompletion:"",tags:[],originalMarkdown:"",scheduledDateIsInferred:!1,id:"",dependsOn:[]})};var jw=(r,e,t,n,i)=>{var f;if(r)return t instanceof gh.MarkdownView;if(!(t instanceof gh.MarkdownView))return;let s=(f=t.file)==null?void 0:f.path;if(s===void 0)return;let o=e.getCursor().line,u=e.getLine(o),l=Sl({line:u,path:s}),c=m=>{let y=_t.removeInferredStatusIfNeeded(l,m).map(b=>b.toFileLineString()).join(`
`);e.setLine(o,y)};new ti({app:n,task:l,onSubmit:c,allTasks:i}).open()};var yh=require("obsidian");var Gw=(r,e,t)=>{var u;if(r)return t instanceof yh.MarkdownView;if(!(t instanceof yh.MarkdownView))return;let n=(u=t.file)==null?void 0:u.path;if(n===void 0)return;let i=e.getCursor(),s=i.line,a=e.getLine(s),o=Th(a,n);e.setLine(s,o.text),e.setCursor(tF(i,o))},Th=(r,e)=>{let t=ee.fromLine({line:r,taskLocation:ht.fromUnknownPosition(new ut(e)),fallbackDate:null});if(t!==null){let n=t.toggleWithRecurrenceInUsersOrder().map(i=>i.toFileLineString());return{text:n.join(`
`),moveTo:{line:n.length-1}}}else{let n=r.match(Z.taskRegex);if(n!==null){let i=n[3],a=De.getInstance().bySymbol(i).nextStatusSymbol;return{text:r.replace(Z.taskRegex,`$1- [${a}] $4`)}}else if(Z.listItemRegex.test(r)){let i=r.replace(Z.listItemRegex,"$1$2 [ ]");return{text:i,moveTo:{ch:i.length}}}else{let i=r.replace(Z.indentationRegex,"$1- ");return{text:i,moveTo:{ch:i.length}}}}},tF=(r,e)=>{var s;let t={line:0,ch:r.ch},n=K(K({},t),(s=e.moveTo)!=null?s:{}),i=e.text.split(`
`)[n.line].length;return{line:r.line+n.line,ch:Math.min(n.ch,i)}};var Ol=class{get app(){return this.plugin.app}constructor({plugin:e}){this.plugin=e,e.addCommand({id:"edit-task",name:"Create or edit task",icon:"pencil",editorCheckCallback:(t,n,i)=>jw(t,n,i,this.app,this.plugin.getTasks())}),e.addCommand({id:"toggle-done",name:"Toggle task done",icon:"check-in-circle",editorCheckCallback:Gw})}};var ri=class{constructor(){this.hidePostponeButton=!1;this.hideTaskCount=!1;this.hideBacklinks=!1;this.hideEditButton=!1;this.hideUrgency=!0;this.hideTree=!0;this.shortMode=!1;this.explainQuery=!1}};function ji(r,e){let t=`Error: ${r}.
The error message was:
    `,n="";return e instanceof Error?n+=e:n+="Unknown error",`${t}"${n}"`}var rF=Object.prototype.toString,Ls=Array.isArray||function(e){return rF.call(e)==="[object Array]"};function _h(r){return typeof r=="function"}function nF(r){return Ls(r)?"array":typeof r}function bh(r){return r.replace(/[\-\[\]{}()*+?.,\\\^$|#\s]/g,"\\$&")}function Yw(r,e){return r!=null&&typeof r=="object"&&e in r}function iF(r,e){return r!=null&&typeof r!="object"&&r.hasOwnProperty&&r.hasOwnProperty(e)}var sF=RegExp.prototype.test;function aF(r,e){return sF.call(r,e)}var oF=/\S/;function uF(r){return!aF(oF,r)}var lF={"&":"&amp;","<":"&lt;",">":"&gt;",'"':"&quot;","'":"&#39;","/":"&#x2F;","`":"&#x60;","=":"&#x3D;"};function cF(r){return String(r).replace(/[&<>"'`=\/]/g,function(t){return lF[t]})}var dF=/\s*/,fF=/\s+/,Bw=/\s*=/,pF=/\s*\}/,mF=/#|\^|\/|>|\{|&|=|!/;function hF(r,e){if(!r)return[];var t=!1,n=[],i=[],s=[],a=!1,o=!1,u="",l=0;function c(){if(a&&!o)for(;s.length;)delete i[s.pop()];else s=[];a=!1,o=!1}var d,f,m;function y(B){if(typeof B=="string"&&(B=B.split(fF,2)),!Ls(B)||B.length!==2)throw new Error("Invalid tags: "+B);d=new RegExp(bh(B[0])+"\\s*"),f=new RegExp("\\s*"+bh(B[1])),m=new RegExp("\\s*"+bh("}"+B[1]))}y(e||_r.tags);for(var b=new qo(r),k,_,R,S,F,q;!b.eos();){if(k=b.pos,R=b.scanUntil(d),R)for(var ne=0,G=R.length;ne<G;++ne)S=R.charAt(ne),uF(S)?(s.push(i.length),u+=S):(o=!0,t=!0,u+=" "),i.push(["text",S,k,k+1]),k+=1,S===`
`&&(c(),u="",l=0,t=!1);if(!b.scan(d))break;if(a=!0,_=b.scan(mF)||"name",b.scan(dF),_==="="?(R=b.scanUntil(Bw),b.scan(Bw),b.scanUntil(f)):_==="{"?(R=b.scanUntil(m),b.scan(pF),b.scanUntil(f),_="&"):R=b.scanUntil(f),!b.scan(f))throw new Error("Unclosed tag at "+b.pos);if(_==">"?F=[_,R,k,b.pos,u,l,t]:F=[_,R,k,b.pos],l++,i.push(F),_==="#"||_==="^")n.push(F);else if(_==="/"){if(q=n.pop(),!q)throw new Error('Unopened section "'+R+'" at '+k);if(q[1]!==R)throw new Error('Unclosed section "'+q[1]+'" at '+k)}else _==="name"||_==="{"||_==="&"?o=!0:_==="="&&y(R)}if(c(),q=n.pop(),q)throw new Error('Unclosed section "'+q[1]+'" at '+b.pos);return yF(gF(i))}function gF(r){for(var e=[],t,n,i=0,s=r.length;i<s;++i)t=r[i],t&&(t[0]==="text"&&n&&n[0]==="text"?(n[1]+=t[1],n[3]=t[3]):(e.push(t),n=t));return e}function yF(r){for(var e=[],t=e,n=[],i,s,a=0,o=r.length;a<o;++a)switch(i=r[a],i[0]){case"#":case"^":t.push(i),n.push(i),t=i[4]=[];break;case"/":s=n.pop(),s[5]=i[2],t=n.length>0?n[n.length-1][4]:e;break;default:t.push(i)}return e}function qo(r){this.string=r,this.tail=r,this.pos=0}qo.prototype.eos=function(){return this.tail===""};qo.prototype.scan=function(e){var t=this.tail.match(e);if(!t||t.index!==0)return"";var n=t[0];return this.tail=this.tail.substring(n.length),this.pos+=n.length,n};qo.prototype.scanUntil=function(e){var t=this.tail.search(e),n;switch(t){case-1:n=this.tail,this.tail="";break;case 0:n="";break;default:n=this.tail.substring(0,t),this.tail=this.tail.substring(t)}return this.pos+=n.length,n};function Fs(r,e){this.view=r,this.cache={".":this.view},this.parent=e}Fs.prototype.push=function(e){return new Fs(e,this)};Fs.prototype.lookup=function(e){var t=this.cache,n;if(t.hasOwnProperty(e))n=t[e];else{for(var i=this,s,a,o,u=!1;i;){if(e.indexOf(".")>0)for(s=i.view,a=e.split("."),o=0;s!=null&&o<a.length;)o===a.length-1&&(u=Yw(s,a[o])||iF(s,a[o])),s=s[a[o++]];else s=i.view[e],u=Yw(i.view,e);if(u){n=s;break}i=i.parent}t[e]=n}return _h(n)&&(n=n.call(this.view)),n};function At(){this.templateCache={_cache:{},set:function(e,t){this._cache[e]=t},get:function(e){return this._cache[e]},clear:function(){this._cache={}}}}At.prototype.clearCache=function(){typeof this.templateCache!="undefined"&&this.templateCache.clear()};At.prototype.parse=function(e,t){var n=this.templateCache,i=e+":"+(t||_r.tags).join(":"),s=typeof n!="undefined",a=s?n.get(i):void 0;return a==null&&(a=hF(e,t),s&&n.set(i,a)),a};At.prototype.render=function(e,t,n,i){var s=this.getConfigTags(i),a=this.parse(e,s),o=t instanceof Fs?t:new Fs(t,void 0);return this.renderTokens(a,o,n,e,i)};At.prototype.renderTokens=function(e,t,n,i,s){for(var a="",o,u,l,c=0,d=e.length;c<d;++c)l=void 0,o=e[c],u=o[0],u==="#"?l=this.renderSection(o,t,n,i,s):u==="^"?l=this.renderInverted(o,t,n,i,s):u===">"?l=this.renderPartial(o,t,n,s):u==="&"?l=this.unescapedValue(o,t):u==="name"?l=this.escapedValue(o,t,s):u==="text"&&(l=this.rawValue(o)),l!==void 0&&(a+=l);return a};At.prototype.renderSection=function(e,t,n,i,s){var a=this,o="",u=t.lookup(e[1]);function l(f){return a.render(f,t,n,s)}if(!!u){if(Ls(u))for(var c=0,d=u.length;c<d;++c)o+=this.renderTokens(e[4],t.push(u[c]),n,i,s);else if(typeof u=="object"||typeof u=="string"||typeof u=="number")o+=this.renderTokens(e[4],t.push(u),n,i,s);else if(_h(u)){if(typeof i!="string")throw new Error("Cannot use higher-order sections without the original template");u=u.call(t.view,i.slice(e[3],e[5]),l),u!=null&&(o+=u)}else o+=this.renderTokens(e[4],t,n,i,s);return o}};At.prototype.renderInverted=function(e,t,n,i,s){var a=t.lookup(e[1]);if(!a||Ls(a)&&a.length===0)return this.renderTokens(e[4],t,n,i,s)};At.prototype.indentPartial=function(e,t,n){for(var i=t.replace(/[^ \t]/g,""),s=e.split(`
`),a=0;a<s.length;a++)s[a].length&&(a>0||!n)&&(s[a]=i+s[a]);return s.join(`
`)};At.prototype.renderPartial=function(e,t,n,i){if(!!n){var s=this.getConfigTags(i),a=_h(n)?n(e[1]):n[e[1]];if(a!=null){var o=e[6],u=e[5],l=e[4],c=a;u==0&&l&&(c=this.indentPartial(a,l,o));var d=this.parse(c,s);return this.renderTokens(d,t,n,c,i)}}};At.prototype.unescapedValue=function(e,t){var n=t.lookup(e[1]);if(n!=null)return n};At.prototype.escapedValue=function(e,t,n){var i=this.getConfigEscape(n)||_r.escape,s=t.lookup(e[1]);if(s!=null)return typeof s=="number"&&i===_r.escape?String(s):i(s)};At.prototype.rawValue=function(e){return e[1]};At.prototype.getConfigTags=function(e){return Ls(e)?e:e&&typeof e=="object"?e.tags:void 0};At.prototype.getConfigEscape=function(e){if(e&&typeof e=="object"&&!Ls(e))return e.escape};var _r={name:"mustache.js",version:"4.2.0",tags:["{{","}}"],clearCache:void 0,escape:void 0,parse:void 0,render:void 0,Scanner:void 0,Context:void 0,Writer:void 0,set templateCache(r){Wo.templateCache=r},get templateCache(){return Wo.templateCache}},Wo=new At;_r.clearCache=function(){return Wo.clearCache()};_r.parse=function(e,t){return Wo.parse(e,t)};_r.render=function(e,t,n,i){if(typeof e!="string")throw new TypeError('Invalid template! Template should be a "string" but "'+nF(e)+'" was given as the first argument for mustache#render(template, view, partials)');return Wo.render(e,t,n,i)};_r.escape=cF;_r.Scanner=qo;_r.Context=Fs;_r.Writer=At;var vh=_r;var Qw=ma(Kw());function Xw(r,e){vh.escape=function(t){return t};try{return vh.render(r,(0,Qw.default)(e))}catch(t){let n="";throw t instanceof Error?n=`There was an error expanding one or more placeholders.

The error message was:
    ${t.message.replace(/ > /g,".").replace("Missing Mustache data property","Unknown property")}`:n="Unknown error expanding placeholders.",n+=`

The problem is in:
    ${r}`,Error(n)}}function Zw(r){return kh(r,[])}function kh(r,e){return{query:{file:r,allTasks:e}}}var Us=class{constructor(e=""){this.indentation=e}explainQuery(e){if(e.error!==void 0)return this.explainError(e);let t=[];return t.push(this.explainFilters(e)),t.push(this.explainGroups(e)),t.push(this.explainSorters(e)),t.push(this.explainQueryLimits(e)),t.push(this.explainDebugSettings()),t.filter(n=>n!=="").join(`
`)}explainError(e){let t="";return t+=`Query has an error:
`,t+=e.error+`
`,t}explainFilters(e){return e.filters.length===0?this.indent(`No filters supplied. All tasks will match the query.
`):e.filters.map(n=>n.explainFilterIndented(this.indentation)).join(`
`)}explainGroups(e){let t=e.grouping.length;if(t===0)return this.indent(`No grouping instructions supplied.
`);let n="";for(let i=0;i<t;i++)n+=this.indentation+e.grouping[i].instruction+`
`;return n}explainSorters(e){let t=e.sorting.length;if(t===0)return this.indent(`No sorting instructions supplied.
`);let n="";for(let i=0;i<t;i++)n+=this.indentation+e.sorting[i].instruction+`
`;return n}explainQueryLimits(e){function t(i){let s=`At most ${i} task`;return i!==1&&(s+="s"),s}let n=[];if(e.limit!==void 0){let i=t(e.limit)+`.
`;n.push(this.indent(i))}if(e.taskGroupLimit!==void 0){let i=t(e.taskGroupLimit)+` per group (if any "group by" options are supplied).
`;n.push(this.indent(i))}return n.join(`
`)}explainDebugSettings(){let e="",{debugSettings:t}=Q();return t.ignoreSortInstructions&&(e+=this.indent(`NOTE: All sort instructions, including default sort order, are disabled, due to 'ignoreSortInstructions' setting.
`)),e}indent(e){return this.indentation+e}};var Se=class{constructor(e,t=[],n=""){this.description=e,this.symbol=n,this.children=t}static booleanAnd(e){return this.combineOrCreateExplanation("All of",e,"AND")}static booleanOr(e){return this.combineOrCreateExplanation("At least one of",e,"OR")}static booleanNot(e){return new Se("None of",e,"NOT")}static booleanXor(e){return new Se("Exactly one of",e,"XOR")}asString(e=""){if(this.children.length==0)return e+this.description;let t=e;this.symbol===""?t+=this.description:(t+=this.symbol,this.children.length>1&&(t+=` (${this.description})`),t+=":");let n=e+"  ";for(let i=0;i<this.children.length;i++)t+=`
${this.children[i].asString(n)}`;return t}static combineOrCreateExplanation(e,t,n){if(t.length===2){let i=t[0],s=t[1];if(i.symbol===n&&s.symbol==="")return i.children.push(s),i}return new Se(e,t,n)}};var Ws=class{matchesAnyOf(e){return e.some(t=>this.matches(t))}};var qs=class extends Ws{constructor(t){super();this.stringToFind=t}matches(t){return qs.stringIncludesCaseInsensitive(t,this.stringToFind)}static stringIncludesCaseInsensitive(t,n){return t.toLocaleLowerCase().includes(n.toLocaleLowerCase())}explanation(t){return new Se(t)}};var ni=class extends Ws{constructor(t){super();this.regex=t}static validateAndConstruct(t){let n=/^\/(.+)\/([^/]*)$/,i=t.match(n);if(i!==null){let s=new RegExp(i[1],i[2]);return new ni(s)}else return null}matches(t){return t.match(this.regex)!==null}static helpMessage(){return String.raw`See https://publish.obsidian.md/tasks/Queries/Regular+Expressions

Regular expressions must look like this:
    /pattern/
or this:
    /pattern/flags

Where:
- pattern: The 'regular expression' pattern to search for.
- flags:   Optional characters that modify the search.
           i => make the search case-insensitive
           u => add Unicode support

Examples:  /^Log/
           /^Log/i
           /File Name\.md/
           /waiting|waits|waited/i
           /\d\d:\d\d/

The following characters have special meaning in the pattern:
to find them literally, you must add a \ before them:
    [\^$.|?*+()

CAUTION! Regular expression (or 'regex') searching is a powerful
but advanced feature that requires thorough knowledge in order to
use successfully, and not miss intended search results.
`}explanation(t){let i=TF(t,"using regex: ",this.regexAsString());return new Se(i)}regexAsString(){let t=`'${this.regex.source}' with `;switch(this.regex.flags.length){case 0:t+="no flags";break;case 1:t+=`flag '${this.regex.flags}'`;break;default:t+=`flags '${this.regex.flags}'`;break}return t}};function TF(r,e,t){var o;let n=r.match(/\//);if(!n)return"Error explaining instruction. Could not find a slash character";let i=2,s=((o=n.index)!=null?o:i)-i;return`${e.padEnd(s)}${t}`}var Wr=class{constructor(e,t,n,i){this.instruction=e,this.property=t,this.comparator=Wr.maybeReverse(i,n)}static maybeReverse(e,t){return e?Wr.makeReversedComparator(t):t}static makeReversedComparator(e){return(t,n,i)=>e(t,n,i)*-1}};var ii=class{constructor(e,t,n,i){this.instruction=e,this.property=t,this.grouper=n,this.reverse=i}};var Ee=class{canCreateFilterForLine(e){return Ee.lineMatchesFilter(this.filterRegExp(),e)}static lineMatchesFilter(e,t){return e?e.test(t):!1}static getMatch(e,t){return e?t.match(e):null}fieldNameSingular(){return this.fieldName()}fieldNameSingularEscaped(){return Zn(this.fieldNameSingular())}supportsSorting(){return!1}createSorterFromLine(e){if(!this.supportsSorting())return null;let t=Ee.getMatch(this.sorterRegExp(),e);if(t===null)return null;let n=!!t[1];return this.createSorter(n)}sorterRegExp(){if(!this.supportsSorting())throw Error(`sorterRegExp() unimplemented for ${this.fieldNameSingular()}`);return new RegExp(`^sort by ${this.fieldNameSingularEscaped()}( reverse)?`,"i")}sorterInstruction(e){let t=`sort by ${this.fieldNameSingular()}`;return e&&(t+=" reverse"),t}comparator(){throw Error(`comparator() unimplemented for ${this.fieldNameSingular()}`)}createSorter(e){return new Wr(this.sorterInstruction(e),this.fieldNameSingular(),this.comparator(),e)}createNormalSorter(){return this.createSorter(!1)}createReverseSorter(){return this.createSorter(!0)}supportsGrouping(){return!1}createGrouperFromLine(e){if(!this.supportsGrouping())return null;let t=Ee.getMatch(this.grouperRegExp(),e);if(t===null)return null;let n=!!t[1];return this.createGrouper(n)}grouperRegExp(){if(!this.supportsGrouping())throw Error(`grouperRegExp() unimplemented for ${this.fieldNameSingular()}`);return new RegExp(`^group by ${this.fieldNameSingularEscaped()}( reverse)?$`,"i")}grouperInstruction(e){let t=`group by ${this.fieldNameSingular()}`;return e&&(t+=" reverse"),t}grouper(){throw Error(`grouper() unimplemented for ${this.fieldNameSingular()}`)}createGrouper(e){return new ii(this.grouperInstruction(e),this.fieldNameSingular(),this.grouper(),e)}createNormalGrouper(){return this.createGrouper(!1)}createReverseGrouper(){return this.createGrouper(!0)}};var Ln=class{constructor(e,t){this._rawInstruction=e,this._anyContinuationLinesRemoved=t.trim(),this._anyPlaceholdersExpanded=this._anyContinuationLinesRemoved}recordExpandedPlaceholders(e){this._anyPlaceholdersExpanded=e}get rawInstruction(){return this._rawInstruction}get anyContinuationLinesRemoved(){return this._anyContinuationLinesRemoved}get anyPlaceholdersExpanded(){return this._anyPlaceholdersExpanded}explainStatement(e){function t(a,o){o!==a&&(s+=` =>
${e}${o}`)}let n=this._rawInstruction.trim(),i=n.split(`
`).join(`
`+e),s=`${e}${i}`;return this._rawInstruction.includes(`
`)&&(s+=`
`+e),t(n,this._anyContinuationLinesRemoved),t(this._anyContinuationLinesRemoved,this._anyPlaceholdersExpanded),s}allLinesIdentical(){return this._rawInstruction===this._anyContinuationLinesRemoved&&this._rawInstruction===this._anyPlaceholdersExpanded}};var vt=class{constructor(e,t,n){this._statement=new Ln(e,e),this.explanation=n,this.filterFunction=t}get statement(){return this._statement}setStatement(e){this._statement=e}get instruction(){return this._statement.anyPlaceholdersExpanded}explainFilterIndented(e){let t=this._statement.explainStatement(e);return this.onlyNeedsOneLineExplanation()?`${t}
`:`${t} =>
${this.explanation.asString(e+"  ")}
`}simulateExplainFilter(){return this.onlyNeedsOneLineExplanation()?this.explanation:new Se(this.instruction+" =>",[this.explanation])}onlyNeedsOneLineExplanation(){return this.explanation.asString("")===this.instruction}};var an=class{constructor(e){this.instruction=e}get queryComponent(){return this._queryComponent}set queryComponent(e){this._queryComponent=e}get error(){return this._error}set error(e){this._error=e}isValid(){return this._queryComponent!==void 0}static fromObject(e,t){let n=new an(e);return n._queryComponent=t,n}static fromError(e,t){let n=new an(e);return n._error=t,n}};var se=class{constructor(e){this.object=e}get instruction(){return this.object.instruction}get filter(){return this.object.queryComponent}isValid(){return this.object.isValid()}get error(){return this.object.error}get filterFunction(){if(this.filter)return this.filter.filterFunction}static fromFilter(e){return new se(an.fromObject(e.instruction,e))}static fromError(e,t){return new se(an.fromError(e,t))}};var Ie=class extends Ee{createFilterOrErrorMessage(e){let t=Ee.getMatch(this.filterRegExp(),e);if(t===null)return se.fromError(e,`do not understand query filter (${this.fieldName()})`);let n=t[1].toLowerCase(),i=t[2],s=null;if(n.includes("include"))s=new qs(i);else if(n.includes("regex")){try{s=ni.validateAndConstruct(i)}catch(u){let l=ji("Parsing regular expression",u)+`

${ni.helpMessage()}`;return se.fromError(e,l)}if(s===null)return se.fromError(e,`Invalid instruction: '${e}'

${ni.helpMessage()}`)}if(s===null)return se.fromError(e,`do not understand query filter (${this.fieldName()})`);let a=n.match(/not/)!==null,o=new vt(e,this.getFilter(s,a),s.explanation(e));return se.fromFilter(o)}fieldPattern(){return this.fieldNameSingularEscaped()}filterOperatorPattern(){return"includes|does not include|regex matches|regex does not match"}filterRegExp(){return new RegExp(`^(?:${this.fieldPattern()}) (${this.filterOperatorPattern()}) (.*)`,"i")}getFilter(e,t){return n=>{let i=e.matches(this.value(n));return t?!i:i}}comparator(){return(e,t)=>this.value(e).localeCompare(this.value(t),void 0,{numeric:!0})}grouper(){return e=>[this.value(e)]}static escapeMarkdownCharacters(e){return e.replace(/\\/g,"\\\\").replace(/_/g,"\\_")}};var Gi=class extends Ie{fieldName(){return"description"}value(e){return ve.getInstance().removeAsSubstringFrom(e.description)}supportsSorting(){return!0}comparator(){return(e,t)=>{let n=Gi.cleanDescription(e.description),i=Gi.cleanDescription(t.description);return n.localeCompare(i,void 0,{numeric:!0})}}static cleanDescription(e){e=ve.getInstance().removeAsSubstringFrom(e);let t=/^\[\[?([^\]]*)]]?/,n=e.match(t);if(n!==null){let i=n[1];e=i.substring(i.indexOf("|")+1)+e.replace(t,"")}return e=this.replaceFormatting(e,/^\*\*([^*]+)\*\*/),e=this.replaceFormatting(e,/^\*([^*]+)\*/),e=this.replaceFormatting(e,/^==([^=]+)==/),e=this.replaceFormatting(e,/^__([^_]+)__/),e=this.replaceFormatting(e,/^_([^_]+)_/),e}static replaceFormatting(e,t){let n=e.match(t);return n!==null&&(e=n[1]+e.replace(t,"")),e}};var Dl=class{findUnexpandedDateText(e){let t=["<%","YYYY-MM-DD"];for(let n of t)if(e.includes(n))return this.unexpandedDateTextMessage(n);return null}unexpandedDateTextMessage(e){return`Instruction contains unexpanded template text: "${e}" - and cannot be interpreted.

Possible causes:
- The query is an a template file, and is not intended to be searched.
- A command such as "Replace templates in the active file" needs to be run.
- The core "Daily notes" plugin is in use, and the template contained
  date calculations that it does not support.
- Some sample template text was accidentally pasted in to a tasks query,
  instead of in to a template file.

See: https://publish.obsidian.md/tasks/Advanced/Instruction+contains+unexpanded+template+text
`}};var xl=class{constructor(e,t){this._instruction=e,this._filter=t}canCreateFilterForLine(e){return e.toLocaleLowerCase()===this._instruction.toLocaleLowerCase()}createFilterOrErrorMessage(e){return this.canCreateFilterForLine(e)?se.fromFilter(new vt(e,this._filter,new Se(e))):se.fromError(e,`do not understand filter: ${e}`)}};var jt=class{constructor(){this._filters=[]}add(e,t){this._filters.push(new xl(e,t))}canCreateFilterForLine(e){return this._filters.some(t=>t.canCreateFilterForLine(e))}createFilterOrErrorMessage(e){for(let t of this._filters){let n=t.createFilterOrErrorMessage(e);if(n.isValid())return n}return se.fromError(e,`do not understand filter: ${e}`)}};var at=class extends Ee{constructor(t=null){super();t!==null?this.filterInstructions=t:(this.filterInstructions=new jt,this.filterInstructions.add(`has ${this.fieldName()} date`,n=>this.date(n)!==null),this.filterInstructions.add(`no ${this.fieldName()} date`,n=>this.date(n)===null),this.filterInstructions.add(`${this.fieldName()} date is invalid`,n=>{let i=this.date(n);return i!==null&&!i.isValid()}))}canCreateFilterForLine(t){return this.filterInstructions.canCreateFilterForLine(t)?!0:super.canCreateFilterForLine(t)}createFilterOrErrorMessage(t){var f;let n=this.checkForUnexpandedTemplateText(t);if(n)return se.fromError(t,n);let i=this.filterInstructions.createFilterOrErrorMessage(t);if(i.isValid())return i;let s=Ee.getMatch(this.filterRegExp(),t);if(s===null)return se.fromError(t,"do not understand query filter ("+this.fieldName()+" date)");let a=s[1],o=(f=s[2])==null?void 0:f.toLowerCase(),u=s[3],l=Vt.parseDateRange(u);if(!l.isValid()){let m=Vt.parseDate(a);m.isValid()&&(l=new Dt(m,m))}if(!l.isValid())return se.fromError(t,"do not understand "+this.fieldName()+" date");let c=this.buildFilterFunction(o,l),d=at.buildExplanation(this.fieldNameForExplanation(),o,this.filterResultIfFieldMissing(),l);return se.fromFilter(new vt(t,c,d))}buildFilterFunction(t,n){let i;switch(t){case"before":i=s=>s?s.isBefore(n.start):this.filterResultIfFieldMissing();break;case"after":i=s=>s?s.isAfter(n.end):this.filterResultIfFieldMissing();break;case"on or before":case"in or before":i=s=>s?s.isSameOrBefore(n.end):this.filterResultIfFieldMissing();break;case"on or after":case"in or after":i=s=>s?s.isSameOrAfter(n.start):this.filterResultIfFieldMissing();break;default:i=s=>s?s.isSameOrAfter(n.start)&&s.isSameOrBefore(n.end):this.filterResultIfFieldMissing()}return this.getFilter(i)}getFilter(t){return n=>t(this.date(n))}filterRegExp(){return new RegExp(`^${this.fieldNameForFilterInstruction()} (((?:on|in) or before|before|(?:on|in) or after|after|on|in)? ?(.*))`,"i")}fieldNameForFilterInstruction(){return this.fieldName()}static buildExplanation(t,n,i,s){let a=n,o="YYYY-MM-DD (dddd Do MMMM YYYY)",u;switch(n){case"before":case"on or after":u=s.start.format(o);break;case"after":case"on or before":u=s.end.format(o);break;case"in or before":a="on or before",u=s.end.format(o);break;case"in or after":a="on or after",u=s.start.format(o);break;default:if(!s.start.isSame(s.end)){let c=`${t} date is between:`,d=[new Se(`${s.start.format(o)} and`),new Se(`${s.end.format(o)} inclusive`)];return i&&d.push(new Se(`OR no ${t} date`)),new Se(c,d)}a="on",u=s.start.format(o);break}let l=`${t} date is ${a} ${u}`;return i&&(l+=` OR no ${t} date`),new Se(l)}fieldNameForExplanation(){return this.fieldName()}supportsSorting(){return!0}comparator(){return(t,n)=>dr(this.date(t),this.date(n))}supportsGrouping(){return!0}grouper(){return t=>{let n=this.date(t);return n===null?["No "+this.fieldName()+" date"]:n.isValid()?[n.format("YYYY-MM-DD dddd")]:["%%0%% Invalid "+this.fieldName()+" date"]}}checkForUnexpandedTemplateText(t){return new Dl().findUnexpandedDateText(t)}};var Rl=class extends at{fieldName(){return"created"}date(e){return e.createdDate}filterResultIfFieldMissing(){return!1}};var Ml=class extends at{fieldName(){return"done"}date(e){return e.doneDate}filterResultIfFieldMissing(){return!1}};var $s=class extends at{fieldName(){return"due"}date(e){return e.dueDate}filterResultIfFieldMissing(){return!1}};var vr=class extends Ee{constructor(){super(...arguments);this._filters=new jt}canCreateFilterForLine(t){return this._filters.canCreateFilterForLine(t)}createFilterOrErrorMessage(t){return this._filters.createFilterOrErrorMessage(t)}filterRegExp(){return null}};var Cl=class extends vr{constructor(){super(),this._filters.add("exclude sub-items",e=>{if(e.indentation==="")return!0;let t=e.indentation.lastIndexOf(">");return t===-1?!1:/^ ?$/.test(e.indentation.slice(t+1))})}fieldName(){return"exclude"}};var $o=class extends an{};function Eh(r,e){let t=r.map(([n])=>n);try{let n=e.includes("return")?e:`return ${e}`,i=e&&new Function(...t,n);return i instanceof Function?$o.fromObject(e,i):$o.fromError(e,"Error parsing group function")}catch(n){return $o.fromError(e,ji(`Failed parsing expression "${e}"`,n))}}function Sh(r,e){let t=e.map(([n,i])=>i);return r(...t)}function Oh(r,e,t){try{return Sh(r,e)}catch(n){return ji(`Failed calculating expression "${t}"`,n)}}function Al(r,e){return[["task",r],["query",e?e.query:null]]}function Jw(r,e,t){let n=Al(r,t||null),i=Eh(n,e);return i.error?i.error:Oh(i.queryComponent,n,e)}var jo=class{constructor(e){this.line=e,this.functionOrError=Eh(Al(null,null),e)}isValid(){return this.functionOrError.isValid()}get parseError(){return this.functionOrError.error}evaluate(e,t){if(!this.isValid())throw Error(`Error: Cannot evaluate an expression which is not valid: "${this.line}" gave error: "${this.parseError}"`);return Sh(this.functionOrError.queryComponent,Al(e,t||null))}evaluateOrCatch(e,t){return this.isValid()?Oh(this.functionOrError.queryComponent,Al(e,t),this.line):`Error: Cannot evaluate an expression which is not valid: "${this.line}" gave error: "${this.parseError}"`}};function Dh(r){if(r===null)return"null";let e=typeof r;return e==="object"?r.constructor.name:e}var Pl=class extends Ee{createFilterOrErrorMessage(e){let t=Ee.getMatch(this.filterRegExp(),e);if(t===null)return se.fromError(e,"Unable to parse line");let n=t[1],i=new jo(n);return i.isValid()?se.fromFilter(new vt(e,bF(i),new Se(e))):se.fromError(e,i.parseError)}fieldName(){return"function"}filterRegExp(){return new RegExp(`^filter by ${this.fieldNameSingularEscaped()} (.*)`,"i")}supportsSorting(){return!0}sorterRegExp(){return new RegExp(`^sort by ${this.fieldNameSingularEscaped()}( reverse)? (.*)`,"i")}createSorterFromLine(e){let t=Ee.getMatch(this.sorterRegExp(),e);if(t===null)return null;let n=!!t[1],i=t[2],s=new jo(i);if(!s.isValid())throw new Error(s.parseError);let a=(o,u,l)=>{try{let c=l.queryContext(),d=this.validateTaskSortKey(s.evaluate(o,c)),f=this.validateTaskSortKey(s.evaluate(u,c));return this.compareTaskSortKeys(d,f)}catch(c){throw c instanceof Error&&(c.message+=`: while evaluating instruction '${e}'`),c}};return new Wr(e,this.fieldNameSingular(),a,n)}validateTaskSortKey(e){function t(n){throw new Error(`"${n}" is not a valid sort key`)}return e===void 0&&t("undefined"),Number.isNaN(e)&&t("NaN (Not a Number)"),Array.isArray(e)&&t("array"),e}compareTaskSortKeys(e,t){let n=Dh(e),i=Dh(t),s=this.compareTaskSortKeysIfOptionalMoment(e,t,n,i);if(s!==void 0)return s;let a=this.compareTaskSortKeysIfEitherIsNull(e,t);if(a!==void 0)return a;if(n!==i)throw new Error(`Unable to compare two different sort key types '${n}' and '${i}' order`);if(n==="string")return e.localeCompare(t,void 0,{numeric:!0});if(n==="TasksDate")return dr(e.moment,t.moment);if(n==="boolean")return Number(t)-Number(e);let o=Number(e)-Number(t);if(isNaN(o))throw new Error(`Unable to determine sort order for sort key types '${n}' and '${i}'`);return o}compareTaskSortKeysIfOptionalMoment(e,t,n,i){let s=n==="Moment",a=i==="Moment";if(s&&a||s&&t===null||a&&e===null)return dr(e,t)}compareTaskSortKeysIfEitherIsNull(e,t){if(e===null&&t===null)return 0;if(e===null&&t!==null)return-1;if(e!==null&&t===null)return 1}supportsGrouping(){return!0}createGrouperFromLine(e){let t=Ee.getMatch(this.grouperRegExp(),e);if(t===null)return null;let n=!!t[1],i=t[2];return new ii(e,"function",vF(i),n)}grouperRegExp(){return new RegExp(`^group by ${this.fieldNameSingularEscaped()}( reverse)? (.*)`,"i")}grouper(){throw Error("grouper() function not valid for FunctionField. Use createGrouperFromLine() instead.")}};function bF(r){return(e,t)=>{let n=t.queryContext();return _F(r,e,n)}}function _F(r,e,t){let n=r.evaluate(e,t);if(typeof n=="boolean")return n;throw Error(`filtering function must return true or false. This returned "${n}".`)}function vF(r){return(e,t)=>{let n=t.queryContext();return wF(e,r,n)}}function wF(r,e,t){try{let n=Jw(r,e,t);return Array.isArray(n)?n.map(s=>s.toString()):n===null?[]:[n.toString()]}catch(n){let i=`Error: Failed calculating expression "${e}". The error message was: `;return n instanceof Error?[i+n.message]:[i+"Unknown error"]}}var Nl=class extends Ie{fieldName(){return"heading"}value(e){return e.precedingHeader?e.precedingHeader:""}supportsSorting(){return!0}supportsGrouping(){return!0}grouper(){return e=>e.precedingHeader===null||e.precedingHeader.length===0?["(No heading)"]:[e.precedingHeader]}};var js=class extends Ie{fieldName(){return"path"}value(e){return e.path}supportsSorting(){return!0}supportsGrouping(){return!0}grouper(){return e=>[Ie.escapeMarkdownCharacters(e.path.replace(".md",""))]}};var xh=class extends Ee{createFilterOrErrorMessage(e){var n;let t=Ee.getMatch(this.filterRegExp(),e);if(t!==null){let i=t[5],s=null;switch(i.toLowerCase()){case"lowest":s="5";break;case"low":s="4";break;case"none":s="3";break;case"medium":s="2";break;case"high":s="1";break;case"highest":s="0";break}if(s===null)return se.fromError(e,"do not understand priority");let a=e,o;switch((n=t[3])==null?void 0:n.toLowerCase()){case"above":o=u=>u.priority.localeCompare(s)<0;break;case"below":o=u=>u.priority.localeCompare(s)>0;break;case"not":o=u=>u.priority!==s;break;default:o=u=>u.priority===s,a=`${this.fieldName()} is ${i}`}return se.fromFilter(new vt(e,o,new Se(a)))}else return se.fromError(e,"do not understand query filter (priority)")}fieldName(){return"priority"}filterRegExp(){return xh.priorityRegexp}supportsSorting(){return!0}comparator(){return(e,t)=>e.priority.localeCompare(t.priority)}supportsGrouping(){return!0}grouper(){return e=>[e.priorityNameGroupText]}},Yi=xh;Yi.priorityRegexp=/^priority(\s+is)?(\s+(above|below|not))?(\s+(lowest|low|none|medium|high|highest))$/i;var Il=class extends at{fieldName(){return"scheduled"}date(e){return e.scheduledDate}filterResultIfFieldMissing(){return!1}};var Fl=class extends at{fieldName(){return"start"}fieldNameForFilterInstruction(){return"starts"}date(e){return e.startDate}filterResultIfFieldMissing(){return!0}};var Ll=class extends at{constructor(){let e=new jt;e.add("has happens date",t=>this.dates(t).some(n=>n!==null)),e.add("no happens date",t=>!this.dates(t).some(n=>n!==null)),super(e)}fieldName(){return"happens"}fieldNameForExplanation(){return"due, start or scheduled"}date(e){return this.earliestDate(e)}dates(e){return e.happensDates}earliestDate(e){return e.happens.moment}filterResultIfFieldMissing(){return!1}getFilter(e){return t=>this.dates(t).some(n=>e(n))}};var Ul=class extends vr{constructor(){super(),this._filters.add("is recurring",e=>e.recurrence!==null),this._filters.add("is not recurring",e=>e.recurrence===null)}fieldName(){return"recurring"}supportsSorting(){return!0}comparator(){return(e,t)=>e.recurrence!==null&&t.recurrence===null?-1:e.recurrence===null&&t.recurrence!==null?1:0}supportsGrouping(){return!0}grouper(){return e=>e.recurrence!==null?["Recurring"]:["Not Recurring"]}};var si=class extends vr{constructor(){super(),this._filters.add("done",e=>e.isDone),this._filters.add("not done",e=>!e.isDone)}fieldName(){return"status"}supportsSorting(){return!0}comparator(){return(e,t)=>{let n=si.oldStatusName(e),i=si.oldStatusName(t);return n<i?1:n>i?-1:0}}static oldStatusName(e){return e.isDone?"Done":"Todo"}supportsGrouping(){return!0}grouper(){return e=>[si.oldStatusName(e)]}};var Wl=class extends Ie{fieldNamePlural(){return this.fieldNameSingular()+"s"}fieldName(){return`${this.fieldNameSingular()}/${this.fieldNamePlural()}`}fieldPattern(){return`${this.fieldNameSingular()}|${this.fieldNamePlural()}`}filterOperatorPattern(){return`${super.filterOperatorPattern()}|include|do not include`}value(e){return this.values(e).join(", ")}getFilter(e,t){return n=>{let i=e.matchesAnyOf(this.values(n));return t?!i:i}}createGrouper(e){return new ii(this.grouperInstruction(e),this.fieldNamePlural(),this.grouper(),e)}grouperRegExp(){if(!this.supportsGrouping())throw Error(`grouperRegExp() unimplemented for ${this.fieldNameSingular()}`);return new RegExp(`^group by ${this.fieldNamePlural()}( reverse)?$`,"i")}grouperInstruction(e){let t=`group by ${this.fieldNamePlural()}`;return e&&(t+=" reverse"),t}};var Bi=class extends Wl{constructor(){super();this.filterInstructions=new jt,this.filterInstructions.add(`has ${this.fieldNameSingular()}`,t=>this.values(t).length>0),this.filterInstructions.add(`has ${this.fieldNamePlural()}`,t=>this.values(t).length>0),this.filterInstructions.add(`no ${this.fieldNameSingular()}`,t=>this.values(t).length===0),this.filterInstructions.add(`no ${this.fieldNamePlural()}`,t=>this.values(t).length===0)}createFilterOrErrorMessage(t){let n=this.filterInstructions.createFilterOrErrorMessage(t);return n.isValid()?n:super.createFilterOrErrorMessage(t)}canCreateFilterForLine(t){return this.filterInstructions.canCreateFilterForLine(t)?!0:super.canCreateFilterForLine(t)}fieldNameSingular(){return"tag"}values(t){return t.tags}supportsSorting(){return!0}createSorterFromLine(t){let n=t.match(this.sorterRegExp());if(n===null)return null;let i=!!n[1],s=isNaN(+n[2])?1:+n[2],a=Bi.makeCompareByTagComparator(s);return new Wr(t,this.fieldNameSingular(),a,i)}sorterRegExp(){return/^sort by tag( reverse)?[\s]*(\d+)?/i}comparator(){return Bi.makeCompareByTagComparator(1)}static makeCompareByTagComparator(t){return(n,i)=>{if(n.tags.length===0&&i.tags.length===0)return 0;if(n.tags.length===0)return 1;if(i.tags.length===0)return-1;let s=t-1;if(n.tags.length<t&&i.tags.length>=t)return 1;if(i.tags.length<t&&n.tags.length>=t)return-1;if(n.tags.length<t&&i.tags.length<t)return 0;let a=n.tags[s],o=i.tags[s];return a.localeCompare(o,void 0,{numeric:!0})}}supportsGrouping(){return!0}grouper(){return t=>t.tags.length==0?["(No tags)"]:t.tags}};var pk=ma(fk());function ai(r){return new RegExp("["+Zn(r)+"]").source}var Uh=[["(",")"],["[","]"],["{","}"],['"','"']],Un=class{constructor(e,t,n){this.openFilterChars=e,this.closeFilterChars=t,this.openAndCloseFilterChars=n,this.openFilter=ai(this.openFilterChars),this.closeFilter=ai(this.closeFilterChars)}static allSupportedDelimiters(){let e="",t="",n="";for(let[i,s]of Uh)e+=i,t+=s,n+=Un.openAndClosing(i,s);return new Un(e,t,n)}static fromInstructionLine(e){let t=e.trim(),i=/^[A-Z ]*\s*(.*)/.exec(t);if(i){let a=i[1],o=a[0],u=a.slice(-1);for(let[l,c]of Uh)if(o===l&&u===c){let d=this.openAndClosing(l,c);return new Un(l,c,d)}}let s="All filters in a Boolean instruction must be inside one of these pairs of delimiter characters: "+Uh.map(([a,o])=>a+"..."+o).join(" or ")+". Combinations of those delimiters are no longer supported.";throw new Error(s)}static openAndClosing(e,t){let n=e;return t!=e&&(n+=t),n}};var oi=class{static preprocessExpression(e,t){let n=oi.splitLine(e,t);return oi.getFiltersAndSimplifiedLine(n,t)}static splitLine(e,t){let n=new RegExp("("+t.closeFilter+"\\s*(?:AND|OR|AND +NOT|OR +NOT|XOR)\\s*"+t.openFilter+")"),i=e.split(n),s=new RegExp("(NOT\\s*"+t.openFilter+")"),a=i.flatMap(l=>l.split(s)).filter(l=>l!==""),o=new RegExp("(^"+ai(t.openFilterChars+" ")+"*)"),u=new RegExp("("+ai(t.closeFilterChars+" ")+"*$)");return a.flatMap(l=>l.split(o)).flatMap(l=>l.split(u)).filter(l=>l!=="")}static getFiltersAndSimplifiedLine(e,t){let n="",i=1,s={};e.forEach(l=>{if(!oi.isAFilter(l,t))n+=`${l}`;else{let c=`f${i}`;s[c]=l,n+=c,i++}});let a=new RegExp(`(${t.closeFilter})([A-Z])`,"g");n=n.replace(a,"$1 $2");let o=new RegExp(`([A-Z])(${t.openFilter})`,"g");n=n.replace(o,"$1 $2");let u=t.openFilterChars;if(u!='"'&&u!="("){let l=new RegExp(ai(u),"g");n=n.replace(l,"(");let c=t.closeFilterChars,d=new RegExp(ai(c),"g");n=n.replace(d,")")}return{simplifiedLine:n,filters:s}}static isAFilter(e,t){let n=new RegExp("^"+ai(" "+t.openAndCloseFilterChars)+"+$"),i=new RegExp("^ *"+t.closeFilter+" *(AND|OR|XOR) *"+t.openFilter+" *$"),s=new RegExp("^(AND|OR|XOR|NOT) *"+t.openFilter+"$"),a=new RegExp("^"+t.closeFilter+" *(AND|OR|XOR)$");return![n,i,s,a,/^(AND|OR|XOR|NOT)$/].some(u=>RegExp(u).exec(e))}};var Yl=class extends Ee{constructor(){super();this.supportedOperators=["AND","OR","XOR","NOT"];this.subFields={};let t=Un.allSupportedDelimiters();this.basicBooleanRegexp=new RegExp("(.*(AND|OR|XOR|NOT)\\s*"+t.openFilter+".*|"+t.openFilter+".+"+t.closeFilter+")","g")}filterRegExp(){return this.basicBooleanRegexp}createFilterOrErrorMessage(t){return this.parseLine(t)}fieldName(){return"boolean query"}parseLine(t){if(t.length===0)return se.fromError(t,"empty line");let n;try{n=Un.fromInstructionLine(t)}catch(o){let u=o instanceof Error?o.message:"unknown error type";return se.fromError(t,this.helpMessageFromSimpleError(t,u))}let i=oi.preprocessExpression(t,n),s=i.simplifiedLine,a=i.filters;try{let o=(0,pk.parse)(s);for(let c of o)if(c.name==="IDENTIFIER"&&c.value){let d=c.value.trim(),f=a[d];if(c.value=f,!(f in this.subFields)){let m=Yo(f);if(m===null)return this.helpMessage(t,`couldn't parse sub-expression '${f}'`,i);if(m.error)return this.helpMessage(t,`couldn't parse sub-expression '${f}': ${m.error}`,i);m.filter&&(this.subFields[f]=m.filter)}}else if(c.name==="OPERATOR"){if(c.value==null)return this.helpMessage(t,"empty operator in boolean query",i);if(!this.supportedOperators.includes(c.value))return this.helpMessage(t,`unknown boolean operator '${c.value}'`,i)}let u=(c,d)=>this.filterTaskWithParsedQuery(c,o,d),l=this.constructExplanation(o);return se.fromFilter(new vt(t,u,l))}catch(o){let u=o instanceof Error?o.message:"unknown error type";return this.helpMessage(t,`malformed boolean query -- ${u} (check the documentation for guidelines)`,i)}}filterTaskWithParsedQuery(t,n,i){let s=u=>u==="true",a=u=>u?"true":"false",o=[];for(let u of n)if(u.name==="IDENTIFIER"){if(u.value==null)throw Error("null token value");let c=this.subFields[u.value.trim()].filterFunction(t,i);o.push(a(c))}else if(u.name==="OPERATOR")if(u.value==="NOT"){let l=s(o.pop());o.push(a(!l))}else if(u.value==="OR"){let l=s(o.pop()),c=s(o.pop());o.push(a(l||c))}else if(u.value==="AND"){let l=s(o.pop()),c=s(o.pop());o.push(a(l&&c))}else if(u.value==="XOR"){let l=s(o.pop()),c=s(o.pop());o.push(a(l&&!c||!l&&c))}else throw Error("Unsupported operator: "+u.value);else throw Error("Unsupported token type: "+u);return s(o[0])}constructExplanation(t){let n=[];for(let i of t)if(i.name==="IDENTIFIER")this.explainExpression(i,n);else if(i.name==="OPERATOR")this.explainOperator(i,n);else throw Error("Unsupported token type: "+i.name);return n[0]}explainExpression(t,n){if(t.value==null)throw Error("null token value");let i=this.subFields[t.value.trim()],s=this.simulateExplainFilter(i);n.push(s)}simulateExplainFilter(t){return t.simulateExplainFilter()}explainOperator(t,n){if(t.value==="NOT"){let i=n.pop();n.push(Se.booleanNot([i]))}else if(t.value==="OR"){let i=n.pop(),s=n.pop();n.push(Se.booleanOr([s,i]))}else if(t.value==="AND"){let i=n.pop(),s=n.pop();n.push(Se.booleanAnd([s,i]))}else if(t.value==="XOR"){let i=n.pop(),s=n.pop();n.push(Se.booleanXor([s,i]))}else throw Error("Unsupported operator: "+t.value)}helpMessage(t,n,i){let s=i.filters,a=this.stringifySubExpressionsForErrorMessage(s),u=`${this.helpMessageFromSimpleError(t,n)}

The instruction was converted to the following simplified line:
    ${i.simplifiedLine}

Where the sub-expressions in the simplified line are:
${a}

For help, see:
    https://publish.obsidian.md/tasks/Queries/Combining+Filters
`;return se.fromError(t,u)}stringifySubExpressionsForErrorMessage(t){return Object.entries(t).map(([n,i])=>`    '${n}': '${i}'
        => ${this.stringifySubExpressionStatus(i)}`).join(`
`)}stringifySubExpressionStatus(t){let n=Yo(t);return n?n.error?`ERROR:
           ${n.error.split(`
`).map(s=>s.trim()).join(`
           `)}`:"OK":`ERROR:
           do not understand query`}helpMessageFromSimpleError(t,n){return`Could not interpret the following instruction as a Boolean combination:
    ${t}

The error message is:
    ${n}`}};var Bl=class extends Ie{fieldName(){return"filename"}value(e){let t=e.filename;return t===null?"":t+".md"}supportsSorting(){return!0}supportsGrouping(){return!0}grouper(){return e=>{let t=e.filename;return t===null?["Unknown Location"]:["[["+t+"]]"]}}};var Qs=class extends Ee{canCreateFilterForLine(e){return!1}createFilterOrErrorMessage(e){return se.fromError(e,"Filtering by urgency is not yet supported")}fieldName(){return"urgency"}filterRegExp(){throw Error(`filterRegExp() unimplemented for ${this.fieldName()}`)}supportsSorting(){return!0}comparator(){return(e,t)=>t.urgency-e.urgency}supportsGrouping(){return!0}grouper(){return e=>[`${e.urgency.toFixed(2)}`]}createGrouper(e){return super.createGrouper(!e)}grouperInstruction(e){return super.grouperInstruction(!e)}};var Hl=class extends Ie{constructor(){super()}fieldName(){return"status.name"}value(e){return e.status.name}supportsSorting(){return!0}supportsGrouping(){return!0}};var cn=class extends Ee{canCreateFilterForLine(e){let t=new RegExp(`^(?:${this.fieldNameSingularEscaped()})`,"i");return Ee.lineMatchesFilter(t,e)}createFilterOrErrorMessage(e){let t=Ee.getMatch(this.filterRegExp(),e);if(t===null)return this.helpMessage(e);let n=t[1].toLowerCase(),i=t[2],s=xt[i.toUpperCase()];if(!s)return this.helpMessage(e);let a;switch(n){case"is":a=o=>o.status.type===s;break;case"is not":a=o=>o.status.type!==s;break;default:return this.helpMessage(e)}return se.fromFilter(new vt(e,a,new Se(e)))}filterRegExp(){return new RegExp(`^(?:${this.fieldNameSingularEscaped()}) (is|is not) ([^ ]+)$`,"i")}helpMessage(e){let t=Object.values(xt).filter(i=>i!=="EMPTY").join(" "),n=`Invalid ${this.fieldNameSingular()} instruction: '${e}'.
    Allowed options: 'is' and 'is not' (without quotes).
    Allowed values:  ${t}
                     Note: values are case-insensitive,
                           so 'in_progress' works too, for example.
    Example:         ${this.fieldNameSingular()} is not NON_TASK`;return se.fromError(e,n)}fieldName(){return"status.type"}value(e){return e.status.type}supportsSorting(){return!0}comparator(){return(e,t)=>{let n=cn.groupName(e),i=cn.groupName(t);return n.localeCompare(i,void 0,{numeric:!0})}}supportsGrouping(){return!0}grouper(){return e=>[cn.groupName(e)]}static groupName(e){return e.status.typeGroupText}};var Vl=class extends Ie{fieldName(){return"recurrence"}value(e){return e.recurrence!==null?e.recurrence.toText():""}supportsGrouping(){return!0}grouper(){return e=>e.recurrence!==null?[e.recurrence.toText()]:["None"]}};var zl=class extends Ie{fieldName(){return"folder"}value(e){return e.file.folder}supportsGrouping(){return!0}grouper(){return e=>[Ie.escapeMarkdownCharacters(this.value(e))]}};var Kl=class extends Ie{fieldName(){return"root"}value(e){return e.file.root}supportsGrouping(){return!0}grouper(){return e=>[Ie.escapeMarkdownCharacters(this.value(e))]}};var Ql=class extends Ie{fieldName(){return"backlink"}value(e){let t=e.getLinkText({isFilenameUnique:!0});return t===null?"Unknown Location":t}createFilterOrErrorMessage(e){return se.fromError(e,"backlink field does not support filtering")}canCreateFilterForLine(e){return!1}supportsGrouping(){return!0}grouper(){return e=>{let t=e.filename;if(t===null)return["Unknown Location"];let n=e.precedingHeader;return n===null?["[["+t+"]]"]:[`[[${t}#${n}|${t} > ${n}]]`]}}};var Xl=class extends at{fieldName(){return"cancelled"}date(e){return e.cancelledDate}filterResultIfFieldMissing(){return!1}};var Zl=class extends vr{constructor(){super(),this._filters.add("is blocking",(e,t)=>e.isBlocking(t.allTasks)),this._filters.add("is not blocking",(e,t)=>!e.isBlocking(t.allTasks)),this._filters.add("is blocked",(e,t)=>e.isBlocked(t.allTasks)),this._filters.add("is not blocked",(e,t)=>!e.isBlocked(t.allTasks))}fieldName(){return"blocking"}};var Jl=class extends Ie{constructor(){super();this.filterInstructions=new jt;this.filterInstructions.add("has id",t=>t.id.length>0),this.filterInstructions.add("no id",t=>t.id.length===0)}canCreateFilterForLine(t){return this.filterInstructions.canCreateFilterForLine(t)?!0:super.canCreateFilterForLine(t)}createFilterOrErrorMessage(t){let n=this.filterInstructions.createFilterOrErrorMessage(t);return n.isValid()?n:super.createFilterOrErrorMessage(t)}fieldName(){return"id"}value(t){return t.id}supportsSorting(){return!0}supportsGrouping(){return!0}};var ec=class extends Ee{constructor(){super();this.filterInstructions=new jt;this.filterInstructions.add("has depends on",t=>t.dependsOn.length>0),this.filterInstructions.add("no depends on",t=>t.dependsOn.length===0)}canCreateFilterForLine(t){return this.filterInstructions.canCreateFilterForLine(t)?!0:super.canCreateFilterForLine(t)}createFilterOrErrorMessage(t){let n=this.filterInstructions.createFilterOrErrorMessage(t);return n.isValid()?n:se.fromError(t,"Unknown instruction")}fieldName(){return"blocked by"}filterRegExp(){return null}};var tc=class extends vr{fieldName(){return"random"}supportsSorting(){return!0}comparator(){return(e,t)=>this.sortKey(e)-this.sortKey(t)}sortKey(e){let t=i=>{let s=0,a=9;for(;s<i.length;)a=Math.imul(a^i.charCodeAt(s++),387420489);return a^a>>>9},n=window.moment().format("Y-MM-DD");return t(n+" "+e.description)}};var Wh=[()=>new Hl,()=>new cn,()=>new si,()=>new Ul,()=>new Yi,()=>new Ll,()=>new Xl,()=>new Rl,()=>new Fl,()=>new Il,()=>new $s,()=>new Ml,()=>new js,()=>new zl,()=>new Kl,()=>new Ql,()=>new Gi,()=>new Bi,()=>new Nl,()=>new Cl,()=>new Bl,()=>new Qs,()=>new Vl,()=>new Pl,()=>new Jl,()=>new ec,()=>new Zl,()=>new tc,()=>new Yl];function Yo(r){for(let e of Wh){let t=e();if(t.canCreateFilterForLine(r))return t.createFilterOrErrorMessage(r)}return null}function mk(r){let e=/^sort by /i;if(r.match(e)===null)return null;for(let t of Wh){let i=t().createSorterFromLine(r);if(i)return i}return null}function hk(r){let e=/^group by /i;if(r.match(e)===null)return null;for(let t of Wh){let i=t().createGrouperFromLine(r);if(i)return i}return null}var rc=class{constructor(e,t,n){this.nestingLevel=e,this.displayName=t,this.property=n}};var nc=class{constructor(e,t){this.lastHeadingAtLevel=new Array;this.groupers=t;let i=e.keys().next().value.length;for(let s=0;s<i;s++)this.lastHeadingAtLevel.push("")}getHeadingsForTaskGroup(e){let t=new Array;for(let n=0;n<e.length;n++){let i=e[n];if(i!=this.lastHeadingAtLevel[n]){t.push(new rc(n,i,this.groupers[n].property));for(let s=n;s<e.length;s++)this.lastHeadingAtLevel[s]="";this.lastHeadingAtLevel[n]=i}}return t}};var ic=class{constructor(e){this.children=new Map;this.values=[];this.values=e}generateAllPaths(e=[]){let t=new Map;if(this.children.size==0)return t.set([...e],this.values),t;for(let[n,i]of this.children)e.push(n),i.generateAllPaths(e).forEach((a,o)=>t.set(o,a)),e.pop();return t}};var sc=class extends ic{},ac=class{constructor(e,t,n){this.root=new sc(t),this.buildGroupingTree(e,n)}buildGroupingTree(e,t){let n=[this.root];for(let i of e){let s=[];for(let a of n)for(let o of a.values){let u=i.grouper(o,t);u.length===0&&u.push("");for(let l of u){let c=a.children.get(l);c===void 0&&(c=new sc([]),a.children.set(l,c),s.push(c)),c.values.push(o)}}n=s}}generateTaskTreeStorage(){return this.root.generateAllPaths()}};var oc=class{constructor(e,t){this.groups=e,this.groupHeadings=[],this.tasks=t}setGroupHeadings(e){for(let t of e)this.groupHeadings.push(t)}applyTaskLimit(e){this.tasks=this.tasks.slice(0,e)}tasksAsStringOfLines(){let e="";for(let t of this.tasks)e+=t.toFileLineString()+`
`;return e}toString(){let e=`
`;e+=`Group names: [${this.groups}]
`;for(let t of this.groupHeadings)e+=`${"#".repeat(4+t.nestingLevel)} [${t.property}] ${t.displayName}
`;return e+=this.tasksAsStringOfLines(),e}};var Xs=class{constructor(e,t,n){this._groups=new Array;this._totalTaskCount=0;this._totalTaskCount=t.length,this._groupers=e;let s=new ac(e,t,n).generateTaskTreeStorage();this.addTaskGroups(s),this.sortTaskGroups(),this.setGroupsHeadings(s)}get groupers(){return this._groupers}get groups(){return this._groups}totalTasksCount(){return this._totalTaskCount}toString(){let e="";e+=`Groupers (if any):
`;for(let n of this._groupers){let i=n.reverse?" reverse":"";e+=`- ${n.property}${i}
`}for(let n of this.groups)e+=n.toString(),e+=`
---
`;return e+=`
${this.totalTasksCount()} tasks
`,e}addTaskGroups(e){for(let[t,n]of e){let i=new oc(t,n);this.addTaskGroup(i)}}addTaskGroup(e){this._groups.push(e)}sortTaskGroups(){let e=(t,n)=>{let i=t.groups,s=n.groups;for(let a=0;a<i.length;a++){let o=this._groupers[a],u=i[a].localeCompare(s[a],void 0,{numeric:!0});if(u!==0)return o.reverse?-u:u}return 0};this._groups.sort(e)}setGroupsHeadings(e){let t=new nc(e,this._groupers);for(let n of this._groups)n.setGroupHeadings(t.getHeadingsForTaskGroup(n.groups))}applyTaskLimit(e){this._groupers.length!==0&&(this._groups.forEach(t=>{t.applyTaskLimit(e)}),this.recalculateTotalTaskCount())}recalculateTotalTaskCount(){let e=[];this._groups.forEach(n=>{e=[...e,...n.tasks]});let t=[...new Set(e)];this._totalTaskCount=t.length}};var ui=class{constructor(e,t){this.tasksFile=e,this.allTasks=[...t]}static fromAllTasks(e){return new ui(void 0,e)}get queryPath(){var e,t;return(t=(e=this.tasksFile)==null?void 0:e.path)!=null?t:void 0}queryContext(){return this.tasksFile?kh(this.tasksFile,this.allTasks):void 0}};function gk(r){return`task${r!==1?"s":""}`}var zi=class{constructor(e,t){this.totalTasksCountBeforeLimit=0;this._searchErrorMessage=void 0;this.taskGroups=e,this.totalTasksCountBeforeLimit=t}get searchErrorMessage(){return this._searchErrorMessage}set searchErrorMessage(e){this._searchErrorMessage=e}get totalTasksCount(){return this.taskGroups.totalTasksCount()}totalTasksCountDisplayText(){let e=this.totalTasksCount,t=this.totalTasksCountBeforeLimit;return e===t?`${e} ${gk(e)}`:`${e} of ${t} ${gk(t)}`}get groups(){return this.taskGroups.groups}static fromError(e){let t=new zi(new Xs([],[],ui.fromAllTasks([])),0);return t._searchErrorMessage=e,t}};function yk(r){return r.endsWith("\\")}function Tk(r){return r.endsWith("\\\\")}function QF(r){return r.replace(/^[ \t]*/,"")}function XF(r){return r.replace(/[ \t]*\\$/,"")}function ZF(r,e){let t=r;return e&&(t=QF(r)),Tk(t)?t=t.slice(0,-1):yk(r)&&(t=XF(t)),t}function bk(r){let e=[],t=!1,n="",i="";for(let s of r.split(`
`)){let a=ZF(s,t);t?(n+=`
`+s,i+=" "+a):(n=s,i=a),Tk(s)?t=!1:t=yk(s),t||(i.trim()!==""&&e.push(new Ln(n,i)),n="",i="")}return e}var Zs=class{static by(e,t,n){let i=this.defaultSorters().map(a=>a.comparator),s=[];for(let a of e)s.push(a.comparator);return t.sort(Zs.makeCompositeComparator([...s,...i],n))}static defaultSorters(){return[new cn().createNormalSorter(),new Qs().createNormalSorter(),new $s().createNormalSorter(),new Yi().createNormalSorter(),new js().createNormalSorter()]}static makeCompositeComparator(e,t){return(n,i)=>{for(let s of e){let a=s(n,i,t);if(a!==0)return a}return 0}}};var $r=class{constructor(e,t=void 0){this._limit=void 0;this._taskGroupLimit=void 0;this._taskLayoutOptions=new en;this._queryLayoutOptions=new ri;this._filters=[];this._error=void 0;this._sorting=[];this._grouping=[];this._ignoreGlobalQuery=!1;this.hideOptionsRegexp=/^(hide|show) (task count|backlink|priority|cancelled date|created date|start date|scheduled date|done date|due date|recurrence rule|edit button|postpone button|urgency|tags|depends on|id|on completion|tree)/i;this.shortModeRegexp=/^short/i;this.fullModeRegexp=/^full/i;this.explainQueryRegexp=/^explain/i;this.ignoreGlobalQueryRegexp=/^ignore global query/i;this.logger=Rt.getLogger("tasks.Query");this._queryId="";this.limitRegexp=/^limit (groups )?(to )?(\d+)( tasks?)?/i;this.commentRegexp=/^#.*/;this._queryId=this.generateQueryId(10),this.source=e,this.tasksFile=t,this.debug(`Creating query: ${this.formatQueryForLogging()}`),bk(e).forEach(n=>{let i=this.expandPlaceholders(n,t);if(this.error===void 0)try{this.parseLine(i,n)}catch(s){let a;s instanceof Error?a=s.message:a="Unknown error",this.setError(a,n);return}})}get filePath(){var e,t;return(t=(e=this.tasksFile)==null?void 0:e.path)!=null?t:void 0}get queryId(){return this._queryId}parseLine(e,t){switch(!0){case this.shortModeRegexp.test(e):this._queryLayoutOptions.shortMode=!0;break;case this.fullModeRegexp.test(e):this._queryLayoutOptions.shortMode=!1;break;case this.explainQueryRegexp.test(e):this._queryLayoutOptions.explainQuery=!0;break;case this.ignoreGlobalQueryRegexp.test(e):this._ignoreGlobalQuery=!0;break;case this.limitRegexp.test(e):this.parseLimit(e);break;case this.parseSortBy(e):break;case this.parseGroupBy(e):break;case this.hideOptionsRegexp.test(e):this.parseHideOptions(e);break;case this.commentRegexp.test(e):break;case this.parseFilter(e,t):break;default:this.setError("do not understand query",t)}}formatQueryForLogging(){return`[${this.source.split(`
`).join(" ; ")}]`}expandPlaceholders(e,t){let n=e.anyContinuationLinesRemoved;if(n.includes("{{")&&n.includes("}}")&&this.tasksFile===void 0)return this._error=`The query looks like it contains a placeholder, with "{{" and "}}"
but no file path has been supplied, so cannot expand placeholder values.
The query is:
${n}`,n;let i=n;if(t){let s=Zw(t);try{i=Xw(n,s)}catch(a){return a instanceof Error?this._error=a.message:this._error="Internal error. expandPlaceholders() threw something other than Error.",n}}return e.recordExpandedPlaceholders(i),i}append(e){return this.source===""?e:e.source===""?this:new $r(`${this.source}
${e.source}`,this.tasksFile)}explainQuery(){return new Us().explainQuery(this)}get limit(){return this._limit}get taskGroupLimit(){return this._taskGroupLimit}get taskLayoutOptions(){return this._taskLayoutOptions}get queryLayoutOptions(){return this._queryLayoutOptions}get filters(){return this._filters}addFilter(e){this._filters.push(e)}get sorting(){return this._sorting}get grouping(){return this._grouping}get error(){return this._error}setError(e,t){this._error=$r.generateErrorMessage(t,e)}static generateErrorMessage(e,t){return e.allLinesIdentical()?`${t}
Problem line: "${e.rawInstruction}"`:`${t}
Problem statement:
${e.explainStatement("    ")}
`}get ignoreGlobalQuery(){return this._ignoreGlobalQuery}applyQueryToTasks(e){this.debug(`Executing query: ${this.formatQueryForLogging()}`);let t=new ui(this.tasksFile,e),n;try{this.filters.forEach(u=>{n=u.statement,e=e.filter(l=>u.filterFunction(l,t))}),n=void 0;let{debugSettings:i}=Q(),s=i.ignoreSortInstructions?e:Zs.by(this.sorting,e,t),a=s.slice(0,this.limit),o=new Xs(this.grouping,a,t);return this._taskGroupLimit!==void 0&&o.applyTaskLimit(this._taskGroupLimit),new zi(o,s.length)}catch(i){let a=ji("Search failed",i);return n&&(a=$r.generateErrorMessage(n,a)),zi.fromError(a)}}parseHideOptions(e){let t=e.match(this.hideOptionsRegexp);if(t!==null){let n=t[1].toLowerCase()==="hide";switch(t[2].toLowerCase()){case"tree":this._queryLayoutOptions.hideTree=n;break;case"task count":this._queryLayoutOptions.hideTaskCount=n;break;case"backlink":this._queryLayoutOptions.hideBacklinks=n;break;case"postpone button":this._queryLayoutOptions.hidePostponeButton=n;break;case"priority":this._taskLayoutOptions.setVisibility("priority",!n);break;case"cancelled date":this._taskLayoutOptions.setVisibility("cancelledDate",!n);break;case"created date":this._taskLayoutOptions.setVisibility("createdDate",!n);break;case"start date":this._taskLayoutOptions.setVisibility("startDate",!n);break;case"scheduled date":this._taskLayoutOptions.setVisibility("scheduledDate",!n);break;case"due date":this._taskLayoutOptions.setVisibility("dueDate",!n);break;case"done date":this._taskLayoutOptions.setVisibility("doneDate",!n);break;case"recurrence rule":this._taskLayoutOptions.setVisibility("recurrenceRule",!n);break;case"edit button":this._queryLayoutOptions.hideEditButton=n;break;case"urgency":this._queryLayoutOptions.hideUrgency=n;break;case"tags":this._taskLayoutOptions.setTagsVisibility(!n);break;case"id":this._taskLayoutOptions.setVisibility("id",!n);break;case"depends on":this._taskLayoutOptions.setVisibility("dependsOn",!n);break;case"on completion":this._taskLayoutOptions.setVisibility("onCompletion",!n);break;default:this.setError("do not understand hide/show option",new Ln(e,e))}}}parseFilter(e,t){var i;let n=Yo(e);return n!=null?(n.filter?(n.filter.setStatement(t),this._filters.push(n.filter)):this.setError((i=n.error)!=null?i:"Unknown error",t),!0):!1}parseLimit(e){let t=e.match(this.limitRegexp);if(t===null){this.setError("do not understand query limit",new Ln(e,e));return}let n=Number.parseInt(t[3],10);t[1]!==void 0?this._taskGroupLimit=n:this._limit=n}parseSortBy(e){let t=mk(e);return t?(this._sorting.push(t),!0):!1}parseGroupBy(e){let t=hk(e);return t?(this._grouping.push(t),!0):!1}generateQueryId(e){let t="AaBbCcDdEeFfGgHhIiJjKkLlMmNnOoPpQqRrSsTtUuVvWwXxYyZz1234567890";return Array.from({length:e},()=>t[Math.floor(Math.random()*t.length)]).join("")}debug(e,t){this.logger.debugWithId(this._queryId,`"${this.filePath}": ${e}`,t)}};var li=class{constructor(e=li.empty){this._source=e}static getInstance(){return li.instance||(li.instance=new li),li.instance}set(e){this._source=e}query(e=void 0){return new $r(this._source,e)}hasInstructions(){return this._source.trim()!==li.empty}},tr=li;tr.empty="";var uc=class{constructor({obsidianEvents:e}){this.logger=Rt.getLogger("tasks.Events");this.obsidianEvents=e}onCacheUpdate(e){return this.logger.debug("TasksEvents.onCacheUpdate()"),this.obsidianEvents.on("obsidian-tasks-plugin:cache-update",e)}triggerCacheUpdate(e){this.logger.debug("TasksEvents.triggerCacheUpdate()"),this.obsidianEvents.trigger("obsidian-tasks-plugin:cache-update",e)}onRequestCacheUpdate(e){return this.logger.debug("TasksEvents.onRequestCacheUpdate()"),this.obsidianEvents.on("obsidian-tasks-plugin:request-cache-update",e)}triggerRequestCacheUpdate(e){this.logger.debug("TasksEvents.triggerRequestCacheUpdate()"),this.obsidianEvents.trigger("obsidian-tasks-plugin:request-cache-update",e)}off(e){this.logger.debug("TasksEvents.off()"),this.obsidianEvents.offref(e)}};var Fk=require("obsidian");var Ik=require("obsidian");var qh=class{constructor(e){this.newStatus=e}apply(e){return this.isCheckedForTask(e)?[e]:e.handleNewStatusWithRecurrenceInUsersOrder(this.newStatus)}instructionDisplayName(){return`Change status to: [${this.newStatus.symbol}] ${this.newStatus.name}`}isCheckedForTask(e){return this.newStatus.symbol===e.status.symbol}};function _k(r){let e=[],t=new Le().coreStatuses.map(n=>n.symbol);for(let n of[!0,!1])for(let i of r.registeredStatuses)t.includes(i.symbol)===n&&e.push(new qh(i));return e}var vk=require("obsidian");var $h="---",Js=class{apply(e){throw new Error("MenuDividerInstruction.apply(): Method not implemented.")}instructionDisplayName(){return $h}isCheckedForTask(e){return!1}};function Wn(r,e){return A(this,null,function*(){yield br({originalTask:r,newTasks:e})})}var ci=class extends vk.Menu{constructor(t){super();this.taskSaver=t}addItemsForInstructions(t,n){for(let i of t)this.addItemForInstruction(n,i)}addItemForInstruction(t,n){n.instructionDisplayName()===$h?this.addSeparator():this.addItem(i=>this.getMenuItemCallback(t,i,n))}getMenuItemCallback(t,n,i){n.setTitle(i.instructionDisplayName()).setChecked(i.isCheckedForTask(t)).onClick(()=>A(this,null,function*(){let s=i.apply(t);(s.length!==1||!Object.is(s[0],t))&&(yield this.taskSaver(t,s))}))}};var lc=class extends ci{constructor(e,t,n=Wn){super(n);let i=_k(e);this.addItemsForInstructions(i,t)}};function wk(r){for(let t of ee.allDateFields()){let n=r[t];if(n&&!n.isValid())return!1}let e=r.happensDates.some(t=>!!(t!=null&&t.isValid()));return!r.isDone&&e}function ea(r){return r.dueDate?"dueDate":r.scheduledDate?"scheduledDate":r.startDate?"startDate":null}function jh(r,e,t,n){let i=r[e];return Sk(i,r,e,t,n)}function kk(r,e,t,n){let i=window.moment();return Sk(i,r,e,t,n)}function Ek(r,e,t,n){return Ok(r,e,null)}function Sk(r,e,t,n,i){let s=new mt(r).postpone(n,i);return Ok(e,t,s)}function Ok(r,e,t){let n=_t.removeInferredStatusIfNeeded(r,[new ee(de(K({},r),{[e]:t}))])[0];return{postponedDate:t,postponedTask:n}}function Dk(r,e){if(r){let t=r==null?void 0:r.format("DD MMM YYYY");return`Task's ${e} changed to ${t}`}else return`Task's ${e} removed`}function xk(r,e,t){return`\u2139\uFE0F ${Gh(r,e,t)} (right-click for more options)`}function Gh(r,e,t){let n=ea(r),i=r[n];return ta(n,i,e,t)}function Rk(r,e,t){let n=ea(r),i=window.moment().startOf("day");return ta(n,i,e,t)}function Mk(r,e,t){let n=ea(r);return Yh(n,r)}function Yh(r,e){return r==="scheduledDate"&&e.scheduledDateIsInferred?"Cannot remove inferred scheduled date":`Remove ${cc(r)}`}function JF(r){return Rs(r.replace("Date",""))}function cc(r){return r.replace("Date"," date")}function ta(r,e,t,n){let s=new mt(e).postpone(n,t).format("ddd Do MMM"),a=t!=1?t:"a";if(e.isSameOrBefore(window.moment(),"day")){let u=JF(r);return(t>=0?`${u} in ${a} ${n}, on ${s}`:`${u} ${-a} ${n} ago, on ${s}`).replace(" 1 day ago"," yesterday").replace(" in 0 days"," today").replace("in a day","tomorrow")}let o=cc(r);return t>=0?`Postpone ${o} by ${a} ${n}, to ${s}`:`Backdate ${o} by ${-a} ${n}, to ${s}`}function Ck(r){return["startDate","scheduledDate","dueDate"].includes(r)}var Bo=class{constructor(e,t,n){this.newDate=t,this.dateFieldToEdit=e,this.displayName=n!=null?n:`Set Date: ${this.newDate.toDateString()}`}apply(e){return this.isCheckedForTask(e)?[e]:[new ee(de(K({},e),{[this.dateFieldToEdit]:window.moment(this.newDate)}))]}instructionDisplayName(){return this.displayName}isCheckedForTask(e){var t;return((t=e[this.dateFieldToEdit])==null?void 0:t.isSame(window.moment(this.newDate)))||!1}},jr=class extends Bo{constructor(e,t,n,i){var u;let s=(u=t[e])!=null?u:window.moment(),a=ta(e,s,n,i),o=new mt(window.moment(s)).postpone(i,n).toDate();super(e,o,a)}},Bh=class{constructor(e,t){this.dateFieldToEdit=e,this.displayName=Yh(e,t)}apply(e){let t=this.dateFieldToEdit==="scheduledDate"&&e.scheduledDateIsInferred;return e[this.dateFieldToEdit]===null||t?[e]:[new ee(de(K({},e),{[this.dateFieldToEdit]:null}))]}instructionDisplayName(){return this.displayName}isCheckedForTask(e){return!1}};function Ak(r,e){return Nk(e,r,1)}function Pk(r,e){return Nk(e,r,-1)}function Nk(r,e,t){let n=window.moment().startOf("day"),i=n.toDate(),s=new mt(n.clone());return[new Bo(e,i,ta(e,n,0,"days")),new Bo(e,s.postpone("day",t).toDate(),ta(e,n,t,"day")),new Js,new jr(e,r,t*2,"days"),new jr(e,r,t*3,"days"),new jr(e,r,t*4,"days"),new jr(e,r,t*5,"days"),new jr(e,r,t*6,"days"),new Js,new jr(e,r,t,"week"),new jr(e,r,t*2,"weeks"),new jr(e,r,t*3,"weeks"),new jr(e,r,t,"month"),new Js,new Bh(e,r)]}var dc=class extends ci{constructor(e,t,n=Wn){super(n);let i=Ck(e)?Ak(e,t):Pk(e,t);this.addItemsForInstructions(i,t)}};var fc=class{constructor(){this.data=eL}addDataAttribute(e,t,n){this.data[n].addDataAttribute(e,t,n)}addClassName(e,t){let n=this.data[t].className;e.classList.add(n)}},Hh=class{constructor(e,t,n){if(e==="")throw Error("Developer note: CSS class cannot be an empty string, please specify one.");this.className=e,this.attributeName=t,this.attributeValueCalculator=n}addDataAttribute(e,t,n){if(this.attributeName===Hh.noAttributeName)return;let i=this.attributeValueCalculator(n,t);i!==""&&(e.dataset[this.attributeName]=i)}},Gr=Hh;Gr.noAttributeName="",Gr.noAttributeValueCalculator=()=>"",Gr.dateAttributeCalculator=(e,t)=>{let i="far";function s(o){let l=window.moment().startOf("day").diff(o,"days");if(isNaN(l))return null;if(l===0)return"today";let c="";return l>0?c+="past-":l<0&&(c+="future-"),Math.abs(l)<=7?c+=Math.abs(l).toString()+"d":c+=i,c}let a=t[e];if(!Array.isArray(a)&&a instanceof window.moment){let o=s(a);if(o)return o}return""};function ra(r){return new Gr(r,Gr.noAttributeName,Gr.noAttributeValueCalculator)}function na(r,e){return new Gr(r,e,Gr.dateAttributeCalculator)}var eL={createdDate:na("task-created","taskCreated"),dueDate:na("task-due","taskDue"),startDate:na("task-start","taskStart"),scheduledDate:na("task-scheduled","taskScheduled"),doneDate:na("task-done","taskDone"),cancelledDate:na("task-cancelled","taskCancelled"),priority:new Gr("task-priority","taskPriority",(r,e)=>Mn.priorityNameUsingNormal(e.priority).toLocaleLowerCase()),description:ra("task-description"),recurrenceRule:ra("task-recurring"),onCompletion:ra("task-onCompletion"),dependsOn:ra("task-dependsOn"),id:ra("task-id"),blockLink:ra("task-block-link")};function Xe(r,e){let t=document.createElement(r);return e.appendChild(t),t}var qn=class{static obsidianMarkdownRenderer(e,t,n,i){return A(this,null,function*(){!i||(yield Ik.MarkdownRenderer.renderMarkdown(e,t,n,i))})}constructor({textRenderer:e=qn.obsidianMarkdownRenderer,obsidianComponent:t,parentUlElement:n,taskLayoutOptions:i,queryLayoutOptions:s}){this.textRenderer=e,this.obsidianComponent=t,this.parentUlElement=n,this.taskLayoutOptions=i,this.queryLayoutOptions=s}renderTaskLine(e,t,n){return A(this,null,function*(){let i=Xe("li",this.parentUlElement);i.classList.add("task-list-item","plugin-tasks-list-item");let s=Xe("span",i);s.classList.add("tasks-list-text"),yield this.taskToHtml(e,s,i);let a=Xe("input",i);return a.classList.add("task-list-item-checkbox"),a.type="checkbox",e.status.symbol!==" "&&(a.checked=!0,i.classList.add("is-checked")),e.taskLocation.hasKnownPath&&(a.addEventListener("click",u=>{u.preventDefault(),u.stopPropagation(),a.disabled=!0;let l=e.toggleWithRecurrenceInUsersOrder();br({originalTask:e,newTasks:l})}),a.addEventListener("contextmenu",u=>{new lc(De.getInstance(),e).showAtPosition({x:u.clientX,y:u.clientY})}),a.setAttribute("title","Right-click for options")),i.prepend(a),i.setAttribute("data-task",e.status.symbol.trim()),i.setAttribute("data-line",t.toString()),i.setAttribute("data-task-status-name",e.status.name),i.setAttribute("data-task-status-type",e.status.type),a.setAttribute("data-line",t.toString()),this.queryLayoutOptions.shortMode&&this.addTooltip(e,s,n),i})}taskToHtml(e,t,n){return A(this,null,function*(){let i=new fc,s=Nr.tasksPluginEmoji.taskSerializer;for(let a of this.taskLayoutOptions.shownComponents){let o=s.componentToString(e,this.queryLayoutOptions.shortMode,a);if(o){let u=Xe("span",t),l=Xe("span",u);if(yield this.renderComponentText(l,o,a,e),this.addInternalClasses(a,l),i.addClassName(u,a),i.addDataAttribute(u,e,a),i.addDataAttribute(n,e,a),ee.allDateFields().includes(a)){let c=a;u.addEventListener("contextmenu",d=>{d.preventDefault(),d.stopPropagation(),new dc(c,e,Wn).showAtPosition({x:d.clientX,y:d.clientY})}),u.setAttribute("title",`Right-click to edit ${cc(c)}`)}}}for(let a of this.taskLayoutOptions.hiddenComponents)i.addDataAttribute(n,e,a);n.dataset.taskPriority===void 0&&i.addDataAttribute(n,e,"priority")})}renderComponentText(e,t,n,i){return A(this,null,function*(){if(n==="description"){t=ve.getInstance().removeAsWordFromDependingOnSettings(t);let{debugSettings:s}=Q();s.showTaskHiddenData&&(t+=`<br>\u{1F41B} <b>${i.lineNumber}</b> . ${i.sectionStart} . ${i.sectionIndex} . '<code>${i.originalMarkdown}</code>'<br>'<code>${i.path}</code>' > '<code>${i.precedingHeader}</code>'<br>`),yield this.textRenderer(t,e,i.path,this.obsidianComponent);let a=e.querySelector("blockquote"),o=a!=null?a:e,u=o.querySelector("p");if(u!==null){for(;u.firstChild;)o.insertBefore(u.firstChild,u);u.remove()}e.querySelectorAll("p").forEach(l=>{l.hasChildNodes()||l.remove()}),e.querySelectorAll(".footnotes").forEach(l=>{l.remove()})}else e.innerHTML=t})}addInternalClasses(e,t){function n(i){let s=/["&\x00\r\n]/g,a=i.replace(s,"-");return a=a.replace(/^[-_]+/,""),a.length>0?a:null}if(e==="description"){let i=t.getElementsByClassName("tag");for(let s=0;s<i.length;s++){let a=i[s].textContent;if(a){let o=n(a),u=i[s];o&&(u.dataset.tagName=o)}}}}addTooltip(e,t,n){let{recurrenceSymbol:i,startDateSymbol:s,createdDateSymbol:a,scheduledDateSymbol:o,dueDateSymbol:u,cancelledDateSymbol:l,doneDateSymbol:c}=Nr.tasksPluginEmoji.taskSerializer.symbols;t.addEventListener("mouseenter",()=>{function d(b,k,_){k&&b.createDiv().setText(f({signifier:_,date:k}))}function f({signifier:b,date:k}){return`${b} ${k.format(Z.dateFormat)} (${k.from(window.moment().startOf("day"))})`}let m=t.createDiv();m.addClasses(["tooltip","pop-up"]),e.recurrence&&m.createDiv().setText(`${i} ${e.recurrence.toText()}`),d(m,e.createdDate,a),d(m,e.startDate,s),d(m,e.scheduledDate,o),d(m,e.dueDate,u),d(m,e.cancelledDate,l),d(m,e.doneDate,c);let y=e.getLinkText({isFilenameUnique:n});y&&m.createDiv().setText(`\u{1F517} ${y}`),t.addEventListener("mouseleave",()=>{m.remove()})})}};var pc=class{constructor({plugin:e}){this.markdownPostProcessor=this._markdownPostProcessor.bind(this);e.registerMarkdownPostProcessor(this._markdownPostProcessor.bind(this))}_markdownPostProcessor(e,t){return A(this,null,function*(){var d;let n=new Fk.MarkdownRenderChild(e);t.addChild(n);let i=e.findAll(".task-list-item").filter(f=>{var b;let m=(b=f.textContent)==null?void 0:b.split(`
`);if(m===void 0)return!1;let y=null;for(let k=0;k<m.length;k=k+1)if(m[k]!==""){y=m[k];break}return y===null?!1:ve.getInstance().includedIn(y)});if(i.length===0)return;let s=t.sourcePath,a=t.getSectionInfo(e);if(a===null)return;let o=a.text.split(`
`),u=0,l=[];for(let f=a.lineStart;f<=a.lineEnd;f++){let m=o[f];if(m===void 0)continue;let y=null,b=ee.fromLine({line:m,taskLocation:new ht(new ut(s),f,a.lineStart,u,y),fallbackDate:null});b!==null&&(l.push(b),u++)}let c=new qn({obsidianComponent:n,parentUlElement:e,taskLayoutOptions:new en,queryLayoutOptions:new ri});for(let f=0;f<i.length;f++){let m=l[f],y=i[f];if(m===void 0||y===void 0)continue;let b=(d=y.getAttr("data-line"))!=null?d:"0",k=Number.parseInt(b,10),_=yield c.renderTaskLine(m,k),R=y.childNodes;for(let q=0;q<R.length;q=q+1){let ne=R[q],G=ne.nodeName.toLowerCase();G==="div"?_.prepend(ne):(G==="ul"||G==="ol")&&_.append(ne)}let S=y.querySelectorAll("[data-footnote-id]"),F=_.querySelectorAll("[data-footnote-id]");if(S.length===F.length)for(let q=0;q<S.length;q++)F[q].replaceWith(S[q]);y.replaceWith(_)}})}};var Lk=require("@codemirror/view"),Uk=require("obsidian");var Wk=()=>Lk.ViewPlugin.fromClass(Vh),Vh=class{constructor(e){this.view=e,this.handleClickEvent=this.handleClickEvent.bind(this),this.view.dom.addEventListener("click",this.handleClickEvent)}destroy(){this.view.dom.removeEventListener("click",this.handleClickEvent)}handleClickEvent(e){let{target:t}=e;if(!t||!(t instanceof HTMLInputElement)||t.type!=="checkbox")return!1;let n=t.closest("ul.plugin-tasks-query-result, div.callout-content");if(n){if(n.matches("div.callout-content")){let f=`obsidian-tasks-plugin warning: Tasks cannot add or remove completion dates or make the next copy of a recurring task for tasks written inside a callout when you click their checkboxes in Live Preview. 
If you wanted Tasks to do these things, please undo your change, then either click the line of the task and use the "Toggle Task Done" command, or switch to Reading View to click the checkbox.`;console.warn(f),new Uk.Notice(f,45e3)}return!1}let{state:i}=this.view,s=this.view.posAtDOM(t),a=i.doc.lineAt(s),o=ee.fromLine({line:a.text,taskLocation:ht.fromUnknownPosition(new ut("")),fallbackDate:null});if(o===null)return!1;e.preventDefault();let u=o.toggleWithRecurrenceInUsersOrder(),l=u.map(f=>f.toFileLineString()).join(i.lineBreak),c=i.update({changes:{from:a.from,to:a.to,insert:l}});if(this.view.dispatch(c),u.length===1){let f=u[0].status.symbol!==" ";setTimeout(()=>{t.checked=f},1)}return!0}};var Tc=require("obsidian"),jk=require("obsidian");function qk(r,e,t,n=void 0){let i="";e.isEmpty()||(i+=`Only tasks containing the global filter '${e.get()}'.

`);let s=new Us("  "),a=new $r(r,n);if(!a.ignoreGlobalQuery&&t.hasInstructions()){let o=t.query(n);i+=`Explanation of the global query:

${s.explainQuery(o)}
`}return i+=`Explanation of this Tasks code block query:

${s.explainQuery(a)}`,i}function Ho(r,e,t){let n=new $r(r,t);return n.ignoreGlobalQuery?n:e.query(t).append(n)}function Vo(r,e,t){e&&r.push(tL(t))}function tL(r){return`tasks-layout-hide-${r}`}var mc=class{constructor(e){e?this.queryLayoutOptions=e:this.queryLayoutOptions=new ri}getHiddenClasses(){let e=[],t=[[this.queryLayoutOptions.hideUrgency,"urgency"],[this.queryLayoutOptions.hideBacklinks,"backlinks"],[this.queryLayoutOptions.hideEditButton,"edit-button"],[this.queryLayoutOptions.hidePostponeButton,"postpone-button"]];for(let[n,i]of t)Vo(e,n,i);return this.queryLayoutOptions.shortMode&&e.push("tasks-layout-short-mode"),e}};var hc=class{constructor(e){e?this.taskLayoutOptions=e:this.taskLayoutOptions=new en}generateHiddenClasses(){let e=[];return this.taskLayoutOptions.toggleableComponents.forEach(t=>{Vo(e,!this.taskLayoutOptions.isShown(t),t)}),Vo(e,!this.taskLayoutOptions.areTagsShown(),"tags"),e}};var zo=class{constructor(e){this.label=e,this.start()}start(){!this.recordTimings()||performance.mark(this.labelForStart())}finish(){!this.recordTimings()||(performance.mark(this.labelForEnd()),performance.measure(this.label,this.labelForStart(),this.labelForEnd()),this.printDuration())}printDuration(){let e=performance.getEntriesByName(this.label),t=e[e.length-1];t?console.log(this.label+":",t.duration.toFixed(2),"milliseconds"):console.log(`Measurement for ${this.label} not found`)}labelForStart(){return`${this.label} - start`}labelForEnd(){return`${this.label} - end`}recordTimings(){let{debugSettings:e}=Q();return e.recordTimings}};var zh=require("obsidian");var di=class extends ci{constructor(e,t,n=Wn){super(n);let i=(l,c,d,f,m,y)=>{var R;let b=!1,k=ea(t);if(k){let{postponedDate:S}=y(t,k,d,f);(R=t[k])!=null&&R.isSame(S,"day")&&(b=!0)}let _=m(t,f,d);c.setChecked(b).setTitle(_).onClick(()=>di.postponeOnClickCallback(l,t,f,d,y,n))},s=Rk,a=kk;this.addItem(l=>i(e,l,"days",0,s,a)),this.addItem(l=>i(e,l,"day",1,s,a)),this.addSeparator();let o=Gh,u=jh;this.addItem(l=>i(e,l,"days",2,o,u)),this.addItem(l=>i(e,l,"days",3,o,u)),this.addItem(l=>i(e,l,"days",4,o,u)),this.addItem(l=>i(e,l,"days",5,o,u)),this.addItem(l=>i(e,l,"days",6,o,u)),this.addSeparator(),this.addItem(l=>i(e,l,"week",1,o,u)),this.addItem(l=>i(e,l,"weeks",2,o,u)),this.addItem(l=>i(e,l,"weeks",3,o,u)),this.addItem(l=>i(e,l,"month",1,o,u)),this.addSeparator(),this.addItem(l=>i(e,l,"days",2,Mk,Ek))}static postponeOnClickCallback(o,u,l,c){return A(this,arguments,function*(e,t,n,i,s=jh,a=Wn){var y;let d=ea(t);if(d===null){let b="\u26A0\uFE0F Postponement requires a date: due, scheduled or start.";return new zh.Notice(b,1e4)}let{postponedDate:f,postponedTask:m}=s(t,d,i,n);(y=t[d])!=null&&y.isSame(f,"day")||(yield a(t,m),di.postponeSuccessCallback(e,d,f))})}static postponeSuccessCallback(e,t,n){e.style.pointerEvents="none";let i=Dk(n,t);new zh.Notice(i,2e3)}};function $k(r){let e=r.parent;for(;e!==null&&!(e instanceof ee);)e=e.parent;return e}var gc=class{constructor(e,t,n,i,s,a=qn.obsidianMarkdownRenderer){switch(this.source=t,this.tasksFile=n,this.renderMarkdown=i,this.obsidianComponent=s,this.textRenderer=a,e){case"block-language-tasks":this.query=Ho(this.source,tr.getInstance(),this.tasksFile),this.queryType="tasks";break;default:this.query=Ho(this.source,tr.getInstance(),this.tasksFile),this.queryType="tasks";break}}get filePath(){var e,t;return(t=(e=this.tasksFile)==null?void 0:e.path)!=null?t:void 0}render(e,t,n,i){return A(this,null,function*(){e==="Warm"&&this.query.error===void 0?yield this.renderQuerySearchResults(t,e,n,i):this.query.error!==void 0?this.renderErrorMessage(n,this.query.error):this.renderLoadingMessage(n)})}renderQuerySearchResults(e,t,n,i){return A(this,null,function*(){let s=this.explainAndPerformSearch(t,e,n);if(s.searchErrorMessage!==void 0){this.renderErrorMessage(n,s.searchErrorMessage);return}yield this.renderSearchResults(s,n,i)})}explainAndPerformSearch(e,t,n){let i=new zo(`Search: ${this.query.queryId} - ${this.filePath}`);i.start(),this.query.debug(`[render] Render called: plugin state: ${e}; searching ${t.length} tasks`),this.query.queryLayoutOptions.explainQuery&&this.createExplanation(n);let s=this.query.applyQueryToTasks(t);return i.finish(),s}renderSearchResults(e,t,n){return A(this,null,function*(){let i=new zo(`Render: ${this.query.queryId} - ${this.filePath}`);i.start(),yield this.addAllTaskGroups(e.taskGroups,t,n);let s=e.totalTasksCount;this.addTaskCount(t,e),this.query.debug(`[render] ${s} tasks displayed`),i.finish()})}renderErrorMessage(e,t){e.createDiv().innerHTML=`<pre>Tasks query: ${t.replace(/\n/g,"<br>")}</pre>`}renderLoadingMessage(e){e.setText("Loading Tasks ...")}createExplanation(e){let t=qk(this.source,ve.getInstance(),tr.getInstance(),this.tasksFile),n=Xe("pre",e);n.classList.add("plugin-tasks-query-explanation"),n.setText(t),e.appendChild(n)}addAllTaskGroups(e,t,n){return A(this,null,function*(){for(let i of e.groups){yield this.addGroupHeadings(t,i.groupHeadings);let s=new Set;yield this.createTaskList(i.tasks,t,n,s)}})}createTaskList(e,t,n,i){return A(this,null,function*(){let s=Xe("ul",t);s.classList.add("contains-task-list","plugin-tasks-query-result");let a=new hc(this.query.taskLayoutOptions);s.classList.add(...a.generateHiddenClasses());let o=new mc(this.query.queryLayoutOptions);s.classList.add(...o.getHiddenClasses());let u=this.getGroupingAttribute();u&&u.length>0&&(s.dataset.taskGroupBy=u);let l=new qn({textRenderer:this.textRenderer,obsidianComponent:this.obsidianComponent,parentUlElement:s,taskLayoutOptions:this.query.taskLayoutOptions,queryLayoutOptions:this.query.queryLayoutOptions});for(let[c,d]of e.entries())this.query.queryLayoutOptions.hideTree?d instanceof ee&&(yield this.addTask(s,l,d,c,n)):yield this.addTaskOrListItemAndChildren(s,l,d,c,n,e,i);t.appendChild(s)})}willBeRenderedLater(e,t,n){let i=$k(e);return i?!!(!t.has(i)&&n.includes(i)):!1}alreadyRendered(e,t){return t.has(e)}addTaskOrListItemAndChildren(e,t,n,i,s,a,o){return A(this,null,function*(){if(this.alreadyRendered(n,o)||this.willBeRenderedLater(n,o,a))return;let u=yield this.addTaskOrListItem(e,t,n,i,s);o.add(n),n.children.length>0&&(yield this.createTaskList(n.children,u,s,o),n.children.forEach(l=>{o.add(l)}))})}addTaskOrListItem(e,t,n,i,s){return A(this,null,function*(){return n instanceof ee?yield this.addTask(e,t,n,i,s):yield this.addListItem(e,n)})}addListItem(e,t){return A(this,null,function*(){var s,a;let n=Xe("li",e),i=Xe("span",n);return yield this.textRenderer(t.description,i,(a=(s=$k(t))==null?void 0:s.path)!=null?a:"",this.obsidianComponent),n})}addTask(e,t,n,i,s){return A(this,null,function*(){let a=this.isFilenameUnique({task:n},s.allMarkdownFiles),o=yield t.renderTaskLine(n,i,a);o.querySelectorAll("[data-footnote-id]").forEach(d=>d.remove());let l=Xe("span",o);l.classList.add("task-extras"),this.query.queryLayoutOptions.hideUrgency||this.addUrgency(l,n);let c=this.query.queryLayoutOptions.shortMode;return this.query.queryLayoutOptions.hideBacklinks||this.addBacklinks(l,n,c,a,s),this.query.queryLayoutOptions.hideEditButton||this.addEditButton(l,n,s),!this.query.queryLayoutOptions.hidePostponeButton&&wk(n)&&this.addPostponeButton(l,n,c),e.appendChild(o),o})}addEditButton(e,t,n){let i=Xe("a",e);i.classList.add("tasks-edit"),i.title="Edit task",i.href="#",i.addEventListener("click",s=>n.editTaskPencilClickHandler(s,t,n.allTasks))}addUrgency(e,t){let n=new Intl.NumberFormat().format(t.urgency);e.createSpan({text:n,cls:"tasks-urgency"})}addGroupHeadings(e,t){return A(this,null,function*(){for(let n of t)yield this.addGroupHeading(e,n)})}addGroupHeading(e,t){return A(this,null,function*(){let n="h6";t.nestingLevel===0?n="h4":t.nestingLevel===1&&(n="h5");let i=Xe(n,e);i.classList.add("tasks-group-heading"),this.obsidianComponent!==null&&(yield this.renderMarkdown(t.displayName,i,this.tasksFile.path,this.obsidianComponent))})}addBacklinks(e,t,n,i,s){var l;let a=Xe("span",e);a.classList.add("tasks-backlink"),n||a.append(" (");let o=Xe("a",a);o.rel="noopener",o.target="_blank",o.classList.add("internal-link"),n&&o.classList.add("internal-link-short-mode");let u;n?u=" \u{1F517}":u=(l=t.getLinkText({isFilenameUnique:i}))!=null?l:"",o.text=u,o.addEventListener("click",c=>A(this,null,function*(){yield s.backlinksClickHandler(c,t)})),o.addEventListener("mousedown",c=>A(this,null,function*(){yield s.backlinksMousedownHandler(c,t)})),n||a.append(")")}addPostponeButton(e,t,n){let s="day",a=xk(t,1,s),o=Xe("a",e);o.classList.add("tasks-postpone"),n&&o.classList.add("tasks-postpone-short-mode"),o.title=a,o.addEventListener("click",u=>{u.preventDefault(),u.stopPropagation(),di.postponeOnClickCallback(o,t,1,s)}),o.addEventListener("contextmenu",u=>A(this,null,function*(){u.preventDefault(),u.stopPropagation(),new di(o,t).showAtPosition({x:u.clientX,y:u.clientY})}))}addTaskCount(e,t){if(!this.query.queryLayoutOptions.hideTaskCount){let n=Xe("div",e);n.classList.add("task-count"),n.textContent=t.totalTasksCountDisplayText()}}isFilenameUnique({task:e},t){let n=e.path.match(/([^/]*)\..+$/i);if(n===null)return;let i=n[1];return t.filter(a=>{if(a.basename===i)return!0}).length<2}getGroupingAttribute(){let e=[];for(let t of this.query.grouping)e.push(t.property);return e.join(",")}};var yc=class{constructor({plugin:e,events:t}){this.addQueryRenderChild=this._addQueryRenderChild.bind(this);this.app=e.app,this.plugin=e,this.events=t,e.registerMarkdownCodeBlockProcessor("tasks",this._addQueryRenderChild.bind(this))}_addQueryRenderChild(e,t,n){return A(this,null,function*(){let i=new Kh({app:this.app,plugin:this.plugin,events:this.events,container:t,source:e,tasksFile:new ut(n.sourcePath)});n.addChild(i),i.load()})}},Kh=class extends Tc.MarkdownRenderChild{constructor({app:t,plugin:n,events:i,container:s,source:a,tasksFile:o}){super(s);this.queryResultsRenderer=new gc(this.containerEl.className,a,o,Tc.MarkdownRenderer.renderMarkdown,this),this.app=t,this.plugin=n,this.events=i}onload(){this.events.triggerRequestCacheUpdate(this.render.bind(this)),this.renderEventRef=this.events.onCacheUpdate(this.render.bind(this)),this.reloadQueryAtMidnight()}onunload(){this.renderEventRef!==void 0&&this.events.off(this.renderEventRef),this.queryReloadTimeout!==void 0&&clearTimeout(this.queryReloadTimeout)}reloadQueryAtMidnight(){let t=new Date;t.setHours(24,0,0,0);let n=new Date,i=t.getTime()-n.getTime();this.queryReloadTimeout=setTimeout(()=>{this.queryResultsRenderer.query=Ho(this.queryResultsRenderer.source,tr.getInstance(),this.queryResultsRenderer.tasksFile),this.events.triggerRequestCacheUpdate(this.render.bind(this)),this.reloadQueryAtMidnight()},i+1e3)}render(i){return A(this,arguments,function*({tasks:t,state:n}){var a;let s=Xe("div",this.containerEl);yield this.queryResultsRenderer.render(n,t,s,{allTasks:this.plugin.getTasks(),allMarkdownFiles:this.app.vault.getMarkdownFiles(),backlinksClickHandler:nL,backlinksMousedownHandler:iL,editTaskPencilClickHandler:rL}),(a=this.containerEl.firstChild)==null||a.replaceWith(s)})}};function rL(r,e,t){r.preventDefault();let n=s=>A(this,null,function*(){yield br({originalTask:e,newTasks:_t.removeInferredStatusIfNeeded(e,s)})});new ti({app,task:e,onSubmit:n,allTasks:t}).open()}function nL(r,e){return A(this,null,function*(){let t=yield fh(e,app.vault);if(t){let[n,i]=t,s=app.workspace.getLeaf(jk.Keymap.isModEvent(r));r.preventDefault(),yield s.openFile(i,{eState:{line:n}})}})}function iL(r,e){return A(this,null,function*(){if(r.button===1){let t=yield fh(e,app.vault);if(t){let[n,i]=t,s=app.workspace.getLeaf("tab");r.preventDefault(),yield s.openFile(i,{eState:{line:n}})}}})}var le=require("obsidian");var bc=class{constructor(e){this._markdown="";this.columnNames=e,this.addTitleRow()}get markdown(){return this._markdown}addTitleRow(){let e="|",t="|";this.columnNames.forEach(n=>{e+=` ${n} |`,t+=" ----- |"}),this._markdown+=`${e}
`,this._markdown+=`${t}
`}addRow(e){let t=this.makeRowText(e);this._markdown+=`${t}
`}addRowIfNew(e){let t=this.makeRowText(e);this._markdown.includes(t)||(this._markdown+=`${t}
`)}makeRowText(e){let t="|";return e.forEach(n=>{t+=` ${n} |`}),t}};function Gk(r,e){return r.findIndex(t=>t.symbol===e)}function $n(r){return r===""?r:"`"+(r!==" "?r:"space")+"`"}function sL(r,e){let t=re.getTypeForUnknownSymbol(r.symbol);r.type!==t&&(t==="TODO"&&r.symbol!==" "||e.push(`For information, the conventional type for status symbol ${$n(r.symbol)} is ${$n(t)}: you may wish to review this type.`))}function aL(r,e,t){let n=Gk(r,e.nextStatusSymbol);if(n===-1){t.push(`Next symbol ${$n(e.nextStatusSymbol)} is unknown: create a status with symbol ${$n(e.nextStatusSymbol)}.`);return}if(e.type!=="DONE")return;let i=r[n];if(i){if(i.type!=="TODO"&&i.type!=="IN_PROGRESS"){let s="https://publish.obsidian.md/tasks/Getting+Started/Statuses/Recurring+Tasks+and+Custom+Statuses",a=[`This \`DONE\` status is followed by ${$n(i.type)}, not \`TODO\` or \`IN_PROGRESS\`.`,"If used to complete a recurring task, it will instead be followed by `TODO` or `IN_PROGRESS`, to ensure the next task matches the `not done` filter.",`See [Recurring Tasks and Custom Statuses](${s}).`].join("<br>");t.push(a)}}else t.push("Unexpected failure to find the next status.")}function oL(r,e,t){let n=[];return e.symbol===re.EMPTY.symbol?(n.push("Empty symbol: this status will be ignored."),n):Gk(r,e.symbol)!=t?(n.push(`Duplicate symbol '${$n(e.symbol)}': this status will be ignored.`),n):(sL(e,n),aL(r,e,n),n)}function Yk(r){let e=new bc(["Status Symbol","Next Status Symbol","Status Name","Status Type","Problems (if any)"]),t=Le.allStatuses(r);return t.forEach((n,i)=>{e.addRow([$n(n.symbol),$n(n.nextStatusSymbol),n.name,$n(n.type),oL(t,n,i).join("<br>")])}),e.markdown}function Bk(r,e,t,n){let s=Yk(r),a=e.mermaidDiagram(!0);return`# ${t}

## About this file

This file was created by the Obsidian Tasks plugin (version ${n}) to help visualise the task statuses in this vault.

If you change the Tasks status settings, you can get an updated report by:

- Going to \`Settings\` -> \`Tasks\`.
- Clicking on \`Review and check your Statuses\`.

You can delete this file any time.

## Status Settings

<!--
Switch to Live Preview or Reading Mode to see the table.
If there are any Markdown formatting characters in status names, such as '*' or '_',
Obsidian may only render the table correctly in Reading Mode.
-->

These are the status values in the Core and Custom statuses sections.

${s}
## Loaded Settings

<!-- Switch to Live Preview or Reading Mode to see the diagram. -->

These are the settings actually used by Tasks.
${a}`}function Hk(){return[[" ","Unchecked","x","TODO"],["x","Checked"," ","DONE"],[">","Rescheduled","x","TODO"],["<","Scheduled","x","TODO"],["!","Important","x","TODO"],["-","Cancelled"," ","CANCELLED"],["/","In Progress","x","IN_PROGRESS"],["?","Question","x","TODO"],["*","Star","x","TODO"],["n","Note","x","TODO"],["l","Location","x","TODO"],["i","Information","x","TODO"],["I","Idea","x","TODO"],["S","Amount","x","TODO"],["p","Pro","x","TODO"],["c","Con","x","TODO"],["b","Bookmark","x","TODO"],['"',"Quote","x","TODO"],["0","Speech bubble 0","0","NON_TASK"],["1","Speech bubble 1","1","NON_TASK"],["2","Speech bubble 2","2","NON_TASK"],["3","Speech bubble 3","3","NON_TASK"],["4","Speech bubble 4","4","NON_TASK"],["5","Speech bubble 5","5","NON_TASK"],["6","Speech bubble 6","6","NON_TASK"],["7","Speech bubble 7","7","NON_TASK"],["8","Speech bubble 8","8","NON_TASK"],["9","Speech bubble 9","9","NON_TASK"]]}function Vk(){return[[" ","incomplete","x","TODO"],["x","complete / done"," ","DONE"],["-","cancelled"," ","CANCELLED"],[">","deferred","x","TODO"],["/","in progress, or half-done","x","IN_PROGRESS"],["!","Important","x","TODO"],["?","question","x","TODO"],["R","review","x","TODO"],["+","Inbox / task that should be processed later","x","TODO"],["b","bookmark","x","TODO"],["B","brainstorm","x","TODO"],["D","deferred or scheduled","x","TODO"],["I","Info","x","TODO"],["i","idea","x","TODO"],["N","note","x","TODO"],["Q","quote","x","TODO"],["W","win / success / reward","x","TODO"],["P","pro","x","TODO"],["C","con","x","TODO"]]}function zk(){return[[" ","To Do","x","TODO"],["/","In Progress","x","IN_PROGRESS"],["x","Done"," ","DONE"],["-","Cancelled"," ","CANCELLED"],[">","Rescheduled","x","TODO"],["<","Scheduled","x","TODO"],["!","Important","x","TODO"],["?","Question","x","TODO"],["i","Infomation","x","TODO"],["S","Amount","x","TODO"],["*","Star","x","TODO"],["b","Bookmark","x","TODO"],["\u201C","Quote","x","TODO"],["n","Note","x","TODO"],["l","Location","x","TODO"],["I","Idea","x","TODO"],["p","Pro","x","TODO"],["c","Con","x","TODO"],["u","Up","x","TODO"],["d","Down","x","TODO"]]}function Kk(){return[[" ","Unchecked","x","TODO"],["x","Checked"," ","DONE"],["-","Cancelled"," ","CANCELLED"],["/","In Progress","x","IN_PROGRESS"],[">","Deferred","x","TODO"],["!","Important","x","TODO"],["?","Question","x","TODO"],["r","Review","x","TODO"]]}function Qk(){return[[" ","Unchecked","x","TODO"],["x","Regular"," ","DONE"],["X","Checked"," ","DONE"],["-","Dropped"," ","CANCELLED"],[">","Forward","x","TODO"],["D","Date","x","TODO"],["?","Question","x","TODO"],["/","Half Done","x","IN_PROGRESS"],["+","Add","x","TODO"],["R","Research","x","TODO"],["!","Important","x","TODO"],["i","Idea","x","TODO"],["B","Brainstorm","x","TODO"],["P","Pro","x","TODO"],["C","Con","x","TODO"],["Q","Quote","x","TODO"],["N","Note","x","TODO"],["b","Bookmark","x","TODO"],["I","Information","x","TODO"],["p","Paraphrase","x","TODO"],["L","Location","x","TODO"],["E","Example","x","TODO"],["A","Answer","x","TODO"],["r","Reward","x","TODO"],["c","Choice","x","TODO"],["d","Doing","x","IN_PROGRESS"],["T","Time","x","TODO"],["@","Character / Person","x","TODO"],["t","Talk","x","TODO"],["O","Outline / Plot","x","TODO"],["~","Conflict","x","TODO"],["W","World","x","TODO"],["f","Clue / Find","x","TODO"],["F","Foreshadow","x","TODO"],["H","Favorite / Health","x","TODO"],["&","Symbolism","x","TODO"],["s","Secret","x","TODO"]]}function Xk(){return[[" ","Unchecked","x","TODO"],["x","Checked"," ","DONE"],[">","Rescheduled","x","TODO"],["<","Scheduled","x","TODO"],["!","Important","x","TODO"],["-","Cancelled"," ","CANCELLED"],["/","In Progress","x","IN_PROGRESS"],["?","Question","x","TODO"],["*","Star","x","TODO"],["n","Note","x","TODO"],["l","Location","x","TODO"],["i","Information","x","TODO"],["I","Idea","x","TODO"],["S","Amount","x","TODO"],["p","Pro","x","TODO"],["c","Con","x","TODO"],["b","Bookmark","x","TODO"],["f","Fire","x","TODO"],["k","Key","x","TODO"],["w","Win","x","TODO"],["u","Up","x","TODO"],["d","Down","x","TODO"]]}function Zk(){return[[" ","to-do","x","TODO"],["/","incomplete","x","IN_PROGRESS"],["x","done"," ","DONE"],["-","canceled"," ","CANCELLED"],[">","forwarded","x","TODO"],["<","scheduling","x","TODO"],["?","question","x","TODO"],["!","important","x","TODO"],["*","star","x","TODO"],['"',"quote","x","TODO"],["l","location","x","TODO"],["b","bookmark","x","TODO"],["i","information","x","TODO"],["S","savings","x","TODO"],["I","idea","x","TODO"],["p","pros","x","TODO"],["c","cons","x","TODO"],["f","fire","x","TODO"],["k","key","x","TODO"],["w","win","x","TODO"],["u","up","x","TODO"],["d","down","x","TODO"]]}function Jk(){return[[" ","to-do","x","TODO"],["/","incomplete","x","IN_PROGRESS"],["x","done"," ","DONE"],["-","canceled"," ","CANCELLED"],[">","forwarded","x","TODO"],["<","scheduling","x","TODO"],["?","question","x","TODO"],["!","important","x","TODO"],["*","star","x","TODO"],['"',"quote","x","TODO"],["l","location","x","TODO"],["b","bookmark","x","TODO"],["i","information","x","TODO"],["S","savings","x","TODO"],["I","idea","x","TODO"],["p","pros","x","TODO"],["c","cons","x","TODO"],["f","fire","x","TODO"],["k","key","x","TODO"],["w","win","x","TODO"],["u","up","x","TODO"],["d","down","x","TODO"]]}var eE=[{text:"Core Statuses",level:"h3",class:"",open:!0,notice:{class:"setting-item-description",text:null,html:"<p>These are the core statuses that Tasks supports natively, with no need for custom CSS styling or theming.</p><p>You can add edit and add your own custom statuses in the section below.</p>"},settings:[{name:"",description:"",type:"function",initialValue:"",placeholder:"",settingName:"insertTaskCoreStatusSettings",featureFlag:"",notice:null}]},{text:"Custom Statuses",level:"h3",class:"",open:!0,notice:{class:"setting-item-description",text:null,html:`<p>You should first <b>select and install a CSS Snippet or Theme</b> to style custom checkboxes.</p><p>Then, use the buttons below to set up your custom statuses, to match your chosen CSS checkboxes.</p><p><b>Note</b> Any statuses with the same symbol as any earlier statuses will be ignored. You can confirm the actually loaded statuses by running the 'Create or edit task' command and looking at the Status drop-down.</p><p></p><p>See the <a href="https://publish.obsidian.md/tasks/Getting+Started/Statuses">documentation</a> to get started!</p>`},settings:[{name:"",description:"",type:"function",initialValue:"",placeholder:"",settingName:"insertCustomTaskStatusSettings",featureFlag:"",notice:null}]}];var Yr=require("obsidian");var Ki=class{validate(e){let t=[];return t.push(...this.validateSymbol(e)),t.push(...this.validateName(e)),t.push(...this.validateNextSymbol(e)),t}validateStatusCollectionEntry(e){let[t,n,i,s]=e,a=[];if(a.push(...this.validateType(s)),t===i&&s!=="NON_TASK"&&a.push(`Status symbol '${t}' toggles to itself`),a.length>0)return a;let o=re.createFromImportedValue(e).configuration;return a.push(...this.validateSymbolTypeConventions(o)),a.push(...this.validate(o)),a}validateSymbol(e){return Ki.validateOneSymbol(e.symbol,"Task Status Symbol")}validateNextSymbol(e){return Ki.validateOneSymbol(e.nextStatusSymbol,"Task Next Status Symbol")}validateName(e){let t=[];return e.name.length===0&&t.push("Task Status Name cannot be empty."),t}validateType(e){let t=xt[e],n=[];return t||n.push(`Status Type "${e}" is not a valid type`),t=="EMPTY"&&n.push('Status Type "EMPTY" is not permitted in user data'),n}validateSymbolTypeConventions(e){let t=[],n=e.symbol,i=new De,s=n==="X"?"x":n,a=i.bySymbol(s);return a.type!=="EMPTY"&&(e.nextStatusSymbol!==a.nextStatusSymbol&&t.push(`Next Status Symbol for symbol '${n}': '${e.nextStatusSymbol}' is inconsistent with convention '${a.nextStatusSymbol}'`),e.type!==a.type&&t.push(`Status Type for symbol '${n}': '${e.type}' is inconsistent with convention '${a.type}'`)),t}static validateOneSymbol(e,t){let n=[];return e.length===0&&n.push(`${t} cannot be empty.`),e.length>1&&n.push(`${t} ("${e}") must be a single character.`),n}};var Qi=new Ki,rr=class extends Yr.Modal{constructor(t,n,i){super(t.app);this.plugin=t;this.saved=!1;this.error=!1;this.statusSymbol=n.symbol,this.statusName=n.name,this.statusNextSymbol=n.nextStatusSymbol,this.statusAvailableAsCommand=n.availableAsCommand,this.type=n.type,this.isCoreStatus=i}statusConfiguration(){return new Qe(this.statusSymbol,this.statusName,this.statusNextSymbol,this.statusAvailableAsCommand,this.type)}display(){return A(this,null,function*(){let{contentEl:t}=this;t.empty();let n=t.createDiv(),i;new Yr.Setting(n).setName("Task Status Symbol").setDesc("This is the character between the square braces. (It can only be edited for Custom statuses, and not Core statuses.)").addText(l=>{i=l,l.setValue(this.statusSymbol).onChange(c=>{this.statusSymbol=c,rr.setValid(l,Qi.validateSymbol(this.statusConfiguration()))})}).setDisabled(this.isCoreStatus).then(l=>{rr.setValid(i,Qi.validateSymbol(this.statusConfiguration()))});let s;new Yr.Setting(n).setName("Task Status Name").setDesc("This is the friendly name of the task status.").addText(l=>{s=l,l.setValue(this.statusName).onChange(c=>{this.statusName=c,rr.setValid(l,Qi.validateName(this.statusConfiguration()))})}).then(l=>{rr.setValid(s,Qi.validateName(this.statusConfiguration()))});let a;new Yr.Setting(n).setName("Task Next Status Symbol").setDesc("When clicked on this is the symbol that should be used next.").addText(l=>{a=l,l.setValue(this.statusNextSymbol).onChange(c=>{this.statusNextSymbol=c,rr.setValid(l,Qi.validateNextSymbol(this.statusConfiguration()))})}).then(l=>{rr.setValid(a,Qi.validateNextSymbol(this.statusConfiguration()))}),new Yr.Setting(n).setName("Task Status Type").setDesc("Control how the status behaves for searching and toggling.").addDropdown(l=>{["TODO","IN_PROGRESS","DONE","CANCELLED","NON_TASK"].forEach(d=>{l.addOption(d,d)}),l.setValue(this.type).onChange(d=>{this.type=re.getTypeFromStatusTypeString(d)})}),re.tasksPluginCanCreateCommandsForStatuses()&&new Yr.Setting(n).setName("Available as command").setDesc("If enabled this status will be available as a command so you can assign a hotkey and toggle the status using it.").addToggle(l=>{l.setValue(this.statusAvailableAsCommand).onChange(c=>A(this,null,function*(){this.statusAvailableAsCommand=c}))});let o=t.createDiv(),u=new Yr.Setting(o);u.addButton(l=>(l.setTooltip("Save").setIcon("checkmark").onClick(()=>A(this,null,function*(){let c=Qi.validate(this.statusConfiguration());if(c.length>0){let d=c.join(`
`)+`

Fix errors before saving.`;new Yr.Notice(d);return}this.saved=!0,this.close()})),l)),u.addExtraButton(l=>(l.setIcon("cross").setTooltip("Cancel").onClick(()=>{this.saved=!1,this.close()}),l))})}onOpen(){this.display()}static setValidationError(t){t.inputEl.addClass("tasks-settings-is-invalid")}static removeValidationError(t){t.inputEl.removeClass("tasks-settings-is-invalid")}static setValid(t,n){n.length===0?rr.removeValidationError(t):rr.setValidationError(t)}};var Pt=class extends le.PluginSettingTab{constructor({plugin:t}){super(t.app,t);this.customFunctions={insertTaskCoreStatusSettings:this.insertTaskCoreStatusSettings.bind(this),insertCustomTaskStatusSettings:this.insertCustomTaskStatusSettings.bind(this)};this.plugin=t}saveSettings(t){return A(this,null,function*(){yield this.plugin.saveSettings(),t&&this.display()})}display(){let{containerEl:t}=this;t.empty(),this.containerEl.addClass("tasks-settings"),t.createEl("p",{cls:"tasks-setting-important",text:"Changing any settings requires a restart of obsidian."}),new le.Setting(t).setName("Task Format").setDesc(Pt.createFragmentWithHTML('<p>The format that Tasks uses to read and write tasks.</p><p><b>Important:</b> Tasks currently only supports one format at a time. Selecting Dataview will currently <b>stop Tasks reading its own emoji signifiers</b>.</p><p>See the <a href="https://publish.obsidian.md/tasks/Reference/Task+Formats/About+Task+Formats">documentation</a>.</p>')).addDropdown(l=>{for(let c of Object.keys(Nr))l.addOption(c,Nr[c].displayName);l.setValue(Q().taskFormat).onChange(c=>A(this,null,function*(){ze({taskFormat:c}),yield this.plugin.saveSettings()}))}),new le.Setting(t).setName("Global task filter").setHeading();let n=null;new le.Setting(t).setName("Global filter").setDesc(Pt.createFragmentWithHTML('<p><b>Recommended: Leave empty if you want all checklist items in your vault to be tasks managed by this plugin.</b></p><p>Use a global filter if you want Tasks to only act on a subset of your "<code>- [ ]</code>" checklist items, so that a checklist item must include the specified string in its description in order to be considered a task.<p><p>For example, if you set the global filter to <code>#task</code>, the Tasks plugin will only handle checklist items tagged with <code>#task</code>.</br>Other checklist items will remain normal checklist items and not appear in queries or get a done date set.</p><p>See the <a href="https://publish.obsidian.md/tasks/Getting+Started/Global+Filter">documentation</a>.</p>')).addText(l=>{l.setPlaceholder("e.g. #task or TODO").setValue(ve.getInstance().get()).onChange(c=>A(this,null,function*(){ze({globalFilter:c}),ve.getInstance().set(c),yield this.plugin.saveSettings(),dn(n,c.length>0)}))}),n=new le.Setting(t).setName("Remove global filter from description").setDesc("Enabling this removes the string that you set as global filter from the task description when displaying a task.").addToggle(l=>{let c=Q();l.setValue(c.removeGlobalFilter).onChange(d=>A(this,null,function*(){ze({removeGlobalFilter:d}),ve.getInstance().setRemoveGlobalFilter(d),yield this.plugin.saveSettings()}))}),dn(n,Q().globalFilter.length>0),new le.Setting(t).setName("Global Query").setHeading(),dL(new le.Setting(t).setDesc(Pt.createFragmentWithHTML('<p>A query that is automatically included at the start of every Tasks block in the vault. Useful for adding default filters, or layout options.</p><p>See the <a href="https://publish.obsidian.md/tasks/Queries/Global+Query">documentation</a>.</p>')).addTextArea(l=>{let c=Q();l.inputEl.rows=4,l.setPlaceholder(`# For example...
path does not include _templates/
limit 300
show urgency`).setValue(c.globalQuery).onChange(d=>A(this,null,function*(){ze({globalQuery:d}),tr.getInstance().set(d),yield this.plugin.saveSettings()}))})),new le.Setting(t).setName("Task Statuses").setHeading();let{headingOpened:i}=Q();eE.forEach(l=>{var f;let c=(f=i[l.text])!=null?f:!0,d=this.addOneSettingsBlock(t,l,i);d.open=c}),new le.Setting(t).setName("Dates").setHeading(),new le.Setting(t).setName("Set created date on every added task").setDesc(Pt.createFragmentWithHTML(`Enabling this will add a timestamp \u2795 YYYY-MM-DD before other date values, when a task is created with 'Create or edit task', or by completing a recurring task.</br><p>See the <a href="https://publish.obsidian.md/tasks/Getting+Started/Dates#Created+date">documentation</a>.</p>`)).addToggle(l=>{let c=Q();l.setValue(c.setCreatedDate).onChange(d=>A(this,null,function*(){ze({setCreatedDate:d}),yield this.plugin.saveSettings()}))}),new le.Setting(t).setName("Set done date on every completed task").setDesc(Pt.createFragmentWithHTML('Enabling this will add a timestamp \u2705 YYYY-MM-DD at the end when a task is toggled to done.</br><p>See the <a href="https://publish.obsidian.md/tasks/Getting+Started/Dates#Done+date">documentation</a>.</p>')).addToggle(l=>{let c=Q();l.setValue(c.setDoneDate).onChange(d=>A(this,null,function*(){ze({setDoneDate:d}),yield this.plugin.saveSettings()}))}),new le.Setting(t).setName("Set cancelled date on every cancelled task").setDesc(Pt.createFragmentWithHTML('Enabling this will add a timestamp \u274C YYYY-MM-DD at the end when a task is toggled to cancelled.</br><p>See the <a href="https://publish.obsidian.md/tasks/Getting+Started/Dates#Cancelled+date">documentation</a>.</p>')).addToggle(l=>{let c=Q();l.setValue(c.setCancelledDate).onChange(d=>A(this,null,function*(){ze({setCancelledDate:d}),yield this.plugin.saveSettings()}))}),new le.Setting(t).setName("Dates from file names").setHeading();let s=null,a=null;new le.Setting(t).setName("Use filename as Scheduled date for undated tasks").setDesc(Pt.createFragmentWithHTML('Save time entering Scheduled (\u23F3) dates.</br>If this option is enabled, any undated tasks will be given a default Scheduled date extracted from their file name.</br>By default, Tasks plugin will match both <code>YYYY-MM-DD</code> and <code>YYYYMMDD</code> date formats.</br>Undated tasks have none of Due (\u{1F4C5} ), Scheduled (\u23F3) and Start (\u{1F6EB}) dates.</br><p>See the <a href="https://publish.obsidian.md/tasks/Getting+Started/Use+Filename+as+Default+Date">documentation</a>.</p>')).addToggle(l=>{let c=Q();l.setValue(c.useFilenameAsScheduledDate).onChange(d=>A(this,null,function*(){ze({useFilenameAsScheduledDate:d}),dn(s,d),dn(a,d),yield this.plugin.saveSettings()}))}),s=new le.Setting(t).setName("Additional filename date format as Scheduled date for undated tasks").setDesc(Pt.createFragmentWithHTML('An additional date format that Tasks plugin will recogize when using the file name as the Scheduled date for undated tasks.</br><p><a href="https://momentjs.com/docs/#/displaying/format/">Syntax Reference</a></p>')).addText(l=>{let c=Q();l.setPlaceholder("example: MMM DD YYYY").setValue(c.filenameAsScheduledDateFormat).onChange(d=>A(this,null,function*(){ze({filenameAsScheduledDateFormat:d}),yield this.plugin.saveSettings()}))}),a=new le.Setting(t).setName("Folders with default Scheduled dates").setDesc("Leave empty if you want to use default Scheduled dates everywhere, or enter a comma-separated list of folders.").addText(l=>A(this,null,function*(){let c=Q();yield this.plugin.saveSettings(),l.setValue(Pt.renderFolderArray(c.filenameAsDateFolders)).onChange(d=>A(this,null,function*(){let f=Pt.parseCommaSeparatedFolders(d);ze({filenameAsDateFolders:f}),yield this.plugin.saveSettings()}))})),dn(s,Q().useFilenameAsScheduledDate),dn(a,Q().useFilenameAsScheduledDate),new le.Setting(t).setName("Recurring tasks").setHeading(),new le.Setting(t).setName("Next recurrence appears on the line below").setDesc(Pt.createFragmentWithHTML('Enabling this will make the next recurrence of a task appear on the line below the completed task. Otherwise the next recurrence will appear before the completed one.</br><p>See the <a href="https://publish.obsidian.md/tasks/Getting+Started/Recurring+Tasks">documentation</a>.</p>')).addToggle(l=>{let{recurrenceOnNextLine:c}=Q();l.setValue(c).onChange(d=>A(this,null,function*(){ze({recurrenceOnNextLine:d}),yield this.plugin.saveSettings()}))}),new le.Setting(t).setName("Auto-suggest").setHeading();let o=null,u=null;new le.Setting(t).setName("Auto-suggest task content").setDesc(Pt.createFragmentWithHTML('Enabling this will open an intelligent suggest menu while typing inside a recognized task line.</br><p>See the <a href="https://publish.obsidian.md/tasks/Getting+Started/Auto-Suggest">documentation</a>.</p>')).addToggle(l=>{let c=Q();l.setValue(c.autoSuggestInEditor).onChange(d=>A(this,null,function*(){ze({autoSuggestInEditor:d}),yield this.plugin.saveSettings(),dn(o,d),dn(u,d)}))}),o=new le.Setting(t).setName("Minimum match length for auto-suggest").setDesc("If higher than 0, auto-suggest will be triggered only when the beginning of any supported keywords is recognized.").addSlider(l=>{let c=Q();l.setLimits(0,3,1).setValue(c.autoSuggestMinMatch).setDynamicTooltip().onChange(d=>A(this,null,function*(){ze({autoSuggestMinMatch:d}),yield this.plugin.saveSettings()}))}),u=new le.Setting(t).setName("Maximum number of auto-suggestions to show").setDesc('How many suggestions should be shown when an auto-suggest menu pops up (including the "\u23CE" option).').addSlider(l=>{let c=Q();l.setLimits(3,20,1).setValue(c.autoSuggestMaxItems).setDynamicTooltip().onChange(d=>A(this,null,function*(){ze({autoSuggestMaxItems:d}),yield this.plugin.saveSettings()}))}),dn(o,Q().autoSuggestInEditor),dn(u,Q().autoSuggestInEditor),new le.Setting(t).setName("Dialogs").setHeading(),new le.Setting(t).setName("Provide access keys in dialogs").setDesc(Pt.createFragmentWithHTML('If the access keys (keyboard shortcuts) for various controls in dialog boxes conflict with system keyboard shortcuts or assistive technology functionality that is important for you, you may want to deactivate them here.</br><p>See the <a href="https://publish.obsidian.md/tasks/Getting+Started/Create+or+edit+Task#Keyboard+shortcuts">documentation</a>.</p>')).addToggle(l=>{let c=Q();l.setValue(c.provideAccessKeys).onChange(d=>A(this,null,function*(){ze({provideAccessKeys:d}),yield this.plugin.saveSettings()}))})}addOneSettingsBlock(t,n,i){let s=t.createEl("details",{cls:"tasks-nested-settings",attr:K({},n.open||i[n.text]?{open:!0}:{})});s.empty(),s.ontoggle=()=>{i[n.text]=s.open,ze({headingOpened:i}),this.plugin.saveSettings()};let a=s.createEl("summary");if(new le.Setting(a).setHeading().setName(n.text),a.createDiv("collapser").createDiv("handle"),n.notice!==null){let o=s.createEl("div",{cls:n.notice.class,text:n.notice.text});n.notice.html!==null&&o.insertAdjacentHTML("beforeend",n.notice.html)}return n.settings.forEach(o=>{if(!(o.featureFlag!==""&&!Fv(o.featureFlag))&&(o.type==="checkbox"?new le.Setting(s).setName(o.name).setDesc(o.description).addToggle(u=>{let l=Q();l.generalSettings[o.settingName]||Ni(o.settingName,o.initialValue),u.setValue(l.generalSettings[o.settingName]).onChange(c=>A(this,null,function*(){Ni(o.settingName,c),yield this.plugin.saveSettings()}))}):o.type==="text"?new le.Setting(s).setName(o.name).setDesc(o.description).addText(u=>{let l=Q();l.generalSettings[o.settingName]||Ni(o.settingName,o.initialValue);let c=d=>A(this,null,function*(){Ni(o.settingName,d),yield this.plugin.saveSettings()});u.setPlaceholder(o.placeholder.toString()).setValue(l.generalSettings[o.settingName].toString()).onChange((0,le.debounce)(c,500,!0))}):o.type==="textarea"?new le.Setting(s).setName(o.name).setDesc(o.description).addTextArea(u=>{let l=Q();l.generalSettings[o.settingName]||Ni(o.settingName,o.initialValue);let c=d=>A(this,null,function*(){Ni(o.settingName,d),yield this.plugin.saveSettings()});u.setPlaceholder(o.placeholder.toString()).setValue(l.generalSettings[o.settingName].toString()).onChange((0,le.debounce)(c,500,!0)),u.inputEl.rows=8,u.inputEl.cols=40}):o.type==="function"&&this.customFunctions[o.settingName](s,this),o.notice!==null)){let u=s.createEl("p",{cls:o.notice.class,text:o.notice.text});o.notice.html!==null&&u.insertAdjacentHTML("beforeend",o.notice.html)}}),s}static parseCommaSeparatedFolders(t){return t.split(",").map(n=>n.trim()).map(n=>n.replace(/^\/|\/$/g,"")).filter(n=>n!=="")}static renderFolderArray(t){return t.join(",")}insertTaskCoreStatusSettings(t,n){let{statusSettings:i}=Q();i.coreStatuses.forEach(a=>{tE(t,a,i.coreStatuses,i,n,n.plugin,!0)}),new le.Setting(t).addButton(a=>{let o="Review and check your Statuses";a.setButtonText(o).setCta().onClick(()=>A(this,null,function*(){let l=window.moment().format("YYYY-MM-DD HH-mm-ss"),c=`Tasks Plugin - ${o} ${l}.md`,d=this.plugin.manifest.version,f=De.getInstance(),m=Bk(i,f,o,d),y=yield this.app.vault.create(c,m);yield this.app.workspace.getLeaf(!0).openFile(y)})),a.setTooltip("Create a new file in the root of the vault, containing a Mermaid diagram of the current status settings.")}).infoEl.remove()}insertCustomTaskStatusSettings(t,n){let{statusSettings:i}=Q();i.customStatuses.forEach(l=>{tE(t,l,i.customStatuses,i,n,n.plugin,!1)}),t.createEl("div"),new le.Setting(t).addButton(l=>{l.setButtonText("Add New Task Status").setCta().onClick(()=>A(this,null,function*(){Le.addStatus(i.customStatuses,new Qe("","","",!1,"TODO")),yield ia(i,n)}))}).infoEl.remove();let a=[["AnuPpuccin Theme",Hk()],["Aura Theme",Vk()],["Border Theme",zk()],["Ebullientworks Theme",Kk()],["ITS Theme & SlRvb Checkboxes",Qk()],["Minimal Theme",Zk()],["Things Theme",Jk()],["LYT Mode Theme (Dark mode only)",Xk()]];for(let[l,c]of a)new le.Setting(t).addButton(f=>{let m=`${l}: Add ${c.length} supported Statuses`;f.setButtonText(m).onClick(()=>A(this,null,function*(){yield cL(c,i,n)}))}).infoEl.remove();new le.Setting(t).addButton(l=>{l.setButtonText("Add All Unknown Status Types").setCta().onClick(()=>A(this,null,function*(){let d=this.plugin.getTasks().map(m=>m.status),f=De.getInstance().findUnknownStatuses(d);f.length!==0&&(f.forEach(m=>{Le.addStatus(i.customStatuses,m)}),yield ia(i,n))}))}).infoEl.remove(),new le.Setting(t).addButton(l=>{l.setButtonText("Reset Custom Status Types to Defaults").setWarning().onClick(()=>A(this,null,function*(){Le.resetAllCustomStatuses(i),yield ia(i,n)}))}).infoEl.remove()}},Ko=Pt;Ko.createFragmentWithHTML=t=>createFragment(n=>n.createDiv().innerHTML=t);function tE(r,e,t,n,i,s,a){let o=r.createEl("pre");o.addClass("row-for-status"),o.textContent=new re(e).previewText();let u=new le.Setting(r);u.infoEl.replaceWith(o),a||u.addExtraButton(l=>{l.setIcon("cross").setTooltip("Delete").onClick(()=>A(this,null,function*(){Le.deleteStatus(t,e)&&(yield ia(n,i))}))}),u.addExtraButton(l=>{l.setIcon("pencil").setTooltip("Edit").onClick(()=>A(this,null,function*(){let c=new rr(s,e,a);c.onClose=()=>A(this,null,function*(){c.saved&&Le.replaceStatus(t,e,c.statusConfiguration())&&(yield ia(n,i))}),c.open()}))}),u.infoEl.remove()}function cL(r,e,t){return A(this,null,function*(){Le.bulkAddStatusCollection(e,r).forEach(i=>{new le.Notice(i)}),yield ia(e,t)})}function ia(r,e){return A(this,null,function*(){ze({statusSettings:r}),Le.applyToStatusRegistry(r,De.getInstance()),yield e.saveSettings(!0)})}function dL(r){let{settingEl:e,infoEl:t,controlEl:n}=r,i=n.querySelector("textarea");i!==null&&(e.style.display="block",t.style.marginRight="0px",i.style.minWidth="-webkit-fill-available")}function dn(r,e){r?r.setVisibility(e):console.warn("Setting has not be initialised. Can update visibility of setting UI - in setSettingVisibility")}var fi=require("obsidian");function fL(r){console.error(r),new fi.Notice(r+`

This message has been written to the console.
`,1e4)}var _c=class extends fi.EditorSuggest{constructor(t,n,i){super(t);this.settings=n,this.plugin=i,t.scope.register([],"Tab",()=>{var a;let s=(a=this.context)==null?void 0:a.editor;return s?(s.exec("indentMore"),!1):!0})}onTrigger(t,n,i){if(!this.settings.autoSuggestInEditor)return null;let s=n.getLine(t.line);return Nv(s,t,n)?{start:{line:t.line,ch:0},end:{line:t.line,ch:s.length},query:s}:null}getSuggestions(t){var c,d,f;let n=t.query,i=t.editor.getCursor(),s=this.plugin.getTasks(),a=s.find(m=>m.taskLocation.path==t.file.path&&m.taskLocation.lineNumber==i.line),o=this.getMarkdownFileInfo(t),u=this.canSaveEdits(o);return((f=(d=(c=Do()).buildSuggestions)==null?void 0:d.call(c,n,i.ch,this.settings,s,u,a))!=null?f:[]).map(m=>de(K({},m),{context:t}))}getMarkdownFileInfo(t){return t.editor.cm.state.field(fi.editorInfoField)}canSaveEdits(t){return t instanceof fi.MarkdownView}renderSuggestion(t,n){n.setText(t.displayText)}selectSuggestion(t,n){return A(this,null,function*(){var l,c,d;let i=t.context.editor;if(t.suggestionType==="empty"){this.close();let f=new KeyboardEvent("keydown",{code:"Enter",key:"Enter"});(c=(l=i==null?void 0:i.cm)==null?void 0:l.contentDOM)==null||c.dispatchEvent(f);return}if(t.taskItDependsOn!=null){let f=il(t.taskItDependsOn,this.plugin.getTasks().map(m=>m.id));if(t.appendText+=` ${f.id}`,t.taskItDependsOn!==f)if(t.context.file.path==f.path){let m=t.taskItDependsOn.originalMarkdown,y={line:t.taskItDependsOn.lineNumber,ch:0},b={line:t.taskItDependsOn.lineNumber,ch:m.length},k=t.context.editor.getRange(y,b);if(k!==m){let _=`Error adding new ID, due to mismatched data in Tasks memory and the editor:
task line in memory: '${t.taskItDependsOn.originalMarkdown}'

task line in editor: '${k}'

file: '${f.path}'
`;fL(_);return}t.context.editor.replaceRange(f.toFileLineString(),y,b)}else br({originalTask:t.taskItDependsOn,newTasks:f})}let s=t.context.editor.getCursor(),a={line:s.line,ch:(d=t.insertAt)!=null?d:s.ch},o=t.insertSkip?{line:s.line,ch:a.ch+t.insertSkip}:void 0;t.context.editor.replaceRange(t.appendText,a,o),t.context.editor.setCursor({line:s.line,ch:a.ch+t.appendText.length});let u=this.getMarkdownFileInfo(t.context);this.canSaveEdits(u)&&(yield u.save())})}};var rE=(r,e)=>{let t,n=new Promise((a,o)=>{t=a});return e(r,a=>{let o=a.map(u=>u.toFileLineString()).join(`
`);t(o)}).open(),n};var nE=(r,e)=>{let t=Sl({line:"",path:""});return new ti({app:r,task:t,onSubmit:e,allTasks:[]})};var iE=r=>({createTaskLineModal:()=>rE(r,nE),executeToggleTaskDoneCommand:(e,t)=>Th(e,t).text});var vc=class extends sE.Plugin{get apiV1(){return iE(this.app)}onload(){return A(this,null,function*(){Rt.registerConsoleLogger(),Vm("info",`loading plugin "${this.manifest.name}" v${this.manifest.version}`),yield this.loadSettings();let{loggingOptions:t}=Q();Rt.configure(t),this.addSettingTab(new Ko({plugin:this})),xw({metadataCache:this.app.metadataCache,vault:this.app.vault,workspace:this.app.workspace}),yield this.loadTaskStatuses();let n=new uc({obsidianEvents:this.app.workspace});this.cache=new Es({metadataCache:this.app.metadataCache,vault:this.app.vault,workspace:this.app.workspace,events:n}),this.inlineRenderer=new pc({plugin:this}),this.queryRenderer=new yc({plugin:this,events:n}),this.registerEditorExtension(Wk()),this.registerEditorSuggest(new _c(this.app,Q(),this)),new Ol({plugin:this})})}loadTaskStatuses(){return A(this,null,function*(){let{statusSettings:t}=Q();Le.applyToStatusRegistry(t,De.getInstance())})}onunload(){var t;Vm("info",`unloading plugin "${this.manifest.name}" v${this.manifest.version}`),(t=this.cache)==null||t.unload()}loadSettings(){return A(this,null,function*(){let t=yield this.loadData();ze(t),t=Q(),ve.getInstance().set(t.globalFilter),ve.getInstance().setRemoveGlobalFilter(t.removeGlobalFilter),tr.getInstance().set(t.globalQuery),yield this.loadTaskStatuses()})}saveSettings(){return A(this,null,function*(){yield this.saveData(Q())})}getTasks(){return this.cache===void 0?[]:this.cache.getTasks()}};
/*!
 * EventEmitter2
 * https://github.com/hij1nx/EventEmitter2
 *
 * Copyright (c) 2013 hij1nx
 * Licensed under the MIT license.
 */
/*!
 * mustache.js - Logic-less {{mustache}} templates with JavaScript
 * http://github.com/janl/mustache.js
 */

/* nosourcemap */