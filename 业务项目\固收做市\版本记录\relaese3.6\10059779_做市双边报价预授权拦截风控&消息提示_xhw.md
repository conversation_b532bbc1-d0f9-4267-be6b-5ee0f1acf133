# 1. 流程图

```
title 做市双边报价预授权拦截

participant 前端
participant 交易网关
participant 风控管理
participant 业务后台
database 数据库

participant 报价管理

前端->交易网关:手动发起做市双边报价/启用策略/策略重发
alt 当日单
交易网关->风控管理:风控处理
alt 对应交易员该笔报价不满足预授权条件
风控管理->业务后台:拦截报价
业务后台->数据库:保存报价信息-状态(风控拦截)
业务后台->数据库:保存风控拦截提示信息
前端<-业务后台:发送风控提示
else 风控通过
风控管理->业务后台:报价信息
业务后台->数据库:保存报价信息-状态(报价中)
风控管理->报价管理:报价信息
end
else 未来单
交易网关->业务后台:报价信息
业务后台->数据库:保存报价信息-状态(已创建)
end
```

# 2. 涉及的数据库、Redis、Kafka 等中间件的修改/新增图或者说明

不涉及

# 3. 前后端交互接口信息

## 做市报价接口--/marketMaker/doQuote

入参新增字段

|字段名|类型|描述|
|---|---|---|
|quoteMode|String|报价方式，0-手动，1-策略(中债估值) 2-策略(全市场最优) 3-策略(做市商最优) 4-策略(全市场最优(中间价))|

# 4. 配置&部署修改信息

不涉及

# 5. 新增技术组件说明

不涉及

# 6. 影响范围

做市双边报价预授权风控

做市双边报价风控流程

策略启用

策略重发

风控提示

# 7. 外部依赖项

不涉及