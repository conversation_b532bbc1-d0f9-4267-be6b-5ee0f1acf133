  

# 1.部署验证

在做市生产nginx服务器(**10.5.80.73,10.5.80.72**)上执行curl命令分别请求Choice部署服务(**10.207.97.35,10.207.97.15**)验证请求响应正常

## 1.1 验证命令

> 以请求**10.207.97.15**为例,请求完成后需再次请求**10.207.97.35**.保证两台服务器均运行正常

### 1.1.1 验证使用净价计算(calcType=0)

1. 计算债券102100912.IB

请求命令

```Bash
curl --location 'http://10.207.97.15/api/Bond/ExecCmd' \--header 'Content-Type: application/json' \--data '{    "cmd": "rpt name=MutualCalc date=20240322 calcTypes=0 columns=emCode,dirtyPrice,netPrice,ytm,exerciseYtm emCodes=102100912.IB tradeManners=1 prices=99.2"}'
```

验证命令结果为

```JSON
{"dataset":[{"table":"MutualCalc","pages":1,"page":0,"size":1,"count":1,"ColumnType":[{"emCode":"String","netPrice":"Double","dirtyPrice":"Double","ytm":"Double","exerciseYtm":"Double"}],"data":[{"emCode":["102100912.IB"],"netPrice":[99.2],"dirtyPrice":[102.383387978142],"ytm":[11.6090261520274],"exerciseYtm":[null]}]}]}
```

---

1. 计算债券2228024.IB

```Bash
curl --location 'http://10.207.97.15/api/Bond/ExecCmd' \--header 'Content-Type: application/json' \--data '{"cmd": "rpt name=MutualCalc date=20240322 calcTypes=0 columns=emCode,dirtyPrice,netPrice,ytm,exerciseYtm emCodes=2228024.IB tradeManners=1 prices=102.8451"}'
```

验证命令结果为

```JSON
{"dataset":[{"table":"MutualCalc","pages":1,"page":0,"size":1,"count":1,"ColumnType":[{"emCode":"String","netPrice":"Double","dirtyPrice":"Double","ytm":"Double","exerciseYtm":"Double"}],"data":[{"emCode":["2228024.IB"],"netPrice":[102.8451],"dirtyPrice":[106.162908219178],"ytm":[3.09384676561505],"exerciseYtm":[2.51677460667751]}]}]}
```

### 1.1.2 验证使用全价计算(calcType=1)

1. 计算债券102001657.IB

```Bash
curl --location 'http://10.207.97.15/api/Bond/ExecCmd' \--header 'Content-Type: application/json' \--data '{"cmd": "rpt name=MutualCalc date=20240321 calcTypes=1 columns=emCode,dirtyPrice,netPrice,ytm,exerciseYtm emCodes=102001657.IB tradeManners=1 prices=17"}'
```

验证命令结果为

```JSON
{"dataset":[{"table":"MutualCalc","pages":1,"page":0,"size":1,"count":1,"ColumnType":[{"emCode":"String","netPrice":"Double","dirtyPrice":"Double","ytm":"Double","exerciseYtm":"Double"}],"data":[{"emCode":["102001657.IB"],"netPrice":[17],"dirtyPrice":[17],"ytm":[107.586706533875],"exerciseYtm":[null]}]}]}
```

---

1. 计算债券 102001441.IB

```Bash
curl --location 'http://10.207.97.15/api/Bond/ExecCmd' \--header 'Content-Type: application/json' \--data '{"cmd": "rpt name=MutualCalc date=20240321 calcTypes=1 columns=emCode,dirtyPrice,netPrice,ytm,exerciseYtm emCodes=102001441.IB tradeManners=1 prices=9.3"}'
```

验证命令结果为

```JSON
{"dataset":[{"table":"MutualCalc","pages":1,"page":0,"size":1,"count":1,"ColumnType":[{"emCode":"String","netPrice":"Double","dirtyPrice":"Double","ytm":"Double","exerciseYtm":"Double"}],"data":[{"emCode":["102001441.IB"],"netPrice":[9.3],"dirtyPrice":[9.3],"ytm":[null],"exerciseYtm":[null]}]}]}
```

### 1.1.3 验证使用到期收益率计算(calcType=2)

1. 计算债券2128025.IB

```Bash
curl --location 'http://10.207.97.15/api/Bond/ExecCmd' \--header 'Content-Type: application/json' \--data '{"cmd": "rpt name=MutualCalc date=20240322 calcTypes=2 columns=emCode,dirtyPrice,netPrice,ytm,exerciseYtm emCodes=2128025.IB tradeManners=1 prices=2.47"}'
```

验证命令结果为

```JSON
{"dataset":[{"table":"MutualCalc","pages":1,"page":0,"size":1,"count":1,"ColumnType":[{"emCode":"String","netPrice":"Double","dirtyPrice":"Double","ytm":"Double","exerciseYtm":"Double"}],"data":[{"emCode":["2128025.IB"],"netPrice":[106.520025977064],"dirtyPrice":[108.675094470215],"ytm":[2.47],"exerciseYtm":[0.672249072044467]}]}]}
```

---

1. 计算债券102381309.IB

```Bash
curl --location 'http://10.207.97.15/api/Bond/ExecCmd' \--header 'Content-Type: application/json' \--data '{"cmd": "rpt name=MutualCalc date=20240322 calcTypes=2 columns=emCode,dirtyPrice,netPrice,ytm,exerciseYtm emCodes=102381309.IB tradeManners=1 prices=2.41"}'
```

验证命令结果为

```JSON
{"dataset":[{"table":"MutualCalc","pages":1,"page":0,"size":1,"count":1,"ColumnType":[{"emCode":"String","netPrice":"Double","dirtyPrice":"Double","ytm":"Double","exerciseYtm":"Double"}],"data":[{"emCode":["102381309.IB"],"netPrice":[102.748477149771],"dirtyPrice":[106.567083707149],"ytm":[2.41],"exerciseYtm":[-8.45480218407036]}]}]}
```

### 1.1.4 验证使用行权收益率计算(calcType=3)

1. 计算债券 2120062.IB

```Bash
curl --location 'http://10.207.97.15/api/Bond/ExecCmd' \--header 'Content-Type: application/json' \--data '{"cmd": "rpt name=MutualCalc date=20240321 calcTypes=3 columns=emCode,dirtyPrice,netPrice,ytm,exerciseYtm emCodes=2120062.IB tradeManners=1 prices=0.5"}'
```

验证命令结果为

```JSON
{"dataset":[{"table":"MutualCalc","pages":1,"page":0,"size":1,"count":1,"ColumnType":[{"emCode":"String","netPrice":"Double","dirtyPrice":"Double","ytm":"Double","exerciseYtm":"Double"}],"data":[{"emCode":["2120062.IB"],"netPrice":[107.270988913548],"dirtyPrice":[109.824868694968],"ytm":[2.57405437916076],"exerciseYtm":[0.5]}]}]}
```

---

1. 计算债券2128045.IB

```Bash
curl --location 'http://10.207.97.15/api/Bond/ExecCmd' \--header 'Content-Type: application/json' \--data '{"cmd": "rpt name=MutualCalc date=20240321 calcTypes=3 columns=emCode,dirtyPrice,netPrice,ytm,exerciseYtm emCodes=2128045.IB tradeManners=1 prices=2.53"}'
```

验证命令结果为

```JSON
{"dataset":[{"table":"MutualCalc","pages":1,"page":0,"size":1,"count":1,"ColumnType":[{"emCode":"String","netPrice":"Double","dirtyPrice":"Double","ytm":"Double","exerciseYtm":"Double"}],"data":[{"emCode":["2128045.IB"],"netPrice":[102.837488337948],"dirtyPrice":[103.974365050277],"ytm":[3.21528084928324],"exerciseYtm":[2.53]}]}]}
```

  

