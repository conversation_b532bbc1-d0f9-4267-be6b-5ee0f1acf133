---
created: 2024-11-20 14:48
updated: 2024-12-12 10:31
---
管理端行权扣税数据同步前置条件:
1.管理端创建一个与APP端证券代码相同的上市公司。
2.在公司下创建一个期权激励计划，行权开始日期大于当前日期，行权结束日期小于当前日期，设置行权代码与APP端行权代码相同。
3.添加一个资金账户与APP端相同的激励对象数据，解锁激励数据。
4.APP端行权，定时任务每日凌晨和下午5点同步行权扣税数据。
补充说明：目前测试环境数仓只同步了run3节点的柜台数据，针对其他节点需手动将行权数据插入数仓。涉及表FJY_ORDERREC和h_fjy_orderrec。
测试数据：目前测试环境针对541220113676账号，创建了冀东公司，已同步部分行权扣税数据。
# 1. 流程图


![[../../../attachments/行权扣税数据同步/行权扣税数据同步.png|../../../../../attachs/行权扣税数据同步.png]]
# 2. 涉及的数据库、Redis、Kafka 等中间件的修改/新增图或者说明
数据中间件修改数仓ORACLE查询当日委托
```sql
select FUNDID, ORDERQTY, ORDERSNO, orderdate, ordertime, taxamt  
FROM (SELECT *  
      FROM (SELECT fundid,  
                   orderqty,  
                   ORDERSNO,  
                   orderdate,  
                   ordertime,  
                   cancelflag,  
                   orderstatus,  
                   taxamt  
            FROM run1.fjy_orderrec  
            UNION ALL  
            SELECT fundid,  
                   orderqty,  
                   ORDERSNO,  
                   orderdate,  
                   ordertime,  
                   cancelflag,  
                   orderstatus,  
                   taxamt  
            FROM run2.fjy_orderrec  
            UNION ALL  
            SELECT fundid,  
                   orderqty,  
                   ORDERSNO,  
                   orderdate,  
                   ordertime,  
                   cancelflag,  
                   orderstatus,  
                   taxamt  
            FROM run3.fjy_orderrec  
            UNION ALL  
            SELECT fundid,  
                   orderqty,  
                   ORDERSNO,  
                   orderdate,  
                   ordertime,  
                   cancelflag,  
                   orderstatus,  
                   taxamt  
            FROM run4.fjy_orderrec  
            UNION ALL  
            SELECT fundid,  
                   orderqty,  
                   ORDERSNO,  
                   orderdate,  
                   ordertime,  
                   cancelflag,  
                   orderstatus,  
                   taxamt  
            FROM run5.fjy_orderrec  
            UNION ALL  
            SELECT fundid,  
                   orderqty,  
                   ORDERSNO,  
                   orderdate,  
                   ordertime,  
                   cancelflag,  
                   orderstatus,  
                   taxamt  
            FROM rzrq.fjy_orderrec  
            UNION ALL  
            SELECT fundid,  
                   orderqty,  
                   ORDERSNO,  
                   orderdate,  
                   ordertime,  
                   cancelflag,  
                   orderstatus,  
                   taxamt  
            FROM run7.fjy_orderrec  
            UNION ALL  
            SELECT fundid,  
                   orderqty,  
                   ORDERSNO,  
                   orderdate,  
                   ordertime,  
                   cancelflag,  
                   orderstatus,  
                   taxamt  
            FROM run8.fjy_orderrec) t  
      WHERE t.cancelflag = 'F'  
        AND t.orderstatus = '1'  
        AND t.ORDERSNO =  #{orderSno}    
AND t.ORDERDATE = #{orderDate}    
AND t.FUNDID = #{fundId}) a
```
数据中间件修改数仓ORACLE查询历史委托
```sql
select FUNDID, ORDERQTY, ORDERSNO, orderdate, ordertime,taxamt FROM  
        (SELECT * FROM (SELECT  fundid, orderqty, ORDERSNO, orderdate, ordertime, cancelflag, orderstatus,taxamt FROM run1_his.h_fjy_orderrec  
        UNION ALL SELECT  fundid, orderqty, ORDERSNO, orderdate, ordertime, cancelflag, orderstatus,taxamt FROM run2_his.h_fjy_orderrec  
        UNION ALL SELECT  fundid, orderqty, ORDERSNO, orderdate, ordertime, cancelflag, orderstatus,taxamt FROM run3_his.h_fjy_orderrec  
        UNION ALL SELECT  fundid, orderqty, ORDERSNO, orderdate, ordertime, cancelflag, orderstatus,taxamt FROM run4_his.h_fjy_orderrec  
        UNION ALL SELECT  fundid, orderqty, ORDERSNO, orderdate, ordertime, cancelflag, orderstatus,taxamt FROM run5_his.h_fjy_orderrec  
        UNION ALL SELECT  fundid, orderqty, ORDERSNO, orderdate, ordertime, cancelflag, orderstatus,taxamt FROM rzrq_his.h_fjy_orderrec  
        UNION ALL SELECT  fundid, orderqty, ORDERSNO, orderdate, ordertime, cancelflag, orderstatus,taxamt FROM run7_his.h_fjy_orderrec  
        UNION ALL SELECT  fundid, orderqty, ORDERSNO, orderdate, ordertime, cancelflag, orderstatus,taxamt FROM run8_his.h_fjy_orderrec  
        ) t  
        WHERE t.cancelflag = 'F' AND t.orderstatus = '1'             
AND t.ORDERSNO = #{orderSno}  
        AND t.ORDERDATE = #{orderDate}  
        AND t.FUNDID = #{fundId}  
        ) a
```
# 3. 前后端交互接口信息
不涉及
# 4. 配置&部署修改信息
不涉及
# 5. 新增技术组件说明
不涉及
# 6. 影响范围
行权扣税列表查询
# 7. 外部依赖项
数据中间件