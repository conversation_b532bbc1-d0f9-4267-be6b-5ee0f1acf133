user  nginx nginx;
worker_processes  8;
worker_cpu_affinity 00000001 00000010 00000100 00001000 00010000 00100000 01000000 10000000;
worker_rlimit_nofile 102400;

error_log  logs/error.log crit;
#error_log  logs/error.log  notice;
#error_log  logs/error.log  info;

pid        logs/nginx.pid;


events {
    use epoll;
    worker_connections  102400;
}


http {
    include       mime.types;
    default_type  application/octet-stream;

    charset utf-8;

    server_names_hash_bucket_size 128;
    client_header_buffer_size 16k;
    large_client_header_buffers 4 32k;
    open_file_cache max=102400 inactive=20s;
    open_file_cache_valid 30s;
    open_file_cache_min_uses 1;
    
    client_max_body_size 150m;
    client_body_buffer_size 128k;
    client_body_temp_path /dev/shm/client_body_temp;
    proxy_connect_timeout 600;
    proxy_read_timeout 600;
    proxy_send_timeout 600;
    proxy_buffer_size 16k;
    proxy_buffers 4 32k;
    proxy_busy_buffers_size 64k;
    proxy_temp_file_write_size 64k;
    proxy_temp_path /dev/shm/proxy_temp;
    
    log_format  main  '$remote_addr - $remote_user [$time_local] "$request" '
                      '$status $body_bytes_sent "$http_referer" '
                      '"$http_user_agent" "$http_x_forwarded_for" "$request_time" "$upstream_response_time"';

    #access_log  logs/access.log  main;
 
    sendfile       on;
    tcp_nopush     on;
    tcp_nodelay    on;

    #keepalive_timeout  0;
    keepalive_timeout  180;

    gzip  on;
    gzip_min_length  0;
    gzip_buffers     4 16k;
    #gzip_http_version 1.0;
    gzip_comp_level 9;
    gzip_types       text/plain application/x-javascript text/css application/xml application/pdf application/octet-stream application/x-download;
    gzip_vary off;
    gzip_proxied any;

    include vhosts/*.conf;


    upstream webServer {
        server *************:30000;
        server *************:30000;
    }

    upstream wechatServer {
        server *************:20000;
	server *************:20000;
    }

    server {
        listen 8899 ssl;
        listen 28899;
        ssl_certificate  /etc/pki/tls/certs/18.cn-server.pem;
        ssl_certificate_key  /etc/pki/tls/certs/18.cn-server.key;
        ssl_session_timeout  5m;
        ssl_protocols  SSLv2 SSLv3 TLSv1 TLSv1.1 TLSv1.2;
        ssl_ciphers  ALL:!ADH:!EXPORT56:RC4+RSA:+HIGH:+MEDIUM:+LOW:+SSLv2:+EXP:ECDHE-RSA-AES128-GCM-SHA256:ECDHE:ECDH:AES:HIGH:!NULL:!aNULL:!MD5:!ADH:!RC4:!DH:!DHE;
        ssl_prefer_server_ciphers  on;
        location /stockholders {
        alias /home/<USER>/stockholders/dist;
          index index.html index.htm;
          try_files $uri $uri/ /index.html;
        }

#        location ~* (/ei/file|/file) {
#          proxy_pass http://*************:30000;
#          client_max_body_size 150m;
#        }

        location /secstkincentive {
            proxy_set_header X-Real-Ip $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forworded-For $http_x_forwarded_for;
            proxy_set_header Host $host:$server_port;
            proxy_pass http://webServer/;
            proxy_connect_timeout 60;
            proxy_send_timeout 1000;
            proxy_read_timeout 1000;
            client_max_body_size 150m;
        }
		error_page 405 =200 @405;
		location @405{
			root /srv/http;
			proxy_pass http://webServer$request_uri;
			proxy_set_header Host $host;
			proxy_set_header X-Real-IP $remote_addr;
			proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
		}
        location /secstkincentive/wechat {
            proxy_set_header X-Real-Ip $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forworded-For $http_x_forwarded_for;
            proxy_set_header Host $host:$server_port;
            proxy_pass http://wechatServer/;
            proxy_connect_timeout 60;
            proxy_send_timeout 1000;
            proxy_read_timeout 1000;
        }
        location /wealth {
                alias /home/<USER>/em-integrated-management-web/wealth/dist;
                index index.html index.htm;
                try_files $uri $uri/ /wealth/index.html;
        }
    }
	

    upstream gq-manage-server{
        server  *************:18877;
        server  *************:18877;
    }

    server {
        client_max_body_size 2048m;
        listen 8877 ssl;
        ssl_certificate  /etc/pki/tls/certs/eastmoneysec.cn.pem;
        ssl_certificate_key  /etc/pki/tls/certs/eastmoneysec.cn.key;
        ssl_session_timeout  5m;
        ssl_protocols  SSLv2 SSLv3 TLSv1 TLSv1.1 TLSv1.2;
        ssl_ciphers  ALL:!ADH:!EXPORT56:RC4+RSA:+HIGH:+MEDIUM:+LOW:+SSLv2:+EXP:ECDHE-RSA-AES128-GCM-SHA256:ECDHE:ECDH:AES:HIGH:!NULL:!aNULL:!MD5:!ADH:!RC4:!DH:!DHE;
        ssl_prefer_server_ciphers  on;
        location /admin {
            alias /usr/local/nginx/html/gq-manage;
            index index.html index.htm;
            try_files $uri $uri/ /index.html;
            }
        location ^~ /gq-manage/ {
            proxy_set_header Host $proxy_host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-NginX-Proxy true;
            proxy_pass   http://gq-manage-server/;
            }
        }
}
