INSERT INTO LCIMS.ERROR_MESSAGE_CONVERT (EID, EITIME, EUTIME, ERR_ACTION, ERR_CODE, ORI_MSG, MATCH_RULES, EXTRACT_RULES, DES_MSG, STATUS) VALUES (214000061457904144, TIMESTAMP '2021-05-14 16:41:41', TIMESTAMP '2021-05-14 16:41:41', '201', '-150905372', 'CODE: -150622020, LEVEL: 1, MSG: -150622020[-150905372]该证券账户[A357034267]无此权限[开通风险警示证券买入]!', '[{"keyword":"开通风险警示证券买入"}]', null, '您尚未开通该市场风险警示股票交易权限，请至业务办理功能开通权限', '1');
INSERT INTO LCIMS.ERROR_MESSAGE_CONVERT (EID, EITIME, EUTIME, ERR_ACTION, ERR_CODE, ORI_MSG, MATCH_RULES, EXTRACT_RULES, DES_MSG, STATUS) VALUES (214000061926634619, TIMESTAMP '2021-05-28 17:48:35', TIMESTAMP '2021-05-28 17:48:35', '201', '-150905065', 'CODE: -150905360, LEVEL: 1, MSG: -150905360[-150905065]该证券账户[0097398356]无此权限[基础设施基金交易权限]!，不允许进行基础设施基金交易!', '[{"keyword":"无此权限[基础设施基金交易权限]"}]', null, '您暂未开通该市场基础设施基金交易权限，无法交易该基金，请至业务办理功能开通权限，详询客服热线95357。', '1');
INSERT INTO LCIMS.ERROR_MESSAGE_CONVERT (EID, EITIME, EUTIME, ERR_ACTION, ERR_CODE, ORI_MSG, MATCH_RULES, EXTRACT_RULES, DES_MSG, STATUS) VALUES (214000061927719263, TIMESTAMP '2021-05-28 19:01:29', TIMESTAMP '2022-08-02 17:05:05', '274', '-150908220', 'CODE: -150908220, LEVEL: 1, MSG: -150908220[-150908220]此证券类别禁止以该组合条件操作', '[{"keyword":"此证券类别禁止以该组合条件操作"}]', null, '该证券无法买卖，请至场内基金功能进行操作', '1');
INSERT INTO LCIMS.ERROR_MESSAGE_CONVERT (EID, EITIME, EUTIME, ERR_ACTION, ERR_CODE, ORI_MSG, MATCH_RULES, EXTRACT_RULES, DES_MSG, STATUS) VALUES (214000062632183407, TIMESTAMP '2021-06-18 16:48:30', TIMESTAMP '2021-06-18 16:48:30', '203', '-990240201', 'CODE: -990240201, LEVEL: 1, MSG: -990240201沪Ａ市场[1]的LOF赎回业务[2g]不允许做基础设施基金证券类别[u]的业务!" ', '[{"keyword":"LOF赎回业务[2g]不允许做基础设施基金证券类别[u]的业务"}]', null, '该基金不支持赎回', '1');
INSERT INTO LCIMS.ERROR_MESSAGE_CONVERT (EID, EITIME, EUTIME, ERR_ACTION, ERR_CODE, ORI_MSG, MATCH_RULES, EXTRACT_RULES, DES_MSG, STATUS) VALUES (214000064177742844, TIMESTAMP '2021-07-30 16:43:30', TIMESTAMP '2022-08-02 17:02:00', '231', '-999003088', '[-999003088]系统正在交收,不允许夜市委托,系统状态status = ''4''', '[{"keyword":"系统正在交收"}]', null, '当前时间无法进行该项业务', '1');
INSERT INTO LCIMS.ERROR_MESSAGE_CONVERT (EID, EITIME, EUTIME, ERR_ACTION, ERR_CODE, ORI_MSG, MATCH_RULES, EXTRACT_RULES, DES_MSG, STATUS) VALUES (214000064440323622, TIMESTAMP '2021-08-06 16:25:40', TIMESTAMP '2022-08-02 17:03:30', '202', '-410413020', 'CODE: -410413020, LEVEL: 1, MSG: -410413020系统正在交收,不允许委托申报,状态status = ''4''', '[{"keyword":"系统正在交收"}]', null, '当前时间无法进行该项业务', '1');
INSERT INTO LCIMS.ERROR_MESSAGE_CONVERT (EID, EITIME, EUTIME, ERR_ACTION, ERR_CODE, ORI_MSG, MATCH_RULES, EXTRACT_RULES, DES_MSG, STATUS) VALUES (214000064440330150, TIMESTAMP '2021-08-06 16:26:36', TIMESTAMP '2022-08-02 17:03:39', '202', '-410413020', 'CODE: -410413020, LEVEL: 1, MSG: -410413020系统初始化前备份,不允许委托申报,状态status = ''5''', '[{"keyword":"系统初始化前备份"}]', null, '当前时间无法进行该项业务', '1');
INSERT INTO LCIMS.ERROR_MESSAGE_CONVERT (EID, EITIME, EUTIME, ERR_ACTION, ERR_CODE, ORI_MSG, MATCH_RULES, EXTRACT_RULES, DES_MSG, STATUS) VALUES (214000066966399219, TIMESTAMP '2021-10-21 19:53:42', TIMESTAMP '2021-10-21 19:53:42', '257', '-150909010', 'CODE:-430007020,LEVEL:2,MSG:-430007020[-150909010]本交易所今日闭市', '[{"keyword":"本交易所今日闭市"}]', null, '当前时间暂不支持港股通交易', '1');
INSERT INTO LCIMS.ERROR_MESSAGE_CONVERT (EID, EITIME, EUTIME, ERR_ACTION, ERR_CODE, ORI_MSG, MATCH_RULES, EXTRACT_RULES, DES_MSG, STATUS) VALUES (214000067740111609, TIMESTAMP '2021-11-12 18:52:15', TIMESTAMP '2021-11-12 18:52:15', '1201', '-150905360', 'CODE:-150905866,LEVEL:1,MSG:-150905866[-150905360]该证券账户[0604258404]无此权限[2k 新创业板权限]', '[{"keyword":"无此权限[2k 新创业板权限]"}]', null, '您暂未开通创业板交易权限或尚未重新签署《创业板投资风险揭示书》，无法买卖创业板股票，请前往开通或签署相关协议。', '1');
INSERT INTO LCIMS.ERROR_MESSAGE_CONVERT (EID, EITIME, EUTIME, ERR_ACTION, ERR_CODE, ORI_MSG, MATCH_RULES, EXTRACT_RULES, DES_MSG, STATUS) VALUES (214000069047228196, TIMESTAMP '2021-12-17 16:00:52', TIMESTAMP '2021-12-17 16:00:52', '201', '-150905396', 'CODE:-150905396,LEVEL:1,MSG:-150905396首日指定交易的客户，下一个交易日才可开展债券现券、通用质押式回购的交易申报', '[{"keyword":"首日指定交易的客户"}]', null, '新开户用户，下一个交易日起才可以参与上交所债券/国债逆回购交易', '1');
INSERT INTO LCIMS.ERROR_MESSAGE_CONVERT (EID, EITIME, EUTIME, ERR_ACTION, ERR_CODE, ORI_MSG, MATCH_RULES, EXTRACT_RULES, DES_MSG, STATUS) VALUES (214000069047249937, TIMESTAMP '2021-12-17 16:02:12', TIMESTAMP '2021-12-17 16:02:12', '274', '-150905396', 'CODE:-150905396,LEVEL:1,MSG:-150905396首日指定交易的客户，下一个交易日才可开展债券现券、通用质押式回购的交易申报', '[{"keyword":"首日指定交易的客户"}]', null, '新开户用户，下一个交易日起才可以参与上交所债券/国债逆回购交易', '1');
INSERT INTO LCIMS.ERROR_MESSAGE_CONVERT (EID, EITIME, EUTIME, ERR_ACTION, ERR_CODE, ORI_MSG, MATCH_RULES, EXTRACT_RULES, DES_MSG, STATUS) VALUES (214000074256345294, TIMESTAMP '2022-05-13 17:18:17', TIMESTAMP '2022-05-13 17:18:17', '231', '-150906250', 'CODE: -150906250, LEVEL: 1, MSG: -150906250[-150906250] 交易类型输入有误: t', '[{"keyword":"交易类型输入有误: t"}]', null, '当前功能暂不支持委托该证券', '1');
INSERT INTO LCIMS.ERROR_MESSAGE_CONVERT (EID, EITIME, EUTIME, ERR_ACTION, ERR_CODE, ORI_MSG, MATCH_RULES, EXTRACT_RULES, DES_MSG, STATUS) VALUES (214000074263994424, TIMESTAMP '2022-05-14 16:31:12', TIMESTAMP '2022-08-02 17:08:35', '1201', '-150670452', 'CODE: -150670452, LEVEL: 1, MSG: -150670452取债券现券交易业务参数[市场:0,证券代码:115101,交易方式:1]失败！', '[{"keyword":"取债券现券交易业务参数"}]', null, '当前时间无法做该项业务', '1');
INSERT INTO LCIMS.ERROR_MESSAGE_CONVERT (EID, EITIME, EUTIME, ERR_ACTION, ERR_CODE, ORI_MSG, MATCH_RULES, EXTRACT_RULES, DES_MSG, STATUS) VALUES (214000074519302914, TIMESTAMP '2022-05-20 16:28:23', TIMESTAMP '2022-05-20 16:28:23', '274', '-150905420', 'CODE: -150905360, LEVEL: 1, MSG: -150905360[-150905420]该证券账户[A134141562]无此权限[债券普通投资者权限]', '[{"keyword":"无此权限[债券普通投资者权限]"}]', null, '您暂未开通该市场债券普通投资者权限，无法交易该债券，请至权限开通功能开通权限。', '1');
INSERT INTO LCIMS.ERROR_MESSAGE_CONVERT (EID, EITIME, EUTIME, ERR_ACTION, ERR_CODE, ORI_MSG, MATCH_RULES, EXTRACT_RULES, DES_MSG, STATUS) VALUES (214000075865277591, TIMESTAMP '2022-06-24 17:24:27', TIMESTAMP '2022-08-02 17:10:27', '279', '-410450061', 'CODE: -410450061, LEVEL: 1, MSG: -410450061证券[123149]不存在价格信息', '[{"keyword":"不存在价格信息"}]', null, '当前证券暂无法进行该项业务', '1');
INSERT INTO LCIMS.ERROR_MESSAGE_CONVERT (EID, EITIME, EUTIME, ERR_ACTION, ERR_CODE, ORI_MSG, MATCH_RULES, EXTRACT_RULES, DES_MSG, STATUS) VALUES (214000075865281189, TIMESTAMP '2022-06-24 17:25:10', TIMESTAMP '2022-08-02 17:11:07', '1244', '-410450060', 'CODE: -410450060, LEVEL: 1, MSG: -410450060股份信息 688327 不存在', '[{"keyword":"不存在"}]', null, '当前证券暂无法进行该项业务', '1');
INSERT INTO LCIMS.ERROR_MESSAGE_CONVERT (EID, EITIME, EUTIME, ERR_ACTION, ERR_CODE, ORI_MSG, MATCH_RULES, EXTRACT_RULES, DES_MSG, STATUS) VALUES (214000090169700510, TIMESTAMP '2023-06-16 15:18:11', TIMESTAMP '2023-06-16 15:18:11', '212', '-160002004', 'CODE: -160002004, LEVEL: 1, MSG: -160002004处理投票方案[1.01]失败:[先行赔付]业务，当前时间不允许委托!', '[{"keyword":"失败:[先行赔付]业务，当前时间不允许委托!"}]', null, '当前非申报时间，无法提交委托', '1');
INSERT INTO LCIMS.ERROR_MESSAGE_CONVERT (EID, EITIME, EUTIME, ERR_ACTION, ERR_CODE, ORI_MSG, MATCH_RULES, EXTRACT_RULES, DES_MSG, STATUS) VALUES (214000090169703551, TIMESTAMP '2023-06-16 15:18:45', TIMESTAMP '2023-06-16 15:18:45', '212', '-160002005', 'CODE: -160002005, LEVEL: 1, MSG: -160002005处理投票方案[1.01]失败:非正常交易时间，不允许交易', '[{"keyword":"失败:非正常交易时间，不允许交易"}]', null, '当前非交易时间，无法提交委托', '1');
INSERT INTO LCIMS.ERROR_MESSAGE_CONVERT (EID, EITIME, EUTIME, ERR_ACTION, ERR_CODE, ORI_MSG, MATCH_RULES, EXTRACT_RULES, DES_MSG, STATUS) VALUES (214000041466542934, TIMESTAMP '2020-04-17 16:15:36', TIMESTAMP '2020-04-17 16:18:52', '1201', '-150906300', 'CODE: -150906300, LEVEL: 1, MSG: -150906300[-150906300]融券负债可用数不足stkdebtsqty = 0', '[{"keyword":"融券负债可用数"}]', '[{"start":"stkdebtsqty ="}]', '还券数量超过最多可还数量：{0}', '1');
INSERT INTO LCIMS.ERROR_MESSAGE_CONVERT (EID, EITIME, EUTIME, ERR_ACTION, ERR_CODE, ORI_MSG, MATCH_RULES, EXTRACT_RULES, DES_MSG, STATUS) VALUES (214000055424715791, TIMESTAMP '2020-10-23 15:19:55', TIMESTAMP '2020-10-23 15:19:55', '201', '-150905041', '-150622020[-150905041]该证券账户[0249614172]无此权限[可转换公司债券权限]!，不允许进行可转债买入与认申购!', '[{"keyword":"可转换公司债券权限"}]', null, '您暂未开通该市场可转债交易权限，无法交易该证券，请前往业务办理功能开通权限。', '1');
INSERT INTO LCIMS.ERROR_MESSAGE_CONVERT (EID, EITIME, EUTIME, ERR_ACTION, ERR_CODE, ORI_MSG, MATCH_RULES, EXTRACT_RULES, DES_MSG, STATUS) VALUES (214000056014746037, TIMESTAMP '2020-11-13 19:11:03', TIMESTAMP '2022-08-02 16:55:41', '201', '-999003088', '[-999003088]系统初始化前备份,不允许夜市委托,系统状态status = ''5''', '[{"keyword":"系统初始化前备份"}]', null, '当前时间无法做该项业务，预计15分钟以后可以进行沪深A股委托，请稍后再试。', '1');
INSERT INTO LCIMS.ERROR_MESSAGE_CONVERT (EID, EITIME, EUTIME, ERR_ACTION, ERR_CODE, ORI_MSG, MATCH_RULES, EXTRACT_RULES, DES_MSG, STATUS) VALUES (214000061457760110, TIMESTAMP '2021-05-14 16:40:58', TIMESTAMP '2021-05-14 16:40:58', '201', '-150905371', 'CODE: -150622020, LEVEL: 1, MSG: -150622020[-150905371]该证券账户[A357034267]无此权限[开通风险警示证券买入]!', '[{"keyword":"开通风险警示证券买入"}]', null, '您尚未开通该市场风险警示股票交易权限，请至业务办理功能开通权限', '1');
INSERT INTO LCIMS.ERROR_MESSAGE_CONVERT (EID, EITIME, EUTIME, ERR_ACTION, ERR_CODE, ORI_MSG, MATCH_RULES, EXTRACT_RULES, DES_MSG, STATUS) VALUES (214000061926637402, TIMESTAMP '2021-05-28 17:49:15', TIMESTAMP '2021-05-28 17:49:15', '1201', '-150905065', 'CODE: -150905065, LEVEL: 1, MSG: -150905065[-150905065]该股东帐号没有基础设施基金权限或权限已过期，不允许进行基础设施基金交易', '[{"keyword":"没有基础设施基金权限或权限已过期"}]', null, '您暂未开通该市场基础设施基金交易权限，无法交易该基金，请至业务办理功能开通权限，详询客服热线95357。', '1');
INSERT INTO LCIMS.ERROR_MESSAGE_CONVERT (EID, EITIME, EUTIME, ERR_ACTION, ERR_CODE, ORI_MSG, MATCH_RULES, EXTRACT_RULES, DES_MSG, STATUS) VALUES (214000061927671430, TIMESTAMP '2021-05-28 19:00:12', TIMESTAMP '2022-08-02 17:04:53', '201', '-150908220', 'CODE: -150908220, LEVEL: 1, MSG: -150908220[-150908220]此证券类别禁止以该组合条件操作', '[{"keyword":"此证券类别禁止以该组合条件操作"}]', null, '该证券无法买卖，请至场内基金功能进行操作', '1');
INSERT INTO LCIMS.ERROR_MESSAGE_CONVERT (EID, EITIME, EUTIME, ERR_ACTION, ERR_CODE, ORI_MSG, MATCH_RULES, EXTRACT_RULES, DES_MSG, STATUS) VALUES (214000062632365012, TIMESTAMP '2021-06-18 16:49:26', TIMESTAMP '2021-06-18 16:49:26', '204', '-150904051', 'CODE: -150904051, LEVEL: 1, MSG: -150904051沪Ａ市场[1]的LOF赎回业务[2g]不允许做基础设施基金证券类别[u]的业务!" ', '[{"keyword":"LOF赎回业务[2g]不允许做基础设施基金证券类别[u]的业务"}]', null, '该基金不支持赎回', '1');
INSERT INTO LCIMS.ERROR_MESSAGE_CONVERT (EID, EITIME, EUTIME, ERR_ACTION, ERR_CODE, ORI_MSG, MATCH_RULES, EXTRACT_RULES, DES_MSG, STATUS) VALUES (214000064440340116, TIMESTAMP '2021-08-06 16:27:26', TIMESTAMP '2022-08-02 17:03:50', '202', '-410413020', 'CODE: -410413020, LEVEL: 1, MSG: -410413020系统正在初始化,不允许委托申报,状态status = ''1''', '[{"keyword":"系统正在初始化"}]', null, '当前时间无法进行该项业务', '1');
INSERT INTO LCIMS.ERROR_MESSAGE_CONVERT (EID, EITIME, EUTIME, ERR_ACTION, ERR_CODE, ORI_MSG, MATCH_RULES, EXTRACT_RULES, DES_MSG, STATUS) VALUES (214000065391716330, TIMESTAMP '2021-08-31 15:58:54', TIMESTAMP '2021-08-31 15:58:54', '201', '-888888004', 'code：-888888004，LEVEL:1，MSG：-888888004最低类别投资者只能交易最低风险产品', '[{"keyword":"最低类别投资者只能交易最低风险产品"}]', null, '您的风险承受能力等级为C1-保守型（风险承受能力最低类别），仅允许交易风险等级为R1-低风险的产品，详询客服热线95357。', '1');
INSERT INTO LCIMS.ERROR_MESSAGE_CONVERT (EID, EITIME, EUTIME, ERR_ACTION, ERR_CODE, ORI_MSG, MATCH_RULES, EXTRACT_RULES, DES_MSG, STATUS) VALUES (214000066159084386, TIMESTAMP '2021-09-22 09:15:05', TIMESTAMP '2021-09-22 09:15:05', '231', '-150905336', 'CODE: -150905360, LEVEL: 1, MSG: -150905360[-150905336]该证券账户[0000009216]无此权限[股转二类合格投资者]!', '[{"keyword":"无此权限[股转二类合格投资者]"}]', null, '该账户未开通股转二类合格投资者权限，暂无法交易该证券。', '1');
INSERT INTO LCIMS.ERROR_MESSAGE_CONVERT (EID, EITIME, EUTIME, ERR_ACTION, ERR_CODE, ORI_MSG, MATCH_RULES, EXTRACT_RULES, DES_MSG, STATUS) VALUES (214000067740114396, TIMESTAMP '2021-11-12 18:53:36', TIMESTAMP '2021-11-12 18:53:36', '201', '-150905360', 'CODE:-150905360,LEVEL:1,MSG:-150905360该证券账户[0186737877]无此权限[开通创业板]', '[{"keyword":"无此权限[开通创业板]"}]', null, '您暂未开通创业板交易权限或尚未重新签署《创业板投资风险揭示书》，无法买卖创业板股票，请前往开通或签署相关协议。', '1');
INSERT INTO LCIMS.ERROR_MESSAGE_CONVERT (EID, EITIME, EUTIME, ERR_ACTION, ERR_CODE, ORI_MSG, MATCH_RULES, EXTRACT_RULES, DES_MSG, STATUS) VALUES (214000067746883219, TIMESTAMP '2021-11-13 13:49:32', TIMESTAMP '2021-11-13 13:49:32', '231', '-150905334', 'CODE:-150905370,LEVEL:1,MSG:-150905370[-150905334]客户无法交易该层级[创新层]证券:该证券账户[0028907329]的权限[股转二类合格投资者]不在有效期内!', '[{"keyword":"权限[股转二类合格投资者]不在有效期内"}]', null, '您的新三板合格投资者权限尚未生效，请于下个交易日再试。', '1');
INSERT INTO LCIMS.ERROR_MESSAGE_CONVERT (EID, EITIME, EUTIME, ERR_ACTION, ERR_CODE, ORI_MSG, MATCH_RULES, EXTRACT_RULES, DES_MSG, STATUS) VALUES (214000067746887807, TIMESTAMP '2021-11-13 13:50:12', TIMESTAMP '2021-11-13 13:50:12', '231', '-150905334', 'CODE:-150905370,LEVEL:1,MSG:-150905370[-150905334]客户无法交易该层级[基础层]证券:该证券账户[0028907329]的权限[股转一类合格投资者]不在有效期内!', '[{"keyword":"权限[股转一类合格投资者]不在有效期内"}]', null, '您的新三板合格投资者权限尚未生效，请于下个交易日再试。', '1');
INSERT INTO LCIMS.ERROR_MESSAGE_CONVERT (EID, EITIME, EUTIME, ERR_ACTION, ERR_CODE, ORI_MSG, MATCH_RULES, EXTRACT_RULES, DES_MSG, STATUS) VALUES (214000036523528808, TIMESTAMP '2019-09-10 14:45:29', TIMESTAMP '2019-11-12 15:49:53', null, '-990203020', '225 201 CODE: -990203020, LEVEL: 1, MSG: -990203020[-990203020]exec run..up_customer_check没有结果集', null, null, '没有该市场股东账号，如有疑问请联系客服95357', '1');
INSERT INTO LCIMS.ERROR_MESSAGE_CONVERT (EID, EITIME, EUTIME, ERR_ACTION, ERR_CODE, ORI_MSG, MATCH_RULES, EXTRACT_RULES, DES_MSG, STATUS) VALUES (214000040810291384, TIMESTAMP '2020-03-23 14:38:35', TIMESTAMP '2024-10-25 16:46:50', '201', '-150905020', 'CODE: -150905020, LEVEL: 1, MSG: -150905020[-150905020]该股东帐号未指定: secuid=A417996917', null, null, '该股东账号未指定，请于交易时间进行指定交易操作', '1');
INSERT INTO LCIMS.ERROR_MESSAGE_CONVERT (EID, EITIME, EUTIME, ERR_ACTION, ERR_CODE, ORI_MSG, MATCH_RULES, EXTRACT_RULES, DES_MSG, STATUS) VALUES (214000038358932982, TIMESTAMP '2019-12-03 14:52:01', TIMESTAMP '2022-08-02 16:44:47', '366', '-480011011', 'CODE: -480011011, LEVEL: 1, MSG:-480011011系统非正常运行状态，不允许中签预冻结资金，状态status=''9''', null, null, '当前时间无法做该项业务', '1');
INSERT INTO LCIMS.ERROR_MESSAGE_CONVERT (EID, EITIME, EUTIME, ERR_ACTION, ERR_CODE, ORI_MSG, MATCH_RULES, EXTRACT_RULES, DES_MSG, STATUS) VALUES (214000038358956613, TIMESTAMP '2019-12-03 14:52:48', TIMESTAMP '2022-08-02 16:45:07', '367', '-411550011', 'CODE: -411550011, LEVEL: 1, MSG:-411550011系统非正常运行状态，不允许中签预冻结资金解冻，状态status=''9''', null, null, '当前时间无法做该项业务', '1');
INSERT INTO LCIMS.ERROR_MESSAGE_CONVERT (EID, EITIME, EUTIME, ERR_ACTION, ERR_CODE, ORI_MSG, MATCH_RULES, EXTRACT_RULES, DES_MSG, STATUS) VALUES (214000044242336620, TIMESTAMP '2020-08-03 17:46:09', TIMESTAMP '2020-08-03 17:46:09', '201', '-150905870', '-150905870[-150905870]该证券账户[0602710246]无此权限[2k 新创业板权限]', null, null, '您暂未开通创业板交易权限或尚未重新签署《创业板投资风险揭示书》，无法买卖创业板股票，请前往开通或签署相关协议。', '1');
INSERT INTO LCIMS.ERROR_MESSAGE_CONVERT (EID, EITIME, EUTIME, ERR_ACTION, ERR_CODE, ORI_MSG, MATCH_RULES, EXTRACT_RULES, DES_MSG, STATUS) VALUES (214000044242345945, TIMESTAMP '2020-08-03 17:47:09', TIMESTAMP '2020-08-03 17:47:09', '1201', '-150905876', '-150905876[-150905876]该证券账户[0604379073]无此权限[2k 新创业板权限]', null, null, '您暂未开通创业板交易权限或尚未重新签署《创业板投资风险揭示书》，无法买卖创业板股票，请前往开通或签署相关协议。', '1');
INSERT INTO LCIMS.ERROR_MESSAGE_CONVERT (EID, EITIME, EUTIME, ERR_ACTION, ERR_CODE, ORI_MSG, MATCH_RULES, EXTRACT_RULES, DES_MSG, STATUS) VALUES (214000036523502252, TIMESTAMP '2019-09-10 14:44:50', TIMESTAMP '2022-08-02 16:43:52', null, '-150906060', 'CODE: -150906060, LEVEL: 1, MSG: -150906060[-150906060]系统状态非正常运行,不允许普通委托,状态status =', null, null, '当前时间无法做该项业务', '1');
INSERT INTO LCIMS.ERROR_MESSAGE_CONVERT (EID, EITIME, EUTIME, ERR_ACTION, ERR_CODE, ORI_MSG, MATCH_RULES, EXTRACT_RULES, DES_MSG, STATUS) VALUES (214000044242326273, TIMESTAMP '2020-08-03 17:45:39', TIMESTAMP '2020-08-03 17:45:39', '201', '-150905866', '-150905866[-150905866]该证券账户[0052343942]无此权限[新创业板权限]', null, null, '您暂未开通创业板交易权限或尚未重新签署《创业板投资风险揭示书》，无法买卖创业板股票，请前往开通或签署相关协议。', '1');
INSERT INTO LCIMS.ERROR_MESSAGE_CONVERT (EID, EITIME, EUTIME, ERR_ACTION, ERR_CODE, ORI_MSG, MATCH_RULES, EXTRACT_RULES, DES_MSG, STATUS) VALUES (214000044242341350, TIMESTAMP '2020-08-03 17:46:37', TIMESTAMP '2020-08-03 17:46:37', '201', '-150905871', '-150905871[-150905871]该证券账户[0130528317]无此权限[新创业板权限]', null, null, '您暂未开通创业板交易权限或尚未重新签署《创业板投资风险揭示书》，无法买卖创业板股票，请前往开通或签署相关协议。', '1');
INSERT INTO LCIMS.ERROR_MESSAGE_CONVERT (EID, EITIME, EUTIME, ERR_ACTION, ERR_CODE, ORI_MSG, MATCH_RULES, EXTRACT_RULES, DES_MSG, STATUS) VALUES (214000044242354278, TIMESTAMP '2020-08-03 17:47:59', TIMESTAMP '2020-08-03 17:47:59', '1201', '-150905867', '-150905867该证券账户[0602710246]无此权限[2k 新创业板权限]', null, null, '您暂未开通创业板交易权限或尚未重新签署《创业板投资风险揭示书》，无法买卖创业板股票，请前往开通或签署相关协议。', '1');
INSERT INTO LCIMS.ERROR_MESSAGE_CONVERT (EID, EITIME, EUTIME, ERR_ACTION, ERR_CODE, ORI_MSG, MATCH_RULES, EXTRACT_RULES, DES_MSG, STATUS) VALUES (214000036727678391, TIMESTAMP '2019-09-19 14:20:16', TIMESTAMP '2019-11-12 15:50:04', null, '-150905620', 'CODE: -150905360, LEVEL: 1, MSG: -150905360[-150905620]该证券账户[开通退市证券买入权限]!', null, null, '该证券账户尚未开通退市证券买入权限', '1');
INSERT INTO LCIMS.ERROR_MESSAGE_CONVERT (EID, EITIME, EUTIME, ERR_ACTION, ERR_CODE, ORI_MSG, MATCH_RULES, EXTRACT_RULES, DES_MSG, STATUS) VALUES (214000036727706551, TIMESTAMP '2019-09-19 14:21:23', TIMESTAMP '2022-08-02 16:44:13', null, '-990268020', 'CODE: -410413020, LEVEL: 1, MSG: -410413020[-990268020]系统状态非正常运行,不允许普通委托撤单,状态status = ''9''', null, null, '当前时间无法做该项业务', '1');
INSERT INTO LCIMS.ERROR_MESSAGE_CONVERT (EID, EITIME, EUTIME, ERR_ACTION, ERR_CODE, ORI_MSG, MATCH_RULES, EXTRACT_RULES, DES_MSG, STATUS) VALUES (214000037705477985, TIMESTAMP '2019-11-04 19:01:18', TIMESTAMP '2019-11-12 15:38:02', null, '-150911058', '[-150911058]该证券帐号[%s]的创业板权限有效期信息没有找到', null, null, '您的信用账户暂未开通创业板交易权限，无法进行该项业务', '1');
INSERT INTO LCIMS.ERROR_MESSAGE_CONVERT (EID, EITIME, EUTIME, ERR_ACTION, ERR_CODE, ORI_MSG, MATCH_RULES, EXTRACT_RULES, DES_MSG, STATUS) VALUES (214000037792086519, TIMESTAMP '2019-11-07 19:02:07', TIMESTAMP '2019-11-12 15:38:37', '1201', '-150905310', 'CODE: -150905310, LEVEL: 1, MSG: -150905310该证券账户[0604797699]无此权限[03 开通退市证券买入权限]', null, null, '该股东账户尚未开通退市证券买入权限', '1');
INSERT INTO LCIMS.ERROR_MESSAGE_CONVERT (EID, EITIME, EUTIME, ERR_ACTION, ERR_CODE, ORI_MSG, MATCH_RULES, EXTRACT_RULES, DES_MSG, STATUS) VALUES (214000037792093309, TIMESTAMP '2019-11-07 19:02:56', TIMESTAMP '2019-11-12 15:38:49', '402', '*********', 'CODE: -*********, LEVEL: 1, MSG: -*********[*********]该银行非交易时间orgid =', null, null, '抱歉，该银行非交易时间，暂不支持该项业务', '1');
INSERT INTO LCIMS.ERROR_MESSAGE_CONVERT (EID, EITIME, EUTIME, ERR_ACTION, ERR_CODE, ORI_MSG, MATCH_RULES, EXTRACT_RULES, DES_MSG, STATUS) VALUES (214000037821648239, TIMESTAMP '2019-11-08 19:10:51', TIMESTAMP '2019-11-12 15:45:09', '1226&1228', '*********', 'CODE: -*********, LEVEL: 1, MSG: -*********[*********]非交易日期禁止交易orgid = ''4205'' bankcode = ''3007'' moneytype = ''0'' sysdate = ******** phydate = ********', null, null, '抱歉，该银行非交易时间，暂不支持该项业务', '1');
INSERT INTO LCIMS.ERROR_MESSAGE_CONVERT (EID, EITIME, EUTIME, ERR_ACTION, ERR_CODE, ORI_MSG, MATCH_RULES, EXTRACT_RULES, DES_MSG, STATUS) VALUES (214000037821660864, TIMESTAMP '2019-11-08 19:11:34', TIMESTAMP '2019-11-12 15:45:22', '1226', '*********', 'CODE: -*********, LEVEL: 1, MSG: -*********[*********]该客户当日有委托,需保留最低手续费! orgid = ''5410'' fundid = ************ moneytype = ''0'' fundavl = 178647.08 fundeffect = 178647.08', null, null, '当日有交易委托，不允许全部转出，请保留0.1元', '1');
INSERT INTO LCIMS.ERROR_MESSAGE_CONVERT (EID, EITIME, EUTIME, ERR_ACTION, ERR_CODE, ORI_MSG, MATCH_RULES, EXTRACT_RULES, DES_MSG, STATUS) VALUES (214000037821684217, TIMESTAMP '2019-11-08 19:13:43', TIMESTAMP '2019-11-12 15:05:53', '1216', '-*********', 'CODE: -*********, LEVEL: 1, MSG: -*********[-*********]融券卖出价格不能低于融券卖出控制价格:3.640', null, '[{"start":"控制价格:"}]', '根据交易所规则，融券卖出委托价格不能低于最新价：{0}', '1');
INSERT INTO LCIMS.ERROR_MESSAGE_CONVERT (EID, EITIME, EUTIME, ERR_ACTION, ERR_CODE, ORI_MSG, MATCH_RULES, EXTRACT_RULES, DES_MSG, STATUS) VALUES (214000037821687271, TIMESTAMP '2019-11-08 19:14:26', TIMESTAMP '2019-11-11 15:12:05', '1201', '-153175023', 'CODE: -153175040, LEVEL: 1, MSG: -153175040[-153175023]融券头寸[2000]证券[000789]不存在', null, null, '可用股份数不足！', '1');
INSERT INTO LCIMS.ERROR_MESSAGE_CONVERT (EID, EITIME, EUTIME, ERR_ACTION, ERR_CODE, ORI_MSG, MATCH_RULES, EXTRACT_RULES, DES_MSG, STATUS) VALUES (214000037821730016, TIMESTAMP '2019-11-08 19:18:25', TIMESTAMP '2019-11-12 15:46:14', '1201', '-150906330', 'CODE: -150906330, LEVEL: 1, MSG: -150906330[-150906330]融券负债可用数不足stkdebtsqty = 500"', null, '[{"start":"stkdebtsqty ="}]', '还券数量超过最多可还数量：{0}', '1');
INSERT INTO LCIMS.ERROR_MESSAGE_CONVERT (EID, EITIME, EUTIME, ERR_ACTION, ERR_CODE, ORI_MSG, MATCH_RULES, EXTRACT_RULES, DES_MSG, STATUS) VALUES (214000037821762449, TIMESTAMP '2019-11-08 19:22:01', TIMESTAMP '2019-11-08 19:22:01', '1201', '-990221020', 'CODE: -990221020, LEVEL: 1, MSG: -990221020[-990221020]无此证券代码!\nstkcode = 113503 market = 0', null, null, '无此证券代码', '1');
INSERT INTO LCIMS.ERROR_MESSAGE_CONVERT (EID, EITIME, EUTIME, ERR_ACTION, ERR_CODE, ORI_MSG, MATCH_RULES, EXTRACT_RULES, DES_MSG, STATUS) VALUES (214000107969693134, TIMESTAMP '2024-10-25 16:47:32', TIMESTAMP '2024-10-25 16:47:32', '201', '-990224018', 'CODE::-990224018,,LEVEL::1,,MSG::-990224018[-990224018]股东开户当天不允许委托!', null, null, '当日新开户用户，下个交易日起才可以交易股票', '1');
INSERT INTO LCIMS.ERROR_MESSAGE_CONVERT (EID, EITIME, EUTIME, ERR_ACTION, ERR_CODE, ORI_MSG, MATCH_RULES, EXTRACT_RULES, DES_MSG, STATUS) VALUES (214000002195663110, TIMESTAMP '2016-04-07 15:01:10', TIMESTAMP '2016-04-08 09:41:24', '201', '-400411002', 'CODE: -400411002, LEVEL: 1, MSG: -400411002委托数量[9223372036854775807]超过系统支持的上限[2147483647],请修改委托数量!', null, null, '委托数量无效！', '1');
INSERT INTO LCIMS.ERROR_MESSAGE_CONVERT (EID, EITIME, EUTIME, ERR_ACTION, ERR_CODE, ORI_MSG, MATCH_RULES, EXTRACT_RULES, DES_MSG, STATUS) VALUES (214000002195663111, TIMESTAMP '2016-04-07 15:01:10', TIMESTAMP '2016-04-07 17:01:12', '310', '-411518015', 'CODE: -411518015, LEVEL: 1, MSG: -411518015查询起始日期201600120非法', null, null, '日期格式有误！', '1');
INSERT INTO LCIMS.ERROR_MESSAGE_CONVERT (EID, EITIME, EUTIME, ERR_ACTION, ERR_CODE, ORI_MSG, MATCH_RULES, EXTRACT_RULES, DES_MSG, STATUS) VALUES (214000043059185303, TIMESTAMP '2020-07-01 18:58:21', TIMESTAMP '2022-08-02 16:45:26', '201', '-150906153', 'CODE: -150906153, LEVEL: 1, MSG: -150906153此功能不允许做交易
 market = ''6'' stkcode = ''889999''', null, null, '此功能无法做交易', '1');
INSERT INTO LCIMS.ERROR_MESSAGE_CONVERT (EID, EITIME, EUTIME, ERR_ACTION, ERR_CODE, ORI_MSG, MATCH_RULES, EXTRACT_RULES, DES_MSG, STATUS) VALUES (214000002195663113, TIMESTAMP '2016-04-07 15:01:10', TIMESTAMP '2017-01-23 11:03:02', '310', '11', 'CODE: 11, LEVEL: 99999, MSG: 11[Microsoft][ODBC SQL Server Driver][DBNETLIB]ConnectionWrite (send()). [Microsoft][ODBC SQL Server Driver][DBNETLIB]一般性网络错误。请检查网络文档。', null, null, '服务器繁忙，请稍后再试！', '1');
INSERT INTO LCIMS.ERROR_MESSAGE_CONVERT (EID, EITIME, EUTIME, ERR_ACTION, ERR_CODE, ORI_MSG, MATCH_RULES, EXTRACT_RULES, DES_MSG, STATUS) VALUES (214000002195663114, TIMESTAMP '2016-04-07 15:01:10', TIMESTAMP '2016-04-07 17:01:32', '310', '-411518020', 'CODE: -411518020, LEVEL: 1, MSG: -411518020查询结束日期-118847629非法', null, null, '日期格式有误！', '1');
INSERT INTO LCIMS.ERROR_MESSAGE_CONVERT (EID, EITIME, EUTIME, ERR_ACTION, ERR_CODE, ORI_MSG, MATCH_RULES, EXTRACT_RULES, DES_MSG, STATUS) VALUES (214000002195663115, TIMESTAMP '2016-04-07 15:01:10', TIMESTAMP '2016-04-07 17:01:39', '312', '-411519015', 'CODE: -411519015, LEVEL: 1, MSG: -411519015查询起始日期2015121非法', null, null, '日期格式有误！', '1');
INSERT INTO LCIMS.ERROR_MESSAGE_CONVERT (EID, EITIME, EUTIME, ERR_ACTION, ERR_CODE, ORI_MSG, MATCH_RULES, EXTRACT_RULES, DES_MSG, STATUS) VALUES (214000002195663116, TIMESTAMP '2016-04-07 15:01:10', TIMESTAMP '2022-08-02 16:36:30', '211', '-411546090', 'CODE: -411546090, LEVEL: 1, MSG: -411546090此系统状态下不允许修改', null, null, '当前时间无法修改', '1');
INSERT INTO LCIMS.ERROR_MESSAGE_CONVERT (EID, EITIME, EUTIME, ERR_ACTION, ERR_CODE, ORI_MSG, MATCH_RULES, EXTRACT_RULES, DES_MSG, STATUS) VALUES (214000002195663117, TIMESTAMP '2016-04-07 15:01:10', TIMESTAMP '2016-04-07 17:01:51', '318', '-411547010', 'CODE: -411547010, LEVEL: 1, MSG: -411547010客户:540700203147 交易密码错误', null, null, '交易密码错误！', '1');
INSERT INTO LCIMS.ERROR_MESSAGE_CONVERT (EID, EITIME, EUTIME, ERR_ACTION, ERR_CODE, ORI_MSG, MATCH_RULES, EXTRACT_RULES, DES_MSG, STATUS) VALUES (214000002195663118, TIMESTAMP '2016-04-07 15:01:10', TIMESTAMP '2016-04-07 17:01:56', '313', '-411610020', 'CODE: -411610020, LEVEL: 1, MSG: -411610020客户:-565686484 交易密码错误', null, null, '交易密码错误！', '1');
INSERT INTO LCIMS.ERROR_MESSAGE_CONVERT (EID, EITIME, EUTIME, ERR_ACTION, ERR_CODE, ORI_MSG, MATCH_RULES, EXTRACT_RULES, DES_MSG, STATUS) VALUES (214000002195663119, TIMESTAMP '2016-04-07 15:01:10', TIMESTAMP '2016-04-07 17:02:04', '201', '-990221021', 'CODE: -990221021, LEVEL: 1, MSG: -990221021[-990221021]市场[1]证券代码[611618]的证券交易属性信息不存在!', null, null, '该证券代码不存在！', '1');
INSERT INTO LCIMS.ERROR_MESSAGE_CONVERT (EID, EITIME, EUTIME, ERR_ACTION, ERR_CODE, ORI_MSG, MATCH_RULES, EXTRACT_RULES, DES_MSG, STATUS) VALUES (214000045080424196, TIMESTAMP '2020-08-28 16:25:07', TIMESTAMP '2020-08-28 16:25:07', '201', '-150128010', '[-150128200]报价回购提前终止数量10加上当日已提前终止数量0大于按照\n（该品种巨额购回额度10.00/100）\n计算出来的最大可提前终止数量0！', null, null, '该品种今日购回额度已满，请于下一交易日重新操作。', '1');
INSERT INTO LCIMS.ERROR_MESSAGE_CONVERT (EID, EITIME, EUTIME, ERR_ACTION, ERR_CODE, ORI_MSG, MATCH_RULES, EXTRACT_RULES, DES_MSG, STATUS) VALUES (214000055453161835, TIMESTAMP '2020-10-26 10:32:39', TIMESTAMP '2022-08-02 16:45:47', '426', '-1002070351', 'ErrMsg=-1002070351系统正在初始化,不允许夜市委托,系统状态status = ''1''', null, null, '当前时间无法做该项业务', '1');
INSERT INTO LCIMS.ERROR_MESSAGE_CONVERT (EID, EITIME, EUTIME, ERR_ACTION, ERR_CODE, ORI_MSG, MATCH_RULES, EXTRACT_RULES, DES_MSG, STATUS) VALUES (214000066129928984, TIMESTAMP '2021-09-18 14:54:38', TIMESTAMP '2021-09-18 14:54:38', '201', '-990224020', 'CODE: -990224020, LEVEL: 1, MSG: -990224020[-990224020]无此股东帐号: " } - 54********01', null, null, '您暂未开通该市场股东账号，详询客服热线95357。', '1');
INSERT INTO LCIMS.ERROR_MESSAGE_CONVERT (EID, EITIME, EUTIME, ERR_ACTION, ERR_CODE, ORI_MSG, MATCH_RULES, EXTRACT_RULES, DES_MSG, STATUS) VALUES (214000002195663124, TIMESTAMP '2016-04-07 15:01:10', TIMESTAMP '2016-04-07 17:02:32', '201', '-150900143', 'CODE: -150900143, LEVEL: 1, MSG: -150900143执行当前操作后，货币资产账户[orgid:5403,fundid:540300058363,moneytype:0] 可用资金[0.07]小于有委托保留最低金额[0.10]', null, null, '账户可用资金不足！', '1');
INSERT INTO LCIMS.ERROR_MESSAGE_CONVERT (EID, EITIME, EUTIME, ERR_ACTION, ERR_CODE, ORI_MSG, MATCH_RULES, EXTRACT_RULES, DES_MSG, STATUS) VALUES (214000002195663125, TIMESTAMP '2016-04-07 15:01:10', TIMESTAMP '2016-04-07 17:02:37', '201', '-150904001', 'CODE: -150904001, LEVEL: 1, MSG: -150904001[-150904001]委托价格[1.126]不是该证券代码[600478]最小价位[10]厘的整数倍', null, null, '委托价格无效！', '1');
INSERT INTO LCIMS.ERROR_MESSAGE_CONVERT (EID, EITIME, EUTIME, ERR_ACTION, ERR_CODE, ORI_MSG, MATCH_RULES, EXTRACT_RULES, DES_MSG, STATUS) VALUES (214000002195663126, TIMESTAMP '2016-04-07 15:01:10', TIMESTAMP '2022-08-02 16:36:51', '201', '-150904020', 'CODE: -150904020, LEVEL: 1, MSG: -150904020[-150904020]夜市委托国债''109511''的计息日期20160121与系统委托日期20160122不符', null, null, '当前时间无法做国债委托', '1');
INSERT INTO LCIMS.ERROR_MESSAGE_CONVERT (EID, EITIME, EUTIME, ERR_ACTION, ERR_CODE, ORI_MSG, MATCH_RULES, EXTRACT_RULES, DES_MSG, STATUS) VALUES (214000002195663128, TIMESTAMP '2016-04-07 15:01:10', TIMESTAMP '2016-04-07 17:02:54', '201', '-150904060', 'CODE: -150904060, LEVEL: 1, MSG: -150904060[-150904060]该证券禁止买入', null, null, '该证券禁止买入！', '1');
INSERT INTO LCIMS.ERROR_MESSAGE_CONVERT (EID, EITIME, EUTIME, ERR_ACTION, ERR_CODE, ORI_MSG, MATCH_RULES, EXTRACT_RULES, DES_MSG, STATUS) VALUES (214000002195663131, TIMESTAMP '2016-04-07 15:01:10', TIMESTAMP '2016-04-07 17:03:08', '201', '-150904150', 'CODE: -150904150, LEVEL: 1, MSG: -150904150[-150904150]委托数量高于最高交易数量maxqty = 999999900', null, null, '委托数量大于最高交易数量！', '1');
INSERT INTO LCIMS.ERROR_MESSAGE_CONVERT (EID, EITIME, EUTIME, ERR_ACTION, ERR_CODE, ORI_MSG, MATCH_RULES, EXTRACT_RULES, DES_MSG, STATUS) VALUES (214000002195663132, TIMESTAMP '2016-04-07 15:01:10', TIMESTAMP '2016-04-07 17:03:13', '201', '-150904160', 'CODE: -150904160, LEVEL: 1, MSG: -150904160[-150904160]委托数量低于最低交易数量minqty = 50000', null, null, '委托数量小于最低交易数量！', '1');
INSERT INTO LCIMS.ERROR_MESSAGE_CONVERT (EID, EITIME, EUTIME, ERR_ACTION, ERR_CODE, ORI_MSG, MATCH_RULES, EXTRACT_RULES, DES_MSG, STATUS) VALUES (214000002195663133, TIMESTAMP '2016-04-07 15:01:10', TIMESTAMP '2024-07-29 08:53:54', '201', '-150904180', 'CODE: -150904180, LEVEL: 1, MSG: -150904180[-150904180]委托数量高于最高交易数量maxqty = 1000000', null, '[{"start":"maxqty ="}]', '该证券单笔委托数量上限为：{0}，当前委托数量超过上限，请重新发起委托', '1');
INSERT INTO LCIMS.ERROR_MESSAGE_CONVERT (EID, EITIME, EUTIME, ERR_ACTION, ERR_CODE, ORI_MSG, MATCH_RULES, EXTRACT_RULES, DES_MSG, STATUS) VALUES (214000002195663141, TIMESTAMP '2016-04-07 15:01:10', TIMESTAMP '2024-05-20 15:39:54', '201', '-150906022', 'CODE:-150906022,LEVEL:1,MSG:-150906022[-150906022]不足100股的零股需一次性卖出', null, '[{"start":"不足","end":"的零股"}]', '不足{0}的零股部分不能分拆委托！', '1');
INSERT INTO LCIMS.ERROR_MESSAGE_CONVERT (EID, EITIME, EUTIME, ERR_ACTION, ERR_CODE, ORI_MSG, MATCH_RULES, EXTRACT_RULES, DES_MSG, STATUS) VALUES (214000002195663142, TIMESTAMP '2016-04-07 15:01:10', TIMESTAMP '2016-04-07 17:04:14', '201', '-150906051', 'CODE: -150906051, LEVEL: 1, MSG: -150906051该业务禁止夜市委托', null, null, '该业务禁止夜市委托！', '1');
INSERT INTO LCIMS.ERROR_MESSAGE_CONVERT (EID, EITIME, EUTIME, ERR_ACTION, ERR_CODE, ORI_MSG, MATCH_RULES, EXTRACT_RULES, DES_MSG, STATUS) VALUES (214000002195663143, TIMESTAMP '2016-04-07 15:01:10', TIMESTAMP '2022-08-02 16:37:10', '201', '-150906085', 'CODE: -150906085, LEVEL: 1, MSG: -150906085[-150906085]根据设置定价申购只能在该时刻[09260000]后做该业务', null, null, '当前时间无法做该业务', '1');
INSERT INTO LCIMS.ERROR_MESSAGE_CONVERT (EID, EITIME, EUTIME, ERR_ACTION, ERR_CODE, ORI_MSG, MATCH_RULES, EXTRACT_RULES, DES_MSG, STATUS) VALUES (214000002195663145, TIMESTAMP '2016-04-07 15:01:10', TIMESTAMP '2022-08-02 16:37:30', '201', '-150906090', 'CODE: -150906090, LEVEL: 1, MSG: -150906090新股配售同一只证券代码不允许重复委托!', null, null, '新股、新债申购时，同一证券不能重复委托', '1');
INSERT INTO LCIMS.ERROR_MESSAGE_CONVERT (EID, EITIME, EUTIME, ERR_ACTION, ERR_CODE, ORI_MSG, MATCH_RULES, EXTRACT_RULES, DES_MSG, STATUS) VALUES (214000002195663146, TIMESTAMP '2016-04-07 15:01:10', TIMESTAMP '2016-04-07 17:04:33', '201', '-150906130', 'CODE: -150906130, LEVEL: 1, MSG: -150906130[-150906130]资金可用数不足,尚需1425.03', null, null, '可用资金不足！', '1');
INSERT INTO LCIMS.ERROR_MESSAGE_CONVERT (EID, EITIME, EUTIME, ERR_ACTION, ERR_CODE, ORI_MSG, MATCH_RULES, EXTRACT_RULES, DES_MSG, STATUS) VALUES (214000002195663147, TIMESTAMP '2016-04-07 15:01:10', TIMESTAMP '2016-04-07 17:04:39', '201', '-150906135', 'CODE: -150906135, LEVEL: 1, MSG: -150906135[-150906135]股份可用数不足,尚需100', null, null, '可用股份数不足！', '1');
INSERT INTO LCIMS.ERROR_MESSAGE_CONVERT (EID, EITIME, EUTIME, ERR_ACTION, ERR_CODE, ORI_MSG, MATCH_RULES, EXTRACT_RULES, DES_MSG, STATUS) VALUES (214000002195663152, TIMESTAMP '2016-04-07 15:01:10', TIMESTAMP '2016-10-20 09:59:42', '201', '90001', 'CODE: 90001, LEVEL: 0, MSG: 90001没有qty项的数据', null, null, '数据异常，请联系客服95357', '1');
INSERT INTO LCIMS.ERROR_MESSAGE_CONVERT (EID, EITIME, EUTIME, ERR_ACTION, ERR_CODE, ORI_MSG, MATCH_RULES, EXTRACT_RULES, DES_MSG, STATUS) VALUES (214000002195663154, TIMESTAMP '2016-04-07 15:01:10', TIMESTAMP '2016-04-07 17:05:17', '201', '-410411005', 'CODE: -990203010, LEVEL: 1, MSG: -990203010[-410411005]客户号540600153003交易密码错误!', null, null, '交易密码错误!', '1');
INSERT INTO LCIMS.ERROR_MESSAGE_CONVERT (EID, EITIME, EUTIME, ERR_ACTION, ERR_CODE, ORI_MSG, MATCH_RULES, EXTRACT_RULES, DES_MSG, STATUS) VALUES (214000002195663155, TIMESTAMP '2016-04-07 15:01:10', TIMESTAMP '2016-04-08 09:39:53', '201', '-990203010', 'CODE: -990203010, LEVEL: 1, MSG: -990203010[-990203010]该证券账户[0193155894]无此权限[开通创业板]!', null, '[{"start":"无此权限"}]', '该证券账户无此权限{0}', '1');
INSERT INTO LCIMS.ERROR_MESSAGE_CONVERT (EID, EITIME, EUTIME, ERR_ACTION, ERR_CODE, ORI_MSG, MATCH_RULES, EXTRACT_RULES, DES_MSG, STATUS) VALUES (214000002662810963, TIMESTAMP '2016-05-30 15:09:40', TIMESTAMP '2022-08-02 16:38:03', '201', '-150906501', 'CODE: -430007020, LEVEL: 2, MSG: -430007020[-150906501]参考汇率表中汇率记录[market=5,moneytype=1]的使用日期[20160527]不等于当前委托日期[20160530]', null, null, '当前时间无法委托', '1');
INSERT INTO LCIMS.ERROR_MESSAGE_CONVERT (EID, EITIME, EUTIME, ERR_ACTION, ERR_CODE, ORI_MSG, MATCH_RULES, EXTRACT_RULES, DES_MSG, STATUS) VALUES (214000002726319762, TIMESTAMP '2016-06-06 13:35:18', TIMESTAMP '2016-06-07 14:49:57', '105', '-120140040', 'CODE: -120140040, LEVEL: 1, MSG: -120140040已经是该状态[0]', null, null, '该账号未锁定！', '1');
INSERT INTO LCIMS.ERROR_MESSAGE_CONVERT (EID, EITIME, EUTIME, ERR_ACTION, ERR_CODE, ORI_MSG, MATCH_RULES, EXTRACT_RULES, DES_MSG, STATUS) VALUES (214000003211949426, TIMESTAMP '2016-08-09 14:20:06', TIMESTAMP '2022-08-02 16:38:24', '505', '-240116040', 'CODE: -410805030, LEVEL: 1, MSG: -410805030[-240116040]基金代码状态错!\nofcode = D70002 status = 9', null, null, '基金处于封闭状态，无法进行转换操作', '1');
INSERT INTO LCIMS.ERROR_MESSAGE_CONVERT (EID, EITIME, EUTIME, ERR_ACTION, ERR_CODE, ORI_MSG, MATCH_RULES, EXTRACT_RULES, DES_MSG, STATUS) VALUES (214000004756530236, TIMESTAMP '2016-10-11 13:59:48', TIMESTAMP '2022-08-02 16:38:51', '402', '-140022606', 'CODE: -410620020, LEVEL: 1, MSG: -410620020[-140022606]不允许转帐资金帐户之间内转', null, null, '不支持辅账户和辅账户之间进行资金划转', '1');
INSERT INTO LCIMS.ERROR_MESSAGE_CONVERT (EID, EITIME, EUTIME, ERR_ACTION, ERR_CODE, ORI_MSG, MATCH_RULES, EXTRACT_RULES, DES_MSG, STATUS) VALUES (214000006371486103, TIMESTAMP '2016-12-16 14:38:54', TIMESTAMP '2016-12-16 14:38:54', null, '-168971045', '-168971045该客户已开通银证转账账户或其他行三方存管户!', null, null, '已开通银证转账账户或其他行三方存管户', '1');
INSERT INTO LCIMS.ERROR_MESSAGE_CONVERT (EID, EITIME, EUTIME, ERR_ACTION, ERR_CODE, ORI_MSG, MATCH_RULES, EXTRACT_RULES, DES_MSG, STATUS) VALUES (214000006371506465, TIMESTAMP '2016-12-16 14:47:19', TIMESTAMP '2016-12-16 14:47:19', null, '160901010', '3054[1689290007][160901010]营业部[5403]及总部[0000]都无此转帐银行设置', null, null, '无此转账银行设置', '1');
INSERT INTO LCIMS.ERROR_MESSAGE_CONVERT (EID, EITIME, EUTIME, ERR_ACTION, ERR_CODE, ORI_MSG, MATCH_RULES, EXTRACT_RULES, DES_MSG, STATUS) VALUES (214000006880589131, TIMESTAMP '2017-01-24 16:26:37', TIMESTAMP '2022-08-02 16:42:05', null, '-1689290009', '回绑旧卡：CODE: -1689290009, LEVEL: 0, MSG: -1689290009客户[0]有转账流水,不允许签约', null, null, '操作变更当日有转账流水，无法签约', '1');
INSERT INTO LCIMS.ERROR_MESSAGE_CONVERT (EID, EITIME, EUTIME, ERR_ACTION, ERR_CODE, ORI_MSG, MATCH_RULES, EXTRACT_RULES, DES_MSG, STATUS) VALUES (214000007455208691, TIMESTAMP '2017-03-28 10:33:41', TIMESTAMP '2021-05-14 16:40:04', '201', '-150908051', 'CODE: -990203010, LEVEL: 1, MSG: -990203010[-150908051]该资金帐号未开通市价委托交易: fundid=110100000171 orgid=1101"', null, null, '您尚未开通市价委托权限，请至业务办理功能开通权限', '1');
INSERT INTO LCIMS.ERROR_MESSAGE_CONVERT (EID, EITIME, EUTIME, ERR_ACTION, ERR_CODE, ORI_MSG, MATCH_RULES, EXTRACT_RULES, DES_MSG, STATUS) VALUES (214000007516511378, TIMESTAMP '2017-04-06 16:49:44', TIMESTAMP '2017-04-06 16:49:44', '307', '-411511015', 'CODE: -411511015, LEVEL: 1, MSG: -411511015查询起始日期2015123非法', null, null, '日期格式有误！', '1');
INSERT INTO LCIMS.ERROR_MESSAGE_CONVERT (EID, EITIME, EUTIME, ERR_ACTION, ERR_CODE, ORI_MSG, MATCH_RULES, EXTRACT_RULES, DES_MSG, STATUS) VALUES (214000007516517317, TIMESTAMP '2017-04-06 16:55:33', TIMESTAMP '2021-10-13 15:02:35', '100', '-980023093', 'CODE: -980023093, LEVEL: 2, MSG: -980023093[-980023093]资金帐号不允许以该操作方式[9]登录', null, null, '登录失败，详情请咨询客服热线95357', '1');
INSERT INTO LCIMS.ERROR_MESSAGE_CONVERT (EID, EITIME, EUTIME, ERR_ACTION, ERR_CODE, ORI_MSG, MATCH_RULES, EXTRACT_RULES, DES_MSG, STATUS) VALUES (214000007516517854, TIMESTAMP '2017-04-06 16:56:19', TIMESTAMP '2021-10-13 15:02:53', '100', '-980023095', 'CODE: -980023095, LEVEL: 2, MSG: -980023095[-980023095]客户密码错误,已超过错误次数被锁定', null, null, '您输入的信息有误，由于错误次数过多，系统已锁定账户,如有疑问请联系客服95357！', '1');
INSERT INTO LCIMS.ERROR_MESSAGE_CONVERT (EID, EITIME, EUTIME, ERR_ACTION, ERR_CODE, ORI_MSG, MATCH_RULES, EXTRACT_RULES, DES_MSG, STATUS) VALUES (214000007516518239, TIMESTAMP '2017-04-06 16:56:53', TIMESTAMP '2017-04-06 16:56:53', '203', '-990240020', 'CODE: -990240020, LEVEL: 1, MSG: -990240020[-990240020] [-990221021]市场[0]证券代码[161613]的证券交易属性信息不存在!', null, null, '该证券代码不存在！', '1');
INSERT INTO LCIMS.ERROR_MESSAGE_CONVERT (EID, EITIME, EUTIME, ERR_ACTION, ERR_CODE, ORI_MSG, MATCH_RULES, EXTRACT_RULES, DES_MSG, STATUS) VALUES (214000007516519478, TIMESTAMP '2017-04-06 16:57:39', TIMESTAMP '2017-04-06 16:57:39', '203', '-990240210', 'CODE: -990240210, LEVEL: 1, MSG: -990240210[-990240210]该证券禁止买入', null, null, '该证券无法买入！', '1');
INSERT INTO LCIMS.ERROR_MESSAGE_CONVERT (EID, EITIME, EUTIME, ERR_ACTION, ERR_CODE, ORI_MSG, MATCH_RULES, EXTRACT_RULES, DES_MSG, STATUS) VALUES (214000007516520638, TIMESTAMP '2017-04-06 16:59:23', TIMESTAMP '2017-04-06 16:59:23', '203', '-990240250', 'CODE: -990240250, LEVEL: 1, MSG: -990240250[-990240250]委托价格orderprice= 12.440与系统定价fixprice= 8.190不同', null, null, '委托价格有误！', '1');
INSERT INTO LCIMS.ERROR_MESSAGE_CONVERT (EID, EITIME, EUTIME, ERR_ACTION, ERR_CODE, ORI_MSG, MATCH_RULES, EXTRACT_RULES, DES_MSG, STATUS) VALUES (214000007516522883, TIMESTAMP '2017-04-06 17:00:07', TIMESTAMP '2017-04-06 17:00:07', '203', '-990240260', 'CODE: -990240260, LEVEL: 1, MSG: -990240260[-990240260]委托价格有误dbOrderprice = 0.000', null, null, '委托价格有误！', '1');
INSERT INTO LCIMS.ERROR_MESSAGE_CONVERT (EID, EITIME, EUTIME, ERR_ACTION, ERR_CODE, ORI_MSG, MATCH_RULES, EXTRACT_RULES, DES_MSG, STATUS) VALUES (214000007516542674, TIMESTAMP '2017-04-06 17:16:03', TIMESTAMP '2017-04-06 17:16:03', '402', '160912020', 'CODE: -*********, LEVEL: 1, MSG: -*********[160912020]该客户可取现金不足!orgid = ''5407'' fundid = 540700046167 moneytype = ''0'' fundcash = 16.11', null, null, '您账户的可取资金不足！', '1');
INSERT INTO LCIMS.ERROR_MESSAGE_CONVERT (EID, EITIME, EUTIME, ERR_ACTION, ERR_CODE, ORI_MSG, MATCH_RULES, EXTRACT_RULES, DES_MSG, STATUS) VALUES (214000003315385566, TIMESTAMP '2016-08-22 14:40:56', TIMESTAMP '2021-05-28 16:25:31', '100', '-980023091', 'CODE: -980023091, LEVEL: 2, MSG: -980023091[-980023091]帐户状态被锁定,不允许登陆', null, null, '您的账户状态异常，无法登录交易。如有疑问，请联系客服热线95357。', '1');
INSERT INTO LCIMS.ERROR_MESSAGE_CONVERT (EID, EITIME, EUTIME, ERR_ACTION, ERR_CODE, ORI_MSG, MATCH_RULES, EXTRACT_RULES, DES_MSG, STATUS) VALUES (214000005178132764, TIMESTAMP '2016-10-25 16:35:47', TIMESTAMP '2022-08-02 16:39:13', '203', '-990240290', 'CODE: -990240290, LEVEL: 1, MSG: -990240290[-990240290]委托价格超过跌停价格', null, null, '委托价格不能低于跌停价格。', '1');
INSERT INTO LCIMS.ERROR_MESSAGE_CONVERT (EID, EITIME, EUTIME, ERR_ACTION, ERR_CODE, ORI_MSG, MATCH_RULES, EXTRACT_RULES, DES_MSG, STATUS) VALUES (214000006138595175, TIMESTAMP '2016-12-01 09:49:36', TIMESTAMP '2016-12-16 14:48:14', null, '1689290003', 'cubsbScOpenAcct 调用失败，-1689290003[1689290003]客户状态不正常', null, null, '客户资金账户状态异常', '1');
INSERT INTO LCIMS.ERROR_MESSAGE_CONVERT (EID, EITIME, EUTIME, ERR_ACTION, ERR_CODE, ORI_MSG, MATCH_RULES, EXTRACT_RULES, DES_MSG, STATUS) VALUES (214000007515417951, TIMESTAMP '2017-04-06 16:19:17', TIMESTAMP '2017-04-06 16:19:17', '201', '-150908032', 'CODE: -990203010, LEVEL: 1, MSG: -990203010[-150908032]转帐台帐不允许证券交易: fundid=540800032437', null, null, '转帐台帐无法证券交易！', '1');
INSERT INTO LCIMS.ERROR_MESSAGE_CONVERT (EID, EITIME, EUTIME, ERR_ACTION, ERR_CODE, ORI_MSG, MATCH_RULES, EXTRACT_RULES, DES_MSG, STATUS) VALUES (214000007516507034, TIMESTAMP '2017-04-06 16:45:48', TIMESTAMP '2017-04-06 16:45:48', '301', '-410501010', 'CODE: -410501010, LEVEL: 1, MSG: -410501010客户/代理人:540600164935 交易密码错误', null, null, '交易密码错误！', '1');
INSERT INTO LCIMS.ERROR_MESSAGE_CONVERT (EID, EITIME, EUTIME, ERR_ACTION, ERR_CODE, ORI_MSG, MATCH_RULES, EXTRACT_RULES, DES_MSG, STATUS) VALUES (214000007516508471, TIMESTAMP '2017-04-06 16:47:18', TIMESTAMP '2017-04-06 16:47:18', '302', '-410529020', 'CODE: -410529020, LEVEL: 1, MSG: -410529020客户:540700230580 交易密码错误', null, null, '交易密码错误！', '1');
INSERT INTO LCIMS.ERROR_MESSAGE_CONVERT (EID, EITIME, EUTIME, ERR_ACTION, ERR_CODE, ORI_MSG, MATCH_RULES, EXTRACT_RULES, DES_MSG, STATUS) VALUES (214000007516509099, TIMESTAMP '2017-04-06 16:47:58', TIMESTAMP '2017-04-06 16:47:58', '405', '-410601005', 'CODE: -410601005, LEVEL: 1, MSG: -410601005客户/代理人:540700044010 交易密码错误', null, null, '交易密码错误！', '1');
INSERT INTO LCIMS.ERROR_MESSAGE_CONVERT (EID, EITIME, EUTIME, ERR_ACTION, ERR_CODE, ORI_MSG, MATCH_RULES, EXTRACT_RULES, DES_MSG, STATUS) VALUES (214000007516511928, TIMESTAMP '2017-04-06 16:50:23', TIMESTAMP '2017-04-06 16:50:23', '307', '-411511020', 'CODE: -411511020, LEVEL: 1, MSG: -411511020查询结束日期201510非法', null, null, '日期格式有误！', '1');
INSERT INTO LCIMS.ERROR_MESSAGE_CONVERT (EID, EITIME, EUTIME, ERR_ACTION, ERR_CODE, ORI_MSG, MATCH_RULES, EXTRACT_RULES, DES_MSG, STATUS) VALUES (214000007516512926, TIMESTAMP '2017-04-06 16:51:08', TIMESTAMP '2017-04-06 16:51:08', '308', '-411513015', 'CODE: -411513015, LEVEL: 1, MSG: -411513015查询起始日期-1081080386非法', null, null, '日期格式有误！', '1');
INSERT INTO LCIMS.ERROR_MESSAGE_CONVERT (EID, EITIME, EUTIME, ERR_ACTION, ERR_CODE, ORI_MSG, MATCH_RULES, EXTRACT_RULES, DES_MSG, STATUS) VALUES (214000007516513433, TIMESTAMP '2017-04-06 16:51:49', TIMESTAMP '2017-04-06 16:51:49', '308', '-411513020', 'CODE: -411513020, LEVEL: 1, MSG: -411513020查询结束日期201601非法', null, null, '日期格式有误！', '1');
INSERT INTO LCIMS.ERROR_MESSAGE_CONVERT (EID, EITIME, EUTIME, ERR_ACTION, ERR_CODE, ORI_MSG, MATCH_RULES, EXTRACT_RULES, DES_MSG, STATUS) VALUES (24, TIMESTAMP '2024-11-25 15:39:10', TIMESTAMP '2024-11-25 15:37:50', '282', '-150527021', 'CODE: -150527021, LEVEL: 1, MSG: -150527021席位[284300]参数中托管单元不能为空！', '[{"keyword":"参数中托管单元不能为空"}]', null, '服务开小差了，请稍后重试！', '1');
INSERT INTO LCIMS.ERROR_MESSAGE_CONVERT (EID, EITIME, EUTIME, ERR_ACTION, ERR_CODE, ORI_MSG, MATCH_RULES, EXTRACT_RULES, DES_MSG, STATUS) VALUES (214000061926653014, TIMESTAMP '2021-05-28 17:51:57', TIMESTAMP '2021-05-28 17:51:57', '20000', '27050653', 'CODE：27050653，LEVEL：1，MSG：客户协议签署[客户代码:%lld,协议类型:A]不存在', '[{"keyword":"协议类型:A]不存在"}]', null, '购买该产品需签署基础设施基金风险揭示书，建议您通过场内基金菜单进行购买。', '1');
INSERT INTO LCIMS.ERROR_MESSAGE_CONVERT (EID, EITIME, EUTIME, ERR_ACTION, ERR_CODE, ORI_MSG, MATCH_RULES, EXTRACT_RULES, DES_MSG, STATUS) VALUES (214000061927693049, TIMESTAMP '2021-05-28 19:00:50', TIMESTAMP '2022-08-02 17:04:40', '204', '-150908220', 'CODE: -150908220, LEVEL: 1, MSG: -150908220[-150908220]此证券类别禁止以该组合条件操作', '[{"keyword":"此证券类别禁止以该组合条件操作"}]', null, '当前时间暂无法交易该证券', '1');
INSERT INTO LCIMS.ERROR_MESSAGE_CONVERT (EID, EITIME, EUTIME, ERR_ACTION, ERR_CODE, ORI_MSG, MATCH_RULES, EXTRACT_RULES, DES_MSG, STATUS) VALUES (214000062632030306, TIMESTAMP '2021-06-18 16:47:47', TIMESTAMP '2021-06-18 16:47:47', '204', '-150904051', 'CODE: -150904051, LEVEL: 1, MSG: -150904051沪Ａ市场[1]的LOF申购业务[2f]不允许做基础设施基金证券类别[u]的业务!', '[{"keyword":"LOF申购业务[2f]不允许做基础设施基金证券类别[u]的业务"}]', null, '该基金不支持申购', '1');
INSERT INTO LCIMS.ERROR_MESSAGE_CONVERT (EID, EITIME, EUTIME, ERR_ACTION, ERR_CODE, ORI_MSG, MATCH_RULES, EXTRACT_RULES, DES_MSG, STATUS) VALUES (214000064178093798, TIMESTAMP '2021-07-30 16:46:40', TIMESTAMP '2022-08-02 17:03:19', '202', '-410413020', 'CODE: -410413020, LEVEL: 1, MSG: -410413020系统已交收尚未初始化,不允许委托申报,状态status = ''9''', '[{"keyword":"系统已交收尚未初始化"}]', null, '当前时间无法进行该项业务', '1');
INSERT INTO LCIMS.ERROR_MESSAGE_CONVERT (EID, EITIME, EUTIME, ERR_ACTION, ERR_CODE, ORI_MSG, MATCH_RULES, EXTRACT_RULES, DES_MSG, STATUS) VALUES (214000064728665284, TIMESTAMP '2021-08-13 16:25:40', TIMESTAMP '2021-08-13 16:30:41', '274', '-150908020', 'CODE: -150908020, LEVEL: 1, MSG: -150908020[-150908020]资金帐号状态不正常440200000177', '[{"keyword":"资金帐号状态不正常"}]', null, '您的资金账号状态不正常，详询客服热线95357。', '1');
INSERT INTO LCIMS.ERROR_MESSAGE_CONVERT (EID, EITIME, EUTIME, ERR_ACTION, ERR_CODE, ORI_MSG, MATCH_RULES, EXTRACT_RULES, DES_MSG, STATUS) VALUES (214000064728680916, TIMESTAMP '2021-08-13 16:26:28', TIMESTAMP '2021-08-13 16:26:28', '402', '168000016', 'CODE: -160920120, LEVEL: 1, MSG: -160920120[168000016]资金帐户[440200000177]状态[1]不正常!', '[{"keyword":"状态[1]不正常"}]', null, '您的资金账号状态不正常，详询客服热线95357。', '1');
INSERT INTO LCIMS.ERROR_MESSAGE_CONVERT (EID, EITIME, EUTIME, ERR_ACTION, ERR_CODE, ORI_MSG, MATCH_RULES, EXTRACT_RULES, DES_MSG, STATUS) VALUES (214000065391722797, TIMESTAMP '2021-08-31 15:59:42', TIMESTAMP '2021-08-31 15:59:42', '274', '-888888004', 'code：-888888004，LEVEL:1，MSG：-888888004最低类别投资者只能交易最低风险产品', '[{"keyword":"最低类别投资者只能交易最低风险产品"}]', null, '您的风险承受能力等级为C1-保守型（风险承受能力最低类别），仅允许交易风险等级为R1-低风险的产品，详询客服热线95357。', '1');
INSERT INTO LCIMS.ERROR_MESSAGE_CONVERT (EID, EITIME, EUTIME, ERR_ACTION, ERR_CODE, ORI_MSG, MATCH_RULES, EXTRACT_RULES, DES_MSG, STATUS) VALUES (214000065391746656, TIMESTAMP '2021-08-31 16:00:28', TIMESTAMP '2021-08-31 16:00:28', '260', '-888888004', 'code：-888888004，LEVEL:1，MSG：-888888004最低类别投资者只能交易最低风险产品', '[{"keyword":"最低类别投资者只能交易最低风险产品"}]', null, '您的风险承受能力等级为C1-保守型（风险承受能力最低类别），仅允许交易风险等级为R1-低风险的产品，详询客服热线95357。', '1');
INSERT INTO LCIMS.ERROR_MESSAGE_CONVERT (EID, EITIME, EUTIME, ERR_ACTION, ERR_CODE, ORI_MSG, MATCH_RULES, EXTRACT_RULES, DES_MSG, STATUS) VALUES (214000067502562329, TIMESTAMP '2021-11-05 17:40:44', TIMESTAMP '2021-11-12 18:31:27', '231', '-150905336', 'CODE:-150905360,LEVEL:1,MSG:-150905360[-150905336]该证券账户[0000009216]无此权限[北交所合格投资者]', '[{"keyword":"无此权限[北交所合格投资者]"}]', null, '交易北交所股票需要开通北交所合格投资者权限并签署交易须知，请前往权限开通功能办理。', '1');
INSERT INTO LCIMS.ERROR_MESSAGE_CONVERT (EID, EITIME, EUTIME, ERR_ACTION, ERR_CODE, ORI_MSG, MATCH_RULES, EXTRACT_RULES, DES_MSG, STATUS) VALUES (214000067739526889, TIMESTAMP '2021-11-12 18:27:10', TIMESTAMP '2021-11-12 18:27:10', '231', '-150905334', 'CODE:-150905360, LEVEL:1, MSG:-150905360[-150905334]客户无法交易该层级[基础层]证券:该证券账户[0104461035]无此权限[股转挂牌公司受限投资者转让权限]', '[{"keyword":"无此权限[股转挂牌公司受限投资者转让权限]"}]', null, '您尚未开通受限投资者转让权限，暂无法交易该证券。请至我司线下营业部开通该权限，详询客服热线95357。', '1');
INSERT INTO LCIMS.ERROR_MESSAGE_CONVERT (EID, EITIME, EUTIME, ERR_ACTION, ERR_CODE, ORI_MSG, MATCH_RULES, EXTRACT_RULES, DES_MSG, STATUS) VALUES (214000067739529878, TIMESTAMP '2021-11-12 18:27:45', TIMESTAMP '2021-11-12 18:27:45', '231', '-150905347', 'CODE:-150905370, LEVEL:1, MSG:-150905370[-150905347]客户无法交易该层级[北交所]证券:该证券账户[0028907329]的权限[北交所合格投资者]不在有效期内', '[{"keyword":"权限[北交所合格投资者]不在有效期内"}]', null, '您的北交所合格投资者权限尚未生效，请于下个交易日再试。', '1');
INSERT INTO LCIMS.ERROR_MESSAGE_CONVERT (EID, EITIME, EUTIME, ERR_ACTION, ERR_CODE, ORI_MSG, MATCH_RULES, EXTRACT_RULES, DES_MSG, STATUS) VALUES (214000067740113074, TIMESTAMP '2021-11-12 18:52:56', TIMESTAMP '2021-11-12 18:52:56', '1201', '-150905360', 'CODE:-150905519,LEVEL:1,MSG:-150905519[-150905360]该证券账户[E042564309]无此权限[2C 科创板交易权限]', '[{"keyword":"无此权限[2C 科创板交易权限]"}]', null, '您暂未开通科创板交易权限或尚未签署科创板相关协议，无法买卖科创板股票。', '1');
INSERT INTO LCIMS.ERROR_MESSAGE_CONVERT (EID, EITIME, EUTIME, ERR_ACTION, ERR_CODE, ORI_MSG, MATCH_RULES, EXTRACT_RULES, DES_MSG, STATUS) VALUES (214000069047238321, TIMESTAMP '2021-12-17 16:01:30', TIMESTAMP '2021-12-17 16:01:30', '201', '-150905023', 'CODE:-150905023,LEVEL:1,MSG:-150905023首日指定交易的客户，下一个交易日才可开展债券现券的交易申报', '[{"keyword":"首日指定交易的客户"}]', null, '新开户用户，下一个交易日起才可以参与上交所债券/国债逆回购交易', '1');
INSERT INTO LCIMS.ERROR_MESSAGE_CONVERT (EID, EITIME, EUTIME, ERR_ACTION, ERR_CODE, ORI_MSG, MATCH_RULES, EXTRACT_RULES, DES_MSG, STATUS) VALUES (214000074256340719, TIMESTAMP '2022-05-13 17:17:39', TIMESTAMP '2022-05-13 17:17:39', '231', '-150904051', 'CODE:-150904051,LEVEL:1,MSG:-150904051股转Ａ市场[6]的证券买入业务[0B]不允许做转换债券证券类别[8]的业务!', '[{"keyword":"不允许做转换债券证券类别[8]的业务"}]', null, '当前功能暂不支持委托该证券', '1');
INSERT INTO LCIMS.ERROR_MESSAGE_CONVERT (EID, EITIME, EUTIME, ERR_ACTION, ERR_CODE, ORI_MSG, MATCH_RULES, EXTRACT_RULES, DES_MSG, STATUS) VALUES (214000074263991734, TIMESTAMP '2022-05-14 16:30:37', TIMESTAMP '2022-08-02 17:08:18', '201', '-150670452', 'CODE: -150670452, LEVEL: 1, MSG: -150670452取债券现券交易业务参数[市场:0,证券代码:136017,交易方式:1]失败！', '[{"keyword":"取债券现券交易业务参数"}]', null, '当前时间无法做该项业务', '1');
INSERT INTO LCIMS.ERROR_MESSAGE_CONVERT (EID, EITIME, EUTIME, ERR_ACTION, ERR_CODE, ORI_MSG, MATCH_RULES, EXTRACT_RULES, DES_MSG, STATUS) VALUES (214000074264004654, TIMESTAMP '2022-05-14 16:33:01', TIMESTAMP '2022-05-14 16:33:01', '274', '-150906425', 'CODE: -150906400, LEVEL: 1, MSG: -150906400[-150906425]私募债券没有授权或授权到期，禁止对该证券类别委托，错误信息[ 校验的OEM代码【私募债券】不存在!]', '[{"keyword":"私募债券没有授权或授权到期"}]', null, '当前功能暂不支持委托该证券', '1');
INSERT INTO LCIMS.ERROR_MESSAGE_CONVERT (EID, EITIME, EUTIME, ERR_ACTION, ERR_CODE, ORI_MSG, MATCH_RULES, EXTRACT_RULES, DES_MSG, STATUS) VALUES (214000043942284809, TIMESTAMP '2020-07-24 15:18:59', TIMESTAMP '2022-08-02 16:54:31', '1201', '-160002006', 'CODE: -160002006, LEVEL: 1, MSG: -160002006[配售申购]业务,当前时间不允许委托,可委托的时间段为：091500-150500!', '[{"keyword":"当前时间不允许委托"}]', null, '当前时间无法进行该项业务', '1');
INSERT INTO LCIMS.ERROR_MESSAGE_CONVERT (EID, EITIME, EUTIME, ERR_ACTION, ERR_CODE, ORI_MSG, MATCH_RULES, EXTRACT_RULES, DES_MSG, STATUS) VALUES (214000056014756041, TIMESTAMP '2020-11-13 19:13:02', TIMESTAMP '2022-08-02 16:56:15', '201', '-999003088', '[-999003088]系统已交收尚未初始化,不允许夜市委托,系统状态status = ''9''', '[{"keyword":"不允许夜市委托"}]', null, '当前时间无法做该项业务', '1');
INSERT INTO LCIMS.ERROR_MESSAGE_CONVERT (EID, EITIME, EUTIME, ERR_ACTION, ERR_CODE, ORI_MSG, MATCH_RULES, EXTRACT_RULES, DES_MSG, STATUS) VALUES (214000056014762748, TIMESTAMP '2020-11-13 19:14:48', TIMESTAMP '2022-08-02 16:56:44', '260', '-999003088', '[-999003088]系统正在初始化,不允许夜市委托,系统状态status = ''1''', '[{"keyword":"系统正在初始化"}]', null, '当前时间无法做该项业务', '1');
INSERT INTO LCIMS.ERROR_MESSAGE_CONVERT (EID, EITIME, EUTIME, ERR_ACTION, ERR_CODE, ORI_MSG, MATCH_RULES, EXTRACT_RULES, DES_MSG, STATUS) VALUES (214000056014768491, TIMESTAMP '2020-11-13 19:15:38', TIMESTAMP '2022-08-02 16:56:58', '260', '-999003088', '[-999003088]系统初始化前备份,不允许夜市委托,系统状态status = ''5''', '[{"keyword":"系统初始化前备份"}]', null, '当前时间无法做该项业务', '1');
INSERT INTO LCIMS.ERROR_MESSAGE_CONVERT (EID, EITIME, EUTIME, ERR_ACTION, ERR_CODE, ORI_MSG, MATCH_RULES, EXTRACT_RULES, DES_MSG, STATUS) VALUES (214000056014775138, TIMESTAMP '2020-11-13 19:16:38', TIMESTAMP '2022-08-02 16:57:10', '260', '-999003088', '[-999003088]系统正在交收,不允许夜市委托,系统状态status = ''4''', '[{"keyword":"系统正在交收"}]', null, '当前时间无法做该项业务', '1');
INSERT INTO LCIMS.ERROR_MESSAGE_CONVERT (EID, EITIME, EUTIME, ERR_ACTION, ERR_CODE, ORI_MSG, MATCH_RULES, EXTRACT_RULES, DES_MSG, STATUS) VALUES (214000056014780425, TIMESTAMP '2020-11-13 19:17:40', TIMESTAMP '2022-08-02 16:58:06', '274', '-999003088', '[-999003088]系统正在交收,不允许夜市委托,系统状态status = ''4''', '[{"keyword":"系统正在交收"}]', null, '当前时间无法做该项业务。预计5分钟以后可以进行沪深A股委托，请稍后再试。', '1');
INSERT INTO LCIMS.ERROR_MESSAGE_CONVERT (EID, EITIME, EUTIME, ERR_ACTION, ERR_CODE, ORI_MSG, MATCH_RULES, EXTRACT_RULES, DES_MSG, STATUS) VALUES (214000061458119374, TIMESTAMP '2021-05-14 16:43:37', TIMESTAMP '2021-05-14 16:43:37', '274', '-150905372', 'CODE: -150622020, LEVEL: 1, MSG: -150622020[-150905372]该证券账户[A357034267]无此权限[开通风险警示证券买入]!', '[{"keyword":"开通风险警示证券买入"}]', null, '您尚未开通该市场风险警示股票交易权限，请至业务办理功能开通权限', '1');
INSERT INTO LCIMS.ERROR_MESSAGE_CONVERT (EID, EITIME, EUTIME, ERR_ACTION, ERR_CODE, ORI_MSG, MATCH_RULES, EXTRACT_RULES, DES_MSG, STATUS) VALUES (214000061458183053, TIMESTAMP '2021-05-14 16:44:18', TIMESTAMP '2021-05-14 16:44:18', '201', '-150905375', 'CODE: -150905360, LEVEL: 1, MSG: -150905360[-150905375]该证券账户[0103645380]无此权限[开通风险警示证券买入]', '[{"keyword":"开通风险警示证券买入"}]', null, '您尚未开通该市场风险警示股票交易权限，请至业务办理功能开通权限', '1');
INSERT INTO LCIMS.ERROR_MESSAGE_CONVERT (EID, EITIME, EUTIME, ERR_ACTION, ERR_CODE, ORI_MSG, MATCH_RULES, EXTRACT_RULES, DES_MSG, STATUS) VALUES (214000061458367755, TIMESTAMP '2021-05-14 16:45:04', TIMESTAMP '2021-05-14 16:45:04', '274', '-150905375', 'CODE: -150905360, LEVEL: 1, MSG: -150905360[-150905375]该证券账户[0103645380]无此权限[开通风险警示证券买入]', '[{"keyword":"开通风险警示证券买入"}]', null, '您尚未开通该市场风险警示股票交易权限，请至业务办理功能开通权限', '1');
INSERT INTO LCIMS.ERROR_MESSAGE_CONVERT (EID, EITIME, EUTIME, ERR_ACTION, ERR_CODE, ORI_MSG, MATCH_RULES, EXTRACT_RULES, DES_MSG, STATUS) VALUES (214000061458514584, TIMESTAMP '2021-05-14 16:45:42', TIMESTAMP '2021-05-14 16:45:42', '1201', '-150905311', 'CODE: -150622020, LEVEL: 1, MSG: -150622020[-150905311]该证券账户[E050152459]无此权限[04 开通风险警示证券买入权限]', '[{"keyword":"开通风险警示证券买入权限"}]', null, '您尚未开通该市场风险警示股票交易权限', '1');
INSERT INTO LCIMS.ERROR_MESSAGE_CONVERT (EID, EITIME, EUTIME, ERR_ACTION, ERR_CODE, ORI_MSG, MATCH_RULES, EXTRACT_RULES, DES_MSG, STATUS) VALUES (214000064177953735, TIMESTAMP '2021-07-30 16:45:09', TIMESTAMP '2022-08-02 17:02:13', '231', '-999003088', '[-999003088]系统初始化前备份,不允许夜市委托,系统状态status = ''5''', '[{"keyword":"系统初始化前备份"}]', null, '当前时间无法进行该项业务', '1');
INSERT INTO LCIMS.ERROR_MESSAGE_CONVERT (EID, EITIME, EUTIME, ERR_ACTION, ERR_CODE, ORI_MSG, MATCH_RULES, EXTRACT_RULES, DES_MSG, STATUS) VALUES (214000064177962579, TIMESTAMP '2021-07-30 16:45:55', TIMESTAMP '2022-08-02 17:02:42', '260', '-999003088', '[-999003088]系统已交收尚未初始化,不允许夜市委托,系统状态status = ''9''', '[{"keyword":"系统已交收尚未初始化"}]', null, '当前时间无法进行该项业务', '1');
INSERT INTO LCIMS.ERROR_MESSAGE_CONVERT (EID, EITIME, EUTIME, ERR_ACTION, ERR_CODE, ORI_MSG, MATCH_RULES, EXTRACT_RULES, DES_MSG, STATUS) VALUES (214000065508366045, TIMESTAMP '2021-09-02 17:42:56', TIMESTAMP '2021-09-02 17:42:56', '204', '-888888004', 'code：-888888004，LEVEL:1，MSG：-888888004最低类别投资者只能交易最低风险产品', '[{"keyword":"最低类别投资者只能交易最低风险产品"}]', null, '您的风险承受能力等级为C1-保守型（风险承受能力最低类别），仅允许交易风险等级为R1-低风险的产品，详询客服热线95357。', '1');
INSERT INTO LCIMS.ERROR_MESSAGE_CONVERT (EID, EITIME, EUTIME, ERR_ACTION, ERR_CODE, ORI_MSG, MATCH_RULES, EXTRACT_RULES, DES_MSG, STATUS) VALUES (214000066966403399, TIMESTAMP '2021-10-21 19:54:28', TIMESTAMP '2021-10-21 19:54:28', '260', '-150909010', 'CODE:-430007020,LEVEL:2,MSG:-430007020[-150909010]本交易所今日闭市', '[{"keyword":"本交易所今日闭市"}]', null, '当前时间暂不支持港股通交易', '1');
INSERT INTO LCIMS.ERROR_MESSAGE_CONVERT (EID, EITIME, EUTIME, ERR_ACTION, ERR_CODE, ORI_MSG, MATCH_RULES, EXTRACT_RULES, DES_MSG, STATUS) VALUES (214000067739522157, TIMESTAMP '2021-11-12 18:26:27', TIMESTAMP '2021-11-12 18:26:27', '231', '-150905347', 'CODE:-150905360,LEVEL:1,MSG:-150905360[-150905347]客户无法交易该层级[北交所]证券:该证券账户[0211171687]无此权限[股转挂牌公司受限投资者转让权限]', '[{"keyword":"无此权限[股转挂牌公司受限投资者转让权限]"}]', null, '您尚未开通受限投资者转让权限，暂无法交易该证券。请至我司线下营业部开通该权限，详询客服热线95357。', '1');
INSERT INTO LCIMS.ERROR_MESSAGE_CONVERT (EID, EITIME, EUTIME, ERR_ACTION, ERR_CODE, ORI_MSG, MATCH_RULES, EXTRACT_RULES, DES_MSG, STATUS) VALUES (214000072213162494, TIMESTAMP '2022-03-17 18:25:31', TIMESTAMP '2022-03-17 18:25:31', '201', '-150905797', 'CODE:-150905360,LEVEL:1,MSG:-150905360[-150905797]该证券账户[A285257554]无此权限[科创板交易权限]!"', '[{"keyword":"无此权限[科创板交易权限]"}]', null, '您暂未开通科创板交易权限或尚未签署科创板相关协议，无法买卖科创板股票。', '1');
INSERT INTO LCIMS.ERROR_MESSAGE_CONVERT (EID, EITIME, EUTIME, ERR_ACTION, ERR_CODE, ORI_MSG, MATCH_RULES, EXTRACT_RULES, DES_MSG, STATUS) VALUES (214000072787367572, TIMESTAMP '2022-03-31 16:27:21', TIMESTAMP '2022-08-02 17:05:25', '204', '-150904901', 'CODE:-150904901,LEVEL:1,MSG:-150904901[-150904901]该上证LOF证券不允许认购', '[{"keyword":"该上证LOF证券不允许认购"}]', null, '您输入的代码无法认购', '1');
INSERT INTO LCIMS.ERROR_MESSAGE_CONVERT (EID, EITIME, EUTIME, ERR_ACTION, ERR_CODE, ORI_MSG, MATCH_RULES, EXTRACT_RULES, DES_MSG, STATUS) VALUES (214000074256347290, TIMESTAMP '2022-05-13 17:19:02', TIMESTAMP '2022-08-02 17:06:00', '201', '-150906956', 'CODE: -150906956, LEVEL: 1, MSG: -150906956深圳债券交易已迁移到固收平台，请使用固收平台专用菜单或外围操作', '[{"keyword":"深圳债券交易已迁移到固收平台"}]', null, '债券交易无法进行市价委托', '1');
INSERT INTO LCIMS.ERROR_MESSAGE_CONVERT (EID, EITIME, EUTIME, ERR_ACTION, ERR_CODE, ORI_MSG, MATCH_RULES, EXTRACT_RULES, DES_MSG, STATUS) VALUES (214000074256348655, TIMESTAMP '2022-05-13 17:19:35', TIMESTAMP '2024-05-20 15:47:48', '231', '-*********', 'CODE: -*********, LEVEL: 1, MSG: -*********[-*********]当前交易时间禁止委托该证券类别 stktype = ''8''', '[{"keyword":"当前交易时间禁止委托该证券类别 stktype = ''''8''''"}]', null, '当前时间无法做该项业务', '1');
INSERT INTO LCIMS.ERROR_MESSAGE_CONVERT (EID, EITIME, EUTIME, ERR_ACTION, ERR_CODE, ORI_MSG, MATCH_RULES, EXTRACT_RULES, DES_MSG, STATUS) VALUES (214000074256353731, TIMESTAMP '2022-05-13 17:20:52', TIMESTAMP '2022-08-02 17:07:08', '1201', '-150906956', 'CODE: -150906956, LEVEL: 1, MSG: -150906956深圳债券交易已迁移到固收平台，请使用固收平台专用菜单或外围操作。', '[{"keyword":"深圳债券交易已迁移到固收平台"}]', null, '债券无法以该种方式进行委托', '1');
INSERT INTO LCIMS.ERROR_MESSAGE_CONVERT (EID, EITIME, EUTIME, ERR_ACTION, ERR_CODE, ORI_MSG, MATCH_RULES, EXTRACT_RULES, DES_MSG, STATUS) VALUES (214000074263997961, TIMESTAMP '2022-05-14 16:31:52', TIMESTAMP '2022-05-14 16:31:52', '274', '-150670452', 'CODE: -150670452, LEVEL: 1, MSG: -150670452取债券现券交易业务参数[市场:0,证券代码:136017,交易方式:1]失败！', '[{"keyword":"取债券现券交易业务参数"}]', null, '当前功能暂不支持委托该证券', '1');
INSERT INTO LCIMS.ERROR_MESSAGE_CONVERT (EID, EITIME, EUTIME, ERR_ACTION, ERR_CODE, ORI_MSG, MATCH_RULES, EXTRACT_RULES, DES_MSG, STATUS) VALUES (214000074264001840, TIMESTAMP '2022-05-14 16:32:26', TIMESTAMP '2022-05-14 16:32:26', '203', '-150670452', 'CODE: -150670452, LEVEL: 1, MSG: -150670452取债券现券交易业务参数[市场:0,证券代码:136017,交易方式:1]失败！', '[{"keyword":"取债券现券交易业务参数"}]', null, '当前功能暂不支持委托该证券', '1');
INSERT INTO LCIMS.ERROR_MESSAGE_CONVERT (EID, EITIME, EUTIME, ERR_ACTION, ERR_CODE, ORI_MSG, MATCH_RULES, EXTRACT_RULES, DES_MSG, STATUS) VALUES (214000074268727624, TIMESTAMP '2022-05-15 16:30:50', TIMESTAMP '2022-08-02 17:07:32', '1216', '-150670452', 'CODE: -150670452, LEVEL: 1, MSG: -150670452取债券现券交易业务参数[市场:0,证券代码:100303,交易方式:1]失败！', '[{"keyword":"取债券现券交易业务参数"}]', null, '当前时间无法做该项业务', '1');
INSERT INTO LCIMS.ERROR_MESSAGE_CONVERT (EID, EITIME, EUTIME, ERR_ACTION, ERR_CODE, ORI_MSG, MATCH_RULES, EXTRACT_RULES, DES_MSG, STATUS) VALUES (214000074519214674, TIMESTAMP '2022-05-20 16:12:40', TIMESTAMP '2022-05-20 16:12:40', '1201', '-150905360', 'CODE: -150905313, LEVEL: 1, MSG: -150905313[-150905360]该证券账户[E043752890]无此权限[0v 债券专业投资者权限]', '[{"keyword":"无此权限[0v 债券专业投资者权限]"}]', null, '您暂未开通该市场债券专业投资者权限，无法交易该债券，详询客服热线95357。', '1');
INSERT INTO LCIMS.ERROR_MESSAGE_CONVERT (EID, EITIME, EUTIME, ERR_ACTION, ERR_CODE, ORI_MSG, MATCH_RULES, EXTRACT_RULES, DES_MSG, STATUS) VALUES (214000074519291271, TIMESTAMP '2022-05-20 16:26:14', TIMESTAMP '2022-05-20 16:26:14', '274', '-150905287', 'CODE: -150905360, LEVEL: 1, MSG: -150905360[-150905287]该证券账户[A621532881]无此权限[债券专业投资者权限]', '[{"keyword":"无此权限[债券专业投资者权限]"}]', null, '您暂未开通该市场债券专业投资者权限，无法交易该债券，详询客服热线95357。', '1');
INSERT INTO LCIMS.ERROR_MESSAGE_CONVERT (EID, EITIME, EUTIME, ERR_ACTION, ERR_CODE, ORI_MSG, MATCH_RULES, EXTRACT_RULES, DES_MSG, STATUS) VALUES (214000074519304921, TIMESTAMP '2022-05-20 16:29:07', TIMESTAMP '2022-05-20 16:29:07', '1201', '-150905360', 'CODE: -150905301, LEVEL: 1, MSG: -150905301[-150905360]该证券账户[0604842114]无此权限[2q 债券普通投资者权限]', '[{"keyword":"无此权限[2q 债券普通投资者权限]"}]', null, '您暂未开通该市场债券普通投资者权限，无法交易该债券，请至权限开通功能开通权限。', '1');
INSERT INTO LCIMS.ERROR_MESSAGE_CONVERT (EID, EITIME, EUTIME, ERR_ACTION, ERR_CODE, ORI_MSG, MATCH_RULES, EXTRACT_RULES, DES_MSG, STATUS) VALUES (214000074519320736, TIMESTAMP '2022-05-20 16:30:35', TIMESTAMP '2022-05-20 16:30:35', '263', '-430013050', 'CODE:-430013050,LEVEL:1,MSG:-430013050终止日期不能小于起始日期', '[{"keyword":"终止日期不能小于起始日期"}]', null, '起止日期输入有误，请检查后重新输入。', '1');
INSERT INTO LCIMS.ERROR_MESSAGE_CONVERT (EID, EITIME, EUTIME, ERR_ACTION, ERR_CODE, ORI_MSG, MATCH_RULES, EXTRACT_RULES, DES_MSG, STATUS) VALUES (214000075750799275, TIMESTAMP '2022-06-22 16:53:09', TIMESTAMP '2022-06-22 16:53:09', '231', '-150905341', 'CODE: -150905360, LEVEL: 1, MSG: -150905360[-150905341]该证券账户[0203964037]无此权限[两网及退市公司股票转让]', '[{"keyword":"无此权限[两网及退市公司股票转让]"}]', null, '您暂未开通该市场两网及退市公司股票转让权限，无法交易该股票，详询客服热线95357。', '1');
INSERT INTO LCIMS.ERROR_MESSAGE_CONVERT (EID, EITIME, EUTIME, ERR_ACTION, ERR_CODE, ORI_MSG, MATCH_RULES, EXTRACT_RULES, DES_MSG, STATUS) VALUES (214000075865269359, TIMESTAMP '2022-06-24 17:23:04', TIMESTAMP '2022-08-02 17:09:41', '279', '-410450060', 'CODE: -410450060, LEVEL: 1, MSG: -410450060股份信息 603191 不存在', '[{"keyword":"不存在"}]', null, '当前证券暂无法进行该项业务', '1');
INSERT INTO LCIMS.ERROR_MESSAGE_CONVERT (EID, EITIME, EUTIME, ERR_ACTION, ERR_CODE, ORI_MSG, MATCH_RULES, EXTRACT_RULES, DES_MSG, STATUS) VALUES (214000076692423459, TIMESTAMP '2022-07-15 15:37:29', TIMESTAMP '2022-07-15 15:37:29', '274', '-150905796', 'CODE: -150905360, LEVEL: 1, MSG: -150905360[-150905796]该证券账户[A181003571]无此权限[科创板交易权限]', '[{"keyword":"无此权限[科创板交易权限]"}]', null, '您暂未开通科创板交易权限或尚未签署科创板相关协议，无法买卖科创板股票。', '1');
INSERT INTO LCIMS.ERROR_MESSAGE_CONVERT (EID, EITIME, EUTIME, ERR_ACTION, ERR_CODE, ORI_MSG, MATCH_RULES, EXTRACT_RULES, DES_MSG, STATUS) VALUES (214000078296445038, TIMESTAMP '2022-08-25 17:49:09', TIMESTAMP '2022-08-25 17:49:09', '257', '-150906040', 'CODE: -150906040, LEVEL: 1, MSG: -150906040[-150906040]本交易所今日闭市', '[{"keyword":"本交易所今日闭市"}]', null, '当前时间暂不支持港股通交易', '1');
INSERT INTO LCIMS.ERROR_MESSAGE_CONVERT (EID, EITIME, EUTIME, ERR_ACTION, ERR_CODE, ORI_MSG, MATCH_RULES, EXTRACT_RULES, DES_MSG, STATUS) VALUES (214000079380966959, TIMESTAMP '2022-09-23 15:34:39', TIMESTAMP '2022-09-23 15:34:39', '204', '-150918012', 'CODE: -150918012, LEVEL: 1, MSG: -150918012[-150918012]跨市场etf在表[etfattribute]中无记录,ofcode=[159997],market=[0]', '[{"keyword":"表[etfattribute]中无记录"}]', null, '暂不支持该ETF的申赎操作', '1');
INSERT INTO LCIMS.ERROR_MESSAGE_CONVERT (EID, EITIME, EUTIME, ERR_ACTION, ERR_CODE, ORI_MSG, MATCH_RULES, EXTRACT_RULES, DES_MSG, STATUS) VALUES (214000097250498874, TIMESTAMP '2023-12-15 19:21:05', TIMESTAMP '2023-12-15 19:23:23', '20000', '27030016', 'CODE: 27030016, LEVEL: 1, MSG: 产品类别清算属性不存在[产品大类:7,产品子类:79,市场代码:3,交易类别:111]', '[{"keyword":"产品类别清算属性不存在"}]', null, '暂不支持养老金产品，请前往东方财富APP购买', '1');
INSERT INTO LCIMS.ERROR_MESSAGE_CONVERT (EID, EITIME, EUTIME, ERR_ACTION, ERR_CODE, ORI_MSG, MATCH_RULES, EXTRACT_RULES, DES_MSG, STATUS) VALUES (214000102168326398, TIMESTAMP '2024-04-19 16:59:40', TIMESTAMP '2024-04-19 17:01:06', '1350', '-150906052', 'CODE:-150906052,LEVEL:1,MSG:-150906052[-150906052]系统委托日期有误sysdate=20240417,orderdate=20240416', '[{"keyword":"系统委托日期有误"}]', null, '当前时间无法做该项业务', '1');
INSERT INTO LCIMS.ERROR_MESSAGE_CONVERT (EID, EITIME, EUTIME, ERR_ACTION, ERR_CODE, ORI_MSG, MATCH_RULES, EXTRACT_RULES, DES_MSG, STATUS) VALUES (214000102593228129, TIMESTAMP '2024-04-30 17:04:54', TIMESTAMP '2024-04-30 17:06:39', '404', '160918800', 'CODE:-*********,LEVEL:1,MSG:-*********[160918800]现金宝申请至交收阶段不允许转帐!', '[{"keyword":"不允许转帐"}]', null, '当前时间无法进行转账', '1');
INSERT INTO LCIMS.ERROR_MESSAGE_CONVERT (EID, EITIME, EUTIME, ERR_ACTION, ERR_CODE, ORI_MSG, MATCH_RULES, EXTRACT_RULES, DES_MSG, STATUS) VALUES (214000105927876592, TIMESTAMP '2024-08-16 17:41:14', TIMESTAMP '2024-08-16 17:41:14', '1201', '-150906300', 'CODE: -150906300, LEVEL: 1, MSG: -150906300[-150906300]买券还券委托数量不得大于融券负债数量 stkdebtsqty = 0', '[{"keyword":"买券还券委托数量"}]', '[{"start":"stkdebtsqty ="}]', '还券数量超过最多可还数量：{0}', '1');
INSERT INTO LCIMS.ERROR_MESSAGE_CONVERT (EID, EITIME, EUTIME, ERR_ACTION, ERR_CODE, ORI_MSG, MATCH_RULES, EXTRACT_RULES, DES_MSG, STATUS) VALUES (214000105927877968, TIMESTAMP '2024-08-16 17:42:05', TIMESTAMP '2024-08-16 17:42:05', '404', '-*********', 'CODE: -*********, LEVEL: 1, MSG: -*********系统当常状态为[1],不允许做转帐!', '[{"keyword":"系统当常状态为[1],不允许做转帐"}]', null, '当前时间不支持转账业务', '1');
INSERT INTO LCIMS.ERROR_MESSAGE_CONVERT (EID, EITIME, EUTIME, ERR_ACTION, ERR_CODE, ORI_MSG, MATCH_RULES, EXTRACT_RULES, DES_MSG, STATUS) VALUES (214000093981484846, TIMESTAMP '2023-09-15 18:00:20', TIMESTAMP '2024-05-20 15:48:22', '231', '-*********', 'CODE: -*********, LEVEL: 1, MSG: -*********[-*********]当前交易时间禁止委托该证券类别 stktype = ''w''', '[{"keyword":"当前交易时间禁止委托该证券类别 stktype = ''''w''''"}]', null, '当前时间无法做该项业务', '1');
INSERT INTO LCIMS.ERROR_MESSAGE_CONVERT (EID, EITIME, EUTIME, ERR_ACTION, ERR_CODE, ORI_MSG, MATCH_RULES, EXTRACT_RULES, DES_MSG, STATUS) VALUES (214000103311378426, TIMESTAMP '2024-05-20 15:41:28', TIMESTAMP '2024-05-20 15:41:28', '231', '-150906250', 'CODE: -150906250, LEVEL: 1, MSG: -150906250[-150906250] 交易类型输入有误: 2', '[{"keyword":"交易类型输入有误: 2"}]', null, '该证券不支持在当前页面交易，请至北交所新股申购功能中操作', '1');
INSERT INTO LCIMS.ERROR_MESSAGE_CONVERT (EID, EITIME, EUTIME, ERR_ACTION, ERR_CODE, ORI_MSG, MATCH_RULES, EXTRACT_RULES, DES_MSG, STATUS) VALUES (214000103311458978, TIMESTAMP '2024-05-20 15:45:28', TIMESTAMP '2024-05-20 15:45:28', '1350', '-150906250', 'CODE: -150906250, LEVEL: 1, MSG: -150906250[-150906250] 该证券不支持竞价申报（交易类型[2]有误，需为[0]正常交易）！', '[{"keyword":"交易类型[2]有误"}]', null, '该证券不支持在当前页面交易，请至北交所新股申购功能中操作', '1');
INSERT INTO LCIMS.ERROR_MESSAGE_CONVERT (EID, EITIME, EUTIME, ERR_ACTION, ERR_CODE, ORI_MSG, MATCH_RULES, EXTRACT_RULES, DES_MSG, STATUS) VALUES (214000104814207387, TIMESTAMP '2024-06-28 15:59:14', TIMESTAMP '2024-06-28 15:59:14', '274', '-150904062', '\"errmsg\": \"CODE: -150904062, LEVEL: 1, MSG: -150904062[-150904062]上证基金通禁止在普通买卖菜单委托，请到相应的业务菜单委托!\"', '[{"keyword":"上证基金通禁止在普通买卖菜单委托"}]', null, '该基金不支持买卖，可前往场内基金模块进行申赎操作。', '1');
INSERT INTO LCIMS.ERROR_MESSAGE_CONVERT (EID, EITIME, EUTIME, ERR_ACTION, ERR_CODE, ORI_MSG, MATCH_RULES, EXTRACT_RULES, DES_MSG, STATUS) VALUES (214000106197286012, TIMESTAMP '2024-08-30 16:55:44', TIMESTAMP '2024-08-30 16:55:44', '1203', '-471024045', 'CODE:？-471024045,？LEVEL:？1,？MSG:？-471024045系统状态非正常交易,不允许直接还款,状态status？=？''9''', '[{"keyword":"系统状态非正常交易"}]', null, '当前时间无法进行该项业务', '1');
INSERT INTO LCIMS.ERROR_MESSAGE_CONVERT (EID, EITIME, EUTIME, ERR_ACTION, ERR_CODE, ORI_MSG, MATCH_RULES, EXTRACT_RULES, DES_MSG, STATUS) VALUES (214000098324985619, TIMESTAMP '2024-01-12 14:56:17', TIMESTAMP '2024-01-12 14:58:54', '201', '-150904100', 'CODE:-150904100.LEVEL:1.MSG:1509041001-1509041001北京匹配成交请使用专用接口', '[{"keyword":"北京匹配成交"}]', null, '升级至最新版本可交易北交所债券', '1');
INSERT INTO LCIMS.ERROR_MESSAGE_CONVERT (EID, EITIME, EUTIME, ERR_ACTION, ERR_CODE, ORI_MSG, MATCH_RULES, EXTRACT_RULES, DES_MSG, STATUS) VALUES (214000102593214378, TIMESTAMP '2024-04-30 17:03:56', TIMESTAMP '2024-04-30 17:06:20', '402', '160920800', 'CODE:-160920800,LEVEL:1,MSG:-160920800[160920800]现金宝申请至交收阶段不允许转帐!', '[{"keyword":"不允许转帐"}]', null, '当前时间无法进行转账', '1');
INSERT INTO LCIMS.ERROR_MESSAGE_CONVERT (EID, EITIME, EUTIME, ERR_ACTION, ERR_CODE, ORI_MSG, MATCH_RULES, EXTRACT_RULES, DES_MSG, STATUS) VALUES (214000104814205753, TIMESTAMP '2024-06-28 15:58:23', TIMESTAMP '2024-06-28 15:58:23', '201', '-150904062', '\"errmsg\": \"CODE: -150904062, LEVEL: 1, MSG: -150904062[-150904062]上证基金通禁止在普通买卖菜单委托，请到相应的业务菜单委托!\"', '[{"keyword":"上证基金通禁止在普通买卖菜单委托"}]', null, '该基金不支持买卖，可前往场内基金模块进行申赎操作。', '1');
INSERT INTO LCIMS.ERROR_MESSAGE_CONVERT (EID, EITIME, EUTIME, ERR_ACTION, ERR_CODE, ORI_MSG, MATCH_RULES, EXTRACT_RULES, DES_MSG, STATUS) VALUES (214000105927884403, TIMESTAMP '2024-08-16 17:45:19', TIMESTAMP '2024-08-16 17:45:19', '404', '160918800', 'CODE:？-*********,？LEVEL:？1,？MSG:？-*********[160918800]系统为非正常状态，暂不受理转帐业务，请稍候!', '[{"keyword":"暂不受理转帐业务"}]', null, '当前时间不支持转账业务，请稍后再试', '1');
INSERT INTO LCIMS.ERROR_MESSAGE_CONVERT (EID, EITIME, EUTIME, ERR_ACTION, ERR_CODE, ORI_MSG, MATCH_RULES, EXTRACT_RULES, DES_MSG, STATUS) VALUES (214000102168241938, TIMESTAMP '2024-04-19 16:57:40', TIMESTAMP '2024-04-19 16:57:40', '201', '-150904025', 'CODE: -150904025, LEVEL: 1, MSG: -150904025[-150904025]市价委托证券''159745''的行情信息日期20240308与系统委托日期20240311不符', '[{"keyword":"市价委托"}]', null, '夜市委托期间不支持市价委托', '1');
INSERT INTO LCIMS.ERROR_MESSAGE_CONVERT (EID, EITIME, EUTIME, ERR_ACTION, ERR_CODE, ORI_MSG, MATCH_RULES, EXTRACT_RULES, DES_MSG, STATUS) VALUES (214000102168253637, TIMESTAMP '2024-04-19 16:58:20', TIMESTAMP '2024-04-19 16:58:20', '274', '-150904025', 'CODE: -150904025, LEVEL: 1, MSG: -150904025[-150904025]市价委托证券''159745''的行情信息日期20240308与系统委托日期20240311不符', '[{"keyword":"市价委托"}]', null, '夜市委托期间不支持市价委托', '1');
INSERT INTO LCIMS.ERROR_MESSAGE_CONVERT (EID, EITIME, EUTIME, ERR_ACTION, ERR_CODE, ORI_MSG, MATCH_RULES, EXTRACT_RULES, DES_MSG, STATUS) VALUES (214000102168258188, TIMESTAMP '2024-04-19 16:59:01', TIMESTAMP '2024-04-19 17:00:43', '1201', '-150906052', 'CODE:-150906052,LEVEL:1,MSG:-150906052[-150906052]系统委托日期有误sysdate=20240417,orderdate=20240416', '[{"keyword":"系统委托日期有误"}]', null, '当前时间无法做该项业务', '1');
INSERT INTO LCIMS.ERROR_MESSAGE_CONVERT (EID, EITIME, EUTIME, ERR_ACTION, ERR_CODE, ORI_MSG, MATCH_RULES, EXTRACT_RULES, DES_MSG, STATUS) VALUES (214000103311481886, TIMESTAMP '2024-05-20 15:46:25', TIMESTAMP '2024-05-20 15:46:25', '1350', '-150904926', 'CODE: -150904926, LEVEL: 1, MSG: -150904926[-150904926]信用交易不能做新股申购!', '[{"keyword":"信用交易不能做新股申购"}]', null, '该证券不支持在当前页面交易，请至北交所新股申购功能中操作', '1');
INSERT INTO LCIMS.ERROR_MESSAGE_CONVERT (EID, EITIME, EUTIME, ERR_ACTION, ERR_CODE, ORI_MSG, MATCH_RULES, EXTRACT_RULES, DES_MSG, STATUS) VALUES (214000105927880541, TIMESTAMP '2024-08-16 17:43:08', TIMESTAMP '2024-08-16 17:43:08', '404', '-*********', 'CODE: -*********, LEVEL: 1, MSG: -*********存管转帐银行未做转帐日切不允许转帐！', '[{"keyword":"存管转帐银行未做转帐日切不允许转帐"}]', null, '当前时间不支持转账业务', '1');
INSERT INTO LCIMS.ERROR_MESSAGE_CONVERT (EID, EITIME, EUTIME, ERR_ACTION, ERR_CODE, ORI_MSG, MATCH_RULES, EXTRACT_RULES, DES_MSG, STATUS) VALUES (214000105927881595, TIMESTAMP '2024-08-16 17:43:51', TIMESTAMP '2024-08-16 17:43:51', '402', '-*********', 'CODE:？-*********,？LEVEL:？1,？MSG:？-*********存管转帐银行未做转帐日切不允许转帐！', '[{"keyword":"存管转帐银行未做转帐日切不允许转帐"}]', null, '当前时间不支持转账业务', '1');
INSERT INTO LCIMS.ERROR_MESSAGE_CONVERT (EID, EITIME, EUTIME, ERR_ACTION, ERR_CODE, ORI_MSG, MATCH_RULES, EXTRACT_RULES, DES_MSG, STATUS) VALUES (214000105927882411, TIMESTAMP '2024-08-16 17:44:36', TIMESTAMP '2024-08-16 17:44:36', '402', '160920800', 'CODE: -160920800, LEVEL: 1, MSG: -160920800[160920800]系统为非正常状态，暂不受理转帐业务，请稍候!', '[{"keyword":"暂不受理转帐业务"}]', null, '当前时间不支持转账业务，请稍后再试', '1');
INSERT INTO LCIMS.ERROR_MESSAGE_CONVERT (EID, EITIME, EUTIME, ERR_ACTION, ERR_CODE, ORI_MSG, MATCH_RULES, EXTRACT_RULES, DES_MSG, STATUS) VALUES (214000097250497580, TIMESTAMP '2023-12-15 19:20:13', TIMESTAMP '2023-12-15 19:24:34', '20000', '27900080', 'CODE: 27900080, LEVEL: 1, MSG: 税优产品请使用专用功能交易', '[{"keyword":"税优产品"}]', null, '暂不支持养老金产品，请前往东方财富APP购买', '1');
INSERT INTO LCIMS.ERROR_MESSAGE_CONVERT (EID, EITIME, EUTIME, ERR_ACTION, ERR_CODE, ORI_MSG, MATCH_RULES, EXTRACT_RULES, DES_MSG, STATUS) VALUES (214000098923750222, TIMESTAMP '2024-01-26 15:48:41', TIMESTAMP '2024-01-26 15:48:41', '1226', '1609200008', 'CODE: -1609200008, LEVEL: 1, MSG: -1609200008[1609200008]星期[7]不允许转帐', '[{"keyword":"不允许转帐"}]', null, '当前时间不支持转账业务', '1');
INSERT INTO LCIMS.ERROR_MESSAGE_CONVERT (EID, EITIME, EUTIME, ERR_ACTION, ERR_CODE, ORI_MSG, MATCH_RULES, EXTRACT_RULES, DES_MSG, STATUS) VALUES (214000103311438686, TIMESTAMP '2024-05-20 15:44:38', TIMESTAMP '2024-05-20 15:44:38', '1350', '-990249010', 'CODE: -990249010, LEVEL: 1, MSG: -990249010[-990249010] 市场[B]的买卖类别[0D]没有定义', '[{"keyword":"市场[B]的买卖类别[0D]没有定义"}]', null, '该证券不支持在当前页面交易，请至北交所新股申购功能中操作', '1');
INSERT INTO LCIMS.ERROR_MESSAGE_CONVERT (EID, EITIME, EUTIME, ERR_ACTION, ERR_CODE, ORI_MSG, MATCH_RULES, EXTRACT_RULES, DES_MSG, STATUS) VALUES (214000091475351172, TIMESTAMP '2023-07-19 15:29:24', TIMESTAMP '2023-07-19 15:34:22', '1201', '-410411000', 'CODE:-410411000,LEVEL:1,MSG:-410411000该市场不支持！', '[{"keyword":"该市场不支持"}]', null, '暂不支持北交所两融业务，请通过顶部导航“沪深网页交易”进入东方财富证券网页版交易操作', '1');
INSERT INTO LCIMS.ERROR_MESSAGE_CONVERT (EID, EITIME, EUTIME, ERR_ACTION, ERR_CODE, ORI_MSG, MATCH_RULES, EXTRACT_RULES, DES_MSG, STATUS) VALUES (214000091475376841, TIMESTAMP '2023-07-19 15:30:30', TIMESTAMP '2023-07-19 15:33:57', '1216', '-410410000', 'CODE:-410410000,LEVEL:1,MSG:-410410000该市场不支持！', '[{"keyword":"该市场不支持"}]', null, '暂不支持北交所两融业务，请通过顶部导航“沪深网页交易”进入东方财富证券网页版交易操作', '1');
INSERT INTO LCIMS.ERROR_MESSAGE_CONVERT (EID, EITIME, EUTIME, ERR_ACTION, ERR_CODE, ORI_MSG, MATCH_RULES, EXTRACT_RULES, DES_MSG, STATUS) VALUES (214000007516513928, TIMESTAMP '2017-04-06 16:52:33', TIMESTAMP '2017-04-06 16:52:33', '311', '-411520015', 'CODE: -411520015, LEVEL: 1, MSG: -411520015查询起始日期2015121非法', null, null, '日期格式有误！', '1');
INSERT INTO LCIMS.ERROR_MESSAGE_CONVERT (EID, EITIME, EUTIME, ERR_ACTION, ERR_CODE, ORI_MSG, MATCH_RULES, EXTRACT_RULES, DES_MSG, STATUS) VALUES (214000007516514344, TIMESTAMP '2017-04-06 16:53:13', TIMESTAMP '2017-04-06 16:53:13', '311', '-411520020', 'CODE: -411520020, LEVEL: 1, MSG: -411520020查询结束日期20150931非法', null, null, '日期格式有误！', '1');
INSERT INTO LCIMS.ERROR_MESSAGE_CONVERT (EID, EITIME, EUTIME, ERR_ACTION, ERR_CODE, ORI_MSG, MATCH_RULES, EXTRACT_RULES, DES_MSG, STATUS) VALUES (214000007516527662, TIMESTAMP '2017-04-06 17:00:47', TIMESTAMP '2017-04-06 17:00:47', '203', '-990240270', 'CODE: -990240270, LEVEL: 1, MSG: -990240270[-990240270]委托价位与证券价位不符', null, null, '委托价格有误！', '1');
INSERT INTO LCIMS.ERROR_MESSAGE_CONVERT (EID, EITIME, EUTIME, ERR_ACTION, ERR_CODE, ORI_MSG, MATCH_RULES, EXTRACT_RULES, DES_MSG, STATUS) VALUES (214000003315363405, TIMESTAMP '2016-08-22 14:37:36', TIMESTAMP '2021-10-13 15:03:14', '100', '-980023096', 'CODE: -980023096, LEVEL: 2, MSG: -980023096[-980023096]客户密码错误或账号密码不匹配,还剩余9次机会', null, null, '您输入的信息有误，请重新输入', '1');
INSERT INTO LCIMS.ERROR_MESSAGE_CONVERT (EID, EITIME, EUTIME, ERR_ACTION, ERR_CODE, ORI_MSG, MATCH_RULES, EXTRACT_RULES, DES_MSG, STATUS) VALUES (214000005194032138, TIMESTAMP '2016-10-26 10:24:26', TIMESTAMP '2022-08-02 16:39:33', '203', '-990240280', 'CODE: -990240280, LEVEL: 1, MSG: -990240280[-990240280]委托价格超过涨停价格', null, null, '委托价格不能超过涨停价格。', '1');
INSERT INTO LCIMS.ERROR_MESSAGE_CONVERT (EID, EITIME, EUTIME, ERR_ACTION, ERR_CODE, ORI_MSG, MATCH_RULES, EXTRACT_RULES, DES_MSG, STATUS) VALUES (214000005650328842, TIMESTAMP '2016-11-23 18:27:51', TIMESTAMP '2017-01-24 16:24:12', null, '-168921010', 'CODE: -168921010, LEVEL: 0, MSG: -168921010该资金帐号[0]今天有委托', null, null, '操作变更当日有委托', '1');
INSERT INTO LCIMS.ERROR_MESSAGE_CONVERT (EID, EITIME, EUTIME, ERR_ACTION, ERR_CODE, ORI_MSG, MATCH_RULES, EXTRACT_RULES, DES_MSG, STATUS) VALUES (214000005651382628, TIMESTAMP '2016-11-23 18:33:10', TIMESTAMP '2016-11-23 18:33:10', null, '-168929020', '回绑旧卡：CODE: -168929020, LEVEL: 0, MSG: -168929020该客户已开通银证转账账户或其他行三方存管户!', null, null, '已开通银证转账账户或其他行三方存管户', '1');
INSERT INTO LCIMS.ERROR_MESSAGE_CONVERT (EID, EITIME, EUTIME, ERR_ACTION, ERR_CODE, ORI_MSG, MATCH_RULES, EXTRACT_RULES, DES_MSG, STATUS) VALUES (214000005651956055, TIMESTAMP '2016-11-23 18:34:49', TIMESTAMP '2022-08-02 16:40:23', null, '-168972060', 'CODE: -168972060, LEVEL: 0, MSG: -168972060客户[0]资金余额不等于0,不允许销户', null, null, '资金余额不等于0，无法解绑', '1');
INSERT INTO LCIMS.ERROR_MESSAGE_CONVERT (EID, EITIME, EUTIME, ERR_ACTION, ERR_CODE, ORI_MSG, MATCH_RULES, EXTRACT_RULES, DES_MSG, STATUS) VALUES (214000005655451977, TIMESTAMP '2016-11-23 18:51:19', TIMESTAMP '2022-08-02 16:41:22', null, '-168921013', 'CODE: -168921013, LEVEL: 0, MSG: -168921013客户[0]有转账流水,不允许销户', null, null, '操作变更当日有转账流水，无法解绑', '1');
INSERT INTO LCIMS.ERROR_MESSAGE_CONVERT (EID, EITIME, EUTIME, ERR_ACTION, ERR_CODE, ORI_MSG, MATCH_RULES, EXTRACT_RULES, DES_MSG, STATUS) VALUES (214000006371474910, TIMESTAMP '2016-12-16 14:34:27', TIMESTAMP '2016-12-16 14:34:27', null, '-168923600', '-168923600该客户未做本行的三方存管户预指定!', null, null, '尚未完成银行的三方存管户预指定', '1');
INSERT INTO LCIMS.ERROR_MESSAGE_CONVERT (EID, EITIME, EUTIME, ERR_ACTION, ERR_CODE, ORI_MSG, MATCH_RULES, EXTRACT_RULES, DES_MSG, STATUS) VALUES (214000006371478667, TIMESTAMP '2016-12-16 14:35:57', TIMESTAMP '2016-12-16 14:35:57', null, '1689290004', '-1689290004[1689290004]资金帐号状态不正常', null, null, '资金账号状态异常', '1');
INSERT INTO LCIMS.ERROR_MESSAGE_CONVERT (EID, EITIME, EUTIME, ERR_ACTION, ERR_CODE, ORI_MSG, MATCH_RULES, EXTRACT_RULES, DES_MSG, STATUS) VALUES (214000006371481251, TIMESTAMP '2016-12-16 14:36:45', TIMESTAMP '2016-12-16 14:36:45', null, '1689290006', '-1689290006[1689290006]银行帐号信息已经存在', null, null, '银行账号信息已经存在', '1');
INSERT INTO LCIMS.ERROR_MESSAGE_CONVERT (EID, EITIME, EUTIME, ERR_ACTION, ERR_CODE, ORI_MSG, MATCH_RULES, EXTRACT_RULES, DES_MSG, STATUS) VALUES (214000006371489275, TIMESTAMP '2016-12-16 14:40:03', TIMESTAMP '2016-12-16 14:40:29', null, '-168971110', '-168971110客户 0 状态不正常 3, 0', null, null, '资金账户状态异常', '1');
INSERT INTO LCIMS.ERROR_MESSAGE_CONVERT (EID, EITIME, EUTIME, ERR_ACTION, ERR_CODE, ORI_MSG, MATCH_RULES, EXTRACT_RULES, DES_MSG, STATUS) VALUES (214000006371497874, TIMESTAMP '2016-12-16 14:43:48', TIMESTAMP '2016-12-16 14:43:48', null, '-980013020', '-990257010[-980013020]功能号[168923]在当前时间禁止使用', null, null, '当前时间禁止使用此功能', '1');
INSERT INTO LCIMS.ERROR_MESSAGE_CONVERT (EID, EITIME, EUTIME, ERR_ACTION, ERR_CODE, ORI_MSG, MATCH_RULES, EXTRACT_RULES, DES_MSG, STATUS) VALUES (214000006371498780, TIMESTAMP '2016-12-16 14:44:20', TIMESTAMP '2016-12-16 14:44:20', null, '-980013050', '-990257010[-980013050]柜员代码不存在', null, null, '柜员代码不存在', '1');
INSERT INTO LCIMS.ERROR_MESSAGE_CONVERT (EID, EITIME, EUTIME, ERR_ACTION, ERR_CODE, ORI_MSG, MATCH_RULES, EXTRACT_RULES, DES_MSG, STATUS) VALUES (214000006371502730, TIMESTAMP '2016-12-16 14:45:36', TIMESTAMP '2016-12-16 14:45:36', null, '-168971080', '2007 1 cubsbScOpenAcct 调用失败,-168971080该银行非交易时间!', null, null, '非银行交易时间', '1');
INSERT INTO LCIMS.ERROR_MESSAGE_CONVERT (EID, EITIME, EUTIME, ERR_ACTION, ERR_CODE, ORI_MSG, MATCH_RULES, EXTRACT_RULES, DES_MSG, STATUS) VALUES (214000007515424295, TIMESTAMP '2017-04-06 16:24:40', TIMESTAMP '2017-04-06 16:24:40', '201', '-150908150', 'CODE: -990203010, LEVEL: 1, MSG: -990203010[-150908150]此证券类别禁止该资金分类操作: fundkind=a', null, null, '此证券类别禁止该资金分类操作！', '1');
INSERT INTO LCIMS.ERROR_MESSAGE_CONVERT (EID, EITIME, EUTIME, ERR_ACTION, ERR_CODE, ORI_MSG, MATCH_RULES, EXTRACT_RULES, DES_MSG, STATUS) VALUES (214000007515629199, TIMESTAMP '2017-04-06 16:28:55', TIMESTAMP '2022-08-02 16:42:29', '402', '-160920450', 'CODE: -160920450, LEVEL: 1, MSG: -160920450资金帐户[540600102545]内转资金不允许转出,超出[125000.00]', null, null, '内转资金当日无法转出', '1');
INSERT INTO LCIMS.ERROR_MESSAGE_CONVERT (EID, EITIME, EUTIME, ERR_ACTION, ERR_CODE, ORI_MSG, MATCH_RULES, EXTRACT_RULES, DES_MSG, STATUS) VALUES (214000007516498171, TIMESTAMP '2017-04-06 16:38:13', TIMESTAMP '2017-04-06 16:38:13', '615', '-240563065', 'CODE: -410915020, LEVEL: 1, MSG: -410915020[-240563065]预约取款日期须大于委托日期', null, null, '预约取现日期须大于委托日期！', '1');
INSERT INTO LCIMS.ERROR_MESSAGE_CONVERT (EID, EITIME, EUTIME, ERR_ACTION, ERR_CODE, ORI_MSG, MATCH_RULES, EXTRACT_RULES, DES_MSG, STATUS) VALUES (214000007516500260, TIMESTAMP '2017-04-06 16:39:37', TIMESTAMP '2017-04-06 16:39:37', '615', '-240563140', 'CODE: -410915020, LEVEL: 1, MSG: -410915020[-240563140]该日[20160108]已设置预约取款!', null, null, '已申请预约取现！', '1');
INSERT INTO LCIMS.ERROR_MESSAGE_CONVERT (EID, EITIME, EUTIME, ERR_ACTION, ERR_CODE, ORI_MSG, MATCH_RULES, EXTRACT_RULES, DES_MSG, STATUS) VALUES (214000007516502993, TIMESTAMP '2017-04-06 16:42:27', TIMESTAMP '2017-04-06 16:42:27', '615', '-249958030', 'CODE: -410915020, LEVEL: 1, MSG: -410915020[-240563100][-249958030]系统正在清算,不允许夜市委托,状态status = ''4''', null, null, '当前时间无法夜市委托！', '1');
INSERT INTO LCIMS.ERROR_MESSAGE_CONVERT (EID, EITIME, EUTIME, ERR_ACTION, ERR_CODE, ORI_MSG, MATCH_RULES, EXTRACT_RULES, DES_MSG, STATUS) VALUES (214000007516503629, TIMESTAMP '2017-04-06 16:43:03', TIMESTAMP '2017-04-06 16:43:03', '615', '-249958040', 'CODE: -410915020, LEVEL: 1, MSG: -410915020[-240563100][-249958040]系统非交易状态,不允许委托,状态status = ''9''', null, null, '当前状态无法委托！', '1');
INSERT INTO LCIMS.ERROR_MESSAGE_CONVERT (EID, EITIME, EUTIME, ERR_ACTION, ERR_CODE, ORI_MSG, MATCH_RULES, EXTRACT_RULES, DES_MSG, STATUS) VALUES (214000007516504613, TIMESTAMP '2017-04-06 16:44:13', TIMESTAMP '2017-04-06 16:44:13', '402', '-400605005', 'CODE: -400605005, LEVEL: 1, MSG: -400605005客户/代理人:10171509 交易密码错误', null, null, '交易密码错误！', '1');
INSERT INTO LCIMS.ERROR_MESSAGE_CONVERT (EID, EITIME, EUTIME, ERR_ACTION, ERR_CODE, ORI_MSG, MATCH_RULES, EXTRACT_RULES, DES_MSG, STATUS) VALUES (214000007516506137, TIMESTAMP '2017-04-06 16:45:05', TIMESTAMP '2017-04-06 16:45:05', '202', '-410413005', 'CODE: -410413005, LEVEL: 1, MSG: -410413005客户/代理人:540700201513 交易密码错误', null, null, '交易密码错误！', '1');
INSERT INTO LCIMS.ERROR_MESSAGE_CONVERT (EID, EITIME, EUTIME, ERR_ACTION, ERR_CODE, ORI_MSG, MATCH_RULES, EXTRACT_RULES, DES_MSG, STATUS) VALUES (214000007516507623, TIMESTAMP '2017-04-06 16:46:21', TIMESTAMP '2017-04-06 16:46:21', '203', '-410503005', 'CODE: -410503005, LEVEL: 1, MSG: -410503005客户/代理人:540700148929 交易密码错误', null, null, '交易密码错误！', '1');
INSERT INTO LCIMS.ERROR_MESSAGE_CONVERT (EID, EITIME, EUTIME, ERR_ACTION, ERR_CODE, ORI_MSG, MATCH_RULES, EXTRACT_RULES, DES_MSG, STATUS) VALUES (214000007516510197, TIMESTAMP '2017-04-06 16:48:25', TIMESTAMP '2017-04-06 16:48:25', '404', '-410606005', 'CODE: -410606005, LEVEL: 1, MSG: -410606005客户/代理人:540700251490 交易密码错误', null, null, '交易密码错误！', '1');
INSERT INTO LCIMS.ERROR_MESSAGE_CONVERT (EID, EITIME, EUTIME, ERR_ACTION, ERR_CODE, ORI_MSG, MATCH_RULES, EXTRACT_RULES, DES_MSG, STATUS) VALUES (214000007516510805, TIMESTAMP '2017-04-06 16:49:05', TIMESTAMP '2017-04-06 16:49:05', '403', '-410608005', 'CODE: -410608005, LEVEL: 1, MSG: -410608005客户/代理人:540700075292 交易密码错误', null, null, '交易密码错误！', '1');
INSERT INTO LCIMS.ERROR_MESSAGE_CONVERT (EID, EITIME, EUTIME, ERR_ACTION, ERR_CODE, ORI_MSG, MATCH_RULES, EXTRACT_RULES, DES_MSG, STATUS) VALUES (214000007516515247, TIMESTAMP '2017-04-06 16:54:02', TIMESTAMP '2021-10-13 15:02:14', '100', '-*********', 'CODE: -*********, LEVEL: 2, MSG: -*********[-*********]资金帐号不存在''***********''', null, null, '您输入的信息有误，请重新输入', '1');
INSERT INTO LCIMS.ERROR_MESSAGE_CONVERT (EID, EITIME, EUTIME, ERR_ACTION, ERR_CODE, ORI_MSG, MATCH_RULES, EXTRACT_RULES, DES_MSG, STATUS) VALUES (214000007516543073, TIMESTAMP '2017-04-06 17:16:38', TIMESTAMP '2017-04-06 17:16:38', '402', '*********', 'CODE: -*********, LEVEL: 1, MSG: -*********[*********]转帐金额超出该银行单帐号交易限制!orgid = ''5406'' bankcode = ''2007'' moneytype = ''0'' fundid = ************ fundeffect = 500000.00 limit = ********.00 total = ********.00', null, null, '转账金额超出该银行的单账号交易限制！', '1');
INSERT INTO LCIMS.ERROR_MESSAGE_CONVERT (EID, EITIME, EUTIME, ERR_ACTION, ERR_CODE, ORI_MSG, MATCH_RULES, EXTRACT_RULES, DES_MSG, STATUS) VALUES (214000007516545904, TIMESTAMP '2017-04-06 17:19:35', TIMESTAMP '2017-04-06 17:19:35', '402', '*********', 'CODE: -*********, LEVEL: 1, MSG: -*********[*********]转帐金额错!fundeffect = 0.00', null, null, '转账金额有误！', '1');
INSERT INTO LCIMS.ERROR_MESSAGE_CONVERT (EID, EITIME, EUTIME, ERR_ACTION, ERR_CODE, ORI_MSG, MATCH_RULES, EXTRACT_RULES, DES_MSG, STATUS) VALUES (214000008506146154, TIMESTAMP '2017-07-28 23:48:24', TIMESTAMP '2017-07-31 13:47:51', '901', '-********', 'CODE: 100105, LEVEL: 0, MSG: 远程调用失败:业务级错误[错误码:-********,错误信息:调用KCBP_NODE:33,LBM_ID:L3100009,-********客户号:''10132377'' 密码锁定 状态不正常]', null, null, '密码锁定，暂无法修改', '1');
INSERT INTO LCIMS.ERROR_MESSAGE_CONVERT (EID, EITIME, EUTIME, ERR_ACTION, ERR_CODE, ORI_MSG, MATCH_RULES, EXTRACT_RULES, DES_MSG, STATUS) VALUES (214000069047260502, TIMESTAMP '2021-12-17 16:02:51', TIMESTAMP '2021-12-17 16:02:51', '274', '-150905023', 'CODE:-150905023,LEVEL:1,MSG:-150905023首日指定交易的客户，下一个交易日才可开展债券现券的交易申报', '[{"keyword":"首日指定交易的客户"}]', null, '新开户用户，下一个交易日起才可以参与上交所债券/国债逆回购交易', '1');
INSERT INTO LCIMS.ERROR_MESSAGE_CONVERT (EID, EITIME, EUTIME, ERR_ACTION, ERR_CODE, ORI_MSG, MATCH_RULES, EXTRACT_RULES, DES_MSG, STATUS) VALUES (214000074268731181, TIMESTAMP '2022-05-15 16:31:35', TIMESTAMP '2022-08-02 17:07:55', '203', '-150918003', 'CODE: -150918003, LEVEL: 1, MSG: -150918003取债券通用质押式回购业务参数[市场:0,证券代码:131810,交易方式:1]失败！', '[{"keyword":"取债券通用质押式回购业务参数"}]', null, '当前时间无法做该项业务', '1');
INSERT INTO LCIMS.ERROR_MESSAGE_CONVERT (EID, EITIME, EUTIME, ERR_ACTION, ERR_CODE, ORI_MSG, MATCH_RULES, EXTRACT_RULES, DES_MSG, STATUS) VALUES (214000074268928866, TIMESTAMP '2022-05-15 17:30:15', TIMESTAMP '2022-08-02 17:08:58', '201', '-150918003', 'CODE: -150918003, LEVEL: 1, MSG: -150918003取债券通用质押式回购业务参数[市场:0,证券代码:131810,交易方式:1]失败！', '[{"keyword":"取债券通用质押式回购业务参数"}]', null, '当前时间无法做该项业务', '1');
INSERT INTO LCIMS.ERROR_MESSAGE_CONVERT (EID, EITIME, EUTIME, ERR_ACTION, ERR_CODE, ORI_MSG, MATCH_RULES, EXTRACT_RULES, DES_MSG, STATUS) VALUES (214000074269430016, TIMESTAMP '2022-05-15 20:01:00', TIMESTAMP '2022-08-02 17:09:18', '274', '-150918003', 'CODE: -150918003, LEVEL: 1, MSG: -150918003取债券通用质押式回购业务参数[市场:0,证券代码:131810,交易方式:1]失败！', '[{"keyword":"取债券通用质押式回购业务参数"}]', null, '当前时间无法做该项业务', '1');
INSERT INTO LCIMS.ERROR_MESSAGE_CONVERT (EID, EITIME, EUTIME, ERR_ACTION, ERR_CODE, ORI_MSG, MATCH_RULES, EXTRACT_RULES, DES_MSG, STATUS) VALUES (214000074519293060, TIMESTAMP '2022-05-20 16:26:56', TIMESTAMP '2022-05-20 16:26:56', '201', '-150905287', 'CODE: -150905360, LEVEL: 1, MSG: -150905360[-150905287]该证券账户[A621532881]无此权限[债券专业投资者权限]', '[{"keyword":"无此权限[债券专业投资者权限]"}]', null, '您暂未开通该市场债券专业投资者权限，无法交易该债券，详询客服热线95357。', '1');
INSERT INTO LCIMS.ERROR_MESSAGE_CONVERT (EID, EITIME, EUTIME, ERR_ACTION, ERR_CODE, ORI_MSG, MATCH_RULES, EXTRACT_RULES, DES_MSG, STATUS) VALUES (214000074519307146, TIMESTAMP '2022-05-20 16:29:48', TIMESTAMP '2022-05-20 16:29:48', '201', '-990203013', 'CODE: -990203013, LEVEL: 1, MSG: -990203013[-990203013]股东账号不能为空', '[{"keyword":"股东账号不能为空"}]', null, '您尚未开通该市场股东账号，详询客服热线95357。', '1');
INSERT INTO LCIMS.ERROR_MESSAGE_CONVERT (EID, EITIME, EUTIME, ERR_ACTION, ERR_CODE, ORI_MSG, MATCH_RULES, EXTRACT_RULES, DES_MSG, STATUS) VALUES (214000075865271567, TIMESTAMP '2022-06-24 17:23:44', TIMESTAMP '2022-08-02 17:10:01', '279', '-410450062', 'CODE: -410450062, LEVEL: 1, MSG: -410450062证券[123147]价格为0，不允许重置成本价', '[{"keyword":"不允许重置成本价"}]', null, '当前证券暂无法进行该项业务', '1');
INSERT INTO LCIMS.ERROR_MESSAGE_CONVERT (EID, EITIME, EUTIME, ERR_ACTION, ERR_CODE, ORI_MSG, MATCH_RULES, EXTRACT_RULES, DES_MSG, STATUS) VALUES (214000076692376124, TIMESTAMP '2022-07-15 15:35:18', TIMESTAMP '2022-07-15 15:35:18', '1201', '-150906995', 'CODE: -150906995, LEVEL: 1, MSG: -150906995未找到该证券非交易信息\nstkcode = 118001, bsflag = 0G', '[{"keyword":"未找到该证券非交易信息"}]', null, '未找到该证券非交易信息', '1');
INSERT INTO LCIMS.ERROR_MESSAGE_CONVERT (EID, EITIME, EUTIME, ERR_ACTION, ERR_CODE, ORI_MSG, MATCH_RULES, EXTRACT_RULES, DES_MSG, STATUS) VALUES (214000076692389250, TIMESTAMP '2022-07-15 15:35:59', TIMESTAMP '2022-07-15 15:35:59', '1202', '-990268050', 'CODE: -410413020, LEVEL: 1, MSG: -410413020[-990268050]该笔委托已经全部成交或全部撤单', '[{"keyword":"该笔委托已经全部成交或全部撤单"}]', null, '撤单失败，该笔委托已经全部成交或全部撤单', '1');
INSERT INTO LCIMS.ERROR_MESSAGE_CONVERT (EID, EITIME, EUTIME, ERR_ACTION, ERR_CODE, ORI_MSG, MATCH_RULES, EXTRACT_RULES, DES_MSG, STATUS) VALUES (214000076692405694, TIMESTAMP '2022-07-15 15:36:37', TIMESTAMP '2022-07-15 15:36:37', '202', '-990268050', 'CODE: -410413020, LEVEL: 1, MSG: -410413020[-990268050]该笔委托已经全部成交或全部撤单', '[{"keyword":"该笔委托已经全部成交或全部撤单"}]', null, '撤单失败，该笔委托已经全部成交或全部撤单', '1');
INSERT INTO LCIMS.ERROR_MESSAGE_CONVERT (EID, EITIME, EUTIME, ERR_ACTION, ERR_CODE, ORI_MSG, MATCH_RULES, EXTRACT_RULES, DES_MSG, STATUS) VALUES (214000076898831750, TIMESTAMP '2022-07-21 16:09:56', TIMESTAMP '2022-07-21 16:09:56', '274', '-150906089', 'CODE: -150906089, LEVEL: 1, MSG: -150906089[-150906089]ETF基本信息无参数记录!', '[{"keyword":"ETF基本信息无参数记录"}]', null, '暂不支持交易该证券，如有疑问请联系客服95357', '1');
INSERT INTO LCIMS.ERROR_MESSAGE_CONVERT (EID, EITIME, EUTIME, ERR_ACTION, ERR_CODE, ORI_MSG, MATCH_RULES, EXTRACT_RULES, DES_MSG, STATUS) VALUES (214000076898841759, TIMESTAMP '2022-07-21 16:10:30', TIMESTAMP '2022-07-21 16:10:30', '274', '-150906089', 'CODE: -150906089, LEVEL: 1, MSG: -150906089[-150906089],ETF初始化日期[20161111]与系统日期[20220721]不一致，请初始化该ETF当日申赎清单!', '[{"keyword":"请初始化该ETF当日申赎清单"}]', null, '暂不支持交易该证券，如有疑问请联系客服95357', '1');
INSERT INTO LCIMS.ERROR_MESSAGE_CONVERT (EID, EITIME, EUTIME, ERR_ACTION, ERR_CODE, ORI_MSG, MATCH_RULES, EXTRACT_RULES, DES_MSG, STATUS) VALUES (214000076898848198, TIMESTAMP '2022-07-21 16:11:09', TIMESTAMP '2022-07-21 16:11:09', '204', '-150906089', 'CODE: -150906089, LEVEL: 1, MSG: -150906089[-150906089],ETF初始化日期[20161111]与系统日期[20220721]不一致，请初始化该ETF当日申赎清单!', '[{"keyword":"请初始化该ETF当日申赎清单"}]', null, '暂不支持该基金的申赎委托', '1');
INSERT INTO LCIMS.ERROR_MESSAGE_CONVERT (EID, EITIME, EUTIME, ERR_ACTION, ERR_CODE, ORI_MSG, MATCH_RULES, EXTRACT_RULES, DES_MSG, STATUS) VALUES (214000077107160154, TIMESTAMP '2022-07-27 18:05:03', TIMESTAMP '2022-07-27 18:06:12', '101', '11000041', 'CODE:11000041,LEVEL:0,MSG:用户[540700009520]的旧认证数据不正确', '[{"keyword":"旧认证数据不正确"}]', null, '原资金密码不正确，请重新输入', '1');
INSERT INTO LCIMS.ERROR_MESSAGE_CONVERT (EID, EITIME, EUTIME, ERR_ACTION, ERR_CODE, ORI_MSG, MATCH_RULES, EXTRACT_RULES, DES_MSG, STATUS) VALUES (214000077995356697, TIMESTAMP '2022-08-18 16:01:37', TIMESTAMP '2022-08-18 16:01:37', '1201', '-150906106', '-150622020[-150906106]债券信息[%s]计息日期与委托日期不符，请在系统刷新当日行情后委托', '[{"keyword":"请在系统刷新当日行情后委托"}]', null, '当前时间不支持进行该项业务', '1');
INSERT INTO LCIMS.ERROR_MESSAGE_CONVERT (EID, EITIME, EUTIME, ERR_ACTION, ERR_CODE, ORI_MSG, MATCH_RULES, EXTRACT_RULES, DES_MSG, STATUS) VALUES (214000078490106653, TIMESTAMP '2022-08-31 08:42:09', TIMESTAMP '2022-08-31 08:42:09', '101', '11000113', 'CODE: 11000113, LEVEL: 1, MSG: 客户[110700000005]状态异常', '[{"keyword":"状态异常"}]', null, '密码修改失败，请联系客服95357协助处理。', '1');
INSERT INTO LCIMS.ERROR_MESSAGE_CONVERT (EID, EITIME, EUTIME, ERR_ACTION, ERR_CODE, ORI_MSG, MATCH_RULES, EXTRACT_RULES, DES_MSG, STATUS) VALUES (214000086930715744, TIMESTAMP '2023-03-31 21:24:38', TIMESTAMP '2023-03-31 21:24:38', '100', '-410301060', 'CODE: -410301060, LEVEL: 2, MSG: -410301060账户处于密码锁定状态，将于下一交易日自动解锁。', '[{"keyword":"账户处于密码锁定状态，将于下一交易日自动解锁。"}]', null, '账户处于密码锁定状态，请至登录-遇到问题中申请解锁，或等到下一交易日自动解锁。', '1');
INSERT INTO LCIMS.ERROR_MESSAGE_CONVERT (EID, EITIME, EUTIME, ERR_ACTION, ERR_CODE, ORI_MSG, MATCH_RULES, EXTRACT_RULES, DES_MSG, STATUS) VALUES (214000056014759383, TIMESTAMP '2020-11-13 19:13:53', TIMESTAMP '2022-08-02 16:56:30', '231', '-999003088', '[-999003088]系统正在初始化,不允许夜市委托,系统状态status = ''1''', '[{"keyword":"不允许夜市委托"}]', null, '当前时间无法做该项业务', '1');
INSERT INTO LCIMS.ERROR_MESSAGE_CONVERT (EID, EITIME, EUTIME, ERR_ACTION, ERR_CODE, ORI_MSG, MATCH_RULES, EXTRACT_RULES, DES_MSG, STATUS) VALUES (214000056014785056, TIMESTAMP '2020-11-13 19:18:24', TIMESTAMP '2022-08-02 16:58:29', '274', '-999003088', '[-999003088]系统初始化前备份,不允许夜市委托,系统状态status = ''5''', '[{"keyword":"系统初始化前备份"}]', null, '当前时间无法做该项业务。预计15分钟以后可以进行沪深A股委托，请稍后再试。', '1');
INSERT INTO LCIMS.ERROR_MESSAGE_CONVERT (EID, EITIME, EUTIME, ERR_ACTION, ERR_CODE, ORI_MSG, MATCH_RULES, EXTRACT_RULES, DES_MSG, STATUS) VALUES (214000056014788878, TIMESTAMP '2020-11-13 19:19:21', TIMESTAMP '2022-08-02 16:58:49', '274', '-999003088', '[-999003088]系统正在初始化,不允许夜市委托,系统状态status = ''1''', '[{"keyword":"系统正在初始化"}]', null, '当前时间无法做该项业务。预计10分钟以后可以进行沪深A股委托，请稍后再试。', '1');
INSERT INTO LCIMS.ERROR_MESSAGE_CONVERT (EID, EITIME, EUTIME, ERR_ACTION, ERR_CODE, ORI_MSG, MATCH_RULES, EXTRACT_RULES, DES_MSG, STATUS) VALUES (214000056014792410, TIMESTAMP '2020-11-13 19:20:05', TIMESTAMP '2022-08-02 16:59:02', '274', '-999003088', '[-999003088]系统已交收尚未初始化,不允许夜市委托,系统状态status = ''9''', '[{"keyword":"不允许夜市委托"}]', null, '当前时间无法做该项业务', '1');
INSERT INTO LCIMS.ERROR_MESSAGE_CONVERT (EID, EITIME, EUTIME, ERR_ACTION, ERR_CODE, ORI_MSG, MATCH_RULES, EXTRACT_RULES, DES_MSG, STATUS) VALUES (214000056014796261, TIMESTAMP '2020-11-13 19:21:00', TIMESTAMP '2022-08-02 16:59:22', '202', '-999003088', '[-999003088]系统正在初始化,不允许夜市委托,系统状态status = ''1''', '[{"keyword":"系统正在初始化"}]', null, '当前时间无法做该项业务，预计10分钟以后可以进行沪深A股委托，请稍后再试。', '1');
INSERT INTO LCIMS.ERROR_MESSAGE_CONVERT (EID, EITIME, EUTIME, ERR_ACTION, ERR_CODE, ORI_MSG, MATCH_RULES, EXTRACT_RULES, DES_MSG, STATUS) VALUES (214000056014801240, TIMESTAMP '2020-11-13 19:21:55', TIMESTAMP '2022-08-02 16:59:36', '202', '-999003088', '[-999003088]系统初始化前备份,不允许夜市委托,系统状态status = ''5''', '[{"keyword":"系统初始化前备份"}]', null, '当前时间无法做该项业务，预计15分钟以后可以进行沪深A股委托，请稍后再试。', '1');
INSERT INTO LCIMS.ERROR_MESSAGE_CONVERT (EID, EITIME, EUTIME, ERR_ACTION, ERR_CODE, ORI_MSG, MATCH_RULES, EXTRACT_RULES, DES_MSG, STATUS) VALUES (214000056014809181, TIMESTAMP '2020-11-13 19:23:35', TIMESTAMP '2022-08-02 17:00:10', '204', '-999003088', '[-999003088]系统正在初始化,不允许夜市委托,系统状态status = ''1''', '[{"keyword":"不允许夜市委托"}]', null, '当前时间无法做该项业务', '1');
INSERT INTO LCIMS.ERROR_MESSAGE_CONVERT (EID, EITIME, EUTIME, ERR_ACTION, ERR_CODE, ORI_MSG, MATCH_RULES, EXTRACT_RULES, DES_MSG, STATUS) VALUES (214000056753638535, TIMESTAMP '2020-12-10 16:53:44', TIMESTAMP '2020-12-10 16:53:44', '424', '-1002090440', '-1002090440当前系统号状态4,报价回购流程状态5不允许做T+2日的计划转账', '[{"keyword":"不允许做T+2日的计划转账"}]', null, '当前时间暂不支持操作，请稍后重试', '1');
INSERT INTO LCIMS.ERROR_MESSAGE_CONVERT (EID, EITIME, EUTIME, ERR_ACTION, ERR_CODE, ORI_MSG, MATCH_RULES, EXTRACT_RULES, DES_MSG, STATUS) VALUES (214000056753658473, TIMESTAMP '2020-12-10 16:56:42', TIMESTAMP '2020-12-10 16:56:42', '424', '-1002090430', '-1002090430当前系统号状态0,报价回购流程状态5不允许做T+1日的计划转账', '[{"keyword":"不允许做T+1日的计划转账"}]', null, '当前时间暂不支持操作，请稍后重试', '1');
INSERT INTO LCIMS.ERROR_MESSAGE_CONVERT (EID, EITIME, EUTIME, ERR_ACTION, ERR_CODE, ORI_MSG, MATCH_RULES, EXTRACT_RULES, DES_MSG, STATUS) VALUES (214000057774127212, TIMESTAMP '2021-01-12 17:02:39', TIMESTAMP '2022-08-02 17:01:26', '1282', '-411550030', 'CODE: -411550030, LEVEL: 1, MSG: -411550030客户无中签预冻结资金', '[{"keyword":"客户无中签预冻结资金"}]', null, '当前时间无法做该项业务，请稍后再试，预计1小时后可以解冻资金。', '1');
INSERT INTO LCIMS.ERROR_MESSAGE_CONVERT (EID, EITIME, EUTIME, ERR_ACTION, ERR_CODE, ORI_MSG, MATCH_RULES, EXTRACT_RULES, DES_MSG, STATUS) VALUES (214000061458116109, TIMESTAMP '2021-05-14 16:43:00', TIMESTAMP '2021-05-14 16:43:00', '274', '-150905371', 'CODE: -150622020, LEVEL: 1, MSG: -150622020[-150905371]该证券账户[A357034267]无此权限[开通风险警示证券买入]!', '[{"keyword":"开通风险警示证券买入"}]', null, '您尚未开通该市场风险警示股票交易权限，请至业务办理功能开通权限', '1');
INSERT INTO LCIMS.ERROR_MESSAGE_CONVERT (EID, EITIME, EUTIME, ERR_ACTION, ERR_CODE, ORI_MSG, MATCH_RULES, EXTRACT_RULES, DES_MSG, STATUS) VALUES (214000061682925860, TIMESTAMP '2021-05-21 15:31:05', TIMESTAMP '2021-05-21 15:31:05', '1201', '-410411021', 'CODE:-410411021,LEVEL:1,MSG:-410411021信用交易类型为[A]时，买卖类别必须为[0S]', '[{"keyword":"信用交易类型为[A]时，买卖类别必须为[0S]"}]', null, '融券卖出时不能使用市价委托，请使用限价委托方式', '1');
INSERT INTO LCIMS.ERROR_MESSAGE_CONVERT (EID, EITIME, EUTIME, ERR_ACTION, ERR_CODE, ORI_MSG, MATCH_RULES, EXTRACT_RULES, DES_MSG, STATUS) VALUES (214000061926644503, TIMESTAMP '2021-05-28 17:50:33', TIMESTAMP '2021-05-28 17:50:33', '204', '-150905065', 'CODE: -150905360, LEVEL: 1, MSG: -150905360[-150905065]该证券账户[0097398356]无此权限[基础设施基金交易权限]!，不允许进行基础设施基金交易!', '[{"keyword":"无此权限[基础设施基金交易权限]"}]', null, '您暂未开通该市场基础设施基金交易权限，无法交易该基金，请至业务办理功能开通权限，详询客服热线95357。', '1');
INSERT INTO LCIMS.ERROR_MESSAGE_CONVERT (EID, EITIME, EUTIME, ERR_ACTION, ERR_CODE, ORI_MSG, MATCH_RULES, EXTRACT_RULES, DES_MSG, STATUS) VALUES (214000005650082125, TIMESTAMP '2016-11-23 17:54:08', TIMESTAMP '2016-11-29 14:57:22', null, '160903010', 'CODE: -168000129, LEVEL: 0, MSG: -168000129[160903010]无此客户银行帐户信息', null, null, '查询不到原存管银行账户信息', '1');
INSERT INTO LCIMS.ERROR_MESSAGE_CONVERT (EID, EITIME, EUTIME, ERR_ACTION, ERR_CODE, ORI_MSG, MATCH_RULES, EXTRACT_RULES, DES_MSG, STATUS) VALUES (214000061926650897, TIMESTAMP '2021-05-28 17:51:17', TIMESTAMP '2021-05-28 17:51:17', '274', '-150905065', 'CODE: -150905360, LEVEL: 1, MSG: -150905360[-150905065]该证券账户[0097398356]无此权限[基础设施基金交易权限]!，不允许进行基础设施基金交易!', '[{"keyword":"无此权限[基础设施基金交易权限]"}]', null, '您暂未开通该市场基础设施基金交易权限，无法交易该基金，请至业务办理功能开通权限，详询客服热线95357。', '1');
INSERT INTO LCIMS.ERROR_MESSAGE_CONVERT (EID, EITIME, EUTIME, ERR_ACTION, ERR_CODE, ORI_MSG, MATCH_RULES, EXTRACT_RULES, DES_MSG, STATUS) VALUES (214000005650493927, TIMESTAMP '2016-11-23 18:30:55', TIMESTAMP '2022-08-02 16:39:55', null, '-168921310', 'CODE: -168921310, LEVEL: 0, MSG: -168921310客户[0]存在解冻资金,不允许销户', null, null, '存在解冻资金，无法解绑', '1');
INSERT INTO LCIMS.ERROR_MESSAGE_CONVERT (EID, EITIME, EUTIME, ERR_ACTION, ERR_CODE, ORI_MSG, MATCH_RULES, EXTRACT_RULES, DES_MSG, STATUS) VALUES (214000005652838841, TIMESTAMP '2016-11-23 18:36:40', TIMESTAMP '2022-08-02 16:40:49', null, '-168921100', 'CODE: -168921100, LEVEL: 0, MSG: -168921100客户下挂股东[0]有未缴股息红利税，不允许销户!', null, null, '有未缴股息红利税，无法解绑', '1');
INSERT INTO LCIMS.ERROR_MESSAGE_CONVERT (EID, EITIME, EUTIME, ERR_ACTION, ERR_CODE, ORI_MSG, MATCH_RULES, EXTRACT_RULES, DES_MSG, STATUS) VALUES (214000005655553245, TIMESTAMP '2016-11-23 18:52:37', TIMESTAMP '2022-08-02 16:41:44', null, '-168921982', 'CODE: -168921982, LEVEL: 0, MSG: -168921982资金帐号[0]有场外开代交易类委托存在未处理委托,不允许销户', null, null, '资金账户有场外开代交易类委托/存在未处理委托，无法解绑', '1');
INSERT INTO LCIMS.ERROR_MESSAGE_CONVERT (EID, EITIME, EUTIME, ERR_ACTION, ERR_CODE, ORI_MSG, MATCH_RULES, EXTRACT_RULES, DES_MSG, STATUS) VALUES (214000005655656321, TIMESTAMP '2016-11-23 18:54:38', TIMESTAMP '2016-11-23 19:00:21', null, '-168921908', 'CODE: -168921908, LEVEL: 0, MSG: -168921908资金帐户[0]为非存管帐户!', null, null, '资金账户为非存管账户', '1');
INSERT INTO LCIMS.ERROR_MESSAGE_CONVERT (EID, EITIME, EUTIME, ERR_ACTION, ERR_CODE, ORI_MSG, MATCH_RULES, EXTRACT_RULES, DES_MSG, STATUS) VALUES (214000006211580221, TIMESTAMP '2016-12-06 17:01:15', TIMESTAMP '2016-12-08 10:32:09', '402', '168000026', 'CODE: -160920120, LEVEL: 1, MSG: -160920120[168000026]资金密码错误fundid = 540800232020', null, null, '资金密码错误！', '1');
INSERT INTO LCIMS.ERROR_MESSAGE_CONVERT (EID, EITIME, EUTIME, ERR_ACTION, ERR_CODE, ORI_MSG, MATCH_RULES, EXTRACT_RULES, DES_MSG, STATUS) VALUES (214000006371492123, TIMESTAMP '2016-12-16 14:41:46', TIMESTAMP '2016-12-16 14:41:46', null, '-168971270', '-168971270该资金帐号此银行已开过户!', null, null, '资金账号已在银行完成开户', '1');
INSERT INTO LCIMS.ERROR_MESSAGE_CONVERT (EID, EITIME, EUTIME, ERR_ACTION, ERR_CODE, ORI_MSG, MATCH_RULES, EXTRACT_RULES, DES_MSG, STATUS) VALUES (214000006371495585, TIMESTAMP '2016-12-16 14:42:45', TIMESTAMP '2016-12-16 14:42:45', null, '-990220010', '-168971320[-990220010]柜员(5403555)不能操作该机构', null, null, '柜员不能操作该机构', '1');
INSERT INTO LCIMS.ERROR_MESSAGE_CONVERT (EID, EITIME, EUTIME, ERR_ACTION, ERR_CODE, ORI_MSG, MATCH_RULES, EXTRACT_RULES, DES_MSG, STATUS) VALUES (214000007516538481, TIMESTAMP '2017-04-06 17:12:07', TIMESTAMP '2017-04-06 17:12:07', '302', '-990290010', 'CODE: -410529050, LEVEL: 1, MSG: -410529050[-990290010]资金信息不存在, fundid=540300076267, Orgid=5403, Moneytype=0', null, null, '资产查询失败，如有疑问请联系客服95357！', '1');
INSERT INTO LCIMS.ERROR_MESSAGE_CONVERT (EID, EITIME, EUTIME, ERR_ACTION, ERR_CODE, ORI_MSG, MATCH_RULES, EXTRACT_RULES, DES_MSG, STATUS) VALUES (214000007516540004, TIMESTAMP '2017-04-06 17:13:48', TIMESTAMP '2017-04-06 17:13:48', '901', '11000043', 'CODE: 11000043, LEVEL: 1, MSG: 用户[607687]没有设置过认证数据,无法重置', null, null, '无法重置密码，如有疑问请联系客服95357！', '1');
INSERT INTO LCIMS.ERROR_MESSAGE_CONVERT (EID, EITIME, EUTIME, ERR_ACTION, ERR_CODE, ORI_MSG, MATCH_RULES, EXTRACT_RULES, DES_MSG, STATUS) VALUES (214000007516540529, TIMESTAMP '2017-04-06 17:14:29', TIMESTAMP '2017-04-06 17:14:29', '901', '12000200', 'CODE: 12000200, LEVEL: 0, MSG: 资产账户代码[0]在系统内尚未开户', null, null, '尚未开户！', '1');
INSERT INTO LCIMS.ERROR_MESSAGE_CONVERT (EID, EITIME, EUTIME, ERR_ACTION, ERR_CODE, ORI_MSG, MATCH_RULES, EXTRACT_RULES, DES_MSG, STATUS) VALUES (214000010473852541, TIMESTAMP '2017-11-14 14:47:23', TIMESTAMP '2022-08-02 16:42:51', '20000', '27000003', 'CODE: 27000003, LEVEL: 1, MSG: 市场[机构间市场]当前时间不允许下单', null, null, '当前时间无法下单', '1');
INSERT INTO LCIMS.ERROR_MESSAGE_CONVERT (EID, EITIME, EUTIME, ERR_ACTION, ERR_CODE, ORI_MSG, MATCH_RULES, EXTRACT_RULES, DES_MSG, STATUS) VALUES (214000010473971462, TIMESTAMP '2017-11-14 14:53:18', TIMESTAMP '2018-07-21 15:43:47', '20000', '27040031', 'CODE: 27040031, LEVEL: 1, MSG: 委托金额不能低于首次最低金额[50000.00]', null, null, '委托金额不能低于首次最低金额', '1');
INSERT INTO LCIMS.ERROR_MESSAGE_CONVERT (EID, EITIME, EUTIME, ERR_ACTION, ERR_CODE, ORI_MSG, MATCH_RULES, EXTRACT_RULES, DES_MSG, STATUS) VALUES (214000021042357658, TIMESTAMP '2019-01-16 10:48:25', TIMESTAMP '2019-01-16 10:50:46', '410605', '168000021', 'CODE:-160920120  LEVEL: 1    MSG:-160920120[168000021]该资金帐号禁止券商端发起银证转出\n" ?"orgid = ''%s'' fundid = %I64d" ?"fundlimit = ''B', null, null, '您可能由于以下原因被限制资金转出：1.在我司存留的身份证明文件过期；2.融资融券、财富贷平仓后资不抵债；3.司法冻结；4.交易所重点监控账户限制；5.其他您签署的文件授权我司有权进行限制的情况。如有疑问，请联系客服95357。', '1');
INSERT INTO LCIMS.ERROR_MESSAGE_CONVERT (EID, EITIME, EUTIME, ERR_ACTION, ERR_CODE, ORI_MSG, MATCH_RULES, EXTRACT_RULES, DES_MSG, STATUS) VALUES (214000044242310506, TIMESTAMP '2020-08-03 17:44:32', TIMESTAMP '2020-08-03 17:44:32', '201', '-150905860', '-150622020[-150905860]该证券账户[0128331107]无此权限[新创业板权限]！', null, null, '您暂未开通创业板交易权限或尚未重新签署《创业板投资风险揭示书》，无法买卖创业板股票，请前往开通或签署相关协议。', '1');
INSERT INTO LCIMS.ERROR_MESSAGE_CONVERT (EID, EITIME, EUTIME, ERR_ACTION, ERR_CODE, ORI_MSG, MATCH_RULES, EXTRACT_RULES, DES_MSG, STATUS) VALUES (214000010474098471, TIMESTAMP '2017-11-14 14:59:05', TIMESTAMP '2017-11-14 14:59:05', '20000', '27050005', 'CODE: 27050005, LEVEL: 1, MSG: 客户[540700136935]电子合同签署记录不存在', null, null, '请先签署相关产品协议与说明', '1');
INSERT INTO LCIMS.ERROR_MESSAGE_CONVERT (EID, EITIME, EUTIME, ERR_ACTION, ERR_CODE, ORI_MSG, MATCH_RULES, EXTRACT_RULES, DES_MSG, STATUS) VALUES (214000010474113918, TIMESTAMP '2017-11-14 15:00:04', TIMESTAMP '2017-11-14 15:00:04', '20000', '27020034', 'CODE: 27020034, LEVEL: 1, MSG: 产品编码[SV3396]机构[5401]申请额度[********.00]超过可用额度[8900000.00]', null, null, '您输入的投资金额超过该产品的剩余额度，请重新输入', '1');
INSERT INTO LCIMS.ERROR_MESSAGE_CONVERT (EID, EITIME, EUTIME, ERR_ACTION, ERR_CODE, ORI_MSG, MATCH_RULES, EXTRACT_RULES, DES_MSG, STATUS) VALUES (214000031180892380, TIMESTAMP '2019-06-13 18:02:33', TIMESTAMP '2019-06-13 18:02:33', '402', '-168000015', 'CODE: -168000015, LEVEL: 1, MSG: -168000015系统当常状态为[9],不允许做转帐!', null, null, '系统维护中，当前暂不支持银证转账。', '1');
INSERT INTO LCIMS.ERROR_MESSAGE_CONVERT (EID, EITIME, EUTIME, ERR_ACTION, ERR_CODE, ORI_MSG, MATCH_RULES, EXTRACT_RULES, DES_MSG, STATUS) VALUES (214000044242318375, TIMESTAMP '2020-08-03 17:45:10', TIMESTAMP '2020-08-03 17:45:10', '201', '-150905861', '-150622020[-150905861]该证券账户[0128331108]无此权限[新创业板权限]！', null, null, '您暂未开通创业板交易权限或尚未重新签署《创业板投资风险揭示书》，无法买卖创业板股票，请前往开通或签署相关协议。', '1');
INSERT INTO LCIMS.ERROR_MESSAGE_CONVERT (EID, EITIME, EUTIME, ERR_ACTION, ERR_CODE, ORI_MSG, MATCH_RULES, EXTRACT_RULES, DES_MSG, STATUS) VALUES (214000025921143048, TIMESTAMP '2019-03-29 16:15:21', TIMESTAMP '2019-03-29 16:15:21', '1226', '160911030', 'CODE:*********, LEVEL: 1, MSG: -*********[160911030]该银行非交易时间', null, null, '该银行非交易时间', '1');
INSERT INTO LCIMS.ERROR_MESSAGE_CONVERT (EID, EITIME, EUTIME, ERR_ACTION, ERR_CODE, ORI_MSG, MATCH_RULES, EXTRACT_RULES, DES_MSG, STATUS) VALUES (214000012024205149, TIMESTAMP '2018-02-09 14:12:07', TIMESTAMP '2018-02-09 14:12:07', '624', '-249256600', 'CODE: -410831030, LEVEL: 1, MSG: -410831030[-249256600]申请金额超过本产品可取现余额,当前可取现余额：0.00', null, null, '今日天天宝快速取现总额度已用完，请进行普通取现或于下一交易日进行快速取现', '1');
INSERT INTO LCIMS.ERROR_MESSAGE_CONVERT (EID, EITIME, EUTIME, ERR_ACTION, ERR_CODE, ORI_MSG, MATCH_RULES, EXTRACT_RULES, DES_MSG, STATUS) VALUES (214000036440846348, TIMESTAMP '2019-09-06 15:29:10', TIMESTAMP '2019-11-12 15:48:33', null, '*********', 'CODE: -*********, LEVEL: 1, MSG: -*********[*********]非交易日期禁止交易orgid = ''5407'' bankcode = ''2028'' moneytype = ''0'' sysdate = ******** phydate = ********', null, null, '抱歉，当前时间暂不支持银证转账。', '1');
INSERT INTO LCIMS.ERROR_MESSAGE_CONVERT (EID, EITIME, EUTIME, ERR_ACTION, ERR_CODE, ORI_MSG, MATCH_RULES, EXTRACT_RULES, DES_MSG, STATUS) VALUES (214000021063503549, TIMESTAMP '2019-01-16 18:37:21', TIMESTAMP '2021-06-15 15:09:05', '20000', '********', 'CODE: ********, LEVEL: 1, MSG: 客户[************]不允许购买产品[S20403]', null, null, '暂不支持购买该产品', '1');
INSERT INTO LCIMS.ERROR_MESSAGE_CONVERT (EID, EITIME, EUTIME, ERR_ACTION, ERR_CODE, ORI_MSG, MATCH_RULES, EXTRACT_RULES, DES_MSG, STATUS) VALUES (214000029389583300, TIMESTAMP '2019-05-23 18:44:46', TIMESTAMP '2019-05-23 18:44:46', '40006', '-700110', 'CODE: -700110, LEVEL: 0, MSG: \\\"【适当性外围接口】客户问卷作答录入调用失败!错误代码:-700110,错误信息：您今日测评次数已超过调查表【1】单日测评次数限制,不能再测评', null, null, '尊敬的用户，风险测评次数每日限制2次，您已达到上限，请于明天再试。详询客服热线95357。', '1');
INSERT INTO LCIMS.ERROR_MESSAGE_CONVERT (EID, EITIME, EUTIME, ERR_ACTION, ERR_CODE, ORI_MSG, MATCH_RULES, EXTRACT_RULES, DES_MSG, STATUS) VALUES (214000032426219058, TIMESTAMP '2019-06-27 09:48:54', TIMESTAMP '2019-06-27 09:48:54', null, '-150905386', 'CODE: -150905386, LEVEL: 1, MSG: -150905386该证券账户[A761168795]无此权限[科创板交易权限]', null, null, '您的科创板权限尚未开通或激活，请前往开通或激活权限。', '1');
INSERT INTO LCIMS.ERROR_MESSAGE_CONVERT (EID, EITIME, EUTIME, ERR_ACTION, ERR_CODE, ORI_MSG, MATCH_RULES, EXTRACT_RULES, DES_MSG, STATUS) VALUES (214000032426332556, TIMESTAMP '2019-06-27 09:50:40', TIMESTAMP '2019-06-27 09:50:40', null, '-150905039', 'CODE: -150905039, LEVEL: 1, MSG: -150905039该证券账户[E051443455]无此权限[2C 科创板交易权限]', null, null, '您的信用账户科创板权限尚未开通或激活，请前往开通或激活权限。', '1');
INSERT INTO LCIMS.ERROR_MESSAGE_CONVERT (EID, EITIME, EUTIME, ERR_ACTION, ERR_CODE, ORI_MSG, MATCH_RULES, EXTRACT_RULES, DES_MSG, STATUS) VALUES (214000035651966624, TIMESTAMP '2019-08-02 17:10:12', TIMESTAMP '2019-08-02 17:15:08', null, '-150905335', 'CODE: -150905335, LEVEL: 1, MSG: -150905335该股东该股份不是特定股份，大宗减持类型不能选择特定股份减持\', null, null, '您持有的股份为非受限股，不支持受限股减持', '1');
INSERT INTO LCIMS.ERROR_MESSAGE_CONVERT (EID, EITIME, EUTIME, ERR_ACTION, ERR_CODE, ORI_MSG, MATCH_RULES, EXTRACT_RULES, DES_MSG, STATUS) VALUES (214000036440832723, TIMESTAMP '2019-09-06 15:28:05', TIMESTAMP '2019-11-12 15:48:14', null, '*********', 'CODE: -*********, LEVEL: 1, MSG: -*********[*********]该银行非交易时间orgid = ''1102'' bankcode = ''2030'' moneytype = ''0'' begintime = 90000 endtime = 160000', null, null, '抱歉，该银行非交易时间，暂不支持银证转账。', '1');
INSERT INTO LCIMS.ERROR_MESSAGE_CONVERT (EID, EITIME, EUTIME, ERR_ACTION, ERR_CODE, ORI_MSG, MATCH_RULES, EXTRACT_RULES, DES_MSG, STATUS) VALUES (214000036440838459, TIMESTAMP '2019-09-06 15:28:33', TIMESTAMP '2019-11-12 15:48:23', null, '*********', 'CODE: -*********, LEVEL: 1, MSG: -*********[*********]非交易日期禁止交易orgid = ''6101'' bankcode = ''2020'' moneytype =''0'' sysdate = ******** phydate = ********', null, null, '抱歉，当前时间暂不支持银证转账。', '1');
INSERT INTO LCIMS.ERROR_MESSAGE_CONVERT (EID, EITIME, EUTIME, ERR_ACTION, ERR_CODE, ORI_MSG, MATCH_RULES, EXTRACT_RULES, DES_MSG, STATUS) VALUES (214000036450230822, TIMESTAMP '2019-09-06 21:00:26', TIMESTAMP '2022-08-02 16:43:11', null, '-*********', 'CODE: -*********, LEVEL: 1, MSG: -*********[-*********]功能在当前时间禁止使用', null, null, '当前时间无法做该项业务', '1');
INSERT INTO LCIMS.ERROR_MESSAGE_CONVERT (EID, EITIME, EUTIME, ERR_ACTION, ERR_CODE, ORI_MSG, MATCH_RULES, EXTRACT_RULES, DES_MSG, STATUS) VALUES (214000036450236501, TIMESTAMP '2019-09-06 21:00:51', TIMESTAMP '2022-08-02 16:43:32', null, '-*********', 'CODE: -*********, LEVEL: 1, MSG: -*********[-*********]当前交易时间禁止委托该买卖类别', null, null, '当前时间不支持该项业务', '1');
INSERT INTO LCIMS.ERROR_MESSAGE_CONVERT (EID, EITIME, EUTIME, ERR_ACTION, ERR_CODE, ORI_MSG, MATCH_RULES, EXTRACT_RULES, DES_MSG, STATUS) VALUES (214000090169631693, TIMESTAMP '2023-06-16 15:15:07', TIMESTAMP '2023-06-16 15:15:07', '212', '-160002006', 'CODE:？-160002006,？LEVEL:？1,？MSG:？-160002006处理投票方案[1.01]失败:[先行赔付]业务，当前时间不允许委托!', '[{"keyword":"失败:[先行赔付]业务，当前时间不允许委托!"}]', null, '当前非申报时间，无法提交委托', '1');
INSERT INTO LCIMS.ERROR_MESSAGE_CONVERT (EID, EITIME, EUTIME, ERR_ACTION, ERR_CODE, ORI_MSG, MATCH_RULES, EXTRACT_RULES, DES_MSG, STATUS) VALUES (214000090169661212, TIMESTAMP '2023-06-16 15:15:44', TIMESTAMP '2023-06-16 15:15:44', '212', '-150905010', 'CODE:？-150905010,？LEVEL:？1,？MSG:？-150905010处理投票方案[1.01]失败:[-150905010]股东帐号状态不正常\n？secuid？=？A110961675？status？=？1', '[{"keyword":"失败:[-150905010]股东帐号状态不正常"}]', null, '股东账号状态异常，无法提交委托，如需帮助请联系客服95357', '1');
INSERT INTO LCIMS.ERROR_MESSAGE_CONVERT (EID, EITIME, EUTIME, ERR_ACTION, ERR_CODE, ORI_MSG, MATCH_RULES, EXTRACT_RULES, DES_MSG, STATUS) VALUES (214000090169674430, TIMESTAMP '2023-06-16 15:16:22', TIMESTAMP '2023-06-16 15:16:56', '212', '-150908020', 'CODE:？-150908020,？LEVEL:？1,？MSG:？-150908020处理投票方案[1.01]失败:[-150908020]资金帐号状态不正常\n？fundid？=？110200000233？status？=？1', '[{"keyword":"失败:[-150908020]资金帐号状态不正常"}]', null, '资金账号状态异常，无法提交委托，如需帮助请联系客服95357', '1');
INSERT INTO LCIMS.ERROR_MESSAGE_CONVERT (EID, EITIME, EUTIME, ERR_ACTION, ERR_CODE, ORI_MSG, MATCH_RULES, EXTRACT_RULES, DES_MSG, STATUS) VALUES (214000090687103157, TIMESTAMP '2023-06-30 15:15:44', TIMESTAMP '2023-06-30 15:15:44', '212', '-160002003', 'CODE: -160002003, LEVEL: 1, MSG: -160002003处理投票方案[1.01]失败:[-160002003]系统委托日期有误sysdate= 20230614,orderdate=20230615', '[{"keyword":"失败:[-160002003]系统委托日期有误"}]', null, '当前非交易时间，无法提交委托', '1');
INSERT INTO LCIMS.ERROR_MESSAGE_CONVERT (EID, EITIME, EUTIME, ERR_ACTION, ERR_CODE, ORI_MSG, MATCH_RULES, EXTRACT_RULES, DES_MSG, STATUS) VALUES (214000043038739579, TIMESTAMP '2020-07-01 11:20:20', TIMESTAMP '2024-05-20 15:42:42', '232', '-990249010', 'CODE:-990203010, LEVEL: 1, MSG:-990203010[-990249010] 市场[B]的买卖类别[0D]没有定义', '[{"keyword":"市场[B]的买卖类别[0D]没有定义"}]', null, '该证券不支持在当前页面交易，请至北交所新股申购功能中操作', '1');
INSERT INTO LCIMS.ERROR_MESSAGE_CONVERT (EID, EITIME, EUTIME, ERR_ACTION, ERR_CODE, ORI_MSG, MATCH_RULES, EXTRACT_RULES, DES_MSG, STATUS) VALUES (214000043133451231, TIMESTAMP '2020-07-03 16:37:43', TIMESTAMP '2022-08-02 16:53:57', '202', '-990268090', '[-990268090]该证券【market:%c,stkcode:%s】的交易属性控制不允许撤单！', '[{"keyword":"交易属性控制不允许撤单"}]', null, '该笔委托无法撤单', '1');
INSERT INTO LCIMS.ERROR_MESSAGE_CONVERT (EID, EITIME, EUTIME, ERR_ACTION, ERR_CODE, ORI_MSG, MATCH_RULES, EXTRACT_RULES, DES_MSG, STATUS) VALUES (214000043133686187, TIMESTAMP '2020-07-03 16:38:50', TIMESTAMP '2022-08-02 16:54:15', '202', '-990268091', '[-990268091]该买卖类别【market:%c,bsflag:%s】的属性控制不允许撤单！', '[{"keyword":"属性控制不允许撤单"}]', null, '该笔委托无法撤单', '1');
INSERT INTO LCIMS.ERROR_MESSAGE_CONVERT (EID, EITIME, EUTIME, ERR_ACTION, ERR_CODE, ORI_MSG, MATCH_RULES, EXTRACT_RULES, DES_MSG, STATUS) VALUES (214000055424731906, TIMESTAMP '2020-10-23 15:20:48', TIMESTAMP '2020-10-23 15:20:48', '1201', '-150905041', '-150622020[-150905041]该股东帐号没有可转债适当性权限或权限已过期，不允许进行可转债买入与认申购', '[{"keyword":"可转债适当性权限"}]', null, '您暂未开通该市场可转债交易权限，无法交易该证券，请前往业务办理功能开通权限。', '1');
INSERT INTO LCIMS.ERROR_MESSAGE_CONVERT (EID, EITIME, EUTIME, ERR_ACTION, ERR_CODE, ORI_MSG, MATCH_RULES, EXTRACT_RULES, DES_MSG, STATUS) VALUES (214000056014740432, TIMESTAMP '2020-11-13 19:10:00', TIMESTAMP '2022-08-02 16:55:24', '201', '-999003088', '[-999003088]系统正在交收,不允许夜市委托,系统状态status = ''4''', '[{"keyword":"系统正在交收"}]', null, '当前时间无法做该项业务，预计5分钟以后可以进行沪深A股委托，请稍后再试。', '1');
INSERT INTO LCIMS.ERROR_MESSAGE_CONVERT (EID, EITIME, EUTIME, ERR_ACTION, ERR_CODE, ORI_MSG, MATCH_RULES, EXTRACT_RULES, DES_MSG, STATUS) VALUES (214000056014814439, TIMESTAMP '2020-11-13 19:24:20', TIMESTAMP '2020-11-18 17:40:43', '202', '-990268010', '[-990268010]该笔委托不存在ordersno = %d, orderdate = %d', '[{"keyword":"该笔委托不存在"}]', null, '该笔委托无需撤单，若有股份或资金冻结，将于清算完成后可用。', '1');
INSERT INTO LCIMS.ERROR_MESSAGE_CONVERT (EID, EITIME, EUTIME, ERR_ACTION, ERR_CODE, ORI_MSG, MATCH_RULES, EXTRACT_RULES, DES_MSG, STATUS) VALUES (214000056014818140, TIMESTAMP '2020-11-13 19:25:11', TIMESTAMP '2022-08-02 17:01:07', '274', '-150906065', '[-150906065]跨境ETF申赎业务在夜市中不允许', '[{"keyword":"在夜市中不允许"}]', null, '该项业务无法进行夜市委托', '1');
INSERT INTO LCIMS.ERROR_MESSAGE_CONVERT (EID, EITIME, EUTIME, ERR_ACTION, ERR_CODE, ORI_MSG, MATCH_RULES, EXTRACT_RULES, DES_MSG, STATUS) VALUES (214000056753647250, TIMESTAMP '2020-12-10 16:54:49', TIMESTAMP '2020-12-10 16:54:49', '424', '-1003140900', '-1003140900非生产环境或者系统状态非正常0或者非清算后初始化前状态9,不允许进行报价回购业务类设置,以免影响深圳报价回购批量下单', '[{"keyword":"不允许进行报价回购业务类设置"}]', null, '当前时间暂不支持操作，请稍后重试', '1');
INSERT INTO LCIMS.ERROR_MESSAGE_CONVERT (EID, EITIME, EUTIME, ERR_ACTION, ERR_CODE, ORI_MSG, MATCH_RULES, EXTRACT_RULES, DES_MSG, STATUS) VALUES (214000056753652882, TIMESTAMP '2020-12-10 16:55:38', TIMESTAMP '2020-12-10 16:55:38', '424', '-1003140905', '-1003140905此表本无此记录，默认配置异常''b''记录，由人工判断酌情修改配置', '[{"keyword":"此表本无此记录"}]', null, '当前时间暂不支持操作，请稍后重试', '1');
INSERT INTO LCIMS.ERROR_MESSAGE_CONVERT (EID, EITIME, EUTIME, ERR_ACTION, ERR_CODE, ORI_MSG, MATCH_RULES, EXTRACT_RULES, DES_MSG, STATUS) VALUES (214000062632371833, TIMESTAMP '2021-06-18 16:50:07', TIMESTAMP '2021-06-18 16:50:07', '274', '-150904051', 'CODE: -150904051, LEVEL: 1, MSG: -150904051沪Ａ市场[1]的LOF申购业务[2f]不允许做基础设施基金证券类别[u]的业务!', '[{"keyword":"LOF申购业务[2f]不允许做基础设施基金证券类别[u]的业务"}]', null, '该基金不支持申购', '1');
INSERT INTO LCIMS.ERROR_MESSAGE_CONVERT (EID, EITIME, EUTIME, ERR_ACTION, ERR_CODE, ORI_MSG, MATCH_RULES, EXTRACT_RULES, DES_MSG, STATUS) VALUES (214000062632376202, TIMESTAMP '2021-06-18 16:50:48', TIMESTAMP '2021-06-18 16:50:48', '274', '-150904051', 'CODE: -150904051, LEVEL: 1, MSG: -150904051沪Ａ市场[1]的LOF赎回业务[2g]不允许做基础设施基金证券类别[u]的业务!" ', '[{"keyword":"LOF赎回业务[2g]不允许做基础设施基金证券类别[u]的业务"}]', null, '该基金不支持赎回', '1');
INSERT INTO LCIMS.ERROR_MESSAGE_CONVERT (EID, EITIME, EUTIME, ERR_ACTION, ERR_CODE, ORI_MSG, MATCH_RULES, EXTRACT_RULES, DES_MSG, STATUS) VALUES (214000064728652247, TIMESTAMP '2021-08-13 16:24:58', TIMESTAMP '2021-08-13 16:30:09', '201', '-150908020', 'CODE: -150908020, LEVEL: 1, MSG: -150908020[-150908020]资金帐号状态不正常440200000177', '[{"keyword":"资金帐号状态不正常"}]', null, '您的资金账号状态不正常，详询客服热线95357。', '1');
INSERT INTO LCIMS.ERROR_MESSAGE_CONVERT (EID, EITIME, EUTIME, ERR_ACTION, ERR_CODE, ORI_MSG, MATCH_RULES, EXTRACT_RULES, DES_MSG, STATUS) VALUES (214000066159113990, TIMESTAMP '2021-09-22 09:16:03', TIMESTAMP '2021-09-22 09:16:03', '231', '-150905336', 'CODE: -150905360, LEVEL: 1, MSG: -150905360[-150905336]该证券账户[0020919435]无此权限[股转一类合格投资者]!', '[{"keyword":"无此权限[股转一类合格投资者]"}]', null, '该账户未开通股转一类合格投资者权限，暂无法交易该证券。', '1');
INSERT INTO LCIMS.ERROR_MESSAGE_CONVERT (EID, EITIME, EUTIME, ERR_ACTION, ERR_CODE, ORI_MSG, MATCH_RULES, EXTRACT_RULES, DES_MSG, STATUS) VALUES (214000067739508660, TIMESTAMP '2021-11-12 18:24:35', TIMESTAMP '2021-11-12 18:24:35', '231', '-150905347', 'CODE: -150905360, LEVEL: 1, MSG: -150905360[-150905347]客户无法交易该层级[北交所]证券:该证券账户[0020461133]无此权限[北交所合格投资者]', '[{"keyword":"无此权限[北交所合格投资者]"}]', null, '交易北交所股票需要开通北交所合格投资者权限并签署交易须知，请前往权限开通功能办理。', '1');
INSERT INTO LCIMS.ERROR_MESSAGE_CONVERT (EID, EITIME, EUTIME, ERR_ACTION, ERR_CODE, ORI_MSG, MATCH_RULES, EXTRACT_RULES, DES_MSG, STATUS) VALUES (214000067739514836, TIMESTAMP '2021-11-12 18:25:12', TIMESTAMP '2021-11-12 18:25:12', '231', '-150905334', 'CODE: -150905360, LEVEL: 1, MSG: -150905360[-150905334]客户无法交易该层级[基础层]证券:该证券账户[0027494837]无此权限[股转一类合格投资者]', '[{"keyword":"无此权限[股转一类合格投资者]"}]', null, '您尚未开通新三板一类合格投资者权限，暂无法交易该证券。', '1');
INSERT INTO LCIMS.ERROR_MESSAGE_CONVERT (EID, EITIME, EUTIME, ERR_ACTION, ERR_CODE, ORI_MSG, MATCH_RULES, EXTRACT_RULES, DES_MSG, STATUS) VALUES (214000067739517864, TIMESTAMP '2021-11-12 18:25:46', TIMESTAMP '2021-11-12 18:25:46', '231', '-150905334', 'CODE: -150905360, LEVEL: 1, MSG: -150905360[-150905334]客户无法交易该层级[创新层]证券:该证券账户[0027494837]无此权限[股转二类合格投资者]', '[{"keyword":"无此权限[股转二类合格投资者]"}]', null, '您尚未开通新三板二类合格投资者权限，暂无法交易该证券。', '1');
INSERT INTO LCIMS.ERROR_MESSAGE_CONVERT (EID, EITIME, EUTIME, ERR_ACTION, ERR_CODE, ORI_MSG, MATCH_RULES, EXTRACT_RULES, DES_MSG, STATUS) VALUES (214000067845295684, TIMESTAMP '2021-11-16 16:27:40', TIMESTAMP '2022-08-02 17:04:14', '231', '-150905351', 'CODE:-150905351,LEVEL:1,MSG: -150905351证券分层信息还没刷新不允许做交易', '[{"keyword":"证券分层信息还没刷新不允许做交易"}]', null, '当前时间无法做该项业务', '1');
INSERT INTO LCIMS.ERROR_MESSAGE_CONVERT (EID, EITIME, EUTIME, ERR_ACTION, ERR_CODE, ORI_MSG, MATCH_RULES, EXTRACT_RULES, DES_MSG, STATUS) VALUES (214000074256352173, TIMESTAMP '2022-05-13 17:20:18', TIMESTAMP '2022-08-02 17:06:54', '274', '-150906956', 'CODE: -150906956, LEVEL: 1, MSG: -150906956深圳债券交易已迁移到固收平台，请使用固收平台专用菜单或外围操作', '[{"keyword":"深圳债券交易已迁移到固收平台"}]', null, '债券无法以该种方式进行委托，如有任何疑问，请致电客服95357', '1');
INSERT INTO LCIMS.ERROR_MESSAGE_CONVERT (EID, EITIME, EUTIME, ERR_ACTION, ERR_CODE, ORI_MSG, MATCH_RULES, EXTRACT_RULES, DES_MSG, STATUS) VALUES (214000074256357401, TIMESTAMP '2022-05-13 17:21:29', TIMESTAMP '2022-05-13 17:21:29', '201', '-150906425', 'CODE: -150906400, LEVEL: 1, MSG: -150906400[-150906425]私募债券没有授权或授权到期，禁止对该证券类别委托，错误信息[ 校验的OEM代码【私募债券】不存在!]', '[{"keyword":"私募债券没有授权或授权到期"}]', null, '当前功能暂不支持委托该证券', '1');
INSERT INTO LCIMS.ERROR_MESSAGE_CONVERT (EID, EITIME, EUTIME, ERR_ACTION, ERR_CODE, ORI_MSG, MATCH_RULES, EXTRACT_RULES, DES_MSG, STATUS) VALUES (214000074519297733, TIMESTAMP '2022-05-20 16:27:40', TIMESTAMP '2022-05-20 16:27:40', '201', '-150905420', 'CODE: -150905360, LEVEL: 1, MSG: -150905360[-150905420]该证券账户[0001396278]无此权限[债券普通投资者权限]', '[{"keyword":"无此权限[债券普通投资者权限]"}]', null, '您暂未开通该市场债券普通投资者权限，无法交易该债券，请至权限开通功能开通权限。', '1');
INSERT INTO LCIMS.ERROR_MESSAGE_CONVERT (EID, EITIME, EUTIME, ERR_ACTION, ERR_CODE, ORI_MSG, MATCH_RULES, EXTRACT_RULES, DES_MSG, STATUS) VALUES (214000074519332628, TIMESTAMP '2022-05-20 16:31:18', TIMESTAMP '2022-05-20 16:31:18', '308', '-430013050', 'CODE:-430013050,LEVEL:1,MSG:-430013050终止日期不能小于起始日期', '[{"keyword":"终止日期不能小于起始日期"}]', null, '起止日期输入有误，请检查后重新输入。', '1');
INSERT INTO LCIMS.ERROR_MESSAGE_CONVERT (EID, EITIME, EUTIME, ERR_ACTION, ERR_CODE, ORI_MSG, MATCH_RULES, EXTRACT_RULES, DES_MSG, STATUS) VALUES (214000076898854871, TIMESTAMP '2022-07-21 16:11:51', TIMESTAMP '2022-07-21 16:11:51', '204', '-150906089', 'CODE: -150906089, LEVEL: 1, MSG: -150906089[-150906089]ETF基本信息无参数记录!', '[{"keyword":"ETF基本信息无参数记录"}]', null, '暂不支持该基金的申赎委托', '1');
INSERT INTO LCIMS.ERROR_MESSAGE_CONVERT (EID, EITIME, EUTIME, ERR_ACTION, ERR_CODE, ORI_MSG, MATCH_RULES, EXTRACT_RULES, DES_MSG, STATUS) VALUES (214000078296302214, TIMESTAMP '2022-08-25 17:37:41', TIMESTAMP '2022-08-25 17:49:14', '260', '-150906040', 'CODE: -150906040, LEVEL: 1, MSG: -150906040[-150906040]本交易所今日闭市', '[{"keyword":"本交易所今日闭市"}]', null, '当前时间暂不支持港股通交易', '1');
INSERT INTO LCIMS.ERROR_MESSAGE_CONVERT (EID, EITIME, EUTIME, ERR_ACTION, ERR_CODE, ORI_MSG, MATCH_RULES, EXTRACT_RULES, DES_MSG, STATUS) VALUES (214000082991194517, TIMESTAMP '2022-12-23 15:03:33', TIMESTAMP '2022-12-23 15:03:33', '402', '-*********', 'CODE: -*********, LEVEL: 1, MSG: -*********执行当前操作后，货币资产账户[orgid:5407,fundid:540700188009,moneytype:0] 可用资金[0.01]小于有委托保留最低金额[0.10]', '[{"keyword":"委托保留最低金额"}]', null, '可用资金须不低于委托保留最低金额！', '1');
INSERT INTO LCIMS.ERROR_MESSAGE_CONVERT (EID, EITIME, EUTIME, ERR_ACTION, ERR_CODE, ORI_MSG, MATCH_RULES, EXTRACT_RULES, DES_MSG, STATUS) VALUES (214000082991205077, TIMESTAMP '2022-12-23 15:04:56', TIMESTAMP '2022-12-23 15:04:56', '1350', '-150905389', 'CODE: -150905389, LEVEL: 1, MSG: -150905389[-150905389]证券账号[0601924081]对证券[839106]无权限[2j 北交所合格投资者]！', '[{"keyword":"无权限[2j 北交所合格投资者]"}]', null, '您暂未开通北交所信用交易权限，请前往东方财富App开通，详询客服热线95357。', '1');
INSERT INTO LCIMS.ERROR_MESSAGE_CONVERT (EID, EITIME, EUTIME, ERR_ACTION, ERR_CODE, ORI_MSG, MATCH_RULES, EXTRACT_RULES, DES_MSG, STATUS) VALUES (214000083504125009, TIMESTAMP '2023-01-06 15:09:58', TIMESTAMP '2023-01-06 15:09:58', '1350', '-150906300', 'CODE: -150906300, LEVEL: 1, MSG: -150906300[-150906300]买券还券委托数量不得大于融券负债数量 stkdebtsqty = 0', '[{"keyword":"买券还券委托数量"}]', '[{"start":"stkdebtsqty ="}]', '还券数量超过最多可还数量：{0}', '1');
INSERT INTO LCIMS.ERROR_MESSAGE_CONVERT (EID, EITIME, EUTIME, ERR_ACTION, ERR_CODE, ORI_MSG, MATCH_RULES, EXTRACT_RULES, DES_MSG, STATUS) VALUES (214000083504151800, TIMESTAMP '2023-01-06 15:10:55', TIMESTAMP '2023-01-06 15:10:55', '1350', '-410611021', 'CODE: -410611021, LEVEL: 1, MSG: -410611021信用交易类型为[A]时，买卖类别必须为[0S]', '[{"keyword":"信用交易类型为[A]时，买卖类别必须为[0S]"}]', null, '融券卖出时不能使用市价委托，请使用限价委托方式', '1');
INSERT INTO LCIMS.ERROR_MESSAGE_CONVERT (EID, EITIME, EUTIME, ERR_ACTION, ERR_CODE, ORI_MSG, MATCH_RULES, EXTRACT_RULES, DES_MSG, STATUS) VALUES (214000055424747856, TIMESTAMP '2020-10-23 15:22:56', TIMESTAMP '2020-10-23 15:22:56', '201', '-150905041', '-150908041[-150905041]该股东帐号没有可转债适当性权限或权限不在有效期内，不允许进行可转债买入与认申购\nsecuid = A341032492 orgid = 3503 market = 1 date = 20201022', '[{"keyword":"可转债适当性权限"}]', null, '您暂未开通该市场可转债交易权限，无法交易该证券，请前往业务办理功能开通权限。', '1');
INSERT INTO LCIMS.ERROR_MESSAGE_CONVERT (EID, EITIME, EUTIME, ERR_ACTION, ERR_CODE, ORI_MSG, MATCH_RULES, EXTRACT_RULES, DES_MSG, STATUS) VALUES (214000056014749629, TIMESTAMP '2020-11-13 19:11:54', TIMESTAMP '2022-08-02 16:55:58', '201', '-999003088', '[-999003088]系统正在初始化,不允许夜市委托,系统状态status = ''1''', '[{"keyword":"系统正在初始化"}]', null, '当前时间无法做该项业务，预计10分钟以后可以进行沪深A股委托，请稍后再试。', '1');
INSERT INTO LCIMS.ERROR_MESSAGE_CONVERT (EID, EITIME, EUTIME, ERR_ACTION, ERR_CODE, ORI_MSG, MATCH_RULES, EXTRACT_RULES, DES_MSG, STATUS) VALUES (214000056014805997, TIMESTAMP '2020-11-13 19:22:48', TIMESTAMP '2022-08-02 16:59:50', '202', '-999003088', '[-999003088]系统正在交收,不允许夜市委托,系统状态status = ''4''', '[{"keyword":"系统正在交收"}]', null, '当前时间无法做该项业务，预计5分钟以后可以进行沪深A股委托，请稍后再试。', '1');
INSERT INTO LCIMS.ERROR_MESSAGE_CONVERT (EID, EITIME, EUTIME, ERR_ACTION, ERR_CODE, ORI_MSG, MATCH_RULES, EXTRACT_RULES, DES_MSG, STATUS) VALUES (12, TIMESTAMP '2024-11-25 15:39:10', TIMESTAMP '2024-11-25 15:37:50', '283', '-901020106', 'CODE: -410986060, LEVEL: 1, MSG: -410986060[-901020106]没有对应的股权激励人员信息!', '[{"keyword":"没有对应的股权激励人员信息"}]', null, '当前暂无股权激励人员信息', '1');
INSERT INTO LCIMS.ERROR_MESSAGE_CONVERT (EID, EITIME, EUTIME, ERR_ACTION, ERR_CODE, ORI_MSG, MATCH_RULES, EXTRACT_RULES, DES_MSG, STATUS) VALUES (13, TIMESTAMP '2024-11-25 15:39:10', TIMESTAMP '2024-11-25 15:37:50', '282', '-990270083', 'CODE: -150527021, LEVEL: 1, MSG: -150527021[-990270083]委托后无法满足履约担保比例控制要求', '[{"keyword":"委托后无法满足履约担保比例控制要求"}]', null, '当前账户委托后无法满足履约担保比例控制要求', '1');
INSERT INTO LCIMS.ERROR_MESSAGE_CONVERT (EID, EITIME, EUTIME, ERR_ACTION, ERR_CODE, ORI_MSG, MATCH_RULES, EXTRACT_RULES, DES_MSG, STATUS) VALUES (15, TIMESTAMP '2024-11-25 15:39:10', TIMESTAMP '2024-11-25 15:37:50', '282', '-150527021', 'CODE: -150527021, LEVEL: 1, MSG: -150527021系统已交收尚未初始化,不允许委托申报,状态status = ''9''', '[{"keyword":"系统已交收尚未初始化,不允许委托申报"}]', null, '当前时间无法提交委托', '1');
INSERT INTO LCIMS.ERROR_MESSAGE_CONVERT (EID, EITIME, EUTIME, ERR_ACTION, ERR_CODE, ORI_MSG, MATCH_RULES, EXTRACT_RULES, DES_MSG, STATUS) VALUES (16, TIMESTAMP '2024-11-25 15:39:10', TIMESTAMP '2024-11-25 15:37:50', '282', '-150527021', 'CODE: -150527021, LEVEL: 1, MSG: -150527021该账户[0199900000]无[融资行权业务]权限，或权限生效日期己过期!', '[{"keyword":"无[融资行权业务]权限，或权限生效日期己过期"}]', null, '当前账户无融资行权业务权限', '1');
INSERT INTO LCIMS.ERROR_MESSAGE_CONVERT (EID, EITIME, EUTIME, ERR_ACTION, ERR_CODE, ORI_MSG, MATCH_RULES, EXTRACT_RULES, DES_MSG, STATUS) VALUES (22, TIMESTAMP '2016-03-15 10:57:29', TIMESTAMP '2016-04-07 17:42:43', '521', '-150906135', 'CODE: -150906135, LEVEL: 1, MSG: -150906135[-150906135]股份可用数不足,尚需100', '[{"keyword":"CODE: -150906135, LEVEL: 1, MSG: -150906135[-150906135]股份可用数不足,尚需"}]', null, '可用股份数不足！', '1');
INSERT INTO LCIMS.ERROR_MESSAGE_CONVERT (EID, EITIME, EUTIME, ERR_ACTION, ERR_CODE, ORI_MSG, MATCH_RULES, EXTRACT_RULES, DES_MSG, STATUS) VALUES (25, TIMESTAMP '2024-11-25 15:39:10', TIMESTAMP '2024-11-25 15:37:50', '283', '-901020104', 'CODE: -410986060, LEVEL: 1, MSG: -410986060[-901020104]没有对应的融资行权参数管理信息!', '[{"keyword":"没有对应的融资行权参数管理信息"}]', null, '当前人员信息异常', '1');
INSERT INTO LCIMS.ERROR_MESSAGE_CONVERT (EID, EITIME, EUTIME, ERR_ACTION, ERR_CODE, ORI_MSG, MATCH_RULES, EXTRACT_RULES, DES_MSG, STATUS) VALUES (1, TIMESTAMP '2024-11-25 15:39:10', TIMESTAMP '2024-11-25 15:37:50', '282', '-150904070', 'CODE: -150527021, LEVEL: 1, MSG: -150527021[-150904070] 委托数量必须是每手[100]股的倍数', '[{"keyword":"委托数量必须是每手"}]', null, 'COMMON', '1');
INSERT INTO LCIMS.ERROR_MESSAGE_CONVERT (EID, EITIME, EUTIME, ERR_ACTION, ERR_CODE, ORI_MSG, MATCH_RULES, EXTRACT_RULES, DES_MSG, STATUS) VALUES (2, TIMESTAMP '2024-11-25 15:39:10', TIMESTAMP '2024-11-25 15:37:50', '282', '-990221020', 'CODE: -150527021, LEVEL: 1, MSG: -150527021[-990221020]无此证券代码!\nstkcode = 03803 market = 0 ', '[{"keyword":"-150527021[-990221020]无此证券代码"}]', null, '证券代码填写错误', '1');
INSERT INTO LCIMS.ERROR_MESSAGE_CONVERT (EID, EITIME, EUTIME, ERR_ACTION, ERR_CODE, ORI_MSG, MATCH_RULES, EXTRACT_RULES, DES_MSG, STATUS) VALUES (3, TIMESTAMP '2024-11-25 15:39:10', TIMESTAMP '2024-11-25 15:37:50', '283', '-990221020', 'CODE: -410986060, LEVEL: 1, MSG: -410986060[-990221020]无此证券代码!stkcode = 0380391 market = 0 ', '[{"keyword":"无此证券代码"}]', null, '行权代码填写错误', '1');
INSERT INTO LCIMS.ERROR_MESSAGE_CONVERT (EID, EITIME, EUTIME, ERR_ACTION, ERR_CODE, ORI_MSG, MATCH_RULES, EXTRACT_RULES, DES_MSG, STATUS) VALUES (4, TIMESTAMP '2024-11-25 15:39:10', TIMESTAMP '2024-11-25 15:37:50', '282', '-310300040', 'CODE: -150527021, LEVEL: 1, MSG: -150527021[-310300040]输入委托数量有误', '[{"keyword":"输入委托数量有误"}]', null, '输入委托数量有误', '1');
INSERT INTO LCIMS.ERROR_MESSAGE_CONVERT (EID, EITIME, EUTIME, ERR_ACTION, ERR_CODE, ORI_MSG, MATCH_RULES, EXTRACT_RULES, DES_MSG, STATUS) VALUES (5, TIMESTAMP '2024-11-25 15:39:10', TIMESTAMP '2024-11-25 15:37:50', '282', '90001', 'CODE: 90001, LEVEL: 0, MSG: 90001没有orderqty项的数据', '[{"keyword":"没有orderqty项的数据"}]', null, '输入委托数量有误', '1');
INSERT INTO LCIMS.ERROR_MESSAGE_CONVERT (EID, EITIME, EUTIME, ERR_ACTION, ERR_CODE, ORI_MSG, MATCH_RULES, EXTRACT_RULES, DES_MSG, STATUS) VALUES (6, TIMESTAMP '2021-08-11 19:48:38', TIMESTAMP '2021-08-12 15:52:08', '521', '-150906053', 'CODE: -150906053, LEVEL: 1, MSG: -150906053[-150906053]当前时间不允许做该项业务', '[{"keyword":"当前时间不允许做该项业务"}]', null, '当前时间不允许进行该项业务', '1');
INSERT INTO LCIMS.ERROR_MESSAGE_CONVERT (EID, EITIME, EUTIME, ERR_ACTION, ERR_CODE, ORI_MSG, MATCH_RULES, EXTRACT_RULES, DES_MSG, STATUS) VALUES (7, TIMESTAMP '2024-11-26 19:10:13', TIMESTAMP '2024-11-26 19:10:13', '520', '-410984020', 'CODE: -410984020, LEVEL: 2, MSG: -410984020系统状态非正常交易,不允许直接还款,状态status = ''4''', '[{"keyword":"系统状态非正常交易,不允许直接还款"}]', null, '当前时间不允许直接还款', '1');
INSERT INTO LCIMS.ERROR_MESSAGE_CONVERT (EID, EITIME, EUTIME, ERR_ACTION, ERR_CODE, ORI_MSG, MATCH_RULES, EXTRACT_RULES, DES_MSG, STATUS) VALUES (8, TIMESTAMP '2024-11-26 19:10:13', TIMESTAMP '2024-11-26 19:10:13', '520', '-901020111', 'CODE: -410984020, LEVEL: 2, MSG: -410984020[-901020111]还款金额大于资金可用', '[{"keyword":"还款金额大于资金可用"}]', null, '还款金额不能大于可还负债', '1');
INSERT INTO LCIMS.ERROR_MESSAGE_CONVERT (EID, EITIME, EUTIME, ERR_ACTION, ERR_CODE, ORI_MSG, MATCH_RULES, EXTRACT_RULES, DES_MSG, STATUS) VALUES (9, TIMESTAMP '2024-11-27 09:11:15', TIMESTAMP '2024-11-27 09:11:21', '521', '-990265060', 'CODE: -990265060, LEVEL: 1, MSG: -990265060[-990265060]委托价格超过跌停价格', '[{"keyword":"CODE: -990265060, LEVEL: 1, MSG: -990265060[-990265060]委托价格超过跌停价格"}]', null, '委托价格不允许低于跌停价格！', '1');
INSERT INTO LCIMS.ERROR_MESSAGE_CONVERT (EID, EITIME, EUTIME, ERR_ACTION, ERR_CODE, ORI_MSG, MATCH_RULES, EXTRACT_RULES, DES_MSG, STATUS) VALUES (10, TIMESTAMP '2024-11-27 09:12:34', TIMESTAMP '2024-11-27 09:12:40', '521', '-990265050', 'CODE: -990265050, LEVEL: 1, MSG: -990265050[-990265050]委托价格超过涨停价格', '[{"keyword":"CODE: -990265050, LEVEL: 1, MSG: -990265050[-990265050]委托价格超过涨停价格"}]', null, '委托价格不允许超过涨停价格！', '1');
INSERT INTO LCIMS.ERROR_MESSAGE_CONVERT (EID, EITIME, EUTIME, ERR_ACTION, ERR_CODE, ORI_MSG, MATCH_RULES, EXTRACT_RULES, DES_MSG, STATUS) VALUES (11, TIMESTAMP '2024-11-27 09:14:47', TIMESTAMP '2024-11-27 09:14:52', '521', '-150906021', 'CODE: -150906021, LEVEL: 1, MSG: -150906021[-150906021]不允许将整股拆成零股来卖', '[{"keyword":"CODE: -150906021, LEVEL: 1, MSG: -150906021[-150906021]不允许将整股拆成零股来卖"}]', null, '不允许将整股拆成零股委托！', '1');
INSERT INTO LCIMS.ERROR_MESSAGE_CONVERT (EID, EITIME, EUTIME, ERR_ACTION, ERR_CODE, ORI_MSG, MATCH_RULES, EXTRACT_RULES, DES_MSG, STATUS) VALUES (14, TIMESTAMP '2022-11-30 14:31:28', TIMESTAMP '2022-11-30 14:45:16', '521', '-151000069', 'CODE: -151000069, LEVEL: 1, MSG: -151000069[-151000069]客户没有可偿还负债', '[{"keyword":"客户没有可偿还负债"}]', null, '当前没有可还负债', '1');
INSERT INTO LCIMS.ERROR_MESSAGE_CONVERT (EID, EITIME, EUTIME, ERR_ACTION, ERR_CODE, ORI_MSG, MATCH_RULES, EXTRACT_RULES, DES_MSG, STATUS) VALUES (20, TIMESTAMP '2017-02-20 15:45:33', TIMESTAMP '2017-02-20 15:45:33', '521', '-150906210', 'CODE: -150906210, LEVEL: 1, MSG: -150906210[-150906210]该委托的价格[6.00001232]有误!', '[{"keyword":"CODE: -150906210, LEVEL: 1, MSG: -150906210[-150906210]该委托的价格"}, {"keyword":"有误"}]', '[{"start":"该委托的价格[", "end":"]有误!"}]', '委托价格有误！', '1');
INSERT INTO LCIMS.ERROR_MESSAGE_CONVERT (EID, EITIME, EUTIME, ERR_ACTION, ERR_CODE, ORI_MSG, MATCH_RULES, EXTRACT_RULES, DES_MSG, STATUS) VALUES (21, TIMESTAMP '2016-03-15 10:57:29', TIMESTAMP '2016-04-07 17:39:53', '521', '-150904001', 'CODE: -150904001, LEVEL: 1, MSG: -150904001[-150904001]委托价格[1.126]不是该证券代码[600478]最小价位[10]厘的整数倍', '[{"keyword":"CODE: -150904001, LEVEL: 1, MSG: -150904001[-150904001]委托价格["},{"keyword":"]厘的整数倍"}]', null, '委托价格无效！', '1');
