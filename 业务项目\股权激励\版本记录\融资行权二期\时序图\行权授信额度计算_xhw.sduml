title 最高可申请额度查询


participant APP
participant 证券交易网关
participant 股权激励中台服务
participant 交易前置机
participant 柜台
database 交易业务库

APP->证券交易网关: 最高可申请额度查询

证券交易网关->股权激励中台服务:最高可申请额度查询

股权激励中台服务->交易前置机:查询281[410990]-股权激励人员信息
股权激励中台服务->交易前置机:查询持仓明细
交易前置机->柜台:查询303[410503]-持股明细
交易前置机<-柜台:return
股权激励中台服务<-交易前置机:return
股权激励中台服务->股权激励中台服务:筛选证券类型为Q权证的持仓
loop 持仓明细信息
股权激励中台服务->交易前置机:查询权证信息
交易前置机->柜台:查询275[410231]-取权证信息
交易前置机<-柜台:return
股权激励中台服务<-交易前置机:return
end
股权激励中台服务->股权激励中台服务:过滤行权结束日早于当前日期的权证
alt 存在权证行权日期包含当前日期
    股权激励中台服务->股权激励中台服务:计算所有符合条件权证的(行权数量*行权价)\n选择最大值对应的权证
else 不存在权证行权日期包含当前日期
    股权激励中台服务->股权激励中台服务:筛选行权开始日在当前日期之后的权证\n选择距离当前日期最近的权证\n若有多个则取(行权数量*行权价)最大值的权证
end
股权激励中台服务->交易业务库:查询信用等级系数配置
股权激励中台服务<-交易业务库:return
股权激励中台服务->股权激励中台服务:获取资质得分
股权激励中台服务->股权激励中台服务: 计算行权授信额度和对应信用等级额度上限的最小值
证券交易网关<-股权激励中台服务:返回授信额度
APP<-证券交易网关:返回授信额度
