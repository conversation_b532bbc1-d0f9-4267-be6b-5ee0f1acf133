# 个券可投额度查询技术方案

## 1. 流程图

```plantuml
title 个券可投额度查询接口

participant 前端
participant em_ptms_ficc
participant MySQL
participant Choice接口


前端->em_ptms_ficc:POST /api/creditQuotaManage/queryInvestableQuota\n(bondCode, dayNum)


em_ptms_ficc->Choice接口:查询债券基本信息\n(债券简称、债券类型、剩余期限、发行人、担保人、\n所属主体、主体内评、是否永续、是否次级、\n主体类型、所属区域)

em_ptms_ficc<--Choice接口:返回债券基本信息

em_ptms_ficc->MySQL:查询手动入库债券

em_ptms_ficc<--MySQL:返回手动入库债券信息

alt 非手动入库债券
alt 债券为利率债/可转债
em_ptms_ficc->前端:返回"不适用"
else 债券为信用债

alt 发行人内评为1-5或7
alt 是永续债或次级债
em_ptms_ficc->前端:返回"不可投"
else 非永续、次级
note over em_ptms_ficc:继续判断
end
else 发行人内评6

alt 非永续、次级，债券由城投主体发行、行权剩余期限在3年以内、含利率跳升机制保护或含回售条款
note over em_ptms_ficc:继续判断

else
em_ptms_ficc->前端:返回"不可投"

end
else 发行人内评>7
em_ptms_ficc->前端:返回"不可投"
end
end
end
note over em_ptms_ficc:授信主体确认(发行人内评>=担保人内评？发行人:担保人)
em_ptms_ficc->MySQL:查询主体自营限额、主体做市限额
em_ptms_ficc<-MySQL:主体自营限额、主体做市限额信息
note over em_ptms_ficc:授信主体分别判断是否有主体自营限额、主体做市限额
alt 无主体限额
em_ptms_ficc->前端:返回"不可投(无主体限额)"
end

note over em_ptms_ficc:计算可投额度

em_ptms_ficc->MySQL:查询内部证券账户权限
em_ptms_ficc<-MySQL:内部证券账户权限
group 计算主体限额



alt 无对应组别权限
em_ptms_ficc->em_ptms_ficc:主体限额为无权限
else 有对应权限
em_ptms_ficc->MySQL:查询主体限额配置
em_ptms_ficc<--MySQL:返回主体限额
end
end
group 计算区域限额
alt 主体类型为城投债或主体类型为空
alt 个券属于成都市-区县级或重庆市-区县级
em_ptms_ficc->em_ptms_ficc:区域限额=Min(市区，区县)
else 
em_ptms_ficc->em_ptms_ficc:区域限额=所属区域的限额
end
else 
em_ptms_ficc->em_ptms_ficc:区域限额=不限
end
em_ptms_ficc->MySQL:查询区域限额配置

em_ptms_ficc<--MySQL:返回区域限额


end
group 计算集中度限额
alt 同业存单或境外债
em_ptms_ficc->em_ptms_ficc:集中度限额=不限
else 
em_ptms_ficc->MySQL:查询集中度限额配置

em_ptms_ficc<--MySQL:返回集中度限额


end
end
group 计算内评限额
alt 主体的内评<=6
em_ptms_ficc->MySQL:查询主体内评为6及以下的总规模限额（万元）
em_ptms_ficc<-MySQL:return
else 主体的内评<=7
em_ptms_ficc->MySQL:查询主体内评为7及以下的总规模限额（万元）
em_ptms_ficc<-MySQL:return
end


end
em_ptms_ficc->MySQL:查询持仓
em_ptms_ficc<-MySQL:持仓
note over em_ptms_ficc:计算四种限额的T+0和T+N剩余可买面额，\n取最小值作为可投额度
em_ptms_ficc->前端:返回债券基本信息、可投额度及各限额明细




```

## 2. 涉及的数据库、Redis、Kafka 等中间件的修改/新增图或者说明

不涉及
## 3. 前后端交互接口信息

### 3.1 个券可投额度查询接口

#### 3.1.1 请求路径
POST /api/creditQuotaManage/queryInvestableQuota

#### 3.1.2 入参
| 字段名   | 字段类型 | 描述              | 是否必传 |
| -------- | -------- | ----------------- | -------- |
| bondCode | String   | 债券代码          | 是       |
| dayNum   | Integer  | 查询天数范围(1-7) | 是       |

#### 3.1.3 出参
| 字段名                     | 字段类型       | 描述              | 是否不为空 |
| ----------------------- | ---------- | --------------- | ----- |
| bondCode                | String     | 债券代码            | 是     |
| bondName                | String     | 债券简称            | 是     |
| bondType                | String     | 债券类别            | 是     |
| canEntry                | Boolean    | 是否可入库           | 是     |
| remainingTerm           | BigDecimal | 剩余期限(行权,年)      | 是     |
| issuer                  | String     | 发行人             | 是     |
| guarantor               | String     | 担保人             | 否     |
| belongSubject           | String     | 所属主体            | 是     |
| subjectInternalRating   | Integer    | 主体内评            | 是     |
| subjectType             | String     | 主体类型(YY)        | 是     |
| perpetualBond           | String     | 是否永续            | 是     |
| isSubordinated          | String     | 是否次级            | 是     |
| belongRegion            | String     | 所属区域            | 否     |
| groupType               | String     | 组别              | 是     |
| investableQuota         | BigDecimal | 可投额度(万元)        | 否     |
| t0SubjectRemainingQuota | BigDecimal | T+0所属主体剩余额度(万元) | 否     |
| tnSubjectRemainingQuota | BigDecimal | T+N所属主体剩余额度(万元) | 否     |
| t0RegionRemainingQuota  | BigDecimal | T+0所属区域剩余额度(万元) | 否     |
| tnRegionRemainingQuota  | BigDecimal | T+N所属区域剩余额度(万元) | 否     |
| t0ConcentrationQuota    | BigDecimal | T+0集中度限额(万元)    | 否     |
| tnConcentrationQuota    | BigDecimal | T+N集中度限额(万元)    | 否     |
| t0RatingQuota           | BigDecimal | T+0内评限额(万元)     | 否     |
| tnRatingQuota           | BigDecimal | T+N内评限额(万元)     | 否     |

## 4. 配置&部署修改信息
不涉及

## 5. 新增技术组件说明
不涉及

## 6. 影响范围
- 新增个券可投额度查询功能
- 影响菜单：【综合管理】-【信用债管理】-【个券可投额度查询】
## 7. 外部依赖项
- Choice接口：获取债券基本信息
