/*
THIS IS A GENERATED/BUNDLED FILE BY ESBUILD
if you want to view the source, please visit the github repository of this plugin
*/

function __extractDefault(module2) {
    return module2 && module2.__esModule && module2.default ? module2.default : module2;
  }

(function patchRequireEsmDefault() {
    const __require = require;
    require = Object.assign((id) => {
      const module2 = __require(id) ?? {};
      return __extractDefault(module2);
    }, __require);
  })()

"use strict";var N_=Object.create;var ei=Object.defineProperty;var R_=Object.getOwnPropertyDescriptor;var q_=Object.getOwnPropertyNames;var j_=Object.getPrototypeOf,B_=Object.prototype.hasOwnProperty;var S=(e,t)=>()=>(e&&(t=e(e=0)),t);var $=(e,t)=>()=>(t||e((t={exports:{}}).exports,t),t.exports),ti=(e,t)=>{for(var r in t)ei(e,r,{get:t[r],enumerable:!0})},Ru=(e,t,r,n)=>{if(t&&typeof t=="object"||typeof t=="function")for(let a of q_(t))!B_.call(e,a)&&a!==r&&ei(e,a,{get:()=>t[a],enumerable:!(n=R_(t,a))||n.enumerable});return e};var ee=(e,t,r)=>(r=e!=null?N_(j_(e)):{},Ru(t||!e||!e.__esModule?ei(r,"default",{value:e,enumerable:!0}):r,e)),Ua=e=>Ru(ei({},"__esModule",{value:!0}),e);var er=$((WE,Yu)=>{"use strict";var Ha=Object.defineProperty,V_=Object.getOwnPropertyDescriptor,z_=Object.getOwnPropertyNames,U_=Object.prototype.hasOwnProperty,W_=(e,t)=>{for(var r in t)Ha(e,r,{get:t[r],enumerable:!0})},H_=(e,t,r,n)=>{if(t&&typeof t=="object"||typeof t=="function")for(let a of z_(t))!U_.call(e,a)&&a!==r&&Ha(e,a,{get:()=>t[a],enumerable:!(n=V_(t,a))||n.enumerable});return e},G_=e=>H_(Ha({},"__esModule",{value:!0}),e),qu={};W_(qu,{CustomArrayDictImpl:()=>$_,FileExtension:()=>J_,GraphColorGroup:()=>Fk,InternalPluginName:()=>qt,ViewType:()=>pe,createTFileInstance:()=>Sk,createTFolderInstance:()=>$a,getAllPropertiesViewConstructor:()=>rk,getAppConstructor:()=>Z_,getAudioViewConstructor:()=>nk,getBacklinkViewConstructor:()=>ik,getBookmarksViewConstructor:()=>ak,getBrowserHistoryViewConstructor:()=>ok,getBrowserViewConstructor:()=>sk,getCanvasViewConstructor:()=>lk,getEmptyViewConstructor:()=>uk,getFileExplorerViewConstructor:()=>ck,getFilePropertiesViewConstructor:()=>fk,getGraphViewConstructor:()=>dk,getImageViewConstructor:()=>hk,getInternalPluginConstructor:()=>K_,getInternalPluginsConstructor:()=>X_,getLocalGraphViewConstructor:()=>pk,getMarkdownViewConstructor:()=>mk,getOutgoingLinkViewConstructor:()=>gk,getOutlineViewConstructor:()=>yk,getPdfViewConstructor:()=>wk,getReleaseNotesViewConstructor:()=>bk,getSearchViewConstructor:()=>_k,getSyncViewConstructor:()=>kk,getTFileConstructor:()=>ju,getTFolderConstructor:()=>Bu,getTagViewConstructor:()=>vk,getVideoViewConstructor:()=>xk,getViewConstructorByViewType:()=>me,isEmbedCache:()=>Ck,isFrontmatterLinkCache:()=>Pk,isLinkCache:()=>Ok,isReferenceCache:()=>Ja,parentFolderPath:()=>Ga});Yu.exports=G_(qu);var $_=class{data=new Map;add(e,t){let r=this.get(e);r||(r=[],this.data.set(e,r)),r.includes(t)||r.push(t)}remove(e,t){let r=this.get(e);r&&(r.remove(t),r.length===0&&this.clear(e))}get(e){return this.data.get(e)||null}keys(){return Array.from(this.data.keys())}clear(e){this.data.delete(e)}clearAll(){this.data.clear()}contains(e,t){return!!this.get(e)?.contains(t)}count(){let e=0;for(let t in this.keys())e+=this.get(t)?.length??0;return e}},J_={_3gp:"3gp",avif:"avif",bmp:"bmp",canvas:"canvas",flac:"flac",gif:"gif",jpeg:"jpeg",jpg:"jpg",m4a:"m4a",md:"md",mkv:"mkv",mov:"mov",mp3:"mp3",mp4:"mp4",oga:"oga",ogg:"ogg",ogv:"ogv",opus:"opus",pdf:"pdf",png:"png",svg:"svg",wav:"wav",webm:"webm",webp:"webp"},qt={AudioRecorder:"audio-recorder",Backlink:"backlink",Bookmarks:"bookmarks",Browser:"browser",Canvas:"canvas",CommandPalette:"command-palette",DailyNotes:"daily-notes",EditorStatus:"editor-status",FileExplorer:"file-explorer",FileRecovery:"file-recovery",GlobalSearch:"global-search",Graph:"graph",MarkdownImporter:"markdown-importer",NoteComposer:"note-composer",OutgoingLink:"outgoing-link",Outline:"outline",PagePreview:"page-preview",Properties:"properties",Publish:"publish",RandomNote:"random-note",SlashCommand:"slash-command",Slides:"slides",Switcher:"switcher",Sync:"sync",TagPane:"tag-pane",Templates:"templates",WordCount:"word-count",Workspaces:"workspaces",ZkPrefixer:"zk-prefixer"},pe={AllProperties:"all-properties",Audio:"audio",Backlink:qt.Backlink,Bookmarks:qt.Bookmarks,Browser:"browser",BrowserHistory:"browser-history",Canvas:qt.Canvas,Empty:"empty",FileExplorer:qt.FileExplorer,FileProperties:"file-properties",Graph:qt.Graph,Image:"image",LocalGraph:"localgraph",Markdown:"markdown",OutgoingLink:qt.OutgoingLink,Outline:qt.Outline,Pdf:"pdf",ReleaseNotes:"release-notes",Search:"search",Sync:"sync",Tag:"tag",Video:"video"},Q_=require("obsidian");function Z_(){return Q_.App}function K_(e){let t=Object.values(e.internalPlugins.plugins)[0];if(!t)throw new Error("No internal plugin found");return t.constructor}function X_(e){return e.internalPlugins.constructor}var ek=require("obsidian");function ju(){return ek.TFile}var tk=require("obsidian");function Bu(){return tk.TFolder}function me(e,t){let r=e.workspace.createLeafInTabGroup();try{let n=e.viewRegistry.getViewCreatorByType(t);if(!n)throw new Error("View creator not found");return n(r).constructor}finally{r.detach()}}function rk(e){return me(e,pe.AllProperties)}function nk(e){return me(e,pe.Audio)}function ik(e){return me(e,pe.Backlink)}function ak(e){return me(e,pe.Bookmarks)}function ok(e){return me(e,pe.BrowserHistory)}function sk(e){return me(e,pe.Browser)}function lk(e){return me(e,pe.Canvas)}function uk(e){return me(e,pe.Empty)}function ck(e){return me(e,pe.FileExplorer)}function fk(e){return me(e,pe.FileProperties)}function dk(e){return me(e,pe.Graph)}function hk(e){return me(e,pe.Image)}function pk(e){return me(e,pe.LocalGraph)}function mk(e){return me(e,pe.Markdown)}function gk(e){return me(e,pe.OutgoingLink)}function yk(e){return me(e,pe.Outline)}function wk(e){return me(e,pe.Pdf)}function bk(e){return me(e,pe.ReleaseNotes)}function _k(e){return me(e,pe.Search)}function kk(e){return me(e,pe.Sync)}function vk(e){return me(e,pe.Tag)}function xk(e){return me(e,pe.Video)}function Ga(e){return e.replace(/\/?[^\/]*$/,"")||"/"}function $a(e,t){let r=e.vault.getFolderByPath(t);return r||(r=new(Bu())(e.vault,t),r.parent=$a(e,Ga(t)),r.deleted=!0,r)}function Sk(e,t){let r=e.vault.getFileByPath(t);return r||(r=new(ju())(e.vault,t),r.parent=$a(e,Ga(t)),r.deleted=!0,r)}function Ja(e){return!!e.position}function Ck(e){return Ja(e)&&e.original[0]==="!"}function Pk(e){return!!e.key}function Ok(e){return Ja(e)&&e.original[0]!=="!"}var Fk=class{color;query}});var Uu=$(($E,zu)=>{var Ek=globalThis.process??{browser:!0,cwd:()=>"/",env:{},platform:"android"};function pt(e){if(typeof e!="string")throw new TypeError("Path must be a string. Received "+JSON.stringify(e))}function Vu(e,t){for(var r="",n=0,a=-1,s=0,l,c=0;c<=e.length;++c){if(c<e.length)l=e.charCodeAt(c);else{if(l===47)break;l=47}if(l===47){if(!(a===c-1||s===1))if(a!==c-1&&s===2){if(r.length<2||n!==2||r.charCodeAt(r.length-1)!==46||r.charCodeAt(r.length-2)!==46){if(r.length>2){var d=r.lastIndexOf("/");if(d!==r.length-1){d===-1?(r="",n=0):(r=r.slice(0,d),n=r.length-1-r.lastIndexOf("/")),a=c,s=0;continue}}else if(r.length===2||r.length===1){r="",n=0,a=c,s=0;continue}}t&&(r.length>0?r+="/..":r="..",n=2)}else r.length>0?r+="/"+e.slice(a+1,c):r=e.slice(a+1,c),n=c-a-1;a=c,s=0}else l===46&&s!==-1?++s:s=-1}return r}function Ak(e,t){var r=t.dir||t.root,n=t.base||(t.name||"")+(t.ext||"");return r?r===t.root?r+n:r+e+n:n}var Sr={resolve:function(){for(var t="",r=!1,n,a=arguments.length-1;a>=-1&&!r;a--){var s;a>=0?s=arguments[a]:(n===void 0&&(n=Ek.cwd()),s=n),pt(s),s.length!==0&&(t=s+"/"+t,r=s.charCodeAt(0)===47)}return t=Vu(t,!r),r?t.length>0?"/"+t:"/":t.length>0?t:"."},normalize:function(t){if(pt(t),t.length===0)return".";var r=t.charCodeAt(0)===47,n=t.charCodeAt(t.length-1)===47;return t=Vu(t,!r),t.length===0&&!r&&(t="."),t.length>0&&n&&(t+="/"),r?"/"+t:t},isAbsolute:function(t){return pt(t),t.length>0&&t.charCodeAt(0)===47},join:function(){if(arguments.length===0)return".";for(var t,r=0;r<arguments.length;++r){var n=arguments[r];pt(n),n.length>0&&(t===void 0?t=n:t+="/"+n)}return t===void 0?".":Sr.normalize(t)},relative:function(t,r){if(pt(t),pt(r),t===r||(t=Sr.resolve(t),r=Sr.resolve(r),t===r))return"";for(var n=1;n<t.length&&t.charCodeAt(n)===47;++n);for(var a=t.length,s=a-n,l=1;l<r.length&&r.charCodeAt(l)===47;++l);for(var c=r.length,d=c-l,p=s<d?s:d,h=-1,m=0;m<=p;++m){if(m===p){if(d>p){if(r.charCodeAt(l+m)===47)return r.slice(l+m+1);if(m===0)return r.slice(l+m)}else s>p&&(t.charCodeAt(n+m)===47?h=m:m===0&&(h=0));break}var y=t.charCodeAt(n+m),w=r.charCodeAt(l+m);if(y!==w)break;y===47&&(h=m)}var v="";for(m=n+h+1;m<=a;++m)(m===a||t.charCodeAt(m)===47)&&(v.length===0?v+="..":v+="/..");return v.length>0?v+r.slice(l+h):(l+=h,r.charCodeAt(l)===47&&++l,r.slice(l))},_makeLong:function(t){return t},dirname:function(t){if(pt(t),t.length===0)return".";for(var r=t.charCodeAt(0),n=r===47,a=-1,s=!0,l=t.length-1;l>=1;--l)if(r=t.charCodeAt(l),r===47){if(!s){a=l;break}}else s=!1;return a===-1?n?"/":".":n&&a===1?"//":t.slice(0,a)},basename:function(t,r){if(r!==void 0&&typeof r!="string")throw new TypeError('"ext" argument must be a string');pt(t);var n=0,a=-1,s=!0,l;if(r!==void 0&&r.length>0&&r.length<=t.length){if(r.length===t.length&&r===t)return"";var c=r.length-1,d=-1;for(l=t.length-1;l>=0;--l){var p=t.charCodeAt(l);if(p===47){if(!s){n=l+1;break}}else d===-1&&(s=!1,d=l+1),c>=0&&(p===r.charCodeAt(c)?--c===-1&&(a=l):(c=-1,a=d))}return n===a?a=d:a===-1&&(a=t.length),t.slice(n,a)}else{for(l=t.length-1;l>=0;--l)if(t.charCodeAt(l)===47){if(!s){n=l+1;break}}else a===-1&&(s=!1,a=l+1);return a===-1?"":t.slice(n,a)}},extname:function(t){pt(t);for(var r=-1,n=0,a=-1,s=!0,l=0,c=t.length-1;c>=0;--c){var d=t.charCodeAt(c);if(d===47){if(!s){n=c+1;break}continue}a===-1&&(s=!1,a=c+1),d===46?r===-1?r=c:l!==1&&(l=1):r!==-1&&(l=-1)}return r===-1||a===-1||l===0||l===1&&r===a-1&&r===n+1?"":t.slice(r,a)},format:function(t){if(t===null||typeof t!="object")throw new TypeError('The "pathObject" argument must be of type Object. Received type '+typeof t);return Ak("/",t)},parse:function(t){pt(t);var r={root:"",dir:"",base:"",ext:"",name:""};if(t.length===0)return r;var n=t.charCodeAt(0),a=n===47,s;a?(r.root="/",s=1):s=0;for(var l=-1,c=0,d=-1,p=!0,h=t.length-1,m=0;h>=s;--h){if(n=t.charCodeAt(h),n===47){if(!p){c=h+1;break}continue}d===-1&&(p=!1,d=h+1),n===46?l===-1?l=h:m!==1&&(m=1):l!==-1&&(m=-1)}return l===-1||d===-1||m===0||m===1&&l===d-1&&l===c+1?d!==-1&&(c===0&&a?r.base=r.name=t.slice(1,d):r.base=r.name=t.slice(c,d)):(c===0&&a?(r.name=t.slice(1,l),r.base=t.slice(1,d)):(r.name=t.slice(c,l),r.base=t.slice(c,d)),r.ext=t.slice(l,d)),c>0?r.dir=t.slice(0,c-1):a&&(r.dir="/"),r},sep:"/",delimiter:":",win32:null,posix:null};Sr.posix=Sr;zu.exports=Sr});var Hu=$((JE,Qa)=>{"use strict";var Tk=Object.prototype.hasOwnProperty,je="~";function tn(){}Object.create&&(tn.prototype=Object.create(null),new tn().__proto__||(je=!1));function Dk(e,t,r){this.fn=e,this.context=t,this.once=r||!1}function Wu(e,t,r,n,a){if(typeof r!="function")throw new TypeError("The listener must be a function");var s=new Dk(r,n||e,a),l=je?je+t:t;return e._events[l]?e._events[l].fn?e._events[l]=[e._events[l],s]:e._events[l].push(s):(e._events[l]=s,e._eventsCount++),e}function ri(e,t){--e._eventsCount===0?e._events=new tn:delete e._events[t]}function Te(){this._events=new tn,this._eventsCount=0}Te.prototype.eventNames=function(){var t=[],r,n;if(this._eventsCount===0)return t;for(n in r=this._events)Tk.call(r,n)&&t.push(je?n.slice(1):n);return Object.getOwnPropertySymbols?t.concat(Object.getOwnPropertySymbols(r)):t};Te.prototype.listeners=function(t){var r=je?je+t:t,n=this._events[r];if(!n)return[];if(n.fn)return[n.fn];for(var a=0,s=n.length,l=new Array(s);a<s;a++)l[a]=n[a].fn;return l};Te.prototype.listenerCount=function(t){var r=je?je+t:t,n=this._events[r];return n?n.fn?1:n.length:0};Te.prototype.emit=function(t,r,n,a,s,l){var c=je?je+t:t;if(!this._events[c])return!1;var d=this._events[c],p=arguments.length,h,m;if(d.fn){switch(d.once&&this.removeListener(t,d.fn,void 0,!0),p){case 1:return d.fn.call(d.context),!0;case 2:return d.fn.call(d.context,r),!0;case 3:return d.fn.call(d.context,r,n),!0;case 4:return d.fn.call(d.context,r,n,a),!0;case 5:return d.fn.call(d.context,r,n,a,s),!0;case 6:return d.fn.call(d.context,r,n,a,s,l),!0}for(m=1,h=new Array(p-1);m<p;m++)h[m-1]=arguments[m];d.fn.apply(d.context,h)}else{var y=d.length,w;for(m=0;m<y;m++)switch(d[m].once&&this.removeListener(t,d[m].fn,void 0,!0),p){case 1:d[m].fn.call(d[m].context);break;case 2:d[m].fn.call(d[m].context,r);break;case 3:d[m].fn.call(d[m].context,r,n);break;case 4:d[m].fn.call(d[m].context,r,n,a);break;default:if(!h)for(w=1,h=new Array(p-1);w<p;w++)h[w-1]=arguments[w];d[m].fn.apply(d[m].context,h)}}return!0};Te.prototype.on=function(t,r,n){return Wu(this,t,r,n,!1)};Te.prototype.once=function(t,r,n){return Wu(this,t,r,n,!0)};Te.prototype.removeListener=function(t,r,n,a){var s=je?je+t:t;if(!this._events[s])return this;if(!r)return ri(this,s),this;var l=this._events[s];if(l.fn)l.fn===r&&(!a||l.once)&&(!n||l.context===n)&&ri(this,s);else{for(var c=0,d=[],p=l.length;c<p;c++)(l[c].fn!==r||a&&!l[c].once||n&&l[c].context!==n)&&d.push(l[c]);d.length?this._events[s]=d.length===1?d[0]:d:ri(this,s)}return this};Te.prototype.removeAllListeners=function(t){var r;return t?(r=je?je+t:t,this._events[r]&&ri(this,r)):(this._events=new tn,this._eventsCount=0),this};Te.prototype.off=Te.prototype.removeListener;Te.prototype.addListener=Te.prototype.on;Te.prefixed=je;Te.EventEmitter=Te;typeof Qa<"u"&&(Qa.exports=Te)});var mt=$((QE,Ju)=>{function Lk(e){return e&&e.__esModule&&e.default?e.default:e}(function(){let t=require;require=Object.assign(r=>{let n=t(r)??{};return Lk(n)},t)})();var Za=Object.defineProperty,Mk=Object.getOwnPropertyDescriptor,Ik=Object.getOwnPropertyNames,Nk=Object.prototype.hasOwnProperty,Rk=(e,t)=>{for(var r in t)Za(e,r,{get:t[r],enumerable:!0})},qk=(e,t,r,n)=>{if(t&&typeof t=="object"||typeof t=="function")for(let a of Ik(t))!Nk.call(e,a)&&a!==r&&Za(e,a,{get:()=>t[a],enumerable:!(n=Mk(t,a))||n.enumerable});return e},jk=e=>qk(Za({},"__esModule",{value:!0}),e),Gu={};Rk(Gu,{emitAsyncErrorEvent:()=>Yk,errorToString:()=>Vk,getStackTrace:()=>zk,printError:()=>$u,registerAsyncErrorEventHandler:()=>Uk,throwExpression:()=>Wk});Ju.exports=jk(Gu);var Bk=Hu(),ni="asyncError",ii=new Bk.EventEmitter;ii.on(ni,Hk);function Yk(e){ii.emit(ni,e)}function Vk(e){return Ka(e).map(t=>"  ".repeat(t.level)+t.message).join(`
`)}function zk(e=0){return(new Error().stack??"").split(`
`).slice(e+2).join(`
`)}function $u(e,t){t??=globalThis.console;let r=Ka(e);for(let n of r)n.shouldClearAnsiSequence?t.error(`\x1B[0m${n.message}\x1B[0m`):t.error(n.message)}function Uk(e){return ii.on(ni,e),()=>ii.off(ni,e)}function Wk(e){throw e}function Hk(e){$u(new Error("An unhandled error occurred executing async operation",{cause:e}))}function Ka(e,t=0,r=[]){if(e===void 0)return r;if(!(e instanceof Error)){let a="";return e===null?a="(null)":typeof e=="string"?a=e:a=JSON.stringify(e)??"undefined",r.push({level:t,message:a}),r}let n=`${e.name}: ${e.message}`;if(r.push({level:t,message:n,shouldClearAnsiSequence:!0}),e.stack){let a=e.stack.startsWith(n)?e.stack.slice(n.length+1):e.stack;r.push({level:t,message:`Error stack:
${a}`})}return e.cause!==void 0&&(r.push({level:t,message:"Caused by:"}),Ka(e.cause,t+1,r)),r}});var eo=$((ZE,Zu)=>{function Gk(e){return e&&e.__esModule&&e.default?e.default:e}(function(){let t=require;require=Object.assign(r=>{let n=t(r)??{};return Gk(n)},t)})();var Xa=Object.defineProperty,$k=Object.getOwnPropertyDescriptor,Jk=Object.getOwnPropertyNames,Qk=Object.prototype.hasOwnProperty,Zk=(e,t)=>{for(var r in t)Xa(e,r,{get:t[r],enumerable:!0})},Kk=(e,t,r,n)=>{if(t&&typeof t=="object"||typeof t=="function")for(let a of Jk(t))!Qk.call(e,a)&&a!==r&&Xa(e,a,{get:()=>t[a],enumerable:!(n=$k(t,a))||n.enumerable});return e},Xk=e=>Kk(Xa({},"__esModule",{value:!0}),e),Qu={};Zk(Qu,{escapeRegExp:()=>ev,isValidRegExp:()=>tv});Zu.exports=Xk(Qu);function ev(e){return e.replaceAll(/[.*+?^${}()|[\]\\]/g,"\\$&")}function tv(e){try{return new RegExp(e),!0}catch{return!1}}});var ai=$((KE,Xu)=>{function rv(e){return e&&e.__esModule&&e.default?e.default:e}(function(){let t=require;require=Object.assign(r=>{let n=t(r)??{};return rv(n)},t)})();var to=Object.defineProperty,nv=Object.getOwnPropertyDescriptor,iv=Object.getOwnPropertyNames,av=Object.prototype.hasOwnProperty,ov=(e,t)=>{for(var r in t)to(e,r,{get:t[r],enumerable:!0})},sv=(e,t,r,n)=>{if(t&&typeof t=="object"||typeof t=="function")for(let a of iv(t))!av.call(e,a)&&a!==r&&to(e,a,{get:()=>t[a],enumerable:!(n=nv(t,a))||n.enumerable});return e},lv=e=>sv(to({},"__esModule",{value:!0}),e),Ku={};ov(Ku,{resolveValue:()=>uv});Xu.exports=lv(Ku);async function uv(e,...t){return cv(e)?await e(...t):e}function cv(e){return typeof e=="function"}});var Et=$((XE,nc)=>{function fv(e){return e&&e.__esModule&&e.default?e.default:e}(function(){let t=require;require=Object.assign(r=>{let n=t(r)??{};return fv(n)},t)})();var ro=Object.defineProperty,dv=Object.getOwnPropertyDescriptor,hv=Object.getOwnPropertyNames,pv=Object.prototype.hasOwnProperty,mv=(e,t)=>{for(var r in t)ro(e,r,{get:t[r],enumerable:!0})},gv=(e,t,r,n)=>{if(t&&typeof t=="object"||typeof t=="function")for(let a of hv(t))!pv.call(e,a)&&a!==r&&ro(e,a,{get:()=>t[a],enumerable:!(n=dv(t,a))||n.enumerable});return e},yv=e=>gv(ro({},"__esModule",{value:!0}),e),ec={};mv(ec,{ensureEndsWith:()=>kv,ensureStartsWith:()=>vv,escape:()=>xv,insertAt:()=>Sv,makeValidVariableName:()=>Cv,normalize:()=>Pv,replace:()=>no,replaceAll:()=>tr,replaceAllAsync:()=>Ov,trimEnd:()=>Fv,trimStart:()=>Ev,unescape:()=>Av});nc.exports=yv(ec);var wv=mt(),bv=eo(),_v=ai(),tc={"\n":"\\n","\r":"\\r","	":"\\t","\b":"\\b","\f":"\\f","'":"\\'",'"':'\\"',"\\":"\\\\"},rc={};for(let[e,t]of Object.entries(tc))rc[t]=e;function kv(e,t){return e.endsWith(t)?e:e+t}function vv(e,t){return e.startsWith(t)?e:t+e}function xv(e){return no(e,tc)}function Sv(e,t,r,n){return n??=r,e.slice(0,r)+t+e.slice(n)}function Cv(e){return tr(e,/[^a-zA-Z0-9_]/g,"_")}function Pv(e){return tr(e,/\u00A0|\u202F/g," ").normalize("NFC")}function no(e,t){let r=new RegExp(Object.keys(t).map(n=>(0,bv.escapeRegExp)(n)).join("|"),"g");return tr(e,r,({substring:n})=>t[n]??(0,wv.throwExpression)(new Error(`Unexpected replacement source: ${n}`)))}function tr(e,t,r){return typeof r>"u"?e:(t instanceof RegExp&&!t.global&&(t=new RegExp(t.source,`${t.flags}g`)),typeof r=="string"?e.replaceAll(t,r):e.replaceAll(t,(n,...a)=>{let s=typeof a.at(-1)=="object",l=s?a.length-2:a.length-1,c={groups:s?a.at(-1):void 0,offset:a.at(l-1),source:a.at(l),substring:n},d=a.slice(0,l-1);return r(c,...d)??c.substring}))}async function Ov(e,t,r){if(typeof r=="string")return tr(e,t,r);let n=[];tr(e,t,(s,...l)=>(n.push((0,_v.resolveValue)(r,s,...l)),""));let a=await Promise.all(n);return tr(e,t,s=>a.shift()??s.substring)}function Fv(e,t,r){if(e.endsWith(t))return e.slice(0,-t.length);if(r)throw new Error(`String ${e} does not end with suffix ${t}`);return e}function Ev(e,t,r){if(e.startsWith(t))return e.slice(t.length);if(r)throw new Error(`String ${e} does not start with prefix ${t}`);return e}function Av(e){return no(e,rc)}});var ct=$((eA,hc)=>{function ic(e){return e&&e.__esModule&&e.default?e.default:e}(function(){let t=require;require=Object.assign(r=>{let n=t(r)??{};return ic(n)},t)})();var Tv=Object.create,oi=Object.defineProperty,Dv=Object.getOwnPropertyDescriptor,Lv=Object.getOwnPropertyNames,Mv=Object.getPrototypeOf,Iv=Object.prototype.hasOwnProperty,Nv=(e,t)=>{for(var r in t)oi(e,r,{get:t[r],enumerable:!0})},ac=(e,t,r,n)=>{if(t&&typeof t=="object"||typeof t=="function")for(let a of Lv(t))!Iv.call(e,a)&&a!==r&&oi(e,a,{get:()=>t[a],enumerable:!(n=Dv(t,a))||n.enumerable});return e},Rv=(e,t,r)=>(r=e!=null?Tv(Mv(e)):{},ac(t||!e||!e.__esModule?oi(r,"default",{value:e,enumerable:!0}):r,e)),qv=e=>ac(oi({},"__esModule",{value:!0}),e),oc={};Nv(oc,{basename:()=>Yv,delimiter:()=>jv,dirname:()=>cc,extname:()=>Vv,format:()=>zv,getDirname:()=>Jv,getFilename:()=>fc,isAbsolute:()=>Uv,join:()=>Wv,makeFileName:()=>Qv,normalize:()=>Hv,normalizeIfRelative:()=>Zv,parse:()=>Gv,posix:()=>Ke,relative:()=>$v,resolve:()=>dc,sep:()=>Bv,toPosixBuffer:()=>Kv,toPosixPath:()=>io});hc.exports=qv(oc);var sc=Rv(ic(Uu()),1),lc=Et(),uc=/[a-zA-Z]:\/[^:]*$/,Ke=sc.default.posix,jv=Ke.delimiter,Bv=sc.default.posix.sep,Yv=Ke.basename,cc=Ke.dirname,Vv=Ke.extname,zv=Ke.format;function Uv(e){return Ke.isAbsolute(e)||uc.exec(e)?.[0]===e}var Wv=Ke.join,Hv=Ke.normalize,Gv=Ke.parse,$v=Ke.relative;function Jv(e){return cc(fc(e))}function fc(e){return dc(new URL(e).pathname)}function Qv(e,t){return t?`${e}.${t}`:e}function Zv(e){return e.startsWith("/")||e.includes(":")?e:(0,lc.ensureStartsWith)(e,"./")}function dc(...e){let t=Ke.resolve(...e);return t=io(t),uc.exec(t)?.[0]??t}function Kv(e){return Buffer.from(io(e.toString()))}function io(e){return(0,lc.replaceAll)(e,"\\","/")}});var Xe=$((tA,xc)=>{function Xv(e){return e&&e.__esModule&&e.default?e.default:e}(function(){let t=require;require=Object.assign(r=>{let n=t(r)??{};return Xv(n)},t)})();var ao=Object.defineProperty,ex=Object.getOwnPropertyDescriptor,tx=Object.getOwnPropertyNames,rx=Object.prototype.hasOwnProperty,nx=(e,t)=>{for(var r in t)ao(e,r,{get:t[r],enumerable:!0})},ix=(e,t,r,n)=>{if(t&&typeof t=="object"||typeof t=="function")for(let a of tx(t))!rx.call(e,a)&&a!==r&&ao(e,a,{get:()=>t[a],enumerable:!(n=ex(t,a))||n.enumerable});return e},ax=e=>ix(ao({},"__esModule",{value:!0}),e),mc={};nx(mc,{CANVAS_FILE_EXTENSION:()=>yc,MARKDOWN_FILE_EXTENSION:()=>so,checkExtension:()=>lo,getAbstractFile:()=>sx,getAbstractFileOrNull:()=>an,getFile:()=>lx,getFileOrNull:()=>si,getFolder:()=>wc,getFolderOrNull:()=>uo,getMarkdownFiles:()=>ux,getOrCreateFile:()=>cx,getOrCreateFolder:()=>bc,getPath:()=>fx,isAbstractFile:()=>co,isCanvasFile:()=>_c,isFile:()=>fo,isFolder:()=>kc,isMarkdownFile:()=>rn,isNote:()=>dx,trimMarkdownExtension:()=>hx});xc.exports=ax(mc);var nn=require("obsidian"),oo=er(),gc=ct(),ox=Et(),so="md",yc="canvas";function lo(e,t,r){if(fo(t))return t.extension===r;if(typeof t=="string"){let n=si(e,t);return n?n.extension===r:(0,gc.extname)(t).slice(1)===r}return!1}function sx(e,t,r){let n=an(e,t,r);if(!n)throw new Error(`Abstract file not found: ${t}`);return n}function an(e,t,r){if(t===null)return null;if(co(t))return t;let n=pc(e,t,r);if(n)return n;let a=vc(t);return a===t?null:pc(e,a,r)}function lx(e,t,r,n){let a=si(e,t,n);if(!a)if(r)a=(0,oo.createTFileInstance)(e,t);else throw new Error(`File not found: ${t}`);return a}function si(e,t,r){let n=an(e,t,r);return fo(n)?n:null}function wc(e,t,r,n){let a=uo(e,t,n);if(!a)if(r)a=(0,oo.createTFolderInstance)(e,t);else throw new Error(`Folder not found: ${t}`);return a}function uo(e,t,r){let n=an(e,t,r);return kc(n)?n:null}function ux(e,t,r){let n=wc(e,t),a=[];return r?nn.Vault.recurseChildren(n,s=>{rn(e,s)&&a.push(s)}):a=n.children.filter(s=>rn(e,s)),a=a.sort((s,l)=>s.path.localeCompare(l.path)),a}async function cx(e,t){let r=si(e,t);if(r)return r;let n=(0,oo.parentFolderPath)(t);return await bc(e,n),await e.vault.create(t,"")}async function bc(e,t){let r=uo(e,t);return r||await e.vault.createFolder(t)}function fx(e,t){if(co(t))return t.path;let r=an(e,t);return r?r.path:vc(t)}function co(e){return e instanceof nn.TAbstractFile}function _c(e,t){return lo(e,t,yc)}function fo(e){return e instanceof nn.TFile}function kc(e){return e instanceof nn.TFolder}function rn(e,t){return lo(e,t,so)}function dx(e,t){return rn(e,t)||_c(e,t)}function hx(e,t){return rn(e,t)?(0,ox.trimEnd)(t.path,"."+so):t.path}function pc(e,t,r){return r?e.vault.getAbstractFileByPathInsensitive(t):e.vault.getAbstractFileByPath(t)}function vc(e){return(0,nn.normalizePath)((0,gc.resolve)("/",e))}});var mo=$((rA,Fc)=>{function px(e){return e&&e.__esModule&&e.default?e.default:e}(function(){let t=require;require=Object.assign(r=>{let n=t(r)??{};return px(n)},t)})();var po=Object.defineProperty,mx=Object.getOwnPropertyDescriptor,gx=Object.getOwnPropertyNames,yx=Object.prototype.hasOwnProperty,wx=(e,t)=>{for(var r in t)po(e,r,{get:t[r],enumerable:!0})},bx=(e,t,r,n)=>{if(t&&typeof t=="object"||typeof t=="function")for(let a of gx(t))!yx.call(e,a)&&a!==r&&po(e,a,{get:()=>t[a],enumerable:!(n=mx(t,a))||n.enumerable});return e},_x=e=>bx(po({},"__esModule",{value:!0}),e),Cc={};wx(Cc,{getAttachmentFilePath:()=>Pc,getAttachmentFolderPath:()=>ho,getAvailablePathForAttachments:()=>Oc,hasOwnAttachmentFolder:()=>vx});Fc.exports=_x(Cc);var kx=er(),li=ct(),on=Et(),sn=Xe();async function Pc(e,t,r){let n=(0,sn.getPath)(e,t),a=(0,sn.getPath)(e,r),s=(0,sn.getFile)(e,a,!0),l=(0,li.extname)(n),c=(0,li.basename)(n,l),d=e.vault.getAvailablePathForAttachments;return d.isExtended?d(c,l.slice(1),s,!0):await Oc(e,c,l.slice(1),s,!0)}async function ho(e,t){return(0,kx.parentFolderPath)(await Pc(e,"DUMMY_FILE.pdf",t))}async function Oc(e,t,r,n,a){let s=e.vault.getConfig("attachmentFolderPath"),l=s==="."||s==="./",c=null;s.startsWith("./")&&(c=(0,on.trimStart)(s,"./")),l?s=n?n.parent?.path??"":"":c&&(s=(n?n.parent?.getParentPrefix()??"":"")+c),s=(0,on.normalize)(Sc(s)),t=(0,on.normalize)(Sc(t));let d=(0,sn.getFolderOrNull)(e,s,!0);!d&&c&&(a?d=(0,sn.getFolder)(e,s,!0):d=await e.vault.createFolder(s));let p=d?.getParentPrefix()??"";return e.vault.getAvailablePath(p+t,r)}async function vx(e,t){let r=await ho(e,t),n=await ho(e,(0,li.join)((0,li.dirname)(t),"DUMMY_FILE.md"));return r!==n}function Sc(e){return e=(0,on.replaceAll)(e,/([\\/])+/g,"/"),e=(0,on.replaceAll)(e,/(^\/+|\/+$)/g,""),e||"/"}});var Ac=$((nA,Ec)=>{var Cr=1e3,Pr=Cr*60,Or=Pr*60,rr=Or*24,xx=rr*7,Sx=rr*365.25;Ec.exports=function(e,t){t=t||{};var r=typeof e;if(r==="string"&&e.length>0)return Cx(e);if(r==="number"&&isFinite(e))return t.long?Ox(e):Px(e);throw new Error("val is not a non-empty string or a valid number. val="+JSON.stringify(e))};function Cx(e){if(e=String(e),!(e.length>100)){var t=/^(-?(?:\d+)?\.?\d+) *(milliseconds?|msecs?|ms|seconds?|secs?|s|minutes?|mins?|m|hours?|hrs?|h|days?|d|weeks?|w|years?|yrs?|y)?$/i.exec(e);if(t){var r=parseFloat(t[1]),n=(t[2]||"ms").toLowerCase();switch(n){case"years":case"year":case"yrs":case"yr":case"y":return r*Sx;case"weeks":case"week":case"w":return r*xx;case"days":case"day":case"d":return r*rr;case"hours":case"hour":case"hrs":case"hr":case"h":return r*Or;case"minutes":case"minute":case"mins":case"min":case"m":return r*Pr;case"seconds":case"second":case"secs":case"sec":case"s":return r*Cr;case"milliseconds":case"millisecond":case"msecs":case"msec":case"ms":return r;default:return}}}}function Px(e){var t=Math.abs(e);return t>=rr?Math.round(e/rr)+"d":t>=Or?Math.round(e/Or)+"h":t>=Pr?Math.round(e/Pr)+"m":t>=Cr?Math.round(e/Cr)+"s":e+"ms"}function Ox(e){var t=Math.abs(e);return t>=rr?ui(e,t,rr,"day"):t>=Or?ui(e,t,Or,"hour"):t>=Pr?ui(e,t,Pr,"minute"):t>=Cr?ui(e,t,Cr,"second"):e+" ms"}function ui(e,t,r,n){var a=t>=r*1.5;return Math.round(e/r)+" "+n+(a?"s":"")}});var go=$((iA,Tc)=>{function Fx(e){r.debug=r,r.default=r,r.coerce=d,r.disable=l,r.enable=a,r.enabled=c,r.humanize=Ac(),r.destroy=p,Object.keys(e).forEach(h=>{r[h]=e[h]}),r.names=[],r.skips=[],r.formatters={};function t(h){let m=0;for(let y=0;y<h.length;y++)m=(m<<5)-m+h.charCodeAt(y),m|=0;return r.colors[Math.abs(m)%r.colors.length]}r.selectColor=t;function r(h){let m,y=null,w,v;function x(...E){if(!x.enabled)return;let O=x,z=Number(new Date),A=z-(m||z);O.diff=A,O.prev=m,O.curr=z,m=z,E[0]=r.coerce(E[0]),typeof E[0]!="string"&&E.unshift("%O");let Z=0;E[0]=E[0].replace(/%([a-zA-Z%])/g,(P,G)=>{if(P==="%%")return"%";Z++;let B=r.formatters[G];if(typeof B=="function"){let U=E[Z];P=B.call(O,U),E.splice(Z,1),Z--}return P}),r.formatArgs.call(O,E),(O.log||r.log).apply(O,E)}return x.namespace=h,x.useColors=r.useColors(),x.color=r.selectColor(h),x.extend=n,x.destroy=r.destroy,Object.defineProperty(x,"enabled",{enumerable:!0,configurable:!1,get:()=>y!==null?y:(w!==r.namespaces&&(w=r.namespaces,v=r.enabled(h)),v),set:E=>{y=E}}),typeof r.init=="function"&&r.init(x),x}function n(h,m){let y=r(this.namespace+(typeof m>"u"?":":m)+h);return y.log=this.log,y}function a(h){r.save(h),r.namespaces=h,r.names=[],r.skips=[];let m=(typeof h=="string"?h:"").trim().replace(" ",",").split(",").filter(Boolean);for(let y of m)y[0]==="-"?r.skips.push(y.slice(1)):r.names.push(y)}function s(h,m){let y=0,w=0,v=-1,x=0;for(;y<h.length;)if(w<m.length&&(m[w]===h[y]||m[w]==="*"))m[w]==="*"?(v=w,x=y,w++):(y++,w++);else if(v!==-1)w=v+1,x++,y=x;else return!1;for(;w<m.length&&m[w]==="*";)w++;return w===m.length}function l(){let h=[...r.names,...r.skips.map(m=>"-"+m)].join(",");return r.enable(""),h}function c(h){for(let m of r.skips)if(s(h,m))return!1;for(let m of r.names)if(s(h,m))return!0;return!1}function d(h){return h instanceof Error?h.stack||h.message:h}function p(){console.warn("Instance method `debug.destroy()` is deprecated and no longer does anything. It will be removed in the next major version of `debug`.")}return r.enable(r.load()),r}Tc.exports=Fx});var Dc=$((He,ci)=>{var yo=globalThis.process??{browser:!0,cwd:()=>"/",env:{},platform:"android"};He.formatArgs=Ax;He.save=Tx;He.load=Dx;He.useColors=Ex;He.storage=Lx();He.destroy=(()=>{let e=!1;return()=>{e||(e=!0,console.warn("Instance method `debug.destroy()` is deprecated and no longer does anything. It will be removed in the next major version of `debug`."))}})();He.colors=["#0000CC","#0000FF","#0033CC","#0033FF","#0066CC","#0066FF","#0099CC","#0099FF","#00CC00","#00CC33","#00CC66","#00CC99","#00CCCC","#00CCFF","#3300CC","#3300FF","#3333CC","#3333FF","#3366CC","#3366FF","#3399CC","#3399FF","#33CC00","#33CC33","#33CC66","#33CC99","#33CCCC","#33CCFF","#6600CC","#6600FF","#6633CC","#6633FF","#66CC00","#66CC33","#9900CC","#9900FF","#9933CC","#9933FF","#99CC00","#99CC33","#CC0000","#CC0033","#CC0066","#CC0099","#CC00CC","#CC00FF","#CC3300","#CC3333","#CC3366","#CC3399","#CC33CC","#CC33FF","#CC6600","#CC6633","#CC9900","#CC9933","#CCCC00","#CCCC33","#FF0000","#FF0033","#FF0066","#FF0099","#FF00CC","#FF00FF","#FF3300","#FF3333","#FF3366","#FF3399","#FF33CC","#FF33FF","#FF6600","#FF6633","#FF9900","#FF9933","#FFCC00","#FFCC33"];function Ex(){if(typeof window<"u"&&window.process&&(window.process.type==="renderer"||window.process.__nwjs))return!0;if(typeof navigator<"u"&&navigator.userAgent&&navigator.userAgent.toLowerCase().match(/(edge|trident)\/(\d+)/))return!1;let e;return typeof document<"u"&&document.documentElement&&document.documentElement.style&&document.documentElement.style.WebkitAppearance||typeof window<"u"&&window.console&&(window.console.firebug||window.console.exception&&window.console.table)||typeof navigator<"u"&&navigator.userAgent&&(e=navigator.userAgent.toLowerCase().match(/firefox\/(\d+)/))&&parseInt(e[1],10)>=31||typeof navigator<"u"&&navigator.userAgent&&navigator.userAgent.toLowerCase().match(/applewebkit\/(\d+)/)}function Ax(e){if(e[0]=(this.useColors?"%c":"")+this.namespace+(this.useColors?" %c":" ")+e[0]+(this.useColors?"%c ":" ")+"+"+ci.exports.humanize(this.diff),!this.useColors)return;let t="color: "+this.color;e.splice(1,0,t,"color: inherit");let r=0,n=0;e[0].replace(/%[a-zA-Z%]/g,a=>{a!=="%%"&&(r++,a==="%c"&&(n=r))}),e.splice(n,0,t)}He.log=console.debug||console.log||(()=>{});function Tx(e){try{e?He.storage.setItem("debug",e):He.storage.removeItem("debug")}catch{}}function Dx(){let e;try{e=He.storage.getItem("debug")}catch{}return!e&&typeof yo<"u"&&"env"in yo&&(e=yo.env.DEBUG),e}function Lx(){try{return localStorage}catch{}}ci.exports=go()(He);var{formatters:Mx}=ci.exports;Mx.j=function(e){try{return JSON.stringify(e)}catch(t){return"[UnexpectedJSONParseError]: "+t.message}}});var Mc=$((aA,Lc)=>{var Ix=globalThis.process??{browser:!0,cwd:()=>"/",env:{},platform:"android"};Lc.exports=(e,t=Ix.argv)=>{let r=e.startsWith("-")?"":e.length===1?"-":"--",n=t.indexOf(r+e),a=t.indexOf("--");return n!==-1&&(a===-1||n<a)}});var qc=$((oA,Rc)=>{var Nc=globalThis.process??{browser:!0,cwd:()=>"/",env:{},platform:"android"},Nx=require("os"),Ic=require("tty"),et=Mc(),{env:ke}=Nc,jt;et("no-color")||et("no-colors")||et("color=false")||et("color=never")?jt=0:(et("color")||et("colors")||et("color=true")||et("color=always"))&&(jt=1);"FORCE_COLOR"in ke&&(ke.FORCE_COLOR==="true"?jt=1:ke.FORCE_COLOR==="false"?jt=0:jt=ke.FORCE_COLOR.length===0?1:Math.min(parseInt(ke.FORCE_COLOR,10),3));function wo(e){return e===0?!1:{level:e,hasBasic:!0,has256:e>=2,has16m:e>=3}}function bo(e,t){if(jt===0)return 0;if(et("color=16m")||et("color=full")||et("color=truecolor"))return 3;if(et("color=256"))return 2;if(e&&!t&&jt===void 0)return 0;let r=jt||0;if(ke.TERM==="dumb")return r;if(Nc.platform==="win32"){let n=Nx.release().split(".");return Number(n[0])>=10&&Number(n[2])>=10586?Number(n[2])>=14931?3:2:1}if("CI"in ke)return["TRAVIS","CIRCLECI","APPVEYOR","GITLAB_CI","GITHUB_ACTIONS","BUILDKITE"].some(n=>n in ke)||ke.CI_NAME==="codeship"?1:r;if("TEAMCITY_VERSION"in ke)return/^(9\.(0*[1-9]\d*)\.|\d{2,}\.)/.test(ke.TEAMCITY_VERSION)?1:0;if(ke.COLORTERM==="truecolor")return 3;if("TERM_PROGRAM"in ke){let n=parseInt((ke.TERM_PROGRAM_VERSION||"").split(".")[0],10);switch(ke.TERM_PROGRAM){case"iTerm.app":return n>=3?3:2;case"Apple_Terminal":return 2}}return/-256(color)?$/i.test(ke.TERM)?2:/^screen|^xterm|^vt100|^vt220|^rxvt|color|ansi|cygwin|linux/i.test(ke.TERM)||"COLORTERM"in ke?1:r}function Rx(e){let t=bo(e,e&&e.isTTY);return wo(t)}Rc.exports={supportsColor:Rx,stdout:wo(bo(!0,Ic.isatty(1))),stderr:wo(bo(!0,Ic.isatty(2)))}});var Bc=$((ve,di)=>{var nr=globalThis.process??{browser:!0,cwd:()=>"/",env:{},platform:"android"},qx=require("tty"),fi=require("util");ve.init=Wx;ve.log=Vx;ve.formatArgs=Bx;ve.save=zx;ve.load=Ux;ve.useColors=jx;ve.destroy=fi.deprecate(()=>{},"Instance method `debug.destroy()` is deprecated and no longer does anything. It will be removed in the next major version of `debug`.");ve.colors=[6,2,3,4,5,1];try{let e=qc();e&&(e.stderr||e).level>=2&&(ve.colors=[20,21,26,27,32,33,38,39,40,41,42,43,44,45,56,57,62,63,68,69,74,75,76,77,78,79,80,81,92,93,98,99,112,113,128,129,134,135,148,149,160,161,162,163,164,165,166,167,168,169,170,171,172,173,178,179,184,185,196,197,198,199,200,201,202,203,204,205,206,207,208,209,214,215,220,221])}catch{}ve.inspectOpts=Object.keys(nr.env).filter(e=>/^debug_/i.test(e)).reduce((e,t)=>{let r=t.substring(6).toLowerCase().replace(/_([a-z])/g,(a,s)=>s.toUpperCase()),n=nr.env[t];return/^(yes|on|true|enabled)$/i.test(n)?n=!0:/^(no|off|false|disabled)$/i.test(n)?n=!1:n==="null"?n=null:n=Number(n),e[r]=n,e},{});function jx(){return"colors"in ve.inspectOpts?!!ve.inspectOpts.colors:qx.isatty(nr.stderr.fd)}function Bx(e){let{namespace:t,useColors:r}=this;if(r){let n=this.color,a="\x1B[3"+(n<8?n:"8;5;"+n),s=`  ${a};1m${t} \x1B[0m`;e[0]=s+e[0].split(`
`).join(`
`+s),e.push(a+"m+"+di.exports.humanize(this.diff)+"\x1B[0m")}else e[0]=Yx()+t+" "+e[0]}function Yx(){return ve.inspectOpts.hideDate?"":new Date().toISOString()+" "}function Vx(...e){return nr.stderr.write(fi.formatWithOptions(ve.inspectOpts,...e)+`
`)}function zx(e){e?nr.env.DEBUG=e:delete nr.env.DEBUG}function Ux(){return nr.env.DEBUG}function Wx(e){e.inspectOpts={};let t=Object.keys(ve.inspectOpts);for(let r=0;r<t.length;r++)e.inspectOpts[t[r]]=ve.inspectOpts[t[r]]}di.exports=go()(ve);var{formatters:jc}=di.exports;jc.o=function(e){return this.inspectOpts.colors=this.useColors,fi.inspect(e,this.inspectOpts).split(`
`).map(t=>t.trim()).join(" ")};jc.O=function(e){return this.inspectOpts.colors=this.useColors,fi.inspect(e,this.inspectOpts)}});var ko=$((sA,_o)=>{var hi=globalThis.process??{browser:!0,cwd:()=>"/",env:{},platform:"android"};typeof hi>"u"||hi.type==="renderer"||hi.browser===!0||hi.__nwjs?_o.exports=Dc():_o.exports=Bc()});var ir=$((lA,Hc)=>{function Yc(e){return e&&e.__esModule&&e.default?e.default:e}(function(){let t=require;require=Object.assign(r=>{let n=t(r)??{};return Yc(n)},t)})();var Hx=Object.create,mi=Object.defineProperty,Gx=Object.getOwnPropertyDescriptor,$x=Object.getOwnPropertyNames,Jx=Object.getPrototypeOf,Qx=Object.prototype.hasOwnProperty,Zx=(e,t)=>{for(var r in t)mi(e,r,{get:t[r],enumerable:!0})},Vc=(e,t,r,n)=>{if(t&&typeof t=="object"||typeof t=="function")for(let a of $x(t))!Qx.call(e,a)&&a!==r&&mi(e,a,{get:()=>t[a],enumerable:!(n=Gx(t,a))||n.enumerable});return e},Kx=(e,t,r)=>(r=e!=null?Hx(Jx(e)):{},Vc(t||!e||!e.__esModule?mi(r,"default",{value:e,enumerable:!0}):r,e)),Xx=e=>Vc(mi({},"__esModule",{value:!0}),e),zc={};Zx(zc,{getDebugger:()=>e0,initDebugHelpers:()=>t0});Hc.exports=Xx(zc);var ln=Kx(Yc(ko()),1),Uc=",",pi="-";function e0(e){let t=ln.default.default(e);return t.log=(r,...n)=>{i0(e,r,...n)},t.printStackTrace=(r,n)=>{Wc(e,r,n)},t}function t0(){window.DEBUG={disable:r0,enable:n0,get:vo,set:xo}}function r0(e){let t=new Set(vo());for(let r of un(e)){if(r.startsWith(pi))continue;let n=pi+r;t.has(r)&&t.delete(r),t.add(n)}xo(Array.from(t))}function n0(e){let t=new Set(vo());for(let r of un(e)){if(!r.startsWith(pi)){let n=pi+r;t.has(n)&&t.delete(n)}t.add(r)}xo(Array.from(t))}function vo(){return un(ln.default.load()??"")}function i0(e,t,...r){if(!ln.default.enabled(e))return;let s=(new Error().stack?.split(`
`)??[])[4]??"";console.debug(t,...r),Wc(e,s,"Original debug message caller")}function Wc(e,t,r){ln.default.enabled(e)&&(t||(t="(unavailable)"),r||(r="Caller stack trace"),console.debug(`NotError:${e}:${r}
${t}`))}function xo(e){ln.default.enable(un(e).join(Uc))}function un(e){return typeof e=="string"?e.split(Uc).filter(Boolean):e.flatMap(un)}});var Fr=$((uA,$c)=>{function a0(e){return e&&e.__esModule&&e.default?e.default:e}(function(){let t=require;require=Object.assign(r=>{let n=t(r)??{};return a0(n)},t)})();var So=Object.defineProperty,o0=Object.getOwnPropertyDescriptor,s0=Object.getOwnPropertyNames,l0=Object.prototype.hasOwnProperty,u0=(e,t)=>{for(var r in t)So(e,r,{get:t[r],enumerable:!0})},c0=(e,t,r,n)=>{if(t&&typeof t=="object"||typeof t=="function")for(let a of s0(t))!l0.call(e,a)&&a!==r&&So(e,a,{get:()=>t[a],enumerable:!(n=o0(t,a))||n.enumerable});return e},f0=e=>c0(So({},"__esModule",{value:!0}),e),Gc={};u0(Gc,{noop:()=>d0,noopAsync:()=>h0,omitAsyncReturnType:()=>p0,omitReturnType:()=>m0});$c.exports=f0(Gc);function d0(){}async function h0(){}function p0(e){return async(...t)=>{await e(...t)}}function m0(e){return(...t)=>{e(...t)}}});var Po=$((fA,ef)=>{function Zc(e){return e&&e.__esModule&&e.default?e.default:e}(function(){let t=require;require=Object.assign(r=>{let n=t(r)??{};return Zc(n)},t)})();var g0=Object.create,gi=Object.defineProperty,y0=Object.getOwnPropertyDescriptor,w0=Object.getOwnPropertyNames,b0=Object.getPrototypeOf,_0=Object.prototype.hasOwnProperty,k0=(e,t)=>{for(var r in t)gi(e,r,{get:t[r],enumerable:!0})},Kc=(e,t,r,n)=>{if(t&&typeof t=="object"||typeof t=="function")for(let a of w0(t))!_0.call(e,a)&&a!==r&&gi(e,a,{get:()=>t[a],enumerable:!(n=y0(t,a))||n.enumerable});return e},v0=(e,t,r)=>(r=e!=null?g0(b0(e)):{},Kc(t||!e||!e.__esModule?gi(r,"default",{value:e,enumerable:!0}):r,e)),x0=e=>Kc(gi({},"__esModule",{value:!0}),e),Xc={};k0(Xc,{PluginBase:()=>Co});ef.exports=x0(Xc);var S0=v0(Zc(ko()),1),Jc=require("obsidian"),C0=ir(),P0=mt(),Qc=Fr(),cA=globalThis.process??{browser:!0,cwd:()=>"/",env:{},platform:"android"},Co=class extends Jc.Plugin{consoleDebug;get abortSignal(){return this._abortSignal}get settingsCopy(){return this.createPluginSettings(this.settings.toJSON())}get settings(){return this._settings}_abortSignal;_settings;notice;constructor(t,r){super(t,r),(0,C0.initDebugHelpers)(),this.consoleDebug=S0.default.default(r.id),console.debug(`Debug messages for plugin '${r.name}' are not shown by default. Set window.DEBUG.enable('${r.id}') to see them. See https://github.com/debug-js/debug?tab=readme-ov-file for more information`)}async onload(){this.register((0,P0.registerAsyncErrorEventHandler)(()=>{this.showNotice("An unhandled error occurred. Please check the console for more information.")})),await this.loadSettings();let t=this.createPluginSettingsTab();t&&this.addSettingTab(t);let r=new AbortController;this._abortSignal=r.signal,this.register(()=>{r.abort()}),await this.onloadComplete(),this.app.workspace.onLayoutReady(this.onLayoutReady.bind(this))}async saveSettings(t){let r=t.toJSON();this._settings=this.createPluginSettings(r),await this.saveData(r)}onLayoutReady(){(0,Qc.noop)()}onloadComplete(){(0,Qc.noop)()}showNotice(t){this.notice&&this.notice.hide(),this.notice=new Jc.Notice(`${this.manifest.name}
${t}`)}async loadSettings(){let t=await this.loadData();this._settings=this.createPluginSettings(t),this._settings.shouldSaveAfterLoad()&&await this.saveSettings(this._settings)}}});var tf=$(cn=>{"use strict";function O0(e,t){let r=Object.keys(t).map(n=>F0(e,n,t[n]));return r.length===1?r[0]:function(){r.forEach(n=>n())}}function F0(e,t,r){let n=e[t],a=e.hasOwnProperty(t),s=a?n:function(){return Object.getPrototypeOf(e)[t].apply(this,arguments)},l=r(s);return n&&Object.setPrototypeOf(l,n),Object.setPrototypeOf(c,l),e[t]=c,d;function c(...p){return l===s&&e[t]===c&&d(),l.apply(this,p)}function d(){e[t]===c&&(a?e[t]=s:delete e[t]),l!==s&&(l=s,Object.setPrototypeOf(c,n||Function))}}function E0(e,t,r){return n[e]=e,n;function n(...a){return(t[e]===e?t:r).apply(this,a)}}function Oo(e,t){return e.then(t,t)}function A0(e){let t=Promise.resolve();function r(...n){return t=new Promise((a,s)=>{Oo(t,()=>{e.apply(this,n).then(a,s)})})}return r.after=function(){return t=new Promise((n,a)=>{Oo(t,n)})},r}cn.after=Oo;cn.around=O0;cn.dedupe=E0;cn.serialize=A0});var At=$((pA,uf)=>{function T0(e){return e&&e.__esModule&&e.default?e.default:e}(function(){let t=require;require=Object.assign(r=>{let n=t(r)??{};return T0(n)},t)})();var Eo=Object.defineProperty,D0=Object.getOwnPropertyDescriptor,L0=Object.getOwnPropertyNames,M0=Object.prototype.hasOwnProperty,I0=(e,t)=>{for(var r in t)Eo(e,r,{get:t[r],enumerable:!0})},N0=(e,t,r,n)=>{if(t&&typeof t=="object"||typeof t=="function")for(let a of L0(t))!M0.call(e,a)&&a!==r&&Eo(e,a,{get:()=>t[a],enumerable:!(n=D0(t,a))||n.enumerable});return e},R0=e=>N0(Eo({},"__esModule",{value:!0}),e),rf={};I0(rf,{FunctionHandlingMode:()=>af,assignWithNonEnumerableProperties:()=>j0,cloneWithNonEnumerableProperties:()=>B0,deepEqual:()=>of,deleteProperties:()=>Y0,deleteProperty:()=>sf,getNestedPropertyValue:()=>V0,getPrototypeOf:()=>yi,nameof:()=>z0,normalizeOptionalProperties:()=>U0,setNestedPropertyValue:()=>W0,toJson:()=>H0});uf.exports=R0(rf);var nf=mt(),q0=Et(),hA=globalThis.process??{browser:!0,cwd:()=>"/",env:{},platform:"android"},af=(e=>(e.Exclude="exclude",e.Full="full",e.NameOnly="nameOnly",e))(af||{});function j0(e,...t){return lf(e,...t)}function B0(e){return Object.create(yi(e),Object.getOwnPropertyDescriptors(e))}function of(e,t){if(e===t)return!0;if(typeof e!="object"||typeof t!="object"||e===null||t===null)return!1;let r=Object.keys(e),n=Object.keys(t);if(r.length!==n.length)return!1;let a=e,s=t;for(let l of r)if(!n.includes(l)||!of(a[l],s[l]))return!1;return!0}function Y0(e,t){let r=!1;for(let n of t)r=sf(e,n)||r;return r}function sf(e,t){return Object.prototype.hasOwnProperty.call(e,t)?(delete e[t],!0):!1}function V0(e,t){let r=e,n=t.split(".");for(let a of n){if(r===void 0)return;r=r[a]}return r}function yi(e){return e==null?e:Object.getPrototypeOf(e)}function z0(e){return e}function U0(e){return e}function W0(e,t,r){let n=new Error(`Property path ${t} not found`),a=e,s=t.split(".");for(let c of s.slice(0,-1)){if(a===void 0)throw n;a=a[c]}let l=s.at(-1);if(a===void 0||l===void 0)throw n;a[l]=r}function H0(e,t={}){let r={functionHandlingMode:"exclude",maxDepth:-1,shouldCatchToJSONErrors:!1,shouldHandleCircularReferences:!1,shouldHandleErrors:!1,shouldHandleUndefined:!1,shouldSortKeys:!1,space:2,tokenSubstitutions:{circularReference:Fo("CircularReference"),maxDepthLimitReached:Fo("MaxDepthLimitReached"),toJSONFailed:Fo("ToJSONFailed")}},n={...r,...t,tokenSubstitutions:{...r.tokenSubstitutions,...t.tokenSubstitutions}};n.maxDepth===-1&&(n.maxDepth=1/0);let a=[],s=new WeakSet,l=e?.constructor?.name??"Object",c=p(e,"",0,!0),d=JSON.stringify(c,null,n.space)??"";return d=(0,q0.replaceAll)(d,/"\[\[([A-Za-z]+)(\d*)\]\]"/g,(h,m,y)=>G0({functionTexts:a,index:y?parseInt(y):0,key:m,substitutions:n.tokenSubstitutions})),d;function p(h,m,y,w){if(h===void 0)return y===0||n.shouldHandleUndefined?Er("Undefined"):void 0;if(h===null)return null;if(typeof h=="function"){if(n.functionHandlingMode==="exclude")return;let x=a.length,E=n.functionHandlingMode==="full"?h.toString():`function ${h.name||"anonymous"}() { /* ... */ }`;return a.push(E),Er("Function",x)}if(typeof h!="object")return h;if(s.has(h)){if(n.shouldHandleCircularReferences)return Er("CircularReference");throw new TypeError(`Converting circular structure to JSON
    --> starting at object with constructor '${l}'
    --- property '${m}' closes the circle`)}if(s.add(h),w){let x=h.toJSON;if(typeof x=="function")try{return h=x.call(h,m),p(h,m,y,!1)}catch(E){if(n.shouldCatchToJSONErrors)return Er("ToJSONFailed");throw E}}if(Array.isArray(h))return y>n.maxDepth?Er("MaxDepthLimitReachedArray",h.length):h.map((x,E)=>p(x,E.toString(),y+1,w));if(y>n.maxDepth)return Er("MaxDepthLimitReached");if(h instanceof Error&&n.shouldHandleErrors)return(0,nf.errorToString)(h);let v=Object.entries(h);return n.shouldSortKeys&&v.sort(([x],[E])=>x.localeCompare(E)),Object.fromEntries(v.map(([x,E])=>[x,p(E,x,y+1,w)]))}}function lf(e,...t){for(let n of t)Object.defineProperties(e,Object.getOwnPropertyDescriptors(n));let r=t.map(n=>yi(n)).filter(n=>!!n);if(r.length>0){let n=lf({},yi(e),...r);Object.setPrototypeOf(e,n)}return e}function G0(e){switch(e.key){case"CircularReference":return e.substitutions.circularReference;case"Function":return e.functionTexts[e.index]??(0,nf.throwExpression)(new Error(`Function with index ${e.index.toString()} not found`));case"MaxDepthLimitReached":return e.substitutions.maxDepthLimitReached;case"MaxDepthLimitReachedArray":return`Array(${e.index.toString()})`;case"ToJSONFailed":return e.substitutions.toJSONFailed;case"Undefined":return"undefined";default:return}}function Fo(e){return`{ "[[${e}]]": null }`}function Er(e,t){return`[[${e}${t?.toString()??""}]]`}});var To=$((mA,ff)=>{function $0(e){return e&&e.__esModule&&e.default?e.default:e}(function(){let t=require;require=Object.assign(r=>{let n=t(r)??{};return $0(n)},t)})();var Ao=Object.defineProperty,J0=Object.getOwnPropertyDescriptor,Q0=Object.getOwnPropertyNames,Z0=Object.prototype.hasOwnProperty,K0=(e,t)=>{for(var r in t)Ao(e,r,{get:t[r],enumerable:!0})},X0=(e,t,r,n)=>{if(t&&typeof t=="object"||typeof t=="function")for(let a of Q0(t))!Z0.call(e,a)&&a!==r&&Ao(e,a,{get:()=>t[a],enumerable:!(n=J0(t,a))||n.enumerable});return e},eS=e=>X0(Ao({},"__esModule",{value:!0}),e),cf={};K0(cf,{ValueWrapper:()=>wi,getApp:()=>tS,getObsidianDevUtilsState:()=>rS});ff.exports=eS(cf);var wi=class{constructor(t){this.value=t}};function tS(){let e;try{globalThis.require.resolve("obsidian/app"),e=!0}catch{e=!1}if(e)return globalThis.require("obsidian/app");let t=globalThis.app;if(t)return t;throw new Error("Obsidian app not found")}function rS(e,t,r){let n=e,a=n.obsidianDevUtilsState??={};return a[t]??=new wi(r)}});function ar(e,t){let r=t||nS,n=typeof r.includeImageAlt=="boolean"?r.includeImageAlt:!0,a=typeof r.includeHtml=="boolean"?r.includeHtml:!0;return hf(e,n,a)}function hf(e,t,r){if(iS(e)){if("value"in e)return e.type==="html"&&!r?"":e.value;if(t&&"alt"in e&&e.alt)return e.alt;if("children"in e)return df(e.children,t,r)}return Array.isArray(e)?df(e,t,r):""}function df(e,t,r){let n=[],a=-1;for(;++a<e.length;)n[a]=hf(e[a],t,r);return n.join("")}function iS(e){return!!(e&&typeof e=="object")}var nS,pf=S(()=>{nS={}});var bi=S(()=>{pf()});var Do,mf=S(()=>{Do={AElig:"\xC6",AMP:"&",Aacute:"\xC1",Abreve:"\u0102",Acirc:"\xC2",Acy:"\u0410",Afr:"\u{1D504}",Agrave:"\xC0",Alpha:"\u0391",Amacr:"\u0100",And:"\u2A53",Aogon:"\u0104",Aopf:"\u{1D538}",ApplyFunction:"\u2061",Aring:"\xC5",Ascr:"\u{1D49C}",Assign:"\u2254",Atilde:"\xC3",Auml:"\xC4",Backslash:"\u2216",Barv:"\u2AE7",Barwed:"\u2306",Bcy:"\u0411",Because:"\u2235",Bernoullis:"\u212C",Beta:"\u0392",Bfr:"\u{1D505}",Bopf:"\u{1D539}",Breve:"\u02D8",Bscr:"\u212C",Bumpeq:"\u224E",CHcy:"\u0427",COPY:"\xA9",Cacute:"\u0106",Cap:"\u22D2",CapitalDifferentialD:"\u2145",Cayleys:"\u212D",Ccaron:"\u010C",Ccedil:"\xC7",Ccirc:"\u0108",Cconint:"\u2230",Cdot:"\u010A",Cedilla:"\xB8",CenterDot:"\xB7",Cfr:"\u212D",Chi:"\u03A7",CircleDot:"\u2299",CircleMinus:"\u2296",CirclePlus:"\u2295",CircleTimes:"\u2297",ClockwiseContourIntegral:"\u2232",CloseCurlyDoubleQuote:"\u201D",CloseCurlyQuote:"\u2019",Colon:"\u2237",Colone:"\u2A74",Congruent:"\u2261",Conint:"\u222F",ContourIntegral:"\u222E",Copf:"\u2102",Coproduct:"\u2210",CounterClockwiseContourIntegral:"\u2233",Cross:"\u2A2F",Cscr:"\u{1D49E}",Cup:"\u22D3",CupCap:"\u224D",DD:"\u2145",DDotrahd:"\u2911",DJcy:"\u0402",DScy:"\u0405",DZcy:"\u040F",Dagger:"\u2021",Darr:"\u21A1",Dashv:"\u2AE4",Dcaron:"\u010E",Dcy:"\u0414",Del:"\u2207",Delta:"\u0394",Dfr:"\u{1D507}",DiacriticalAcute:"\xB4",DiacriticalDot:"\u02D9",DiacriticalDoubleAcute:"\u02DD",DiacriticalGrave:"`",DiacriticalTilde:"\u02DC",Diamond:"\u22C4",DifferentialD:"\u2146",Dopf:"\u{1D53B}",Dot:"\xA8",DotDot:"\u20DC",DotEqual:"\u2250",DoubleContourIntegral:"\u222F",DoubleDot:"\xA8",DoubleDownArrow:"\u21D3",DoubleLeftArrow:"\u21D0",DoubleLeftRightArrow:"\u21D4",DoubleLeftTee:"\u2AE4",DoubleLongLeftArrow:"\u27F8",DoubleLongLeftRightArrow:"\u27FA",DoubleLongRightArrow:"\u27F9",DoubleRightArrow:"\u21D2",DoubleRightTee:"\u22A8",DoubleUpArrow:"\u21D1",DoubleUpDownArrow:"\u21D5",DoubleVerticalBar:"\u2225",DownArrow:"\u2193",DownArrowBar:"\u2913",DownArrowUpArrow:"\u21F5",DownBreve:"\u0311",DownLeftRightVector:"\u2950",DownLeftTeeVector:"\u295E",DownLeftVector:"\u21BD",DownLeftVectorBar:"\u2956",DownRightTeeVector:"\u295F",DownRightVector:"\u21C1",DownRightVectorBar:"\u2957",DownTee:"\u22A4",DownTeeArrow:"\u21A7",Downarrow:"\u21D3",Dscr:"\u{1D49F}",Dstrok:"\u0110",ENG:"\u014A",ETH:"\xD0",Eacute:"\xC9",Ecaron:"\u011A",Ecirc:"\xCA",Ecy:"\u042D",Edot:"\u0116",Efr:"\u{1D508}",Egrave:"\xC8",Element:"\u2208",Emacr:"\u0112",EmptySmallSquare:"\u25FB",EmptyVerySmallSquare:"\u25AB",Eogon:"\u0118",Eopf:"\u{1D53C}",Epsilon:"\u0395",Equal:"\u2A75",EqualTilde:"\u2242",Equilibrium:"\u21CC",Escr:"\u2130",Esim:"\u2A73",Eta:"\u0397",Euml:"\xCB",Exists:"\u2203",ExponentialE:"\u2147",Fcy:"\u0424",Ffr:"\u{1D509}",FilledSmallSquare:"\u25FC",FilledVerySmallSquare:"\u25AA",Fopf:"\u{1D53D}",ForAll:"\u2200",Fouriertrf:"\u2131",Fscr:"\u2131",GJcy:"\u0403",GT:">",Gamma:"\u0393",Gammad:"\u03DC",Gbreve:"\u011E",Gcedil:"\u0122",Gcirc:"\u011C",Gcy:"\u0413",Gdot:"\u0120",Gfr:"\u{1D50A}",Gg:"\u22D9",Gopf:"\u{1D53E}",GreaterEqual:"\u2265",GreaterEqualLess:"\u22DB",GreaterFullEqual:"\u2267",GreaterGreater:"\u2AA2",GreaterLess:"\u2277",GreaterSlantEqual:"\u2A7E",GreaterTilde:"\u2273",Gscr:"\u{1D4A2}",Gt:"\u226B",HARDcy:"\u042A",Hacek:"\u02C7",Hat:"^",Hcirc:"\u0124",Hfr:"\u210C",HilbertSpace:"\u210B",Hopf:"\u210D",HorizontalLine:"\u2500",Hscr:"\u210B",Hstrok:"\u0126",HumpDownHump:"\u224E",HumpEqual:"\u224F",IEcy:"\u0415",IJlig:"\u0132",IOcy:"\u0401",Iacute:"\xCD",Icirc:"\xCE",Icy:"\u0418",Idot:"\u0130",Ifr:"\u2111",Igrave:"\xCC",Im:"\u2111",Imacr:"\u012A",ImaginaryI:"\u2148",Implies:"\u21D2",Int:"\u222C",Integral:"\u222B",Intersection:"\u22C2",InvisibleComma:"\u2063",InvisibleTimes:"\u2062",Iogon:"\u012E",Iopf:"\u{1D540}",Iota:"\u0399",Iscr:"\u2110",Itilde:"\u0128",Iukcy:"\u0406",Iuml:"\xCF",Jcirc:"\u0134",Jcy:"\u0419",Jfr:"\u{1D50D}",Jopf:"\u{1D541}",Jscr:"\u{1D4A5}",Jsercy:"\u0408",Jukcy:"\u0404",KHcy:"\u0425",KJcy:"\u040C",Kappa:"\u039A",Kcedil:"\u0136",Kcy:"\u041A",Kfr:"\u{1D50E}",Kopf:"\u{1D542}",Kscr:"\u{1D4A6}",LJcy:"\u0409",LT:"<",Lacute:"\u0139",Lambda:"\u039B",Lang:"\u27EA",Laplacetrf:"\u2112",Larr:"\u219E",Lcaron:"\u013D",Lcedil:"\u013B",Lcy:"\u041B",LeftAngleBracket:"\u27E8",LeftArrow:"\u2190",LeftArrowBar:"\u21E4",LeftArrowRightArrow:"\u21C6",LeftCeiling:"\u2308",LeftDoubleBracket:"\u27E6",LeftDownTeeVector:"\u2961",LeftDownVector:"\u21C3",LeftDownVectorBar:"\u2959",LeftFloor:"\u230A",LeftRightArrow:"\u2194",LeftRightVector:"\u294E",LeftTee:"\u22A3",LeftTeeArrow:"\u21A4",LeftTeeVector:"\u295A",LeftTriangle:"\u22B2",LeftTriangleBar:"\u29CF",LeftTriangleEqual:"\u22B4",LeftUpDownVector:"\u2951",LeftUpTeeVector:"\u2960",LeftUpVector:"\u21BF",LeftUpVectorBar:"\u2958",LeftVector:"\u21BC",LeftVectorBar:"\u2952",Leftarrow:"\u21D0",Leftrightarrow:"\u21D4",LessEqualGreater:"\u22DA",LessFullEqual:"\u2266",LessGreater:"\u2276",LessLess:"\u2AA1",LessSlantEqual:"\u2A7D",LessTilde:"\u2272",Lfr:"\u{1D50F}",Ll:"\u22D8",Lleftarrow:"\u21DA",Lmidot:"\u013F",LongLeftArrow:"\u27F5",LongLeftRightArrow:"\u27F7",LongRightArrow:"\u27F6",Longleftarrow:"\u27F8",Longleftrightarrow:"\u27FA",Longrightarrow:"\u27F9",Lopf:"\u{1D543}",LowerLeftArrow:"\u2199",LowerRightArrow:"\u2198",Lscr:"\u2112",Lsh:"\u21B0",Lstrok:"\u0141",Lt:"\u226A",Map:"\u2905",Mcy:"\u041C",MediumSpace:"\u205F",Mellintrf:"\u2133",Mfr:"\u{1D510}",MinusPlus:"\u2213",Mopf:"\u{1D544}",Mscr:"\u2133",Mu:"\u039C",NJcy:"\u040A",Nacute:"\u0143",Ncaron:"\u0147",Ncedil:"\u0145",Ncy:"\u041D",NegativeMediumSpace:"\u200B",NegativeThickSpace:"\u200B",NegativeThinSpace:"\u200B",NegativeVeryThinSpace:"\u200B",NestedGreaterGreater:"\u226B",NestedLessLess:"\u226A",NewLine:`
`,Nfr:"\u{1D511}",NoBreak:"\u2060",NonBreakingSpace:"\xA0",Nopf:"\u2115",Not:"\u2AEC",NotCongruent:"\u2262",NotCupCap:"\u226D",NotDoubleVerticalBar:"\u2226",NotElement:"\u2209",NotEqual:"\u2260",NotEqualTilde:"\u2242\u0338",NotExists:"\u2204",NotGreater:"\u226F",NotGreaterEqual:"\u2271",NotGreaterFullEqual:"\u2267\u0338",NotGreaterGreater:"\u226B\u0338",NotGreaterLess:"\u2279",NotGreaterSlantEqual:"\u2A7E\u0338",NotGreaterTilde:"\u2275",NotHumpDownHump:"\u224E\u0338",NotHumpEqual:"\u224F\u0338",NotLeftTriangle:"\u22EA",NotLeftTriangleBar:"\u29CF\u0338",NotLeftTriangleEqual:"\u22EC",NotLess:"\u226E",NotLessEqual:"\u2270",NotLessGreater:"\u2278",NotLessLess:"\u226A\u0338",NotLessSlantEqual:"\u2A7D\u0338",NotLessTilde:"\u2274",NotNestedGreaterGreater:"\u2AA2\u0338",NotNestedLessLess:"\u2AA1\u0338",NotPrecedes:"\u2280",NotPrecedesEqual:"\u2AAF\u0338",NotPrecedesSlantEqual:"\u22E0",NotReverseElement:"\u220C",NotRightTriangle:"\u22EB",NotRightTriangleBar:"\u29D0\u0338",NotRightTriangleEqual:"\u22ED",NotSquareSubset:"\u228F\u0338",NotSquareSubsetEqual:"\u22E2",NotSquareSuperset:"\u2290\u0338",NotSquareSupersetEqual:"\u22E3",NotSubset:"\u2282\u20D2",NotSubsetEqual:"\u2288",NotSucceeds:"\u2281",NotSucceedsEqual:"\u2AB0\u0338",NotSucceedsSlantEqual:"\u22E1",NotSucceedsTilde:"\u227F\u0338",NotSuperset:"\u2283\u20D2",NotSupersetEqual:"\u2289",NotTilde:"\u2241",NotTildeEqual:"\u2244",NotTildeFullEqual:"\u2247",NotTildeTilde:"\u2249",NotVerticalBar:"\u2224",Nscr:"\u{1D4A9}",Ntilde:"\xD1",Nu:"\u039D",OElig:"\u0152",Oacute:"\xD3",Ocirc:"\xD4",Ocy:"\u041E",Odblac:"\u0150",Ofr:"\u{1D512}",Ograve:"\xD2",Omacr:"\u014C",Omega:"\u03A9",Omicron:"\u039F",Oopf:"\u{1D546}",OpenCurlyDoubleQuote:"\u201C",OpenCurlyQuote:"\u2018",Or:"\u2A54",Oscr:"\u{1D4AA}",Oslash:"\xD8",Otilde:"\xD5",Otimes:"\u2A37",Ouml:"\xD6",OverBar:"\u203E",OverBrace:"\u23DE",OverBracket:"\u23B4",OverParenthesis:"\u23DC",PartialD:"\u2202",Pcy:"\u041F",Pfr:"\u{1D513}",Phi:"\u03A6",Pi:"\u03A0",PlusMinus:"\xB1",Poincareplane:"\u210C",Popf:"\u2119",Pr:"\u2ABB",Precedes:"\u227A",PrecedesEqual:"\u2AAF",PrecedesSlantEqual:"\u227C",PrecedesTilde:"\u227E",Prime:"\u2033",Product:"\u220F",Proportion:"\u2237",Proportional:"\u221D",Pscr:"\u{1D4AB}",Psi:"\u03A8",QUOT:'"',Qfr:"\u{1D514}",Qopf:"\u211A",Qscr:"\u{1D4AC}",RBarr:"\u2910",REG:"\xAE",Racute:"\u0154",Rang:"\u27EB",Rarr:"\u21A0",Rarrtl:"\u2916",Rcaron:"\u0158",Rcedil:"\u0156",Rcy:"\u0420",Re:"\u211C",ReverseElement:"\u220B",ReverseEquilibrium:"\u21CB",ReverseUpEquilibrium:"\u296F",Rfr:"\u211C",Rho:"\u03A1",RightAngleBracket:"\u27E9",RightArrow:"\u2192",RightArrowBar:"\u21E5",RightArrowLeftArrow:"\u21C4",RightCeiling:"\u2309",RightDoubleBracket:"\u27E7",RightDownTeeVector:"\u295D",RightDownVector:"\u21C2",RightDownVectorBar:"\u2955",RightFloor:"\u230B",RightTee:"\u22A2",RightTeeArrow:"\u21A6",RightTeeVector:"\u295B",RightTriangle:"\u22B3",RightTriangleBar:"\u29D0",RightTriangleEqual:"\u22B5",RightUpDownVector:"\u294F",RightUpTeeVector:"\u295C",RightUpVector:"\u21BE",RightUpVectorBar:"\u2954",RightVector:"\u21C0",RightVectorBar:"\u2953",Rightarrow:"\u21D2",Ropf:"\u211D",RoundImplies:"\u2970",Rrightarrow:"\u21DB",Rscr:"\u211B",Rsh:"\u21B1",RuleDelayed:"\u29F4",SHCHcy:"\u0429",SHcy:"\u0428",SOFTcy:"\u042C",Sacute:"\u015A",Sc:"\u2ABC",Scaron:"\u0160",Scedil:"\u015E",Scirc:"\u015C",Scy:"\u0421",Sfr:"\u{1D516}",ShortDownArrow:"\u2193",ShortLeftArrow:"\u2190",ShortRightArrow:"\u2192",ShortUpArrow:"\u2191",Sigma:"\u03A3",SmallCircle:"\u2218",Sopf:"\u{1D54A}",Sqrt:"\u221A",Square:"\u25A1",SquareIntersection:"\u2293",SquareSubset:"\u228F",SquareSubsetEqual:"\u2291",SquareSuperset:"\u2290",SquareSupersetEqual:"\u2292",SquareUnion:"\u2294",Sscr:"\u{1D4AE}",Star:"\u22C6",Sub:"\u22D0",Subset:"\u22D0",SubsetEqual:"\u2286",Succeeds:"\u227B",SucceedsEqual:"\u2AB0",SucceedsSlantEqual:"\u227D",SucceedsTilde:"\u227F",SuchThat:"\u220B",Sum:"\u2211",Sup:"\u22D1",Superset:"\u2283",SupersetEqual:"\u2287",Supset:"\u22D1",THORN:"\xDE",TRADE:"\u2122",TSHcy:"\u040B",TScy:"\u0426",Tab:"	",Tau:"\u03A4",Tcaron:"\u0164",Tcedil:"\u0162",Tcy:"\u0422",Tfr:"\u{1D517}",Therefore:"\u2234",Theta:"\u0398",ThickSpace:"\u205F\u200A",ThinSpace:"\u2009",Tilde:"\u223C",TildeEqual:"\u2243",TildeFullEqual:"\u2245",TildeTilde:"\u2248",Topf:"\u{1D54B}",TripleDot:"\u20DB",Tscr:"\u{1D4AF}",Tstrok:"\u0166",Uacute:"\xDA",Uarr:"\u219F",Uarrocir:"\u2949",Ubrcy:"\u040E",Ubreve:"\u016C",Ucirc:"\xDB",Ucy:"\u0423",Udblac:"\u0170",Ufr:"\u{1D518}",Ugrave:"\xD9",Umacr:"\u016A",UnderBar:"_",UnderBrace:"\u23DF",UnderBracket:"\u23B5",UnderParenthesis:"\u23DD",Union:"\u22C3",UnionPlus:"\u228E",Uogon:"\u0172",Uopf:"\u{1D54C}",UpArrow:"\u2191",UpArrowBar:"\u2912",UpArrowDownArrow:"\u21C5",UpDownArrow:"\u2195",UpEquilibrium:"\u296E",UpTee:"\u22A5",UpTeeArrow:"\u21A5",Uparrow:"\u21D1",Updownarrow:"\u21D5",UpperLeftArrow:"\u2196",UpperRightArrow:"\u2197",Upsi:"\u03D2",Upsilon:"\u03A5",Uring:"\u016E",Uscr:"\u{1D4B0}",Utilde:"\u0168",Uuml:"\xDC",VDash:"\u22AB",Vbar:"\u2AEB",Vcy:"\u0412",Vdash:"\u22A9",Vdashl:"\u2AE6",Vee:"\u22C1",Verbar:"\u2016",Vert:"\u2016",VerticalBar:"\u2223",VerticalLine:"|",VerticalSeparator:"\u2758",VerticalTilde:"\u2240",VeryThinSpace:"\u200A",Vfr:"\u{1D519}",Vopf:"\u{1D54D}",Vscr:"\u{1D4B1}",Vvdash:"\u22AA",Wcirc:"\u0174",Wedge:"\u22C0",Wfr:"\u{1D51A}",Wopf:"\u{1D54E}",Wscr:"\u{1D4B2}",Xfr:"\u{1D51B}",Xi:"\u039E",Xopf:"\u{1D54F}",Xscr:"\u{1D4B3}",YAcy:"\u042F",YIcy:"\u0407",YUcy:"\u042E",Yacute:"\xDD",Ycirc:"\u0176",Ycy:"\u042B",Yfr:"\u{1D51C}",Yopf:"\u{1D550}",Yscr:"\u{1D4B4}",Yuml:"\u0178",ZHcy:"\u0416",Zacute:"\u0179",Zcaron:"\u017D",Zcy:"\u0417",Zdot:"\u017B",ZeroWidthSpace:"\u200B",Zeta:"\u0396",Zfr:"\u2128",Zopf:"\u2124",Zscr:"\u{1D4B5}",aacute:"\xE1",abreve:"\u0103",ac:"\u223E",acE:"\u223E\u0333",acd:"\u223F",acirc:"\xE2",acute:"\xB4",acy:"\u0430",aelig:"\xE6",af:"\u2061",afr:"\u{1D51E}",agrave:"\xE0",alefsym:"\u2135",aleph:"\u2135",alpha:"\u03B1",amacr:"\u0101",amalg:"\u2A3F",amp:"&",and:"\u2227",andand:"\u2A55",andd:"\u2A5C",andslope:"\u2A58",andv:"\u2A5A",ang:"\u2220",ange:"\u29A4",angle:"\u2220",angmsd:"\u2221",angmsdaa:"\u29A8",angmsdab:"\u29A9",angmsdac:"\u29AA",angmsdad:"\u29AB",angmsdae:"\u29AC",angmsdaf:"\u29AD",angmsdag:"\u29AE",angmsdah:"\u29AF",angrt:"\u221F",angrtvb:"\u22BE",angrtvbd:"\u299D",angsph:"\u2222",angst:"\xC5",angzarr:"\u237C",aogon:"\u0105",aopf:"\u{1D552}",ap:"\u2248",apE:"\u2A70",apacir:"\u2A6F",ape:"\u224A",apid:"\u224B",apos:"'",approx:"\u2248",approxeq:"\u224A",aring:"\xE5",ascr:"\u{1D4B6}",ast:"*",asymp:"\u2248",asympeq:"\u224D",atilde:"\xE3",auml:"\xE4",awconint:"\u2233",awint:"\u2A11",bNot:"\u2AED",backcong:"\u224C",backepsilon:"\u03F6",backprime:"\u2035",backsim:"\u223D",backsimeq:"\u22CD",barvee:"\u22BD",barwed:"\u2305",barwedge:"\u2305",bbrk:"\u23B5",bbrktbrk:"\u23B6",bcong:"\u224C",bcy:"\u0431",bdquo:"\u201E",becaus:"\u2235",because:"\u2235",bemptyv:"\u29B0",bepsi:"\u03F6",bernou:"\u212C",beta:"\u03B2",beth:"\u2136",between:"\u226C",bfr:"\u{1D51F}",bigcap:"\u22C2",bigcirc:"\u25EF",bigcup:"\u22C3",bigodot:"\u2A00",bigoplus:"\u2A01",bigotimes:"\u2A02",bigsqcup:"\u2A06",bigstar:"\u2605",bigtriangledown:"\u25BD",bigtriangleup:"\u25B3",biguplus:"\u2A04",bigvee:"\u22C1",bigwedge:"\u22C0",bkarow:"\u290D",blacklozenge:"\u29EB",blacksquare:"\u25AA",blacktriangle:"\u25B4",blacktriangledown:"\u25BE",blacktriangleleft:"\u25C2",blacktriangleright:"\u25B8",blank:"\u2423",blk12:"\u2592",blk14:"\u2591",blk34:"\u2593",block:"\u2588",bne:"=\u20E5",bnequiv:"\u2261\u20E5",bnot:"\u2310",bopf:"\u{1D553}",bot:"\u22A5",bottom:"\u22A5",bowtie:"\u22C8",boxDL:"\u2557",boxDR:"\u2554",boxDl:"\u2556",boxDr:"\u2553",boxH:"\u2550",boxHD:"\u2566",boxHU:"\u2569",boxHd:"\u2564",boxHu:"\u2567",boxUL:"\u255D",boxUR:"\u255A",boxUl:"\u255C",boxUr:"\u2559",boxV:"\u2551",boxVH:"\u256C",boxVL:"\u2563",boxVR:"\u2560",boxVh:"\u256B",boxVl:"\u2562",boxVr:"\u255F",boxbox:"\u29C9",boxdL:"\u2555",boxdR:"\u2552",boxdl:"\u2510",boxdr:"\u250C",boxh:"\u2500",boxhD:"\u2565",boxhU:"\u2568",boxhd:"\u252C",boxhu:"\u2534",boxminus:"\u229F",boxplus:"\u229E",boxtimes:"\u22A0",boxuL:"\u255B",boxuR:"\u2558",boxul:"\u2518",boxur:"\u2514",boxv:"\u2502",boxvH:"\u256A",boxvL:"\u2561",boxvR:"\u255E",boxvh:"\u253C",boxvl:"\u2524",boxvr:"\u251C",bprime:"\u2035",breve:"\u02D8",brvbar:"\xA6",bscr:"\u{1D4B7}",bsemi:"\u204F",bsim:"\u223D",bsime:"\u22CD",bsol:"\\",bsolb:"\u29C5",bsolhsub:"\u27C8",bull:"\u2022",bullet:"\u2022",bump:"\u224E",bumpE:"\u2AAE",bumpe:"\u224F",bumpeq:"\u224F",cacute:"\u0107",cap:"\u2229",capand:"\u2A44",capbrcup:"\u2A49",capcap:"\u2A4B",capcup:"\u2A47",capdot:"\u2A40",caps:"\u2229\uFE00",caret:"\u2041",caron:"\u02C7",ccaps:"\u2A4D",ccaron:"\u010D",ccedil:"\xE7",ccirc:"\u0109",ccups:"\u2A4C",ccupssm:"\u2A50",cdot:"\u010B",cedil:"\xB8",cemptyv:"\u29B2",cent:"\xA2",centerdot:"\xB7",cfr:"\u{1D520}",chcy:"\u0447",check:"\u2713",checkmark:"\u2713",chi:"\u03C7",cir:"\u25CB",cirE:"\u29C3",circ:"\u02C6",circeq:"\u2257",circlearrowleft:"\u21BA",circlearrowright:"\u21BB",circledR:"\xAE",circledS:"\u24C8",circledast:"\u229B",circledcirc:"\u229A",circleddash:"\u229D",cire:"\u2257",cirfnint:"\u2A10",cirmid:"\u2AEF",cirscir:"\u29C2",clubs:"\u2663",clubsuit:"\u2663",colon:":",colone:"\u2254",coloneq:"\u2254",comma:",",commat:"@",comp:"\u2201",compfn:"\u2218",complement:"\u2201",complexes:"\u2102",cong:"\u2245",congdot:"\u2A6D",conint:"\u222E",copf:"\u{1D554}",coprod:"\u2210",copy:"\xA9",copysr:"\u2117",crarr:"\u21B5",cross:"\u2717",cscr:"\u{1D4B8}",csub:"\u2ACF",csube:"\u2AD1",csup:"\u2AD0",csupe:"\u2AD2",ctdot:"\u22EF",cudarrl:"\u2938",cudarrr:"\u2935",cuepr:"\u22DE",cuesc:"\u22DF",cularr:"\u21B6",cularrp:"\u293D",cup:"\u222A",cupbrcap:"\u2A48",cupcap:"\u2A46",cupcup:"\u2A4A",cupdot:"\u228D",cupor:"\u2A45",cups:"\u222A\uFE00",curarr:"\u21B7",curarrm:"\u293C",curlyeqprec:"\u22DE",curlyeqsucc:"\u22DF",curlyvee:"\u22CE",curlywedge:"\u22CF",curren:"\xA4",curvearrowleft:"\u21B6",curvearrowright:"\u21B7",cuvee:"\u22CE",cuwed:"\u22CF",cwconint:"\u2232",cwint:"\u2231",cylcty:"\u232D",dArr:"\u21D3",dHar:"\u2965",dagger:"\u2020",daleth:"\u2138",darr:"\u2193",dash:"\u2010",dashv:"\u22A3",dbkarow:"\u290F",dblac:"\u02DD",dcaron:"\u010F",dcy:"\u0434",dd:"\u2146",ddagger:"\u2021",ddarr:"\u21CA",ddotseq:"\u2A77",deg:"\xB0",delta:"\u03B4",demptyv:"\u29B1",dfisht:"\u297F",dfr:"\u{1D521}",dharl:"\u21C3",dharr:"\u21C2",diam:"\u22C4",diamond:"\u22C4",diamondsuit:"\u2666",diams:"\u2666",die:"\xA8",digamma:"\u03DD",disin:"\u22F2",div:"\xF7",divide:"\xF7",divideontimes:"\u22C7",divonx:"\u22C7",djcy:"\u0452",dlcorn:"\u231E",dlcrop:"\u230D",dollar:"$",dopf:"\u{1D555}",dot:"\u02D9",doteq:"\u2250",doteqdot:"\u2251",dotminus:"\u2238",dotplus:"\u2214",dotsquare:"\u22A1",doublebarwedge:"\u2306",downarrow:"\u2193",downdownarrows:"\u21CA",downharpoonleft:"\u21C3",downharpoonright:"\u21C2",drbkarow:"\u2910",drcorn:"\u231F",drcrop:"\u230C",dscr:"\u{1D4B9}",dscy:"\u0455",dsol:"\u29F6",dstrok:"\u0111",dtdot:"\u22F1",dtri:"\u25BF",dtrif:"\u25BE",duarr:"\u21F5",duhar:"\u296F",dwangle:"\u29A6",dzcy:"\u045F",dzigrarr:"\u27FF",eDDot:"\u2A77",eDot:"\u2251",eacute:"\xE9",easter:"\u2A6E",ecaron:"\u011B",ecir:"\u2256",ecirc:"\xEA",ecolon:"\u2255",ecy:"\u044D",edot:"\u0117",ee:"\u2147",efDot:"\u2252",efr:"\u{1D522}",eg:"\u2A9A",egrave:"\xE8",egs:"\u2A96",egsdot:"\u2A98",el:"\u2A99",elinters:"\u23E7",ell:"\u2113",els:"\u2A95",elsdot:"\u2A97",emacr:"\u0113",empty:"\u2205",emptyset:"\u2205",emptyv:"\u2205",emsp13:"\u2004",emsp14:"\u2005",emsp:"\u2003",eng:"\u014B",ensp:"\u2002",eogon:"\u0119",eopf:"\u{1D556}",epar:"\u22D5",eparsl:"\u29E3",eplus:"\u2A71",epsi:"\u03B5",epsilon:"\u03B5",epsiv:"\u03F5",eqcirc:"\u2256",eqcolon:"\u2255",eqsim:"\u2242",eqslantgtr:"\u2A96",eqslantless:"\u2A95",equals:"=",equest:"\u225F",equiv:"\u2261",equivDD:"\u2A78",eqvparsl:"\u29E5",erDot:"\u2253",erarr:"\u2971",escr:"\u212F",esdot:"\u2250",esim:"\u2242",eta:"\u03B7",eth:"\xF0",euml:"\xEB",euro:"\u20AC",excl:"!",exist:"\u2203",expectation:"\u2130",exponentiale:"\u2147",fallingdotseq:"\u2252",fcy:"\u0444",female:"\u2640",ffilig:"\uFB03",fflig:"\uFB00",ffllig:"\uFB04",ffr:"\u{1D523}",filig:"\uFB01",fjlig:"fj",flat:"\u266D",fllig:"\uFB02",fltns:"\u25B1",fnof:"\u0192",fopf:"\u{1D557}",forall:"\u2200",fork:"\u22D4",forkv:"\u2AD9",fpartint:"\u2A0D",frac12:"\xBD",frac13:"\u2153",frac14:"\xBC",frac15:"\u2155",frac16:"\u2159",frac18:"\u215B",frac23:"\u2154",frac25:"\u2156",frac34:"\xBE",frac35:"\u2157",frac38:"\u215C",frac45:"\u2158",frac56:"\u215A",frac58:"\u215D",frac78:"\u215E",frasl:"\u2044",frown:"\u2322",fscr:"\u{1D4BB}",gE:"\u2267",gEl:"\u2A8C",gacute:"\u01F5",gamma:"\u03B3",gammad:"\u03DD",gap:"\u2A86",gbreve:"\u011F",gcirc:"\u011D",gcy:"\u0433",gdot:"\u0121",ge:"\u2265",gel:"\u22DB",geq:"\u2265",geqq:"\u2267",geqslant:"\u2A7E",ges:"\u2A7E",gescc:"\u2AA9",gesdot:"\u2A80",gesdoto:"\u2A82",gesdotol:"\u2A84",gesl:"\u22DB\uFE00",gesles:"\u2A94",gfr:"\u{1D524}",gg:"\u226B",ggg:"\u22D9",gimel:"\u2137",gjcy:"\u0453",gl:"\u2277",glE:"\u2A92",gla:"\u2AA5",glj:"\u2AA4",gnE:"\u2269",gnap:"\u2A8A",gnapprox:"\u2A8A",gne:"\u2A88",gneq:"\u2A88",gneqq:"\u2269",gnsim:"\u22E7",gopf:"\u{1D558}",grave:"`",gscr:"\u210A",gsim:"\u2273",gsime:"\u2A8E",gsiml:"\u2A90",gt:">",gtcc:"\u2AA7",gtcir:"\u2A7A",gtdot:"\u22D7",gtlPar:"\u2995",gtquest:"\u2A7C",gtrapprox:"\u2A86",gtrarr:"\u2978",gtrdot:"\u22D7",gtreqless:"\u22DB",gtreqqless:"\u2A8C",gtrless:"\u2277",gtrsim:"\u2273",gvertneqq:"\u2269\uFE00",gvnE:"\u2269\uFE00",hArr:"\u21D4",hairsp:"\u200A",half:"\xBD",hamilt:"\u210B",hardcy:"\u044A",harr:"\u2194",harrcir:"\u2948",harrw:"\u21AD",hbar:"\u210F",hcirc:"\u0125",hearts:"\u2665",heartsuit:"\u2665",hellip:"\u2026",hercon:"\u22B9",hfr:"\u{1D525}",hksearow:"\u2925",hkswarow:"\u2926",hoarr:"\u21FF",homtht:"\u223B",hookleftarrow:"\u21A9",hookrightarrow:"\u21AA",hopf:"\u{1D559}",horbar:"\u2015",hscr:"\u{1D4BD}",hslash:"\u210F",hstrok:"\u0127",hybull:"\u2043",hyphen:"\u2010",iacute:"\xED",ic:"\u2063",icirc:"\xEE",icy:"\u0438",iecy:"\u0435",iexcl:"\xA1",iff:"\u21D4",ifr:"\u{1D526}",igrave:"\xEC",ii:"\u2148",iiiint:"\u2A0C",iiint:"\u222D",iinfin:"\u29DC",iiota:"\u2129",ijlig:"\u0133",imacr:"\u012B",image:"\u2111",imagline:"\u2110",imagpart:"\u2111",imath:"\u0131",imof:"\u22B7",imped:"\u01B5",in:"\u2208",incare:"\u2105",infin:"\u221E",infintie:"\u29DD",inodot:"\u0131",int:"\u222B",intcal:"\u22BA",integers:"\u2124",intercal:"\u22BA",intlarhk:"\u2A17",intprod:"\u2A3C",iocy:"\u0451",iogon:"\u012F",iopf:"\u{1D55A}",iota:"\u03B9",iprod:"\u2A3C",iquest:"\xBF",iscr:"\u{1D4BE}",isin:"\u2208",isinE:"\u22F9",isindot:"\u22F5",isins:"\u22F4",isinsv:"\u22F3",isinv:"\u2208",it:"\u2062",itilde:"\u0129",iukcy:"\u0456",iuml:"\xEF",jcirc:"\u0135",jcy:"\u0439",jfr:"\u{1D527}",jmath:"\u0237",jopf:"\u{1D55B}",jscr:"\u{1D4BF}",jsercy:"\u0458",jukcy:"\u0454",kappa:"\u03BA",kappav:"\u03F0",kcedil:"\u0137",kcy:"\u043A",kfr:"\u{1D528}",kgreen:"\u0138",khcy:"\u0445",kjcy:"\u045C",kopf:"\u{1D55C}",kscr:"\u{1D4C0}",lAarr:"\u21DA",lArr:"\u21D0",lAtail:"\u291B",lBarr:"\u290E",lE:"\u2266",lEg:"\u2A8B",lHar:"\u2962",lacute:"\u013A",laemptyv:"\u29B4",lagran:"\u2112",lambda:"\u03BB",lang:"\u27E8",langd:"\u2991",langle:"\u27E8",lap:"\u2A85",laquo:"\xAB",larr:"\u2190",larrb:"\u21E4",larrbfs:"\u291F",larrfs:"\u291D",larrhk:"\u21A9",larrlp:"\u21AB",larrpl:"\u2939",larrsim:"\u2973",larrtl:"\u21A2",lat:"\u2AAB",latail:"\u2919",late:"\u2AAD",lates:"\u2AAD\uFE00",lbarr:"\u290C",lbbrk:"\u2772",lbrace:"{",lbrack:"[",lbrke:"\u298B",lbrksld:"\u298F",lbrkslu:"\u298D",lcaron:"\u013E",lcedil:"\u013C",lceil:"\u2308",lcub:"{",lcy:"\u043B",ldca:"\u2936",ldquo:"\u201C",ldquor:"\u201E",ldrdhar:"\u2967",ldrushar:"\u294B",ldsh:"\u21B2",le:"\u2264",leftarrow:"\u2190",leftarrowtail:"\u21A2",leftharpoondown:"\u21BD",leftharpoonup:"\u21BC",leftleftarrows:"\u21C7",leftrightarrow:"\u2194",leftrightarrows:"\u21C6",leftrightharpoons:"\u21CB",leftrightsquigarrow:"\u21AD",leftthreetimes:"\u22CB",leg:"\u22DA",leq:"\u2264",leqq:"\u2266",leqslant:"\u2A7D",les:"\u2A7D",lescc:"\u2AA8",lesdot:"\u2A7F",lesdoto:"\u2A81",lesdotor:"\u2A83",lesg:"\u22DA\uFE00",lesges:"\u2A93",lessapprox:"\u2A85",lessdot:"\u22D6",lesseqgtr:"\u22DA",lesseqqgtr:"\u2A8B",lessgtr:"\u2276",lesssim:"\u2272",lfisht:"\u297C",lfloor:"\u230A",lfr:"\u{1D529}",lg:"\u2276",lgE:"\u2A91",lhard:"\u21BD",lharu:"\u21BC",lharul:"\u296A",lhblk:"\u2584",ljcy:"\u0459",ll:"\u226A",llarr:"\u21C7",llcorner:"\u231E",llhard:"\u296B",lltri:"\u25FA",lmidot:"\u0140",lmoust:"\u23B0",lmoustache:"\u23B0",lnE:"\u2268",lnap:"\u2A89",lnapprox:"\u2A89",lne:"\u2A87",lneq:"\u2A87",lneqq:"\u2268",lnsim:"\u22E6",loang:"\u27EC",loarr:"\u21FD",lobrk:"\u27E6",longleftarrow:"\u27F5",longleftrightarrow:"\u27F7",longmapsto:"\u27FC",longrightarrow:"\u27F6",looparrowleft:"\u21AB",looparrowright:"\u21AC",lopar:"\u2985",lopf:"\u{1D55D}",loplus:"\u2A2D",lotimes:"\u2A34",lowast:"\u2217",lowbar:"_",loz:"\u25CA",lozenge:"\u25CA",lozf:"\u29EB",lpar:"(",lparlt:"\u2993",lrarr:"\u21C6",lrcorner:"\u231F",lrhar:"\u21CB",lrhard:"\u296D",lrm:"\u200E",lrtri:"\u22BF",lsaquo:"\u2039",lscr:"\u{1D4C1}",lsh:"\u21B0",lsim:"\u2272",lsime:"\u2A8D",lsimg:"\u2A8F",lsqb:"[",lsquo:"\u2018",lsquor:"\u201A",lstrok:"\u0142",lt:"<",ltcc:"\u2AA6",ltcir:"\u2A79",ltdot:"\u22D6",lthree:"\u22CB",ltimes:"\u22C9",ltlarr:"\u2976",ltquest:"\u2A7B",ltrPar:"\u2996",ltri:"\u25C3",ltrie:"\u22B4",ltrif:"\u25C2",lurdshar:"\u294A",luruhar:"\u2966",lvertneqq:"\u2268\uFE00",lvnE:"\u2268\uFE00",mDDot:"\u223A",macr:"\xAF",male:"\u2642",malt:"\u2720",maltese:"\u2720",map:"\u21A6",mapsto:"\u21A6",mapstodown:"\u21A7",mapstoleft:"\u21A4",mapstoup:"\u21A5",marker:"\u25AE",mcomma:"\u2A29",mcy:"\u043C",mdash:"\u2014",measuredangle:"\u2221",mfr:"\u{1D52A}",mho:"\u2127",micro:"\xB5",mid:"\u2223",midast:"*",midcir:"\u2AF0",middot:"\xB7",minus:"\u2212",minusb:"\u229F",minusd:"\u2238",minusdu:"\u2A2A",mlcp:"\u2ADB",mldr:"\u2026",mnplus:"\u2213",models:"\u22A7",mopf:"\u{1D55E}",mp:"\u2213",mscr:"\u{1D4C2}",mstpos:"\u223E",mu:"\u03BC",multimap:"\u22B8",mumap:"\u22B8",nGg:"\u22D9\u0338",nGt:"\u226B\u20D2",nGtv:"\u226B\u0338",nLeftarrow:"\u21CD",nLeftrightarrow:"\u21CE",nLl:"\u22D8\u0338",nLt:"\u226A\u20D2",nLtv:"\u226A\u0338",nRightarrow:"\u21CF",nVDash:"\u22AF",nVdash:"\u22AE",nabla:"\u2207",nacute:"\u0144",nang:"\u2220\u20D2",nap:"\u2249",napE:"\u2A70\u0338",napid:"\u224B\u0338",napos:"\u0149",napprox:"\u2249",natur:"\u266E",natural:"\u266E",naturals:"\u2115",nbsp:"\xA0",nbump:"\u224E\u0338",nbumpe:"\u224F\u0338",ncap:"\u2A43",ncaron:"\u0148",ncedil:"\u0146",ncong:"\u2247",ncongdot:"\u2A6D\u0338",ncup:"\u2A42",ncy:"\u043D",ndash:"\u2013",ne:"\u2260",neArr:"\u21D7",nearhk:"\u2924",nearr:"\u2197",nearrow:"\u2197",nedot:"\u2250\u0338",nequiv:"\u2262",nesear:"\u2928",nesim:"\u2242\u0338",nexist:"\u2204",nexists:"\u2204",nfr:"\u{1D52B}",ngE:"\u2267\u0338",nge:"\u2271",ngeq:"\u2271",ngeqq:"\u2267\u0338",ngeqslant:"\u2A7E\u0338",nges:"\u2A7E\u0338",ngsim:"\u2275",ngt:"\u226F",ngtr:"\u226F",nhArr:"\u21CE",nharr:"\u21AE",nhpar:"\u2AF2",ni:"\u220B",nis:"\u22FC",nisd:"\u22FA",niv:"\u220B",njcy:"\u045A",nlArr:"\u21CD",nlE:"\u2266\u0338",nlarr:"\u219A",nldr:"\u2025",nle:"\u2270",nleftarrow:"\u219A",nleftrightarrow:"\u21AE",nleq:"\u2270",nleqq:"\u2266\u0338",nleqslant:"\u2A7D\u0338",nles:"\u2A7D\u0338",nless:"\u226E",nlsim:"\u2274",nlt:"\u226E",nltri:"\u22EA",nltrie:"\u22EC",nmid:"\u2224",nopf:"\u{1D55F}",not:"\xAC",notin:"\u2209",notinE:"\u22F9\u0338",notindot:"\u22F5\u0338",notinva:"\u2209",notinvb:"\u22F7",notinvc:"\u22F6",notni:"\u220C",notniva:"\u220C",notnivb:"\u22FE",notnivc:"\u22FD",npar:"\u2226",nparallel:"\u2226",nparsl:"\u2AFD\u20E5",npart:"\u2202\u0338",npolint:"\u2A14",npr:"\u2280",nprcue:"\u22E0",npre:"\u2AAF\u0338",nprec:"\u2280",npreceq:"\u2AAF\u0338",nrArr:"\u21CF",nrarr:"\u219B",nrarrc:"\u2933\u0338",nrarrw:"\u219D\u0338",nrightarrow:"\u219B",nrtri:"\u22EB",nrtrie:"\u22ED",nsc:"\u2281",nsccue:"\u22E1",nsce:"\u2AB0\u0338",nscr:"\u{1D4C3}",nshortmid:"\u2224",nshortparallel:"\u2226",nsim:"\u2241",nsime:"\u2244",nsimeq:"\u2244",nsmid:"\u2224",nspar:"\u2226",nsqsube:"\u22E2",nsqsupe:"\u22E3",nsub:"\u2284",nsubE:"\u2AC5\u0338",nsube:"\u2288",nsubset:"\u2282\u20D2",nsubseteq:"\u2288",nsubseteqq:"\u2AC5\u0338",nsucc:"\u2281",nsucceq:"\u2AB0\u0338",nsup:"\u2285",nsupE:"\u2AC6\u0338",nsupe:"\u2289",nsupset:"\u2283\u20D2",nsupseteq:"\u2289",nsupseteqq:"\u2AC6\u0338",ntgl:"\u2279",ntilde:"\xF1",ntlg:"\u2278",ntriangleleft:"\u22EA",ntrianglelefteq:"\u22EC",ntriangleright:"\u22EB",ntrianglerighteq:"\u22ED",nu:"\u03BD",num:"#",numero:"\u2116",numsp:"\u2007",nvDash:"\u22AD",nvHarr:"\u2904",nvap:"\u224D\u20D2",nvdash:"\u22AC",nvge:"\u2265\u20D2",nvgt:">\u20D2",nvinfin:"\u29DE",nvlArr:"\u2902",nvle:"\u2264\u20D2",nvlt:"<\u20D2",nvltrie:"\u22B4\u20D2",nvrArr:"\u2903",nvrtrie:"\u22B5\u20D2",nvsim:"\u223C\u20D2",nwArr:"\u21D6",nwarhk:"\u2923",nwarr:"\u2196",nwarrow:"\u2196",nwnear:"\u2927",oS:"\u24C8",oacute:"\xF3",oast:"\u229B",ocir:"\u229A",ocirc:"\xF4",ocy:"\u043E",odash:"\u229D",odblac:"\u0151",odiv:"\u2A38",odot:"\u2299",odsold:"\u29BC",oelig:"\u0153",ofcir:"\u29BF",ofr:"\u{1D52C}",ogon:"\u02DB",ograve:"\xF2",ogt:"\u29C1",ohbar:"\u29B5",ohm:"\u03A9",oint:"\u222E",olarr:"\u21BA",olcir:"\u29BE",olcross:"\u29BB",oline:"\u203E",olt:"\u29C0",omacr:"\u014D",omega:"\u03C9",omicron:"\u03BF",omid:"\u29B6",ominus:"\u2296",oopf:"\u{1D560}",opar:"\u29B7",operp:"\u29B9",oplus:"\u2295",or:"\u2228",orarr:"\u21BB",ord:"\u2A5D",order:"\u2134",orderof:"\u2134",ordf:"\xAA",ordm:"\xBA",origof:"\u22B6",oror:"\u2A56",orslope:"\u2A57",orv:"\u2A5B",oscr:"\u2134",oslash:"\xF8",osol:"\u2298",otilde:"\xF5",otimes:"\u2297",otimesas:"\u2A36",ouml:"\xF6",ovbar:"\u233D",par:"\u2225",para:"\xB6",parallel:"\u2225",parsim:"\u2AF3",parsl:"\u2AFD",part:"\u2202",pcy:"\u043F",percnt:"%",period:".",permil:"\u2030",perp:"\u22A5",pertenk:"\u2031",pfr:"\u{1D52D}",phi:"\u03C6",phiv:"\u03D5",phmmat:"\u2133",phone:"\u260E",pi:"\u03C0",pitchfork:"\u22D4",piv:"\u03D6",planck:"\u210F",planckh:"\u210E",plankv:"\u210F",plus:"+",plusacir:"\u2A23",plusb:"\u229E",pluscir:"\u2A22",plusdo:"\u2214",plusdu:"\u2A25",pluse:"\u2A72",plusmn:"\xB1",plussim:"\u2A26",plustwo:"\u2A27",pm:"\xB1",pointint:"\u2A15",popf:"\u{1D561}",pound:"\xA3",pr:"\u227A",prE:"\u2AB3",prap:"\u2AB7",prcue:"\u227C",pre:"\u2AAF",prec:"\u227A",precapprox:"\u2AB7",preccurlyeq:"\u227C",preceq:"\u2AAF",precnapprox:"\u2AB9",precneqq:"\u2AB5",precnsim:"\u22E8",precsim:"\u227E",prime:"\u2032",primes:"\u2119",prnE:"\u2AB5",prnap:"\u2AB9",prnsim:"\u22E8",prod:"\u220F",profalar:"\u232E",profline:"\u2312",profsurf:"\u2313",prop:"\u221D",propto:"\u221D",prsim:"\u227E",prurel:"\u22B0",pscr:"\u{1D4C5}",psi:"\u03C8",puncsp:"\u2008",qfr:"\u{1D52E}",qint:"\u2A0C",qopf:"\u{1D562}",qprime:"\u2057",qscr:"\u{1D4C6}",quaternions:"\u210D",quatint:"\u2A16",quest:"?",questeq:"\u225F",quot:'"',rAarr:"\u21DB",rArr:"\u21D2",rAtail:"\u291C",rBarr:"\u290F",rHar:"\u2964",race:"\u223D\u0331",racute:"\u0155",radic:"\u221A",raemptyv:"\u29B3",rang:"\u27E9",rangd:"\u2992",range:"\u29A5",rangle:"\u27E9",raquo:"\xBB",rarr:"\u2192",rarrap:"\u2975",rarrb:"\u21E5",rarrbfs:"\u2920",rarrc:"\u2933",rarrfs:"\u291E",rarrhk:"\u21AA",rarrlp:"\u21AC",rarrpl:"\u2945",rarrsim:"\u2974",rarrtl:"\u21A3",rarrw:"\u219D",ratail:"\u291A",ratio:"\u2236",rationals:"\u211A",rbarr:"\u290D",rbbrk:"\u2773",rbrace:"}",rbrack:"]",rbrke:"\u298C",rbrksld:"\u298E",rbrkslu:"\u2990",rcaron:"\u0159",rcedil:"\u0157",rceil:"\u2309",rcub:"}",rcy:"\u0440",rdca:"\u2937",rdldhar:"\u2969",rdquo:"\u201D",rdquor:"\u201D",rdsh:"\u21B3",real:"\u211C",realine:"\u211B",realpart:"\u211C",reals:"\u211D",rect:"\u25AD",reg:"\xAE",rfisht:"\u297D",rfloor:"\u230B",rfr:"\u{1D52F}",rhard:"\u21C1",rharu:"\u21C0",rharul:"\u296C",rho:"\u03C1",rhov:"\u03F1",rightarrow:"\u2192",rightarrowtail:"\u21A3",rightharpoondown:"\u21C1",rightharpoonup:"\u21C0",rightleftarrows:"\u21C4",rightleftharpoons:"\u21CC",rightrightarrows:"\u21C9",rightsquigarrow:"\u219D",rightthreetimes:"\u22CC",ring:"\u02DA",risingdotseq:"\u2253",rlarr:"\u21C4",rlhar:"\u21CC",rlm:"\u200F",rmoust:"\u23B1",rmoustache:"\u23B1",rnmid:"\u2AEE",roang:"\u27ED",roarr:"\u21FE",robrk:"\u27E7",ropar:"\u2986",ropf:"\u{1D563}",roplus:"\u2A2E",rotimes:"\u2A35",rpar:")",rpargt:"\u2994",rppolint:"\u2A12",rrarr:"\u21C9",rsaquo:"\u203A",rscr:"\u{1D4C7}",rsh:"\u21B1",rsqb:"]",rsquo:"\u2019",rsquor:"\u2019",rthree:"\u22CC",rtimes:"\u22CA",rtri:"\u25B9",rtrie:"\u22B5",rtrif:"\u25B8",rtriltri:"\u29CE",ruluhar:"\u2968",rx:"\u211E",sacute:"\u015B",sbquo:"\u201A",sc:"\u227B",scE:"\u2AB4",scap:"\u2AB8",scaron:"\u0161",sccue:"\u227D",sce:"\u2AB0",scedil:"\u015F",scirc:"\u015D",scnE:"\u2AB6",scnap:"\u2ABA",scnsim:"\u22E9",scpolint:"\u2A13",scsim:"\u227F",scy:"\u0441",sdot:"\u22C5",sdotb:"\u22A1",sdote:"\u2A66",seArr:"\u21D8",searhk:"\u2925",searr:"\u2198",searrow:"\u2198",sect:"\xA7",semi:";",seswar:"\u2929",setminus:"\u2216",setmn:"\u2216",sext:"\u2736",sfr:"\u{1D530}",sfrown:"\u2322",sharp:"\u266F",shchcy:"\u0449",shcy:"\u0448",shortmid:"\u2223",shortparallel:"\u2225",shy:"\xAD",sigma:"\u03C3",sigmaf:"\u03C2",sigmav:"\u03C2",sim:"\u223C",simdot:"\u2A6A",sime:"\u2243",simeq:"\u2243",simg:"\u2A9E",simgE:"\u2AA0",siml:"\u2A9D",simlE:"\u2A9F",simne:"\u2246",simplus:"\u2A24",simrarr:"\u2972",slarr:"\u2190",smallsetminus:"\u2216",smashp:"\u2A33",smeparsl:"\u29E4",smid:"\u2223",smile:"\u2323",smt:"\u2AAA",smte:"\u2AAC",smtes:"\u2AAC\uFE00",softcy:"\u044C",sol:"/",solb:"\u29C4",solbar:"\u233F",sopf:"\u{1D564}",spades:"\u2660",spadesuit:"\u2660",spar:"\u2225",sqcap:"\u2293",sqcaps:"\u2293\uFE00",sqcup:"\u2294",sqcups:"\u2294\uFE00",sqsub:"\u228F",sqsube:"\u2291",sqsubset:"\u228F",sqsubseteq:"\u2291",sqsup:"\u2290",sqsupe:"\u2292",sqsupset:"\u2290",sqsupseteq:"\u2292",squ:"\u25A1",square:"\u25A1",squarf:"\u25AA",squf:"\u25AA",srarr:"\u2192",sscr:"\u{1D4C8}",ssetmn:"\u2216",ssmile:"\u2323",sstarf:"\u22C6",star:"\u2606",starf:"\u2605",straightepsilon:"\u03F5",straightphi:"\u03D5",strns:"\xAF",sub:"\u2282",subE:"\u2AC5",subdot:"\u2ABD",sube:"\u2286",subedot:"\u2AC3",submult:"\u2AC1",subnE:"\u2ACB",subne:"\u228A",subplus:"\u2ABF",subrarr:"\u2979",subset:"\u2282",subseteq:"\u2286",subseteqq:"\u2AC5",subsetneq:"\u228A",subsetneqq:"\u2ACB",subsim:"\u2AC7",subsub:"\u2AD5",subsup:"\u2AD3",succ:"\u227B",succapprox:"\u2AB8",succcurlyeq:"\u227D",succeq:"\u2AB0",succnapprox:"\u2ABA",succneqq:"\u2AB6",succnsim:"\u22E9",succsim:"\u227F",sum:"\u2211",sung:"\u266A",sup1:"\xB9",sup2:"\xB2",sup3:"\xB3",sup:"\u2283",supE:"\u2AC6",supdot:"\u2ABE",supdsub:"\u2AD8",supe:"\u2287",supedot:"\u2AC4",suphsol:"\u27C9",suphsub:"\u2AD7",suplarr:"\u297B",supmult:"\u2AC2",supnE:"\u2ACC",supne:"\u228B",supplus:"\u2AC0",supset:"\u2283",supseteq:"\u2287",supseteqq:"\u2AC6",supsetneq:"\u228B",supsetneqq:"\u2ACC",supsim:"\u2AC8",supsub:"\u2AD4",supsup:"\u2AD6",swArr:"\u21D9",swarhk:"\u2926",swarr:"\u2199",swarrow:"\u2199",swnwar:"\u292A",szlig:"\xDF",target:"\u2316",tau:"\u03C4",tbrk:"\u23B4",tcaron:"\u0165",tcedil:"\u0163",tcy:"\u0442",tdot:"\u20DB",telrec:"\u2315",tfr:"\u{1D531}",there4:"\u2234",therefore:"\u2234",theta:"\u03B8",thetasym:"\u03D1",thetav:"\u03D1",thickapprox:"\u2248",thicksim:"\u223C",thinsp:"\u2009",thkap:"\u2248",thksim:"\u223C",thorn:"\xFE",tilde:"\u02DC",times:"\xD7",timesb:"\u22A0",timesbar:"\u2A31",timesd:"\u2A30",tint:"\u222D",toea:"\u2928",top:"\u22A4",topbot:"\u2336",topcir:"\u2AF1",topf:"\u{1D565}",topfork:"\u2ADA",tosa:"\u2929",tprime:"\u2034",trade:"\u2122",triangle:"\u25B5",triangledown:"\u25BF",triangleleft:"\u25C3",trianglelefteq:"\u22B4",triangleq:"\u225C",triangleright:"\u25B9",trianglerighteq:"\u22B5",tridot:"\u25EC",trie:"\u225C",triminus:"\u2A3A",triplus:"\u2A39",trisb:"\u29CD",tritime:"\u2A3B",trpezium:"\u23E2",tscr:"\u{1D4C9}",tscy:"\u0446",tshcy:"\u045B",tstrok:"\u0167",twixt:"\u226C",twoheadleftarrow:"\u219E",twoheadrightarrow:"\u21A0",uArr:"\u21D1",uHar:"\u2963",uacute:"\xFA",uarr:"\u2191",ubrcy:"\u045E",ubreve:"\u016D",ucirc:"\xFB",ucy:"\u0443",udarr:"\u21C5",udblac:"\u0171",udhar:"\u296E",ufisht:"\u297E",ufr:"\u{1D532}",ugrave:"\xF9",uharl:"\u21BF",uharr:"\u21BE",uhblk:"\u2580",ulcorn:"\u231C",ulcorner:"\u231C",ulcrop:"\u230F",ultri:"\u25F8",umacr:"\u016B",uml:"\xA8",uogon:"\u0173",uopf:"\u{1D566}",uparrow:"\u2191",updownarrow:"\u2195",upharpoonleft:"\u21BF",upharpoonright:"\u21BE",uplus:"\u228E",upsi:"\u03C5",upsih:"\u03D2",upsilon:"\u03C5",upuparrows:"\u21C8",urcorn:"\u231D",urcorner:"\u231D",urcrop:"\u230E",uring:"\u016F",urtri:"\u25F9",uscr:"\u{1D4CA}",utdot:"\u22F0",utilde:"\u0169",utri:"\u25B5",utrif:"\u25B4",uuarr:"\u21C8",uuml:"\xFC",uwangle:"\u29A7",vArr:"\u21D5",vBar:"\u2AE8",vBarv:"\u2AE9",vDash:"\u22A8",vangrt:"\u299C",varepsilon:"\u03F5",varkappa:"\u03F0",varnothing:"\u2205",varphi:"\u03D5",varpi:"\u03D6",varpropto:"\u221D",varr:"\u2195",varrho:"\u03F1",varsigma:"\u03C2",varsubsetneq:"\u228A\uFE00",varsubsetneqq:"\u2ACB\uFE00",varsupsetneq:"\u228B\uFE00",varsupsetneqq:"\u2ACC\uFE00",vartheta:"\u03D1",vartriangleleft:"\u22B2",vartriangleright:"\u22B3",vcy:"\u0432",vdash:"\u22A2",vee:"\u2228",veebar:"\u22BB",veeeq:"\u225A",vellip:"\u22EE",verbar:"|",vert:"|",vfr:"\u{1D533}",vltri:"\u22B2",vnsub:"\u2282\u20D2",vnsup:"\u2283\u20D2",vopf:"\u{1D567}",vprop:"\u221D",vrtri:"\u22B3",vscr:"\u{1D4CB}",vsubnE:"\u2ACB\uFE00",vsubne:"\u228A\uFE00",vsupnE:"\u2ACC\uFE00",vsupne:"\u228B\uFE00",vzigzag:"\u299A",wcirc:"\u0175",wedbar:"\u2A5F",wedge:"\u2227",wedgeq:"\u2259",weierp:"\u2118",wfr:"\u{1D534}",wopf:"\u{1D568}",wp:"\u2118",wr:"\u2240",wreath:"\u2240",wscr:"\u{1D4CC}",xcap:"\u22C2",xcirc:"\u25EF",xcup:"\u22C3",xdtri:"\u25BD",xfr:"\u{1D535}",xhArr:"\u27FA",xharr:"\u27F7",xi:"\u03BE",xlArr:"\u27F8",xlarr:"\u27F5",xmap:"\u27FC",xnis:"\u22FB",xodot:"\u2A00",xopf:"\u{1D569}",xoplus:"\u2A01",xotime:"\u2A02",xrArr:"\u27F9",xrarr:"\u27F6",xscr:"\u{1D4CD}",xsqcup:"\u2A06",xuplus:"\u2A04",xutri:"\u25B3",xvee:"\u22C1",xwedge:"\u22C0",yacute:"\xFD",yacy:"\u044F",ycirc:"\u0177",ycy:"\u044B",yen:"\xA5",yfr:"\u{1D536}",yicy:"\u0457",yopf:"\u{1D56A}",yscr:"\u{1D4CE}",yucy:"\u044E",yuml:"\xFF",zacute:"\u017A",zcaron:"\u017E",zcy:"\u0437",zdot:"\u017C",zeetrf:"\u2128",zeta:"\u03B6",zfr:"\u{1D537}",zhcy:"\u0436",zigrarr:"\u21DD",zopf:"\u{1D56B}",zscr:"\u{1D4CF}",zwj:"\u200D",zwnj:"\u200C"}});function Ar(e){return aS.call(Do,e)?Do[e]:!1}var aS,_i=S(()=>{mf();aS={}.hasOwnProperty});function Pe(e,t,r,n){let a=e.length,s=0,l;if(t<0?t=-t>a?0:a+t:t=t>a?a:t,r=r>0?r:0,n.length<1e4)l=Array.from(n),l.unshift(t,r),e.splice(...l);else for(r&&e.splice(t,r);s<n.length;)l=n.slice(s,s+1e4),l.unshift(t,0),e.splice(...l),s+=1e4,t+=1e4}function Be(e,t){return e.length>0?(Pe(e,e.length,0,t),e):t}var Bt=S(()=>{});function yf(e){let t={},r=-1;for(;++r<e.length;)oS(t,e[r]);return t}function oS(e,t){let r;for(r in t){let a=(gf.call(e,r)?e[r]:void 0)||(e[r]={}),s=t[r],l;if(s)for(l in s){gf.call(a,l)||(a[l]=[]);let c=s[l];sS(a[l],Array.isArray(c)?c:c?[c]:[])}}}function sS(e,t){let r=-1,n=[];for(;++r<t.length;)(t[r].add==="after"?e:n).push(t[r]);Pe(e,0,0,n)}var gf,wf=S(()=>{Bt();gf={}.hasOwnProperty});function ki(e,t){let r=Number.parseInt(e,t);return r<9||r===11||r>13&&r<32||r>126&&r<160||r>55295&&r<57344||r>64975&&r<65008||(r&65535)===65535||(r&65535)===65534||r>1114111?"\uFFFD":String.fromCodePoint(r)}var Lo=S(()=>{});function Tt(e){return e.replace(/[\t\n\r ]+/g," ").replace(/^ | $/g,"").toLowerCase().toUpperCase()}var vi=S(()=>{});function fn(e){return e!==null&&(e<32||e===127)}function j(e){return e!==null&&e<-2}function ye(e){return e!==null&&(e<0||e===32)}function Q(e){return e===-2||e===-1||e===32}function Yt(e){return t;function t(r){return r!==null&&r>-1&&e.test(String.fromCharCode(r))}}var Ge,Ye,bf,dn,_f,kf,vf,xf,ce=S(()=>{Ge=Yt(/[A-Za-z]/),Ye=Yt(/[\dA-Za-z]/),bf=Yt(/[#-'*+\--9=?A-Z^-~]/);dn=Yt(/\d/),_f=Yt(/[\dA-Fa-f]/),kf=Yt(/[!-/:-@[-`{-~]/);vf=Yt(/\p{P}|\p{S}/u),xf=Yt(/\s/)});function X(e,t,r,n){let a=n?n-1:Number.POSITIVE_INFINITY,s=0;return l;function l(d){return Q(d)?(e.enter(r),c(d)):t(d)}function c(d){return Q(d)&&s++<a?(e.consume(d),c):(e.exit(r),t(d))}}var xe=S(()=>{ce()});function lS(e){let t=e.attempt(this.parser.constructs.contentInitial,n,a),r;return t;function n(c){if(c===null){e.consume(c);return}return e.enter("lineEnding"),e.consume(c),e.exit("lineEnding"),X(e,t,"linePrefix")}function a(c){return e.enter("paragraph"),s(c)}function s(c){let d=e.enter("chunkText",{contentType:"text",previous:r});return r&&(r.next=d),r=d,l(c)}function l(c){if(c===null){e.exit("chunkText"),e.exit("paragraph"),e.consume(c);return}return j(c)?(e.consume(c),e.exit("chunkText"),s):(e.consume(c),l)}}var Sf,Cf=S(()=>{xe();ce();Sf={tokenize:lS}});function uS(e){let t=this,r=[],n=0,a,s,l;return c;function c(A){if(n<r.length){let Z=r[n];return t.containerState=Z[1],e.attempt(Z[0].continuation,d,p)(A)}return p(A)}function d(A){if(n++,t.containerState._closeFlow){t.containerState._closeFlow=void 0,a&&z();let Z=t.events.length,W=Z,P;for(;W--;)if(t.events[W][0]==="exit"&&t.events[W][1].type==="chunkFlow"){P=t.events[W][1].end;break}O(n);let G=Z;for(;G<t.events.length;)t.events[G][1].end={...P},G++;return Pe(t.events,W+1,0,t.events.slice(Z)),t.events.length=G,p(A)}return c(A)}function p(A){if(n===r.length){if(!a)return y(A);if(a.currentConstruct&&a.currentConstruct.concrete)return v(A);t.interrupt=!!(a.currentConstruct&&!a._gfmTableDynamicInterruptHack)}return t.containerState={},e.check(Pf,h,m)(A)}function h(A){return a&&z(),O(n),y(A)}function m(A){return t.parser.lazy[t.now().line]=n!==r.length,l=t.now().offset,v(A)}function y(A){return t.containerState={},e.attempt(Pf,w,v)(A)}function w(A){return n++,r.push([t.currentConstruct,t.containerState]),y(A)}function v(A){if(A===null){a&&z(),O(0),e.consume(A);return}return a=a||t.parser.flow(t.now()),e.enter("chunkFlow",{_tokenizer:a,contentType:"flow",previous:s}),x(A)}function x(A){if(A===null){E(e.exit("chunkFlow"),!0),O(0),e.consume(A);return}return j(A)?(e.consume(A),E(e.exit("chunkFlow")),n=0,t.interrupt=void 0,c):(e.consume(A),x)}function E(A,Z){let W=t.sliceStream(A);if(Z&&W.push(null),A.previous=s,s&&(s.next=A),s=A,a.defineSkip(A.start),a.write(W),t.parser.lazy[A.start.line]){let P=a.events.length;for(;P--;)if(a.events[P][1].start.offset<l&&(!a.events[P][1].end||a.events[P][1].end.offset>l))return;let G=t.events.length,B=G,U,L;for(;B--;)if(t.events[B][0]==="exit"&&t.events[B][1].type==="chunkFlow"){if(U){L=t.events[B][1].end;break}U=!0}for(O(n),P=G;P<t.events.length;)t.events[P][1].end={...L},P++;Pe(t.events,B+1,0,t.events.slice(G)),t.events.length=P}}function O(A){let Z=r.length;for(;Z-- >A;){let W=r[Z];t.containerState=W[1],W[0].exit.call(t,e)}r.length=A}function z(){a.write([null]),s=void 0,a=void 0,t.containerState._closeFlow=void 0}}function cS(e,t,r){return X(e,e.attempt(this.parser.constructs.document,t,r),"linePrefix",this.parser.constructs.disable.null.includes("codeIndented")?void 0:4)}var Of,Pf,Ff=S(()=>{xe();ce();Bt();Of={tokenize:uS},Pf={tokenize:cS}});function Tr(e){if(e===null||ye(e)||xf(e))return 1;if(vf(e))return 2}var Mo=S(()=>{ce()});function Dr(e,t,r){let n=[],a=-1;for(;++a<e.length;){let s=e[a].resolveAll;s&&!n.includes(s)&&(t=s(t,r),n.push(s))}return t}var xi=S(()=>{});function fS(e,t){let r=-1,n,a,s,l,c,d,p,h;for(;++r<e.length;)if(e[r][0]==="enter"&&e[r][1].type==="attentionSequence"&&e[r][1]._close){for(n=r;n--;)if(e[n][0]==="exit"&&e[n][1].type==="attentionSequence"&&e[n][1]._open&&t.sliceSerialize(e[n][1]).charCodeAt(0)===t.sliceSerialize(e[r][1]).charCodeAt(0)){if((e[n][1]._close||e[r][1]._open)&&(e[r][1].end.offset-e[r][1].start.offset)%3&&!((e[n][1].end.offset-e[n][1].start.offset+e[r][1].end.offset-e[r][1].start.offset)%3))continue;d=e[n][1].end.offset-e[n][1].start.offset>1&&e[r][1].end.offset-e[r][1].start.offset>1?2:1;let m={...e[n][1].end},y={...e[r][1].start};Ef(m,-d),Ef(y,d),l={type:d>1?"strongSequence":"emphasisSequence",start:m,end:{...e[n][1].end}},c={type:d>1?"strongSequence":"emphasisSequence",start:{...e[r][1].start},end:y},s={type:d>1?"strongText":"emphasisText",start:{...e[n][1].end},end:{...e[r][1].start}},a={type:d>1?"strong":"emphasis",start:{...l.start},end:{...c.end}},e[n][1].end={...l.start},e[r][1].start={...c.end},p=[],e[n][1].end.offset-e[n][1].start.offset&&(p=Be(p,[["enter",e[n][1],t],["exit",e[n][1],t]])),p=Be(p,[["enter",a,t],["enter",l,t],["exit",l,t],["enter",s,t]]),p=Be(p,Dr(t.parser.constructs.insideSpan.null,e.slice(n+1,r),t)),p=Be(p,[["exit",s,t],["enter",c,t],["exit",c,t],["exit",a,t]]),e[r][1].end.offset-e[r][1].start.offset?(h=2,p=Be(p,[["enter",e[r][1],t],["exit",e[r][1],t]])):h=0,Pe(e,n-1,r-n+3,p),r=n+p.length-h-2;break}}for(r=-1;++r<e.length;)e[r][1].type==="attentionSequence"&&(e[r][1].type="data");return e}function dS(e,t){let r=this.parser.constructs.attentionMarkers.null,n=this.previous,a=Tr(n),s;return l;function l(d){return s=d,e.enter("attentionSequence"),c(d)}function c(d){if(d===s)return e.consume(d),c;let p=e.exit("attentionSequence"),h=Tr(d),m=!h||h===2&&a||r.includes(d),y=!a||a===2&&h||r.includes(n);return p._open=!!(s===42?m:m&&(a||!y)),p._close=!!(s===42?y:y&&(h||!m)),t(d)}}function Ef(e,t){e.column+=t,e.offset+=t,e._bufferIndex+=t}var hn,Af=S(()=>{Bt();Mo();xi();hn={name:"attention",resolveAll:fS,tokenize:dS}});function hS(e,t,r){let n=0;return a;function a(w){return e.enter("autolink"),e.enter("autolinkMarker"),e.consume(w),e.exit("autolinkMarker"),e.enter("autolinkProtocol"),s}function s(w){return Ge(w)?(e.consume(w),l):w===64?r(w):p(w)}function l(w){return w===43||w===45||w===46||Ye(w)?(n=1,c(w)):p(w)}function c(w){return w===58?(e.consume(w),n=0,d):(w===43||w===45||w===46||Ye(w))&&n++<32?(e.consume(w),c):(n=0,p(w))}function d(w){return w===62?(e.exit("autolinkProtocol"),e.enter("autolinkMarker"),e.consume(w),e.exit("autolinkMarker"),e.exit("autolink"),t):w===null||w===32||w===60||fn(w)?r(w):(e.consume(w),d)}function p(w){return w===64?(e.consume(w),h):bf(w)?(e.consume(w),p):r(w)}function h(w){return Ye(w)?m(w):r(w)}function m(w){return w===46?(e.consume(w),n=0,h):w===62?(e.exit("autolinkProtocol").type="autolinkEmail",e.enter("autolinkMarker"),e.consume(w),e.exit("autolinkMarker"),e.exit("autolink"),t):y(w)}function y(w){if((w===45||Ye(w))&&n++<63){let v=w===45?y:m;return e.consume(w),v}return r(w)}}var Io,Tf=S(()=>{ce();Io={name:"autolink",tokenize:hS}});function pS(e,t,r){return n;function n(s){return Q(s)?X(e,a,"linePrefix")(s):a(s)}function a(s){return s===null||j(s)?t(s):r(s)}}var Vt,Si=S(()=>{xe();ce();Vt={partial:!0,tokenize:pS}});function mS(e,t,r){let n=this;return a;function a(l){if(l===62){let c=n.containerState;return c.open||(e.enter("blockQuote",{_container:!0}),c.open=!0),e.enter("blockQuotePrefix"),e.enter("blockQuoteMarker"),e.consume(l),e.exit("blockQuoteMarker"),s}return r(l)}function s(l){return Q(l)?(e.enter("blockQuotePrefixWhitespace"),e.consume(l),e.exit("blockQuotePrefixWhitespace"),e.exit("blockQuotePrefix"),t):(e.exit("blockQuotePrefix"),t(l))}}function gS(e,t,r){let n=this;return a;function a(l){return Q(l)?X(e,s,"linePrefix",n.parser.constructs.disable.null.includes("codeIndented")?void 0:4)(l):s(l)}function s(l){return e.attempt(Ci,t,r)(l)}}function yS(e){e.exit("blockQuote")}var Ci,Df=S(()=>{xe();ce();Ci={continuation:{tokenize:gS},exit:yS,name:"blockQuote",tokenize:mS}});function wS(e,t,r){return n;function n(s){return e.enter("characterEscape"),e.enter("escapeMarker"),e.consume(s),e.exit("escapeMarker"),a}function a(s){return kf(s)?(e.enter("characterEscapeValue"),e.consume(s),e.exit("characterEscapeValue"),e.exit("characterEscape"),t):r(s)}}var Pi,Lf=S(()=>{ce();Pi={name:"characterEscape",tokenize:wS}});function bS(e,t,r){let n=this,a=0,s,l;return c;function c(m){return e.enter("characterReference"),e.enter("characterReferenceMarker"),e.consume(m),e.exit("characterReferenceMarker"),d}function d(m){return m===35?(e.enter("characterReferenceMarkerNumeric"),e.consume(m),e.exit("characterReferenceMarkerNumeric"),p):(e.enter("characterReferenceValue"),s=31,l=Ye,h(m))}function p(m){return m===88||m===120?(e.enter("characterReferenceMarkerHexadecimal"),e.consume(m),e.exit("characterReferenceMarkerHexadecimal"),e.enter("characterReferenceValue"),s=6,l=_f,h):(e.enter("characterReferenceValue"),s=7,l=dn,h(m))}function h(m){if(m===59&&a){let y=e.exit("characterReferenceValue");return l===Ye&&!Ar(n.sliceSerialize(y))?r(m):(e.enter("characterReferenceMarker"),e.consume(m),e.exit("characterReferenceMarker"),e.exit("characterReference"),t)}return l(m)&&a++<s?(e.consume(m),h):r(m)}}var Oi,Mf=S(()=>{_i();ce();Oi={name:"characterReference",tokenize:bS}});function _S(e,t,r){let n=this,a={partial:!0,tokenize:W},s=0,l=0,c;return d;function d(P){return p(P)}function p(P){let G=n.events[n.events.length-1];return s=G&&G[1].type==="linePrefix"?G[2].sliceSerialize(G[1],!0).length:0,c=P,e.enter("codeFenced"),e.enter("codeFencedFence"),e.enter("codeFencedFenceSequence"),h(P)}function h(P){return P===c?(l++,e.consume(P),h):l<3?r(P):(e.exit("codeFencedFenceSequence"),Q(P)?X(e,m,"whitespace")(P):m(P))}function m(P){return P===null||j(P)?(e.exit("codeFencedFence"),n.interrupt?t(P):e.check(If,x,Z)(P)):(e.enter("codeFencedFenceInfo"),e.enter("chunkString",{contentType:"string"}),y(P))}function y(P){return P===null||j(P)?(e.exit("chunkString"),e.exit("codeFencedFenceInfo"),m(P)):Q(P)?(e.exit("chunkString"),e.exit("codeFencedFenceInfo"),X(e,w,"whitespace")(P)):P===96&&P===c?r(P):(e.consume(P),y)}function w(P){return P===null||j(P)?m(P):(e.enter("codeFencedFenceMeta"),e.enter("chunkString",{contentType:"string"}),v(P))}function v(P){return P===null||j(P)?(e.exit("chunkString"),e.exit("codeFencedFenceMeta"),m(P)):P===96&&P===c?r(P):(e.consume(P),v)}function x(P){return e.attempt(a,Z,E)(P)}function E(P){return e.enter("lineEnding"),e.consume(P),e.exit("lineEnding"),O}function O(P){return s>0&&Q(P)?X(e,z,"linePrefix",s+1)(P):z(P)}function z(P){return P===null||j(P)?e.check(If,x,Z)(P):(e.enter("codeFlowValue"),A(P))}function A(P){return P===null||j(P)?(e.exit("codeFlowValue"),z(P)):(e.consume(P),A)}function Z(P){return e.exit("codeFenced"),t(P)}function W(P,G,B){let U=0;return L;function L(V){return P.enter("lineEnding"),P.consume(V),P.exit("lineEnding"),H}function H(V){return P.enter("codeFencedFence"),Q(V)?X(P,K,"linePrefix",n.parser.constructs.disable.null.includes("codeIndented")?void 0:4)(V):K(V)}function K(V){return V===c?(P.enter("codeFencedFenceSequence"),N(V)):B(V)}function N(V){return V===c?(U++,P.consume(V),N):U>=l?(P.exit("codeFencedFenceSequence"),Q(V)?X(P,Y,"whitespace")(V):Y(V)):B(V)}function Y(V){return V===null||j(V)?(P.exit("codeFencedFence"),G(V)):B(V)}}}function kS(e,t,r){let n=this;return a;function a(l){return l===null?r(l):(e.enter("lineEnding"),e.consume(l),e.exit("lineEnding"),s)}function s(l){return n.parser.lazy[n.now().line]?r(l):t(l)}}var If,Fi,Nf=S(()=>{xe();ce();If={partial:!0,tokenize:kS},Fi={concrete:!0,name:"codeFenced",tokenize:_S}});function xS(e,t,r){let n=this;return a;function a(p){return e.enter("codeIndented"),X(e,s,"linePrefix",5)(p)}function s(p){let h=n.events[n.events.length-1];return h&&h[1].type==="linePrefix"&&h[2].sliceSerialize(h[1],!0).length>=4?l(p):r(p)}function l(p){return p===null?d(p):j(p)?e.attempt(vS,l,d)(p):(e.enter("codeFlowValue"),c(p))}function c(p){return p===null||j(p)?(e.exit("codeFlowValue"),l(p)):(e.consume(p),c)}function d(p){return e.exit("codeIndented"),t(p)}}function SS(e,t,r){let n=this;return a;function a(l){return n.parser.lazy[n.now().line]?r(l):j(l)?(e.enter("lineEnding"),e.consume(l),e.exit("lineEnding"),a):X(e,s,"linePrefix",5)(l)}function s(l){let c=n.events[n.events.length-1];return c&&c[1].type==="linePrefix"&&c[2].sliceSerialize(c[1],!0).length>=4?t(l):j(l)?a(l):r(l)}}var pn,vS,Rf=S(()=>{xe();ce();pn={name:"codeIndented",tokenize:xS},vS={partial:!0,tokenize:SS}});function CS(e){let t=e.length-4,r=3,n,a;if((e[r][1].type==="lineEnding"||e[r][1].type==="space")&&(e[t][1].type==="lineEnding"||e[t][1].type==="space")){for(n=r;++n<t;)if(e[n][1].type==="codeTextData"){e[r][1].type="codeTextPadding",e[t][1].type="codeTextPadding",r+=2,t-=2;break}}for(n=r-1,t++;++n<=t;)a===void 0?n!==t&&e[n][1].type!=="lineEnding"&&(a=n):(n===t||e[n][1].type==="lineEnding")&&(e[a][1].type="codeTextData",n!==a+2&&(e[a][1].end=e[n-1][1].end,e.splice(a+2,n-a-2),t-=n-a-2,n=a+2),a=void 0);return e}function PS(e){return e!==96||this.events[this.events.length-1][1].type==="characterEscape"}function OS(e,t,r){let n=this,a=0,s,l;return c;function c(y){return e.enter("codeText"),e.enter("codeTextSequence"),d(y)}function d(y){return y===96?(e.consume(y),a++,d):(e.exit("codeTextSequence"),p(y))}function p(y){return y===null?r(y):y===32?(e.enter("space"),e.consume(y),e.exit("space"),p):y===96?(l=e.enter("codeTextSequence"),s=0,m(y)):j(y)?(e.enter("lineEnding"),e.consume(y),e.exit("lineEnding"),p):(e.enter("codeTextData"),h(y))}function h(y){return y===null||y===32||y===96||j(y)?(e.exit("codeTextData"),p(y)):(e.consume(y),h)}function m(y){return y===96?(e.consume(y),s++,m):s===a?(e.exit("codeTextSequence"),e.exit("codeText"),t(y)):(l.type="codeTextData",h(y))}}var No,qf=S(()=>{ce();No={name:"codeText",previous:PS,resolve:CS,tokenize:OS}});function mn(e,t){let r=0;if(t.length<1e4)e.push(...t);else for(;r<t.length;)e.push(...t.slice(r,r+1e4)),r+=1e4}var Ei,jf=S(()=>{Ei=class{constructor(t){this.left=t?[...t]:[],this.right=[]}get(t){if(t<0||t>=this.left.length+this.right.length)throw new RangeError("Cannot access index `"+t+"` in a splice buffer of size `"+(this.left.length+this.right.length)+"`");return t<this.left.length?this.left[t]:this.right[this.right.length-t+this.left.length-1]}get length(){return this.left.length+this.right.length}shift(){return this.setCursor(0),this.right.pop()}slice(t,r){let n=r??Number.POSITIVE_INFINITY;return n<this.left.length?this.left.slice(t,n):t>this.left.length?this.right.slice(this.right.length-n+this.left.length,this.right.length-t+this.left.length).reverse():this.left.slice(t).concat(this.right.slice(this.right.length-n+this.left.length).reverse())}splice(t,r,n){let a=r||0;this.setCursor(Math.trunc(t));let s=this.right.splice(this.right.length-a,Number.POSITIVE_INFINITY);return n&&mn(this.left,n),s.reverse()}pop(){return this.setCursor(Number.POSITIVE_INFINITY),this.left.pop()}push(t){this.setCursor(Number.POSITIVE_INFINITY),this.left.push(t)}pushMany(t){this.setCursor(Number.POSITIVE_INFINITY),mn(this.left,t)}unshift(t){this.setCursor(0),this.right.push(t)}unshiftMany(t){this.setCursor(0),mn(this.right,t.reverse())}setCursor(t){if(!(t===this.left.length||t>this.left.length&&this.right.length===0||t<0&&this.left.length===0))if(t<this.left.length){let r=this.left.splice(t,Number.POSITIVE_INFINITY);mn(this.right,r.reverse())}else{let r=this.right.splice(this.left.length+this.right.length-t,Number.POSITIVE_INFINITY);mn(this.left,r.reverse())}}}});function Ai(e){let t={},r=-1,n,a,s,l,c,d,p,h=new Ei(e);for(;++r<h.length;){for(;r in t;)r=t[r];if(n=h.get(r),r&&n[1].type==="chunkFlow"&&h.get(r-1)[1].type==="listItemPrefix"&&(d=n[1]._tokenizer.events,s=0,s<d.length&&d[s][1].type==="lineEndingBlank"&&(s+=2),s<d.length&&d[s][1].type==="content"))for(;++s<d.length&&d[s][1].type!=="content";)d[s][1].type==="chunkText"&&(d[s][1]._isInFirstContentOfListItem=!0,s++);if(n[0]==="enter")n[1].contentType&&(Object.assign(t,FS(h,r)),r=t[r],p=!0);else if(n[1]._container){for(s=r,a=void 0;s--&&(l=h.get(s),l[1].type==="lineEnding"||l[1].type==="lineEndingBlank");)l[0]==="enter"&&(a&&(h.get(a)[1].type="lineEndingBlank"),l[1].type="lineEnding",a=s);a&&(n[1].end={...h.get(a)[1].start},c=h.slice(a,r),c.unshift(n),h.splice(a,r-a+1,c))}}return Pe(e,0,Number.POSITIVE_INFINITY,h.slice(0)),!p}function FS(e,t){let r=e.get(t)[1],n=e.get(t)[2],a=t-1,s=[],l=r._tokenizer||n.parser[r.contentType](r.start),c=l.events,d=[],p={},h,m,y=-1,w=r,v=0,x=0,E=[x];for(;w;){for(;e.get(++a)[1]!==w;);s.push(a),w._tokenizer||(h=n.sliceStream(w),w.next||h.push(null),m&&l.defineSkip(w.start),w._isInFirstContentOfListItem&&(l._gfmTasklistFirstContentOfListItem=!0),l.write(h),w._isInFirstContentOfListItem&&(l._gfmTasklistFirstContentOfListItem=void 0)),m=w,w=w.next}for(w=r;++y<c.length;)c[y][0]==="exit"&&c[y-1][0]==="enter"&&c[y][1].type===c[y-1][1].type&&c[y][1].start.line!==c[y][1].end.line&&(x=y+1,E.push(x),w._tokenizer=void 0,w.previous=void 0,w=w.next);for(l.events=[],w?(w._tokenizer=void 0,w.previous=void 0):E.pop(),y=E.length;y--;){let O=c.slice(E[y],E[y+1]),z=s.pop();d.push([z,z+O.length-1]),e.splice(z,2,O)}for(d.reverse(),y=-1;++y<d.length;)p[v+d[y][0]]=v+d[y][1],v+=d[y][1]-d[y][0]-1;return p}var Ro=S(()=>{Bt();jf()});function AS(e){return Ai(e),e}function TS(e,t){let r;return n;function n(c){return e.enter("content"),r=e.enter("chunkContent",{contentType:"content"}),a(c)}function a(c){return c===null?s(c):j(c)?e.check(ES,l,s)(c):(e.consume(c),a)}function s(c){return e.exit("chunkContent"),e.exit("content"),t(c)}function l(c){return e.consume(c),e.exit("chunkContent"),r.next=e.enter("chunkContent",{contentType:"content",previous:r}),r=r.next,a}}function DS(e,t,r){let n=this;return a;function a(l){return e.exit("chunkContent"),e.enter("lineEnding"),e.consume(l),e.exit("lineEnding"),X(e,s,"linePrefix")}function s(l){if(l===null||j(l))return r(l);let c=n.events[n.events.length-1];return!n.parser.constructs.disable.null.includes("codeIndented")&&c&&c[1].type==="linePrefix"&&c[2].sliceSerialize(c[1],!0).length>=4?t(l):e.interrupt(n.parser.constructs.flow,r,t)(l)}}var qo,ES,Bf=S(()=>{xe();ce();Ro();qo={resolve:AS,tokenize:TS},ES={partial:!0,tokenize:DS}});function Ti(e,t,r,n,a,s,l,c,d){let p=d||Number.POSITIVE_INFINITY,h=0;return m;function m(O){return O===60?(e.enter(n),e.enter(a),e.enter(s),e.consume(O),e.exit(s),y):O===null||O===32||O===41||fn(O)?r(O):(e.enter(n),e.enter(l),e.enter(c),e.enter("chunkString",{contentType:"string"}),x(O))}function y(O){return O===62?(e.enter(s),e.consume(O),e.exit(s),e.exit(a),e.exit(n),t):(e.enter(c),e.enter("chunkString",{contentType:"string"}),w(O))}function w(O){return O===62?(e.exit("chunkString"),e.exit(c),y(O)):O===null||O===60||j(O)?r(O):(e.consume(O),O===92?v:w)}function v(O){return O===60||O===62||O===92?(e.consume(O),w):w(O)}function x(O){return!h&&(O===null||O===41||ye(O))?(e.exit("chunkString"),e.exit(c),e.exit(l),e.exit(n),t(O)):h<p&&O===40?(e.consume(O),h++,x):O===41?(e.consume(O),h--,x):O===null||O===32||O===40||fn(O)?r(O):(e.consume(O),O===92?E:x)}function E(O){return O===40||O===41||O===92?(e.consume(O),x):x(O)}}var jo=S(()=>{ce()});function Di(e,t,r,n,a,s){let l=this,c=0,d;return p;function p(w){return e.enter(n),e.enter(a),e.consume(w),e.exit(a),e.enter(s),h}function h(w){return c>999||w===null||w===91||w===93&&!d||w===94&&!c&&"_hiddenFootnoteSupport"in l.parser.constructs?r(w):w===93?(e.exit(s),e.enter(a),e.consume(w),e.exit(a),e.exit(n),t):j(w)?(e.enter("lineEnding"),e.consume(w),e.exit("lineEnding"),h):(e.enter("chunkString",{contentType:"string"}),m(w))}function m(w){return w===null||w===91||w===93||j(w)||c++>999?(e.exit("chunkString"),h(w)):(e.consume(w),d||(d=!Q(w)),w===92?y:m)}function y(w){return w===91||w===92||w===93?(e.consume(w),c++,m):m(w)}}var Bo=S(()=>{ce()});function Li(e,t,r,n,a,s){let l;return c;function c(y){return y===34||y===39||y===40?(e.enter(n),e.enter(a),e.consume(y),e.exit(a),l=y===40?41:y,d):r(y)}function d(y){return y===l?(e.enter(a),e.consume(y),e.exit(a),e.exit(n),t):(e.enter(s),p(y))}function p(y){return y===l?(e.exit(s),d(l)):y===null?r(y):j(y)?(e.enter("lineEnding"),e.consume(y),e.exit("lineEnding"),X(e,p,"linePrefix")):(e.enter("chunkString",{contentType:"string"}),h(y))}function h(y){return y===l||y===null||j(y)?(e.exit("chunkString"),p(y)):(e.consume(y),y===92?m:h)}function m(y){return y===l||y===92?(e.consume(y),h):h(y)}}var Yo=S(()=>{xe();ce()});function or(e,t){let r;return n;function n(a){return j(a)?(e.enter("lineEnding"),e.consume(a),e.exit("lineEnding"),r=!0,n):Q(a)?X(e,n,r?"linePrefix":"lineSuffix")(a):t(a)}}var Vo=S(()=>{xe();ce()});function MS(e,t,r){let n=this,a;return s;function s(w){return e.enter("definition"),l(w)}function l(w){return Di.call(n,e,c,r,"definitionLabel","definitionLabelMarker","definitionLabelString")(w)}function c(w){return a=Tt(n.sliceSerialize(n.events[n.events.length-1][1]).slice(1,-1)),w===58?(e.enter("definitionMarker"),e.consume(w),e.exit("definitionMarker"),d):r(w)}function d(w){return ye(w)?or(e,p)(w):p(w)}function p(w){return Ti(e,h,r,"definitionDestination","definitionDestinationLiteral","definitionDestinationLiteralMarker","definitionDestinationRaw","definitionDestinationString")(w)}function h(w){return e.attempt(LS,m,m)(w)}function m(w){return Q(w)?X(e,y,"whitespace")(w):y(w)}function y(w){return w===null||j(w)?(e.exit("definition"),n.parser.defined.push(a),t(w)):r(w)}}function IS(e,t,r){return n;function n(c){return ye(c)?or(e,a)(c):r(c)}function a(c){return Li(e,s,r,"definitionTitle","definitionTitleMarker","definitionTitleString")(c)}function s(c){return Q(c)?X(e,l,"whitespace")(c):l(c)}function l(c){return c===null||j(c)?t(c):r(c)}}var zo,LS,Yf=S(()=>{jo();Bo();xe();Yo();Vo();ce();vi();zo={name:"definition",tokenize:MS},LS={partial:!0,tokenize:IS}});function NS(e,t,r){return n;function n(s){return e.enter("hardBreakEscape"),e.consume(s),a}function a(s){return j(s)?(e.exit("hardBreakEscape"),t(s)):r(s)}}var Uo,Vf=S(()=>{ce();Uo={name:"hardBreakEscape",tokenize:NS}});function RS(e,t){let r=e.length-2,n=3,a,s;return e[n][1].type==="whitespace"&&(n+=2),r-2>n&&e[r][1].type==="whitespace"&&(r-=2),e[r][1].type==="atxHeadingSequence"&&(n===r-1||r-4>n&&e[r-2][1].type==="whitespace")&&(r-=n+1===r?2:4),r>n&&(a={type:"atxHeadingText",start:e[n][1].start,end:e[r][1].end},s={type:"chunkText",start:e[n][1].start,end:e[r][1].end,contentType:"text"},Pe(e,n,r-n+1,[["enter",a,t],["enter",s,t],["exit",s,t],["exit",a,t]])),e}function qS(e,t,r){let n=0;return a;function a(h){return e.enter("atxHeading"),s(h)}function s(h){return e.enter("atxHeadingSequence"),l(h)}function l(h){return h===35&&n++<6?(e.consume(h),l):h===null||ye(h)?(e.exit("atxHeadingSequence"),c(h)):r(h)}function c(h){return h===35?(e.enter("atxHeadingSequence"),d(h)):h===null||j(h)?(e.exit("atxHeading"),t(h)):Q(h)?X(e,c,"whitespace")(h):(e.enter("atxHeadingText"),p(h))}function d(h){return h===35?(e.consume(h),d):(e.exit("atxHeadingSequence"),c(h))}function p(h){return h===null||h===35||ye(h)?(e.exit("atxHeadingText"),c(h)):(e.consume(h),p)}}var Wo,zf=S(()=>{xe();ce();Bt();Wo={name:"headingAtx",resolve:RS,tokenize:qS}});var Uf,Ho,Wf=S(()=>{Uf=["address","article","aside","base","basefont","blockquote","body","caption","center","col","colgroup","dd","details","dialog","dir","div","dl","dt","fieldset","figcaption","figure","footer","form","frame","frameset","h1","h2","h3","h4","h5","h6","head","header","hr","html","iframe","legend","li","link","main","menu","menuitem","nav","noframes","ol","optgroup","option","p","param","search","section","summary","table","tbody","td","tfoot","th","thead","title","tr","track","ul"],Ho=["pre","script","style","textarea"]});function YS(e){let t=e.length;for(;t--&&!(e[t][0]==="enter"&&e[t][1].type==="htmlFlow"););return t>1&&e[t-2][1].type==="linePrefix"&&(e[t][1].start=e[t-2][1].start,e[t+1][1].start=e[t-2][1].start,e.splice(t-2,2)),e}function VS(e,t,r){let n=this,a,s,l,c,d;return p;function p(b){return h(b)}function h(b){return e.enter("htmlFlow"),e.enter("htmlFlowData"),e.consume(b),m}function m(b){return b===33?(e.consume(b),y):b===47?(e.consume(b),s=!0,x):b===63?(e.consume(b),a=3,n.interrupt?t:_):Ge(b)?(e.consume(b),l=String.fromCharCode(b),E):r(b)}function y(b){return b===45?(e.consume(b),a=2,w):b===91?(e.consume(b),a=5,c=0,v):Ge(b)?(e.consume(b),a=4,n.interrupt?t:_):r(b)}function w(b){return b===45?(e.consume(b),n.interrupt?t:_):r(b)}function v(b){let Me="CDATA[";return b===Me.charCodeAt(c++)?(e.consume(b),c===Me.length?n.interrupt?t:K:v):r(b)}function x(b){return Ge(b)?(e.consume(b),l=String.fromCharCode(b),E):r(b)}function E(b){if(b===null||b===47||b===62||ye(b)){let Me=b===47,wt=l.toLowerCase();return!Me&&!s&&Ho.includes(wt)?(a=1,n.interrupt?t(b):K(b)):Uf.includes(l.toLowerCase())?(a=6,Me?(e.consume(b),O):n.interrupt?t(b):K(b)):(a=7,n.interrupt&&!n.parser.lazy[n.now().line]?r(b):s?z(b):A(b))}return b===45||Ye(b)?(e.consume(b),l+=String.fromCharCode(b),E):r(b)}function O(b){return b===62?(e.consume(b),n.interrupt?t:K):r(b)}function z(b){return Q(b)?(e.consume(b),z):L(b)}function A(b){return b===47?(e.consume(b),L):b===58||b===95||Ge(b)?(e.consume(b),Z):Q(b)?(e.consume(b),A):L(b)}function Z(b){return b===45||b===46||b===58||b===95||Ye(b)?(e.consume(b),Z):W(b)}function W(b){return b===61?(e.consume(b),P):Q(b)?(e.consume(b),W):A(b)}function P(b){return b===null||b===60||b===61||b===62||b===96?r(b):b===34||b===39?(e.consume(b),d=b,G):Q(b)?(e.consume(b),P):B(b)}function G(b){return b===d?(e.consume(b),d=null,U):b===null||j(b)?r(b):(e.consume(b),G)}function B(b){return b===null||b===34||b===39||b===47||b===60||b===61||b===62||b===96||ye(b)?W(b):(e.consume(b),B)}function U(b){return b===47||b===62||Q(b)?A(b):r(b)}function L(b){return b===62?(e.consume(b),H):r(b)}function H(b){return b===null||j(b)?K(b):Q(b)?(e.consume(b),H):r(b)}function K(b){return b===45&&a===2?(e.consume(b),M):b===60&&a===1?(e.consume(b),re):b===62&&a===4?(e.consume(b),Oe):b===63&&a===3?(e.consume(b),_):b===93&&a===5?(e.consume(b),he):j(b)&&(a===6||a===7)?(e.exit("htmlFlowData"),e.check(jS,Fe,N)(b)):b===null||j(b)?(e.exit("htmlFlowData"),N(b)):(e.consume(b),K)}function N(b){return e.check(BS,Y,Fe)(b)}function Y(b){return e.enter("lineEnding"),e.consume(b),e.exit("lineEnding"),V}function V(b){return b===null||j(b)?N(b):(e.enter("htmlFlowData"),K(b))}function M(b){return b===45?(e.consume(b),_):K(b)}function re(b){return b===47?(e.consume(b),l="",se):K(b)}function se(b){if(b===62){let Me=l.toLowerCase();return Ho.includes(Me)?(e.consume(b),Oe):K(b)}return Ge(b)&&l.length<8?(e.consume(b),l+=String.fromCharCode(b),se):K(b)}function he(b){return b===93?(e.consume(b),_):K(b)}function _(b){return b===62?(e.consume(b),Oe):b===45&&a===2?(e.consume(b),_):K(b)}function Oe(b){return b===null||j(b)?(e.exit("htmlFlowData"),Fe(b)):(e.consume(b),Oe)}function Fe(b){return e.exit("htmlFlow"),t(b)}}function zS(e,t,r){let n=this;return a;function a(l){return j(l)?(e.enter("lineEnding"),e.consume(l),e.exit("lineEnding"),s):r(l)}function s(l){return n.parser.lazy[n.now().line]?r(l):t(l)}}function US(e,t,r){return n;function n(a){return e.enter("lineEnding"),e.consume(a),e.exit("lineEnding"),e.attempt(Vt,t,r)}}var Go,jS,BS,Hf=S(()=>{ce();Wf();Si();Go={concrete:!0,name:"htmlFlow",resolveTo:YS,tokenize:VS},jS={partial:!0,tokenize:US},BS={partial:!0,tokenize:zS}});function WS(e,t,r){let n=this,a,s,l;return c;function c(_){return e.enter("htmlText"),e.enter("htmlTextData"),e.consume(_),d}function d(_){return _===33?(e.consume(_),p):_===47?(e.consume(_),W):_===63?(e.consume(_),A):Ge(_)?(e.consume(_),B):r(_)}function p(_){return _===45?(e.consume(_),h):_===91?(e.consume(_),s=0,v):Ge(_)?(e.consume(_),z):r(_)}function h(_){return _===45?(e.consume(_),w):r(_)}function m(_){return _===null?r(_):_===45?(e.consume(_),y):j(_)?(l=m,re(_)):(e.consume(_),m)}function y(_){return _===45?(e.consume(_),w):m(_)}function w(_){return _===62?M(_):_===45?y(_):m(_)}function v(_){let Oe="CDATA[";return _===Oe.charCodeAt(s++)?(e.consume(_),s===Oe.length?x:v):r(_)}function x(_){return _===null?r(_):_===93?(e.consume(_),E):j(_)?(l=x,re(_)):(e.consume(_),x)}function E(_){return _===93?(e.consume(_),O):x(_)}function O(_){return _===62?M(_):_===93?(e.consume(_),O):x(_)}function z(_){return _===null||_===62?M(_):j(_)?(l=z,re(_)):(e.consume(_),z)}function A(_){return _===null?r(_):_===63?(e.consume(_),Z):j(_)?(l=A,re(_)):(e.consume(_),A)}function Z(_){return _===62?M(_):A(_)}function W(_){return Ge(_)?(e.consume(_),P):r(_)}function P(_){return _===45||Ye(_)?(e.consume(_),P):G(_)}function G(_){return j(_)?(l=G,re(_)):Q(_)?(e.consume(_),G):M(_)}function B(_){return _===45||Ye(_)?(e.consume(_),B):_===47||_===62||ye(_)?U(_):r(_)}function U(_){return _===47?(e.consume(_),M):_===58||_===95||Ge(_)?(e.consume(_),L):j(_)?(l=U,re(_)):Q(_)?(e.consume(_),U):M(_)}function L(_){return _===45||_===46||_===58||_===95||Ye(_)?(e.consume(_),L):H(_)}function H(_){return _===61?(e.consume(_),K):j(_)?(l=H,re(_)):Q(_)?(e.consume(_),H):U(_)}function K(_){return _===null||_===60||_===61||_===62||_===96?r(_):_===34||_===39?(e.consume(_),a=_,N):j(_)?(l=K,re(_)):Q(_)?(e.consume(_),K):(e.consume(_),Y)}function N(_){return _===a?(e.consume(_),a=void 0,V):_===null?r(_):j(_)?(l=N,re(_)):(e.consume(_),N)}function Y(_){return _===null||_===34||_===39||_===60||_===61||_===96?r(_):_===47||_===62||ye(_)?U(_):(e.consume(_),Y)}function V(_){return _===47||_===62||ye(_)?U(_):r(_)}function M(_){return _===62?(e.consume(_),e.exit("htmlTextData"),e.exit("htmlText"),t):r(_)}function re(_){return e.exit("htmlTextData"),e.enter("lineEnding"),e.consume(_),e.exit("lineEnding"),se}function se(_){return Q(_)?X(e,he,"linePrefix",n.parser.constructs.disable.null.includes("codeIndented")?void 0:4)(_):he(_)}function he(_){return e.enter("htmlTextData"),l(_)}}var $o,Gf=S(()=>{xe();ce();$o={name:"htmlText",tokenize:WS}});function JS(e){let t=-1,r=[];for(;++t<e.length;){let n=e[t][1];if(r.push(e[t]),n.type==="labelImage"||n.type==="labelLink"||n.type==="labelEnd"){let a=n.type==="labelImage"?4:2;n.type="data",t+=a}}return e.length!==r.length&&Pe(e,0,e.length,r),e}function QS(e,t){let r=e.length,n=0,a,s,l,c;for(;r--;)if(a=e[r][1],s){if(a.type==="link"||a.type==="labelLink"&&a._inactive)break;e[r][0]==="enter"&&a.type==="labelLink"&&(a._inactive=!0)}else if(l){if(e[r][0]==="enter"&&(a.type==="labelImage"||a.type==="labelLink")&&!a._balanced&&(s=r,a.type!=="labelLink")){n=2;break}}else a.type==="labelEnd"&&(l=r);let d={type:e[s][1].type==="labelLink"?"link":"image",start:{...e[s][1].start},end:{...e[e.length-1][1].end}},p={type:"label",start:{...e[s][1].start},end:{...e[l][1].end}},h={type:"labelText",start:{...e[s+n+2][1].end},end:{...e[l-2][1].start}};return c=[["enter",d,t],["enter",p,t]],c=Be(c,e.slice(s+1,s+n+3)),c=Be(c,[["enter",h,t]]),c=Be(c,Dr(t.parser.constructs.insideSpan.null,e.slice(s+n+4,l-3),t)),c=Be(c,[["exit",h,t],e[l-2],e[l-1],["exit",p,t]]),c=Be(c,e.slice(l+1)),c=Be(c,[["exit",d,t]]),Pe(e,s,e.length,c),e}function ZS(e,t,r){let n=this,a=n.events.length,s,l;for(;a--;)if((n.events[a][1].type==="labelImage"||n.events[a][1].type==="labelLink")&&!n.events[a][1]._balanced){s=n.events[a][1];break}return c;function c(y){return s?s._inactive?m(y):(l=n.parser.defined.includes(Tt(n.sliceSerialize({start:s.end,end:n.now()}))),e.enter("labelEnd"),e.enter("labelMarker"),e.consume(y),e.exit("labelMarker"),e.exit("labelEnd"),d):r(y)}function d(y){return y===40?e.attempt(HS,h,l?h:m)(y):y===91?e.attempt(GS,h,l?p:m)(y):l?h(y):m(y)}function p(y){return e.attempt($S,h,m)(y)}function h(y){return t(y)}function m(y){return s._balanced=!0,r(y)}}function KS(e,t,r){return n;function n(m){return e.enter("resource"),e.enter("resourceMarker"),e.consume(m),e.exit("resourceMarker"),a}function a(m){return ye(m)?or(e,s)(m):s(m)}function s(m){return m===41?h(m):Ti(e,l,c,"resourceDestination","resourceDestinationLiteral","resourceDestinationLiteralMarker","resourceDestinationRaw","resourceDestinationString",32)(m)}function l(m){return ye(m)?or(e,d)(m):h(m)}function c(m){return r(m)}function d(m){return m===34||m===39||m===40?Li(e,p,r,"resourceTitle","resourceTitleMarker","resourceTitleString")(m):h(m)}function p(m){return ye(m)?or(e,h)(m):h(m)}function h(m){return m===41?(e.enter("resourceMarker"),e.consume(m),e.exit("resourceMarker"),e.exit("resource"),t):r(m)}}function XS(e,t,r){let n=this;return a;function a(c){return Di.call(n,e,s,l,"reference","referenceMarker","referenceString")(c)}function s(c){return n.parser.defined.includes(Tt(n.sliceSerialize(n.events[n.events.length-1][1]).slice(1,-1)))?t(c):r(c)}function l(c){return r(c)}}function eC(e,t,r){return n;function n(s){return e.enter("reference"),e.enter("referenceMarker"),e.consume(s),e.exit("referenceMarker"),a}function a(s){return s===93?(e.enter("referenceMarker"),e.consume(s),e.exit("referenceMarker"),e.exit("reference"),t):r(s)}}var sr,HS,GS,$S,Mi=S(()=>{jo();Bo();Yo();Vo();ce();Bt();vi();xi();sr={name:"labelEnd",resolveAll:JS,resolveTo:QS,tokenize:ZS},HS={tokenize:KS},GS={tokenize:XS},$S={tokenize:eC}});function tC(e,t,r){let n=this;return a;function a(c){return e.enter("labelImage"),e.enter("labelImageMarker"),e.consume(c),e.exit("labelImageMarker"),s}function s(c){return c===91?(e.enter("labelMarker"),e.consume(c),e.exit("labelMarker"),e.exit("labelImage"),l):r(c)}function l(c){return c===94&&"_hiddenFootnoteSupport"in n.parser.constructs?r(c):t(c)}}var Jo,$f=S(()=>{Mi();Jo={name:"labelStartImage",resolveAll:sr.resolveAll,tokenize:tC}});function rC(e,t,r){let n=this;return a;function a(l){return e.enter("labelLink"),e.enter("labelMarker"),e.consume(l),e.exit("labelMarker"),e.exit("labelLink"),s}function s(l){return l===94&&"_hiddenFootnoteSupport"in n.parser.constructs?r(l):t(l)}}var Qo,Jf=S(()=>{Mi();Qo={name:"labelStartLink",resolveAll:sr.resolveAll,tokenize:rC}});function nC(e,t){return r;function r(n){return e.enter("lineEnding"),e.consume(n),e.exit("lineEnding"),X(e,t,"linePrefix")}}var gn,Qf=S(()=>{xe();gn={name:"lineEnding",tokenize:nC}});function iC(e,t,r){let n=0,a;return s;function s(p){return e.enter("thematicBreak"),l(p)}function l(p){return a=p,c(p)}function c(p){return p===a?(e.enter("thematicBreakSequence"),d(p)):n>=3&&(p===null||j(p))?(e.exit("thematicBreak"),t(p)):r(p)}function d(p){return p===a?(e.consume(p),n++,d):(e.exit("thematicBreakSequence"),Q(p)?X(e,c,"whitespace")(p):c(p))}}var lr,Zo=S(()=>{xe();ce();lr={name:"thematicBreak",tokenize:iC}});function sC(e,t,r){let n=this,a=n.events[n.events.length-1],s=a&&a[1].type==="linePrefix"?a[2].sliceSerialize(a[1],!0).length:0,l=0;return c;function c(w){let v=n.containerState.type||(w===42||w===43||w===45?"listUnordered":"listOrdered");if(v==="listUnordered"?!n.containerState.marker||w===n.containerState.marker:dn(w)){if(n.containerState.type||(n.containerState.type=v,e.enter(v,{_container:!0})),v==="listUnordered")return e.enter("listItemPrefix"),w===42||w===45?e.check(lr,r,p)(w):p(w);if(!n.interrupt||w===49)return e.enter("listItemPrefix"),e.enter("listItemValue"),d(w)}return r(w)}function d(w){return dn(w)&&++l<10?(e.consume(w),d):(!n.interrupt||l<2)&&(n.containerState.marker?w===n.containerState.marker:w===41||w===46)?(e.exit("listItemValue"),p(w)):r(w)}function p(w){return e.enter("listItemMarker"),e.consume(w),e.exit("listItemMarker"),n.containerState.marker=n.containerState.marker||w,e.check(Vt,n.interrupt?r:h,e.attempt(aC,y,m))}function h(w){return n.containerState.initialBlankLine=!0,s++,y(w)}function m(w){return Q(w)?(e.enter("listItemPrefixWhitespace"),e.consume(w),e.exit("listItemPrefixWhitespace"),y):r(w)}function y(w){return n.containerState.size=s+n.sliceSerialize(e.exit("listItemPrefix"),!0).length,t(w)}}function lC(e,t,r){let n=this;return n.containerState._closeFlow=void 0,e.check(Vt,a,s);function a(c){return n.containerState.furtherBlankLines=n.containerState.furtherBlankLines||n.containerState.initialBlankLine,X(e,t,"listItemIndent",n.containerState.size+1)(c)}function s(c){return n.containerState.furtherBlankLines||!Q(c)?(n.containerState.furtherBlankLines=void 0,n.containerState.initialBlankLine=void 0,l(c)):(n.containerState.furtherBlankLines=void 0,n.containerState.initialBlankLine=void 0,e.attempt(oC,t,l)(c))}function l(c){return n.containerState._closeFlow=!0,n.interrupt=void 0,X(e,e.attempt(De,t,r),"linePrefix",n.parser.constructs.disable.null.includes("codeIndented")?void 0:4)(c)}}function uC(e,t,r){let n=this;return X(e,a,"listItemIndent",n.containerState.size+1);function a(s){let l=n.events[n.events.length-1];return l&&l[1].type==="listItemIndent"&&l[2].sliceSerialize(l[1],!0).length===n.containerState.size?t(s):r(s)}}function cC(e){e.exit(this.containerState.type)}function fC(e,t,r){let n=this;return X(e,a,"listItemPrefixWhitespace",n.parser.constructs.disable.null.includes("codeIndented")?void 0:5);function a(s){let l=n.events[n.events.length-1];return!Q(s)&&l&&l[1].type==="listItemPrefixWhitespace"?t(s):r(s)}}var De,aC,oC,Zf=S(()=>{xe();ce();Si();Zo();De={continuation:{tokenize:lC},exit:cC,name:"list",tokenize:sC},aC={partial:!0,tokenize:fC},oC={partial:!0,tokenize:uC}});function dC(e,t){let r=e.length,n,a,s;for(;r--;)if(e[r][0]==="enter"){if(e[r][1].type==="content"){n=r;break}e[r][1].type==="paragraph"&&(a=r)}else e[r][1].type==="content"&&e.splice(r,1),!s&&e[r][1].type==="definition"&&(s=r);let l={type:"setextHeading",start:{...e[a][1].start},end:{...e[e.length-1][1].end}};return e[a][1].type="setextHeadingText",s?(e.splice(a,0,["enter",l,t]),e.splice(s+1,0,["exit",e[n][1],t]),e[n][1].end={...e[s][1].end}):e[n][1]=l,e.push(["exit",l,t]),e}function hC(e,t,r){let n=this,a;return s;function s(p){let h=n.events.length,m;for(;h--;)if(n.events[h][1].type!=="lineEnding"&&n.events[h][1].type!=="linePrefix"&&n.events[h][1].type!=="content"){m=n.events[h][1].type==="paragraph";break}return!n.parser.lazy[n.now().line]&&(n.interrupt||m)?(e.enter("setextHeadingLine"),a=p,l(p)):r(p)}function l(p){return e.enter("setextHeadingLineSequence"),c(p)}function c(p){return p===a?(e.consume(p),c):(e.exit("setextHeadingLineSequence"),Q(p)?X(e,d,"lineSuffix")(p):d(p))}function d(p){return p===null||j(p)?(e.exit("setextHeadingLine"),t(p)):r(p)}}var Ii,Kf=S(()=>{xe();ce();Ii={name:"setextUnderline",resolveTo:dC,tokenize:hC}});var Ko=S(()=>{Af();Tf();Si();Df();Lf();Mf();Nf();Rf();qf();Bf();Yf();Vf();zf();Hf();Gf();Mi();$f();Jf();Qf();Zf();Kf();Zo()});function pC(e){let t=this,r=e.attempt(Vt,n,e.attempt(this.parser.constructs.flowInitial,a,X(e,e.attempt(this.parser.constructs.flow,a,e.attempt(qo,a)),"linePrefix")));return r;function n(s){if(s===null){e.consume(s);return}return e.enter("lineEndingBlank"),e.consume(s),e.exit("lineEndingBlank"),t.currentConstruct=void 0,r}function a(s){if(s===null){e.consume(s);return}return e.enter("lineEnding"),e.consume(s),e.exit("lineEnding"),t.currentConstruct=void 0,r}}var Xf,ed=S(()=>{Ko();xe();Xf={tokenize:pC}});function id(e){return{resolveAll:ad(e==="text"?mC:void 0),tokenize:t};function t(r){let n=this,a=this.parser.constructs[e],s=r.attempt(a,l,c);return l;function l(h){return p(h)?s(h):c(h)}function c(h){if(h===null){r.consume(h);return}return r.enter("data"),r.consume(h),d}function d(h){return p(h)?(r.exit("data"),s(h)):(r.consume(h),d)}function p(h){if(h===null)return!0;let m=a[h],y=-1;if(m)for(;++y<m.length;){let w=m[y];if(!w.previous||w.previous.call(n,n.previous))return!0}return!1}}}function ad(e){return t;function t(r,n){let a=-1,s;for(;++a<=r.length;)s===void 0?r[a]&&r[a][1].type==="data"&&(s=a,a++):(!r[a]||r[a][1].type!=="data")&&(a!==s+2&&(r[s][1].end=r[a-1][1].end,r.splice(s+2,a-s-2),a=s+2),s=void 0);return e?e(r,n):r}}function mC(e,t){let r=0;for(;++r<=e.length;)if((r===e.length||e[r][1].type==="lineEnding")&&e[r-1][1].type==="data"){let n=e[r-1][1],a=t.sliceStream(n),s=a.length,l=-1,c=0,d;for(;s--;){let p=a[s];if(typeof p=="string"){for(l=p.length;p.charCodeAt(l-1)===32;)c++,l--;if(l)break;l=-1}else if(p===-2)d=!0,c++;else if(p!==-1){s++;break}}if(c){let p={type:r===e.length||d||c<2?"lineSuffix":"hardBreakTrailing",start:{_bufferIndex:s?l:n.start._bufferIndex+l,_index:n.start._index+s,line:n.end.line,column:n.end.column-c,offset:n.end.offset-c},end:{...n.end}};n.end={...p.start},n.start.offset===n.end.offset?Object.assign(n,p):(e.splice(r,0,["enter",p,t],["exit",p,t]),r+=2)}r++}return e}var td,rd,nd,Xo=S(()=>{td={resolveAll:ad()},rd=id("string"),nd=id("text")});var es={};ti(es,{attentionMarkers:()=>xC,contentInitial:()=>yC,disable:()=>SC,document:()=>gC,flow:()=>bC,flowInitial:()=>wC,insideSpan:()=>vC,string:()=>_C,text:()=>kC});var gC,yC,wC,bC,_C,kC,vC,xC,SC,od=S(()=>{Ko();Xo();gC={42:De,43:De,45:De,48:De,49:De,50:De,51:De,52:De,53:De,54:De,55:De,56:De,57:De,62:Ci},yC={91:zo},wC={[-2]:pn,[-1]:pn,32:pn},bC={35:Wo,42:lr,45:[Ii,lr],60:Go,61:Ii,95:lr,96:Fi,126:Fi},_C={38:Oi,92:Pi},kC={[-5]:gn,[-4]:gn,[-3]:gn,33:Jo,38:Oi,42:hn,60:[Io,$o],91:Qo,92:[Uo,Pi],93:sr,95:hn,96:No},vC={null:[hn,td]},xC={null:[42,95]},SC={null:[]}});function sd(e,t,r){let n={_bufferIndex:-1,_index:0,line:r&&r.line||1,column:r&&r.column||1,offset:r&&r.offset||0},a={},s=[],l=[],c=[],d=!0,p={attempt:U(G),check:U(B),consume:Z,enter:W,exit:P,interrupt:U(B,{interrupt:!0})},h={code:null,containerState:{},defineSkip:O,events:[],now:E,parser:e,previous:null,sliceSerialize:v,sliceStream:x,write:w},m=t.tokenize.call(h,p),y;return t.resolveAll&&s.push(t),h;function w(N){return l=Be(l,N),z(),l[l.length-1]!==null?[]:(L(t,0),h.events=Dr(s,h.events,h),h.events)}function v(N,Y){return PC(x(N),Y)}function x(N){return CC(l,N)}function E(){let{_bufferIndex:N,_index:Y,line:V,column:M,offset:re}=n;return{_bufferIndex:N,_index:Y,line:V,column:M,offset:re}}function O(N){a[N.line]=N.column,K()}function z(){let N;for(;n._index<l.length;){let Y=l[n._index];if(typeof Y=="string")for(N=n._index,n._bufferIndex<0&&(n._bufferIndex=0);n._index===N&&n._bufferIndex<Y.length;)A(Y.charCodeAt(n._bufferIndex));else A(Y)}}function A(N){d=void 0,y=N,m=m(N)}function Z(N){j(N)?(n.line++,n.column=1,n.offset+=N===-3?2:1,K()):N!==-1&&(n.column++,n.offset++),n._bufferIndex<0?n._index++:(n._bufferIndex++,n._bufferIndex===l[n._index].length&&(n._bufferIndex=-1,n._index++)),h.previous=N,d=!0}function W(N,Y){let V=Y||{};return V.type=N,V.start=E(),h.events.push(["enter",V,h]),c.push(V),V}function P(N){let Y=c.pop();return Y.end=E(),h.events.push(["exit",Y,h]),Y}function G(N,Y){L(N,Y.from)}function B(N,Y){Y.restore()}function U(N,Y){return V;function V(M,re,se){let he,_,Oe,Fe;return Array.isArray(M)?Me(M):"tokenize"in M?Me([M]):b(M);function b(be){return Qt;function Qt(it){let bt=it!==null&&be[it],Mt=it!==null&&be.null,Wr=[...Array.isArray(bt)?bt:bt?[bt]:[],...Array.isArray(Mt)?Mt:Mt?[Mt]:[]];return Me(Wr)(it)}}function Me(be){return he=be,_=0,be.length===0?se:wt(be[_])}function wt(be){return Qt;function Qt(it){return Fe=H(),Oe=be,be.partial||(h.currentConstruct=be),be.name&&h.parser.constructs.disable.null.includes(be.name)?Jt(it):be.tokenize.call(Y?Object.assign(Object.create(h),Y):h,p,Lt,Jt)(it)}}function Lt(be){return d=!0,N(Oe,Fe),re}function Jt(be){return d=!0,Fe.restore(),++_<he.length?wt(he[_]):se}}}function L(N,Y){N.resolveAll&&!s.includes(N)&&s.push(N),N.resolve&&Pe(h.events,Y,h.events.length-Y,N.resolve(h.events.slice(Y),h)),N.resolveTo&&(h.events=N.resolveTo(h.events,h))}function H(){let N=E(),Y=h.previous,V=h.currentConstruct,M=h.events.length,re=Array.from(c);return{from:M,restore:se};function se(){n=N,h.previous=Y,h.currentConstruct=V,h.events.length=M,c=re,K()}}function K(){n.line in a&&n.column<2&&(n.column=a[n.line],n.offset+=a[n.line]-1)}}function CC(e,t){let r=t.start._index,n=t.start._bufferIndex,a=t.end._index,s=t.end._bufferIndex,l;if(r===a)l=[e[r].slice(n,s)];else{if(l=e.slice(r,a),n>-1){let c=l[0];typeof c=="string"?l[0]=c.slice(n):l.shift()}s>0&&l.push(e[a].slice(0,s))}return l}function PC(e,t){let r=-1,n=[],a;for(;++r<e.length;){let s=e[r],l;if(typeof s=="string")l=s;else switch(s){case-5:{l="\r";break}case-4:{l=`
`;break}case-3:{l=`\r
`;break}case-2:{l=t?" ":"	";break}case-1:{if(!t&&a)continue;l=" ";break}default:l=String.fromCharCode(s)}a=s===-2,n.push(l)}return n.join("")}var ld=S(()=>{ce();Bt();xi()});function ts(e){let n={constructs:yf([es,...(e||{}).extensions||[]]),content:a(Sf),defined:[],document:a(Of),flow:a(Xf),lazy:{},string:a(rd),text:a(nd)};return n;function a(s){return l;function l(c){return sd(n,s,c)}}}var ud=S(()=>{wf();Cf();Ff();ed();Xo();od();ld()});function rs(e){for(;!Ai(e););return e}var cL,cd=S(()=>{Ro();cL=globalThis.process??{browser:!0,cwd:()=>"/",env:{},platform:"android"}});function ns(){let e=1,t="",r=!0,n;return a;function a(s,l,c){let d=[],p,h,m,y,w;for(s=t+(typeof s=="string"?s.toString():new TextDecoder(l||void 0).decode(s)),m=0,t="",r&&(s.charCodeAt(0)===65279&&m++,r=void 0);m<s.length;){if(fd.lastIndex=m,p=fd.exec(s),y=p&&p.index!==void 0?p.index:s.length,w=s.charCodeAt(y),!p){t=s.slice(m);break}if(w===10&&m===y&&n)d.push(-3),n=void 0;else switch(n&&(d.push(-5),n=void 0),m<y&&(d.push(s.slice(m,y)),e+=y-m),w){case 0:{d.push(65533),e++;break}case 9:{for(h=Math.ceil(e/4)*4,d.push(-2);e++<h;)d.push(-1);break}case 10:{d.push(-4),e=1;break}default:n=!0,e=1}m=y+1}return c&&(n&&d.push(-5),t&&d.push(t),d.push(null)),d}}var hL,fd,dd=S(()=>{hL=globalThis.process??{browser:!0,cwd:()=>"/",env:{},platform:"android"},fd=/[\0\t\n\r]/g});var mL,hd=S(()=>{ud();cd();dd();mL=globalThis.process??{browser:!0,cwd:()=>"/",env:{},platform:"android"}});function Ni(e){return e.replace(OC,FC)}function FC(e,t,r){if(t)return t;if(r.charCodeAt(0)===35){let a=r.charCodeAt(1),s=a===120||a===88;return ki(r.slice(s?2:1),s?16:10)}return Ar(r)||e}var OC,is=S(()=>{_i();Lo();OC=/\\([!-/:-@[-`{-~])|&(#(?:\d{1,7}|x[\da-f]{1,6})|[\da-z]{1,31});/gi});function zt(e){return!e||typeof e!="object"?"":"position"in e||"type"in e?pd(e.position):"start"in e||"end"in e?pd(e):"line"in e||"column"in e?as(e):""}function as(e){return md(e&&e.line)+":"+md(e&&e.column)}function pd(e){return as(e&&e.start)+"-"+as(e&&e.end)}function md(e){return e&&typeof e=="number"?e:1}var gd=S(()=>{});var os=S(()=>{gd()});function ss(e,t,r){return typeof t!="string"&&(r=t,t=void 0),EC(r)(rs(ts(r).document().write(ns()(e,t,!0))))}function EC(e){let t={transforms:[],canContainEols:["emphasis","fragment","heading","paragraph","strong"],enter:{autolink:s(Ie),autolinkProtocol:U,autolinkEmail:U,atxHeading:s(In),blockQuote:s(it),characterEscape:U,characterReference:U,codeFenced:s(bt),codeFencedFenceInfo:l,codeFencedFenceMeta:l,codeIndented:s(bt,l),codeText:s(Mt,l),codeTextData:U,data:U,codeFlowValue:U,definition:s(Wr),definitionDestinationString:l,definitionLabelString:l,definitionTitleString:l,emphasis:s(ka),hardBreakEscape:s(Nn),hardBreakTrailing:s(Nn),htmlFlow:s(Rn,l),htmlFlowData:U,htmlText:s(Rn,l),htmlTextData:U,image:s(qn),label:l,link:s(Ie),listItem:s(va),listItemValue:y,listOrdered:s(gr,m),listUnordered:s(gr),paragraph:s(xa),reference:b,referenceString:l,resourceDestinationString:l,resourceTitleString:l,setextHeading:s(In),strong:s(jn),thematicBreak:s(Bn)},exit:{atxHeading:d(),atxHeadingSequence:W,autolink:d(),autolinkEmail:Qt,autolinkProtocol:be,blockQuote:d(),characterEscapeValue:L,characterReferenceMarkerHexadecimal:wt,characterReferenceMarkerNumeric:wt,characterReferenceValue:Lt,characterReference:Jt,codeFenced:d(E),codeFencedFence:x,codeFencedFenceInfo:w,codeFencedFenceMeta:v,codeFlowValue:L,codeIndented:d(O),codeText:d(V),codeTextData:L,data:L,definition:d(),definitionDestinationString:Z,definitionLabelString:z,definitionTitleString:A,emphasis:d(),hardBreakEscape:d(K),hardBreakTrailing:d(K),htmlFlow:d(N),htmlFlowData:L,htmlText:d(Y),htmlTextData:L,image:d(re),label:he,labelText:se,lineEnding:H,link:d(M),listItem:d(),listOrdered:d(),listUnordered:d(),paragraph:d(),referenceString:Me,resourceDestinationString:_,resourceTitleString:Oe,resource:Fe,setextHeading:d(B),setextHeadingLineSequence:G,setextHeadingText:P,strong:d(),thematicBreak:d()}};bd(t,(e||{}).mdastExtensions||[]);let r={};return n;function n(F){let D={type:"root",children:[]},I={stack:[D],tokenStack:[],config:t,enter:c,exit:p,buffer:l,resume:h,data:r},te=[],le=-1;for(;++le<F.length;)if(F[le][1].type==="listOrdered"||F[le][1].type==="listUnordered")if(F[le][0]==="enter")te.push(le);else{let Ee=te.pop();le=a(F,Ee,le)}for(le=-1;++le<F.length;){let Ee=t[F[le][0]];wd.call(Ee,F[le][1].type)&&Ee[F[le][1].type].call(Object.assign({sliceSerialize:F[le][2].sliceSerialize},I),F[le][1])}if(I.tokenStack.length>0){let Ee=I.tokenStack[I.tokenStack.length-1];(Ee[1]||yd).call(I,void 0,Ee[0])}for(D.position={start:Ut(F.length>0?F[0][1].start:{line:1,column:1,offset:0}),end:Ut(F.length>0?F[F.length-2][1].end:{line:1,column:1,offset:0})},le=-1;++le<t.transforms.length;)D=t.transforms[le](D)||D;return D}function a(F,D,I){let te=D-1,le=-1,Ee=!1,at,Ve,Qe,dt;for(;++te<=I;){let Re=F[te];switch(Re[1].type){case"listUnordered":case"listOrdered":case"blockQuote":{Re[0]==="enter"?le++:le--,dt=void 0;break}case"lineEndingBlank":{Re[0]==="enter"&&(at&&!dt&&!le&&!Qe&&(Qe=te),dt=void 0);break}case"linePrefix":case"listItemValue":case"listItemMarker":case"listItemPrefix":case"listItemPrefixWhitespace":break;default:dt=void 0}if(!le&&Re[0]==="enter"&&Re[1].type==="listItemPrefix"||le===-1&&Re[0]==="exit"&&(Re[1].type==="listUnordered"||Re[1].type==="listOrdered")){if(at){let ot=te;for(Ve=void 0;ot--;){let st=F[ot];if(st[1].type==="lineEnding"||st[1].type==="lineEndingBlank"){if(st[0]==="exit")continue;Ve&&(F[Ve][1].type="lineEndingBlank",Ee=!0),st[1].type="lineEnding",Ve=ot}else if(!(st[1].type==="linePrefix"||st[1].type==="blockQuotePrefix"||st[1].type==="blockQuotePrefixWhitespace"||st[1].type==="blockQuoteMarker"||st[1].type==="listItemIndent"))break}Qe&&(!Ve||Qe<Ve)&&(at._spread=!0),at.end=Object.assign({},Ve?F[Ve][1].start:Re[1].end),F.splice(Ve||te,0,["exit",at,Re[2]]),te++,I++}if(Re[1].type==="listItemPrefix"){let ot={type:"listItem",_spread:!1,start:Object.assign({},Re[1].start),end:void 0};at=ot,F.splice(te,0,["enter",ot,Re[2]]),te++,I++,Qe=void 0,dt=!0}}}return F[D][1]._spread=Ee,I}function s(F,D){return I;function I(te){c.call(this,F(te),te),D&&D.call(this,te)}}function l(){this.stack.push({type:"fragment",children:[]})}function c(F,D,I){this.stack[this.stack.length-1].children.push(F),this.stack.push(F),this.tokenStack.push([D,I||void 0]),F.position={start:Ut(D.start),end:void 0}}function d(F){return D;function D(I){F&&F.call(this,I),p.call(this,I)}}function p(F,D){let I=this.stack.pop(),te=this.tokenStack.pop();if(te)te[0].type!==F.type&&(D?D.call(this,F,te[0]):(te[1]||yd).call(this,F,te[0]));else throw new Error("Cannot close `"+F.type+"` ("+zt({start:F.start,end:F.end})+"): it\u2019s not open");I.position.end=Ut(F.end)}function h(){return ar(this.stack.pop())}function m(){this.data.expectingFirstListItemValue=!0}function y(F){if(this.data.expectingFirstListItemValue){let D=this.stack[this.stack.length-2];D.start=Number.parseInt(this.sliceSerialize(F),10),this.data.expectingFirstListItemValue=void 0}}function w(){let F=this.resume(),D=this.stack[this.stack.length-1];D.lang=F}function v(){let F=this.resume(),D=this.stack[this.stack.length-1];D.meta=F}function x(){this.data.flowCodeInside||(this.buffer(),this.data.flowCodeInside=!0)}function E(){let F=this.resume(),D=this.stack[this.stack.length-1];D.value=F.replace(/^(\r?\n|\r)|(\r?\n|\r)$/g,""),this.data.flowCodeInside=void 0}function O(){let F=this.resume(),D=this.stack[this.stack.length-1];D.value=F.replace(/(\r?\n|\r)$/g,"")}function z(F){let D=this.resume(),I=this.stack[this.stack.length-1];I.label=D,I.identifier=Tt(this.sliceSerialize(F)).toLowerCase()}function A(){let F=this.resume(),D=this.stack[this.stack.length-1];D.title=F}function Z(){let F=this.resume(),D=this.stack[this.stack.length-1];D.url=F}function W(F){let D=this.stack[this.stack.length-1];if(!D.depth){let I=this.sliceSerialize(F).length;D.depth=I}}function P(){this.data.setextHeadingSlurpLineEnding=!0}function G(F){let D=this.stack[this.stack.length-1];D.depth=this.sliceSerialize(F).codePointAt(0)===61?1:2}function B(){this.data.setextHeadingSlurpLineEnding=void 0}function U(F){let I=this.stack[this.stack.length-1].children,te=I[I.length-1];(!te||te.type!=="text")&&(te=Ne(),te.position={start:Ut(F.start),end:void 0},I.push(te)),this.stack.push(te)}function L(F){let D=this.stack.pop();D.value+=this.sliceSerialize(F),D.position.end=Ut(F.end)}function H(F){let D=this.stack[this.stack.length-1];if(this.data.atHardBreak){let I=D.children[D.children.length-1];I.position.end=Ut(F.end),this.data.atHardBreak=void 0;return}!this.data.setextHeadingSlurpLineEnding&&t.canContainEols.includes(D.type)&&(U.call(this,F),L.call(this,F))}function K(){this.data.atHardBreak=!0}function N(){let F=this.resume(),D=this.stack[this.stack.length-1];D.value=F}function Y(){let F=this.resume(),D=this.stack[this.stack.length-1];D.value=F}function V(){let F=this.resume(),D=this.stack[this.stack.length-1];D.value=F}function M(){let F=this.stack[this.stack.length-1];if(this.data.inReference){let D=this.data.referenceType||"shortcut";F.type+="Reference",F.referenceType=D,delete F.url,delete F.title}else delete F.identifier,delete F.label;this.data.referenceType=void 0}function re(){let F=this.stack[this.stack.length-1];if(this.data.inReference){let D=this.data.referenceType||"shortcut";F.type+="Reference",F.referenceType=D,delete F.url,delete F.title}else delete F.identifier,delete F.label;this.data.referenceType=void 0}function se(F){let D=this.sliceSerialize(F),I=this.stack[this.stack.length-2];I.label=Ni(D),I.identifier=Tt(D).toLowerCase()}function he(){let F=this.stack[this.stack.length-1],D=this.resume(),I=this.stack[this.stack.length-1];if(this.data.inReference=!0,I.type==="link"){let te=F.children;I.children=te}else I.alt=D}function _(){let F=this.resume(),D=this.stack[this.stack.length-1];D.url=F}function Oe(){let F=this.resume(),D=this.stack[this.stack.length-1];D.title=F}function Fe(){this.data.inReference=void 0}function b(){this.data.referenceType="collapsed"}function Me(F){let D=this.resume(),I=this.stack[this.stack.length-1];I.label=D,I.identifier=Tt(this.sliceSerialize(F)).toLowerCase(),this.data.referenceType="full"}function wt(F){this.data.characterReferenceType=F.type}function Lt(F){let D=this.sliceSerialize(F),I=this.data.characterReferenceType,te;I?(te=ki(D,I==="characterReferenceMarkerNumeric"?10:16),this.data.characterReferenceType=void 0):te=Ar(D);let le=this.stack[this.stack.length-1];le.value+=te}function Jt(F){let D=this.stack.pop();D.position.end=Ut(F.end)}function be(F){L.call(this,F);let D=this.stack[this.stack.length-1];D.url=this.sliceSerialize(F)}function Qt(F){L.call(this,F);let D=this.stack[this.stack.length-1];D.url="mailto:"+this.sliceSerialize(F)}function it(){return{type:"blockquote",children:[]}}function bt(){return{type:"code",lang:null,meta:null,value:""}}function Mt(){return{type:"inlineCode",value:""}}function Wr(){return{type:"definition",identifier:"",label:null,title:null,url:""}}function ka(){return{type:"emphasis",children:[]}}function In(){return{type:"heading",depth:0,children:[]}}function Nn(){return{type:"break"}}function Rn(){return{type:"html",value:""}}function qn(){return{type:"image",title:null,url:"",alt:null}}function Ie(){return{type:"link",title:null,url:"",children:[]}}function gr(F){return{type:"list",ordered:F.type==="listOrdered",start:null,spread:F._spread,children:[]}}function va(F){return{type:"listItem",spread:F._spread,checked:null,children:[]}}function xa(){return{type:"paragraph",children:[]}}function jn(){return{type:"strong",children:[]}}function Ne(){return{type:"text",value:""}}function Bn(){return{type:"thematicBreak"}}}function Ut(e){return{line:e.line,column:e.column,offset:e.offset}}function bd(e,t){let r=-1;for(;++r<t.length;){let n=t[r];Array.isArray(n)?bd(e,n):AC(e,n)}}function AC(e,t){let r;for(r in t)if(wd.call(t,r))switch(r){case"canContainEols":{let n=t[r];n&&e[r].push(...n);break}case"transforms":{let n=t[r];n&&e[r].push(...n);break}case"enter":case"exit":{let n=t[r];n&&Object.assign(e[r],n);break}}}function yd(e,t){throw e?new Error("Cannot close `"+e.type+"` ("+zt({start:e.start,end:e.end})+"): a different token (`"+t.type+"`, "+zt({start:t.start,end:t.end})+") is open"):new Error("Cannot close document, a token (`"+t.type+"`, "+zt({start:t.start,end:t.end})+") is still open")}var PL,wd,_d=S(()=>{bi();hd();Lo();is();vi();_i();os();PL=globalThis.process??{browser:!0,cwd:()=>"/",env:{},platform:"android"},wd={}.hasOwnProperty});var kd=S(()=>{_d()});function yn(e){let t=this;t.parser=r;function r(n){return ss(n,{...t.data("settings"),...e,extensions:t.data("micromarkExtensions")||[],mdastExtensions:t.data("fromMarkdownExtensions")||[]})}}var vd=S(()=>{kd()});var xd={};ti(xd,{default:()=>yn});var ls=S(()=>{vd()});function Cd(e,t){let r=t||{};function n(a,...s){let l=n.invalid,c=n.handlers;if(a&&Sd.call(a,e)){let d=String(a[e]);l=Sd.call(c,d)?c[d]:n.unknown}if(l)return l.call(this,a,...s)}return n.handlers=r.handlers||{},n.invalid=r.invalid,n.unknown=r.unknown,n}var Sd,Pd=S(()=>{Sd={}.hasOwnProperty});function us(e,t){let r=-1,n;if(t.extensions)for(;++r<t.extensions.length;)us(e,t.extensions[r]);for(n in t)if(TC.call(t,n))switch(n){case"extensions":break;case"unsafe":{Od(e[n],t[n]);break}case"join":{Od(e[n],t[n]);break}case"handlers":{DC(e[n],t[n]);break}default:e.options[n]=t[n]}return e}function Od(e,t){t&&e.push(...t)}function DC(e,t){t&&Object.assign(e,t)}var TC,Fd=S(()=>{TC={}.hasOwnProperty});function Ed(e,t,r,n){let a=r.enter("blockquote"),s=r.createTracker(n);s.move("> "),s.shift(2);let l=r.indentLines(r.containerFlow(e,s.current()),LC);return a(),l}function LC(e,t,r){return">"+(r?"":" ")+e}var Ad=S(()=>{});function Ri(e,t){return Td(e,t.inConstruct,!0)&&!Td(e,t.notInConstruct,!1)}function Td(e,t,r){if(typeof t=="string"&&(t=[t]),!t||t.length===0)return r;let n=-1;for(;++n<t.length;)if(e.includes(t[n]))return!0;return!1}var cs=S(()=>{});function fs(e,t,r,n){let a=-1;for(;++a<r.unsafe.length;)if(r.unsafe[a].character===`
`&&Ri(r.stack,r.unsafe[a]))return/[ \t]/.test(n.before)?"":" ";return`\\
`}var Dd=S(()=>{cs()});function Ld(e,t){let r=String(e),n=r.indexOf(t),a=n,s=0,l=0;if(typeof t!="string")throw new TypeError("Expected substring");for(;n!==-1;)n===a?++s>l&&(l=s):s=1,a=n+t.length,n=r.indexOf(t,a);return l}var Md=S(()=>{});function wn(e,t){return!!(t.options.fences===!1&&e.value&&!e.lang&&/[^ \r\n]/.test(e.value)&&!/^[\t ]*(?:[\r\n]|$)|(?:^|[\r\n])[\t ]*$/.test(e.value))}var ds=S(()=>{});function Id(e){let t=e.options.fence||"`";if(t!=="`"&&t!=="~")throw new Error("Cannot serialize code with `"+t+"` for `options.fence`, expected `` ` `` or `~`");return t}var Nd=S(()=>{});function Rd(e,t,r,n){let a=Id(r),s=e.value||"",l=a==="`"?"GraveAccent":"Tilde";if(wn(e,r)){let m=r.enter("codeIndented"),y=r.indentLines(s,MC);return m(),y}let c=r.createTracker(n),d=a.repeat(Math.max(Ld(s,a)+1,3)),p=r.enter("codeFenced"),h=c.move(d);if(e.lang){let m=r.enter(`codeFencedLang${l}`);h+=c.move(r.safe(e.lang,{before:h,after:" ",encode:["`"],...c.current()})),m()}if(e.lang&&e.meta){let m=r.enter(`codeFencedMeta${l}`);h+=c.move(" "),h+=c.move(r.safe(e.meta,{before:h,after:`
`,encode:["`"],...c.current()})),m()}return h+=c.move(`
`),s&&(h+=c.move(s+`
`)),h+=c.move(d),p(),h}function MC(e,t,r){return(r?"":"    ")+e}var qd=S(()=>{Md();ds();Nd()});function Lr(e){let t=e.options.quote||'"';if(t!=='"'&&t!=="'")throw new Error("Cannot serialize title with `"+t+"` for `options.quote`, expected `\"`, or `'`");return t}var qi=S(()=>{});function jd(e,t,r,n){let a=Lr(r),s=a==='"'?"Quote":"Apostrophe",l=r.enter("definition"),c=r.enter("label"),d=r.createTracker(n),p=d.move("[");return p+=d.move(r.safe(r.associationId(e),{before:p,after:"]",...d.current()})),p+=d.move("]: "),c(),!e.url||/[\0- \u007F]/.test(e.url)?(c=r.enter("destinationLiteral"),p+=d.move("<"),p+=d.move(r.safe(e.url,{before:p,after:">",...d.current()})),p+=d.move(">")):(c=r.enter("destinationRaw"),p+=d.move(r.safe(e.url,{before:p,after:e.title?" ":`
`,...d.current()}))),c(),e.title&&(c=r.enter(`title${s}`),p+=d.move(" "+a),p+=d.move(r.safe(e.title,{before:p,after:a,...d.current()})),p+=d.move(a),c()),l(),p}var Bd=S(()=>{qi()});function Yd(e){let t=e.options.emphasis||"*";if(t!=="*"&&t!=="_")throw new Error("Cannot serialize emphasis with `"+t+"` for `options.emphasis`, expected `*`, or `_`");return t}var Vd=S(()=>{});function $e(e){return"&#x"+e.toString(16).toUpperCase()+";"}var Mr=S(()=>{});function Ir(e,t,r){let n=Tr(e),a=Tr(t);return n===void 0?a===void 0?r==="_"?{inside:!0,outside:!0}:{inside:!1,outside:!1}:a===1?{inside:!0,outside:!0}:{inside:!1,outside:!0}:n===1?a===void 0?{inside:!1,outside:!1}:a===1?{inside:!0,outside:!0}:{inside:!1,outside:!1}:a===void 0?{inside:!1,outside:!1}:a===1?{inside:!0,outside:!1}:{inside:!1,outside:!1}}var hs=S(()=>{Mo()});function ps(e,t,r,n){let a=Yd(r),s=r.enter("emphasis"),l=r.createTracker(n),c=l.move(a),d=l.move(r.containerPhrasing(e,{after:a,before:c,...l.current()})),p=d.charCodeAt(0),h=Ir(n.before.charCodeAt(n.before.length-1),p,a);h.inside&&(d=$e(p)+d.slice(1));let m=d.charCodeAt(d.length-1),y=Ir(n.after.charCodeAt(0),m,a);y.inside&&(d=d.slice(0,-1)+$e(m));let w=l.move(a);return s(),r.attentionEncodeSurroundingInfo={after:y.outside,before:h.outside},c+d+w}function IC(e,t,r){return r.options.emphasis||"*"}var zd=S(()=>{Vd();Mr();hs();ps.peek=IC});function NC(e){let t=[],r=-1;for(;++r<e.length;)t[r]=Nr(e[r]);return ji(n);function n(...a){let s=-1;for(;++s<t.length;)if(t[s].apply(this,a))return!0;return!1}}function RC(e){let t=e;return ji(r);function r(n){let a=n,s;for(s in e)if(a[s]!==t[s])return!1;return!0}}function qC(e){return ji(t);function t(r){return r&&r.type===e}}function ji(e){return t;function t(r,n,a){return!!(BC(r)&&e.call(this,r,typeof n=="number"?n:void 0,a||void 0))}}function jC(){return!0}function BC(e){return e!==null&&typeof e=="object"&&"type"in e}var Nr,Ud=S(()=>{Nr=function(e){if(e==null)return jC;if(typeof e=="function")return ji(e);if(typeof e=="object")return Array.isArray(e)?NC(e):RC(e);if(typeof e=="string")return qC(e);throw new Error("Expected function, string, or object as test")}});var ms=S(()=>{Ud()});function Wd(e){return"\x1B[33m"+e+"\x1B[39m"}var Hd=S(()=>{});function gs(e,t,r,n){let a;typeof t=="function"&&typeof r!="function"?(n=r,r=t):a=t;let s=Nr(a),l=n?-1:1;c(e,void 0,[])();function c(d,p,h){let m=d&&typeof d=="object"?d:{};if(typeof m.type=="string"){let w=typeof m.tagName=="string"?m.tagName:typeof m.name=="string"?m.name:void 0;Object.defineProperty(y,"name",{value:"node ("+Wd(d.type+(w?"<"+w+">":""))+")"})}return y;function y(){let w=Gd,v,x,E;if((!t||s(d,p,h[h.length-1]||void 0))&&(w=YC(r(d,h)),w[0]===ur))return w;if("children"in d&&d.children){let O=d;if(O.children&&w[0]!==Yi)for(x=(n?O.children.length:-1)+l,E=h.concat(O);x>-1&&x<O.children.length;){let z=O.children[x];if(v=c(z,x,E)(),v[0]===ur)return v;x=typeof v[1]=="number"?v[1]:x+l}}return w}}}function YC(e){return Array.isArray(e)?e:typeof e=="number"?[Bi,e]:e==null?Gd:[e]}var Gd,Bi,ur,Yi,$d=S(()=>{ms();Hd();Gd=[],Bi=!0,ur=!1,Yi="skip"});var ys=S(()=>{$d()});function ws(e,t,r,n){let a,s,l;typeof t=="function"&&typeof r!="function"?(s=void 0,l=t,a=r):(s=t,l=r,a=n),gs(e,s,c,a);function c(d,p){let h=p[p.length-1],m=h?h.children.indexOf(d):void 0;return l(d,m,h)}}var Jd=S(()=>{ys();ys()});var Qd=S(()=>{Jd()});function Vi(e,t){let r=!1;return ws(e,function(n){if("value"in n&&/\r?\n|\r/.test(n.value)||n.type==="break")return r=!0,ur}),!!((!e.depth||e.depth<3)&&ar(e)&&(t.options.setext||r))}var bs=S(()=>{Qd();bi()});function Zd(e,t,r,n){let a=Math.max(Math.min(6,e.depth||1),1),s=r.createTracker(n);if(Vi(e,r)){let h=r.enter("headingSetext"),m=r.enter("phrasing"),y=r.containerPhrasing(e,{...s.current(),before:`
`,after:`
`});return m(),h(),y+`
`+(a===1?"=":"-").repeat(y.length-(Math.max(y.lastIndexOf("\r"),y.lastIndexOf(`
`))+1))}let l="#".repeat(a),c=r.enter("headingAtx"),d=r.enter("phrasing");s.move(l+" ");let p=r.containerPhrasing(e,{before:"# ",after:`
`,...s.current()});return/^[\t ]/.test(p)&&(p=$e(p.charCodeAt(0))+p.slice(1)),p=p?l+" "+p:l,r.options.closeAtx&&(p+=" "+l),d(),c(),p}var Kd=S(()=>{Mr();bs()});function _s(e){return e.value||""}function VC(){return"<"}var Xd=S(()=>{_s.peek=VC});function ks(e,t,r,n){let a=Lr(r),s=a==='"'?"Quote":"Apostrophe",l=r.enter("image"),c=r.enter("label"),d=r.createTracker(n),p=d.move("![");return p+=d.move(r.safe(e.alt,{before:p,after:"]",...d.current()})),p+=d.move("]("),c(),!e.url&&e.title||/[\0- \u007F]/.test(e.url)?(c=r.enter("destinationLiteral"),p+=d.move("<"),p+=d.move(r.safe(e.url,{before:p,after:">",...d.current()})),p+=d.move(">")):(c=r.enter("destinationRaw"),p+=d.move(r.safe(e.url,{before:p,after:e.title?" ":")",...d.current()}))),c(),e.title&&(c=r.enter(`title${s}`),p+=d.move(" "+a),p+=d.move(r.safe(e.title,{before:p,after:a,...d.current()})),p+=d.move(a),c()),p+=d.move(")"),l(),p}function zC(){return"!"}var eh=S(()=>{qi();ks.peek=zC});function vs(e,t,r,n){let a=e.referenceType,s=r.enter("imageReference"),l=r.enter("label"),c=r.createTracker(n),d=c.move("!["),p=r.safe(e.alt,{before:d,after:"]",...c.current()});d+=c.move(p+"]["),l();let h=r.stack;r.stack=[],l=r.enter("reference");let m=r.safe(r.associationId(e),{before:d,after:"]",...c.current()});return l(),r.stack=h,s(),a==="full"||!p||p!==m?d+=c.move(m+"]"):a==="shortcut"?d=d.slice(0,-1):d+=c.move("]"),d}function UC(){return"!"}var th=S(()=>{vs.peek=UC});function xs(e,t,r){let n=e.value||"",a="`",s=-1;for(;new RegExp("(^|[^`])"+a+"([^`]|$)").test(n);)a+="`";for(/[^ \r\n]/.test(n)&&(/^[ \r\n]/.test(n)&&/[ \r\n]$/.test(n)||/^`|`$/.test(n))&&(n=" "+n+" ");++s<r.unsafe.length;){let l=r.unsafe[s],c=r.compilePattern(l),d;if(l.atBreak)for(;d=c.exec(n);){let p=d.index;n.charCodeAt(p)===10&&n.charCodeAt(p-1)===13&&p--,n=n.slice(0,p)+" "+n.slice(d.index+1)}}return a+n+a}function WC(){return"`"}var rh=S(()=>{xs.peek=WC});function Ss(e,t){let r=ar(e);return!!(!t.options.resourceLink&&e.url&&!e.title&&e.children&&e.children.length===1&&e.children[0].type==="text"&&(r===e.url||"mailto:"+r===e.url)&&/^[a-z][a-z+.-]+:/i.test(e.url)&&!/[\0- <>\u007F]/.test(e.url))}var nh=S(()=>{bi()});function Cs(e,t,r,n){let a=Lr(r),s=a==='"'?"Quote":"Apostrophe",l=r.createTracker(n),c,d;if(Ss(e,r)){let h=r.stack;r.stack=[],c=r.enter("autolink");let m=l.move("<");return m+=l.move(r.containerPhrasing(e,{before:m,after:">",...l.current()})),m+=l.move(">"),c(),r.stack=h,m}c=r.enter("link"),d=r.enter("label");let p=l.move("[");return p+=l.move(r.containerPhrasing(e,{before:p,after:"](",...l.current()})),p+=l.move("]("),d(),!e.url&&e.title||/[\0- \u007F]/.test(e.url)?(d=r.enter("destinationLiteral"),p+=l.move("<"),p+=l.move(r.safe(e.url,{before:p,after:">",...l.current()})),p+=l.move(">")):(d=r.enter("destinationRaw"),p+=l.move(r.safe(e.url,{before:p,after:e.title?" ":")",...l.current()}))),d(),e.title&&(d=r.enter(`title${s}`),p+=l.move(" "+a),p+=l.move(r.safe(e.title,{before:p,after:a,...l.current()})),p+=l.move(a),d()),p+=l.move(")"),c(),p}function HC(e,t,r){return Ss(e,r)?"<":"["}var ih=S(()=>{qi();nh();Cs.peek=HC});function Ps(e,t,r,n){let a=e.referenceType,s=r.enter("linkReference"),l=r.enter("label"),c=r.createTracker(n),d=c.move("["),p=r.containerPhrasing(e,{before:d,after:"]",...c.current()});d+=c.move(p+"]["),l();let h=r.stack;r.stack=[],l=r.enter("reference");let m=r.safe(r.associationId(e),{before:d,after:"]",...c.current()});return l(),r.stack=h,s(),a==="full"||!p||p!==m?d+=c.move(m+"]"):a==="shortcut"?d=d.slice(0,-1):d+=c.move("]"),d}function GC(){return"["}var ah=S(()=>{Ps.peek=GC});function Rr(e){let t=e.options.bullet||"*";if(t!=="*"&&t!=="+"&&t!=="-")throw new Error("Cannot serialize items with `"+t+"` for `options.bullet`, expected `*`, `+`, or `-`");return t}var zi=S(()=>{});function oh(e){let t=Rr(e),r=e.options.bulletOther;if(!r)return t==="*"?"-":"*";if(r!=="*"&&r!=="+"&&r!=="-")throw new Error("Cannot serialize items with `"+r+"` for `options.bulletOther`, expected `*`, `+`, or `-`");if(r===t)throw new Error("Expected `bullet` (`"+t+"`) and `bulletOther` (`"+r+"`) to be different");return r}var sh=S(()=>{zi()});function lh(e){let t=e.options.bulletOrdered||".";if(t!=="."&&t!==")")throw new Error("Cannot serialize items with `"+t+"` for `options.bulletOrdered`, expected `.` or `)`");return t}var uh=S(()=>{});function Ui(e){let t=e.options.rule||"*";if(t!=="*"&&t!=="-"&&t!=="_")throw new Error("Cannot serialize rules with `"+t+"` for `options.rule`, expected `*`, `-`, or `_`");return t}var Os=S(()=>{});function ch(e,t,r,n){let a=r.enter("list"),s=r.bulletCurrent,l=e.ordered?lh(r):Rr(r),c=e.ordered?l==="."?")":".":oh(r),d=t&&r.bulletLastUsed?l===r.bulletLastUsed:!1;if(!e.ordered){let h=e.children?e.children[0]:void 0;if((l==="*"||l==="-")&&h&&(!h.children||!h.children[0])&&r.stack[r.stack.length-1]==="list"&&r.stack[r.stack.length-2]==="listItem"&&r.stack[r.stack.length-3]==="list"&&r.stack[r.stack.length-4]==="listItem"&&r.indexStack[r.indexStack.length-1]===0&&r.indexStack[r.indexStack.length-2]===0&&r.indexStack[r.indexStack.length-3]===0&&(d=!0),Ui(r)===l&&h){let m=-1;for(;++m<e.children.length;){let y=e.children[m];if(y&&y.type==="listItem"&&y.children&&y.children[0]&&y.children[0].type==="thematicBreak"){d=!0;break}}}}d&&(l=c),r.bulletCurrent=l;let p=r.containerFlow(e,n);return r.bulletLastUsed=l,r.bulletCurrent=s,a(),p}var fh=S(()=>{zi();sh();uh();Os()});function dh(e){let t=e.options.listItemIndent||"one";if(t!=="tab"&&t!=="one"&&t!=="mixed")throw new Error("Cannot serialize items with `"+t+"` for `options.listItemIndent`, expected `tab`, `one`, or `mixed`");return t}var hh=S(()=>{});function ph(e,t,r,n){let a=dh(r),s=r.bulletCurrent||Rr(r);t&&t.type==="list"&&t.ordered&&(s=(typeof t.start=="number"&&t.start>-1?t.start:1)+(r.options.incrementListMarker===!1?0:t.children.indexOf(e))+s);let l=s.length+1;(a==="tab"||a==="mixed"&&(t&&t.type==="list"&&t.spread||e.spread))&&(l=Math.ceil(l/4)*4);let c=r.createTracker(n);c.move(s+" ".repeat(l-s.length)),c.shift(l);let d=r.enter("listItem"),p=r.indentLines(r.containerFlow(e,c.current()),h);return d(),p;function h(m,y,w){return y?(w?"":" ".repeat(l))+m:(w?s:s+" ".repeat(l-s.length))+m}}var mh=S(()=>{zi();hh()});function gh(e,t,r,n){let a=r.enter("paragraph"),s=r.enter("phrasing"),l=r.containerPhrasing(e,n);return s(),a(),l}var yh=S(()=>{});var Fs,wh=S(()=>{ms();Fs=Nr(["break","delete","emphasis","footnote","footnoteReference","image","imageReference","inlineCode","inlineMath","link","linkReference","mdxJsxTextElement","mdxTextExpression","strong","text","textDirective"])});var bh=S(()=>{wh()});function _h(e,t,r,n){return(e.children.some(function(l){return Fs(l)})?r.containerPhrasing:r.containerFlow).call(r,e,n)}var kh=S(()=>{bh()});function vh(e){let t=e.options.strong||"*";if(t!=="*"&&t!=="_")throw new Error("Cannot serialize strong with `"+t+"` for `options.strong`, expected `*`, or `_`");return t}var xh=S(()=>{});function Es(e,t,r,n){let a=vh(r),s=r.enter("strong"),l=r.createTracker(n),c=l.move(a+a),d=l.move(r.containerPhrasing(e,{after:a,before:c,...l.current()})),p=d.charCodeAt(0),h=Ir(n.before.charCodeAt(n.before.length-1),p,a);h.inside&&(d=$e(p)+d.slice(1));let m=d.charCodeAt(d.length-1),y=Ir(n.after.charCodeAt(0),m,a);y.inside&&(d=d.slice(0,-1)+$e(m));let w=l.move(a+a);return s(),r.attentionEncodeSurroundingInfo={after:y.outside,before:h.outside},c+d+w}function $C(e,t,r){return r.options.strong||"*"}var Sh=S(()=>{xh();Mr();hs();Es.peek=$C});function Ch(e,t,r,n){return r.safe(e.value,n)}var Ph=S(()=>{});function Oh(e){let t=e.options.ruleRepetition||3;if(t<3)throw new Error("Cannot serialize rules with repetition `"+t+"` for `options.ruleRepetition`, expected `3` or more");return t}var Fh=S(()=>{});function Eh(e,t,r){let n=(Ui(r)+(r.options.ruleSpaces?" ":"")).repeat(Oh(r));return r.options.ruleSpaces?n.slice(0,-1):n}var Ah=S(()=>{Fh();Os()});var Th,Dh=S(()=>{Ad();Dd();qd();Bd();zd();Kd();Xd();eh();th();rh();ih();ah();fh();mh();yh();kh();Sh();Ph();Ah();Th={blockquote:Ed,break:fs,code:Rd,definition:jd,emphasis:ps,hardBreak:fs,heading:Zd,html:_s,image:ks,imageReference:vs,inlineCode:xs,link:Cs,linkReference:Ps,list:ch,listItem:ph,paragraph:gh,root:_h,strong:Es,text:Ch,thematicBreak:Eh}});function JC(e,t,r,n){if(t.type==="code"&&wn(t,n)&&(e.type==="list"||e.type===t.type&&wn(e,n)))return!1;if("spread"in r&&typeof r.spread=="boolean")return e.type==="paragraph"&&(e.type===t.type||t.type==="definition"||t.type==="heading"&&Vi(t,n))?void 0:r.spread?1:0}var Lh,Mh=S(()=>{ds();bs();Lh=[JC]});var cr,Ih,Nh=S(()=>{cr=["autolink","destinationLiteral","destinationRaw","reference","titleQuote","titleApostrophe"],Ih=[{character:"	",after:"[\\r\\n]",inConstruct:"phrasing"},{character:"	",before:"[\\r\\n]",inConstruct:"phrasing"},{character:"	",inConstruct:["codeFencedLangGraveAccent","codeFencedLangTilde"]},{character:"\r",inConstruct:["codeFencedLangGraveAccent","codeFencedLangTilde","codeFencedMetaGraveAccent","codeFencedMetaTilde","destinationLiteral","headingAtx"]},{character:`
`,inConstruct:["codeFencedLangGraveAccent","codeFencedLangTilde","codeFencedMetaGraveAccent","codeFencedMetaTilde","destinationLiteral","headingAtx"]},{character:" ",after:"[\\r\\n]",inConstruct:"phrasing"},{character:" ",before:"[\\r\\n]",inConstruct:"phrasing"},{character:" ",inConstruct:["codeFencedLangGraveAccent","codeFencedLangTilde"]},{character:"!",after:"\\[",inConstruct:"phrasing",notInConstruct:cr},{character:'"',inConstruct:"titleQuote"},{atBreak:!0,character:"#"},{character:"#",inConstruct:"headingAtx",after:`(?:[\r
]|$)`},{character:"&",after:"[#A-Za-z]",inConstruct:"phrasing"},{character:"'",inConstruct:"titleApostrophe"},{character:"(",inConstruct:"destinationRaw"},{before:"\\]",character:"(",inConstruct:"phrasing",notInConstruct:cr},{atBreak:!0,before:"\\d+",character:")"},{character:")",inConstruct:"destinationRaw"},{atBreak:!0,character:"*",after:`(?:[ 	\r
*])`},{character:"*",inConstruct:"phrasing",notInConstruct:cr},{atBreak:!0,character:"+",after:`(?:[ 	\r
])`},{atBreak:!0,character:"-",after:`(?:[ 	\r
-])`},{atBreak:!0,before:"\\d+",character:".",after:`(?:[ 	\r
]|$)`},{atBreak:!0,character:"<",after:"[!/?A-Za-z]"},{character:"<",after:"[!/?A-Za-z]",inConstruct:"phrasing",notInConstruct:cr},{character:"<",inConstruct:"destinationLiteral"},{atBreak:!0,character:"="},{atBreak:!0,character:">"},{character:">",inConstruct:"destinationLiteral"},{atBreak:!0,character:"["},{character:"[",inConstruct:"phrasing",notInConstruct:cr},{character:"[",inConstruct:["label","reference"]},{character:"\\",after:"[\\r\\n]",inConstruct:"phrasing"},{character:"]",inConstruct:["label","reference"]},{atBreak:!0,character:"_"},{character:"_",inConstruct:"phrasing",notInConstruct:cr},{atBreak:!0,character:"`"},{character:"`",inConstruct:["codeFencedLangGraveAccent","codeFencedMetaGraveAccent"]},{character:"`",inConstruct:"phrasing",notInConstruct:cr},{atBreak:!0,character:"~"}]});function Rh(e){return e.label||!e.identifier?e.label||"":Ni(e.identifier)}var qh=S(()=>{is()});function jh(e){if(!e._compiled){let t=(e.atBreak?"[\\r\\n][\\t ]*":"")+(e.before?"(?:"+e.before+")":"");e._compiled=new RegExp((t?"("+t+")":"")+(/[|\\{}()[\]^$+*?.-]/.test(e.character)?"\\":"")+e.character+(e.after?"(?:"+e.after+")":""),"g")}return e._compiled}var Bh=S(()=>{});function Yh(e,t,r){let n=t.indexStack,a=e.children||[],s=[],l=-1,c=r.before,d;n.push(-1);let p=t.createTracker(r);for(;++l<a.length;){let h=a[l],m;if(n[n.length-1]=l,l+1<a.length){let v=t.handle.handlers[a[l+1].type];v&&v.peek&&(v=v.peek),m=v?v(a[l+1],e,t,{before:"",after:"",...p.current()}).charAt(0):""}else m=r.after;s.length>0&&(c==="\r"||c===`
`)&&h.type==="html"&&(s[s.length-1]=s[s.length-1].replace(/(\r?\n|\r)$/," "),c=" ",p=t.createTracker(r),p.move(s.join("")));let y=t.handle(h,e,t,{...p.current(),after:m,before:c});d&&d===y.slice(0,1)&&(y=$e(d.charCodeAt(0))+y.slice(1));let w=t.attentionEncodeSurroundingInfo;t.attentionEncodeSurroundingInfo=void 0,d=void 0,w&&(s.length>0&&w.before&&c===s[s.length-1].slice(-1)&&(s[s.length-1]=s[s.length-1].slice(0,-1)+$e(c.charCodeAt(0))),w.after&&(d=m)),p.move(y),s.push(y),c=y.slice(-1)}return n.pop(),s.join("")}var Vh=S(()=>{Mr()});function zh(e,t,r){let n=t.indexStack,a=e.children||[],s=t.createTracker(r),l=[],c=-1;for(n.push(-1);++c<a.length;){let d=a[c];n[n.length-1]=c,l.push(s.move(t.handle(d,e,t,{before:`
`,after:`
`,...s.current()}))),d.type!=="list"&&(t.bulletLastUsed=void 0),c<a.length-1&&l.push(s.move(QC(d,a[c+1],e,t)))}return n.pop(),l.join("")}function QC(e,t,r,n){let a=n.join.length;for(;a--;){let s=n.join[a](e,t,r,n);if(s===!0||s===1)break;if(typeof s=="number")return`
`.repeat(1+s);if(s===!1)return`

<!---->

`}return`

`}var Uh=S(()=>{});function Wh(e,t){let r=[],n=0,a=0,s;for(;s=ZC.exec(e);)l(e.slice(n,s.index)),r.push(s[0]),n=s.index+s[0].length,a++;return l(e.slice(n)),r.join("");function l(c){r.push(t(c,a,!c))}}var ZC,Hh=S(()=>{ZC=/\r?\n|\r/g});function $h(e,t,r){let n=(r.before||"")+(t||"")+(r.after||""),a=[],s=[],l={},c=-1;for(;++c<e.unsafe.length;){let h=e.unsafe[c];if(!Ri(e.stack,h))continue;let m=e.compilePattern(h),y;for(;y=m.exec(n);){let w="before"in h||!!h.atBreak,v="after"in h,x=y.index+(w?y[1].length:0);a.includes(x)?(l[x].before&&!w&&(l[x].before=!1),l[x].after&&!v&&(l[x].after=!1)):(a.push(x),l[x]={before:w,after:v})}}a.sort(KC);let d=r.before?r.before.length:0,p=n.length-(r.after?r.after.length:0);for(c=-1;++c<a.length;){let h=a[c];h<d||h>=p||h+1<p&&a[c+1]===h+1&&l[h].after&&!l[h+1].before&&!l[h+1].after||a[c-1]===h-1&&l[h].before&&!l[h-1].before&&!l[h-1].after||(d!==h&&s.push(Gh(n.slice(d,h),"\\")),d=h,/[!-/:-@[-`{-~]/.test(n.charAt(h))&&(!r.encode||!r.encode.includes(n.charAt(h)))?s.push("\\"):(s.push($e(n.charCodeAt(h))),d++))}return s.push(Gh(n.slice(d,p),r.after)),s.join("")}function KC(e,t){return e-t}function Gh(e,t){let r=/\\(?=[!-/:-@[-`{-~])/g,n=[],a=[],s=e+t,l=-1,c=0,d;for(;d=r.exec(s);)n.push(d.index);for(;++l<n.length;)c!==n[l]&&a.push(e.slice(c,n[l])),a.push("\\"),c=n[l];return a.push(e.slice(c)),a.join("")}var Jh=S(()=>{Mr();cs()});function Qh(e){let t=e||{},r=t.now||{},n=t.lineShift||0,a=r.line||1,s=r.column||1;return{move:d,current:l,shift:c};function l(){return{now:{line:a,column:s},lineShift:n}}function c(p){n+=p}function d(p){let h=p||"",m=h.split(/\r?\n|\r/g),y=m[m.length-1];return a+=m.length-1,s=m.length===1?s+y.length:1+y.length+n,h}}var Zh=S(()=>{});function As(e,t){let r=t||{},n={associationId:Rh,containerPhrasing:rP,containerFlow:nP,createTracker:Qh,compilePattern:jh,enter:s,handlers:{...Th},handle:void 0,indentLines:Wh,indexStack:[],join:[...Lh],options:{},safe:iP,stack:[],unsafe:[...Ih]};us(n,r),n.options.tightDefinitions&&n.join.push(tP),n.handle=Cd("type",{invalid:XC,unknown:eP,handlers:n.handlers});let a=n.handle(e,void 0,n,{before:`
`,after:`
`,now:{line:1,column:1},lineShift:0});return a&&a.charCodeAt(a.length-1)!==10&&a.charCodeAt(a.length-1)!==13&&(a+=`
`),a;function s(l){return n.stack.push(l),c;function c(){n.stack.pop()}}}function XC(e){throw new Error("Cannot handle value `"+e+"`, expected node")}function eP(e){let t=e;throw new Error("Cannot handle unknown node `"+t.type+"`")}function tP(e,t){if(e.type==="definition"&&e.type===t.type)return 0}function rP(e,t){return Yh(e,this,t)}function nP(e,t){return zh(e,this,t)}function iP(e,t){return $h(this,e,t)}var Kh=S(()=>{Pd();Fd();Dh();Mh();Nh();qh();Bh();Vh();Uh();Hh();Jh();Zh()});var Xh=S(()=>{Kh()});function Wi(e){let t=this;t.compiler=r;function r(n){return As(n,{...t.data("settings"),...e,extensions:t.data("toMarkdownExtensions")||[]})}}var ep=S(()=>{Xh()});var tp=S(()=>{ep()});function Ts(e){if(e)throw e}var rp=S(()=>{});var fp=$((_N,cp)=>{"use strict";var Hi=Object.prototype.hasOwnProperty,up=Object.prototype.toString,np=Object.defineProperty,ip=Object.getOwnPropertyDescriptor,ap=function(t){return typeof Array.isArray=="function"?Array.isArray(t):up.call(t)==="[object Array]"},op=function(t){if(!t||up.call(t)!=="[object Object]")return!1;var r=Hi.call(t,"constructor"),n=t.constructor&&t.constructor.prototype&&Hi.call(t.constructor.prototype,"isPrototypeOf");if(t.constructor&&!r&&!n)return!1;var a;for(a in t);return typeof a>"u"||Hi.call(t,a)},sp=function(t,r){np&&r.name==="__proto__"?np(t,r.name,{enumerable:!0,configurable:!0,value:r.newValue,writable:!0}):t[r.name]=r.newValue},lp=function(t,r){if(r==="__proto__")if(Hi.call(t,r)){if(ip)return ip(t,r).value}else return;return t[r]};cp.exports=function e(){var t,r,n,a,s,l,c=arguments[0],d=1,p=arguments.length,h=!1;for(typeof c=="boolean"&&(h=c,c=arguments[1]||{},d=2),(c==null||typeof c!="object"&&typeof c!="function")&&(c={});d<p;++d)if(t=arguments[d],t!=null)for(r in t)n=lp(c,r),a=lp(t,r),c!==a&&(h&&a&&(op(a)||(s=ap(a)))?(s?(s=!1,l=n&&ap(n)?n:[]):l=n&&op(n)?n:{},sp(c,{name:r,newValue:e(h,l,a)})):typeof a<"u"&&sp(c,{name:r,newValue:a}));return c}});function bn(e){if(typeof e!="object"||e===null)return!1;let t=Object.getPrototypeOf(e);return(t===null||t===Object.prototype||Object.getPrototypeOf(t)===null)&&!(Symbol.toStringTag in e)&&!(Symbol.iterator in e)}var dp=S(()=>{});function Ds(){let e=[],t={run:r,use:n};return t;function r(...a){let s=-1,l=a.pop();if(typeof l!="function")throw new TypeError("Expected function as last argument, not "+l);c(null,...a);function c(d,...p){let h=e[++s],m=-1;if(d){l(d);return}for(;++m<a.length;)(p[m]===null||p[m]===void 0)&&(p[m]=a[m]);a=p,h?hp(h,c)(...p):l(null,...p)}}function n(a){if(typeof a!="function")throw new TypeError("Expected `middelware` to be a function, not "+a);return e.push(a),t}}function hp(e,t){let r;return n;function n(...l){let c=e.length>l.length,d;c&&l.push(a);try{d=e.apply(this,l)}catch(p){let h=p;if(c&&r)throw h;return a(h)}c||(d&&d.then&&typeof d.then=="function"?d.then(s,a):d instanceof Error?a(d):s(d))}function a(l,...c){r||(r=!0,t(l,...c))}function s(l){a(null,l)}}var pp=S(()=>{});var mp=S(()=>{pp()});var Se,gp=S(()=>{os();Se=class extends Error{constructor(t,r,n){super(),typeof r=="string"&&(n=r,r=void 0);let a="",s={},l=!1;if(r&&("line"in r&&"column"in r?s={place:r}:"start"in r&&"end"in r?s={place:r}:"type"in r?s={ancestors:[r],place:r.position}:s={...r}),typeof t=="string"?a=t:!s.cause&&t&&(l=!0,a=t.message,s.cause=t),!s.ruleId&&!s.source&&typeof n=="string"){let d=n.indexOf(":");d===-1?s.ruleId=n:(s.source=n.slice(0,d),s.ruleId=n.slice(d+1))}if(!s.place&&s.ancestors&&s.ancestors){let d=s.ancestors[s.ancestors.length-1];d&&(s.place=d.position)}let c=s.place&&"start"in s.place?s.place.start:s.place;this.ancestors=s.ancestors||void 0,this.cause=s.cause||void 0,this.column=c?c.column:void 0,this.fatal=void 0,this.file,this.message=a,this.line=c?c.line:void 0,this.name=zt(s.place)||"1:1",this.place=s.place||void 0,this.reason=this.message,this.ruleId=s.ruleId||void 0,this.source=s.source||void 0,this.stack=l&&s.cause&&typeof s.cause.stack=="string"?s.cause.stack:"",this.actual,this.expected,this.note,this.url}};Se.prototype.file="";Se.prototype.name="";Se.prototype.reason="";Se.prototype.message="";Se.prototype.stack="";Se.prototype.column=void 0;Se.prototype.line=void 0;Se.prototype.ancestors=void 0;Se.prototype.cause=void 0;Se.prototype.fatal=void 0;Se.prototype.place=void 0;Se.prototype.ruleId=void 0;Se.prototype.source=void 0});var yp=S(()=>{gp()});var tt,wp=S(()=>{tt=ee(require("node:path"),1)});var Ls,AN,bp=S(()=>{Ls=ee(require("node:process"),1),AN=globalThis.process??{browser:!0,cwd:()=>"/",env:{},platform:"android"}});function Gi(e){return!!(e!==null&&typeof e=="object"&&"href"in e&&e.href&&"protocol"in e&&e.protocol&&e.auth===void 0)}var _p=S(()=>{});var Ms,kp=S(()=>{Ms=require("node:url");_p()});function Ns(e,t){if(e&&e.includes(tt.default.sep))throw new Error("`"+t+"` cannot be a path: did not expect `"+tt.default.sep+"`")}function Rs(e,t){if(!e)throw new Error("`"+t+"` cannot be empty")}function vp(e,t){if(!e)throw new Error("Setting `"+t+"` requires `path` to be set too")}function aP(e){return!!(e&&typeof e=="object"&&"byteLength"in e&&"byteOffset"in e)}var IN,Is,_n,xp=S(()=>{yp();wp();bp();kp();IN=globalThis.process??{browser:!0,cwd:()=>"/",env:{},platform:"android"},Is=["history","path","basename","stem","extname","dirname"],_n=class{constructor(t){let r;t?Gi(t)?r={path:t}:typeof t=="string"||aP(t)?r={value:t}:r=t:r={},this.cwd="cwd"in r?"":Ls.default.cwd(),this.data={},this.history=[],this.messages=[],this.value,this.map,this.result,this.stored;let n=-1;for(;++n<Is.length;){let s=Is[n];s in r&&r[s]!==void 0&&r[s]!==null&&(this[s]=s==="history"?[...r[s]]:r[s])}let a;for(a in r)Is.includes(a)||(this[a]=r[a])}get basename(){return typeof this.path=="string"?tt.default.basename(this.path):void 0}set basename(t){Rs(t,"basename"),Ns(t,"basename"),this.path=tt.default.join(this.dirname||"",t)}get dirname(){return typeof this.path=="string"?tt.default.dirname(this.path):void 0}set dirname(t){vp(this.basename,"dirname"),this.path=tt.default.join(t||"",this.basename)}get extname(){return typeof this.path=="string"?tt.default.extname(this.path):void 0}set extname(t){if(Ns(t,"extname"),vp(this.dirname,"extname"),t){if(t.codePointAt(0)!==46)throw new Error("`extname` must start with `.`");if(t.includes(".",1))throw new Error("`extname` cannot contain multiple dots")}this.path=tt.default.join(this.dirname,this.stem+(t||""))}get path(){return this.history[this.history.length-1]}set path(t){Gi(t)&&(t=(0,Ms.fileURLToPath)(t)),Rs(t,"path"),this.path!==t&&this.history.push(t)}get stem(){return typeof this.path=="string"?tt.default.basename(this.path,this.extname):void 0}set stem(t){Rs(t,"stem"),Ns(t,"stem"),this.path=tt.default.join(this.dirname||"",t+(this.extname||""))}fail(t,r,n){let a=this.message(t,r,n);throw a.fatal=!0,a}info(t,r,n){let a=this.message(t,r,n);return a.fatal=void 0,a}message(t,r,n){let a=new Se(t,r,n);return this.path&&(a.name=this.path+":"+a.name,a.file=this.path),a.fatal=!1,this.messages.push(a),a}toString(t){return this.value===void 0?"":typeof this.value=="string"?this.value:new TextDecoder(t||void 0).decode(this.value)}}});var Sp=S(()=>{xp()});var Cp,Pp=S(()=>{Cp=function(e){let n=this.constructor.prototype,a=n[e],s=function(){return a.apply(s,arguments)};return Object.setPrototypeOf(s,n),s}});function qs(e,t){if(typeof t!="function")throw new TypeError("Cannot `"+e+"` without `parser`")}function js(e,t){if(typeof t!="function")throw new TypeError("Cannot `"+e+"` without `compiler`")}function Bs(e,t){if(t)throw new Error("Cannot call `"+e+"` on a frozen processor.\nCreate a new processor first, by calling it: use `processor()` instead of `processor`.")}function Op(e){if(!bn(e)||typeof e.type!="string")throw new TypeError("Expected node, got `"+e+"`")}function Fp(e,t,r){if(!r)throw new Error("`"+e+"` finished async. Use `"+t+"` instead")}function $i(e){return sP(e)?e:new _n(e)}function sP(e){return!!(e&&typeof e=="object"&&"message"in e&&"messages"in e)}function lP(e){return typeof e=="string"||uP(e)}function uP(e){return!!(e&&typeof e=="object"&&"byteLength"in e&&"byteOffset"in e)}var Ji,UN,oP,Ys,Vs,Ep=S(()=>{rp();Ji=ee(fp(),1);dp();mp();Sp();Pp();UN=globalThis.process??{browser:!0,cwd:()=>"/",env:{},platform:"android"},oP={}.hasOwnProperty,Ys=class e extends Cp{constructor(){super("copy"),this.Compiler=void 0,this.Parser=void 0,this.attachers=[],this.compiler=void 0,this.freezeIndex=-1,this.frozen=void 0,this.namespace={},this.parser=void 0,this.transformers=Ds()}copy(){let t=new e,r=-1;for(;++r<this.attachers.length;){let n=this.attachers[r];t.use(...n)}return t.data((0,Ji.default)(!0,{},this.namespace)),t}data(t,r){return typeof t=="string"?arguments.length===2?(Bs("data",this.frozen),this.namespace[t]=r,this):oP.call(this.namespace,t)&&this.namespace[t]||void 0:t?(Bs("data",this.frozen),this.namespace=t,this):this.namespace}freeze(){if(this.frozen)return this;let t=this;for(;++this.freezeIndex<this.attachers.length;){let[r,...n]=this.attachers[this.freezeIndex];if(n[0]===!1)continue;n[0]===!0&&(n[0]=void 0);let a=r.call(t,...n);typeof a=="function"&&this.transformers.use(a)}return this.frozen=!0,this.freezeIndex=Number.POSITIVE_INFINITY,this}parse(t){this.freeze();let r=$i(t),n=this.parser||this.Parser;return qs("parse",n),n(String(r),r)}process(t,r){let n=this;return this.freeze(),qs("process",this.parser||this.Parser),js("process",this.compiler||this.Compiler),r?a(void 0,r):new Promise(a);function a(s,l){let c=$i(t),d=n.parse(c);n.run(d,c,function(h,m,y){if(h||!m||!y)return p(h);let w=m,v=n.stringify(w,y);lP(v)?y.value=v:y.result=v,p(h,y)});function p(h,m){h||!m?l(h):s?s(m):r(void 0,m)}}}processSync(t){let r=!1,n;return this.freeze(),qs("processSync",this.parser||this.Parser),js("processSync",this.compiler||this.Compiler),this.process(t,a),Fp("processSync","process",r),n;function a(s,l){r=!0,Ts(s),n=l}}run(t,r,n){Op(t),this.freeze();let a=this.transformers;return!n&&typeof r=="function"&&(n=r,r=void 0),n?s(void 0,n):new Promise(s);function s(l,c){let d=$i(r);a.run(t,d,p);function p(h,m,y){let w=m||t;h?c(h):l?l(w):n(void 0,w,y)}}}runSync(t,r){let n=!1,a;return this.run(t,r,s),Fp("runSync","run",n),a;function s(l,c){Ts(l),a=c,n=!0}}stringify(t,r){this.freeze();let n=$i(r),a=this.compiler||this.Compiler;return js("stringify",a),Op(t),a(t,n)}use(t,...r){let n=this.attachers,a=this.namespace;if(Bs("use",this.frozen),t!=null)if(typeof t=="function")d(t,r);else if(typeof t=="object")Array.isArray(t)?c(t):l(t);else throw new TypeError("Expected usable value, not `"+t+"`");return this;function s(p){if(typeof p=="function")d(p,[]);else if(typeof p=="object")if(Array.isArray(p)){let[h,...m]=p;d(h,m)}else l(p);else throw new TypeError("Expected usable value, not `"+p+"`")}function l(p){if(!("plugins"in p)&&!("settings"in p))throw new Error("Expected usable value but received an empty preset, which is probably a mistake: presets typically come with `plugins` and sometimes with `settings`, but this has neither");c(p.plugins),p.settings&&(a.settings=(0,Ji.default)(!0,a.settings,p.settings))}function c(p){let h=-1;if(p!=null)if(Array.isArray(p))for(;++h<p.length;){let m=p[h];s(m)}else throw new TypeError("Expected a list of plugins, not `"+p+"`")}function d(p,h){let m=-1,y=-1;for(;++m<n.length;)if(n[m][0]===p){y=m;break}if(y===-1)n.push([p,...h]);else if(h.length>0){let[w,...v]=h,x=n[y][1];bn(x)&&bn(w)&&(w=(0,Ji.default)(!0,x,w)),n[y]=[p,w,...v]}}}},Vs=new Ys().freeze()});var Ap=S(()=>{Ep()});var Tp={};ti(Tp,{remark:()=>cP});var XN,cP,Dp=S(()=>{ls();tp();Ap();XN=globalThis.process??{browser:!0,cwd:()=>"/",env:{},platform:"android"},cP=Vs().use(yn).use(Wi).freeze()});var Mp=$((nR,Lp)=>{(()=>{"use strict";var e={d:(v,x)=>{for(var E in x)e.o(x,E)&&!e.o(v,E)&&Object.defineProperty(v,E,{enumerable:!0,get:x[E]})},o:(v,x)=>Object.prototype.hasOwnProperty.call(v,x),r:v=>{typeof Symbol<"u"&&Symbol.toStringTag&&Object.defineProperty(v,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(v,"__esModule",{value:!0})}},t={};e.r(t),e.d(t,{default:()=>w,wikiLinkPlugin:()=>y});var r={horizontalTab:-2,virtualSpace:-1,nul:0,eof:null,space:32};function n(v){return v<r.nul||v===r.space}function a(v){return v<r.horizontalTab}var s={553:v=>{v.exports=function(x){var E,O;return x._compiled||(E=x.before?"(?:"+x.before+")":"",O=x.after?"(?:"+x.after+")":"",x.atBreak&&(E="[\\r\\n][\\t ]*"+E),x._compiled=new RegExp((E?"("+E+")":"")+(/[|\\{}()[\]^$+*?.-]/.test(x.character)?"\\":"")+x.character+(O||""),"g")),x._compiled}},112:v=>{function x(E,O,z){var A;if(!O)return z;for(typeof O=="string"&&(O=[O]),A=-1;++A<O.length;)if(E.indexOf(O[A])!==-1)return!0;return!1}v.exports=function(E,O){return x(E,O.inConstruct,!0)&&!x(E,O.notInConstruct)}},113:(v,x,E)=>{v.exports=function(W,P,G){for(var B,U,L,H,K,N,Y,V,M=(G.before||"")+(P||"")+(G.after||""),re=[],se=[],he={},_=-1;++_<W.unsafe.length;)if(H=W.unsafe[_],z(W.stack,H))for(K=O(H);N=K.exec(M);)B="before"in H||H.atBreak,U="after"in H,L=N.index+(B?N[1].length:0),re.indexOf(L)===-1?(re.push(L),he[L]={before:B,after:U}):(he[L].before&&!B&&(he[L].before=!1),he[L].after&&!U&&(he[L].after=!1));for(re.sort(A),Y=G.before?G.before.length:0,V=M.length-(G.after?G.after.length:0),_=-1;++_<re.length;)(L=re[_])<Y||L>=V||L+1<V&&re[_+1]===L+1&&he[L].after&&!he[L+1].before&&!he[L+1].after||(Y!==L&&se.push(Z(M.slice(Y,L),"\\")),Y=L,!/[!-/:-@[-`{-~]/.test(M.charAt(L))||G.encode&&G.encode.indexOf(M.charAt(L))!==-1?(se.push("&#x"+M.charCodeAt(L).toString(16).toUpperCase()+";"),Y++):se.push("\\"));return se.push(Z(M.slice(Y,V),G.after)),se.join("")};var O=E(553),z=E(112);function A(W,P){return W-P}function Z(W,P){for(var G,B=/\\(?=[!-/:-@[-`{-~])/g,U=[],L=[],H=-1,K=0,N=W+P;G=B.exec(N);)U.push(G.index);for(;++H<U.length;)K!==U[H]&&L.push(W.slice(K,U[H])),L.push("\\"),K=U[H];return L.push(W.slice(K)),L.join("")}}},l={};function c(v){var x=l[v];if(x!==void 0)return x.exports;var E=l[v]={exports:{}};return s[v](E,E.exports,c),E.exports}c.n=v=>{var x=v&&v.__esModule?()=>v.default:()=>v;return c.d(x,{a:x}),x},c.d=(v,x)=>{for(var E in x)c.o(x,E)&&!c.o(v,E)&&Object.defineProperty(v,E,{enumerable:!0,get:x[E]})},c.o=(v,x)=>Object.prototype.hasOwnProperty.call(v,x);var d={};(()=>{function v(z={}){let A=z.permalinks||[],Z=z.pageResolver||(L=>[L.replace(/ /g,"_").toLowerCase()]),W=z.newClassName||"new",P=z.wikiLinkClassName||"internal",G=z.hrefTemplate||(L=>`#/page/${L}`),B;function U(L){return L[L.length-1]}return{enter:{wikiLink:function(L){B={type:"wikiLink",value:null,data:{alias:null,permalink:null,exists:null}},this.enter(B,L)}},exit:{wikiLinkTarget:function(L){let H=this.sliceSerialize(L);U(this.stack).value=H},wikiLinkAlias:function(L){let H=this.sliceSerialize(L);U(this.stack).data.alias=H},wikiLink:function(L){this.exit(L);let H=B,K=Z(H.value),N=K.find(se=>A.indexOf(se)!==-1),Y=N!==void 0,V;V=Y?N:K[0]||"";let M=H.value;H.data.alias&&(M=H.data.alias);let re=P;Y||(re+=" "+W),H.data.alias=M,H.data.permalink=V,H.data.exists=Y,H.data.hName="a",H.data.hProperties={className:re,href:G(V)},H.data.hChildren=[{type:"text",value:M}]}}}}c.d(d,{V:()=>v,x:()=>O});var x=c(113),E=c.n(x);function O(z={}){let A=z.aliasDivider||":";return{unsafe:[{character:"[",inConstruct:["phrasing","label","reference"]},{character:"]",inConstruct:["label","reference"]}],handlers:{wikiLink:function(Z,W,P){let G=P.enter("wikiLink"),B=E()(P,Z.value,{before:"[",after:"]"}),U=E()(P,Z.data.alias,{before:"[",after:"]"}),L;return L=U!==B?`[[${B}${A}${U}]]`:`[[${B}]]`,G(),L}}}}})();var p=d.V,h=d.x;let m=!1;function y(v={}){let x=this.data();function E(O,z){x[O]?x[O].push(z):x[O]=[z]}!m&&(this.Parser&&this.Parser.prototype&&this.Parser.prototype.blockTokenizers||this.Compiler&&this.Compiler.prototype&&this.Compiler.prototype.visitors)&&(m=!0,console.warn("[remark-wiki-link] Warning: please upgrade to remark 13 to use this plugin")),E("micromarkExtensions",function(){var O=(arguments.length>0&&arguments[0]!==void 0?arguments[0]:{}).aliasDivider||":",z="]]";return{text:{91:{tokenize:function(A,Z,W){var P,G,B=0,U=0,L=0;return function(M){return M!=="[[".charCodeAt(U)?W(M):(A.enter("wikiLink"),A.enter("wikiLinkMarker"),H(M))};function H(M){return U===2?(A.exit("wikiLinkMarker"),function(re){return a(re)||re===r.eof?W(re):(A.enter("wikiLinkData"),A.enter("wikiLinkTarget"),K(re))}(M)):M!=="[[".charCodeAt(U)?W(M):(A.consume(M),U++,H)}function K(M){return M===O.charCodeAt(B)?P?(A.exit("wikiLinkTarget"),A.enter("wikiLinkAliasMarker"),N(M)):W(M):M===z.charCodeAt(L)?P?(A.exit("wikiLinkTarget"),A.exit("wikiLinkData"),A.enter("wikiLinkMarker"),V(M)):W(M):a(M)||M===r.eof?W(M):(n(M)||(P=!0),A.consume(M),K)}function N(M){return B===O.length?(A.exit("wikiLinkAliasMarker"),A.enter("wikiLinkAlias"),Y(M)):M!==O.charCodeAt(B)?W(M):(A.consume(M),B++,N)}function Y(M){return M===z.charCodeAt(L)?G?(A.exit("wikiLinkAlias"),A.exit("wikiLinkData"),A.enter("wikiLinkMarker"),V(M)):W(M):a(M)||M===r.eof?W(M):(n(M)||(G=!0),A.consume(M),Y)}function V(M){return L===2?(A.exit("wikiLinkMarker"),A.exit("wikiLink"),Z(M)):M!==z.charCodeAt(L)?W(M):(A.consume(M),L++,V)}}}}}}(v)),E("fromMarkdownExtensions",p(v)),E("toMarkdownExtensions",h(v))}let w=y;Lp.exports=t})()});var Rp=$((iR,Np)=>{function fP(e){return e&&e.__esModule&&e.default?e.default:e}(function(){let t=require;require=Object.assign(r=>{let n=t(r)??{};return fP(n)},t)})();var zs=Object.defineProperty,dP=Object.getOwnPropertyDescriptor,hP=Object.getOwnPropertyNames,pP=Object.prototype.hasOwnProperty,mP=(e,t)=>{for(var r in t)zs(e,r,{get:t[r],enumerable:!0})},gP=(e,t,r,n)=>{if(t&&typeof t=="object"||typeof t=="function")for(let a of hP(t))!pP.call(e,a)&&a!==r&&zs(e,a,{get:()=>t[a],enumerable:!(n=dP(t,a))||n.enumerable});return e},yP=e=>gP(zs({},"__esModule",{value:!0}),e),Ip={};mP(Ip,{isUrl:()=>wP});Np.exports=yP(Ip);function wP(e){try{return!e.includes("://")||e.trim()!==e?!1:(new URL(e),!0)}catch{return!1}}});var Ws=$((oR,jp)=>{function bP(e){return e&&e.__esModule&&e.default?e.default:e}(function(){let t=require;require=Object.assign(r=>{let n=t(r)??{};return bP(n)},t)})();var Us=Object.defineProperty,_P=Object.getOwnPropertyDescriptor,kP=Object.getOwnPropertyNames,vP=Object.prototype.hasOwnProperty,xP=(e,t)=>{for(var r in t)Us(e,r,{get:t[r],enumerable:!0})},SP=(e,t,r,n)=>{if(t&&typeof t=="object"||typeof t=="function")for(let a of kP(t))!vP.call(e,a)&&a!==r&&Us(e,a,{get:()=>t[a],enumerable:!(n=_P(t,a))||n.enumerable});return e},CP=e=>SP(Us({},"__esModule",{value:!0}),e),qp={};xP(qp,{parseFrontmatter:()=>OP,setFrontmatter:()=>FP});jp.exports=CP(qp);var Qi=require("obsidian"),PP=Et(),aR=globalThis.process??{browser:!0,cwd:()=>"/",env:{},platform:"android"};function OP(e){let t=(0,Qi.getFrontMatterInfo)(e);return(0,Qi.parseYaml)(t.frontmatter)??{}}function FP(e,t){let r=(0,Qi.getFrontMatterInfo)(e);if(Object.keys(t).length===0)return e.slice(r.contentStart);let n=(0,Qi.stringifyYaml)(t);return r.exists?(0,PP.insertAt)(e,n,r.from,r.to):`---
`+n+`---
`+e}});var kn=$((sR,Up)=>{function EP(e){return e&&e.__esModule&&e.default?e.default:e}(function(){let t=require;require=Object.assign(r=>{let n=t(r)??{};return EP(n)},t)})();var $s=Object.defineProperty,AP=Object.getOwnPropertyDescriptor,TP=Object.getOwnPropertyNames,DP=Object.prototype.hasOwnProperty,LP=(e,t)=>{for(var r in t)$s(e,r,{get:t[r],enumerable:!0})},MP=(e,t,r,n)=>{if(t&&typeof t=="object"||typeof t=="function")for(let a of TP(t))!DP.call(e,a)&&a!==r&&$s(e,a,{get:()=>t[a],enumerable:!(n=AP(t,a))||n.enumerable});return e},IP=e=>MP($s({},"__esModule",{value:!0}),e),Bp={};LP(Bp,{addErrorHandler:()=>Yp,asyncFilter:()=>NP,asyncFlatMap:()=>RP,asyncMap:()=>Js,convertAsyncToSync:()=>qP,convertSyncToAsync:()=>jP,invokeAsyncSafely:()=>Vp,marksAsTerminateRetry:()=>BP,retryWithTimeout:()=>YP,runWithTimeout:()=>zp,sleep:()=>Ki,timeout:()=>VP,toArray:()=>zP});Up.exports=IP(Bp);var Hs=ir(),Gs=mt(),Zi=(0,Hs.getDebugger)("obsidian-dev-utils:Async:retryWithTimeout");async function Yp(e){try{await e()}catch(t){(0,Gs.emitAsyncErrorEvent)(t)}}async function NP(e,t){let r=await Js(e,t);return e.filter((n,a)=>r[a]??!1)}async function RP(e,t){return(await Js(e,t)).flat()}async function Js(e,t){return await Promise.all(e.map(t))}function qP(e){return(...t)=>{Vp(()=>e(...t))}}function jP(e){return(...t)=>Promise.resolve().then(()=>e(...t))}function Vp(e){Yp(e)}function BP(e){return Object.assign(e,{__terminateRetry:!0})}async function YP(e,t={},r){r??=(0,Gs.getStackTrace)(1);let a={...{retryDelayInMilliseconds:100,shouldRetryOnError:!1,timeoutInMilliseconds:5e3},...t};await zp(a.timeoutInMilliseconds,async()=>{let s=0;for(;;){a.abortSignal?.throwIfAborted(),s++;let l;try{l=await e()}catch(c){if(!a.shouldRetryOnError||c.__terminateRetry)throw c;(0,Gs.printError)(c),l=!1}if(l){s>1&&(Zi(`Retry completed successfully after ${s.toString()} attempts`),Zi.printStackTrace(r));return}Zi(`Retry attempt ${s.toString()} completed unsuccessfully. Trying again in ${a.retryDelayInMilliseconds.toString()} milliseconds`,{fn:e}),Zi.printStackTrace(r),await Ki(a.retryDelayInMilliseconds)}})}async function zp(e,t){let r=!0,n=null,a=performance.now();if(await Promise.race([s(),l()]),r)throw new Error("Timed out");return n;async function s(){n=await t(),r=!1;let c=performance.now()-a;(0,Hs.getDebugger)("obsidian-dev-utils:Async:runWithTimeout")(`Execution time: ${c.toString()} milliseconds`,{fn:t})}async function l(){if(!r||(await Ki(e),!r))return;let c=performance.now()-a;console.warn(`Timed out in ${c.toString()} milliseconds`,{fn:t}),(0,Hs.getDebugger)("obsidian-dev-utils:Async:timeout").enabled&&(console.warn("The execution is not terminated because debugger obsidian-dev-utils:Async:timeout is enabled. See window.DEBUG.enable('obsidian-dev-utils:Async:timeout') and https://github.com/debug-js/debug?tab=readme-ov-file for more information"),await l())}}async function Ki(e){await new Promise(t=>setTimeout(t,e))}async function VP(e){throw await Ki(e),new Error(`Timed out in ${e.toString()} milliseconds`)}async function zP(e){let t=[];for await(let r of e)t.push(r);return t}});var fr=$((cR,Zp)=>{function UP(e){return e&&e.__esModule&&e.default?e.default:e}(function(){let t=require;require=Object.assign(r=>{let n=t(r)??{};return UP(n)},t)})();var Qs=Object.defineProperty,WP=Object.getOwnPropertyDescriptor,HP=Object.getOwnPropertyNames,GP=Object.prototype.hasOwnProperty,$P=(e,t)=>{for(var r in t)Qs(e,r,{get:t[r],enumerable:!0})},JP=(e,t,r,n)=>{if(t&&typeof t=="object"||typeof t=="function")for(let a of HP(t))!GP.call(e,a)&&a!==r&&Qs(e,a,{get:()=>t[a],enumerable:!(n=WP(t,a))||n.enumerable});return e},QP=e=>JP(Qs({},"__esModule",{value:!0}),e),Wp={};$P(Wp,{copySafe:()=>XP,createFolderSafe:()=>ea,createTempFile:()=>eO,createTempFolder:()=>Xi,getAvailablePath:()=>Zs,getMarkdownFilesSorted:()=>tO,getNoteFilesSorted:()=>rO,getSafeRenamePath:()=>Gp,isEmptyFolder:()=>nO,listSafe:()=>$p,process:()=>iO,readSafe:()=>Jp,renameSafe:()=>aO});Zp.exports=QP(Wp);var lR=require("obsidian"),vn=er(),ZP=kn(),Hp=Fr(),gt=ct(),KP=ai(),Le=Xe(),uR=globalThis.process??{browser:!0,cwd:()=>"/",env:{},platform:"android"};async function XP(e,t,r){let n=(0,Le.getFile)(e,t),a=(0,vn.parentFolderPath)(r);await ea(e,a);let s=Zs(e,r);try{await e.vault.copy(n,s)}catch(l){if(!await e.vault.exists(s))throw l}return s}async function ea(e,t){if(await e.vault.adapter.exists(t))return!1;try{return await e.vault.createFolder(t),!0}catch(r){if(!await e.vault.exists(t))throw r;return!0}}async function eO(e,t){let r=(0,Le.getFileOrNull)(e,t);if(r)return Hp.noopAsync;let n=await Xi(e,(0,vn.parentFolderPath)(t));try{await e.vault.create(t,"")}catch(a){if(!await e.vault.exists(t))throw a}return r=(0,Le.getFile)(e,t),async()=>{r.deleted||await e.fileManager.trashFile(r),await n()}}async function Xi(e,t){let r=(0,Le.getFolderOrNull)(e,t);if(r)return Hp.noopAsync;let n=(0,vn.parentFolderPath)(t);await Xi(e,n);let a=await Xi(e,(0,vn.parentFolderPath)(t));return await ea(e,t),r=(0,Le.getFolder)(e,t),async()=>{r.deleted||await e.fileManager.trashFile(r),await a()}}function Zs(e,t){let r=(0,gt.extname)(t);return e.vault.getAvailablePath((0,gt.join)((0,gt.dirname)(t),(0,gt.basename)(t,r)),r.slice(1))}function tO(e){return e.vault.getMarkdownFiles().sort((t,r)=>t.path.localeCompare(r.path))}function rO(e){return e.vault.getAllLoadedFiles().filter(t=>(0,Le.isFile)(t)&&(0,Le.isNote)(e,t)).sort((t,r)=>t.path.localeCompare(r.path))}function Gp(e,t,r){let n=(0,Le.getPath)(e,t);if(e.vault.adapter.insensitive){let a=(0,gt.dirname)(r),s=(0,gt.basename)(r),l=null;for(;l=(0,Le.getFolderOrNull)(e,a,!0),!l;)s=(0,gt.join)((0,gt.basename)(a),s),a=(0,gt.dirname)(a);r=(0,gt.join)(l.getParentPrefix(),s)}return n.toLowerCase()===r.toLowerCase()?r:Zs(e,r)}async function nO(e,t){let r=await $p(e,(0,Le.getPath)(e,t));return r.files.length===0&&r.folders.length===0}async function $p(e,t){let r=(0,Le.getPath)(e,t),n={files:[],folders:[]};if((await e.vault.adapter.stat(r))?.type!=="folder")return n;try{return await e.vault.adapter.list(r)}catch(a){if(await e.vault.exists(r))throw a;return n}}async function iO(e,t,r,n={}){let s={...{shouldFailOnMissingFile:!0},...n};await(0,ZP.retryWithTimeout)(async()=>{let l=await Jp(e,t);if(l===null)return h();let c=await(0,KP.resolveValue)(r,l);if(c===null)return!1;let d=!0;if(!await Qp(e,t,async m=>{await e.vault.process(m,y=>y!==l?(console.warn("Content has changed since it was read. Retrying...",{actualContent:y,expectedContent:l,path:m.path}),d=!1,y):c)}))return h();return d;function h(){if(s.shouldFailOnMissingFile){let m=(0,Le.getPath)(e,t);throw new Error(`File '${m}' not found`)}return!0}},s)}async function Jp(e,t){let r=null;return await Qp(e,t,async n=>{r=await e.vault.read(n)}),r}async function aO(e,t,r){let n=(0,Le.getFile)(e,t,!1,!0),a=Gp(e,t,r);if(n.path.toLowerCase()===a.toLowerCase())return n.path!==r&&await e.vault.rename(n,a),a;let s=(0,vn.parentFolderPath)(a);await ea(e,s);try{await e.vault.rename(n,a)}catch(l){if(!await e.vault.exists(a)||await e.vault.exists(n.path))throw l}return a}async function Qp(e,t,r){let n=(0,Le.getPath)(e,t),a=(0,Le.getFileOrNull)(e,n);if(!a||a.deleted)return!1;try{return await r(a),!0}catch(s){let l=(0,Le.getFileOrNull)(e,n);if(!l||l.deleted)return!1;throw s}}});var el=$((dR,em)=>{function oO(e){return e&&e.__esModule&&e.default?e.default:e}(function(){let t=require;require=Object.assign(r=>{let n=t(r)??{};return oO(n)},t)})();var Xs=Object.defineProperty,sO=Object.getOwnPropertyDescriptor,lO=Object.getOwnPropertyNames,uO=Object.prototype.hasOwnProperty,cO=(e,t)=>{for(var r in t)Xs(e,r,{get:t[r],enumerable:!0})},fO=(e,t,r,n)=>{if(t&&typeof t=="object"||typeof t=="function")for(let a of lO(t))!uO.call(e,a)&&a!==r&&Xs(e,a,{get:()=>t[a],enumerable:!(n=sO(t,a))||n.enumerable});return e},dO=e=>fO(Xs({},"__esModule",{value:!0}),e),Xp={};cO(Xp,{applyFileChanges:()=>mO,isContentChange:()=>Wt,isFrontmatterChange:()=>xn});em.exports=dO(Xp);var Ks=At(),hO=ai(),ta=Xe(),Kp=Ws(),pO=fr(),fR=globalThis.process??{browser:!0,cwd:()=>"/",env:{},platform:"android"};async function mO(e,t,r,n={}){await(0,pO.process)(e,t,async a=>{let s=await(0,hO.resolveValue)(r),l=(0,ta.isCanvasFile)(e,t)?JSON.parse(a):(0,Kp.parseFrontmatter)(a);for(let h of s)if(Wt(h)){let m=a.slice(h.startIndex,h.endIndex);if(m!==h.oldContent)return console.warn("Content mismatch",{actualContent:m,endIndex:h.endIndex,expectedContent:h.oldContent,path:(0,ta.getPath)(e,t),startIndex:h.startIndex}),null}else if(xn(h)){let m=(0,Ks.getNestedPropertyValue)(l,h.frontmatterKey);if(m!==h.oldContent)return console.warn("Content mismatch",{actualContent:m,expectedContent:h.oldContent,frontmatterKey:h.frontmatterKey,path:(0,ta.getPath)(e,t)}),null}s.sort((h,m)=>Wt(h)&&Wt(m)?h.startIndex-m.startIndex:xn(h)&&xn(m)?h.frontmatterKey.localeCompare(m.frontmatterKey):Wt(h)?-1:1),s=s.filter((h,m)=>h.oldContent===h.newContent?!1:m===0?!0:!(0,Ks.deepEqual)(h,s[m-1]));for(let h=1;h<s.length;h++){let m=s[h];if(!m)continue;let y=s[h-1];if(y&&Wt(y)&&Wt(m)&&y.endIndex&&m.startIndex&&y.endIndex>m.startIndex)return console.warn("Overlapping changes",{change:m,previousChange:y}),null}let c="",d=0,p=!1;for(let h of s)Wt(h)?(c+=a.slice(d,h.startIndex),c+=h.newContent,d=h.endIndex):xn(h)&&((0,Ks.setNestedPropertyValue)(l,h.frontmatterKey,h.newContent),p=!0);return(0,ta.isCanvasFile)(e,t)?c=JSON.stringify(l,null,"	"):(c+=a.slice(d),p&&(c=(0,Kp.setFrontmatter)(c,l))),c},n)}function Wt(e){return e.startIndex!==void 0}function xn(e){return e.frontmatterKey!==void 0}});var ra=$((hR,rm)=>{function gO(e){return e&&e.__esModule&&e.default?e.default:e}(function(){let t=require;require=Object.assign(r=>{let n=t(r)??{};return gO(n)},t)})();var tl=Object.defineProperty,yO=Object.getOwnPropertyDescriptor,wO=Object.getOwnPropertyNames,bO=Object.prototype.hasOwnProperty,_O=(e,t)=>{for(var r in t)tl(e,r,{get:t[r],enumerable:!0})},kO=(e,t,r,n)=>{if(t&&typeof t=="object"||typeof t=="function")for(let a of wO(t))!bO.call(e,a)&&a!==r&&tl(e,a,{get:()=>t[a],enumerable:!(n=yO(t,a))||n.enumerable});return e},vO=e=>kO(tl({},"__esModule",{value:!0}),e),tm={};_O(tm,{referenceToFileChange:()=>xO,sortReferences:()=>SO});rm.exports=vO(tm);var dr=er();function xO(e,t){if((0,dr.isReferenceCache)(e))return{endIndex:e.position.end.offset,newContent:t,oldContent:e.original,startIndex:e.position.start.offset};if((0,dr.isFrontmatterLinkCache)(e))return{frontmatterKey:e.key,newContent:t,oldContent:e.original};throw new Error("Unknown link type")}function SO(e){return e.sort((t,r)=>(0,dr.isFrontmatterLinkCache)(t)&&(0,dr.isFrontmatterLinkCache)(r)?t.key.localeCompare(r.key):(0,dr.isReferenceCache)(t)&&(0,dr.isReferenceCache)(r)?t.position.start.offset-r.position.start.offset:(0,dr.isFrontmatterLinkCache)(t)?1:-1)}});var Cn=$((mR,cm)=>{function CO(e){return e&&e.__esModule&&e.default?e.default:e}(function(){let t=require;require=Object.assign(r=>{let n=t(r)??{};return CO(n)},t)})();var rl=Object.defineProperty,PO=Object.getOwnPropertyDescriptor,OO=Object.getOwnPropertyNames,FO=Object.prototype.hasOwnProperty,EO=(e,t)=>{for(var r in t)rl(e,r,{get:t[r],enumerable:!0})},AO=(e,t,r,n)=>{if(t&&typeof t=="object"||typeof t=="function")for(let a of OO(t))!FO.call(e,a)&&a!==r&&rl(e,a,{get:()=>t[a],enumerable:!(n=PO(t,a))||n.enumerable});return e},TO=e=>AO(rl({},"__esModule",{value:!0}),e),nm={};EO(nm,{ensureMetadataCacheReady:()=>am,getAllLinks:()=>jO,getBacklinksForFileOrPath:()=>om,getBacklinksForFileSafe:()=>BO,getCacheSafe:()=>nl,getFrontmatterSafe:()=>YO,registerFile:()=>sm,tempRegisterFileAndRun:()=>lm});cm.exports=TO(nm);var DO=require("obsidian"),hr=er(),im=kn(),LO=ir(),MO=Fr(),IO=At(),Dt=Xe(),NO=Ws(),RO=ra(),qO=fr(),pR=globalThis.process??{browser:!0,cwd:()=>"/",env:{},platform:"android"},Sn=(0,LO.getDebugger)("obsidian-dev-utils:MetadataCache:getCacheSafe");async function am(e){for(let[t,r]of Object.entries(e.metadataCache.fileCache))r.hash&&(e.metadataCache.metadataCache[r.hash]||await nl(e,t))}function jO(e){let t=[];return e.links&&t.push(...e.links),e.embeds&&t.push(...e.embeds),e.frontmatterLinks&&t.push(...e.frontmatterLinks),(0,RO.sortReferences)(t),t=t.filter((r,n)=>{if(n===0)return!0;let a=t[n-1];return a?(0,hr.isReferenceCache)(r)&&(0,hr.isReferenceCache)(a)?r.position.start.offset!==a.position.start.offset:(0,hr.isFrontmatterLinkCache)(r)&&(0,hr.isFrontmatterLinkCache)(a)?r.key!==a.key:!0:!0}),t}function om(e,t){let r=(0,Dt.getFile)(e,t,!0);return lm(e,r,()=>e.metadataCache.getBacklinksForFile(r))}async function BO(e,t,r={}){let n=e.metadataCache.getBacklinksForFile.safe;if(n)return n(t);let a=null;return await(0,im.retryWithTimeout)(async()=>{let s=(0,Dt.getFile)(e,t);await am(e),a=om(e,s);for(let l of a.keys()){let c=(0,Dt.getFileOrNull)(e,l);if(!c)return!1;await um(e,c);let d=await(0,qO.readSafe)(e,c);if(!d)return!1;let p=(0,NO.parseFrontmatter)(d),h=a.get(l);if(!h)return!1;for(let m of h){let y;if((0,hr.isReferenceCache)(m))y=d.slice(m.position.start.offset,m.position.end.offset);else if((0,hr.isFrontmatterLinkCache)(m)){let w=(0,IO.getNestedPropertyValue)(p,m.key);if(typeof w!="string")return!1;y=w}else return!0;if(y!==m.original)return!1}}return!0},r),a}async function nl(e,t,r={}){let n=null;return await(0,im.retryWithTimeout)(async()=>{let a=(0,Dt.getFileOrNull)(e,t);if(!a||a.deleted)return n=null,!0;await um(e,a);let s=e.metadataCache.getFileInfo(a.path),l=await e.vault.adapter.stat(a.path);return s?l?a.stat.mtime<l.mtime?(e.vault.onChange("modified",a.path,void 0,l),Sn(`Cached timestamp for ${a.path} is from ${new Date(a.stat.mtime).toString()} which is older than the file system modification timestamp ${new Date(l.mtime).toString()}`),!1):s.mtime<l.mtime?(Sn(`File cache info for ${a.path} is from ${new Date(s.mtime).toString()} which is older than the file modification timestamp ${new Date(l.mtime).toString()}`),!1):(n=e.metadataCache.getFileCache(a),n?!0:(Sn(`File cache for ${a.path} is missing`),!1)):(Sn(`File stat for ${a.path} is missing`),!1):(Sn(`File cache info for ${a.path} is missing`),!1)},r),n}async function YO(e,t){return(await nl(e,t))?.frontmatter??{}}function sm(e,t){if(!t.deleted)return MO.noop;let r=[],n=t;for(;n.deleted;)r.push(n.path),e.vault.fileMap[n.path]=n,n=n.parent??(0,Dt.getFolder)(e,(0,hr.parentFolderPath)(n.path),!0);return(0,Dt.isFile)(t)&&e.metadataCache.uniqueFileLookup.add(t.name.toLowerCase(),t),()=>{for(let a of r)delete e.vault.fileMap[a];(0,Dt.isFile)(t)&&e.metadataCache.uniqueFileLookup.remove(t.name.toLowerCase(),t)}}function lm(e,t,r){let n=sm(e,t);try{return r()}finally{n()}}async function um(e,t){if(!(0,Dt.isMarkdownFile)(e,t))return;let r=(0,Dt.getPath)(e,t);for(let n of e.workspace.getLeavesOfType("markdown"))n.view instanceof DO.MarkdownView&&n.view.file?.path===r&&await n.view.save()}});var hm=$((gR,dm)=>{function VO(e){return e&&e.__esModule&&e.default?e.default:e}(function(){let t=require;require=Object.assign(r=>{let n=t(r)??{};return VO(n)},t)})();var il=Object.defineProperty,zO=Object.getOwnPropertyDescriptor,UO=Object.getOwnPropertyNames,WO=Object.prototype.hasOwnProperty,HO=(e,t)=>{for(var r in t)il(e,r,{get:t[r],enumerable:!0})},GO=(e,t,r,n)=>{if(t&&typeof t=="object"||typeof t=="function")for(let a of UO(t))!WO.call(e,a)&&a!==r&&il(e,a,{get:()=>t[a],enumerable:!(n=zO(t,a))||n.enumerable});return e},$O=e=>GO(il({},"__esModule",{value:!0}),e),fm={};HO(fm,{shouldUseRelativeLinks:()=>JO,shouldUseWikilinks:()=>QO});dm.exports=$O(fm);function JO(e){return e.vault.getConfig("newLinkFormat")==="relative"}function QO(e){return!e.vault.getConfig("useMarkdownLinks")}});var ul=$((wR,Om)=>{function gm(e){return e&&e.__esModule&&e.default?e.default:e}(function(){let t=require;require=Object.assign(r=>{let n=t(r)??{};return gm(n)},t)})();var ZO=Object.create,ia=Object.defineProperty,KO=Object.getOwnPropertyDescriptor,XO=Object.getOwnPropertyNames,eF=Object.getPrototypeOf,tF=Object.prototype.hasOwnProperty,rF=(e,t)=>{for(var r in t)ia(e,r,{get:t[r],enumerable:!0})},ym=(e,t,r,n)=>{if(t&&typeof t=="object"||typeof t=="function")for(let a of XO(t))!tF.call(e,a)&&a!==r&&ia(e,a,{get:()=>t[a],enumerable:!(n=KO(t,a))||n.enumerable});return e},nF=(e,t,r)=>(r=e!=null?ZO(eF(e)):{},ym(t||!e||!e.__esModule?ia(r,"default",{value:e,enumerable:!0}):r,e)),iF=e=>ym(ia({},"__esModule",{value:!0}),e),wm={};rF(wm,{convertLink:()=>_m,editBacklinks:()=>dF,editLinks:()=>al,extractLinkFile:()=>km,generateMarkdownLink:()=>vm,parseLink:()=>On,shouldResetAlias:()=>xm,splitSubpath:()=>ol,testAngleBrackets:()=>Sm,testEmbed:()=>sl,testLeadingDot:()=>Cm,testWikilink:()=>ll,updateLink:()=>Pm,updateLinksInFile:()=>hF});Om.exports=iF(wm);var bm=require("obsidian"),aF=(Dp(),Ua(Tp)),oF=nF(gm((ls(),Ua(xd))),1),sF=Mp(),Ht=At(),ze=ct(),Pn=Et(),pm=Rp(),lF=el(),Ue=Xe(),na=Cn(),mm=hm(),uF=ra(),yR=globalThis.process??{browser:!0,cwd:()=>"/",env:{},platform:"android"},cF=/[\\\x00\x08\x0B\x0C\x0E-\x1F ]/g,fF=/[\\[\]<>_*~=`$]/g;function _m(e){let t=km(e.app,e.link,e.oldSourcePathOrFile??e.newSourcePathOrFile);return t?Pm((0,Ht.normalizeOptionalProperties)({app:e.app,link:e.link,newSourcePathOrFile:e.newSourcePathOrFile,newTargetPathOrFile:t,oldSourcePathOrFile:e.oldSourcePathOrFile,shouldForceMarkdownLinks:e.shouldForceMarkdownLinks,shouldUpdateFilenameAlias:e.shouldUpdateFilenameAlias})):e.link.original}async function dF(e,t,r,n={}){let a=await(0,na.getBacklinksForFileSafe)(e,t,n);for(let s of a.keys()){let l=a.get(s)??[],c=new Set(l.map(d=>(0,Ht.toJson)(d)));await al(e,s,d=>{let p=(0,Ht.toJson)(d);if(c.has(p))return r(d)},n)}}async function al(e,t,r,n={}){await(0,lF.applyFileChanges)(e,t,async()=>{let a=await(0,na.getCacheSafe)(e,t);if(!a)return[];let s=[];for(let l of(0,na.getAllLinks)(a)){let c=await r(l);c!==void 0&&s.push((0,uF.referenceToFileChange)(l,c))}return s},n)}function km(e,t,r){let{linkPath:n}=ol(t.link);return e.metadataCache.getFirstLinkpathDest(n,(0,Ue.getPath)(e,r))}function vm(e){let{app:t}=e,n=(t.fileManager.generateMarkdownLink.defaultOptionsFn??(()=>({})))();e={...{isEmptyEmbedAliasAllowed:!0},...n,...e};let s=(0,Ue.getFile)(t,e.targetPathOrFile,e.isNonExistingFileAllowed);return(0,na.tempRegisterFileAndRun)(t,s,()=>{let l=(0,Ue.getPath)(t,e.sourcePathOrFile),c=e.subpath??"",d=e.alias??"",p=e.isEmbed??(e.originalLink?sl(e.originalLink):void 0)??!(0,Ue.isMarkdownFile)(t,s),h=e.isWikilink??(e.originalLink?ll(e.originalLink):void 0)??(0,mm.shouldUseWikilinks)(t),m=e.shouldForceRelativePath??(0,mm.shouldUseRelativeLinks)(t),y=e.shouldUseLeadingDot??(e.originalLink?Cm(e.originalLink):void 0)??!1,w=e.shouldUseAngleBrackets??(e.originalLink?Sm(e.originalLink):void 0)??!1,v=s.path===l&&c?c:m?(0,ze.relative)((0,ze.dirname)(l),h?(0,Ue.trimMarkdownExtension)(t,s):s.path)+c:t.metadataCache.fileToLinktext(s,l,h)+c;m&&y&&!v.startsWith(".")&&!v.startsWith("#")&&(v="./"+v);let x=p?"!":"";if(h){d&&d.toLowerCase()===v.toLowerCase()&&(v=d,d="");let E=d?`|${d}`:"";return`${x}[[${v}${E}]]`}else return w?v=`<${v}>`:v=(0,Pn.replaceAll)(v,cF,({substring:E})=>encodeURIComponent(E)),!d&&(!p||!e.isEmptyEmbedAliasAllowed)&&(d=!e.shouldIncludeAttachmentExtensionToEmbedAlias||(0,Ue.isMarkdownFile)(t,s)?s.basename:s.name),d=(0,Pn.replaceAll)(d,fF,"\\$&"),`${x}[${d}](${v})`})}function On(e){if((0,pm.isUrl)(e))return{isEmbed:!1,isExternal:!0,isWikilink:!1,url:e};let t="!",r="<",n="](",a=")",s="|",l=e.startsWith(t);l&&(e=(0,Pn.trimStart)(e,t));let d=(0,aF.remark)().use(oF.default).use(sF.wikiLinkPlugin,{aliasDivider:s}).parse(e);if(d.children.length!==1)return null;let p=d.children[0];if(p?.type!=="paragraph"||p.children.length!==1)return null;let h=p.children[0];if(h?.position?.start.offset!==0||h.position.end.offset!==e.length)return null;switch(h.type){case"link":{let m=h,y=m.children[0],w=e.slice((y?.position?.end.offset??1)+n.length,(m.position?.end.offset??0)-a.length),v=e.startsWith(r)||w.startsWith(r),x=(0,pm.isUrl)(m.url),E=m.url;if(!x&&!v)try{E=decodeURIComponent(E)}catch(O){console.error(`Failed to decode URL ${E}`,O)}return(0,Ht.normalizeOptionalProperties)({alias:y?.value,hasAngleBrackets:v,isEmbed:l,isExternal:x,isWikilink:!1,title:m.title??void 0,url:E})}case"wikiLink":{let m=h;return(0,Ht.normalizeOptionalProperties)({alias:e.includes(s)?m.data.alias:void 0,isEmbed:l,isWikilink:!0,url:m.value})}default:return null}}function xm(e){let{app:t,displayText:r,isWikilink:n,newSourcePathOrFile:a,oldSourcePathOrFile:s,oldTargetPath:l,targetPathOrFile:c}=e;if(n===!1)return!1;if(!r)return!0;let d=(0,Ue.getFile)(t,c,!0),p=(0,Ue.getPath)(t,a),h=(0,Ue.getPath)(t,s??a),m=(0,ze.dirname)(p),y=(0,ze.dirname)(h),w=new Set;for(let x of[d.path,l]){if(!x)continue;let E=(0,Ue.getPath)(t,x);w.add(E),w.add((0,ze.basename)(E)),w.add((0,ze.relative)(m,E)),w.add((0,ze.relative)(y,E))}for(let x of[h,p])w.add(t.metadataCache.fileToLinktext(d,x,!1));let v=(0,Pn.replaceAll)((0,bm.normalizePath)(r.split(" > ")[0]??""),/^\.\//g,"").toLowerCase();for(let x of w){if(x.toLowerCase()===v)return!0;let E=(0,ze.dirname)(x),O=(0,ze.basename)(x,(0,ze.extname)(x));if((0,ze.join)(E,O).toLowerCase()===v)return!0}return!1}function ol(e){let t=(0,bm.parseLinktext)((0,Pn.normalize)(e));return{linkPath:t.path,subpath:t.subpath}}function Sm(e){return On(e)?.hasAngleBrackets??!1}function sl(e){return On(e)?.isEmbed??!1}function Cm(e){return On(e)?.url.startsWith("./")??!1}function ll(e){return On(e)?.isWikilink??!1}function Pm(e){let{app:t,link:r,newSourcePathOrFile:n,newTargetPathOrFile:a,oldSourcePathOrFile:s,oldTargetPathOrFile:l,shouldForceMarkdownLinks:c,shouldUpdateFilenameAlias:d}=e;if(!a)return r.original;let p=(0,Ue.getFile)(t,a,!0),h=(0,Ue.getPath)(t,l??a),m=ll(r.original)&&c!==!0,{subpath:y}=ol(r.link);if((0,Ue.isCanvasFile)(t,n))return p.path+y;let w=xm((0,Ht.normalizeOptionalProperties)({app:t,displayText:r.displayText,isWikilink:m,newSourcePathOrFile:n,oldSourcePathOrFile:s,oldTargetPath:h,targetPathOrFile:p}))?void 0:r.displayText;return(d??!0)&&(w===(0,ze.basename)(h,(0,ze.extname)(h))?w=p.basename:w===(0,ze.basename)(h)&&(w=p.name)),vm((0,Ht.normalizeOptionalProperties)({alias:w,app:t,isWikilink:c?!1:void 0,originalLink:r.original,sourcePathOrFile:n,subpath:y,targetPathOrFile:p}))}async function hF(e){let{app:t,newSourcePathOrFile:r,oldSourcePathOrFile:n,shouldForceMarkdownLinks:a,shouldUpdateEmbedOnlyLinks:s,shouldUpdateFilenameAlias:l}=e;(0,Ue.isCanvasFile)(t,r)&&!t.internalPlugins.getEnabledPluginById("canvas")||await al(t,r,c=>{let d=sl(c.original);if(!(s!==void 0&&s!==d))return _m((0,Ht.normalizeOptionalProperties)({app:t,link:c,newSourcePathOrFile:r,oldSourcePathOrFile:n,shouldForceMarkdownLinks:a,shouldUpdateFilenameAlias:l}))},e)}});var Am=$((bR,Em)=>{function pF(e){return e&&e.__esModule&&e.default?e.default:e}(function(){let t=require;require=Object.assign(r=>{let n=t(r)??{};return pF(n)},t)})();var cl=Object.defineProperty,mF=Object.getOwnPropertyDescriptor,gF=Object.getOwnPropertyNames,yF=Object.prototype.hasOwnProperty,wF=(e,t)=>{for(var r in t)cl(e,r,{get:t[r],enumerable:!0})},bF=(e,t,r,n)=>{if(t&&typeof t=="object"||typeof t=="function")for(let a of gF(t))!yF.call(e,a)&&a!==r&&cl(e,a,{get:()=>t[a],enumerable:!(n=mF(t,a))||n.enumerable});return e},_F=e=>bF(cl({},"__esModule",{value:!0}),e),Fm={};wF(Fm,{invokeAsyncAndLog:()=>xF});Em.exports=_F(Fm);var kF=ir(),vF=mt(),qr=(0,kF.getDebugger)("obsidian-dev-utils:Logger:invokeAsyncAndLog");async function xF(e,t,r){let n=performance.now();r??=(0,vF.getStackTrace)(1),qr(`${e}:start`,{fn:t,timestampStart:n}),qr.printStackTrace(r);try{await t();let a=performance.now();qr(`${e}:end`,{duration:a-n,fn:t,timestampEnd:a,timestampStart:n}),qr.printStackTrace(r)}catch(a){let s=performance.now();throw qr(`${e}:error`,{duration:s-n,error:a,fn:t,timestampEnd:s,timestampStart:n}),qr.printStackTrace(r),a}}});var hl=$((kR,Nm)=>{function SF(e){return e&&e.__esModule&&e.default?e.default:e}(function(){let t=require;require=Object.assign(r=>{let n=t(r)??{};return SF(n)},t)})();var fl=Object.defineProperty,CF=Object.getOwnPropertyDescriptor,PF=Object.getOwnPropertyNames,OF=Object.prototype.hasOwnProperty,FF=(e,t)=>{for(var r in t)fl(e,r,{get:t[r],enumerable:!0})},EF=(e,t,r,n)=>{if(t&&typeof t=="object"||typeof t=="function")for(let a of PF(t))!OF.call(e,a)&&a!==r&&fl(e,a,{get:()=>t[a],enumerable:!(n=CF(t,a))||n.enumerable});return e},AF=e=>EF(fl({},"__esModule",{value:!0}),e),Dm={};FF(Dm,{addToQueue:()=>MF,addToQueueAndWait:()=>dl,flushQueue:()=>IF});Nm.exports=AF(Dm);var Tm=kn(),Lm=mt(),TF=Fr(),DF=To(),LF=Am(),_R=globalThis.process??{browser:!0,cwd:()=>"/",env:{},platform:"android"};function MF(e,t,r,n){n??=(0,Lm.getStackTrace)(1),dl(e,t,r,n)}async function dl(e,t,r,n){r??=6e4,n??=(0,Lm.getStackTrace)(1);let s=Mm(e).value;s.items.push({fn:t,stackTrace:n,timeoutInMilliseconds:r}),s.promise=s.promise.then(()=>Im(e)),await s.promise}async function IF(e){await dl(e,TF.noop)}function Mm(e){return(0,DF.getObsidianDevUtilsState)(e,"queue",{items:[],promise:Promise.resolve()})}async function Im(e){let t=Mm(e).value,r=t.items[0];r&&(await(0,Tm.addErrorHandler)(()=>(0,Tm.runWithTimeout)(r.timeoutInMilliseconds,()=>(0,LF.invokeAsyncAndLog)(Im.name,r.fn,r.stackTrace))),t.items.shift())}});var yl=$((vR,qm)=>{function NF(e){return e&&e.__esModule&&e.default?e.default:e}(function(){let t=require;require=Object.assign(r=>{let n=t(r)??{};return NF(n)},t)})();var ml=Object.defineProperty,RF=Object.getOwnPropertyDescriptor,qF=Object.getOwnPropertyNames,jF=Object.prototype.hasOwnProperty,BF=(e,t)=>{for(var r in t)ml(e,r,{get:t[r],enumerable:!0})},YF=(e,t,r,n)=>{if(t&&typeof t=="object"||typeof t=="function")for(let a of qF(t))!jF.call(e,a)&&a!==r&&ml(e,a,{get:()=>t[a],enumerable:!(n=RF(t,a))||n.enumerable});return e},VF=e=>YF(ml({},"__esModule",{value:!0}),e),Rm={};BF(Rm,{deleteEmptyFolderHierarchy:()=>WF,deleteSafe:()=>gl});qm.exports=VF(Rm);var zF=mt(),Fn=Xe(),UF=Cn(),pl=fr();async function WF(e,t){let r=(0,Fn.getFolderOrNull)(e,t);for(;r;){if(!await(0,pl.isEmptyFolder)(e,r))return;let n=r.parent;await gl(e,r.path),r=n}}async function gl(e,t,r,n,a){let s=(0,Fn.getAbstractFileOrNull)(e,t);if(!s)return!1;let l=(0,Fn.isFile)(s)||(a??!0);if((0,Fn.isFile)(s)){let c=await(0,UF.getBacklinksForFileSafe)(e,s);r&&c.clear(r),c.count()!==0&&(n&&new Notice(`Attachment ${s.path} is still used by other notes. It will not be deleted.`),l=!1)}else if((0,Fn.isFolder)(s)){let c=await(0,pl.listSafe)(e,s);for(let d of[...c.files,...c.folders])l&&=await gl(e,d,r,n);l&&=await(0,pl.isEmptyFolder)(e,s)}if(l)try{await e.fileManager.trashFile(s)}catch(c){await e.vault.exists(s.path)&&((0,zF.printError)(new Error(`Failed to delete ${s.path}`,{cause:c})),l=!1)}return l}});var Wm=$((xR,Um)=>{function HF(e){return e&&e.__esModule&&e.default?e.default:e}(function(){let t=require;require=Object.assign(r=>{let n=t(r)??{};return HF(n)},t)})();var vl=Object.defineProperty,GF=Object.getOwnPropertyDescriptor,$F=Object.getOwnPropertyNames,JF=Object.prototype.hasOwnProperty,QF=(e,t)=>{for(var r in t)vl(e,r,{get:t[r],enumerable:!0})},ZF=(e,t,r,n)=>{if(t&&typeof t=="object"||typeof t=="function")for(let a of $F(t))!JF.call(e,a)&&a!==r&&vl(e,a,{get:()=>t[a],enumerable:!(n=GF(t,a))||n.enumerable});return e},KF=e=>ZF(vl({},"__esModule",{value:!0}),e),Vm={};QF(Vm,{registerRenameDeleteHandlers:()=>i1});Um.exports=KF(Vm);var XF=tf(),e1=require("obsidian"),xl=ir(),t1=Fr(),oa=At(),We=ct(),r1=Et(),n1=To(),En=mo(),Je=Xe(),An=ul(),pr=Cn(),Sl=hl(),jm=fr(),bl=yl(),_l=new Map,Tn=new Set,aa=new Map;function i1(e,t){let r=sa(e.app),n=e.manifest.id;r.set(n,t),Bm(e.app),e.register(()=>{r.delete(n),Bm(e.app)});let a=e.app;e.registerEvent(a.vault.on("delete",s=>{s1(e,s)})),e.registerEvent(a.vault.on("rename",(s,l)=>{f1(e,s,l)})),e.registerEvent(a.metadataCache.on("deleted",(s,l)=>{u1(e,s,l)}))}async function a1(e,t,r,n,a){if(n.set(t,r),!(0,Je.isNote)(e,t))return;let s=Dn(e),l=await(0,En.getAttachmentFolderPath)(e,t),c=s.shouldRenameAttachmentFolder?await(0,En.getAttachmentFolderPath)(e,r):l,d=(0,Je.getFolderOrNull)(e,l);if(!d||l===c&&!s.shouldRenameAttachmentFiles)return;let p=[];if(await(0,En.hasOwnAttachmentFolder)(e,t))e1.Vault.recurseChildren(d,y=>{(0,Je.isFile)(y)&&p.push(y)});else for(let y of a){let w=(0,An.extractLinkFile)(e,y,t);w&&w.path.startsWith(l)&&(await(0,pr.getBacklinksForFileSafe)(e,w)).keys().length===1&&p.push(w)}let h=(0,We.basename)(t,(0,We.extname)(t)),m=(0,We.basename)(r,(0,We.extname)(r));for(let y of p){if((0,Je.isNote)(e,y))continue;let w=(0,We.relative)(l,y.path),v=(0,We.join)(c,(0,We.dirname)(w)),x=s.shouldRenameAttachmentFiles?(0,r1.replaceAll)(y.basename,h,m):y.basename,E=(0,We.join)(v,(0,We.makeFileName)(x,y.extension));if(y.path!==E){if(s.shouldDeleteConflictingAttachments){let O=(0,Je.getFileOrNull)(e,E);O&&await e.fileManager.trashFile(O)}else E=e.vault.getAvailablePath((0,We.join)(v,x),y.extension);n.set(y.path,E)}}}function sa(e){return(0,n1.getObsidianDevUtilsState)(e,"renameDeleteHandlersMap",new Map).value}function Dn(e){let t=sa(e),r=Array.from(t.values()).reverse(),n={};for(let a of r){let s=a();n.shouldDeleteConflictingAttachments||=s.shouldDeleteConflictingAttachments??!1,n.shouldDeleteEmptyFolders||=s.shouldDeleteEmptyFolders??!1,n.shouldHandleDeletions||=s.shouldHandleDeletions??!1,n.shouldHandleRenames||=s.shouldHandleRenames??!1,n.shouldRenameAttachmentFiles||=s.shouldRenameAttachmentFiles??!1,n.shouldRenameAttachmentFolder||=s.shouldRenameAttachmentFolder??!1,n.shouldUpdateFilenameAliases||=s.shouldUpdateFilenameAliases??!1;let l=n.isPathIgnored;n.isPathIgnored=c=>l?.(c)??s.isPathIgnored?.(c)??!1}return n}async function o1(e,t){if((0,xl.getDebugger)("obsidian-dev-utils:RenameDeleteHandler:handleDelete")(`Handle Delete ${t}`),!(0,Je.isNote)(e,t))return;let r=Dn(e);if(!r.shouldHandleDeletions||r.isPathIgnored?.(t))return;let n=_l.get(t);if(_l.delete(t),n){let l=(0,pr.getAllLinks)(n);for(let c of l){let d=(0,An.extractLinkFile)(e,c,t);d&&((0,Je.isNote)(e,d)||await(0,bl.deleteSafe)(e,d,t,r.shouldDeleteEmptyFolders))}}let a=await(0,En.getAttachmentFolderPath)(e,t),s=(0,Je.getFolderOrNull)(e,a);s&&await(0,En.hasOwnAttachmentFolder)(e,t)&&await(0,bl.deleteSafe)(e,s,t,!1,r.shouldDeleteEmptyFolders)}function s1(e,t){let r=e.app;if(!Cl(e))return;let n=t.path;(0,Sl.addToQueue)(r,()=>o1(r,n))}function l1(e,t,r){let n=Dn(e);n.isPathIgnored?.(t.path)||n.shouldHandleDeletions&&(0,Je.isMarkdownFile)(e,t)&&r&&_l.set(t.path,r)}function u1(e,t,r){Cl(e)&&l1(e.app,t,r)}function c1(e,t,r){let n=zm(t,r);if((0,xl.getDebugger)("obsidian-dev-utils:RenameDeleteHandler:handleRename")(`Handle Rename ${n}`),Tn.has(n)){Tn.delete(n);return}let a=Dn(e);if(!a.shouldHandleRenames||a.isPathIgnored?.(t)||a.isPathIgnored?.(r))return;let s=e.metadataCache.getCache(t)??e.metadataCache.getCache(r),l=s?(0,pr.getAllLinks)(s):[],c=(0,pr.getBacklinksForFileOrPath)(e,t).data;(0,Sl.addToQueue)(e,()=>kl(e,t,r,c,l))}async function kl(e,t,r,n,a,s){let l=aa.get(t);if(l){aa.delete(t);for(let m of l)await kl(e,m.oldPath,r,n,a,m.combinedBacklinksMap)}let c=e.metadataCache.getCache(t)??e.metadataCache.getCache(r),d=c?(0,pr.getAllLinks)(c):[],p=(0,pr.getBacklinksForFileOrPath)(e,t).data;for(let m of d)a.includes(m)||a.push(m);if(e.vault.adapter.insensitive&&t.toLowerCase()===r.toLowerCase()){let m=(0,We.join)((0,We.dirname)(r),"__temp__"+(0,We.basename)(r));await Ym(e,r,m),await kl(e,t,m,n,a),await e.vault.rename((0,Je.getFile)(e,m),r);return}let h=(0,XF.around)(e.fileManager,{updateAllLinks:()=>t1.noopAsync});try{let m=new Map;await a1(e,t,r,m,a);let y=new Map;wl(n,m,y,t),wl(p,m,y,t);for(let x of m.keys()){if(x===t)continue;let E=(await(0,pr.getBacklinksForFileSafe)(e,x)).data;wl(E,m,y,x)}let w=new Set;for(let[x,E]of m.entries()){if(x===t)continue;let O=await Ym(e,x,E);m.set(x,O),w.add((0,We.dirname)(x))}let v=Dn(e);if(v.shouldDeleteEmptyFolders)for(let x of w)await(0,bl.deleteEmptyFolderHierarchy)(e,x);for(let[x,E]of Array.from(y.entries()).concat(Array.from(s?.entries()??[])))await(0,An.editLinks)(e,x,O=>{let z=E.get((0,oa.toJson)(O));if(!z)return;let A=m.get(z);if(A)return(0,An.updateLink)((0,oa.normalizeOptionalProperties)({app:e,link:O,newSourcePathOrFile:x,newTargetPathOrFile:A,oldTargetPathOrFile:z,shouldUpdateFilenameAlias:v.shouldUpdateFilenameAliases}))},{shouldFailOnMissingFile:!1});if((0,Je.isNote)(e,r)&&await(0,An.updateLinksInFile)((0,oa.normalizeOptionalProperties)({app:e,newSourcePathOrFile:r,oldSourcePathOrFile:t,shouldFailOnMissingFile:!1,shouldUpdateFilenameAlias:v.shouldUpdateFilenameAliases})),!(0,Je.getFileOrNull)(e,r)){let x=aa.get(r);x||(x=[],aa.set(r,x)),x.push({combinedBacklinksMap:y,oldPath:t})}}finally{h();let m=Array.from(Tn);(0,Sl.addToQueue)(e,()=>{for(let y of m)Tn.delete(y)})}}function f1(e,t,r){if(!Cl(e)||!(0,Je.isFile)(t))return;let n=t.path;c1(e.app,r,n)}function wl(e,t,r,n){for(let[a,s]of e.entries()){let l=t.get(a)??a,c=r.get(l)??new Map;r.set(l,c);for(let d of s)c.set((0,oa.toJson)(d),n)}}function Bm(e){let t=sa(e);(0,xl.getDebugger)("obsidian-dev-utils:RenameDeleteHandler:logRegisteredHandlers")(`Plugins with registered rename/delete handlers: ${JSON.stringify(Array.from(t.keys()))}`)}function zm(e,t){return`${e} -> ${t}`}async function Ym(e,t,r){if(r=(0,jm.getSafeRenamePath)(e,t,r),t===r)return r;let n=zm(t,r);return Tn.add(n),r=await(0,jm.renameSafe)(e,t,r),r}function Cl(e){let t=e.app,r=e.manifest.id,n=sa(t);return Array.from(n.keys())[0]===r}});var Ol=$((SR,Gm)=>{function d1(e){return e&&e.__esModule&&e.default?e.default:e}(function(){let t=require;require=Object.assign(r=>{let n=t(r)??{};return d1(n)},t)})();var Pl=Object.defineProperty,h1=Object.getOwnPropertyDescriptor,p1=Object.getOwnPropertyNames,m1=Object.prototype.hasOwnProperty,g1=(e,t)=>{for(var r in t)Pl(e,r,{get:t[r],enumerable:!0})},y1=(e,t,r,n)=>{if(t&&typeof t=="object"||typeof t=="function")for(let a of p1(t))!m1.call(e,a)&&a!==r&&Pl(e,a,{get:()=>t[a],enumerable:!(n=h1(t,a))||n.enumerable});return e},w1=e=>y1(Pl({},"__esModule",{value:!0}),e),Hm={};g1(Hm,{appendCodeBlock:()=>b1});Gm.exports=w1(Hm);function b1(e,t){e.createEl("strong",{cls:"markdown-rendered code"},r=>{r.createEl("code",{text:t})})}});var Qm=$((PR,Jm)=>{function _1(e){return e&&e.__esModule&&e.default?e.default:e}(function(){let t=require;require=Object.assign(r=>{let n=t(r)??{};return _1(n)},t)})();var Fl=Object.defineProperty,k1=Object.getOwnPropertyDescriptor,v1=Object.getOwnPropertyNames,x1=Object.prototype.hasOwnProperty,S1=(e,t)=>{for(var r in t)Fl(e,r,{get:t[r],enumerable:!0})},C1=(e,t,r,n)=>{if(t&&typeof t=="object"||typeof t=="function")for(let a of v1(t))!x1.call(e,a)&&a!==r&&Fl(e,a,{get:()=>t[a],enumerable:!(n=k1(t,a))||n.enumerable});return e},P1=e=>C1(Fl({},"__esModule",{value:!0}),e),$m={};S1($m,{loop:()=>E1});Jm.exports=P1($m);var O1=ir(),F1=mt(),CR=globalThis.process??{browser:!0,cwd:()=>"/",env:{},platform:"android"};async function E1(e){let t=e.items,r=0,n=new Notice("",0);for(let a of t){if(e.abortSignal?.aborted){n.hide();return}r++;let s=`# ${r.toString()} / ${t.length.toString()}`,l=e.buildNoticeMessage(a,s);n.setMessage(l),(0,O1.getDebugger)("obsidian-dev-utils:loop")(l);try{await e.processItem(a)}catch(c){if(e.shouldContinueOnError)(0,F1.emitAsyncErrorEvent)(c);else throw n.hide(),c}}n.hide()}});var Xm=$((OR,Km)=>{function A1(e){return e&&e.__esModule&&e.default?e.default:e}(function(){let t=require;require=Object.assign(r=>{let n=t(r)??{};return A1(n)},t)})();var Tl=Object.defineProperty,T1=Object.getOwnPropertyDescriptor,D1=Object.getOwnPropertyNames,L1=Object.prototype.hasOwnProperty,M1=(e,t)=>{for(var r in t)Tl(e,r,{get:t[r],enumerable:!0})},I1=(e,t,r,n)=>{if(t&&typeof t=="object"||typeof t=="function")for(let a of D1(t))!L1.call(e,a)&&a!==r&&Tl(e,a,{get:()=>t[a],enumerable:!(n=T1(t,a))||n.enumerable});return e},N1=e=>I1(Tl({},"__esModule",{value:!0}),e),Zm={};M1(Zm,{confirm:()=>R1});Km.exports=N1(Zm);var El=require("obsidian"),Al=class extends El.Modal{constructor(t,r){super(t.app),this.resolve=r;let n={app:t.app,cancelButtonStyles:{},cancelButtonText:"Cancel",message:"",okButtonStyles:{marginRight:"10px",marginTop:"20px"},okButtonText:"OK",title:""};this.options={...n,...t}}isConfirmed=!1;options;onClose(){this.resolve(this.isConfirmed)}onOpen(){this.titleEl.setText(this.options.title),this.contentEl.createEl("p").setText(this.options.message);let r=new El.ButtonComponent(this.contentEl);r.setButtonText(this.options.okButtonText),r.setCta(),r.onClick(()=>{this.isConfirmed=!0,this.close()}),Object.assign(r.buttonEl.style,this.options.okButtonStyles);let n=new El.ButtonComponent(this.contentEl);n.setButtonText(this.options.cancelButtonText),n.onClick(this.close.bind(this)),Object.assign(r.buttonEl.style,this.options.okButtonStyles)}};async function R1(e){return new Promise(t=>{new Al(e,t).open()})}});var eg=$((Dl,jr)=>{var FR=globalThis.process??{browser:!0,cwd:()=>"/",env:{},platform:"android"};(function(e,t){typeof Dl=="object"&&typeof jr<"u"?jr.exports=t():typeof define=="function"&&define.amd?define(t):e.moment=t()})(Dl,function(){"use strict";var e;function t(){return e.apply(null,arguments)}function r(i){e=i}function n(i){return i instanceof Array||Object.prototype.toString.call(i)==="[object Array]"}function a(i){return i!=null&&Object.prototype.toString.call(i)==="[object Object]"}function s(i,o){return Object.prototype.hasOwnProperty.call(i,o)}function l(i){if(Object.getOwnPropertyNames)return Object.getOwnPropertyNames(i).length===0;var o;for(o in i)if(s(i,o))return!1;return!0}function c(i){return i===void 0}function d(i){return typeof i=="number"||Object.prototype.toString.call(i)==="[object Number]"}function p(i){return i instanceof Date||Object.prototype.toString.call(i)==="[object Date]"}function h(i,o){var u=[],f,g=i.length;for(f=0;f<g;++f)u.push(o(i[f],f));return u}function m(i,o){for(var u in o)s(o,u)&&(i[u]=o[u]);return s(o,"toString")&&(i.toString=o.toString),s(o,"valueOf")&&(i.valueOf=o.valueOf),i}function y(i,o,u,f){return pu(i,o,u,f,!0).utc()}function w(){return{empty:!1,unusedTokens:[],unusedInput:[],overflow:-2,charsLeftOver:0,nullInput:!1,invalidEra:null,invalidMonth:null,invalidFormat:!1,userInvalidated:!1,iso:!1,parsedDateParts:[],era:null,meridiem:null,rfc2822:!1,weekdayMismatch:!1}}function v(i){return i._pf==null&&(i._pf=w()),i._pf}var x;Array.prototype.some?x=Array.prototype.some:x=function(i){var o=Object(this),u=o.length>>>0,f;for(f=0;f<u;f++)if(f in o&&i.call(this,o[f],f,o))return!0;return!1};function E(i){var o=null,u=!1,f=i._d&&!isNaN(i._d.getTime());if(f&&(o=v(i),u=x.call(o.parsedDateParts,function(g){return g!=null}),f=o.overflow<0&&!o.empty&&!o.invalidEra&&!o.invalidMonth&&!o.invalidWeekday&&!o.weekdayMismatch&&!o.nullInput&&!o.invalidFormat&&!o.userInvalidated&&(!o.meridiem||o.meridiem&&u),i._strict&&(f=f&&o.charsLeftOver===0&&o.unusedTokens.length===0&&o.bigHour===void 0)),Object.isFrozen==null||!Object.isFrozen(i))i._isValid=f;else return f;return i._isValid}function O(i){var o=y(NaN);return i!=null?m(v(o),i):v(o).userInvalidated=!0,o}var z=t.momentProperties=[],A=!1;function Z(i,o){var u,f,g,k=z.length;if(c(o._isAMomentObject)||(i._isAMomentObject=o._isAMomentObject),c(o._i)||(i._i=o._i),c(o._f)||(i._f=o._f),c(o._l)||(i._l=o._l),c(o._strict)||(i._strict=o._strict),c(o._tzm)||(i._tzm=o._tzm),c(o._isUTC)||(i._isUTC=o._isUTC),c(o._offset)||(i._offset=o._offset),c(o._pf)||(i._pf=v(o)),c(o._locale)||(i._locale=o._locale),k>0)for(u=0;u<k;u++)f=z[u],g=o[f],c(g)||(i[f]=g);return i}function W(i){Z(this,i),this._d=new Date(i._d!=null?i._d.getTime():NaN),this.isValid()||(this._d=new Date(NaN)),A===!1&&(A=!0,t.updateOffset(this),A=!1)}function P(i){return i instanceof W||i!=null&&i._isAMomentObject!=null}function G(i){t.suppressDeprecationWarnings===!1&&typeof console<"u"&&console.warn&&console.warn("Deprecation warning: "+i)}function B(i,o){var u=!0;return m(function(){if(t.deprecationHandler!=null&&t.deprecationHandler(null,i),u){var f=[],g,k,C,R=arguments.length;for(k=0;k<R;k++){if(g="",typeof arguments[k]=="object"){g+=`
[`+k+"] ";for(C in arguments[0])s(arguments[0],C)&&(g+=C+": "+arguments[0][C]+", ");g=g.slice(0,-2)}else g=arguments[k];f.push(g)}G(i+`
Arguments: `+Array.prototype.slice.call(f).join("")+`
`+new Error().stack),u=!1}return o.apply(this,arguments)},o)}var U={};function L(i,o){t.deprecationHandler!=null&&t.deprecationHandler(i,o),U[i]||(G(o),U[i]=!0)}t.suppressDeprecationWarnings=!1,t.deprecationHandler=null;function H(i){return typeof Function<"u"&&i instanceof Function||Object.prototype.toString.call(i)==="[object Function]"}function K(i){var o,u;for(u in i)s(i,u)&&(o=i[u],H(o)?this[u]=o:this["_"+u]=o);this._config=i,this._dayOfMonthOrdinalParseLenient=new RegExp((this._dayOfMonthOrdinalParse.source||this._ordinalParse.source)+"|"+/\d{1,2}/.source)}function N(i,o){var u=m({},i),f;for(f in o)s(o,f)&&(a(i[f])&&a(o[f])?(u[f]={},m(u[f],i[f]),m(u[f],o[f])):o[f]!=null?u[f]=o[f]:delete u[f]);for(f in i)s(i,f)&&!s(o,f)&&a(i[f])&&(u[f]=m({},u[f]));return u}function Y(i){i!=null&&this.set(i)}var V;Object.keys?V=Object.keys:V=function(i){var o,u=[];for(o in i)s(i,o)&&u.push(o);return u};var M={sameDay:"[Today at] LT",nextDay:"[Tomorrow at] LT",nextWeek:"dddd [at] LT",lastDay:"[Yesterday at] LT",lastWeek:"[Last] dddd [at] LT",sameElse:"L"};function re(i,o,u){var f=this._calendar[i]||this._calendar.sameElse;return H(f)?f.call(o,u):f}function se(i,o,u){var f=""+Math.abs(i),g=o-f.length,k=i>=0;return(k?u?"+":"":"-")+Math.pow(10,Math.max(0,g)).toString().substr(1)+f}var he=/(\[[^\[]*\])|(\\)?([Hh]mm(ss)?|Mo|MM?M?M?|Do|DDDo|DD?D?D?|ddd?d?|do?|w[o|w]?|W[o|W]?|Qo?|N{1,5}|YYYYYY|YYYYY|YYYY|YY|y{2,4}|yo?|gg(ggg?)?|GG(GGG?)?|e|E|a|A|hh?|HH?|kk?|mm?|ss?|S{1,9}|x|X|zz?|ZZ?|.)/g,_=/(\[[^\[]*\])|(\\)?(LTS|LT|LL?L?L?|l{1,4})/g,Oe={},Fe={};function b(i,o,u,f){var g=f;typeof f=="string"&&(g=function(){return this[f]()}),i&&(Fe[i]=g),o&&(Fe[o[0]]=function(){return se(g.apply(this,arguments),o[1],o[2])}),u&&(Fe[u]=function(){return this.localeData().ordinal(g.apply(this,arguments),i)})}function Me(i){return i.match(/\[[\s\S]/)?i.replace(/^\[|\]$/g,""):i.replace(/\\/g,"")}function wt(i){var o=i.match(he),u,f;for(u=0,f=o.length;u<f;u++)Fe[o[u]]?o[u]=Fe[o[u]]:o[u]=Me(o[u]);return function(g){var k="",C;for(C=0;C<f;C++)k+=H(o[C])?o[C].call(g,i):o[C];return k}}function Lt(i,o){return i.isValid()?(o=Jt(o,i.localeData()),Oe[o]=Oe[o]||wt(o),Oe[o](i)):i.localeData().invalidDate()}function Jt(i,o){var u=5;function f(g){return o.longDateFormat(g)||g}for(_.lastIndex=0;u>=0&&_.test(i);)i=i.replace(_,f),_.lastIndex=0,u-=1;return i}var be={LTS:"h:mm:ss A",LT:"h:mm A",L:"MM/DD/YYYY",LL:"MMMM D, YYYY",LLL:"MMMM D, YYYY h:mm A",LLLL:"dddd, MMMM D, YYYY h:mm A"};function Qt(i){var o=this._longDateFormat[i],u=this._longDateFormat[i.toUpperCase()];return o||!u?o:(this._longDateFormat[i]=u.match(he).map(function(f){return f==="MMMM"||f==="MM"||f==="DD"||f==="dddd"?f.slice(1):f}).join(""),this._longDateFormat[i])}var it="Invalid date";function bt(){return this._invalidDate}var Mt="%d",Wr=/\d{1,2}/;function ka(i){return this._ordinal.replace("%d",i)}var In={future:"in %s",past:"%s ago",s:"a few seconds",ss:"%d seconds",m:"a minute",mm:"%d minutes",h:"an hour",hh:"%d hours",d:"a day",dd:"%d days",w:"a week",ww:"%d weeks",M:"a month",MM:"%d months",y:"a year",yy:"%d years"};function Nn(i,o,u,f){var g=this._relativeTime[u];return H(g)?g(i,o,u,f):g.replace(/%d/i,i)}function Rn(i,o){var u=this._relativeTime[i>0?"future":"past"];return H(u)?u(o):u.replace(/%s/i,o)}var qn={D:"date",dates:"date",date:"date",d:"day",days:"day",day:"day",e:"weekday",weekdays:"weekday",weekday:"weekday",E:"isoWeekday",isoweekdays:"isoWeekday",isoweekday:"isoWeekday",DDD:"dayOfYear",dayofyears:"dayOfYear",dayofyear:"dayOfYear",h:"hour",hours:"hour",hour:"hour",ms:"millisecond",milliseconds:"millisecond",millisecond:"millisecond",m:"minute",minutes:"minute",minute:"minute",M:"month",months:"month",month:"month",Q:"quarter",quarters:"quarter",quarter:"quarter",s:"second",seconds:"second",second:"second",gg:"weekYear",weekyears:"weekYear",weekyear:"weekYear",GG:"isoWeekYear",isoweekyears:"isoWeekYear",isoweekyear:"isoWeekYear",w:"week",weeks:"week",week:"week",W:"isoWeek",isoweeks:"isoWeek",isoweek:"isoWeek",y:"year",years:"year",year:"year"};function Ie(i){return typeof i=="string"?qn[i]||qn[i.toLowerCase()]:void 0}function gr(i){var o={},u,f;for(f in i)s(i,f)&&(u=Ie(f),u&&(o[u]=i[f]));return o}var va={date:9,day:11,weekday:11,isoWeekday:11,dayOfYear:4,hour:13,millisecond:16,minute:14,month:8,quarter:7,second:15,weekYear:1,isoWeekYear:1,week:5,isoWeek:5,year:1};function xa(i){var o=[],u;for(u in i)s(i,u)&&o.push({unit:u,priority:va[u]});return o.sort(function(f,g){return f.priority-g.priority}),o}var jn=/\d/,Ne=/\d\d/,Bn=/\d{3}/,F=/\d{4}/,D=/[+-]?\d{6}/,I=/\d\d?/,te=/\d\d\d\d?/,le=/\d\d\d\d\d\d?/,Ee=/\d{1,3}/,at=/\d{1,4}/,Ve=/[+-]?\d{1,6}/,Qe=/\d+/,dt=/[+-]?\d+/,Re=/Z|[+-]\d\d:?\d\d/gi,ot=/Z|[+-]\d\d(?::?\d\d)?/gi,st=/[+-]?\d+(\.\d{1,3})?/,Hr=/[0-9]{0,256}['a-z\u00A0-\u05FF\u0700-\uD7FF\uF900-\uFDCF\uFDF0-\uFF07\uFF10-\uFFEF]{1,256}|[\u0600-\u06FF\/]{1,256}(\s*?[\u0600-\u06FF]{1,256}){1,2}/i,yr=/^[1-9]\d?/,Sa=/^([1-9]\d|\d)/,Yn;Yn={};function q(i,o,u){Yn[i]=H(o)?o:function(f,g){return f&&u?u:o}}function ny(i,o){return s(Yn,i)?Yn[i](o._strict,o._locale):new RegExp(iy(i))}function iy(i){return _t(i.replace("\\","").replace(/\\(\[)|\\(\])|\[([^\]\[]*)\]|\\(.)/g,function(o,u,f,g,k){return u||f||g||k}))}function _t(i){return i.replace(/[-\/\\^$*+?.()|[\]{}]/g,"\\$&")}function Ze(i){return i<0?Math.ceil(i)||0:Math.floor(i)}function ne(i){var o=+i,u=0;return o!==0&&isFinite(o)&&(u=Ze(o)),u}var Ca={};function ue(i,o){var u,f=o,g;for(typeof i=="string"&&(i=[i]),d(o)&&(f=function(k,C){C[o]=ne(k)}),g=i.length,u=0;u<g;u++)Ca[i[u]]=f}function Gr(i,o){ue(i,function(u,f,g,k){g._w=g._w||{},o(u,g._w,g,k)})}function ay(i,o,u){o!=null&&s(Ca,i)&&Ca[i](o,u._a,u,i)}function Vn(i){return i%4===0&&i%100!==0||i%400===0}var Ae=0,kt=1,ht=2,_e=3,lt=4,vt=5,Zt=6,oy=7,sy=8;b("Y",0,0,function(){var i=this.year();return i<=9999?se(i,4):"+"+i}),b(0,["YY",2],0,function(){return this.year()%100}),b(0,["YYYY",4],0,"year"),b(0,["YYYYY",5],0,"year"),b(0,["YYYYYY",6,!0],0,"year"),q("Y",dt),q("YY",I,Ne),q("YYYY",at,F),q("YYYYY",Ve,D),q("YYYYYY",Ve,D),ue(["YYYYY","YYYYYY"],Ae),ue("YYYY",function(i,o){o[Ae]=i.length===2?t.parseTwoDigitYear(i):ne(i)}),ue("YY",function(i,o){o[Ae]=t.parseTwoDigitYear(i)}),ue("Y",function(i,o){o[Ae]=parseInt(i,10)});function $r(i){return Vn(i)?366:365}t.parseTwoDigitYear=function(i){return ne(i)+(ne(i)>68?1900:2e3)};var Kl=wr("FullYear",!0);function ly(){return Vn(this.year())}function wr(i,o){return function(u){return u!=null?(Xl(this,i,u),t.updateOffset(this,o),this):Jr(this,i)}}function Jr(i,o){if(!i.isValid())return NaN;var u=i._d,f=i._isUTC;switch(o){case"Milliseconds":return f?u.getUTCMilliseconds():u.getMilliseconds();case"Seconds":return f?u.getUTCSeconds():u.getSeconds();case"Minutes":return f?u.getUTCMinutes():u.getMinutes();case"Hours":return f?u.getUTCHours():u.getHours();case"Date":return f?u.getUTCDate():u.getDate();case"Day":return f?u.getUTCDay():u.getDay();case"Month":return f?u.getUTCMonth():u.getMonth();case"FullYear":return f?u.getUTCFullYear():u.getFullYear();default:return NaN}}function Xl(i,o,u){var f,g,k,C,R;if(!(!i.isValid()||isNaN(u))){switch(f=i._d,g=i._isUTC,o){case"Milliseconds":return void(g?f.setUTCMilliseconds(u):f.setMilliseconds(u));case"Seconds":return void(g?f.setUTCSeconds(u):f.setSeconds(u));case"Minutes":return void(g?f.setUTCMinutes(u):f.setMinutes(u));case"Hours":return void(g?f.setUTCHours(u):f.setHours(u));case"Date":return void(g?f.setUTCDate(u):f.setDate(u));case"FullYear":break;default:return}k=u,C=i.month(),R=i.date(),R=R===29&&C===1&&!Vn(k)?28:R,g?f.setUTCFullYear(k,C,R):f.setFullYear(k,C,R)}}function uy(i){return i=Ie(i),H(this[i])?this[i]():this}function cy(i,o){if(typeof i=="object"){i=gr(i);var u=xa(i),f,g=u.length;for(f=0;f<g;f++)this[u[f].unit](i[u[f].unit])}else if(i=Ie(i),H(this[i]))return this[i](o);return this}function fy(i,o){return(i%o+o)%o}var ge;Array.prototype.indexOf?ge=Array.prototype.indexOf:ge=function(i){var o;for(o=0;o<this.length;++o)if(this[o]===i)return o;return-1};function Pa(i,o){if(isNaN(i)||isNaN(o))return NaN;var u=fy(o,12);return i+=(o-u)/12,u===1?Vn(i)?29:28:31-u%7%2}b("M",["MM",2],"Mo",function(){return this.month()+1}),b("MMM",0,0,function(i){return this.localeData().monthsShort(this,i)}),b("MMMM",0,0,function(i){return this.localeData().months(this,i)}),q("M",I,yr),q("MM",I,Ne),q("MMM",function(i,o){return o.monthsShortRegex(i)}),q("MMMM",function(i,o){return o.monthsRegex(i)}),ue(["M","MM"],function(i,o){o[kt]=ne(i)-1}),ue(["MMM","MMMM"],function(i,o,u,f){var g=u._locale.monthsParse(i,f,u._strict);g!=null?o[kt]=g:v(u).invalidMonth=i});var dy="January_February_March_April_May_June_July_August_September_October_November_December".split("_"),eu="Jan_Feb_Mar_Apr_May_Jun_Jul_Aug_Sep_Oct_Nov_Dec".split("_"),tu=/D[oD]?(\[[^\[\]]*\]|\s)+MMMM?/,hy=Hr,py=Hr;function my(i,o){return i?n(this._months)?this._months[i.month()]:this._months[(this._months.isFormat||tu).test(o)?"format":"standalone"][i.month()]:n(this._months)?this._months:this._months.standalone}function gy(i,o){return i?n(this._monthsShort)?this._monthsShort[i.month()]:this._monthsShort[tu.test(o)?"format":"standalone"][i.month()]:n(this._monthsShort)?this._monthsShort:this._monthsShort.standalone}function yy(i,o,u){var f,g,k,C=i.toLocaleLowerCase();if(!this._monthsParse)for(this._monthsParse=[],this._longMonthsParse=[],this._shortMonthsParse=[],f=0;f<12;++f)k=y([2e3,f]),this._shortMonthsParse[f]=this.monthsShort(k,"").toLocaleLowerCase(),this._longMonthsParse[f]=this.months(k,"").toLocaleLowerCase();return u?o==="MMM"?(g=ge.call(this._shortMonthsParse,C),g!==-1?g:null):(g=ge.call(this._longMonthsParse,C),g!==-1?g:null):o==="MMM"?(g=ge.call(this._shortMonthsParse,C),g!==-1?g:(g=ge.call(this._longMonthsParse,C),g!==-1?g:null)):(g=ge.call(this._longMonthsParse,C),g!==-1?g:(g=ge.call(this._shortMonthsParse,C),g!==-1?g:null))}function wy(i,o,u){var f,g,k;if(this._monthsParseExact)return yy.call(this,i,o,u);for(this._monthsParse||(this._monthsParse=[],this._longMonthsParse=[],this._shortMonthsParse=[]),f=0;f<12;f++){if(g=y([2e3,f]),u&&!this._longMonthsParse[f]&&(this._longMonthsParse[f]=new RegExp("^"+this.months(g,"").replace(".","")+"$","i"),this._shortMonthsParse[f]=new RegExp("^"+this.monthsShort(g,"").replace(".","")+"$","i")),!u&&!this._monthsParse[f]&&(k="^"+this.months(g,"")+"|^"+this.monthsShort(g,""),this._monthsParse[f]=new RegExp(k.replace(".",""),"i")),u&&o==="MMMM"&&this._longMonthsParse[f].test(i))return f;if(u&&o==="MMM"&&this._shortMonthsParse[f].test(i))return f;if(!u&&this._monthsParse[f].test(i))return f}}function ru(i,o){if(!i.isValid())return i;if(typeof o=="string"){if(/^\d+$/.test(o))o=ne(o);else if(o=i.localeData().monthsParse(o),!d(o))return i}var u=o,f=i.date();return f=f<29?f:Math.min(f,Pa(i.year(),u)),i._isUTC?i._d.setUTCMonth(u,f):i._d.setMonth(u,f),i}function nu(i){return i!=null?(ru(this,i),t.updateOffset(this,!0),this):Jr(this,"Month")}function by(){return Pa(this.year(),this.month())}function _y(i){return this._monthsParseExact?(s(this,"_monthsRegex")||iu.call(this),i?this._monthsShortStrictRegex:this._monthsShortRegex):(s(this,"_monthsShortRegex")||(this._monthsShortRegex=hy),this._monthsShortStrictRegex&&i?this._monthsShortStrictRegex:this._monthsShortRegex)}function ky(i){return this._monthsParseExact?(s(this,"_monthsRegex")||iu.call(this),i?this._monthsStrictRegex:this._monthsRegex):(s(this,"_monthsRegex")||(this._monthsRegex=py),this._monthsStrictRegex&&i?this._monthsStrictRegex:this._monthsRegex)}function iu(){function i(J,ie){return ie.length-J.length}var o=[],u=[],f=[],g,k,C,R;for(g=0;g<12;g++)k=y([2e3,g]),C=_t(this.monthsShort(k,"")),R=_t(this.months(k,"")),o.push(C),u.push(R),f.push(R),f.push(C);o.sort(i),u.sort(i),f.sort(i),this._monthsRegex=new RegExp("^("+f.join("|")+")","i"),this._monthsShortRegex=this._monthsRegex,this._monthsStrictRegex=new RegExp("^("+u.join("|")+")","i"),this._monthsShortStrictRegex=new RegExp("^("+o.join("|")+")","i")}function vy(i,o,u,f,g,k,C){var R;return i<100&&i>=0?(R=new Date(i+400,o,u,f,g,k,C),isFinite(R.getFullYear())&&R.setFullYear(i)):R=new Date(i,o,u,f,g,k,C),R}function Qr(i){var o,u;return i<100&&i>=0?(u=Array.prototype.slice.call(arguments),u[0]=i+400,o=new Date(Date.UTC.apply(null,u)),isFinite(o.getUTCFullYear())&&o.setUTCFullYear(i)):o=new Date(Date.UTC.apply(null,arguments)),o}function zn(i,o,u){var f=7+o-u,g=(7+Qr(i,0,f).getUTCDay()-o)%7;return-g+f-1}function au(i,o,u,f,g){var k=(7+u-f)%7,C=zn(i,f,g),R=1+7*(o-1)+k+C,J,ie;return R<=0?(J=i-1,ie=$r(J)+R):R>$r(i)?(J=i+1,ie=R-$r(i)):(J=i,ie=R),{year:J,dayOfYear:ie}}function Zr(i,o,u){var f=zn(i.year(),o,u),g=Math.floor((i.dayOfYear()-f-1)/7)+1,k,C;return g<1?(C=i.year()-1,k=g+xt(C,o,u)):g>xt(i.year(),o,u)?(k=g-xt(i.year(),o,u),C=i.year()+1):(C=i.year(),k=g),{week:k,year:C}}function xt(i,o,u){var f=zn(i,o,u),g=zn(i+1,o,u);return($r(i)-f+g)/7}b("w",["ww",2],"wo","week"),b("W",["WW",2],"Wo","isoWeek"),q("w",I,yr),q("ww",I,Ne),q("W",I,yr),q("WW",I,Ne),Gr(["w","ww","W","WW"],function(i,o,u,f){o[f.substr(0,1)]=ne(i)});function xy(i){return Zr(i,this._week.dow,this._week.doy).week}var Sy={dow:0,doy:6};function Cy(){return this._week.dow}function Py(){return this._week.doy}function Oy(i){var o=this.localeData().week(this);return i==null?o:this.add((i-o)*7,"d")}function Fy(i){var o=Zr(this,1,4).week;return i==null?o:this.add((i-o)*7,"d")}b("d",0,"do","day"),b("dd",0,0,function(i){return this.localeData().weekdaysMin(this,i)}),b("ddd",0,0,function(i){return this.localeData().weekdaysShort(this,i)}),b("dddd",0,0,function(i){return this.localeData().weekdays(this,i)}),b("e",0,0,"weekday"),b("E",0,0,"isoWeekday"),q("d",I),q("e",I),q("E",I),q("dd",function(i,o){return o.weekdaysMinRegex(i)}),q("ddd",function(i,o){return o.weekdaysShortRegex(i)}),q("dddd",function(i,o){return o.weekdaysRegex(i)}),Gr(["dd","ddd","dddd"],function(i,o,u,f){var g=u._locale.weekdaysParse(i,f,u._strict);g!=null?o.d=g:v(u).invalidWeekday=i}),Gr(["d","e","E"],function(i,o,u,f){o[f]=ne(i)});function Ey(i,o){return typeof i!="string"?i:isNaN(i)?(i=o.weekdaysParse(i),typeof i=="number"?i:null):parseInt(i,10)}function Ay(i,o){return typeof i=="string"?o.weekdaysParse(i)%7||7:isNaN(i)?null:i}function Oa(i,o){return i.slice(o,7).concat(i.slice(0,o))}var Ty="Sunday_Monday_Tuesday_Wednesday_Thursday_Friday_Saturday".split("_"),ou="Sun_Mon_Tue_Wed_Thu_Fri_Sat".split("_"),Dy="Su_Mo_Tu_We_Th_Fr_Sa".split("_"),Ly=Hr,My=Hr,Iy=Hr;function Ny(i,o){var u=n(this._weekdays)?this._weekdays:this._weekdays[i&&i!==!0&&this._weekdays.isFormat.test(o)?"format":"standalone"];return i===!0?Oa(u,this._week.dow):i?u[i.day()]:u}function Ry(i){return i===!0?Oa(this._weekdaysShort,this._week.dow):i?this._weekdaysShort[i.day()]:this._weekdaysShort}function qy(i){return i===!0?Oa(this._weekdaysMin,this._week.dow):i?this._weekdaysMin[i.day()]:this._weekdaysMin}function jy(i,o,u){var f,g,k,C=i.toLocaleLowerCase();if(!this._weekdaysParse)for(this._weekdaysParse=[],this._shortWeekdaysParse=[],this._minWeekdaysParse=[],f=0;f<7;++f)k=y([2e3,1]).day(f),this._minWeekdaysParse[f]=this.weekdaysMin(k,"").toLocaleLowerCase(),this._shortWeekdaysParse[f]=this.weekdaysShort(k,"").toLocaleLowerCase(),this._weekdaysParse[f]=this.weekdays(k,"").toLocaleLowerCase();return u?o==="dddd"?(g=ge.call(this._weekdaysParse,C),g!==-1?g:null):o==="ddd"?(g=ge.call(this._shortWeekdaysParse,C),g!==-1?g:null):(g=ge.call(this._minWeekdaysParse,C),g!==-1?g:null):o==="dddd"?(g=ge.call(this._weekdaysParse,C),g!==-1||(g=ge.call(this._shortWeekdaysParse,C),g!==-1)?g:(g=ge.call(this._minWeekdaysParse,C),g!==-1?g:null)):o==="ddd"?(g=ge.call(this._shortWeekdaysParse,C),g!==-1||(g=ge.call(this._weekdaysParse,C),g!==-1)?g:(g=ge.call(this._minWeekdaysParse,C),g!==-1?g:null)):(g=ge.call(this._minWeekdaysParse,C),g!==-1||(g=ge.call(this._weekdaysParse,C),g!==-1)?g:(g=ge.call(this._shortWeekdaysParse,C),g!==-1?g:null))}function By(i,o,u){var f,g,k;if(this._weekdaysParseExact)return jy.call(this,i,o,u);for(this._weekdaysParse||(this._weekdaysParse=[],this._minWeekdaysParse=[],this._shortWeekdaysParse=[],this._fullWeekdaysParse=[]),f=0;f<7;f++){if(g=y([2e3,1]).day(f),u&&!this._fullWeekdaysParse[f]&&(this._fullWeekdaysParse[f]=new RegExp("^"+this.weekdays(g,"").replace(".","\\.?")+"$","i"),this._shortWeekdaysParse[f]=new RegExp("^"+this.weekdaysShort(g,"").replace(".","\\.?")+"$","i"),this._minWeekdaysParse[f]=new RegExp("^"+this.weekdaysMin(g,"").replace(".","\\.?")+"$","i")),this._weekdaysParse[f]||(k="^"+this.weekdays(g,"")+"|^"+this.weekdaysShort(g,"")+"|^"+this.weekdaysMin(g,""),this._weekdaysParse[f]=new RegExp(k.replace(".",""),"i")),u&&o==="dddd"&&this._fullWeekdaysParse[f].test(i))return f;if(u&&o==="ddd"&&this._shortWeekdaysParse[f].test(i))return f;if(u&&o==="dd"&&this._minWeekdaysParse[f].test(i))return f;if(!u&&this._weekdaysParse[f].test(i))return f}}function Yy(i){if(!this.isValid())return i!=null?this:NaN;var o=Jr(this,"Day");return i!=null?(i=Ey(i,this.localeData()),this.add(i-o,"d")):o}function Vy(i){if(!this.isValid())return i!=null?this:NaN;var o=(this.day()+7-this.localeData()._week.dow)%7;return i==null?o:this.add(i-o,"d")}function zy(i){if(!this.isValid())return i!=null?this:NaN;if(i!=null){var o=Ay(i,this.localeData());return this.day(this.day()%7?o:o-7)}else return this.day()||7}function Uy(i){return this._weekdaysParseExact?(s(this,"_weekdaysRegex")||Fa.call(this),i?this._weekdaysStrictRegex:this._weekdaysRegex):(s(this,"_weekdaysRegex")||(this._weekdaysRegex=Ly),this._weekdaysStrictRegex&&i?this._weekdaysStrictRegex:this._weekdaysRegex)}function Wy(i){return this._weekdaysParseExact?(s(this,"_weekdaysRegex")||Fa.call(this),i?this._weekdaysShortStrictRegex:this._weekdaysShortRegex):(s(this,"_weekdaysShortRegex")||(this._weekdaysShortRegex=My),this._weekdaysShortStrictRegex&&i?this._weekdaysShortStrictRegex:this._weekdaysShortRegex)}function Hy(i){return this._weekdaysParseExact?(s(this,"_weekdaysRegex")||Fa.call(this),i?this._weekdaysMinStrictRegex:this._weekdaysMinRegex):(s(this,"_weekdaysMinRegex")||(this._weekdaysMinRegex=Iy),this._weekdaysMinStrictRegex&&i?this._weekdaysMinStrictRegex:this._weekdaysMinRegex)}function Fa(){function i(qe,Ft){return Ft.length-qe.length}var o=[],u=[],f=[],g=[],k,C,R,J,ie;for(k=0;k<7;k++)C=y([2e3,1]).day(k),R=_t(this.weekdaysMin(C,"")),J=_t(this.weekdaysShort(C,"")),ie=_t(this.weekdays(C,"")),o.push(R),u.push(J),f.push(ie),g.push(R),g.push(J),g.push(ie);o.sort(i),u.sort(i),f.sort(i),g.sort(i),this._weekdaysRegex=new RegExp("^("+g.join("|")+")","i"),this._weekdaysShortRegex=this._weekdaysRegex,this._weekdaysMinRegex=this._weekdaysRegex,this._weekdaysStrictRegex=new RegExp("^("+f.join("|")+")","i"),this._weekdaysShortStrictRegex=new RegExp("^("+u.join("|")+")","i"),this._weekdaysMinStrictRegex=new RegExp("^("+o.join("|")+")","i")}function Ea(){return this.hours()%12||12}function Gy(){return this.hours()||24}b("H",["HH",2],0,"hour"),b("h",["hh",2],0,Ea),b("k",["kk",2],0,Gy),b("hmm",0,0,function(){return""+Ea.apply(this)+se(this.minutes(),2)}),b("hmmss",0,0,function(){return""+Ea.apply(this)+se(this.minutes(),2)+se(this.seconds(),2)}),b("Hmm",0,0,function(){return""+this.hours()+se(this.minutes(),2)}),b("Hmmss",0,0,function(){return""+this.hours()+se(this.minutes(),2)+se(this.seconds(),2)});function su(i,o){b(i,0,0,function(){return this.localeData().meridiem(this.hours(),this.minutes(),o)})}su("a",!0),su("A",!1);function lu(i,o){return o._meridiemParse}q("a",lu),q("A",lu),q("H",I,Sa),q("h",I,yr),q("k",I,yr),q("HH",I,Ne),q("hh",I,Ne),q("kk",I,Ne),q("hmm",te),q("hmmss",le),q("Hmm",te),q("Hmmss",le),ue(["H","HH"],_e),ue(["k","kk"],function(i,o,u){var f=ne(i);o[_e]=f===24?0:f}),ue(["a","A"],function(i,o,u){u._isPm=u._locale.isPM(i),u._meridiem=i}),ue(["h","hh"],function(i,o,u){o[_e]=ne(i),v(u).bigHour=!0}),ue("hmm",function(i,o,u){var f=i.length-2;o[_e]=ne(i.substr(0,f)),o[lt]=ne(i.substr(f)),v(u).bigHour=!0}),ue("hmmss",function(i,o,u){var f=i.length-4,g=i.length-2;o[_e]=ne(i.substr(0,f)),o[lt]=ne(i.substr(f,2)),o[vt]=ne(i.substr(g)),v(u).bigHour=!0}),ue("Hmm",function(i,o,u){var f=i.length-2;o[_e]=ne(i.substr(0,f)),o[lt]=ne(i.substr(f))}),ue("Hmmss",function(i,o,u){var f=i.length-4,g=i.length-2;o[_e]=ne(i.substr(0,f)),o[lt]=ne(i.substr(f,2)),o[vt]=ne(i.substr(g))});function $y(i){return(i+"").toLowerCase().charAt(0)==="p"}var Jy=/[ap]\.?m?\.?/i,Qy=wr("Hours",!0);function Zy(i,o,u){return i>11?u?"pm":"PM":u?"am":"AM"}var uu={calendar:M,longDateFormat:be,invalidDate:it,ordinal:Mt,dayOfMonthOrdinalParse:Wr,relativeTime:In,months:dy,monthsShort:eu,week:Sy,weekdays:Ty,weekdaysMin:Dy,weekdaysShort:ou,meridiemParse:Jy},de={},Kr={},Xr;function Ky(i,o){var u,f=Math.min(i.length,o.length);for(u=0;u<f;u+=1)if(i[u]!==o[u])return u;return f}function cu(i){return i&&i.toLowerCase().replace("_","-")}function Xy(i){for(var o=0,u,f,g,k;o<i.length;){for(k=cu(i[o]).split("-"),u=k.length,f=cu(i[o+1]),f=f?f.split("-"):null;u>0;){if(g=Un(k.slice(0,u).join("-")),g)return g;if(f&&f.length>=u&&Ky(k,f)>=u-1)break;u--}o++}return Xr}function ew(i){return!!(i&&i.match("^[^/\\\\]*$"))}function Un(i){var o=null,u;if(de[i]===void 0&&typeof jr<"u"&&jr&&jr.exports&&ew(i))try{o=Xr._abbr,u=require,u("./locale/"+i),It(o)}catch{de[i]=null}return de[i]}function It(i,o){var u;return i&&(c(o)?u=St(i):u=Aa(i,o),u?Xr=u:typeof console<"u"&&console.warn&&console.warn("Locale "+i+" not found. Did you forget to load it?")),Xr._abbr}function Aa(i,o){if(o!==null){var u,f=uu;if(o.abbr=i,de[i]!=null)L("defineLocaleOverride","use moment.updateLocale(localeName, config) to change an existing locale. moment.defineLocale(localeName, config) should only be used for creating a new locale See http://momentjs.com/guides/#/warnings/define-locale/ for more info."),f=de[i]._config;else if(o.parentLocale!=null)if(de[o.parentLocale]!=null)f=de[o.parentLocale]._config;else if(u=Un(o.parentLocale),u!=null)f=u._config;else return Kr[o.parentLocale]||(Kr[o.parentLocale]=[]),Kr[o.parentLocale].push({name:i,config:o}),null;return de[i]=new Y(N(f,o)),Kr[i]&&Kr[i].forEach(function(g){Aa(g.name,g.config)}),It(i),de[i]}else return delete de[i],null}function tw(i,o){if(o!=null){var u,f,g=uu;de[i]!=null&&de[i].parentLocale!=null?de[i].set(N(de[i]._config,o)):(f=Un(i),f!=null&&(g=f._config),o=N(g,o),f==null&&(o.abbr=i),u=new Y(o),u.parentLocale=de[i],de[i]=u),It(i)}else de[i]!=null&&(de[i].parentLocale!=null?(de[i]=de[i].parentLocale,i===It()&&It(i)):de[i]!=null&&delete de[i]);return de[i]}function St(i){var o;if(i&&i._locale&&i._locale._abbr&&(i=i._locale._abbr),!i)return Xr;if(!n(i)){if(o=Un(i),o)return o;i=[i]}return Xy(i)}function rw(){return V(de)}function Ta(i){var o,u=i._a;return u&&v(i).overflow===-2&&(o=u[kt]<0||u[kt]>11?kt:u[ht]<1||u[ht]>Pa(u[Ae],u[kt])?ht:u[_e]<0||u[_e]>24||u[_e]===24&&(u[lt]!==0||u[vt]!==0||u[Zt]!==0)?_e:u[lt]<0||u[lt]>59?lt:u[vt]<0||u[vt]>59?vt:u[Zt]<0||u[Zt]>999?Zt:-1,v(i)._overflowDayOfYear&&(o<Ae||o>ht)&&(o=ht),v(i)._overflowWeeks&&o===-1&&(o=oy),v(i)._overflowWeekday&&o===-1&&(o=sy),v(i).overflow=o),i}var nw=/^\s*((?:[+-]\d{6}|\d{4})-(?:\d\d-\d\d|W\d\d-\d|W\d\d|\d\d\d|\d\d))(?:(T| )(\d\d(?::\d\d(?::\d\d(?:[.,]\d+)?)?)?)([+-]\d\d(?::?\d\d)?|\s*Z)?)?$/,iw=/^\s*((?:[+-]\d{6}|\d{4})(?:\d\d\d\d|W\d\d\d|W\d\d|\d\d\d|\d\d|))(?:(T| )(\d\d(?:\d\d(?:\d\d(?:[.,]\d+)?)?)?)([+-]\d\d(?::?\d\d)?|\s*Z)?)?$/,aw=/Z|[+-]\d\d(?::?\d\d)?/,Wn=[["YYYYYY-MM-DD",/[+-]\d{6}-\d\d-\d\d/],["YYYY-MM-DD",/\d{4}-\d\d-\d\d/],["GGGG-[W]WW-E",/\d{4}-W\d\d-\d/],["GGGG-[W]WW",/\d{4}-W\d\d/,!1],["YYYY-DDD",/\d{4}-\d{3}/],["YYYY-MM",/\d{4}-\d\d/,!1],["YYYYYYMMDD",/[+-]\d{10}/],["YYYYMMDD",/\d{8}/],["GGGG[W]WWE",/\d{4}W\d{3}/],["GGGG[W]WW",/\d{4}W\d{2}/,!1],["YYYYDDD",/\d{7}/],["YYYYMM",/\d{6}/,!1],["YYYY",/\d{4}/,!1]],Da=[["HH:mm:ss.SSSS",/\d\d:\d\d:\d\d\.\d+/],["HH:mm:ss,SSSS",/\d\d:\d\d:\d\d,\d+/],["HH:mm:ss",/\d\d:\d\d:\d\d/],["HH:mm",/\d\d:\d\d/],["HHmmss.SSSS",/\d\d\d\d\d\d\.\d+/],["HHmmss,SSSS",/\d\d\d\d\d\d,\d+/],["HHmmss",/\d\d\d\d\d\d/],["HHmm",/\d\d\d\d/],["HH",/\d\d/]],ow=/^\/?Date\((-?\d+)/i,sw=/^(?:(Mon|Tue|Wed|Thu|Fri|Sat|Sun),?\s)?(\d{1,2})\s(Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec)\s(\d{2,4})\s(\d\d):(\d\d)(?::(\d\d))?\s(?:(UT|GMT|[ECMP][SD]T)|([Zz])|([+-]\d{4}))$/,lw={UT:0,GMT:0,EDT:-4*60,EST:-5*60,CDT:-5*60,CST:-6*60,MDT:-6*60,MST:-7*60,PDT:-7*60,PST:-8*60};function fu(i){var o,u,f=i._i,g=nw.exec(f)||iw.exec(f),k,C,R,J,ie=Wn.length,qe=Da.length;if(g){for(v(i).iso=!0,o=0,u=ie;o<u;o++)if(Wn[o][1].exec(g[1])){C=Wn[o][0],k=Wn[o][2]!==!1;break}if(C==null){i._isValid=!1;return}if(g[3]){for(o=0,u=qe;o<u;o++)if(Da[o][1].exec(g[3])){R=(g[2]||" ")+Da[o][0];break}if(R==null){i._isValid=!1;return}}if(!k&&R!=null){i._isValid=!1;return}if(g[4])if(aw.exec(g[4]))J="Z";else{i._isValid=!1;return}i._f=C+(R||"")+(J||""),Ma(i)}else i._isValid=!1}function uw(i,o,u,f,g,k){var C=[cw(i),eu.indexOf(o),parseInt(u,10),parseInt(f,10),parseInt(g,10)];return k&&C.push(parseInt(k,10)),C}function cw(i){var o=parseInt(i,10);return o<=49?2e3+o:o<=999?1900+o:o}function fw(i){return i.replace(/\([^()]*\)|[\n\t]/g," ").replace(/(\s\s+)/g," ").replace(/^\s\s*/,"").replace(/\s\s*$/,"")}function dw(i,o,u){if(i){var f=ou.indexOf(i),g=new Date(o[0],o[1],o[2]).getDay();if(f!==g)return v(u).weekdayMismatch=!0,u._isValid=!1,!1}return!0}function hw(i,o,u){if(i)return lw[i];if(o)return 0;var f=parseInt(u,10),g=f%100,k=(f-g)/100;return k*60+g}function du(i){var o=sw.exec(fw(i._i)),u;if(o){if(u=uw(o[4],o[3],o[2],o[5],o[6],o[7]),!dw(o[1],u,i))return;i._a=u,i._tzm=hw(o[8],o[9],o[10]),i._d=Qr.apply(null,i._a),i._d.setUTCMinutes(i._d.getUTCMinutes()-i._tzm),v(i).rfc2822=!0}else i._isValid=!1}function pw(i){var o=ow.exec(i._i);if(o!==null){i._d=new Date(+o[1]);return}if(fu(i),i._isValid===!1)delete i._isValid;else return;if(du(i),i._isValid===!1)delete i._isValid;else return;i._strict?i._isValid=!1:t.createFromInputFallback(i)}t.createFromInputFallback=B("value provided is not in a recognized RFC2822 or ISO format. moment construction falls back to js Date(), which is not reliable across all browsers and versions. Non RFC2822/ISO date formats are discouraged. Please refer to http://momentjs.com/guides/#/warnings/js-date/ for more info.",function(i){i._d=new Date(i._i+(i._useUTC?" UTC":""))});function br(i,o,u){return i??o??u}function mw(i){var o=new Date(t.now());return i._useUTC?[o.getUTCFullYear(),o.getUTCMonth(),o.getUTCDate()]:[o.getFullYear(),o.getMonth(),o.getDate()]}function La(i){var o,u,f=[],g,k,C;if(!i._d){for(g=mw(i),i._w&&i._a[ht]==null&&i._a[kt]==null&&gw(i),i._dayOfYear!=null&&(C=br(i._a[Ae],g[Ae]),(i._dayOfYear>$r(C)||i._dayOfYear===0)&&(v(i)._overflowDayOfYear=!0),u=Qr(C,0,i._dayOfYear),i._a[kt]=u.getUTCMonth(),i._a[ht]=u.getUTCDate()),o=0;o<3&&i._a[o]==null;++o)i._a[o]=f[o]=g[o];for(;o<7;o++)i._a[o]=f[o]=i._a[o]==null?o===2?1:0:i._a[o];i._a[_e]===24&&i._a[lt]===0&&i._a[vt]===0&&i._a[Zt]===0&&(i._nextDay=!0,i._a[_e]=0),i._d=(i._useUTC?Qr:vy).apply(null,f),k=i._useUTC?i._d.getUTCDay():i._d.getDay(),i._tzm!=null&&i._d.setUTCMinutes(i._d.getUTCMinutes()-i._tzm),i._nextDay&&(i._a[_e]=24),i._w&&typeof i._w.d<"u"&&i._w.d!==k&&(v(i).weekdayMismatch=!0)}}function gw(i){var o,u,f,g,k,C,R,J,ie;o=i._w,o.GG!=null||o.W!=null||o.E!=null?(k=1,C=4,u=br(o.GG,i._a[Ae],Zr(fe(),1,4).year),f=br(o.W,1),g=br(o.E,1),(g<1||g>7)&&(J=!0)):(k=i._locale._week.dow,C=i._locale._week.doy,ie=Zr(fe(),k,C),u=br(o.gg,i._a[Ae],ie.year),f=br(o.w,ie.week),o.d!=null?(g=o.d,(g<0||g>6)&&(J=!0)):o.e!=null?(g=o.e+k,(o.e<0||o.e>6)&&(J=!0)):g=k),f<1||f>xt(u,k,C)?v(i)._overflowWeeks=!0:J!=null?v(i)._overflowWeekday=!0:(R=au(u,f,g,k,C),i._a[Ae]=R.year,i._dayOfYear=R.dayOfYear)}t.ISO_8601=function(){},t.RFC_2822=function(){};function Ma(i){if(i._f===t.ISO_8601){fu(i);return}if(i._f===t.RFC_2822){du(i);return}i._a=[],v(i).empty=!0;var o=""+i._i,u,f,g,k,C,R=o.length,J=0,ie,qe;for(g=Jt(i._f,i._locale).match(he)||[],qe=g.length,u=0;u<qe;u++)k=g[u],f=(o.match(ny(k,i))||[])[0],f&&(C=o.substr(0,o.indexOf(f)),C.length>0&&v(i).unusedInput.push(C),o=o.slice(o.indexOf(f)+f.length),J+=f.length),Fe[k]?(f?v(i).empty=!1:v(i).unusedTokens.push(k),ay(k,f,i)):i._strict&&!f&&v(i).unusedTokens.push(k);v(i).charsLeftOver=R-J,o.length>0&&v(i).unusedInput.push(o),i._a[_e]<=12&&v(i).bigHour===!0&&i._a[_e]>0&&(v(i).bigHour=void 0),v(i).parsedDateParts=i._a.slice(0),v(i).meridiem=i._meridiem,i._a[_e]=yw(i._locale,i._a[_e],i._meridiem),ie=v(i).era,ie!==null&&(i._a[Ae]=i._locale.erasConvertYear(ie,i._a[Ae])),La(i),Ta(i)}function yw(i,o,u){var f;return u==null?o:i.meridiemHour!=null?i.meridiemHour(o,u):(i.isPM!=null&&(f=i.isPM(u),f&&o<12&&(o+=12),!f&&o===12&&(o=0)),o)}function ww(i){var o,u,f,g,k,C,R=!1,J=i._f.length;if(J===0){v(i).invalidFormat=!0,i._d=new Date(NaN);return}for(g=0;g<J;g++)k=0,C=!1,o=Z({},i),i._useUTC!=null&&(o._useUTC=i._useUTC),o._f=i._f[g],Ma(o),E(o)&&(C=!0),k+=v(o).charsLeftOver,k+=v(o).unusedTokens.length*10,v(o).score=k,R?k<f&&(f=k,u=o):(f==null||k<f||C)&&(f=k,u=o,C&&(R=!0));m(i,u||o)}function bw(i){if(!i._d){var o=gr(i._i),u=o.day===void 0?o.date:o.day;i._a=h([o.year,o.month,u,o.hour,o.minute,o.second,o.millisecond],function(f){return f&&parseInt(f,10)}),La(i)}}function _w(i){var o=new W(Ta(hu(i)));return o._nextDay&&(o.add(1,"d"),o._nextDay=void 0),o}function hu(i){var o=i._i,u=i._f;return i._locale=i._locale||St(i._l),o===null||u===void 0&&o===""?O({nullInput:!0}):(typeof o=="string"&&(i._i=o=i._locale.preparse(o)),P(o)?new W(Ta(o)):(p(o)?i._d=o:n(u)?ww(i):u?Ma(i):kw(i),E(i)||(i._d=null),i))}function kw(i){var o=i._i;c(o)?i._d=new Date(t.now()):p(o)?i._d=new Date(o.valueOf()):typeof o=="string"?pw(i):n(o)?(i._a=h(o.slice(0),function(u){return parseInt(u,10)}),La(i)):a(o)?bw(i):d(o)?i._d=new Date(o):t.createFromInputFallback(i)}function pu(i,o,u,f,g){var k={};return(o===!0||o===!1)&&(f=o,o=void 0),(u===!0||u===!1)&&(f=u,u=void 0),(a(i)&&l(i)||n(i)&&i.length===0)&&(i=void 0),k._isAMomentObject=!0,k._useUTC=k._isUTC=g,k._l=u,k._i=i,k._f=o,k._strict=f,_w(k)}function fe(i,o,u,f){return pu(i,o,u,f,!1)}var vw=B("moment().min is deprecated, use moment.max instead. http://momentjs.com/guides/#/warnings/min-max/",function(){var i=fe.apply(null,arguments);return this.isValid()&&i.isValid()?i<this?this:i:O()}),xw=B("moment().max is deprecated, use moment.min instead. http://momentjs.com/guides/#/warnings/min-max/",function(){var i=fe.apply(null,arguments);return this.isValid()&&i.isValid()?i>this?this:i:O()});function mu(i,o){var u,f;if(o.length===1&&n(o[0])&&(o=o[0]),!o.length)return fe();for(u=o[0],f=1;f<o.length;++f)(!o[f].isValid()||o[f][i](u))&&(u=o[f]);return u}function Sw(){var i=[].slice.call(arguments,0);return mu("isBefore",i)}function Cw(){var i=[].slice.call(arguments,0);return mu("isAfter",i)}var Pw=function(){return Date.now?Date.now():+new Date},en=["year","quarter","month","week","day","hour","minute","second","millisecond"];function Ow(i){var o,u=!1,f,g=en.length;for(o in i)if(s(i,o)&&!(ge.call(en,o)!==-1&&(i[o]==null||!isNaN(i[o]))))return!1;for(f=0;f<g;++f)if(i[en[f]]){if(u)return!1;parseFloat(i[en[f]])!==ne(i[en[f]])&&(u=!0)}return!0}function Fw(){return this._isValid}function Ew(){return ut(NaN)}function Hn(i){var o=gr(i),u=o.year||0,f=o.quarter||0,g=o.month||0,k=o.week||o.isoWeek||0,C=o.day||0,R=o.hour||0,J=o.minute||0,ie=o.second||0,qe=o.millisecond||0;this._isValid=Ow(o),this._milliseconds=+qe+ie*1e3+J*6e4+R*1e3*60*60,this._days=+C+k*7,this._months=+g+f*3+u*12,this._data={},this._locale=St(),this._bubble()}function Gn(i){return i instanceof Hn}function Ia(i){return i<0?Math.round(-1*i)*-1:Math.round(i)}function Aw(i,o,u){var f=Math.min(i.length,o.length),g=Math.abs(i.length-o.length),k=0,C;for(C=0;C<f;C++)(u&&i[C]!==o[C]||!u&&ne(i[C])!==ne(o[C]))&&k++;return k+g}function gu(i,o){b(i,0,0,function(){var u=this.utcOffset(),f="+";return u<0&&(u=-u,f="-"),f+se(~~(u/60),2)+o+se(~~u%60,2)})}gu("Z",":"),gu("ZZ",""),q("Z",ot),q("ZZ",ot),ue(["Z","ZZ"],function(i,o,u){u._useUTC=!0,u._tzm=Na(ot,i)});var Tw=/([\+\-]|\d\d)/gi;function Na(i,o){var u=(o||"").match(i),f,g,k;return u===null?null:(f=u[u.length-1]||[],g=(f+"").match(Tw)||["-",0,0],k=+(g[1]*60)+ne(g[2]),k===0?0:g[0]==="+"?k:-k)}function Ra(i,o){var u,f;return o._isUTC?(u=o.clone(),f=(P(i)||p(i)?i.valueOf():fe(i).valueOf())-u.valueOf(),u._d.setTime(u._d.valueOf()+f),t.updateOffset(u,!1),u):fe(i).local()}function qa(i){return-Math.round(i._d.getTimezoneOffset())}t.updateOffset=function(){};function Dw(i,o,u){var f=this._offset||0,g;if(!this.isValid())return i!=null?this:NaN;if(i!=null){if(typeof i=="string"){if(i=Na(ot,i),i===null)return this}else Math.abs(i)<16&&!u&&(i=i*60);return!this._isUTC&&o&&(g=qa(this)),this._offset=i,this._isUTC=!0,g!=null&&this.add(g,"m"),f!==i&&(!o||this._changeInProgress?_u(this,ut(i-f,"m"),1,!1):this._changeInProgress||(this._changeInProgress=!0,t.updateOffset(this,!0),this._changeInProgress=null)),this}else return this._isUTC?f:qa(this)}function Lw(i,o){return i!=null?(typeof i!="string"&&(i=-i),this.utcOffset(i,o),this):-this.utcOffset()}function Mw(i){return this.utcOffset(0,i)}function Iw(i){return this._isUTC&&(this.utcOffset(0,i),this._isUTC=!1,i&&this.subtract(qa(this),"m")),this}function Nw(){if(this._tzm!=null)this.utcOffset(this._tzm,!1,!0);else if(typeof this._i=="string"){var i=Na(Re,this._i);i!=null?this.utcOffset(i):this.utcOffset(0,!0)}return this}function Rw(i){return this.isValid()?(i=i?fe(i).utcOffset():0,(this.utcOffset()-i)%60===0):!1}function qw(){return this.utcOffset()>this.clone().month(0).utcOffset()||this.utcOffset()>this.clone().month(5).utcOffset()}function jw(){if(!c(this._isDSTShifted))return this._isDSTShifted;var i={},o;return Z(i,this),i=hu(i),i._a?(o=i._isUTC?y(i._a):fe(i._a),this._isDSTShifted=this.isValid()&&Aw(i._a,o.toArray())>0):this._isDSTShifted=!1,this._isDSTShifted}function Bw(){return this.isValid()?!this._isUTC:!1}function Yw(){return this.isValid()?this._isUTC:!1}function yu(){return this.isValid()?this._isUTC&&this._offset===0:!1}var Vw=/^(-|\+)?(?:(\d*)[. ])?(\d+):(\d+)(?::(\d+)(\.\d*)?)?$/,zw=/^(-|\+)?P(?:([-+]?[0-9,.]*)Y)?(?:([-+]?[0-9,.]*)M)?(?:([-+]?[0-9,.]*)W)?(?:([-+]?[0-9,.]*)D)?(?:T(?:([-+]?[0-9,.]*)H)?(?:([-+]?[0-9,.]*)M)?(?:([-+]?[0-9,.]*)S)?)?$/;function ut(i,o){var u=i,f=null,g,k,C;return Gn(i)?u={ms:i._milliseconds,d:i._days,M:i._months}:d(i)||!isNaN(+i)?(u={},o?u[o]=+i:u.milliseconds=+i):(f=Vw.exec(i))?(g=f[1]==="-"?-1:1,u={y:0,d:ne(f[ht])*g,h:ne(f[_e])*g,m:ne(f[lt])*g,s:ne(f[vt])*g,ms:ne(Ia(f[Zt]*1e3))*g}):(f=zw.exec(i))?(g=f[1]==="-"?-1:1,u={y:Kt(f[2],g),M:Kt(f[3],g),w:Kt(f[4],g),d:Kt(f[5],g),h:Kt(f[6],g),m:Kt(f[7],g),s:Kt(f[8],g)}):u==null?u={}:typeof u=="object"&&("from"in u||"to"in u)&&(C=Uw(fe(u.from),fe(u.to)),u={},u.ms=C.milliseconds,u.M=C.months),k=new Hn(u),Gn(i)&&s(i,"_locale")&&(k._locale=i._locale),Gn(i)&&s(i,"_isValid")&&(k._isValid=i._isValid),k}ut.fn=Hn.prototype,ut.invalid=Ew;function Kt(i,o){var u=i&&parseFloat(i.replace(",","."));return(isNaN(u)?0:u)*o}function wu(i,o){var u={};return u.months=o.month()-i.month()+(o.year()-i.year())*12,i.clone().add(u.months,"M").isAfter(o)&&--u.months,u.milliseconds=+o-+i.clone().add(u.months,"M"),u}function Uw(i,o){var u;return i.isValid()&&o.isValid()?(o=Ra(o,i),i.isBefore(o)?u=wu(i,o):(u=wu(o,i),u.milliseconds=-u.milliseconds,u.months=-u.months),u):{milliseconds:0,months:0}}function bu(i,o){return function(u,f){var g,k;return f!==null&&!isNaN(+f)&&(L(o,"moment()."+o+"(period, number) is deprecated. Please use moment()."+o+"(number, period). See http://momentjs.com/guides/#/warnings/add-inverted-param/ for more info."),k=u,u=f,f=k),g=ut(u,f),_u(this,g,i),this}}function _u(i,o,u,f){var g=o._milliseconds,k=Ia(o._days),C=Ia(o._months);i.isValid()&&(f=f??!0,C&&ru(i,Jr(i,"Month")+C*u),k&&Xl(i,"Date",Jr(i,"Date")+k*u),g&&i._d.setTime(i._d.valueOf()+g*u),f&&t.updateOffset(i,k||C))}var Ww=bu(1,"add"),Hw=bu(-1,"subtract");function ku(i){return typeof i=="string"||i instanceof String}function Gw(i){return P(i)||p(i)||ku(i)||d(i)||Jw(i)||$w(i)||i===null||i===void 0}function $w(i){var o=a(i)&&!l(i),u=!1,f=["years","year","y","months","month","M","days","day","d","dates","date","D","hours","hour","h","minutes","minute","m","seconds","second","s","milliseconds","millisecond","ms"],g,k,C=f.length;for(g=0;g<C;g+=1)k=f[g],u=u||s(i,k);return o&&u}function Jw(i){var o=n(i),u=!1;return o&&(u=i.filter(function(f){return!d(f)&&ku(i)}).length===0),o&&u}function Qw(i){var o=a(i)&&!l(i),u=!1,f=["sameDay","nextDay","lastDay","nextWeek","lastWeek","sameElse"],g,k;for(g=0;g<f.length;g+=1)k=f[g],u=u||s(i,k);return o&&u}function Zw(i,o){var u=i.diff(o,"days",!0);return u<-6?"sameElse":u<-1?"lastWeek":u<0?"lastDay":u<1?"sameDay":u<2?"nextDay":u<7?"nextWeek":"sameElse"}function Kw(i,o){arguments.length===1&&(arguments[0]?Gw(arguments[0])?(i=arguments[0],o=void 0):Qw(arguments[0])&&(o=arguments[0],i=void 0):(i=void 0,o=void 0));var u=i||fe(),f=Ra(u,this).startOf("day"),g=t.calendarFormat(this,f)||"sameElse",k=o&&(H(o[g])?o[g].call(this,u):o[g]);return this.format(k||this.localeData().calendar(g,this,fe(u)))}function Xw(){return new W(this)}function eb(i,o){var u=P(i)?i:fe(i);return this.isValid()&&u.isValid()?(o=Ie(o)||"millisecond",o==="millisecond"?this.valueOf()>u.valueOf():u.valueOf()<this.clone().startOf(o).valueOf()):!1}function tb(i,o){var u=P(i)?i:fe(i);return this.isValid()&&u.isValid()?(o=Ie(o)||"millisecond",o==="millisecond"?this.valueOf()<u.valueOf():this.clone().endOf(o).valueOf()<u.valueOf()):!1}function rb(i,o,u,f){var g=P(i)?i:fe(i),k=P(o)?o:fe(o);return this.isValid()&&g.isValid()&&k.isValid()?(f=f||"()",(f[0]==="("?this.isAfter(g,u):!this.isBefore(g,u))&&(f[1]===")"?this.isBefore(k,u):!this.isAfter(k,u))):!1}function nb(i,o){var u=P(i)?i:fe(i),f;return this.isValid()&&u.isValid()?(o=Ie(o)||"millisecond",o==="millisecond"?this.valueOf()===u.valueOf():(f=u.valueOf(),this.clone().startOf(o).valueOf()<=f&&f<=this.clone().endOf(o).valueOf())):!1}function ib(i,o){return this.isSame(i,o)||this.isAfter(i,o)}function ab(i,o){return this.isSame(i,o)||this.isBefore(i,o)}function ob(i,o,u){var f,g,k;if(!this.isValid())return NaN;if(f=Ra(i,this),!f.isValid())return NaN;switch(g=(f.utcOffset()-this.utcOffset())*6e4,o=Ie(o),o){case"year":k=$n(this,f)/12;break;case"month":k=$n(this,f);break;case"quarter":k=$n(this,f)/3;break;case"second":k=(this-f)/1e3;break;case"minute":k=(this-f)/6e4;break;case"hour":k=(this-f)/36e5;break;case"day":k=(this-f-g)/864e5;break;case"week":k=(this-f-g)/6048e5;break;default:k=this-f}return u?k:Ze(k)}function $n(i,o){if(i.date()<o.date())return-$n(o,i);var u=(o.year()-i.year())*12+(o.month()-i.month()),f=i.clone().add(u,"months"),g,k;return o-f<0?(g=i.clone().add(u-1,"months"),k=(o-f)/(f-g)):(g=i.clone().add(u+1,"months"),k=(o-f)/(g-f)),-(u+k)||0}t.defaultFormat="YYYY-MM-DDTHH:mm:ssZ",t.defaultFormatUtc="YYYY-MM-DDTHH:mm:ss[Z]";function sb(){return this.clone().locale("en").format("ddd MMM DD YYYY HH:mm:ss [GMT]ZZ")}function lb(i){if(!this.isValid())return null;var o=i!==!0,u=o?this.clone().utc():this;return u.year()<0||u.year()>9999?Lt(u,o?"YYYYYY-MM-DD[T]HH:mm:ss.SSS[Z]":"YYYYYY-MM-DD[T]HH:mm:ss.SSSZ"):H(Date.prototype.toISOString)?o?this.toDate().toISOString():new Date(this.valueOf()+this.utcOffset()*60*1e3).toISOString().replace("Z",Lt(u,"Z")):Lt(u,o?"YYYY-MM-DD[T]HH:mm:ss.SSS[Z]":"YYYY-MM-DD[T]HH:mm:ss.SSSZ")}function ub(){if(!this.isValid())return"moment.invalid(/* "+this._i+" */)";var i="moment",o="",u,f,g,k;return this.isLocal()||(i=this.utcOffset()===0?"moment.utc":"moment.parseZone",o="Z"),u="["+i+'("]',f=0<=this.year()&&this.year()<=9999?"YYYY":"YYYYYY",g="-MM-DD[T]HH:mm:ss.SSS",k=o+'[")]',this.format(u+f+g+k)}function cb(i){i||(i=this.isUtc()?t.defaultFormatUtc:t.defaultFormat);var o=Lt(this,i);return this.localeData().postformat(o)}function fb(i,o){return this.isValid()&&(P(i)&&i.isValid()||fe(i).isValid())?ut({to:this,from:i}).locale(this.locale()).humanize(!o):this.localeData().invalidDate()}function db(i){return this.from(fe(),i)}function hb(i,o){return this.isValid()&&(P(i)&&i.isValid()||fe(i).isValid())?ut({from:this,to:i}).locale(this.locale()).humanize(!o):this.localeData().invalidDate()}function pb(i){return this.to(fe(),i)}function vu(i){var o;return i===void 0?this._locale._abbr:(o=St(i),o!=null&&(this._locale=o),this)}var xu=B("moment().lang() is deprecated. Instead, use moment().localeData() to get the language configuration. Use moment().locale() to change languages.",function(i){return i===void 0?this.localeData():this.locale(i)});function Su(){return this._locale}var Jn=1e3,_r=60*Jn,Qn=60*_r,Cu=(365*400+97)*24*Qn;function kr(i,o){return(i%o+o)%o}function Pu(i,o,u){return i<100&&i>=0?new Date(i+400,o,u)-Cu:new Date(i,o,u).valueOf()}function Ou(i,o,u){return i<100&&i>=0?Date.UTC(i+400,o,u)-Cu:Date.UTC(i,o,u)}function mb(i){var o,u;if(i=Ie(i),i===void 0||i==="millisecond"||!this.isValid())return this;switch(u=this._isUTC?Ou:Pu,i){case"year":o=u(this.year(),0,1);break;case"quarter":o=u(this.year(),this.month()-this.month()%3,1);break;case"month":o=u(this.year(),this.month(),1);break;case"week":o=u(this.year(),this.month(),this.date()-this.weekday());break;case"isoWeek":o=u(this.year(),this.month(),this.date()-(this.isoWeekday()-1));break;case"day":case"date":o=u(this.year(),this.month(),this.date());break;case"hour":o=this._d.valueOf(),o-=kr(o+(this._isUTC?0:this.utcOffset()*_r),Qn);break;case"minute":o=this._d.valueOf(),o-=kr(o,_r);break;case"second":o=this._d.valueOf(),o-=kr(o,Jn);break}return this._d.setTime(o),t.updateOffset(this,!0),this}function gb(i){var o,u;if(i=Ie(i),i===void 0||i==="millisecond"||!this.isValid())return this;switch(u=this._isUTC?Ou:Pu,i){case"year":o=u(this.year()+1,0,1)-1;break;case"quarter":o=u(this.year(),this.month()-this.month()%3+3,1)-1;break;case"month":o=u(this.year(),this.month()+1,1)-1;break;case"week":o=u(this.year(),this.month(),this.date()-this.weekday()+7)-1;break;case"isoWeek":o=u(this.year(),this.month(),this.date()-(this.isoWeekday()-1)+7)-1;break;case"day":case"date":o=u(this.year(),this.month(),this.date()+1)-1;break;case"hour":o=this._d.valueOf(),o+=Qn-kr(o+(this._isUTC?0:this.utcOffset()*_r),Qn)-1;break;case"minute":o=this._d.valueOf(),o+=_r-kr(o,_r)-1;break;case"second":o=this._d.valueOf(),o+=Jn-kr(o,Jn)-1;break}return this._d.setTime(o),t.updateOffset(this,!0),this}function yb(){return this._d.valueOf()-(this._offset||0)*6e4}function wb(){return Math.floor(this.valueOf()/1e3)}function bb(){return new Date(this.valueOf())}function _b(){var i=this;return[i.year(),i.month(),i.date(),i.hour(),i.minute(),i.second(),i.millisecond()]}function kb(){var i=this;return{years:i.year(),months:i.month(),date:i.date(),hours:i.hours(),minutes:i.minutes(),seconds:i.seconds(),milliseconds:i.milliseconds()}}function vb(){return this.isValid()?this.toISOString():null}function xb(){return E(this)}function Sb(){return m({},v(this))}function Cb(){return v(this).overflow}function Pb(){return{input:this._i,format:this._f,locale:this._locale,isUTC:this._isUTC,strict:this._strict}}b("N",0,0,"eraAbbr"),b("NN",0,0,"eraAbbr"),b("NNN",0,0,"eraAbbr"),b("NNNN",0,0,"eraName"),b("NNNNN",0,0,"eraNarrow"),b("y",["y",1],"yo","eraYear"),b("y",["yy",2],0,"eraYear"),b("y",["yyy",3],0,"eraYear"),b("y",["yyyy",4],0,"eraYear"),q("N",ja),q("NN",ja),q("NNN",ja),q("NNNN",Rb),q("NNNNN",qb),ue(["N","NN","NNN","NNNN","NNNNN"],function(i,o,u,f){var g=u._locale.erasParse(i,f,u._strict);g?v(u).era=g:v(u).invalidEra=i}),q("y",Qe),q("yy",Qe),q("yyy",Qe),q("yyyy",Qe),q("yo",jb),ue(["y","yy","yyy","yyyy"],Ae),ue(["yo"],function(i,o,u,f){var g;u._locale._eraYearOrdinalRegex&&(g=i.match(u._locale._eraYearOrdinalRegex)),u._locale.eraYearOrdinalParse?o[Ae]=u._locale.eraYearOrdinalParse(i,g):o[Ae]=parseInt(i,10)});function Ob(i,o){var u,f,g,k=this._eras||St("en")._eras;for(u=0,f=k.length;u<f;++u){switch(typeof k[u].since){case"string":g=t(k[u].since).startOf("day"),k[u].since=g.valueOf();break}switch(typeof k[u].until){case"undefined":k[u].until=1/0;break;case"string":g=t(k[u].until).startOf("day").valueOf(),k[u].until=g.valueOf();break}}return k}function Fb(i,o,u){var f,g,k=this.eras(),C,R,J;for(i=i.toUpperCase(),f=0,g=k.length;f<g;++f)if(C=k[f].name.toUpperCase(),R=k[f].abbr.toUpperCase(),J=k[f].narrow.toUpperCase(),u)switch(o){case"N":case"NN":case"NNN":if(R===i)return k[f];break;case"NNNN":if(C===i)return k[f];break;case"NNNNN":if(J===i)return k[f];break}else if([C,R,J].indexOf(i)>=0)return k[f]}function Eb(i,o){var u=i.since<=i.until?1:-1;return o===void 0?t(i.since).year():t(i.since).year()+(o-i.offset)*u}function Ab(){var i,o,u,f=this.localeData().eras();for(i=0,o=f.length;i<o;++i)if(u=this.clone().startOf("day").valueOf(),f[i].since<=u&&u<=f[i].until||f[i].until<=u&&u<=f[i].since)return f[i].name;return""}function Tb(){var i,o,u,f=this.localeData().eras();for(i=0,o=f.length;i<o;++i)if(u=this.clone().startOf("day").valueOf(),f[i].since<=u&&u<=f[i].until||f[i].until<=u&&u<=f[i].since)return f[i].narrow;return""}function Db(){var i,o,u,f=this.localeData().eras();for(i=0,o=f.length;i<o;++i)if(u=this.clone().startOf("day").valueOf(),f[i].since<=u&&u<=f[i].until||f[i].until<=u&&u<=f[i].since)return f[i].abbr;return""}function Lb(){var i,o,u,f,g=this.localeData().eras();for(i=0,o=g.length;i<o;++i)if(u=g[i].since<=g[i].until?1:-1,f=this.clone().startOf("day").valueOf(),g[i].since<=f&&f<=g[i].until||g[i].until<=f&&f<=g[i].since)return(this.year()-t(g[i].since).year())*u+g[i].offset;return this.year()}function Mb(i){return s(this,"_erasNameRegex")||Ba.call(this),i?this._erasNameRegex:this._erasRegex}function Ib(i){return s(this,"_erasAbbrRegex")||Ba.call(this),i?this._erasAbbrRegex:this._erasRegex}function Nb(i){return s(this,"_erasNarrowRegex")||Ba.call(this),i?this._erasNarrowRegex:this._erasRegex}function ja(i,o){return o.erasAbbrRegex(i)}function Rb(i,o){return o.erasNameRegex(i)}function qb(i,o){return o.erasNarrowRegex(i)}function jb(i,o){return o._eraYearOrdinalRegex||Qe}function Ba(){var i=[],o=[],u=[],f=[],g,k,C,R,J,ie=this.eras();for(g=0,k=ie.length;g<k;++g)C=_t(ie[g].name),R=_t(ie[g].abbr),J=_t(ie[g].narrow),o.push(C),i.push(R),u.push(J),f.push(C),f.push(R),f.push(J);this._erasRegex=new RegExp("^("+f.join("|")+")","i"),this._erasNameRegex=new RegExp("^("+o.join("|")+")","i"),this._erasAbbrRegex=new RegExp("^("+i.join("|")+")","i"),this._erasNarrowRegex=new RegExp("^("+u.join("|")+")","i")}b(0,["gg",2],0,function(){return this.weekYear()%100}),b(0,["GG",2],0,function(){return this.isoWeekYear()%100});function Zn(i,o){b(0,[i,i.length],0,o)}Zn("gggg","weekYear"),Zn("ggggg","weekYear"),Zn("GGGG","isoWeekYear"),Zn("GGGGG","isoWeekYear"),q("G",dt),q("g",dt),q("GG",I,Ne),q("gg",I,Ne),q("GGGG",at,F),q("gggg",at,F),q("GGGGG",Ve,D),q("ggggg",Ve,D),Gr(["gggg","ggggg","GGGG","GGGGG"],function(i,o,u,f){o[f.substr(0,2)]=ne(i)}),Gr(["gg","GG"],function(i,o,u,f){o[f]=t.parseTwoDigitYear(i)});function Bb(i){return Fu.call(this,i,this.week(),this.weekday()+this.localeData()._week.dow,this.localeData()._week.dow,this.localeData()._week.doy)}function Yb(i){return Fu.call(this,i,this.isoWeek(),this.isoWeekday(),1,4)}function Vb(){return xt(this.year(),1,4)}function zb(){return xt(this.isoWeekYear(),1,4)}function Ub(){var i=this.localeData()._week;return xt(this.year(),i.dow,i.doy)}function Wb(){var i=this.localeData()._week;return xt(this.weekYear(),i.dow,i.doy)}function Fu(i,o,u,f,g){var k;return i==null?Zr(this,f,g).year:(k=xt(i,f,g),o>k&&(o=k),Hb.call(this,i,o,u,f,g))}function Hb(i,o,u,f,g){var k=au(i,o,u,f,g),C=Qr(k.year,0,k.dayOfYear);return this.year(C.getUTCFullYear()),this.month(C.getUTCMonth()),this.date(C.getUTCDate()),this}b("Q",0,"Qo","quarter"),q("Q",jn),ue("Q",function(i,o){o[kt]=(ne(i)-1)*3});function Gb(i){return i==null?Math.ceil((this.month()+1)/3):this.month((i-1)*3+this.month()%3)}b("D",["DD",2],"Do","date"),q("D",I,yr),q("DD",I,Ne),q("Do",function(i,o){return i?o._dayOfMonthOrdinalParse||o._ordinalParse:o._dayOfMonthOrdinalParseLenient}),ue(["D","DD"],ht),ue("Do",function(i,o){o[ht]=ne(i.match(I)[0])});var Eu=wr("Date",!0);b("DDD",["DDDD",3],"DDDo","dayOfYear"),q("DDD",Ee),q("DDDD",Bn),ue(["DDD","DDDD"],function(i,o,u){u._dayOfYear=ne(i)});function $b(i){var o=Math.round((this.clone().startOf("day")-this.clone().startOf("year"))/864e5)+1;return i==null?o:this.add(i-o,"d")}b("m",["mm",2],0,"minute"),q("m",I,Sa),q("mm",I,Ne),ue(["m","mm"],lt);var Jb=wr("Minutes",!1);b("s",["ss",2],0,"second"),q("s",I,Sa),q("ss",I,Ne),ue(["s","ss"],vt);var Qb=wr("Seconds",!1);b("S",0,0,function(){return~~(this.millisecond()/100)}),b(0,["SS",2],0,function(){return~~(this.millisecond()/10)}),b(0,["SSS",3],0,"millisecond"),b(0,["SSSS",4],0,function(){return this.millisecond()*10}),b(0,["SSSSS",5],0,function(){return this.millisecond()*100}),b(0,["SSSSSS",6],0,function(){return this.millisecond()*1e3}),b(0,["SSSSSSS",7],0,function(){return this.millisecond()*1e4}),b(0,["SSSSSSSS",8],0,function(){return this.millisecond()*1e5}),b(0,["SSSSSSSSS",9],0,function(){return this.millisecond()*1e6}),q("S",Ee,jn),q("SS",Ee,Ne),q("SSS",Ee,Bn);var Nt,Au;for(Nt="SSSS";Nt.length<=9;Nt+="S")q(Nt,Qe);function Zb(i,o){o[Zt]=ne(("0."+i)*1e3)}for(Nt="S";Nt.length<=9;Nt+="S")ue(Nt,Zb);Au=wr("Milliseconds",!1),b("z",0,0,"zoneAbbr"),b("zz",0,0,"zoneName");function Kb(){return this._isUTC?"UTC":""}function Xb(){return this._isUTC?"Coordinated Universal Time":""}var T=W.prototype;T.add=Ww,T.calendar=Kw,T.clone=Xw,T.diff=ob,T.endOf=gb,T.format=cb,T.from=fb,T.fromNow=db,T.to=hb,T.toNow=pb,T.get=uy,T.invalidAt=Cb,T.isAfter=eb,T.isBefore=tb,T.isBetween=rb,T.isSame=nb,T.isSameOrAfter=ib,T.isSameOrBefore=ab,T.isValid=xb,T.lang=xu,T.locale=vu,T.localeData=Su,T.max=xw,T.min=vw,T.parsingFlags=Sb,T.set=cy,T.startOf=mb,T.subtract=Hw,T.toArray=_b,T.toObject=kb,T.toDate=bb,T.toISOString=lb,T.inspect=ub,typeof Symbol<"u"&&Symbol.for!=null&&(T[Symbol.for("nodejs.util.inspect.custom")]=function(){return"Moment<"+this.format()+">"}),T.toJSON=vb,T.toString=sb,T.unix=wb,T.valueOf=yb,T.creationData=Pb,T.eraName=Ab,T.eraNarrow=Tb,T.eraAbbr=Db,T.eraYear=Lb,T.year=Kl,T.isLeapYear=ly,T.weekYear=Bb,T.isoWeekYear=Yb,T.quarter=T.quarters=Gb,T.month=nu,T.daysInMonth=by,T.week=T.weeks=Oy,T.isoWeek=T.isoWeeks=Fy,T.weeksInYear=Ub,T.weeksInWeekYear=Wb,T.isoWeeksInYear=Vb,T.isoWeeksInISOWeekYear=zb,T.date=Eu,T.day=T.days=Yy,T.weekday=Vy,T.isoWeekday=zy,T.dayOfYear=$b,T.hour=T.hours=Qy,T.minute=T.minutes=Jb,T.second=T.seconds=Qb,T.millisecond=T.milliseconds=Au,T.utcOffset=Dw,T.utc=Mw,T.local=Iw,T.parseZone=Nw,T.hasAlignedHourOffset=Rw,T.isDST=qw,T.isLocal=Bw,T.isUtcOffset=Yw,T.isUtc=yu,T.isUTC=yu,T.zoneAbbr=Kb,T.zoneName=Xb,T.dates=B("dates accessor is deprecated. Use date instead.",Eu),T.months=B("months accessor is deprecated. Use month instead",nu),T.years=B("years accessor is deprecated. Use year instead",Kl),T.zone=B("moment().zone is deprecated, use moment().utcOffset instead. http://momentjs.com/guides/#/warnings/zone/",Lw),T.isDSTShifted=B("isDSTShifted is deprecated. See http://momentjs.com/guides/#/warnings/dst-shifted/ for more information",jw);function e_(i){return fe(i*1e3)}function t_(){return fe.apply(null,arguments).parseZone()}function Tu(i){return i}var oe=Y.prototype;oe.calendar=re,oe.longDateFormat=Qt,oe.invalidDate=bt,oe.ordinal=ka,oe.preparse=Tu,oe.postformat=Tu,oe.relativeTime=Nn,oe.pastFuture=Rn,oe.set=K,oe.eras=Ob,oe.erasParse=Fb,oe.erasConvertYear=Eb,oe.erasAbbrRegex=Ib,oe.erasNameRegex=Mb,oe.erasNarrowRegex=Nb,oe.months=my,oe.monthsShort=gy,oe.monthsParse=wy,oe.monthsRegex=ky,oe.monthsShortRegex=_y,oe.week=xy,oe.firstDayOfYear=Py,oe.firstDayOfWeek=Cy,oe.weekdays=Ny,oe.weekdaysMin=qy,oe.weekdaysShort=Ry,oe.weekdaysParse=By,oe.weekdaysRegex=Uy,oe.weekdaysShortRegex=Wy,oe.weekdaysMinRegex=Hy,oe.isPM=$y,oe.meridiem=Zy;function Kn(i,o,u,f){var g=St(),k=y().set(f,o);return g[u](k,i)}function Du(i,o,u){if(d(i)&&(o=i,i=void 0),i=i||"",o!=null)return Kn(i,o,u,"month");var f,g=[];for(f=0;f<12;f++)g[f]=Kn(i,f,u,"month");return g}function Ya(i,o,u,f){typeof i=="boolean"?(d(o)&&(u=o,o=void 0),o=o||""):(o=i,u=o,i=!1,d(o)&&(u=o,o=void 0),o=o||"");var g=St(),k=i?g._week.dow:0,C,R=[];if(u!=null)return Kn(o,(u+k)%7,f,"day");for(C=0;C<7;C++)R[C]=Kn(o,(C+k)%7,f,"day");return R}function r_(i,o){return Du(i,o,"months")}function n_(i,o){return Du(i,o,"monthsShort")}function i_(i,o,u){return Ya(i,o,u,"weekdays")}function a_(i,o,u){return Ya(i,o,u,"weekdaysShort")}function o_(i,o,u){return Ya(i,o,u,"weekdaysMin")}It("en",{eras:[{since:"0001-01-01",until:1/0,offset:1,name:"Anno Domini",narrow:"AD",abbr:"AD"},{since:"0000-12-31",until:-1/0,offset:1,name:"Before Christ",narrow:"BC",abbr:"BC"}],dayOfMonthOrdinalParse:/\d{1,2}(th|st|nd|rd)/,ordinal:function(i){var o=i%10,u=ne(i%100/10)===1?"th":o===1?"st":o===2?"nd":o===3?"rd":"th";return i+u}}),t.lang=B("moment.lang is deprecated. Use moment.locale instead.",It),t.langData=B("moment.langData is deprecated. Use moment.localeData instead.",St);var Ct=Math.abs;function s_(){var i=this._data;return this._milliseconds=Ct(this._milliseconds),this._days=Ct(this._days),this._months=Ct(this._months),i.milliseconds=Ct(i.milliseconds),i.seconds=Ct(i.seconds),i.minutes=Ct(i.minutes),i.hours=Ct(i.hours),i.months=Ct(i.months),i.years=Ct(i.years),this}function Lu(i,o,u,f){var g=ut(o,u);return i._milliseconds+=f*g._milliseconds,i._days+=f*g._days,i._months+=f*g._months,i._bubble()}function l_(i,o){return Lu(this,i,o,1)}function u_(i,o){return Lu(this,i,o,-1)}function Mu(i){return i<0?Math.floor(i):Math.ceil(i)}function c_(){var i=this._milliseconds,o=this._days,u=this._months,f=this._data,g,k,C,R,J;return i>=0&&o>=0&&u>=0||i<=0&&o<=0&&u<=0||(i+=Mu(Va(u)+o)*864e5,o=0,u=0),f.milliseconds=i%1e3,g=Ze(i/1e3),f.seconds=g%60,k=Ze(g/60),f.minutes=k%60,C=Ze(k/60),f.hours=C%24,o+=Ze(C/24),J=Ze(Iu(o)),u+=J,o-=Mu(Va(J)),R=Ze(u/12),u%=12,f.days=o,f.months=u,f.years=R,this}function Iu(i){return i*4800/146097}function Va(i){return i*146097/4800}function f_(i){if(!this.isValid())return NaN;var o,u,f=this._milliseconds;if(i=Ie(i),i==="month"||i==="quarter"||i==="year")switch(o=this._days+f/864e5,u=this._months+Iu(o),i){case"month":return u;case"quarter":return u/3;case"year":return u/12}else switch(o=this._days+Math.round(Va(this._months)),i){case"week":return o/7+f/6048e5;case"day":return o+f/864e5;case"hour":return o*24+f/36e5;case"minute":return o*1440+f/6e4;case"second":return o*86400+f/1e3;case"millisecond":return Math.floor(o*864e5)+f;default:throw new Error("Unknown unit "+i)}}function Pt(i){return function(){return this.as(i)}}var Nu=Pt("ms"),d_=Pt("s"),h_=Pt("m"),p_=Pt("h"),m_=Pt("d"),g_=Pt("w"),y_=Pt("M"),w_=Pt("Q"),b_=Pt("y"),__=Nu;function k_(){return ut(this)}function v_(i){return i=Ie(i),this.isValid()?this[i+"s"]():NaN}function Xt(i){return function(){return this.isValid()?this._data[i]:NaN}}var x_=Xt("milliseconds"),S_=Xt("seconds"),C_=Xt("minutes"),P_=Xt("hours"),O_=Xt("days"),F_=Xt("months"),E_=Xt("years");function A_(){return Ze(this.days()/7)}var Ot=Math.round,vr={ss:44,s:45,m:45,h:22,d:26,w:null,M:11};function T_(i,o,u,f,g){return g.relativeTime(o||1,!!u,i,f)}function D_(i,o,u,f){var g=ut(i).abs(),k=Ot(g.as("s")),C=Ot(g.as("m")),R=Ot(g.as("h")),J=Ot(g.as("d")),ie=Ot(g.as("M")),qe=Ot(g.as("w")),Ft=Ot(g.as("y")),Rt=k<=u.ss&&["s",k]||k<u.s&&["ss",k]||C<=1&&["m"]||C<u.m&&["mm",C]||R<=1&&["h"]||R<u.h&&["hh",R]||J<=1&&["d"]||J<u.d&&["dd",J];return u.w!=null&&(Rt=Rt||qe<=1&&["w"]||qe<u.w&&["ww",qe]),Rt=Rt||ie<=1&&["M"]||ie<u.M&&["MM",ie]||Ft<=1&&["y"]||["yy",Ft],Rt[2]=o,Rt[3]=+i>0,Rt[4]=f,T_.apply(null,Rt)}function L_(i){return i===void 0?Ot:typeof i=="function"?(Ot=i,!0):!1}function M_(i,o){return vr[i]===void 0?!1:o===void 0?vr[i]:(vr[i]=o,i==="s"&&(vr.ss=o-1),!0)}function I_(i,o){if(!this.isValid())return this.localeData().invalidDate();var u=!1,f=vr,g,k;return typeof i=="object"&&(o=i,i=!1),typeof i=="boolean"&&(u=i),typeof o=="object"&&(f=Object.assign({},vr,o),o.s!=null&&o.ss==null&&(f.ss=o.s-1)),g=this.localeData(),k=D_(this,!u,f,g),u&&(k=g.pastFuture(+this,k)),g.postformat(k)}var za=Math.abs;function xr(i){return(i>0)-(i<0)||+i}function Xn(){if(!this.isValid())return this.localeData().invalidDate();var i=za(this._milliseconds)/1e3,o=za(this._days),u=za(this._months),f,g,k,C,R=this.asSeconds(),J,ie,qe,Ft;return R?(f=Ze(i/60),g=Ze(f/60),i%=60,f%=60,k=Ze(u/12),u%=12,C=i?i.toFixed(3).replace(/\.?0+$/,""):"",J=R<0?"-":"",ie=xr(this._months)!==xr(R)?"-":"",qe=xr(this._days)!==xr(R)?"-":"",Ft=xr(this._milliseconds)!==xr(R)?"-":"",J+"P"+(k?ie+k+"Y":"")+(u?ie+u+"M":"")+(o?qe+o+"D":"")+(g||f||i?"T":"")+(g?Ft+g+"H":"")+(f?Ft+f+"M":"")+(i?Ft+C+"S":"")):"P0D"}var ae=Hn.prototype;ae.isValid=Fw,ae.abs=s_,ae.add=l_,ae.subtract=u_,ae.as=f_,ae.asMilliseconds=Nu,ae.asSeconds=d_,ae.asMinutes=h_,ae.asHours=p_,ae.asDays=m_,ae.asWeeks=g_,ae.asMonths=y_,ae.asQuarters=w_,ae.asYears=b_,ae.valueOf=__,ae._bubble=c_,ae.clone=k_,ae.get=v_,ae.milliseconds=x_,ae.seconds=S_,ae.minutes=C_,ae.hours=P_,ae.days=O_,ae.weeks=A_,ae.months=F_,ae.years=E_,ae.humanize=I_,ae.toISOString=Xn,ae.toString=Xn,ae.toJSON=Xn,ae.locale=vu,ae.localeData=Su,ae.toIsoString=B("toIsoString() is deprecated. Please use toISOString() instead (notice the capitals)",Xn),ae.lang=xu,b("X",0,0,"unix"),b("x",0,0,"valueOf"),q("x",dt),q("X",st),ue("X",function(i,o,u){u._d=new Date(parseFloat(i)*1e3)}),ue("x",function(i,o,u){u._d=new Date(ne(i))});return t.version="2.30.1",r(fe),t.fn=T,t.min=Sw,t.max=Cw,t.now=Pw,t.utc=y,t.unix=e_,t.months=r_,t.isDate=p,t.locale=It,t.invalid=O,t.duration=ut,t.isMoment=P,t.weekdays=i_,t.parseZone=t_,t.localeData=St,t.isDuration=Gn,t.monthsShort=n_,t.weekdaysMin=o_,t.defineLocale=Aa,t.updateLocale=tw,t.locales=rw,t.weekdaysShort=a_,t.normalizeUnits=Ie,t.relativeTimeRounding=L_,t.relativeTimeThreshold=M_,t.calendarFormat=Zw,t.prototype=T,t.HTML5_FMT={DATETIME_LOCAL:"YYYY-MM-DDTHH:mm",DATETIME_LOCAL_SECONDS:"YYYY-MM-DDTHH:mm:ss",DATETIME_LOCAL_MS:"YYYY-MM-DDTHH:mm:ss.SSS",DATE:"YYYY-MM-DD",TIME:"HH:mm",TIME_SECONDS:"HH:mm:ss",TIME_MS:"HH:mm:ss.SSS",WEEK:"GGGG-[W]WW",MONTH:"YYYY-MM"},t})});var ng=$((ER,rg)=>{function q1(e){return e&&e.__esModule&&e.default?e.default:e}(function(){let t=require;require=Object.assign(r=>{let n=t(r)??{};return q1(n)},t)})();var Ml=Object.defineProperty,j1=Object.getOwnPropertyDescriptor,B1=Object.getOwnPropertyNames,Y1=Object.prototype.hasOwnProperty,V1=(e,t)=>{for(var r in t)Ml(e,r,{get:t[r],enumerable:!0})},z1=(e,t,r,n)=>{if(t&&typeof t=="object"||typeof t=="function")for(let a of B1(t))!Y1.call(e,a)&&a!==r&&Ml(e,a,{get:()=>t[a],enumerable:!(n=j1(t,a))||n.enumerable});return e},U1=e=>z1(Ml({},"__esModule",{value:!0}),e),tg={};V1(tg,{prompt:()=>W1});rg.exports=U1(tg);var la=require("obsidian"),Ll=class extends la.Modal{constructor(t,r){super(t.app),this.resolve=r;let n={app:t.app,cancelButtonStyles:{},cancelButtonText:"Cancel",defaultValue:"",okButtonStyles:{marginRight:"10px",marginTop:"20px"},okButtonText:"OK",placeholder:"",textBoxStyles:{width:"100%"},title:"",valueValidator:()=>null};this.options={...n,...t},this.value=t.defaultValue??""}isOkClicked=!1;options;value;onClose(){this.resolve(this.isOkClicked?this.value:null)}onOpen(){this.titleEl.setText(this.options.title);let t=new la.TextComponent(this.contentEl);t.setValue(this.value),t.setPlaceholder(this.options.placeholder),Object.assign(t.inputEl.style,this.options.textBoxStyles),t.onChange(a=>this.value=a),t.inputEl.addEventListener("keydown",a=>{a.key==="Enter"?this.handleOk(a,t):a.key==="Escape"&&this.close()}),t.inputEl.addEventListener("input",()=>{let a=this.options.valueValidator(t.inputEl.value);t.inputEl.setCustomValidity(a??""),t.inputEl.reportValidity()});let r=new la.ButtonComponent(this.contentEl);r.setButtonText(this.options.okButtonText),r.setCta(),r.onClick(a=>{this.handleOk(a,t)}),Object.assign(r.buttonEl.style,this.options.okButtonStyles);let n=new la.ButtonComponent(this.contentEl);n.setButtonText(this.options.cancelButtonText),n.onClick(this.close.bind(this)),Object.assign(n.buttonEl.style,this.options.cancelButtonStyles)}handleOk(t,r){t.preventDefault(),r.inputEl.checkValidity()&&(this.isOkClicked=!0,this.close())}};async function W1(e){return new Promise(t=>{new Ll(e,t).open()})}});var Og=$((qR,Pg)=>{function iE(e){return e&&e.__esModule&&e.default?e.default:e}(function(){let t=require;require=Object.assign(r=>{let n=t(r)??{};return iE(n)},t)})();var Vl=Object.defineProperty,aE=Object.getOwnPropertyDescriptor,oE=Object.getOwnPropertyNames,sE=Object.prototype.hasOwnProperty,lE=(e,t)=>{for(var r in t)Vl(e,r,{get:t[r],enumerable:!0})},uE=(e,t,r,n)=>{if(t&&typeof t=="object"||typeof t=="function")for(let a of oE(t))!sE.call(e,a)&&a!==r&&Vl(e,a,{get:()=>t[a],enumerable:!(n=aE(t,a))||n.enumerable});return e},cE=e=>uE(Vl({},"__esModule",{value:!0}),e),Cg={};lE(Cg,{PluginSettingsBase:()=>Yl});Pg.exports=cE(Cg);var Yl=class{init(t){if(t!=null){if(typeof t!="object"||Array.isArray(t)){let r=Array.isArray(t)?"Array":typeof t;console.error(`Invalid data type. Expected Object, got: ${r}`);return}this.initFromRecord(t)}}shouldSaveAfterLoad(){return!1}toJSON(){return Object.fromEntries(Object.entries(this))}initFromRecord(t){for(let[r,n]of Object.entries(t))r in this?this[r]=n:console.error(`Unknown property: ${r}`)}}});var Lg=$((VR,Dg)=>{function fE(e){return e&&e.__esModule&&e.default?e.default:e}(function(){let t=require;require=Object.assign(r=>{let n=t(r)??{};return fE(n)},t)})();var Ul=Object.defineProperty,dE=Object.getOwnPropertyDescriptor,hE=Object.getOwnPropertyNames,pE=Object.prototype.hasOwnProperty,mE=(e,t)=>{for(var r in t)Ul(e,r,{get:t[r],enumerable:!0})},gE=(e,t,r,n)=>{if(t&&typeof t=="object"||typeof t=="function")for(let a of hE(t))!pE.call(e,a)&&a!==r&&Ul(e,a,{get:()=>t[a],enumerable:!(n=dE(t,a))||n.enumerable});return e},yE=e=>gE(Ul({},"__esModule",{value:!0}),e),Tg={};mE(Tg,{PluginSettingsTabBase:()=>zl});Dg.exports=yE(Tg);var wE=require("obsidian"),YR=Po(),zl=class extends wE.PluginSettingTab{constructor(t){super(t.app,t),this.plugin=t}}});var Rg=$((zR,Ng)=>{function bE(e){return e&&e.__esModule&&e.default?e.default:e}(function(){let t=require;require=Object.assign(r=>{let n=t(r)??{};return bE(n)},t)})();var Hl=Object.defineProperty,_E=Object.getOwnPropertyDescriptor,kE=Object.getOwnPropertyNames,vE=Object.prototype.hasOwnProperty,xE=(e,t)=>{for(var r in t)Hl(e,r,{get:t[r],enumerable:!0})},SE=(e,t,r,n)=>{if(t&&typeof t=="object"||typeof t=="function")for(let a of kE(t))!vE.call(e,a)&&a!==r&&Hl(e,a,{get:()=>t[a],enumerable:!(n=_E(t,a))||n.enumerable});return e},CE=e=>SE(Hl({},"__esModule",{value:!0}),e),Ig={};xE(Ig,{extend:()=>OE});Ng.exports=CE(Ig);var ga=require("obsidian"),PE=At(),Wl=class{constructor(t){this.valueComponent=t}asExtended(){return(0,PE.assignWithNonEnumerableProperties)({},this.valueComponent,this)}bind(t,r,n){let s={...{componentToPluginSettingsValueConverter:h=>h,pluginSettingsToComponentValueConverter:h=>h,shouldAutoSave:!0},...n},l=t,c=()=>s.pluginSettings??l.settingsCopy,d=h=>{if(!s.valueValidator)return!0;h??=this.valueComponent.getValue();let m=s.valueValidator(h),y=Mg(this.valueComponent);return y&&(y.setCustomValidity(m??""),y.reportValidity()),!m};this.valueComponent.setValue(s.pluginSettingsToComponentValueConverter(c()[r])).onChange(async h=>{if(!d(h))return;let m=c();m[r]=s.componentToPluginSettingsValueConverter(h),s.shouldAutoSave&&await l.saveSettings(m),await s.onChanged?.()}),d();let p=Mg(this.valueComponent);return p&&(p.addEventListener("focus",()=>d()),p.addEventListener("blur",()=>d())),this.asExtended()}};function OE(e){return new Wl(e).asExtended()}function Mg(e){return e instanceof ga.DropdownComponent?e.selectEl:e instanceof ga.SliderComponent?e.sliderEl:e instanceof ga.TextAreaComponent||e instanceof ga.TextComponent?e.inputEl:null}});var Ug=$((GR,zg)=>{function AE(e){return e&&e.__esModule&&e.default?e.default:e}(function(){let t=require;require=Object.assign(r=>{let n=t(r)??{};return AE(n)},t)})();var $l=Object.defineProperty,TE=Object.getOwnPropertyDescriptor,DE=Object.getOwnPropertyNames,LE=Object.prototype.hasOwnProperty,ME=(e,t)=>{for(var r in t)$l(e,r,{get:t[r],enumerable:!0})},IE=(e,t,r,n)=>{if(t&&typeof t=="object"||typeof t=="function")for(let a of DE(t))!LE.call(e,a)&&a!==r&&$l(e,a,{get:()=>t[a],enumerable:!(n=TE(t,a))||n.enumerable});return e},NE=e=>IE($l({},"__esModule",{value:!0}),e),Bg={};ME(Bg,{blobToArrayBuffer:()=>RE,blobToDataUrl:()=>Yg,blobToJpegArrayBuffer:()=>qE,dataUrlToArrayBuffer:()=>Vg,isImageFile:()=>jE});zg.exports=NE(Bg);async function RE(e){return await new Promise(t=>{let r=new FileReader;r.addEventListener("loadend",n),r.readAsArrayBuffer(e);function n(){t(r.result)}})}async function Yg(e){return await new Promise(t=>{let r=new FileReader;r.addEventListener("loadend",n),r.readAsDataURL(e);function n(){t(r.result)}})}async function qE(e,t){let r=await Yg(e);return new Promise(n=>{let a=new Image;a.addEventListener("load",s),a.src=r;function s(){let l=document.createElement("canvas"),c=l.getContext("2d");if(!c)throw new Error("Could not get 2D context.");let d=a.width,p=a.height;l.width=d,l.height=p,c.fillStyle="#fff",c.fillRect(0,0,d,p),c.save(),c.translate(d/2,p/2),c.drawImage(a,0,0,d,p,-d/2,-p/2,d,p),c.restore();let h=l.toDataURL("image/jpeg",t),m=Vg(h);n(m)}})}function Vg(e){let r=e.split(";base64,")[1];if(!r)throw new Error("Invalid data URL");let n=window.atob(r),a=n.length,s=new Uint8Array(a);for(let l=0;l<a;l++)s[l]=n.charCodeAt(l);return s.buffer}function jE(e){return e.type.startsWith("image/")}});var VE={};ti(VE,{default:()=>YE});module.exports=Ua(VE);var Zl=require("electron");function Wa(e,t){let r=Object.keys(t).map(n=>Y_(e,n,t[n]));return r.length===1?r[0]:function(){r.forEach(n=>n())}}function Y_(e,t,r){let n=e[t],a=e.hasOwnProperty(t),s=a?n:function(){return Object.getPrototypeOf(e)[t].apply(this,arguments)},l=r(s);return n&&Object.setPrototypeOf(l,n),Object.setPrototypeOf(c,l),e[t]=c,d;function c(...p){return l===s&&e[t]===c&&d(),l.apply(this,p)}function d(){e[t]===c&&(a?e[t]=s:delete e[t]),l!==s&&(l=s,Object.setPrototypeOf(c,n||Function))}}var Zg=require("obsidian"),Kg=ee(mo(),1),_a=ee(Xe(),1),Xg=ee(Po(),1),ey=ee(Wm(),1),ty=ee(fr(),1),Ur=ee(ct(),1),ry=ee(er(),1);var Br=require("obsidian"),hg=ee(mt(),1),pg=ee(Ol(),1),mg=ee(At(),1),gg=ee(el(),1),mr=ee(Xe(),1),da=ee(ul(),1),yg=ee(Qm(),1),Yr=ee(Cn(),1),wg=ee(Xm(),1),ha=ee(hl(),1),bg=ee(ra(),1),Vr=ee(fr(),1),_g=ee(yl(),1),rt=ee(ct(),1);var ug=require("obsidian"),cg=ee(ct(),1),fg=ee(eo(),1);var Il=ee(eg(),1),ag=ee(At(),1),Nl=ee(Xe(),1),og=ee(ng(),1),yt=ee(ct(),1),Gt=ee(Et(),1),H1=/^\.{3,}$/,G1=/[. ]+$/,Rl=/[\\/:*?"<>|]/,Ln=/\${(.+?)(?::(.+?))?}/g;function ql(e){let t=new Map;try{let r=new Function("exports",e),n={};r(n);for(let[a,s]of Object.entries(n))t.set(a,s);return t}catch(r){throw new Error("Error initializing custom token formatters",{cause:r})}}function $1(e){return(0,Il.default)().format(e)}function ig(e,t,r,n){let a=(0,Nl.getFileOrNull)(e,t);return a?(0,Il.default)(n(a)).format(r):""}function J1(){return jl("0123456789")}function Q1(){return jl("0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ")}function Z1(){return jl("ABCDEFGHIJKLMNOPQRSTUVWXYZ")}function K1(){return crypto.randomUUID()}function X1(e,t,r){let n=(0,Nl.getFileOrNull)(e,t);if(!n)return"";let a=e.metadataCache.getFileCache(n);if(!a?.frontmatter)return"";let s=(0,ag.getNestedPropertyValue)(a.frontmatter,r)??"";return String(s)}var ft=class e{static formatters=new Map;static{this.registerCustomFormatters("")}fileName;filePath;folderName;folderPath;originalCopiedFileExtension;originalCopiedFileName;constructor(t,r){this.filePath=t,this.fileName=(0,yt.basename)(t,(0,yt.extname)(t)),this.folderName=(0,yt.basename)((0,yt.dirname)(t)),this.folderPath=(0,yt.dirname)(t);let n=(0,yt.extname)(r??"");this.originalCopiedFileName=(0,yt.basename)(r??"",n),this.originalCopiedFileExtension=n.slice(1)}static isRegisteredToken(t){return e.formatters.has(t.toLowerCase())}static registerCustomFormatters(t){this.formatters.clear(),this.registerFormatter("date",(n,a,s)=>$1(s)),this.registerFormatter("fileCreationDate",(n,a,s)=>ig(a,n.filePath,s,l=>l.stat.ctime)),this.registerFormatter("fileModificationDate",(n,a,s)=>ig(a,n.filePath,s,l=>l.stat.mtime)),this.registerFormatter("fileName",n=>n.fileName),this.registerFormatter("filePath",n=>n.filePath),this.registerFormatter("folderName",n=>n.folderName),this.registerFormatter("folderPath",n=>n.folderPath),this.registerFormatter("frontmatter",(n,a,s)=>X1(a,n.filePath,s)),this.registerFormatter("originalCopiedFileExtension",n=>n.originalCopiedFileExtension),this.registerFormatter("originalCopiedFileName",n=>n.originalCopiedFileName),this.registerFormatter("prompt",(n,a)=>n.prompt(a)),this.registerFormatter("randomDigit",()=>J1()),this.registerFormatter("randomDigitOrLetter",()=>Q1()),this.registerFormatter("randomLetter",()=>Z1()),this.registerFormatter("uuid",()=>K1());let r=ql(t)??new Map;for(let[n,a]of r.entries())this.registerFormatter(n,a)}static registerFormatter(t,r){this.formatters.set(t.toLowerCase(),r)}async fillTemplate(t,r){return await(0,Gt.replaceAllAsync)(r,Ln,async(n,a,s)=>{let l=e.formatters.get(a.toLowerCase());if(!l)throw new Error(`Invalid token: ${a}`);try{return String(await l(this,t.app,s)??"")}catch(c){throw new Error(`Error formatting token \${${a}}`,{cause:c})}})}async prompt(t){let r=await(0,og.prompt)({app:t,defaultValue:this.originalCopiedFileName,title:"Provide a value for ${prompt} template",valueValidator:n=>ua(n,!1)});if(r===null)throw new Error("Prompt cancelled");return r}};function ua(e,t=!0){if(t){e=sg(e);let r=lg(e);if(r)return`Unknown token: ${r}`}else if(e.match(Ln))return"Tokens are not allowed in file name";return e==="."||e===".."?"":e?Rl.test(e)?`File name "${e}" contains invalid symbols`:H1.test(e)?`File name "${e}" contains more than two dots`:G1.test(e)?`File name "${e}" contains trailing dots or spaces`:"":"File name is empty"}function Mn(e,t=!0){if(t){e=sg(e);let n=lg(e);if(n)return`Unknown token: ${n}`}else if(e.match(Ln))return"Tokens are not allowed in path";if(e=(0,Gt.trimStart)(e,"/"),e=(0,Gt.trimEnd)(e,"/"),e==="")return"";let r=e.split("/");for(let n of r){let a=ua(n);if(a)return a}return""}function jl(e){return e[Math.floor(Math.random()*e.length)]??""}function sg(e){return(0,Gt.replaceAll)(e,Ln,(t,r)=>`\${${r}}`)}function lg(e){let t=e.matchAll(Ln);for(let r of t){let n=r[1]??"";if(!ft.isRegisteredToken(n))return n}return null}async function ca(e,t,r){return await eE(e,new ft(t,r))}async function fa(e,t){return await dg(e,e.settingsCopy.pastedFileName,t)}function Bl(e,t){if(e.settingsCopy.whitespaceReplacement){t=t.replace(/\s/g,e.settingsCopy.whitespaceReplacement);let r=(0,fg.escapeRegExp)(e.settingsCopy.whitespaceReplacement);t=t.replace(new RegExp(`${r}{2,}`,"g"),e.settingsCopy.whitespaceReplacement)}return t}async function eE(e,t){return await dg(e,e.settingsCopy.attachmentFolderPath,t)}async function dg(e,t,r){let n=await r.fillTemplate(e,t),a=Mn(n,!1);if(a)throw new Error(`Resolved path ${n} is invalid: ${a}`);return e.settingsCopy.toLowerCase&&(n=n.toLowerCase()),n.endsWith("/")||(n=n+"/"),n=Bl(e,n),(n.startsWith("./")||n.startsWith("../"))&&(n=(0,cg.join)(r.folderPath,n)),n=(0,ug.normalizePath)(n),n}var LR=globalThis.process??{browser:!0,cwd:()=>"/",env:{},platform:"android"},tE=[".avif",".bmp",".gif",".jpeg",".jpg",".png",".svg",".webp"];async function kg(e,t,r,n){let a=e.app;r??=t.path,n??=()=>!0;let s=new Br.Notice(`Collecting attachments for ${t.path}`),l=new Map,c=(0,mr.isCanvasFile)(a,t);await(0,gg.applyFileChanges)(a,t,async()=>{let d=await(0,Yr.getCacheSafe)(a,t);if(!d)return[];let p=c?await rE(a,t):(0,Yr.getAllLinks)(d),h=[];for(let m of p){let y=await nE(e,m,t.path,r);if(!y||!n(y.oldAttachmentPath))continue;if((await(0,Yr.getBacklinksForFileSafe)(a,y.oldAttachmentPath)).count()>1?y.newAttachmentPath=await(0,Vr.copySafe)(a,y.oldAttachmentPath,y.newAttachmentPath):(y.newAttachmentPath=await(0,Vr.renameSafe)(a,y.oldAttachmentPath,y.newAttachmentPath),await(0,_g.deleteEmptyFolderHierarchy)(a,(0,rt.dirname)(y.oldAttachmentPath))),l.set(y.oldAttachmentPath,y.newAttachmentPath),!c){let v=(0,da.updateLink)({app:a,link:m,newSourcePathOrFile:t,newTargetPathOrFile:y.newAttachmentPath,oldTargetPathOrFile:y.oldAttachmentPath});h.push((0,bg.referenceToFileChange)(m,v))}}return h}),c&&await(0,Vr.process)(a,t,d=>{let p=JSON.parse(d);for(let h of p.nodes){if(h.type!=="file")continue;let m=l.get(h.file);m&&(h.file=m)}return(0,mg.toJson)(p)}),s.hide()}function vg(e,t){let r=e.app.workspace.getActiveFile();return(0,mr.isNote)(e.app,r)?(t||(0,ha.addToQueue)(e.app,()=>pa(e,r?.parent??(0,hg.throwExpression)(new Error("Parent folder not found")))),!0):!1}function xg(e,t){let r=e.app.workspace.getActiveFile();return!r||!(0,mr.isNote)(e.app,r)?!1:(t||(0,ha.addToQueue)(e.app,()=>kg(e,r)),!0)}function Sg(e){(0,ha.addToQueue)(e.app,()=>pa(e,e.app.vault.getRoot()))}async function pa(e,t){if(!await(0,wg.confirm)({app:e.app,message:createFragment(n=>{n.appendText("Do you want to collect attachments for all notes in folder: "),(0,pg.appendCodeBlock)(n,t.path),n.appendText(" and all its subfolders?"),n.createEl("br"),n.appendText("This operation cannot be undone.")}),title:createFragment(n=>{(0,Br.setIcon)(n.createSpan(),"lucide-alert-triangle"),n.appendText(" Collect attachments in folder")})}))return;e.consoleDebug(`Collect attachments in folder: ${t.path}`);let r=[];Br.Vault.recurseChildren(t,n=>{(0,mr.isNote)(e.app,n)&&r.push(n)}),r.sort((n,a)=>n.path.localeCompare(a.path)),await(0,yg.loop)({abortSignal:e.abortSignal,buildNoticeMessage:(n,a)=>`Collecting attachments ${a} - ${n.path}`,items:r,processItem:async n=>{await kg(e,n)},shouldContinueOnError:!0})}async function rE(e,t){return(await e.vault.readJson(t.path)).nodes.filter(a=>a.type==="file").map(a=>a.file).map(a=>({link:a,original:a,position:{end:{col:0,line:0,loc:0,offset:0},start:{col:0,line:0,loc:0,offset:0}}}))}async function nE(e,t,r,n){let a=e.app,s=(0,da.extractLinkFile)(a,t,n);if(!s||(0,mr.isNote)(e.app,s))return null;let l=s.path,c=s.name,d=(0,rt.basename)(n,(0,rt.extname)(n)),p=(0,rt.basename)(r,(0,rt.extname)(r)),h;e.settingsCopy.renameOnlyImages&&!tE.includes("."+s.extension.toLowerCase())?h=c:e.settingsCopy.renameCollectedFiles?h=(0,rt.makeFileName)(await fa(e,new ft(r,s.name)),s.extension):e.settingsCopy.autoRenameFiles?h=c.replaceAll(d,p):h=c;let m=await ca(e,r,h),y=(0,rt.join)(m,h);return l===y?null:{newAttachmentPath:y,oldAttachmentPath:l}}var Eg=ee(At(),1),Ag=ee(Og(),1);var ma=class extends Ag.PluginSettingsBase{attachmentFolderPath="./assets/${filename}";autoRenameFiles=!1;autoRenameFolder=!0;convertImagesOnDragAndDrop=!1;convertImagesToJpeg=!1;deleteOrphanAttachments=!1;duplicateNameSeparator=" ";jpegQuality=.8;keepEmptyAttachmentFolders=!1;pastedFileName="file-${date:YYYYMMDDHHmmssSSS}";renameAttachmentsOnDragAndDrop=!1;renameCollectedFiles=!1;renameOnlyImages=!0;renamePastedFilesWithKnownNames=!1;toLowerCase=!1;whitespaceReplacement="";get customTokensStr(){return this.#e}set customTokensStr(t){this.#e=t,ft.registerCustomFormatters(this.#e)}#e="";#t=!1;constructor(t){super(),this.init(t)}shouldSaveAfterLoad(){return this.#t}toJSON(){return{...super.toJSON(),customTokensStr:this.customTokensStr}}initFromRecord(t){let r=t,n=r.dateTimeFormat??"YYYYMMDDHHmmssSSS";r.attachmentFolderPath=Fg(r.attachmentFolderPath??"",n),r.pastedFileName=Fg(r.pastedFileName??r.pastedImageFileName??"file-${date}",n),r.replaceWhitespace!==void 0&&(r.whitespaceReplacement=r.replaceWhitespace?"-":""),this.#t=(0,Eg.deleteProperties)(r,["dateTimeFormat","pastedImageFileName","replaceWhitespace"]),super.initFromRecord(r)}};function Fg(e,t){return e.replaceAll("${date}",`\${date:${t}}`)}var we=require("obsidian"),nt=ee(Ol(),1),jg=ee(Lg(),1),Ce=ee(Rg(),1);var UR=globalThis.process??{browser:!0,cwd:()=>"/",env:{},platform:"android"},Gl="\u2423",ya=class extends jg.PluginSettingsTabBase{display(){this.containerEl.empty(),new we.Setting(this.containerEl).setName("Location for New Attachments").setDesc(createFragment(t=>{t.appendText("Start with "),(0,nt.appendCodeBlock)(t,"."),t.appendText(" to use relative path."),t.createEl("br"),t.appendText("See available "),t.createEl("a",{href:"https://github.com/RainCat1998/obsidian-custom-attachment-location?tab=readme-ov-file#tokens",text:"tokens"}),t.appendChild(createEl("br")),t.appendText("Dot-folders like "),(0,nt.appendCodeBlock)(t,".attachments"),t.appendText(" are not recommended, because Obsidian doesn't track them. You might need to use "),t.createEl("a",{href:"https://github.com/polyipseity/obsidian-show-hidden-files/",text:"Show Hidden Files"}),t.appendText(" Plugin to manage them.")})).addText(t=>(0,Ce.extend)(t).bind(this.plugin,"attachmentFolderPath",{componentToPluginSettingsValueConverter(r){return(0,we.normalizePath)(r)},pluginSettingsToComponentValueConverter(r){return r},valueValidator(r){return Mn(r)}}).setPlaceholder("./assets/${filename}")),new we.Setting(this.containerEl).setName("Pasted File Name").setDesc(createFragment(t=>{t.appendText("See available "),t.createEl("a",{href:"https://github.com/RainCat1998/obsidian-custom-attachment-location?tab=readme-ov-file#tokens",text:"tokens"})})).addText(t=>(0,Ce.extend)(t).bind(this.plugin,"pastedFileName",{valueValidator(r){return Mn(r)}}).setPlaceholder("file-${date:YYYYMMDDHHmmssSSS}")),new we.Setting(this.containerEl).setName("Automatically rename attachment folder").setDesc(createFragment(t=>{t.appendText("When renaming md files, automatically rename attachment folder if folder name contains "),(0,nt.appendCodeBlock)(t,"${filename}"),t.appendText(".")})).addToggle(t=>(0,Ce.extend)(t).bind(this.plugin,"autoRenameFolder")),new we.Setting(this.containerEl).setName("Automatically rename attachment files").setDesc(createFragment(t=>{t.appendText("When renaming md files, automatically rename attachment files if file name contains "),(0,nt.appendCodeBlock)(t,"${filename}"),t.appendText(".")})).addToggle(t=>(0,Ce.extend)(t).bind(this.plugin,"autoRenameFiles")),new we.Setting(this.containerEl).setName("Replace whitespaces").setDesc(createFragment(t=>{t.appendText("Automatically replace whitespace in attachment folder and file name with the specified character."),t.appendChild(createEl("br")),t.appendText("Leave blank to disable replacement.")})).addText(t=>(0,Ce.extend)(t).bind(this.plugin,"whitespaceReplacement",{valueValidator(r){return r===""?null:r.length>1?"Whitespace replacement must be a single character or blank.":Rl.exec(r)?"Whitespace replacement must not contain invalid filename path characters.":null}}).setPlaceholder("-")),new we.Setting(this.containerEl).setName("All lowercase names").setDesc("Automatically set all characters in folder name and pasted image name to be lowercase.").addToggle(t=>(0,Ce.extend)(t).bind(this.plugin,"toLowerCase")),new we.Setting(this.containerEl).setName("Convert pasted images to JPEG").setDesc("Paste images from clipboard converting them to JPEG.").addToggle(t=>(0,Ce.extend)(t).bind(this.plugin,"convertImagesToJpeg")),new we.Setting(this.containerEl).setName("JPEG Quality").setDesc("The smaller the quality, the greater the compression ratio.").addDropdown(t=>{t.addOptions(EE()),(0,Ce.extend)(t).bind(this.plugin,"jpegQuality",{componentToPluginSettingsValueConverter:r=>Number(r),pluginSettingsToComponentValueConverter:r=>r.toString()})}),new we.Setting(this.containerEl).setName("Convert images on drag&drop").setDesc(createFragment(t=>{t.appendText("If enabled and "),(0,nt.appendCodeBlock)(t,"Convert pasted images to JPEG"),t.appendText(" setting is enabled, images drag&dropped into the editor will be converted to JPEG.")})).addToggle(t=>(0,Ce.extend)(t).bind(this.plugin,"convertImagesOnDragAndDrop")),new we.Setting(this.containerEl).setName("Rename only images").setDesc(createFragment(t=>{t.appendText("If enabled, only image files will be renamed."),t.appendChild(createEl("br")),t.appendText("If disabled, all attachment files will be renamed.")})).addToggle(t=>(0,Ce.extend)(t).bind(this.plugin,"renameOnlyImages")),new we.Setting(this.containerEl).setName("Rename pasted files with known names").setDesc(createFragment(t=>{t.appendText("If enabled, pasted copied files with known names will be renamed."),t.appendChild(createEl("br")),t.appendText("If disabled, only clipboard image objects (e.g., screenshots) will be renamed.")})).addToggle(t=>(0,Ce.extend)(t).bind(this.plugin,"renamePastedFilesWithKnownNames")),new we.Setting(this.containerEl).setName("Rename attachments on drag&drop").setDesc(createFragment(t=>{t.appendText("If enabled, attachments dragged and dropped into the editor will be renamed according to the "),(0,nt.appendCodeBlock)(t,"Pasted File Name"),t.appendText(" setting.")})).addToggle(t=>(0,Ce.extend)(t).bind(this.plugin,"renameAttachmentsOnDragAndDrop")),new we.Setting(this.containerEl).setName("Rename attachments on collecting").setDesc(createFragment(t=>{t.appendText("If enabled, attachments processed via "),(0,nt.appendCodeBlock)(t,"Collect attachments"),t.appendText(" commands will be renamed according to the "),(0,nt.appendCodeBlock)(t,"Pasted File Name"),t.appendText(" setting.")})).addToggle(t=>(0,Ce.extend)(t).bind(this.plugin,"renameCollectedFiles")),new we.Setting(this.containerEl).setName("Duplicate name separator").setDesc(createFragment(t=>{t.appendText("When you are pasting/dragging a file with the same name as an existing file, this separator will be added to the file name."),t.appendChild(createEl("br")),t.appendText("E.g., when you are dragging file "),(0,nt.appendCodeBlock)(t,"existingFile.pdf"),t.appendText(", it will be renamed to "),(0,nt.appendCodeBlock)(t,"existingFile 1.pdf"),t.appendText(", "),(0,nt.appendCodeBlock)(t,"existingFile 2.pdf"),t.appendText(", etc, getting the first name available.")})).addText(t=>{(0,Ce.extend)(t).bind(this.plugin,"duplicateNameSeparator",{componentToPluginSettingsValueConverter:r=>r.replaceAll(Gl," "),pluginSettingsToComponentValueConverter:qg,valueValidator(r){return ua(`filename${r}1`,!1)}}).setPlaceholder(Gl),t.inputEl.addEventListener("input",()=>{t.inputEl.value=qg(t.inputEl.value)})}),new we.Setting(this.containerEl).setName("Keep empty attachment folders").setDesc("If enabled, empty attachment folders will be preserved, useful for source control purposes.").addToggle(t=>(0,Ce.extend)(t).bind(this.plugin,"keepEmptyAttachmentFolders")),new we.Setting(this.containerEl).setName("Delete orphan attachments").setDesc("If enabled, when the note is deleted, its orphan attachments are deleted as well.").addToggle(t=>(0,Ce.extend)(t).bind(this.plugin,"deleteOrphanAttachments")),new we.Setting(this.containerEl).setName("Custom tokens").setDesc(createFragment(t=>{t.appendText("Custom tokens to be used in the attachment folder path and pasted file name."),t.appendChild(createEl("br")),t.appendText("See "),t.createEl("a",{href:"https://github.com/RainCat1998/obsidian-custom-attachment-location?tab=readme-ov-file#custom-tokens",text:"documentation"}),t.appendText(" for more information.")})).addTextArea(t=>(0,Ce.extend)(t).bind(this.plugin,"customTokensStr",{valueValidator:FE}))}};function FE(e){return ql(e)===null?"Invalid custom tokens code":null}function EE(){let e={};for(let t=1;t<=10;t++){let r=(t/10).toFixed(1);e[r]=r}return e}function qg(e){return e.replaceAll(" ",Gl)}var Wg=require("electron"),Hg=require("obsidian"),Gg=ee(kn(),1),$t=ee(Ug(),1),$g=ee(Xe(),1),zr=ee(ct(),1);var wa=class{constructor(t,r,n){this.event=t;this.eventType=r;this.plugin=n}async handle(){let t=this.event;if(t.handled)return;let r=this.plugin.app.dragManager.draggable,n=this.getTargetType();if(n==="Unsupported"||this.shouldInsertRawLink())return;this.plugin.consoleDebug(`Handle ${n} ${this.eventType}`);let a=this.plugin.app.workspace.getActiveFile();if(!a||!(0,$g.isNote)(this.plugin.app,a))return;let s=this.getDataTransfer();if(!s||!this.event.target)return;this.event.preventDefault(),this.event.stopImmediatePropagation();let l=Array.from(s.items).map(d=>{let p=d.type;if(d.kind==="file"){let h=d.getAsFile();if(!h)throw new Error("Could not get file from item");return{file:h,type:p}}else{if(d.kind==="string")return{textPromise:new Promise(m=>{d.getAsString(y=>{m(y)})}),type:p};throw new Error(`Unsupported item kind ${d.kind}`)}}),c=new DataTransfer;for(let d of l)if(d.textPromise)c.items.add(await d.textPromise,d.type);else if(d.file){let p=(0,zr.extname)(d.file.name).slice(1),h=(0,zr.basename)(d.file.name,"."+p),m;this.shouldConvertImages()&&(0,$t.isImageFile)(d.file)?(m=await(0,$t.blobToJpegArrayBuffer)(d.file,this.plugin.settingsCopy.jpegQuality),p="jpg"):m=await(0,$t.blobToArrayBuffer)(d.file);let y=this.shouldRenameAttachments(d.file),w=y?await fa(this.plugin,new ft(a.path,d.file.name)):h;w=Bl(this.plugin,w);let v={type:d.type.replace("image/","image-override/")};y||(v.lastModified=d.file.lastModified);let x=new File([new Blob([m])],(0,zr.makeFileName)(w,p),v);y||Object.defineProperty(x,"path",{value:Qg(d.file)}),c.items.add(x)}t=this.cloneWithNewDataTransfer(c),t.handled=!0,this.plugin.app.dragManager.draggable=r,this.event.target.dispatchEvent(t)}shouldInsertRawLink(){return!1}getTargetType(){if(!(this.event.target instanceof HTMLElement)||this.plugin.app.workspace.activeEditor?.metadataEditor?.contentEl.contains(this.event.target))return"Unsupported";if(this.plugin.app.workspace.activeEditor?.editor?.containerEl.contains(this.event.target))return"Note";if(this.event.target.closest(".canvas-wrapper"))return this.event.target.isContentEditable?"Unsupported":"Canvas";let t=this.plugin.app.workspace.getActiveFileView();return this.event.target.matches("body")&&t?.getViewType()==="canvas"&&t.containerEl.closest(".mod-active")?"Canvas":"Unsupported"}},Jl=class extends wa{constructor(t,r){super(t,"Drop",r)}cloneWithNewDataTransfer(t){return new DragEvent("drop",{bubbles:this.event.bubbles,cancelable:this.event.cancelable,clientX:this.event.clientX,clientY:this.event.clientY,composed:this.event.composed,dataTransfer:t})}getDataTransfer(){return this.event.dataTransfer}shouldConvertImages(){return this.plugin.settingsCopy.convertImagesToJpeg&&this.plugin.settingsCopy.convertImagesOnDragAndDrop}shouldInsertRawLink(){return Hg.Platform.isMacOS?this.event.altKey:this.event.ctrlKey}shouldRenameAttachments(t){return this.plugin.settingsCopy.renameOnlyImages&&!(0,$t.isImageFile)(t)?!1:this.plugin.settingsCopy.renameAttachmentsOnDragAndDrop}},Ql=class extends wa{constructor(t,r){super(t,"Paste",r)}cloneWithNewDataTransfer(t){return new ClipboardEvent("paste",{bubbles:this.event.bubbles,cancelable:this.event.cancelable,clipboardData:t,composed:this.event.composed})}getDataTransfer(){return this.event.clipboardData}shouldConvertImages(){return this.plugin.settingsCopy.convertImagesToJpeg}shouldRenameAttachments(t){return this.plugin.settingsCopy.renameOnlyImages&&!(0,$t.isImageFile)(t)?!1:Qg(t)===""||this.plugin.settingsCopy.renamePastedFilesWithKnownNames}};function Jg(e){let t=(0,Gg.convertAsyncToSync)(async n=>BE(e,n));r(window),e.app.workspace.on("window-open",(n,a)=>{r(a)});function r(n){e.registerDomEvent(n.document,"paste",t,{capture:!0}),e.registerDomEvent(n.document,"drop",t,{capture:!0})}}function Qg(e){return e.path||(Wg.webUtils?.getPathForFile(e)??"")}async function BE(e,t){await(t.constructor.name==="ClipboardEvent"?new Ql(t,e):new Jl(t,e)).handle()}var ba=class extends Xg.PluginBase{createPluginSettings(t){return new ma(t)}createPluginSettingsTab(){return new ya(this)}onLayoutReady(){this.register(Wa(this.app.vault,{getAvailablePath:()=>this.getAvailablePath.bind(this),getAvailablePathForAttachments:()=>{let t={isExtended:!0};return Object.assign(this.getAvailablePathForAttachments.bind(this),t)}})),Zl.webUtils&&this.register(Wa(Zl.webUtils,{getPathForFile:t=>r=>this.getPathForFile(r,t)}))}onloadComplete(){(0,ey.registerRenameDeleteHandlers)(this,()=>({shouldDeleteEmptyFolders:!this.settings.keepEmptyAttachmentFolders,shouldHandleDeletions:this.settings.deleteOrphanAttachments,shouldHandleRenames:!0,shouldRenameAttachmentFiles:this.settings.autoRenameFiles,shouldRenameAttachmentFolder:this.settings.autoRenameFolder,shouldUpdateFilenameAliases:!0})),Jg(this),this.addCommand({checkCallback:t=>xg(this,t),id:"collect-attachments-current-note",name:"Collect attachments in current note"}),this.addCommand({checkCallback:t=>vg(this,t),id:"collect-attachments-current-folder",name:"Collect attachments in current folder"}),this.addCommand({callback:()=>{Sg(this)},id:"collect-attachments-entire-vault",name:"Collect attachments in entire vault"}),this.registerEvent(this.app.workspace.on("file-menu",this.handleFileMenu.bind(this)))}getAvailablePath(t,r){let n=0;for(;;){let a=(0,Ur.makeFileName)(n==0?t:`${t}${this.settings.duplicateNameSeparator}${n.toString()}`,r);if(!(0,_a.getAbstractFileOrNull)(this.app,a,!0))return a;n++}}async getAvailablePathForAttachments(t,r,n,a){let s;if(!n||!(0,_a.isNote)(this.app,n))s=await(0,Kg.getAvailablePathForAttachments)(this.app,t,r,n,!0);else{let l=await ca(this,n.path,(0,Ur.makeFileName)(t,r));s=this.app.vault.getAvailablePath((0,Ur.join)(l,t),r)}if(!a){let l=(0,ry.parentFolderPath)(s);await this.app.vault.exists(l)||(await(0,ty.createFolderSafe)(this.app,l),this.settings.keepEmptyAttachmentFolders&&await this.app.vault.create((0,Ur.join)(l,".gitkeep"),""))}return s}getPathForFile(t,r){return t.path||r(t)}handleFileMenu(t,r){r instanceof Zg.TFolder&&t.addItem(n=>{n.setTitle("Collect attachments in folder").setIcon("download").onClick(()=>pa(this,r))})}};var YE=ba;
/*! Bundled license information:

moment/moment.js:
  (*! moment.js *)
  (*! version : 2.30.1 *)
  (*! authors : Tim Wood, Iskren Chernev, Moment.js contributors *)
  (*! license : MIT *)
  (*! momentjs.com *)
*/

/* nosourcemap */