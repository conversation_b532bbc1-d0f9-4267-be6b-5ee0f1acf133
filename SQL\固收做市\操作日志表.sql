
CREATE TABLE `ptms`.`operation_log` (
  `EID` CHAR(32) NOT NULL COMMENT '主键UUID',
  `EITIME` DATETIME NOT NULL COMMENT '创建时间',
  `EUTIME` DATETIME NOT NULL COMMENT '更新时间',
  `del` TINYINT(1) NOT NULL DEFAULT 0 COMMENT '逻辑删除标志 (0:未删除, 1:已删除)',
  `biz_module1` VARCHAR(100) NULL COMMENT '一级业务模块',
  `biz_module2` VARCHAR(100) NULL COMMENT '二级业务模块',
  `biz_module3` VARCHAR(100) NULL COMMENT '三级业务模块',
  `biz_id` VARCHAR(100) NULL COMMENT '业务ID',
  `action` VARCHAR(100) NULL COMMENT '操作动作',
  `op_desc` VARCHAR(500) NULL COMMENT '操作描述 (原desc)',
  `op_id` VARCHAR(50) NULL COMMENT '操作人ID',
  `op_name` VARCHAR(100) NULL COMMENT '操作人姓名',
  `op_time` DATETIME NULL COMMENT '操作时间',
  PRIMARY KEY (`EID`),
  INDEX `idx_biz_id` (`biz_id`),
  INDEX `idx_op_time` (`op_time` DESC)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='操作日志主表';

CREATE TABLE `ptms`.`operation_log_change` (
  `EID` CHAR(32) NOT NULL COMMENT '主键UUID',
  `EITIME` DATETIME NOT NULL COMMENT '创建时间',
  `EUTIME` DATETIME NOT NULL COMMENT '更新时间',
  `del` TINYINT(1) NOT NULL DEFAULT 0 COMMENT '逻辑删除标志 (0:未删除, 1:已删除)',
  `log_eid` CHAR(32) NOT NULL COMMENT '关联的操作日志EID',
  `field_name` VARCHAR(100) NULL COMMENT '字段名',
  `old_value` TEXT NULL COMMENT '旧值',
  `new_value` TEXT NULL COMMENT '新值',
  `remark` VARCHAR(500) NULL COMMENT '备注',
  PRIMARY KEY (`EID`),
  INDEX `idx_log_eid` (`log_eid`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='操作日志变更明细表';