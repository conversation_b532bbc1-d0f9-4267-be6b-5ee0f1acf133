/*
 * Copyright (C) 1999-2001 Free Software Foundation, Inc.
 * This file is part of the GNU LIBICONV Library.
 *
 * The GNU LIBICONV Library is free software; you can redistribute it
 * and/or modify it under the terms of the GNU Library General Public
 * License as published by the Free Software Foundation; either version 2
 * of the License, or (at your option) any later version.
 *
 * The GNU LIBICONV Library is distributed in the hope that it will be
 * useful, but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
 * Library General Public License for more details.
 *
 * You should have received a copy of the GNU Library General Public
 * License along with the GNU LIBICONV Library; see the file COPYING.LIB.
 * If not, write to the Free Software Foundation, Inc., 51 Franklin Street,
 * Fifth Floor, Boston, MA 02110-1301, USA.
 */

/*
 * GB/T 12345.1990-0 extensions
 */

static const unsigned short gb12345ext_2uni_page21[12] = {
  /* 0x21 */
  0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd,
  0xfffd, 0xfffd, 0xfffd, 0x2225,
};
static const unsigned short gb12345ext_2uni_page26[85] = {
  /* 0x26 */
  0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd,
  0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd,
  0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd,
  0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd,
  0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd,
  0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd,
  0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd,
  0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfe35,
  0xfe36, 0xfe39, 0xfe3a, 0xfe3f, 0xfe40, 0xfe3d, 0xfe3e, 0xfe41,
  0xfe42, 0xfe43, 0xfe44, 0xfffd, 0xfffd, 0xfe3b, 0xfe3c, 0xfe37,
  0xfe38, 0xfe31, 0xfffd, 0xfe33, 0xfe34,
};
static const unsigned short gb12345ext_2uni_page28[32] = {
  /* 0x28 */
  0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd,
  0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd,
  0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd,
  0xfffd, 0xfffd, 0x0251, 0x1e3f, 0x0144, 0x0148, 0x01f9, 0x0261,
};
static const unsigned short gb12345ext_2uni_page30[6871] = {
  /* 0x30 */
  0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0x769a,
  0xfffd, 0x85f9, 0xfffd, 0xfffd, 0x7919, 0x611b, 0xfffd, 0xfffd,
  0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd,
  0x9aaf, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0x8956,
  0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd,
  0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd,
  0xfffd, 0xfffd, 0x58e9, 0xfffd, 0x7f77, 0xfffd, 0xfffd, 0xfffd,
  0xfffd, 0x64fa, 0xfffd, 0x6557, 0xfffd, 0xfffd, 0xfffd, 0xfffd,
  0xfffd, 0xfffd, 0xfffd, 0x9812, 0xfffd, 0xfffd, 0xfffd, 0xfffd,
  0xfffd, 0xfffd, 0xfffd, 0x8fa6, 0x7d46, 0xfffd, 0x5e6b, 0xfffd,
  0xfffd, 0xfffd, 0x7d81, 0xfffd, 0xfffd, 0xfffd, 0x938a, 0xfffd,
  0x8b17, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd,
  /* 0x31 */
  0xfffd, 0xfffd, 0xfffd, 0xfffd, 0x98fd, 0x5bf6, 0xfffd, 0x5831,
  0xfffd, 0xfffd, 0x9b91, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd,
  0xfffd, 0x8f29, 0xfffd, 0x8c9d, 0x92c7, 0xfffd, 0x72fd, 0x5099,
  0x618a, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd,
  0x7db3, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd,
  0xfffd, 0x7b46, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0x7562, 0x6583,
  0xfffd, 0x5e63, 0xfffd, 0xfffd, 0x9589, 0xfffd, 0xfffd, 0xfffd,
  0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0x908a, 0x7de8,
  0x8cb6, 0xfffd, 0xfffd, 0x8b8a, 0xfffd, 0xfffd, 0x8faf, 0x8fae,
  0xfffd, 0x6a19, 0xfffd, 0xfffd, 0xfffd, 0x9c49, 0xfffd, 0xfffd,
  0x765f, 0xfffd, 0xfffd, 0x7015, 0x6ff1, 0x8cd3, 0x64ef, 0xfffd,
  0xfffd, 0xfffd, 0xfffd, 0xfffd, 0x9905, 0xfffd,
  /* 0x32 */
  0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0x64a5, 0x9262, 0xfffd,
  0xfffd, 0xfffd, 0xfffd, 0x9251, 0xfffd, 0xfffd, 0xfffd, 0xfffd,
  0xfffd, 0xfffd, 0xfffd, 0xfffd, 0x99c1, 0xfffd, 0xfffd, 0xfffd,
  0x88dc, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd,
  0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0x8ca1, 0xfffd, 0xfffd,
  0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0x53c3, 0x8836, 0x6b98,
  0x615a, 0x6158, 0x71e6, 0x84bc, 0x8259, 0x5009, 0x6ec4, 0xfffd,
  0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0x53a0, 0xfffd, 0x5074,
  0xfffd, 0x6e2c, 0x5c64, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd,
  0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0x8a6b, 0xfffd,
  0xfffd, 0xfffd, 0x6519, 0x647b, 0x87ec, 0x995e, 0x8b92, 0x7e8f,
  0x93df, 0x7523, 0x95e1, 0x986b, 0xfffd, 0xfffd,
  /* 0x33 */
  0x5834, 0x5617, 0xfffd, 0x9577, 0x511f, 0x8178, 0x5ee0, 0xfffd,
  0x66a2, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0x9214, 0xfffd, 0xfffd,
  0xfffd, 0xfffd, 0xfffd, 0xfffd, 0x8eca, 0xfffd, 0xfffd, 0xfffd,
  0x5fb9, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0x5875, 0xfffd, 0xfffd,
  0xfffd, 0x9673, 0xfffd, 0x896f, 0xfffd, 0x7a31, 0xfffd, 0xfffd,
  0xfffd, 0xfffd, 0xfffd, 0xfffd, 0x61f2, 0xfffd, 0x8aa0, 0xfffd,
  0xfffd, 0x9a01, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd,
  0x9072, 0xfffd, 0x99b3, 0xfffd, 0x9f52, 0xfffd, 0xfffd, 0xfffd,
  0xfffd, 0xfffd, 0x71be, 0xfffd, 0x885d, 0x87f2, 0xfffd, 0x5bf5,
  0xfffd, 0xfffd, 0x7587, 0x8e8a, 0xfffd, 0xfffd, 0x7c4c, 0xfffd,
  0x7da2, 0xfffd, 0x919c, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd,
  0xfffd, 0x92e4, 0x96db, 0xfffd, 0xfffd, 0xfffd,
  /* 0x34 */
  0x790e, 0x5132, 0xfffd, 0xfffd, 0x89f8, 0x8655, 0xfffd, 0xfffd,
  0xfffd, 0xfffd, 0x50b3, 0xfffd, 0xfffd, 0xfffd, 0x7621, 0xfffd,
  0xfffd, 0xfffd, 0x95d6, 0x5275, 0xfffd, 0xfffd, 0xfffd, 0x9318,
  0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0x7d14, 0xfffd,
  0xfffd, 0x7dbd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0x8fad, 0xfffd,
  0xfffd, 0x8a5e, 0xfffd, 0xfffd, 0x8cdc, 0xfffd, 0x8070, 0xfffd,
  0xfffd, 0xfffd, 0x5f9e, 0x53e2, 0xfffd, 0xfffd, 0xfffd, 0xfffd,
  0xfffd, 0x8ea5, 0xfffd, 0x7ac4, 0xfffd, 0xfffd, 0xfffd, 0xfffd,
  0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd,
  0xfffd, 0xfffd, 0xfffd, 0xfffd, 0x932f, 0xfffd, 0x9054, 0xfffd,
  0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0x5e36,
  0xfffd, 0xfffd, 0x8cb8, 0xfffd, 0xfffd, 0xfffd,
  /* 0x35 */
  0xfffd, 0xfffd, 0x64d4, 0xfffd, 0x55ae, 0x9132, 0x64a3, 0x81bd,
  0xfffd, 0xfffd, 0xfffd, 0x619a, 0xfffd, 0x8a95, 0x5f48, 0xfffd,
  0x7576, 0x64cb, 0x9ee8, 0x8569, 0x6a94, 0xfffd, 0x6417, 0xfffd,
  0xfffd, 0x5cf6, 0x79b1, 0x5c0e, 0xfffd, 0xfffd, 0xfffd, 0xfffd,
  0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0x71c8, 0xfffd, 0xfffd,
  0xfffd, 0xfffd, 0x9127, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0x6575,
  0xfffd, 0xfffd, 0x6ecc, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd,
  0xfffd, 0xfffd, 0xfffd, 0xfffd, 0x905e, 0x7de0, 0x985b, 0xfffd,
  0xfffd, 0xfffd, 0x9ede, 0xfffd, 0xfffd, 0x588a, 0x96fb, 0xfffd,
  0xfffd, 0xfffd, 0xfffd, 0xfffd, 0x6fb1, 0xfffd, 0xfffd, 0xfffd,
  0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0x91e3, 0x8abf, 0xfffd,
  0xfffd, 0xfffd, 0xfffd, 0xfffd, 0x8adc, 0xfffd,
  /* 0x36 */
  0xfffd, 0xfffd, 0xfffd, 0x91d8, 0x9802, 0xfffd, 0x9320, 0xfffd,
  0x8a02, 0xfffd, 0x6771, 0xfffd, 0xfffd, 0xfffd, 0x52d5, 0x68df,
  0xfffd, 0xfffd, 0x51cd, 0xfffd, 0xfffd, 0xfffd, 0x9b25, 0xfffd,
  0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0x72a2, 0x7368,
  0x8b80, 0xfffd, 0xfffd, 0x8ced, 0xfffd, 0x934d, 0xfffd, 0xfffd,
  0xfffd, 0xfffd, 0xfffd, 0xfffd, 0x935b, 0xfffd, 0x65b7, 0x7dde,
  0xfffd, 0xfffd, 0x968a, 0x5c0d, 0xfffd, 0x5678, 0xfffd, 0xfffd,
  0x9813, 0xfffd, 0x920d, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd,
  0x596a, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd,
  0x58ae, 0xfffd, 0xfffd, 0x9d5d, 0xfffd, 0x984d, 0x8a1b, 0xfffd,
  0x60e1, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0x9913, 0xfffd, 0xfffd,
  0x5152, 0xfffd, 0x723e, 0x990c, 0xfffd, 0xfffd,
  /* 0x37 */
  0x8cb3, 0x767c, 0x7f70, 0xfffd, 0xfffd, 0xfffd, 0x95a5, 0xfffd,
  0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0x792c, 0x91e9,
  0xfffd, 0xfffd, 0x7169, 0xfffd, 0xfffd, 0x7bc4, 0x8ca9, 0xfffd,
  0x98ef, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd,
  0xfffd, 0xfffd, 0x8a2a, 0x7d21, 0xfffd, 0xfffd, 0xfffd, 0xfffd,
  0x98db, 0xfffd, 0xfffd, 0x8ab9, 0xfffd, 0xfffd, 0x5ee2, 0xfffd,
  0x8cbb, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0x7d1b, 0x58b3,
  0xfffd, 0xfffd, 0xfffd, 0x596e, 0xfffd, 0xfffd, 0x61a4, 0x7cde,
  0x8c50, 0xfffd, 0x6953, 0xfffd, 0xfffd, 0x92d2, 0x98a8, 0x760b,
  0xfffd, 0xfffd, 0x99ae, 0x7e2b, 0x8af7, 0xfffd, 0x9cf3, 0xfffd,
  0xfffd, 0xfffd, 0xfffd, 0x819a, 0xfffd, 0xfffd, 0xfffd, 0x8f3b,
  0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd,
  /* 0x38 */
  0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0x64ab, 0x8f14,
  0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd,
  0xfffd, 0xfffd, 0x8ce6, 0x5fa9, 0xfffd, 0xfffd, 0xfffd, 0xfffd,
  0xfffd, 0x8ca0, 0xfffd, 0x8a03, 0xfffd, 0x5a66, 0x7e1b, 0xfffd,
  0xfffd, 0xfffd, 0x8a72, 0xfffd, 0xfffd, 0x9223, 0x84cb, 0xfffd,
  0x5e79, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0x8d95, 0xfffd,
  0xfffd, 0xfffd, 0x8d1b, 0x5ca1, 0x525b, 0x92fc, 0xfffd, 0xfffd,
  0x7db1, 0x5d17, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd,
  0xfffd, 0xfffd, 0xfffd, 0x93ac, 0xfffd, 0xfffd, 0xfffd, 0xfffd,
  0x64f1, 0xfffd, 0x9d3f, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd,
  0xfffd, 0xfffd, 0x95a3, 0xfffd, 0x927b, 0x500b, 0xfffd, 0x7d66,
  0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd,
  /* 0x39 */
  0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0x9f94,
  0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0x978f, 0xfffd, 0xfffd,
  0x8ca2, 0xfffd, 0x920e, 0xfffd, 0x6e9d, 0xfffd, 0xfffd, 0xfffd,
  0x69cb, 0x8cfc, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd,
  0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0x8831, 0xfffd, 0xfffd,
  0xfffd, 0xfffd, 0x9867, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0x526e,
  0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0x95dc,
  0xfffd, 0xfffd, 0x89c0, 0xfffd, 0x9928, 0xfffd, 0x6163, 0xfffd,
  0x8cab, 0xfffd, 0x5ee3, 0xfffd, 0xfffd, 0x898f, 0xfffd, 0xfffd,
  0x6b78, 0x9f9c, 0x95a8, 0x8ecc, 0xfffd, 0x8a6d, 0xfffd, 0xfffd,
  0x6ac3, 0xfffd, 0x8cb4, 0x528a, 0x8f25, 0xfffd, 0xfffd, 0x934b,
  0xfffd, 0x570b, 0xfffd, 0xfffd, 0x904e, 0xfffd,
  /* 0x3a */
  0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0x99ed, 0xfffd,
  0xfffd, 0xfffd, 0x97d3, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd,
  0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd,
  0xfffd, 0x6f22, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd,
  0xfffd, 0xfffd, 0xfffd, 0xfffd, 0x865f, 0xfffd, 0xfffd, 0xfffd,
  0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd,
  0xfffd, 0x95a1, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0x9db4, 0x8cc0,
  0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd,
  0xfffd, 0xfffd, 0xfffd, 0x8f5f, 0xfffd, 0xfffd, 0xfffd, 0x9d3b,
  0xfffd, 0xfffd, 0xfffd, 0x7d05, 0xfffd, 0xfffd, 0xfffd, 0xfffd,
  0xfffd, 0xfffd, 0x5f8c, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0x58fa,
  0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd,
  /* 0x3b */
  0xfffd, 0xfffd, 0xfffd, 0x8b77, 0xfffd, 0x6eec, 0xfffd, 0xfffd,
  0x5629, 0x83ef, 0xfffd, 0xfffd, 0x756b, 0x5283, 0xfffd, 0x8a71,
  0xfffd, 0xfffd, 0x61f7, 0xfffd, 0x58de, 0x6b61, 0x74b0, 0xfffd,
  0x9084, 0x7de9, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd,
  0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd,
  0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd,
  0x8b0a, 0xfffd, 0x63ee, 0x8f1d, 0xfffd, 0xfffd, 0xfffd, 0xfffd,
  0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0x8cc4, 0x7a62,
  0x6703, 0x71f4, 0x532f, 0x8af1, 0x8aa8, 0x7e6a, 0x8477, 0xfffd,
  0xfffd, 0xfffd, 0x6e3e, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd,
  0x7372, 0xfffd, 0xfffd, 0xfffd, 0x8ca8, 0x798d, 0x64ca, 0xfffd,
  0xfffd, 0x6a5f, 0xfffd, 0xfffd, 0x7a4d, 0xfffd,
  /* 0x3c */
  0xfffd, 0x9951, 0xfffd, 0xfffd, 0x8b4f, 0x9dc4, 0xfffd, 0x7e3e,
  0x7ddd, 0xfffd, 0x6975, 0xfffd, 0x8f2f, 0xfffd, 0xfffd, 0xfffd,
  0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0x7d1a, 0x64e0, 0x5e7e,
  0xfffd, 0xfffd, 0x858a, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd,
  0x5291, 0xfffd, 0x6fdf, 0xfffd, 0xfffd, 0x8a08, 0x8a18, 0xfffd,
  0xfffd, 0x969b, 0xfffd, 0x7e7c, 0x7d00, 0xfffd, 0xfffd, 0x593e,
  0xfffd, 0xfffd, 0xfffd, 0x83a2, 0x9830, 0x8cc8, 0xfffd, 0x9240,
  0xfffd, 0xfffd, 0x50f9, 0xfffd, 0x99d5, 0xfffd, 0x6bb2, 0x76e3,
  0x5805, 0xfffd, 0x7b8b, 0x9593, 0xfffd, 0xfffd, 0xfffd, 0x8271,
  0xfffd, 0x7dd8, 0x7e6d, 0x6aa2, 0xfffd, 0xfffd, 0x9e7c, 0x63c0,
  0x64bf, 0x7c21, 0x5109, 0xfffd, 0xfffd, 0x85a6, 0x6abb, 0x9452,
  0x8e10, 0x8ce4, 0x898b, 0x9375, 0xfffd, 0xfffd,
  /* 0x3d */
  0xfffd, 0x8266, 0x528d, 0x991e, 0x6f38, 0x6ffa, 0x6f97, 0xfffd,
  0xfffd, 0xfffd, 0x5c07, 0x6f3f, 0xfffd, 0xfffd, 0x8523, 0x69f3,
  0x596c, 0x8b1b, 0xfffd, 0x91ac, 0xfffd, 0xfffd, 0xfffd, 0xfffd,
  0xfffd, 0x81a0, 0xfffd, 0xfffd, 0x6f86, 0x9a55, 0x5b0c, 0xfffd,
  0x652a, 0x9278, 0x77ef, 0x50e5, 0xfffd, 0xfffd, 0xfffd, 0x9903,
  0x7e73, 0x7d5e, 0xfffd, 0xfffd, 0xfffd, 0x8f4e, 0x8f03, 0xfffd,
  0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0x968e, 0xfffd,
  0xfffd, 0x7bc0, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0x6f54,
  0x7d50, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd,
  0xfffd, 0xfffd, 0x8aa1, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd,
  0xfffd, 0xfffd, 0xfffd, 0x7dca, 0x9326, 0x50c5, 0x8b39, 0x9032,
  0xfffd, 0xfffd, 0xfffd, 0xfffd, 0x71fc, 0xfffd,
  /* 0x3e */
  0x76e1, 0x52c1, 0xfffd, 0xfffd, 0x8396, 0xfffd, 0xfffd, 0x9be8,
  0xfffd, 0x9a5a, 0xfffd, 0xfffd, 0x7d93, 0xfffd, 0xfffd, 0xfffd,
  0x9838, 0xfffd, 0xfffd, 0xfffd, 0x93e1, 0x5f91, 0x75d9, 0xfffd,
  0xfffd, 0x7af6, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0x7cfe,
  0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd,
  0x820a, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd,
  0xfffd, 0xfffd, 0xfffd, 0x99d2, 0xfffd, 0xfffd, 0xfffd, 0xfffd,
  0x8209, 0xfffd, 0xfffd, 0xfffd, 0x64da, 0xfffd, 0xfffd, 0xfffd,
  0xfffd, 0x92f8, 0xfffd, 0xfffd, 0x61fc, 0xfffd, 0x5287, 0xfffd,
  0x9d51, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0x7d79, 0xfffd, 0xfffd,
  0xfffd, 0xfffd, 0xfffd, 0xfffd, 0x89ba, 0xfffd, 0x8a23, 0x7d76,
  0xfffd, 0xfffd, 0x921e, 0x8ecd, 0xfffd, 0xfffd,
  /* 0x3f */
  0xfffd, 0xfffd, 0xfffd, 0xfffd, 0x99ff, 0xfffd, 0xfffd, 0xfffd,
  0xfffd, 0x958b, 0xfffd, 0xfffd, 0x51f1, 0xfffd, 0xfffd, 0xfffd,
  0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd,
  0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd,
  0xfffd, 0xfffd, 0xfffd, 0xfffd, 0x9846, 0xfffd, 0x6bbb, 0xfffd,
  0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0x8ab2, 0xfffd, 0xfffd,
  0x58be, 0x61c7, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd,
  0x6473, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd,
  0xfffd, 0x5eab, 0x8932, 0x8a87, 0xfffd, 0xfffd, 0xfffd, 0xfffd,
  0x584a, 0xfffd, 0x5108, 0xfffd, 0x5bec, 0xfffd, 0xfffd, 0xfffd,
  0xfffd, 0xfffd, 0x7926, 0xfffd, 0x66e0, 0xfffd, 0x8667, 0xfffd,
  0x5dcb, 0x7aba, 0xfffd, 0xfffd, 0xfffd, 0xfffd,
  /* 0x40 */
  0x994b, 0xfffd, 0x6f70, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd,
  0x64f4, 0xfffd, 0x95ca, 0xfffd, 0xfffd, 0xfffd, 0x881f, 0x81d8,
  0xfffd, 0xfffd, 0x840a, 0x4f86, 0x8cf4, 0x85cd, 0xfffd, 0x6b04,
  0x6514, 0x7c43, 0x95cc, 0x862d, 0x703e, 0x8b95, 0x652c, 0x89bd,
  0x61f6, 0x7e9c, 0x721b, 0x6feb, 0xfffd, 0xfffd, 0xfffd, 0xfffd,
  0xfffd, 0xfffd, 0xfffd, 0x6488, 0x52de, 0xfffd, 0xfffd, 0xfffd,
  0xfffd, 0xfffd, 0xfffd, 0x6f87, 0xfffd, 0x6a02, 0xfffd, 0x9433,
  0xfffd, 0xfffd, 0xfffd, 0xfffd, 0x58d8, 0xfffd, 0xfffd, 0x985e,
  0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd,
  0x7c6c, 0xfffd, 0x96e2, 0x7055, 0xfffd, 0xfffd, 0x88cf, 0x9bc9,
  0x79ae, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0x9e97, 0x53b2, 0x52f5,
  0x792b, 0x6b77, 0xfffd, 0xfffd, 0xfffd, 0xfffd,
  /* 0x41 */
  0xfffd, 0xfffd, 0xfffd, 0x701d, 0x96b8, 0xfffd, 0xfffd, 0xfffd,
  0x5006, 0x806f, 0x84ee, 0x9023, 0x942e, 0xfffd, 0x6190, 0x6f23,
  0x7c3e, 0x6582, 0x81c9, 0x93c8, 0x6200, 0x7149, 0x7df4, 0x7ce7,
  0xfffd, 0xfffd, 0xfffd, 0xfffd, 0x5169, 0x8f1b, 0xfffd, 0xfffd,
  0xfffd, 0x8ad2, 0xfffd, 0xfffd, 0xfffd, 0x7642, 0xfffd, 0xfffd,
  0x907c, 0xfffd, 0xfffd, 0xfffd, 0x9410, 0xfffd, 0xfffd, 0xfffd,
  0xfffd, 0xfffd, 0xfffd, 0x7375, 0xfffd, 0xfffd, 0xfffd, 0xfffd,
  0x81e8, 0x9130, 0x9c57, 0xfffd, 0xfffd, 0x8cc3, 0xfffd, 0xfffd,
  0xfffd, 0xfffd, 0xfffd, 0x9f61, 0x9234, 0xfffd, 0xfffd, 0xfffd,
  0x9748, 0xfffd, 0x5dba, 0x9818, 0xfffd, 0xfffd, 0xfffd, 0xfffd,
  0xfffd, 0xfffd, 0x993e, 0xfffd, 0x5289, 0xfffd, 0xfffd, 0xfffd,
  0xfffd, 0x9f8d, 0x807e, 0x56a8, 0x7c60, 0xfffd,
  /* 0x42 */
  0xfffd, 0x58df, 0x650f, 0x96b4, 0x6a13, 0x5a41, 0x645f, 0x7c0d,
  0xfffd, 0xfffd, 0x8606, 0x76e7, 0x9871, 0x5eec, 0x7210, 0x64c4,
  0x6ef7, 0x865c, 0x9b6f, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0x8cc2,
  0xfffd, 0xfffd, 0xfffd, 0x9332, 0x9678, 0xfffd, 0x9a62, 0xfffd,
  0x92c1, 0xfffd, 0xfffd, 0xfffd, 0x5c62, 0x7e37, 0x616e, 0xfffd,
  0xfffd, 0xfffd, 0x6ffe, 0x7dd1, 0x5dd2, 0x6523, 0x5b7f, 0x7064,
  0xfffd, 0x4e82, 0xfffd, 0xfffd, 0x6384, 0x8f2a, 0x502b, 0x4f96,
  0x6dea, 0x7db8, 0x8ad6, 0x863f, 0xfffd, 0x7f85, 0x908f, 0x947c,
  0x7c6e, 0x9a3e, 0xfffd, 0xfffd, 0xfffd, 0x99f1, 0x7d61, 0x5abd,
  0xfffd, 0x746a, 0x78bc, 0x879e, 0x99ac, 0x99e1, 0xfffd, 0x55ce,
  0xfffd, 0x8cb7, 0x9ea5, 0x8ce3, 0x9081, 0xfffd, 0x779e, 0x9945,
  0x883b, 0x6eff, 0xfffd, 0xfffd, 0xfffd, 0xfffd,
  /* 0x43 */
  0x8b3e, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd,
  0xfffd, 0x9328, 0xfffd, 0xfffd, 0x925a, 0xfffd, 0xfffd, 0xfffd,
  0xfffd, 0xfffd, 0x8cbf, 0x9ebd, 0xfffd, 0xfffd, 0xfffd, 0xfffd,
  0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0x9382, 0xfffd, 0xfffd,
  0xfffd, 0xfffd, 0xfffd, 0xfffd, 0x9580, 0x60b6, 0x5011, 0xfffd,
  0xfffd, 0xfffd, 0xfffd, 0x9333, 0xfffd, 0x5922, 0xfffd, 0xfffd,
  0xfffd, 0xfffd, 0xfffd, 0xfffd, 0x8b0e, 0x5f4c, 0xfffd, 0xfffd,
  0x8993, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0x7dbf,
  0xfffd, 0xfffd, 0xfffd, 0xfffd, 0x7dec, 0xfffd, 0xfffd, 0xfffd,
  0xfffd, 0xfffd, 0xfffd, 0xfffd, 0x5edf, 0xfffd, 0xfffd, 0x6ec5,
  0xfffd, 0xfffd, 0xfffd, 0xfffd, 0x61ab, 0x95a9, 0xfffd, 0xfffd,
  0x9cf4, 0x9298, 0xfffd, 0xfffd, 0x8b2c, 0xfffd,
  /* 0x44 */
  0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd,
  0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd,
  0x8b00, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0x755d, 0xfffd, 0xfffd,
  0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd,
  0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0x9209, 0xfffd, 0xfffd,
  0x7d0d, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd,
  0x96e3, 0xfffd, 0x6493, 0x8166, 0x60f1, 0x9b27, 0xfffd, 0xfffd,
  0x9912, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd,
  0xfffd, 0x64ec, 0xfffd, 0xfffd, 0x81a9, 0xfffd, 0xfffd, 0xfffd,
  0xfffd, 0xfffd, 0xfffd, 0x6506, 0xfffd, 0xfffd, 0xfffd, 0x91c0,
  0x9ce5, 0xfffd, 0xfffd, 0x8076, 0xfffd, 0x5699, 0x9477, 0x93b3,
  0xfffd, 0xfffd, 0x6ab8, 0x7370, 0xfffd, 0x5be7,
  /* 0x45 */
  0x64f0, 0x6fd8, 0xfffd, 0xfffd, 0x9215, 0x7d10, 0x81bf, 0x6fc3,
  0x8fb2, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd,
  0x7627, 0xfffd, 0xfffd, 0xfffd, 0x8afe, 0xfffd, 0x6b50, 0x9dd7,
  0x6bc6, 0xfffd, 0x5614, 0xfffd, 0x6f1a, 0xfffd, 0xfffd, 0xfffd,
  0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd,
  0xfffd, 0xfffd, 0xfffd, 0x76e4, 0xfffd, 0xfffd, 0xfffd, 0xfffd,
  0xfffd, 0xfffd, 0x9f90, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd,
  0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd,
  0xfffd, 0x8ce0, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0x5674, 0xfffd,
  0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd,
  0xfffd, 0xfffd, 0xfffd, 0x9d6c, 0xfffd, 0xfffd, 0xfffd, 0xfffd,
  0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd,
  /* 0x46 */
  0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd,
  0xfffd, 0xfffd, 0xfffd, 0xfffd, 0x9a19, 0x98c4, 0xfffd, 0xfffd,
  0xfffd, 0xfffd, 0xfffd, 0xfffd, 0x983b, 0x8ca7, 0xfffd, 0xfffd,
  0xfffd, 0xfffd, 0x860b, 0xfffd, 0xfffd, 0x6191, 0xfffd, 0x8a55,
  0xfffd, 0xfffd, 0x6f51, 0x9817, 0xfffd, 0xfffd, 0xfffd, 0xfffd,
  0xfffd, 0xfffd, 0x64b2, 0x92ea, 0x50d5, 0xfffd, 0xfffd, 0xfffd,
  0xfffd, 0xfffd, 0x6a38, 0xfffd, 0xfffd, 0xfffd, 0x8b5c, 0xfffd,
  0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd,
  0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd,
  0xfffd, 0x81cd, 0x9f4a, 0xfffd, 0xfffd, 0xfffd, 0x9a0e, 0xfffd,
  0x8c48, 0xfffd, 0xfffd, 0x5553, 0xfffd, 0xfffd, 0xfffd, 0x6c23,
  0xfffd, 0xfffd, 0xfffd, 0xfffd, 0x8a16, 0xfffd,
  /* 0x47 */
  0xfffd, 0xfffd, 0x727d, 0xfffd, 0x91fa, 0x925b, 0xfffd, 0x9077,
  0x7c3d, 0xfffd, 0x8b19, 0xfffd, 0xfffd, 0x9322, 0x9257, 0xfffd,
  0xfffd, 0xfffd, 0x6dfa, 0x8b74, 0x5879, 0xfffd, 0xfffd, 0xfffd,
  0x69cd, 0x55c6, 0xfffd, 0xfffd, 0x58bb, 0x8594, 0xfffd, 0x6436,
  0xfffd, 0x936c, 0xfffd, 0xfffd, 0x6a4b, 0xfffd, 0x55ac, 0x50d1,
  0xfffd, 0xfffd, 0xfffd, 0x7ff9, 0xfffd, 0xfffd, 0x7ac5, 0xfffd,
  0xfffd, 0xfffd, 0xfffd, 0x7aca, 0x6b3d, 0xfffd, 0x89aa, 0xfffd,
  0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0x5be2, 0xfffd, 0xfffd,
  0x8f15, 0x6c2b, 0x50be, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd,
  0xfffd, 0x9803, 0x8acb, 0x6176, 0x74ca, 0x7aae, 0xfffd, 0xfffd,
  0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0x8da8, 0x5340,
  0xfffd, 0xfffd, 0x8ec0, 0xfffd, 0x9a45, 0xfffd,
  /* 0x48 */
  0xfffd, 0xfffd, 0x9f72, 0xfffd, 0xfffd, 0xfffd, 0x9874, 0x6b0a,
  0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0x52f8,
  0xfffd, 0xfffd, 0xfffd, 0xfffd, 0x9d72, 0xfffd, 0x78ba, 0xfffd,
  0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd,
  0xfffd, 0xfffd, 0x8b93, 0x9952, 0x64fe, 0x7e5e, 0xfffd, 0x71b1,
  0xfffd, 0xfffd, 0xfffd, 0xfffd, 0x97cc, 0xfffd, 0x8a8d, 0xfffd,
  0xfffd, 0x7d09, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd,
  0x69ae, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0x7d68, 0xfffd, 0xfffd,
  0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd,
  0xfffd, 0xfffd, 0xfffd, 0xfffd, 0x8edf, 0xfffd, 0xfffd, 0xfffd,
  0x92ed, 0x958f, 0x6f64, 0xfffd, 0xfffd, 0xfffd, 0x7051, 0x85a9,
  0xfffd, 0x9c13, 0xfffd, 0x8cfd, 0xfffd, 0xfffd,
  /* 0x49 */
  0x5098, 0xfffd, 0xfffd, 0xfffd, 0x55aa, 0xfffd, 0x9a37, 0x6383,
  0xfffd, 0xfffd, 0xfffd, 0x6f80, 0xfffd, 0xfffd, 0xfffd, 0xfffd,
  0x6bba, 0xfffd, 0xfffd, 0x7d17, 0xfffd, 0xfffd, 0xfffd, 0x7be9,
  0x66ec, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd,
  0x9583, 0x965d, 0xfffd, 0x8d0d, 0xfffd, 0xfffd, 0xfffd, 0xfffd,
  0x7e55, 0xfffd, 0x50b7, 0xfffd, 0x8cde, 0xfffd, 0xfffd, 0xfffd,
  0xfffd, 0xfffd, 0xfffd, 0xfffd, 0x71d2, 0xfffd, 0xfffd, 0xfffd,
  0xfffd, 0xfffd, 0xfffd, 0x7d39, 0xfffd, 0x8cd2, 0xfffd, 0xfffd,
  0x6368, 0xfffd, 0x651d, 0xfffd, 0x61fe, 0xfffd, 0xfffd, 0x8a2d,
  0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0x7d33,
  0xfffd, 0xfffd, 0x5be9, 0x5b38, 0xfffd, 0x814e, 0xfffd, 0x6ef2,
  0x8072, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0x7e69,
  /* 0x4a */
  0xfffd, 0xfffd, 0xfffd, 0x52dd, 0x8056, 0x5e2b, 0xfffd, 0x7345,
  0xfffd, 0x6fd5, 0x8a69, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd,
  0x6642, 0xfffd, 0xfffd, 0x8755, 0x5be6, 0x8b58, 0xfffd, 0xfffd,
  0xfffd, 0xfffd, 0x99db, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd,
  0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0x52e2, 0xfffd, 0xfffd,
  0xfffd, 0x9069, 0xfffd, 0xfffd, 0x91cb, 0x98fe, 0xfffd, 0xfffd,
  0xfffd, 0xfffd, 0x8996, 0x8a66, 0xfffd, 0xfffd, 0xfffd, 0xfffd,
  0x58fd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0x7378, 0xfffd, 0x6a1e,
  0xfffd, 0xfffd, 0xfffd, 0x8f38, 0xfffd, 0xfffd, 0xfffd, 0xfffd,
  0x66f8, 0x8d16, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd,
  0xfffd, 0xfffd, 0xfffd, 0x5c6c, 0x8853, 0xfffd, 0x6a39, 0xfffd,
  0xfffd, 0x7aea, 0xfffd, 0xfffd, 0x6578, 0xfffd,
  /* 0x4b */
  0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0x5e25, 0xfffd,
  0xfffd, 0xfffd, 0x96d9, 0xfffd, 0x8ab0, 0xfffd, 0xfffd, 0xfffd,
  0xfffd, 0xfffd, 0x9806, 0xfffd, 0x8aac, 0x78a9, 0xfffd, 0x720d,
  0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0x7d72, 0xfffd,
  0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0x98fc, 0xfffd,
  0x9b06, 0x8073, 0x616b, 0x980c, 0xfffd, 0xfffd, 0x8a1f, 0x8aa6,
  0xfffd, 0xfffd, 0x64fb, 0xfffd, 0x8607, 0xfffd, 0xfffd, 0xfffd,
  0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0x8a34, 0x8085,
  0xfffd, 0xfffd, 0xfffd, 0x96d6, 0xfffd, 0x96a8, 0x7d8f, 0xfffd,
  0xfffd, 0x6b72, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0x5b6b, 0x640d,
  0xfffd, 0xfffd, 0xfffd, 0xfffd, 0x7e2e, 0x7463, 0xfffd, 0x9396,
  0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd,
  /* 0x4c */
  0x737a, 0x64bb, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0x81fa,
  0xfffd, 0xfffd, 0xfffd, 0x614b, 0xfffd, 0xfffd, 0x6524, 0x8caa,
  0x7671, 0x7058, 0x58c7, 0xfffd, 0xfffd, 0xfffd, 0x8b5a, 0x8ac7,
  0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0x5606, 0xfffd, 0x6e6f,
  0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd,
  0xfffd, 0xfffd, 0xfffd, 0x71d9, 0xfffd, 0x6fe4, 0xfffd, 0x7e27,
  0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0x8a0e, 0xfffd, 0xfffd,
  0xfffd, 0x9a30, 0xfffd, 0x8b04, 0xfffd, 0xfffd, 0xfffd, 0x92bb,
  0xfffd, 0x984c, 0xfffd, 0xfffd, 0x9ad4, 0xfffd, 0xfffd, 0xfffd,
  0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd,
  0xfffd, 0xfffd, 0xfffd, 0xfffd, 0x689d, 0xfffd, 0xfffd, 0xfffd,
  0x8cbc, 0x9435, 0xfffd, 0x5ef3, 0x807d, 0x70f4,
  /* 0x4d */
  0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd,
  0xfffd, 0xfffd, 0xfffd, 0xfffd, 0x9285, 0xfffd, 0xfffd, 0xfffd,
  0xfffd, 0xfffd, 0x7d71, 0xfffd, 0xfffd, 0xfffd, 0x982d, 0xfffd,
  0xfffd, 0xfffd, 0xfffd, 0x5716, 0xfffd, 0xfffd, 0x5857, 0xfffd,
  0xfffd, 0xfffd, 0xfffd, 0xfffd, 0x5718, 0xfffd, 0x983d, 0xfffd,
  0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd,
  0xfffd, 0x9d15, 0xfffd, 0x99b1, 0x99dd, 0x6a62, 0xfffd, 0xfffd,
  0xfffd, 0xfffd, 0xfffd, 0xfffd, 0x7aaa, 0xfffd, 0xfffd, 0x896a,
  0xfffd, 0xfffd, 0xfffd, 0x5f4e, 0x7063, 0xfffd, 0x9811, 0xfffd,
  0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd,
  0xfffd, 0x842c, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0x7db2,
  0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd,
  /* 0x4e */
  0xfffd, 0xfffd, 0xfffd, 0x97cb, 0x9055, 0xfffd, 0x570d, 0xfffd,
  0xfffd, 0x7232, 0x6ff0, 0x7dad, 0x8466, 0xfffd, 0xfffd, 0x5049,
  0x50de, 0xfffd, 0x7def, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd,
  0xfffd, 0xfffd, 0xfffd, 0xfffd, 0x8b02, 0xfffd, 0xfffd, 0x885b,
  0xfffd, 0xfffd, 0xfffd, 0xfffd, 0x805e, 0x7d0b, 0xfffd, 0x7a69,
  0xfffd, 0x554f, 0xfffd, 0xfffd, 0xfffd, 0x64be, 0x8778, 0x6e26,
  0x7aa9, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0x55da,
  0x93a2, 0x70cf, 0xfffd, 0x8aa3, 0xfffd, 0x7121, 0x856a, 0xfffd,
  0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd,
  0xfffd, 0xfffd, 0x5862, 0xfffd, 0x9727, 0xfffd, 0xfffd, 0xfffd,
  0x52d9, 0xfffd, 0x8aa4, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd,
  0xfffd, 0xfffd, 0xfffd, 0xfffd, 0x932b, 0x72a7,
  /* 0x4f */
  0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd,
  0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0x8972, 0xfffd, 0x7fd2,
  0xfffd, 0xfffd, 0x9291, 0xfffd, 0xfffd, 0xfffd, 0x6232, 0x7d30,
  0xfffd, 0x8766, 0xfffd, 0xfffd, 0x8f44, 0xfffd, 0x5cfd, 0x4fe0,
  0x72f9, 0xfffd, 0xfffd, 0xfffd, 0x5687, 0xfffd, 0x9341, 0xfffd,
  0xfffd, 0x9bae, 0x7e96, 0xfffd, 0x8ce2, 0x929c, 0xfffd, 0x9591,
  0xfffd, 0xfffd, 0xfffd, 0x986f, 0x96aa, 0x73fe, 0x737b, 0x7e23,
  0xfffd, 0x9921, 0xfffd, 0x61b2, 0xfffd, 0xfffd, 0x7dab, 0xfffd,
  0xfffd, 0x9472, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0x9109, 0xfffd,
  0xfffd, 0x8a73, 0xfffd, 0x97ff, 0xfffd, 0x9805, 0xfffd, 0xfffd,
  0xfffd, 0xfffd, 0xfffd, 0x856d, 0xfffd, 0xfffd, 0xfffd, 0xfffd,
  0x56c2, 0x92b7, 0xfffd, 0xfffd, 0xfffd, 0x66c9,
  /* 0x50 */
  0xfffd, 0xfffd, 0xfffd, 0xfffd, 0x562f, 0xfffd, 0xfffd, 0xfffd,
  0xfffd, 0xfffd, 0xfffd, 0xfffd, 0x5354, 0x633e, 0xfffd, 0xfffd,
  0xfffd, 0x8105, 0x8ae7, 0x5beb, 0xfffd, 0xfffd, 0xfffd, 0xfffd,
  0xfffd, 0x7009, 0x8b1d, 0xfffd, 0xfffd, 0xfffd, 0x92c5, 0xfffd,
  0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0x91c1, 0xfffd, 0xfffd,
  0xfffd, 0xfffd, 0x8208, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd,
  0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd,
  0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd,
  0xfffd, 0x92b9, 0xfffd, 0xfffd, 0x7d89, 0xfffd, 0xfffd, 0xfffd,
  0xfffd, 0xfffd, 0x9808, 0xfffd, 0x8a31, 0xfffd, 0xfffd, 0xfffd,
  0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0x7dd2, 0x7e8c,
  0x8ed2, 0xfffd, 0xfffd, 0x61f8, 0xfffd, 0xfffd,
  /* 0x51 */
  0x9078, 0x766c, 0xfffd, 0x7d62, 0xfffd, 0xfffd, 0x5b78, 0xfffd,
  0xfffd, 0xfffd, 0x52db, 0xfffd, 0xfffd, 0xfffd, 0x8a62, 0x5c0b,
  0x99b4, 0xfffd, 0xfffd, 0xfffd, 0x8a13, 0x8a0a, 0x905c, 0xfffd,
  0x58d3, 0xfffd, 0x9d09, 0x9d28, 0xfffd, 0xfffd, 0xfffd, 0xfffd,
  0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0x555e, 0x4e9e, 0x8a1d,
  0xfffd, 0xfffd, 0x95b9, 0xfffd, 0xfffd, 0x9e7d, 0x56b4, 0xfffd,
  0xfffd, 0xfffd, 0xfffd, 0xfffd, 0x9854, 0x95bb, 0xfffd, 0xfffd,
  0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0x8277, 0xfffd, 0xfffd,
  0x53ad, 0x786f, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0x8afa,
  0x9a57, 0xfffd, 0xfffd, 0x9d26, 0xfffd, 0x694a, 0x63da, 0xfffd,
  0x760d, 0xfffd, 0xfffd, 0x967d, 0xfffd, 0xfffd, 0x7662, 0x990a,
  0x6a23, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd,
  /* 0x52 */
  0xfffd, 0x582f, 0xfffd, 0xfffd, 0x8b21, 0xfffd, 0xfffd, 0xfffd,
  0x85e5, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0x723a, 0xfffd,
  0xfffd, 0xfffd, 0x9801, 0xfffd, 0x696d, 0x8449, 0xfffd, 0xfffd,
  0xfffd, 0xfffd, 0xfffd, 0xfffd, 0x91ab, 0xfffd, 0x92a5, 0xfffd,
  0xfffd, 0xfffd, 0x9824, 0xfffd, 0x907a, 0xfffd, 0x5100, 0xfffd,
  0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0x87fb, 0xfffd,
  0xfffd, 0xfffd, 0xfffd, 0xfffd, 0x85dd, 0xfffd, 0xfffd, 0xfffd,
  0xfffd, 0x5104, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd,
  0xfffd, 0xfffd, 0xfffd, 0x61b6, 0x7fa9, 0xfffd, 0xfffd, 0x8a63,
  0x8b70, 0x8abc, 0x8b6f, 0xfffd, 0xfffd, 0xfffd, 0x7e79, 0xfffd,
  0x852d, 0xfffd, 0xfffd, 0xfffd, 0x9670, 0xfffd, 0xfffd, 0x9280,
  0xfffd, 0xfffd, 0x98f2, 0xfffd, 0xfffd, 0x96b1,
  /* 0x53 */
  0xfffd, 0xfffd, 0x6afb, 0x5b30, 0x9df9, 0x61c9, 0x7e93, 0x7469,
  0x87a2, 0x71df, 0x7192, 0x8805, 0xfffd, 0x8d0f, 0xfffd, 0xfffd,
  0x7a4e, 0xfffd, 0xfffd, 0x55b2, 0x64c1, 0x50ad, 0xfffd, 0x7670,
  0xfffd, 0xfffd, 0x8e34, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd,
  0xfffd, 0xfffd, 0xfffd, 0xfffd, 0x512a, 0xfffd, 0x6182, 0xfffd,
  0xfffd, 0x90f5, 0x923e, 0x7336, 0xfffd, 0xfffd, 0xfffd, 0xfffd,
  0xfffd, 0xfffd, 0xfffd, 0xfffd, 0x8a98, 0xfffd, 0xfffd, 0xfffd,
  0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0x8f3f, 0x9918,
  0xfffd, 0xfffd, 0x9b5a, 0xfffd, 0xfffd, 0x6f01, 0xfffd, 0xfffd,
  0xfffd, 0xfffd, 0x8207, 0x5dbc, 0xfffd, 0xfffd, 0x8a9e, 0xfffd,
  0xfffd, 0xfffd, 0xfffd, 0x9b31, 0xfffd, 0xfffd, 0xfffd, 0xfffd,
  0xfffd, 0xfffd, 0xfffd, 0x7344, 0xfffd, 0x8b7d,
  /* 0x54 */
  0xfffd, 0xfffd, 0xfffd, 0x9810, 0xfffd, 0x99ad, 0x9d1b, 0x6df5,
  0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0x8f45, 0x5712,
  0x54e1, 0x5713, 0xfffd, 0xfffd, 0x7de3, 0x9060, 0xfffd, 0x9858,
  0xfffd, 0xfffd, 0xfffd, 0x7d04, 0xfffd, 0x8e8d, 0x9470, 0xfffd,
  0xfffd, 0xfffd, 0xfffd, 0x95b2, 0xfffd, 0x96f2, 0x9116, 0xfffd,
  0x9695, 0xfffd, 0x904b, 0x85f4, 0x9196, 0x6688, 0xfffd, 0xfffd,
  0xfffd, 0xfffd, 0x96dc, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0x8f09,
  0xfffd, 0xfffd, 0xfffd, 0x6522, 0x66ab, 0x8d0a, 0x8d1c, 0x81df,
  0xfffd, 0xfffd, 0xfffd, 0x947f, 0xfffd, 0x68d7, 0xfffd, 0xfffd,
  0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0x7ac8, 0xfffd, 0x8cac,
  0x64c7, 0x5247, 0x6fa4, 0x8cca, 0xfffd, 0xfffd, 0xfffd, 0xfffd,
  0x8d08, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0x8ecb,
  /* 0x55 */
  0x9358, 0x9598, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd,
  0x8a50, 0xfffd, 0x9f4b, 0xfffd, 0xfffd, 0x50b5, 0xfffd, 0xfffd,
  0x6c08, 0xfffd, 0xfffd, 0xfffd, 0x76de, 0x65ac, 0x8f3e, 0x5d84,
  0xfffd, 0xfffd, 0x68e7, 0xfffd, 0x6230, 0xfffd, 0xfffd, 0x7dbb,
  0xfffd, 0xfffd, 0xfffd, 0xfffd, 0x5f35, 0xfffd, 0x6f32, 0xfffd,
  0xfffd, 0x5e33, 0x8cec, 0xfffd, 0x8139, 0xfffd, 0xfffd, 0xfffd,
  0xfffd, 0xfffd, 0xfffd, 0x8d99, 0xfffd, 0xfffd, 0xfffd, 0xfffd,
  0xfffd, 0xfffd, 0xfffd, 0xfffd, 0x87c4, 0x8f4d, 0xfffd, 0x937a,
  0xfffd, 0x9019, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd,
  0xfffd, 0x8c9e, 0x91dd, 0x5075, 0xfffd, 0xfffd, 0x8a3a, 0xfffd,
  0xfffd, 0x93ae, 0x9663, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd,
  0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd,
  /* 0x56 */
  0x5e40, 0x7665, 0x912d, 0x8b49, 0xfffd, 0xfffd, 0xfffd, 0xfffd,
  0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0x7e54, 0x8077,
  0xfffd, 0xfffd, 0xfffd, 0x57f7, 0xfffd, 0xfffd, 0xfffd, 0xfffd,
  0xfffd, 0xfffd, 0x8879, 0xfffd, 0x7d19, 0xfffd, 0x646f, 0x64f2,
  0xfffd, 0xfffd, 0xfffd, 0x5e5f, 0xfffd, 0xfffd, 0xfffd, 0xfffd,
  0xfffd, 0x8cea, 0xfffd, 0xfffd, 0x6eef, 0xfffd, 0xfffd, 0xfffd,
  0xfffd, 0xfffd, 0x9418, 0xfffd, 0x7d42, 0x7a2e, 0x816b, 0xfffd,
  0xfffd, 0x8846, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0x8b05, 0xfffd,
  0x8ef8, 0xfffd, 0xfffd, 0xfffd, 0x76ba, 0xfffd, 0x665d, 0x9a5f,
  0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0x8af8, 0x8a85, 0xfffd,
  0xfffd, 0x71ed, 0xfffd, 0xfffd, 0x77da, 0x56d1, 0xfffd, 0xfffd,
  0xfffd, 0xfffd, 0xfffd, 0x8caf, 0x9444, 0x7bc9,
  /* 0x57 */
  0xfffd, 0xfffd, 0xfffd, 0x99d0, 0xfffd, 0xfffd, 0xfffd, 0x5c08,
  0x78da, 0x8f49, 0xfffd, 0x8cfa, 0xfffd, 0x6a01, 0x838a, 0x88dd,
  0x599d, 0xfffd, 0x58ef, 0x72c0, 0xfffd, 0x9310, 0xfffd, 0x8d05,
  0x589c, 0x7db4, 0x8ac4, 0x6e96, 0xfffd, 0xfffd, 0xfffd, 0xfffd,
  0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0x6fc1, 0xfffd,
  0xfffd, 0x8cc7, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd,
  0xfffd, 0xfffd, 0xfffd, 0xfffd, 0x6f2c, 0xfffd, 0xfffd, 0xfffd,
  0xfffd, 0xfffd, 0x7d9c, 0x7e3d, 0x7e31, 0x9112, 0xfffd, 0xfffd,
  0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0x8a5b, 0xfffd,
  0x7d44, 0x947d, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd,
  0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd,
  0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd,
  /* 0x58 */
  0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd,
  0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd,
  0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd,
  0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd,
  0xfffd, 0xfffd, 0xfffd, 0x55c7, 0xfffd, 0xfffd, 0x5399, 0xfffd,
  0x53b4, 0xfffd, 0xfffd, 0x9768, 0x8d0b, 0xfffd, 0xfffd, 0x532d,
  0x5331, 0xfffd, 0x8cfe, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd,
  0x5244, 0xfffd, 0x528c, 0x5274, 0xfffd, 0xfffd, 0xfffd, 0xfffd,
  0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd,
  0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd,
  0x50b4, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0x5000, 0x5096, 0xfffd,
  0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd,
  /* 0x59 */
  0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd,
  0xfffd, 0xfffd, 0xfffd, 0xfffd, 0x5115, 0xfffd, 0x5102, 0xfffd,
  0x5114, 0x513c, 0x5137, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd,
  0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd,
  0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0x50e8, 0xfffd,
  0xfffd, 0xfffd, 0xfffd, 0xfffd, 0x50c2, 0x513b, 0x5110, 0x513a,
  0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd,
  0xfffd, 0xfffd, 0xfffd, 0xfffd, 0x50c9, 0xfffd, 0xfffd, 0xfffd,
  0x7cf4, 0xfffd, 0xfffd, 0x9ecc, 0xfffd, 0x56c5, 0xfffd, 0xfffd,
  0xfffd, 0xfffd, 0xfffd, 0x9cec, 0xfffd, 0xfffd, 0xfffd, 0xfffd,
  0xfffd, 0xfffd, 0xfffd, 0x893b, 0x81e0, 0xfffd, 0xfffd, 0xfffd,
  0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd,
  /* 0x5a */
  0xfffd, 0xfffd, 0xfffd, 0xfffd, 0x8a01, 0x8a10, 0x8a0c, 0x8a15,
  0x8b33, 0x8a4e, 0x8a25, 0x8a41, 0x8a36, 0x8a46, 0x8a54, 0x8a58,
  0x8a52, 0x8a86, 0x8a84, 0x8a7f, 0x8a70, 0x8a7c, 0x8a75, 0x8a6c,
  0x8a6e, 0x8acd, 0x8ae2, 0x8a61, 0x8a9a, 0x8aa5, 0x8a91, 0x8a92,
  0x8acf, 0x8ad1, 0x8ac9, 0x8adb, 0x8ad7, 0x8ac2, 0x8ab6, 0x8af6,
  0x8aeb, 0x8b14, 0x8b01, 0x8ae4, 0x8aed, 0x8afc, 0x8af3, 0x8ae6,
  0x8aee, 0x8ade, 0x8b28, 0x8b9c, 0x8b16, 0x8b1a, 0x8b10, 0x8b2b,
  0x8b2d, 0x8b56, 0x8b59, 0x8b4e, 0x8b9e, 0x8b6b, 0x8b96, 0xfffd,
  0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd,
  0xfffd, 0x9658, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd,
  0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0x913a, 0xfffd,
  0x9114, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0x9134,
  /* 0x5b */
  0xfffd, 0xfffd, 0x90df, 0xfffd, 0xfffd, 0x9136, 0xfffd, 0xfffd,
  0x9106, 0x9148, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd,
  0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd,
  0xfffd, 0xfffd, 0x82bb, 0xfffd, 0x52f1, 0xfffd, 0xfffd, 0xfffd,
  0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd,
  0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0x5df0, 0xfffd,
  0x580a, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd,
  0xfffd, 0xfffd, 0x58d9, 0xfffd, 0xfffd, 0x58e2, 0xfffd, 0xfffd,
  0xfffd, 0x58e0, 0xfffd, 0x58da, 0xfffd, 0xfffd, 0xfffd, 0xfffd,
  0xfffd, 0xfffd, 0x57e1, 0xfffd, 0xfffd, 0x584f, 0xfffd, 0xfffd,
  0x5816, 0xfffd, 0xfffd, 0xfffd, 0x5852, 0x581d, 0x5864, 0xfffd,
  0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd,
  /* 0x5c */
  0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd,
  0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd,
  0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd,
  0xfffd, 0xfffd, 0xfffd, 0x858c, 0xfffd, 0xfffd, 0x8553, 0xfffd,
  0xfffd, 0x85f6, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0x83a7,
  0x8407, 0x84ef, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd,
  0x82e7, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0x8622, 0xfffd,
  0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0x8526,
  0xfffd, 0xfffd, 0x584b, 0x7162, 0xfffd, 0xfffd, 0xfffd, 0xfffd,
  0x8558, 0x84fd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd,
  0x854e, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0x8588, 0xfffd, 0xfffd,
  0x85ba, 0xfffd, 0xfffd, 0xfffd, 0x7296, 0x6ece,
  /* 0x5d */
  0x8541, 0xfffd, 0x85ce, 0x8552, 0x84c0, 0x8452, 0x8464, 0xfffd,
  0xfffd, 0x8494, 0x8435, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd,
  0xfffd, 0x859f, 0xfffd, 0xfffd, 0x8555, 0xfffd, 0xfffd, 0xfffd,
  0xfffd, 0x9daf, 0x8493, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd,
  0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd,
  0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd,
  0xfffd, 0xfffd, 0x7e08, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd,
  0xfffd, 0xfffd, 0x8546, 0xfffd, 0xfffd, 0x8562, 0xfffd, 0xfffd,
  0xfffd, 0xfffd, 0xfffd, 0x851e, 0xfffd, 0xfffd, 0xfffd, 0xfffd,
  0xfffd, 0xfffd, 0x9a40, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd,
  0x863a, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0x93a3, 0x8577, 0xfffd,
  0xfffd, 0xfffd, 0xfffd, 0x861e, 0xfffd, 0x85fa,
  /* 0x5e */
  0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd,
  0xfffd, 0xfffd, 0xfffd, 0xfffd, 0x8604, 0xfffd, 0xfffd, 0xfffd,
  0xfffd, 0xfffd, 0xfffd, 0x85ea, 0xfffd, 0xfffd, 0xfffd, 0xfffd,
  0xfffd, 0x861a, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd,
  0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0x5969, 0xfffd, 0xfffd,
  0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0x5c37, 0xfffd,
  0x636b, 0x6476, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0x649f,
  0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd,
  0xfffd, 0x6451, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0x645c,
  0xfffd, 0xfffd, 0xfffd, 0x64b3, 0xfffd, 0xfffd, 0xfffd, 0xfffd,
  0xfffd, 0xfffd, 0x6504, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd,
  0xfffd, 0xfffd, 0xfffd, 0x6516, 0xfffd, 0xfffd,
  /* 0x5f */
  0xfffd, 0x64f7, 0x64fc, 0xfffd, 0x651b, 0xfffd, 0xfffd, 0xfffd,
  0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd,
  0xfffd, 0xfffd, 0xfffd, 0x5630, 0xfffd, 0xfffd, 0xfffd, 0xfffd,
  0xfffd, 0xfffd, 0xfffd, 0x5638, 0x56c8, 0xfffd, 0x56a6, 0xfffd,
  0xfffd, 0x5504, 0x54bc, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd,
  0xfffd, 0xfffd, 0xfffd, 0x5680, 0xfffd, 0xfffd, 0xfffd, 0x565d,
  0xfffd, 0xfffd, 0xfffd, 0xfffd, 0x5660, 0xfffd, 0xfffd, 0x5635,
  0x55f6, 0xfffd, 0xfffd, 0x5666, 0xfffd, 0xfffd, 0xfffd, 0x5672,
  0xfffd, 0x568c, 0xfffd, 0xfffd, 0xfffd, 0x5665, 0xfffd, 0xfffd,
  0x561c, 0xfffd, 0x562e, 0xfffd, 0xfffd, 0xfffd, 0x55e9, 0xfffd,
  0xfffd, 0xfffd, 0xfffd, 0xfffd, 0x5616, 0xfffd, 0xfffd, 0xfffd,
  0x56c0, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd,
  /* 0x60 */
  0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd,
  0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd,
  0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0x560d, 0x56b3, 0xfffd,
  0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0x56c1, 0xfffd,
  0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0x566f,
  0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0x8f61, 0xfffd, 0xfffd,
  0xfffd, 0xfffd, 0x56b6, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd,
  0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0x5695,
  0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd,
  0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0x5707,
  0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0x5e43,
  0xfffd, 0xfffd, 0xfffd, 0x5e6c, 0x5e58, 0x5e57,
  /* 0x61 */
  0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd,
  0xfffd, 0xfffd, 0x5d87, 0xfffd, 0x5cf4, 0xfffd, 0xfffd, 0x5d50,
  0xfffd, 0xfffd, 0xfffd, 0x5d2c, 0xfffd, 0xfffd, 0xfffd, 0xfffd,
  0xfffd, 0xfffd, 0x5da7, 0xfffd, 0x5da0, 0xfffd, 0xfffd, 0x5d97,
  0x5d0d, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd,
  0x5db8, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0x5d81,
  0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd,
  0xfffd, 0xfffd, 0x5dd4, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd,
  0x540e, 0x5fa0, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd,
  0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0x7377, 0x7341, 0xfffd,
  0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0x736a, 0xfffd, 0x733b,
  0xfffd, 0xfffd, 0xfffd, 0xfffd, 0x736b, 0xfffd,
  /* 0x62 */
  0xfffd, 0xfffd, 0xfffd, 0x7380, 0xfffd, 0xfffd, 0xfffd, 0x737c,
  0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd,
  0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd,
  0xfffd, 0xfffd, 0x98e0, 0x9933, 0x98e9, 0x993c, 0x98ea, 0x98eb,
  0x98ed, 0x98f4, 0x9909, 0x9911, 0x4f59, 0x991b, 0x9937, 0x993f,
  0x9943, 0x9948, 0x9949, 0x994a, 0x994c, 0x9962, 0xfffd, 0x5ee1,
  0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd,
  0x8ce1, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd,
  0xfffd, 0xfffd, 0x61fa, 0x61ae, 0xfffd, 0x616a, 0xfffd, 0xfffd,
  0x613e, 0x60b5, 0x6134, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd,
  0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0x61cc,
  0xfffd, 0x615f, 0x61e8, 0x60fb, 0x6137, 0xfffd,
  /* 0x63 */
  0xfffd, 0x60f2, 0xfffd, 0xfffd, 0x6173, 0xfffd, 0xfffd, 0xfffd,
  0xfffd, 0xfffd, 0x611c, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd,
  0xfffd, 0xfffd, 0xfffd, 0x6192, 0xfffd, 0xfffd, 0xfffd, 0xfffd,
  0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd,
  0xfffd, 0xfffd, 0xfffd, 0xfffd, 0x9582, 0x9586, 0x95c8, 0x958e,
  0x9594, 0x958c, 0x95e5, 0x95ad, 0x95ab, 0x9b2e, 0x95ac, 0x95be,
  0x95b6, 0x9b29, 0x95bf, 0x95bd, 0x95bc, 0x95c3, 0x95cb, 0x95d4,
  0x95d0, 0x95d5, 0x95de, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd,
  0xfffd, 0xfffd, 0x7043, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd,
  0xfffd, 0xfffd, 0xfffd, 0xfffd, 0x6f59, 0xfffd, 0xfffd, 0xfffd,
  0x7027, 0x7018, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0x6ffc,
  0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0x6d87,
  /* 0x64 */
  0xfffd, 0xfffd, 0xfffd, 0x6d79, 0x6e5e, 0xfffd, 0xfffd, 0xfffd,
  0xfffd, 0xfffd, 0x6fae, 0xfffd, 0xfffd, 0xfffd, 0x700f, 0x6ef8,
  0x6f6f, 0xfffd, 0xfffd, 0xfffd, 0x6df6, 0x6f7f, 0xfffd, 0xfffd,
  0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd,
  0xfffd, 0x7006, 0xfffd, 0xfffd, 0x6fa0, 0xfffd, 0xfffd, 0xfffd,
  0x700b, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd,
  0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd,
  0x7067, 0xfffd, 0xfffd, 0x7044, 0xfffd, 0x7005, 0xfffd, 0xfffd,
  0xfffd, 0xfffd, 0xfffd, 0x6f77, 0xfffd, 0xfffd, 0xfffd, 0xfffd,
  0xfffd, 0xfffd, 0x7020, 0x701f, 0xfffd, 0xfffd, 0xfffd, 0xfffd,
  0xfffd, 0x7032, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd,
  0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0x7028,
  /* 0x65 */
  0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd,
  0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0x705d,
  0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd,
  0x9a2b, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd,
  0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0x9087, 0xfffd,
  0x9015, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0x9090, 0xfffd, 0xfffd,
  0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd,
  0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd,
  0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd,
  0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0x5c68,
  0xfffd, 0x5f33, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd,
  0xfffd, 0xfffd, 0xfffd, 0x5af5, 0x5ad7, 0xfffd,
  /* 0x66 */
  0xfffd, 0xfffd, 0x5b00, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd,
  0xfffd, 0xfffd, 0x5a6d, 0x5b08, 0xfffd, 0x5b4c, 0xfffd, 0xfffd,
  0xfffd, 0xfffd, 0xfffd, 0x5aa7, 0x5afb, 0xfffd, 0xfffd, 0xfffd,
  0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0x5b0b, 0xfffd,
  0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0x5b21,
  0x5b2a, 0xfffd, 0xfffd, 0xfffd, 0x5b19, 0xfffd, 0xfffd, 0xfffd,
  0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd,
  0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0x99d4,
  0x99df, 0x99d9, 0x9a36, 0x9a5b, 0x99d1, 0x99d8, 0x9a4d, 0x9a4a,
  0x99e2, 0x9a6a, 0x9a0f, 0x9a0d, 0x9a05, 0x9a42, 0x9a2d, 0x9a16,
  0x9a41, 0x9a2e, 0x9a38, 0x9a43, 0x9a44, 0x9a4f, 0x9a65, 0x9a64,
  0x7cf9, 0x7d06, 0x7d02, 0x7d07, 0x7d08, 0x7e8a,
  /* 0x67 */
  0x7d1c, 0x7d15, 0x7d13, 0x7d3a, 0x7d32, 0x7d31, 0x7e10, 0x7d3c,
  0x7d40, 0x7d3f, 0x7d5d, 0x7d4e, 0x7d73, 0x7d86, 0x7d83, 0x7d88,
  0x7dbe, 0x7dba, 0x7dcb, 0x7dd4, 0x7dc4, 0x7d9e, 0x7dac, 0x7db9,
  0x7da3, 0x7db0, 0x7dc7, 0x7dd9, 0x7dd7, 0x7df9, 0x7df2, 0x7e62,
  0x7de6, 0x7df6, 0x7df1, 0x7e0b, 0x7de1, 0x7e09, 0x7e1d, 0x7e1f,
  0x7e1e, 0x7e2d, 0x7e0a, 0x7e11, 0x7e7d, 0x7e39, 0x7e35, 0x7e32,
  0x7e46, 0x7e45, 0x7e88, 0x7e5a, 0x7e52, 0x7e6e, 0x7e7e, 0x7e70,
  0x7e6f, 0x7e98, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd,
  0x74a3, 0x744b, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0x74cf, 0xfffd,
  0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0x980a, 0xfffd,
  0xfffd, 0xfffd, 0xfffd, 0x74bd, 0x743f, 0x7489, 0xfffd, 0xfffd,
  0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd,
  /* 0x68 */
  0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0x74a6,
  0xfffd, 0xfffd, 0xfffd, 0x74d4, 0xfffd, 0xfffd, 0xfffd, 0xfffd,
  0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0x74da, 0xfffd, 0x97d9,
  0x97de, 0x97dc, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0x69aa, 0x6aea,
  0xfffd, 0xfffd, 0xfffd, 0xfffd, 0x6898, 0xfffd, 0x68d6, 0x6a05,
  0x689f, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0x6adb, 0xfffd, 0x6af3,
  0xfffd, 0xfffd, 0x6ae8, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd,
  0xfffd, 0xfffd, 0xfffd, 0xfffd, 0x6adf, 0xfffd, 0x6a89, 0xfffd,
  0xfffd, 0x690f, 0x6a48, 0xfffd, 0x6968, 0xfffd, 0x69bf, 0xfffd,
  0xfffd, 0xfffd, 0x6a3a, 0xfffd, 0x6a9c, 0xfffd, 0x6b12, 0xfffd,
  0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd,
  0x6b1e, 0xfffd, 0xfffd, 0x6add, 0x69e7, 0xfffd,
  /* 0x69 */
  0x6b0f, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd,
  0xfffd, 0xfffd, 0xfffd, 0xfffd, 0x6b16, 0xfffd, 0xfffd, 0xfffd,
  0xfffd, 0xfffd, 0xfffd, 0x6aec, 0x6ada, 0xfffd, 0x6af8, 0xfffd,
  0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd,
  0xfffd, 0xfffd, 0xfffd, 0x6ab3, 0xfffd, 0x6ae7, 0xfffd, 0xfffd,
  0x6aa3, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd,
  0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0x6ad3, 0xfffd, 0xfffd,
  0xfffd, 0x6ade, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd,
  0xfffd, 0xfffd, 0xfffd, 0x6ba4, 0xfffd, 0x6b9e, 0x6bae, 0xfffd,
  0x6bab, 0xfffd, 0x6baf, 0xfffd, 0x8ed4, 0x8edb, 0x8ef2, 0x8efb,
  0x8f64, 0x8ef9, 0x8efc, 0x8eeb, 0x8ee4, 0x8f62, 0x8efa, 0x8efe,
  0x8f0a, 0x8f07, 0x8f05, 0x8f12, 0x8f26, 0x8f1e,
  /* 0x6a */
  0x8f1f, 0x8f1c, 0x8f33, 0x8f46, 0x8f54, 0xfffd, 0x6214, 0x6227,
  0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd,
  0x750c, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd,
  0xfffd, 0xfffd, 0xfffd, 0x66c7, 0xfffd, 0xfffd, 0xfffd, 0xfffd,
  0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd,
  0xfffd, 0x66c4, 0xfffd, 0xfffd, 0x6689, 0xfffd, 0xfffd, 0xfffd,
  0xfffd, 0xfffd, 0x66d6, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd,
  0xfffd, 0x8cc1, 0x8cb0, 0x8cba, 0x8cbd, 0x8d04, 0x8cb2, 0x8cc5,
  0x8d10, 0x8cd1, 0x8cda, 0x8cd5, 0x8ceb, 0x8ce7, 0x8cfb, 0x8998,
  0x89ac, 0x89a1, 0x89bf, 0x89a6, 0x89af, 0x89b2, 0x89b7, 0xfffd,
  0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd,
  0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd,
  /* 0x6b */
  0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0x6bff, 0xfffd,
  0xfffd, 0x6c0c, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd,
  0xfffd, 0x6c2c, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd,
  0x7258, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd,
  0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd,
  0xfffd, 0x6727, 0x8156, 0xfffd, 0x81da, 0xfffd, 0xfffd, 0xfffd,
  0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0x811b, 0xfffd, 0xfffd,
  0xfffd, 0x81be, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd,
  0x8161, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd,
  0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd,
  0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0x81cf, 0xfffd,
  0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd,
  /* 0x6c */
  0xfffd, 0xfffd, 0x6b5f, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd,
  0x98ae, 0x98af, 0x98b6, 0x98bc, 0x98c6, 0x98c8, 0xfffd, 0xfffd,
  0x8f42, 0xfffd, 0xfffd, 0x9f4f, 0x6595, 0xfffd, 0xfffd, 0xfffd,
  0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0x716c, 0x7152, 0xfffd,
  0x7197, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0x71c1, 0xfffd,
  0xfffd, 0xfffd, 0x71dc, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd,
  0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd,
  0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd,
  0xfffd, 0x71fe, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd,
  0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd,
  0xfffd, 0x79b0, 0xfffd, 0xfffd, 0x798e, 0xfffd, 0xfffd, 0x79aa,
  0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd,
  /* 0x6d */
  0x61df, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0x6164,
  0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0x61e3, 0x6207,
  0xfffd, 0xfffd, 0xfffd, 0x6fa9, 0xfffd, 0x78ef, 0xfffd, 0x78ad,
  0xfffd, 0x7868, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0x78b8, 0xfffd,
  0xfffd, 0x792a, 0x7931, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd,
  0xfffd, 0xfffd, 0xfffd, 0x7864, 0x78fd, 0xfffd, 0xfffd, 0xfffd,
  0xfffd, 0xfffd, 0x78e7, 0xfffd, 0xfffd, 0xfffd, 0x78e3, 0xfffd,
  0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd,
  0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0x9f95,
  0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0x7798, 0xfffd, 0xfffd,
  0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd,
  0x775e, 0x77bc, 0xfffd, 0xfffd, 0xfffd, 0xfffd,
  /* 0x6e */
  0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd,
  0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd,
  0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd,
  0xfffd, 0xfffd, 0xfffd, 0x7f86, 0xfffd, 0xfffd, 0x7f88, 0xfffd,
  0xfffd, 0xfffd, 0xfffd, 0x91d2, 0x91d3, 0x91d4, 0x91d9, 0x91d7,
  0x91d5, 0x91f7, 0x91e7, 0x91e4, 0x9346, 0x91f5, 0x91f9, 0x9208,
  0x9226, 0x9245, 0x9211, 0x9210, 0x9201, 0x9227, 0x9204, 0x9225,
  0x9200, 0x923a, 0x9266, 0x9237, 0x9233, 0x9255, 0x923d, 0x9238,
  0x925e, 0x926c, 0x926d, 0x923f, 0x9460, 0x9230, 0x9249, 0x9248,
  0x924d, 0x922e, 0x9239, 0x9438, 0x92ac, 0x92a0, 0x927a, 0x92aa,
  0x92ee, 0x92cf, 0x9403, 0x92e3, 0x943a, 0x92b1, 0x92a6, 0x93a7,
  0x9296, 0x92cc, 0x92a9, 0x93f5, 0x9293, 0x927f,
  /* 0x6f */
  0x93a9, 0x929a, 0x931a, 0x92ab, 0x9283, 0x940b, 0x92a8, 0x92a3,
  0x9412, 0x9338, 0x92f1, 0x93d7, 0x92e5, 0x92f0, 0x92ef, 0x92e8,
  0x92bc, 0x92dd, 0x92f6, 0x9426, 0x9427, 0x92c3, 0x92df, 0x92e6,
  0x9312, 0x9306, 0x9369, 0x931b, 0x9340, 0x9301, 0x9315, 0x932e,
  0x9343, 0x9307, 0x9308, 0x931f, 0x9319, 0x9365, 0x9347, 0x9376,
  0x9354, 0x9364, 0x93aa, 0x9370, 0x9384, 0x93e4, 0x93d8, 0x9428,
  0x9387, 0x93cc, 0x9398, 0x93b8, 0x93bf, 0x93a6, 0x93b0, 0x93b5,
  0x944c, 0x93e2, 0x93dc, 0x93dd, 0x93cd, 0x93de, 0x93c3, 0x93c7,
  0x93d1, 0x9414, 0x941d, 0x93f7, 0x9465, 0x9413, 0x946d, 0x9420,
  0x9479, 0x93f9, 0x9419, 0x944a, 0x9432, 0x943f, 0x9454, 0x9463,
  0x937e, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd,
  0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd,
  /* 0x70 */
  0xfffd, 0xfffd, 0x7a61, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd,
  0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0x9ce9, 0x9cf6,
  0x9d07, 0x9d06, 0x9d23, 0x9d87, 0x9e15, 0x9d1d, 0x9d1f, 0x9de5,
  0x9d2f, 0x9dd9, 0x9d30, 0x9d42, 0x9e1e, 0x9d53, 0x9e1d, 0x9d60,
  0x9d52, 0x9df3, 0x9d5c, 0x9d61, 0x9d93, 0x9d6a, 0x9d6f, 0x9d89,
  0x9d98, 0x9d9a, 0x9dc0, 0x9da5, 0x9da9, 0x9dc2, 0x9dbc, 0x9e1a,
  0x9dd3, 0x9dda, 0x9def, 0x9de6, 0x9df2, 0x9df8, 0x9e0c, 0x9dfa,
  0x9e1b, 0xfffd, 0xfffd, 0x7664, 0x7658, 0xfffd, 0x7667, 0xfffd,
  0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd,
  0x7602, 0xfffd, 0xfffd, 0x7646, 0xfffd, 0xfffd, 0x7647, 0xfffd,
  0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0x7649, 0xfffd,
  0x761e, 0xfffd, 0xfffd, 0x763b, 0xfffd, 0xfffd,
  /* 0x71 */
  0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0x766d,
  0xfffd, 0xfffd, 0x766e, 0xfffd, 0xfffd, 0x7669, 0xfffd, 0xfffd,
  0xfffd, 0x7672, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd,
  0xfffd, 0xfffd, 0xfffd, 0x7ac7, 0xfffd, 0xfffd, 0xfffd, 0x7ab6,
  0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd,
  0x8960, 0xfffd, 0xfffd, 0xfffd, 0x8933, 0xfffd, 0x895d, 0x8947,
  0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd,
  0xfffd, 0x8938, 0xfffd, 0x8964, 0xfffd, 0xfffd, 0xfffd, 0xfffd,
  0xfffd, 0xfffd, 0xfffd, 0x76b8, 0xfffd, 0xfffd, 0xfffd, 0xfffd,
  0xfffd, 0xfffd, 0xfffd, 0x802e, 0xfffd, 0xfffd, 0x802c, 0xfffd,
  0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0x8079, 0xfffd,
  0x8075, 0xfffd, 0xfffd, 0x9807, 0x980e, 0x980f,
  /* 0x72 */
  0x9821, 0x981c, 0x6f41, 0x9826, 0x9837, 0x984e, 0x9853, 0x9873,
  0x9862, 0x9859, 0x9865, 0x986c, 0x9870, 0xfffd, 0xfffd, 0xfffd,
  0x87e3, 0x8806, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd,
  0x8706, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd,
  0xfffd, 0xfffd, 0x8823, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd,
  0x87f6, 0xfffd, 0xfffd, 0x86fa, 0x87ef, 0xfffd, 0x8784, 0xfffd,
  0xfffd, 0xfffd, 0x8810, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd,
  0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd,
  0xfffd, 0xfffd, 0xfffd, 0xfffd, 0x87c8, 0xfffd, 0xfffd, 0xfffd,
  0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0x8811, 0xfffd, 0xfffd,
  0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0x87bb, 0xfffd,
  0xfffd, 0xfffd, 0xfffd, 0xfffd, 0x87ce, 0xfffd,
  /* 0x73 */
  0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd,
  0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd,
  0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd,
  0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0x7f4c, 0xfffd,
  0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0x7be4, 0xfffd, 0x7b67,
  0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd,
  0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0x7c69, 0xfffd, 0xfffd,
  0x7bf3, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd,
  0xfffd, 0xfffd, 0xfffd, 0xfffd, 0x7c00, 0x7bcb, 0xfffd, 0xfffd,
  0xfffd, 0x7c5c, 0xfffd, 0x7c1e, 0xfffd, 0xfffd, 0x7c2b, 0xfffd,
  0x7c23, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd,
  0xfffd, 0xfffd, 0xfffd, 0xfffd, 0x7c6a, 0xfffd,
  /* 0x74 */
  0xfffd, 0xfffd, 0xfffd, 0xfffd, 0x7c5f, 0xfffd, 0xfffd, 0xfffd,
  0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0x8264, 0xfffd,
  0xfffd, 0xfffd, 0xfffd, 0xfffd, 0x826b, 0xfffd, 0xfffd, 0xfffd,
  0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd,
  0x88ca, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0x7fa5, 0xfffd,
  0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0x7cf2, 0x7cf6,
  0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0x7cdd, 0xfffd, 0xfffd,
  0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd,
  0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd,
  0xfffd, 0x7e36, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0x9ea9, 0xfffd,
  0xfffd, 0xfffd, 0xfffd, 0xfffd, 0x8db2, 0xfffd, 0xfffd, 0xfffd,
  0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd,
  /* 0x75 */
  0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0x91c5, 0x91c3, 0xfffd,
  0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd,
  0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd,
  0xfffd, 0x9e7a, 0x8e89, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd,
  0xfffd, 0xfffd, 0xfffd, 0x8e4c, 0xfffd, 0xfffd, 0xfffd, 0x8e92,
  0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0x8e7a, 0x8e55, 0xfffd,
  0x8e9a, 0x8e8b, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd,
  0x8e93, 0xfffd, 0xfffd, 0x8e91, 0xfffd, 0xfffd, 0xfffd, 0xfffd,
  0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0x8ea1, 0x8e63, 0xfffd,
  0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0x8eaa, 0xfffd,
  0xfffd, 0x8ea6, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd,
  0xfffd, 0xfffd, 0xfffd, 0x89f4, 0xfffd, 0xfffd,
  /* 0x76 */
  0xfffd, 0xfffd, 0x89f6, 0xfffd, 0xfffd, 0x975a, 0xfffd, 0x9742,
  0xfffd, 0xfffd, 0x973d, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0x9744,
  0xfffd, 0xfffd, 0x9f54, 0x9f5f, 0x9f59, 0x9f60, 0x9f5c, 0x9f66,
  0x9f6c, 0x9f6a, 0x9f77, 0x9efd, 0x9eff, 0x9f09, 0xfffd, 0xfffd,
  0xfffd, 0xfffd, 0xfffd, 0xfffd, 0x8b8e, 0xfffd, 0x947e, 0xfffd,
  0x93e8, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0x9b77, 0x9b74,
  0x9b81, 0x9b83, 0x9b8e, 0x9c78, 0x7a4c, 0x9b92, 0x9c5f, 0x9b90,
  0x9bad, 0x9b9a, 0x9baa, 0x9b9e, 0x9c6d, 0x9bab, 0x9b9d, 0x9c58,
  0x9bc1, 0x9c7a, 0x9c31, 0x9c39, 0x9c23, 0x9c37, 0x9bc0, 0x9bca,
  0x9bc7, 0x9bfd, 0x9bd6, 0x9bea, 0x9beb, 0x9be1, 0x9be4, 0x9be7,
  0x9bdd, 0x9be2, 0x9bf0, 0x9bdb, 0x9bf4, 0x9bd4, 0x9c5d, 0x9c08,
  0x9c10, 0x9c0d, 0x9c12, 0x9c09, 0x9bff, 0x9c20,
  /* 0x77 */
  0x9c32, 0x9c2d, 0x9c28, 0x9c25, 0x9c29, 0x9c33, 0x9c3e, 0x9c48,
  0x9c3b, 0x9c35, 0x9c45, 0x9c56, 0x9c54, 0x9c52, 0x9c67, 0xfffd,
  0xfffd, 0x97c3, 0x97bd, 0xfffd, 0x97c9, 0xfffd, 0xfffd, 0xfffd,
  0xfffd, 0xfffd, 0xfffd, 0xfffd, 0x9dbb, 0xfffd, 0xfffd, 0xfffd,
  0xfffd, 0xfffd, 0x9acf, 0xfffd, 0x9ad6, 0x9ad5, 0xfffd, 0xfffd,
  0xfffd, 0x9b58, 0x9b4e, 0xfffd, 0xfffd, 0xfffd, 0x9957, 0x995c,
  0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd,
  0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0x9b22, 0xfffd, 0xfffd,
  0x4e48, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd,
  0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd,
  0xfffd, 0x9ef7, 0xfffd, 0xfffd, 0x9ef2, 0xfffd, 0xfffd, 0xfffd,
  0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd,
  /* 0x78 */
  0x896c, 0x95c6, 0x9336, 0x5f46, 0x8514, 0x7e94, 0x5382, 0x51b2,
  0x4e11, 0x9f63, 0x5679, 0x515a, 0x6dc0, 0x9f15, 0x6597, 0x5641,
  0x9aee, 0x8303, 0x4e30, 0x8907, 0x5e72, 0x7a40, 0x98b3, 0x5e7f,
  0x95a4, 0x9b0d, 0x5212, 0x8ff4, 0x5f59, 0x7a6b, 0x98e2, 0x51e0,
  0x50a2, 0x4ef7, 0x8350, 0x8591, 0x5118, 0x636e, 0x6372, 0x524b,
  0x5938, 0x774f, 0x8721, 0x814a, 0x7e8d, 0x91cc, 0x66c6, 0x5e18,
  0x77ad, 0x9e75, 0x56c9, 0x9ef4, 0x6fdb, 0x61de, 0x77c7, 0x7030,
  0x9eb5, 0x884a, 0x95e2, 0x82f9, 0x51ed, 0x6251, 0x4ec6, 0x6734,
  0x97c6, 0x7c64, 0x7e34, 0x97a6, 0x9eaf, 0x786e, 0x820d, 0x672f,
  0x677e, 0x56cc, 0x53f0, 0x98b1, 0x6aaf, 0x7f4e, 0x6d82, 0x7cf0,
  0x4e07, 0x4fc2, 0x7e6b, 0x9e79, 0x56ae, 0x9b1a, 0x846f, 0x53f6,
  0x90c1, 0x79a6, 0x7c72, 0x613f, 0x4e91, 0x9ad2,
  /* 0x79 */
  0x75c7, 0x96bb, 0x53ea, 0x7dfb, 0x88fd, 0x79cd, 0x7843, 0x7b51,
  0x51c6,
};

static int
gb12345ext_mbtowc (conv_t conv, ucs4_t *pwc, const unsigned char *s, int n)
{
  unsigned char c1 = s[0];
  if ((c1 == 0x21) || (c1 == 0x26) || (c1 == 0x28) || (c1 >= 0x30 && c1 <= 0x79)) {
    if (n >= 2) {
      unsigned char c2 = s[1];
      if (c2 >= 0x21 && c2 < 0x7f) {
        unsigned int i = 94 * (c1 - 0x21) + (c2 - 0x21);
        unsigned short wc = 0xfffd;
        if (i < 470) {
          if (i < 12)
            wc = gb12345ext_2uni_page21[i];
        } else if (i < 658) {
          if (i < 555)
            wc = gb12345ext_2uni_page26[i-470];
        } else if (i < 1410) {
          if (i < 690)
            wc = gb12345ext_2uni_page28[i-658];
        } else {
          if (i < 8281)
            wc = gb12345ext_2uni_page30[i-1410];
        }
        if (wc != 0xfffd) {
          *pwc = (ucs4_t) wc;
          return 2;
        }
      }
      return RET_ILSEQ;
    }
    return RET_TOOFEW(0);
  }
  return RET_ILSEQ;
}

static const unsigned short gb12345ext_2charset[2252] = {
  0x283d, 0x283e, 0x283f, 0x283b, 0x2840, 0x283c, 0x212c, 0x7871,
  0x7829, 0x7833, 0x7761, 0x4252, 0x787d, 0x5147, 0x785f, 0x7842,
  0x6245, 0x4034, 0x4258, 0x7872, 0x4f40, 0x5876, 0x4129, 0x3256,
  0x3876, 0x4347, 0x4257, 0x4e30, 0x3260, 0x556c, 0x5877, 0x4921,
  0x3138, 0x7841, 0x5336, 0x342b, 0x5871, 0x552e, 0x494b, 0x4763,
  0x594d, 0x3d76, 0x595d, 0x4748, 0x464d, 0x4e31, 0x3d44, 0x5947,
  0x3c5b, 0x5247, 0x592f, 0x525a, 0x3f6b, 0x3c73, 0x594f, 0x5931,
  0x592d, 0x7845, 0x3325, 0x5345, 0x3422, 0x5933, 0x5950, 0x594e,
  0x5932, 0x3679, 0x782c, 0x413d, 0x7828, 0x7929, 0x3633, 0x7840,
  0x785d, 0x3f2d, 0x783b, 0x5859, 0x5472, 0x7848, 0x3855, 0x3950,
  0x585c, 0x3434, 0x3b2e, 0x3e67, 0x4175, 0x3974, 0x585b, 0x3d23,
  0x3c41, 0x3e22, 0x362f, 0x4e71, 0x512b, 0x4a24, 0x404d, 0x4a46,
  0x5b3d, 0x4078, 0x4830, 0x5850, 0x3b63, 0x5851, 0x4778, 0x502d,
  0x7827, 0x5847, 0x325e, 0x5161, 0x4077, 0x5849, 0x324e, 0x3454,
  0x7923, 0x786b, 0x7878, 0x6161, 0x5f43, 0x5431, 0x5f42, 0x4e4a,
  0x4674, 0x5146, 0x4925, 0x4747, 0x3525, 0x5334, 0x473a, 0x5844,
  0x4270, 0x4e58, 0x5f6f, 0x5f59, 0x4c3e, 0x6036, 0x453b, 0x5f75,
  0x3322, 0x5f69, 0x3b29, 0x5f6b, 0x5025, 0x5f34, 0x5f58, 0x5f3c,
  0x7830, 0x5f50, 0x5f55, 0x5f66, 0x5f5c, 0x6048, 0x5f60, 0x4567,
  0x3656, 0x782b, 0x5f4c, 0x4f45, 0x5f62, 0x6060, 0x4476, 0x5f3f,
  0x417c, 0x7875, 0x6037, 0x514f, 0x6053, 0x5f79, 0x603f, 0x4f79,
  0x5966, 0x5f3d, 0x7853, 0x786a, 0x5676, 0x6070, 0x397a, 0x4e27,
  0x5430, 0x5432, 0x4d3c, 0x4d45, 0x5b6b, 0x5634, 0x3c61, 0x5b51,
  0x5b71, 0x5b76, 0x5222, 0x3128, 0x3321, 0x3f69, 0x5c63, 0x5b6e,
  0x5b75, 0x4d3f, 0x4e6b, 0x5b77, 0x333e, 0x4735, 0x3566, 0x5739,
  0x3669, 0x3758, 0x473d, 0x3f51, 0x4c33, 0x5139, 0x405d, 0x5b5b,
  0x5b64, 0x3b35, 0x4222, 0x5b62, 0x5b5e, 0x3053, 0x5733, 0x3a78,
  0x4a59, 0x434e, 0x7849, 0x3c50, 0x5e46, 0x3661, 0x3d31, 0x375c,
  0x5731, 0x4226, 0x383e, 0x662b, 0x6634, 0x4268, 0x657d, 0x657c,
  0x6635, 0x6623, 0x662c, 0x663f, 0x3d3f, 0x664d, 0x6648, 0x6649,
  0x5324, 0x4974, 0x662e, 0x4b6f, 0x5127, 0x424f, 0x475e, 0x4a35,
  0x447e, 0x4973, 0x5034, 0x3f6d, 0x3368, 0x3126, 0x3d2b, 0x5728,
  0x5130, 0x3654, 0x353c, 0x5e4f, 0x4245, 0x3263, 0x6570, 0x4a74,
  0x3854, 0x612d, 0x353a, 0x4f3f, 0x6141, 0x385a, 0x6134, 0x6130,
  0x6150, 0x5538, 0x612b, 0x6140, 0x613d, 0x613b, 0x6149, 0x416b,
  0x536c, 0x3f79, 0x424d, 0x615b, 0x5b4f, 0x7850, 0x4b27, 0x4a26,
  0x554a, 0x3478, 0x5621, 0x6078, 0x607e, 0x607d, 0x5644, 0x3152,
  0x306f, 0x607c, 0x7835, 0x3849, 0x3c38, 0x7838, 0x3f62, 0x436d,
  0x3327, 0x6250, 0x374f, 0x3963, 0x422e, 0x4c7c, 0x6572, 0x5545,
  0x7824, 0x352f, 0x4356, 0x4d64, 0x783d, 0x3a73, 0x3e36, 0x3453,
  0x6162, 0x3834, 0x3339, 0x626a, 0x4346, 0x3671, 0x4455, 0x6322,
  0x627c, 0x302e, 0x632b, 0x626b, 0x627d, 0x6269, 0x787c, 0x4c2c,
  0x3252, 0x3251, 0x627a, 0x395f, 0x6d28, 0x6266, 0x4b4b, 0x4247,
  0x6325, 0x476c, 0x5347, 0x3139, 0x412f, 0x463e, 0x6334, 0x352c,
  0x375f, 0x4375, 0x6264, 0x4f5c, 0x5264, 0x3f52, 0x5326, 0x6278,
  0x7856, 0x6d21, 0x6d2f, 0x627b, 0x334d, 0x4041, 0x3b33, 0x507c,
  0x6263, 0x3e65, 0x4965, 0x4135, 0x6d30, 0x6a27, 0x6a28, 0x553d,
  0x4f37, 0x785e, 0x502e, 0x4961, 0x5e51, 0x7846, 0x7847, 0x4928,
  0x4255, 0x3c70, 0x516f, 0x3b53, 0x4b70, 0x3537, 0x4740, 0x5e62,
  0x5e68, 0x4227, 0x563f, 0x3f59, 0x5e52, 0x3274, 0x404c, 0x4453,
  0x5e58, 0x3527, 0x3226, 0x3827, 0x464b, 0x5e6c, 0x4c22, 0x4e4e,
  0x3c71, 0x5335, 0x4230, 0x5471, 0x3b77, 0x3532, 0x3523, 0x3e5d,
  0x3c37, 0x4462, 0x3177, 0x4521, 0x3869, 0x5640, 0x4029, 0x5f22,
  0x305a, 0x4b53, 0x5f23, 0x4845, 0x5e73, 0x446c, 0x4223, 0x4039,
  0x5e7c, 0x3273, 0x5f25, 0x4963, 0x545c, 0x424e, 0x4c2f, 0x3d41,
  0x403f, 0x305c, 0x3550, 0x4a7d, 0x4132, 0x3150, 0x6c35, 0x782f,
  0x5536, 0x364f, 0x4a31, 0x5667, 0x544e, 0x6a4d, 0x3329, 0x545d,
  0x6a4a, 0x784f, 0x6a3c, 0x4f7e, 0x6a53, 0x3f75, 0x4939, 0x4a69,
  0x3b61, 0x6b4a, 0x7868, 0x7860, 0x362b, 0x7869, 0x6845, 0x4c75,
  0x6849, 0x6847, 0x5466, 0x3630, 0x553b, 0x6862, 0x516e, 0x3763,
  0x6865, 0x5235, 0x3c2b, 0x683f, 0x4859, 0x6867, 0x3939, 0x4739,
  0x687d, 0x3d30, 0x572e, 0x4056, 0x6848, 0x4225, 0x316a, 0x4a60,
  0x5179, 0x4653, 0x4a77, 0x686b, 0x6863, 0x4745, 0x3b7a, 0x4d56,
  0x685f, 0x3535, 0x686d, 0x3c6c, 0x6949, 0x786d, 0x6944, 0x447b,
  0x3c77, 0x3971, 0x6956, 0x6935, 0x684e, 0x687c, 0x695a, 0x685d,
  0x6946, 0x6853, 0x6840, 0x6934, 0x6850, 0x6937, 0x5323, 0x4038,
  0x4828, 0x6921, 0x686f, 0x692d, 0x6879, 0x4755, 0x4537, 0x6c23,
  0x3b36, 0x4b6a, 0x407a, 0x3969, 0x3250, 0x6966, 0x6964, 0x6969,
  0x6967, 0x696b, 0x3c5f, 0x4931, 0x3f47, 0x4539, 0x6b27, 0x5531,
  0x6b2a, 0x4678, 0x4762, 0x6b32, 0x6424, 0x786f, 0x637e, 0x782d,
  0x4259, 0x5428, 0x6435, 0x4733, 0x4e50, 0x3262, 0x3b6b, 0x6425,
  0x4c40, 0x573c, 0x3935, 0x3257, 0x4370, 0x3553, 0x5c7e, 0x3b26,
  0x564d, 0x4978, 0x4231, 0x6430, 0x427a, 0x5366, 0x453d, 0x3a3a,
  0x4130, 0x5755, 0x5547, 0x3d25, 0x3d2c, 0x7223, 0x4643, 0x3d60,
  0x636d, 0x4873, 0x6431, 0x4023, 0x6464, 0x6436, 0x492c, 0x3d3d,
  0x4054, 0x3d27, 0x6445, 0x5473, 0x6d34, 0x642b, 0x356d, 0x5747,
  0x4528, 0x4a2a, 0x4522, 0x7855, 0x3c43, 0x4c4e, 0x4044, 0x4e2b,
  0x3175, 0x3d26, 0x6378, 0x424b, 0x645e, 0x6442, 0x503a, 0x6449,
  0x642f, 0x3174, 0x6372, 0x4124, 0x646c, 0x646b, 0x6371, 0x647e,
  0x7858, 0x6472, 0x403d, 0x6363, 0x645c, 0x4877, 0x406c, 0x4c32,
  0x6530, 0x4d65, 0x4250, 0x6459, 0x4e5a, 0x4c7e, 0x4e5e, 0x4136,
  0x6c3f, 0x5c64, 0x3733, 0x6c3e, 0x532b, 0x6c41, 0x4848, 0x3363,
  0x6c47, 0x3546, 0x4955, 0x4c4c, 0x6c4b, 0x532a, 0x3253, 0x5672,
  0x3b62, 0x3d7d, 0x6c62, 0x4b38, 0x422f, 0x4043, 0x4e2a, 0x522f,
  0x367b, 0x6b39, 0x4723, 0x5c7d, 0x363f, 0x4e7e, 0x5734, 0x4f41,
  0x3137, 0x534c, 0x6178, 0x616f, 0x537c, 0x4a28, 0x3640, 0x6176,
  0x617d, 0x447c, 0x3b71, 0x4154, 0x616e, 0x4a5e, 0x4c21, 0x4f57,
  0x6228, 0x6224, 0x4f56, 0x6775, 0x6762, 0x4b76, 0x5328, 0x426a,
  0x6776, 0x6761, 0x6828, 0x3b37, 0x6774, 0x476d, 0x6767, 0x682c,
  0x6836, 0x6a31, 0x327a, 0x4436, 0x314f, 0x3b2d, 0x3531, 0x336b,
  0x7921, 0x3e37, 0x7069, 0x3768, 0x5171, 0x7079, 0x342f, 0x4531,
  0x707c, 0x4146, 0x706c, 0x706f, 0x7077, 0x705d, 0x3171, 0x5177,
  0x705c, 0x5622, 0x705f, 0x712e, 0x5122, 0x7128, 0x712b, 0x5338,
  0x4c31, 0x7132, 0x3722, 0x3028, 0x7164, 0x5665, 0x5535, 0x3e21,
  0x3c60, 0x454c, 0x422c, 0x784a, 0x6d79, 0x6d6e, 0x4277, 0x7851,
  0x6d7a, 0x7857, 0x5675, 0x3d43, 0x7927, 0x6d4c, 0x6d3a, 0x7866,
  0x5162, 0x4b36, 0x6d38, 0x6d3f, 0x4837, 0x426b, 0x5729, 0x6d57,
  0x6d53, 0x6d36, 0x6d4d, 0x3421, 0x302d, 0x3f73, 0x6d42, 0x4079,
  0x372f, 0x6d43, 0x3b76, 0x6c75, 0x787a, 0x6c78, 0x4071, 0x6c72,
  0x353b, 0x7926, 0x5656, 0x3346, 0x7836, 0x7655, 0x3b7d, 0x5331,
  0x7023, 0x3b60, 0x4e48, 0x783e, 0x4e51, 0x4d5d, 0x476e, 0x7140,
  0x3f7a, 0x345c, 0x474f, 0x713c, 0x546e, 0x4754, 0x4a7a, 0x3e3a,
  0x314a, 0x7928, 0x7348, 0x3c63, 0x3d5a, 0x3736, 0x567e, 0x7366,
  0x7346, 0x4938, 0x7359, 0x7365, 0x4228, 0x736c, 0x3c72, 0x7371,
  0x736f, 0x4729, 0x4131, 0x403a, 0x336f, 0x736a, 0x7425, 0x417d,
  0x7862, 0x7356, 0x737d, 0x4069, 0x4261, 0x787b, 0x7456, 0x3760,
  0x4138, 0x7870, 0x744f, 0x5961, 0x7450, 0x6679, 0x3e40, 0x3c4d,
  0x667b, 0x543c, 0x3a6c, 0x667a, 0x667c, 0x667d, 0x4852, 0x4e46,
  0x4449, 0x4526, 0x6723, 0x343f, 0x6722, 0x4934, 0x563d, 0x3c36,
  0x3757, 0x6721, 0x3744, 0x4f38, 0x6726, 0x6725, 0x4970, 0x495c,
  0x6724, 0x6728, 0x672a, 0x6729, 0x5655, 0x5769, 0x306d, 0x672c,
  0x3d61, 0x672b, 0x3d4a, 0x4267, 0x5124, 0x3878, 0x485e, 0x4d33,
  0x4b3f, 0x672d, 0x3e78, 0x3e6e, 0x3073, 0x672f, 0x672e, 0x6730,
  0x5065, 0x4b67, 0x3e2d, 0x575b, 0x6736, 0x3371, 0x6739, 0x4f5f,
  0x6737, 0x4e2c, 0x673a, 0x3859, 0x4d78, 0x3141, 0x573a, 0x425a,
  0x6738, 0x6732, 0x5540, 0x3442, 0x6731, 0x4360, 0x6735, 0x673b,
  0x3d74, 0x6733, 0x424c, 0x5077, 0x6734, 0x673d, 0x3c6a, 0x673c,
  0x3c29, 0x3650, 0x355e, 0x6745, 0x5435, 0x6741, 0x3160, 0x3b3a,
  0x4365, 0x4e33, 0x6743, 0x673f, 0x4137, 0x6742, 0x673e, 0x7924,
  0x5d53, 0x6746, 0x674b, 0x6744, 0x6727, 0x674c, 0x383f, 0x6747,
  0x6749, 0x6748, 0x4f58, 0x4c50, 0x376c, 0x674a, 0x4b75, 0x575d,
  0x6750, 0x7863, 0x674f, 0x746a, 0x4246, 0x674e, 0x575c, 0x3c28,
  0x6752, 0x6751, 0x6755, 0x562f, 0x4949, 0x6754, 0x4846, 0x6740,
  0x497e, 0x3b66, 0x7873, 0x3c6b, 0x6756, 0x6759, 0x6758, 0x3d49,
  0x526f, 0x3c4c, 0x674d, 0x6757, 0x6753, 0x667e, 0x5078, 0x784d,
  0x3278, 0x5327, 0x7826, 0x4f4b, 0x675a, 0x4042, 0x733f, 0x786e,
  0x3723, 0x3055, 0x425e, 0x6e3c, 0x6e3f, 0x7447, 0x5265, 0x4f30,
  0x474c, 0x716f, 0x716c, 0x4a25, 0x4e45, 0x412a, 0x344f, 0x4979,
  0x4b4a, 0x7179, 0x4474, 0x5630, 0x7177, 0x4c7d, 0x417b, 0x4b60,
  0x5032, 0x6b56, 0x554d, 0x784c, 0x4976, 0x6b4b, 0x6b61, 0x4454,
  0x5657, 0x3326, 0x3774, 0x3d3a, 0x4465, 0x3528, 0x6b5a, 0x4527,
  0x4133, 0x466a, 0x6b77, 0x4030, 0x6b4d, 0x5460, 0x5975, 0x4159,
  0x4c28, 0x536b, 0x504b, 0x3e59, 0x3e49, 0x7867, 0x3255, 0x742f,
  0x3d22, 0x7435, 0x3c68, 0x515e, 0x5b3b, 0x5c51, 0x785c, 0x7832,
  0x7843, 0x572f, 0x3e25, 0x3c54, 0x5c48, 0x3b2a, 0x5c49, 0x4033,
  0x4d72, 0x5d2b, 0x5236, 0x5d26, 0x5d27, 0x4e2d, 0x7877, 0x3b67,
  0x5d3b, 0x5d2a, 0x3254, 0x5d25, 0x3847, 0x412b, 0x5c4a, 0x5c6a,
  0x7825, 0x5d64, 0x3d2f, 0x5c60, 0x5271, 0x5d21, 0x5d5b, 0x5c71,
  0x5d24, 0x5c3f, 0x5d35, 0x5c69, 0x5d5e, 0x3534, 0x4e5f, 0x4f74,
  0x5d77, 0x5c76, 0x3c3b, 0x5c3c, 0x7844, 0x473e, 0x5d32, 0x3c76,
  0x4878, 0x5c79, 0x4036, 0x5d23, 0x5255, 0x5229, 0x5e34, 0x544c,
  0x5c42, 0x302a, 0x5d7e, 0x5e2d, 0x422b, 0x4b55, 0x463b, 0x5e3a,
  0x5d7c, 0x5c57, 0x403c, 0x5d71, 0x425c, 0x3426, 0x4232, 0x3a45,
  0x3f77, 0x724c, 0x7239, 0x784b, 0x4a34, 0x4f3a, 0x4e4f, 0x724f,
  0x426c, 0x5329, 0x7277, 0x555d, 0x7265, 0x727d, 0x7231, 0x3275,
  0x724d, 0x3366, 0x7249, 0x524f, 0x532c, 0x7232, 0x7253, 0x726e,
  0x402f, 0x7243, 0x3946, 0x324f, 0x4279, 0x565a, 0x785a, 0x4a75,
  0x4e40, 0x3365, 0x563b, 0x7441, 0x406f, 0x3239, 0x5730, 0x7925,
  0x7834, 0x3f63, 0x714d, 0x715a, 0x5974, 0x7150, 0x3040, 0x714f,
  0x7149, 0x715c, 0x4d60, 0x7821, 0x3344, 0x4f2e, 0x3c7b, 0x3966,
  0x4359, 0x4a53, 0x6a68, 0x6a6a, 0x6a6c, 0x4757, 0x6a69, 0x6a6d,
  0x6a6e, 0x6a6f, 0x3e75, 0x4040, 0x6a6b, 0x395b, 0x757c, 0x7623,
  0x3425, 0x5a25, 0x3629, 0x383c, 0x3c46, 0x5136, 0x5a27, 0x4c56,
  0x5a26, 0x5135, 0x5a28, 0x467d, 0x3c47, 0x366f, 0x5148, 0x4b4f,
  0x3e77, 0x5a2b, 0x3743, 0x4968, 0x506d, 0x4b5f, 0x5a2d, 0x556f,
  0x5a2c, 0x5a2e, 0x5a2a, 0x5529, 0x5a31, 0x5a2f, 0x4640, 0x5a30,
  0x5767, 0x344a, 0x5a3c, 0x512f, 0x5268, 0x4a54, 0x4a2b, 0x326f,
  0x5a38, 0x396e, 0x5a39, 0x5a35, 0x3b30, 0x3843, 0x4f6a, 0x5a37,
  0x5a36, 0x5a34, 0x5a33, 0x566f, 0x5a32, 0x3f64, 0x484f, 0x5a3f,
  0x5a40, 0x352e, 0x5355, 0x5a3d, 0x536f, 0x334f, 0x3d6b, 0x4e5c,
  0x4e73, 0x5a3e, 0x4b50, 0x3b65, 0x4b35, 0x4b2d, 0x3f4e, 0x5a47,
  0x374c, 0x526a, 0x3577, 0x5a46, 0x573b, 0x4c38, 0x5a43, 0x476b,
  0x5a3a, 0x5a41, 0x5a42, 0x4142, 0x425b, 0x5a45, 0x5a44, 0x357d,
  0x5a52, 0x5a3b, 0x5a4c, 0x5a50, 0x5033, 0x5a49, 0x5a4d, 0x5a51,
  0x3b64, 0x5a4f, 0x5a48, 0x376d, 0x566e, 0x5168, 0x5a4e, 0x4535,
  0x4431, 0x5a4b, 0x4e3d, 0x4c5c, 0x565f, 0x3b51, 0x4355, 0x5a57,
  0x5a4a, 0x5a55, 0x3079, 0x472b, 0x5a56, 0x3d32, 0x503b, 0x5225,
  0x5a53, 0x5a58, 0x437d, 0x5a59, 0x5a29, 0x3d77, 0x4321, 0x5624,
  0x5a5c, 0x3c25, 0x5a5a, 0x4a36, 0x5a5b, 0x4c37, 0x4657, 0x5a5e,
  0x526b, 0x5269, 0x4734, 0x3b24, 0x537e, 0x3641, 0x3164, 0x7645,
  0x3277, 0x4843, 0x403e, 0x5a5f, 0x5a54, 0x5a5d, 0x4671, 0x3761,
  0x3134, 0x556a, 0x383a, 0x3246, 0x3931, 0x4636, 0x3b75, 0x3737,
  0x4c30, 0x3961, 0x5470, 0x567c, 0x6a5b, 0x6a5f, 0x3721, 0x3973,
  0x3161, 0x4272, 0x347b, 0x6a5c, 0x3751, 0x4c79, 0x6a5d, 0x4333,
  0x3a58, 0x6a5a, 0x4238, 0x415e, 0x3b5f, 0x6a60, 0x574a, 0x3c56,
  0x5474, 0x6a62, 0x495e, 0x3176, 0x6a64, 0x6a63, 0x344d, 0x494d,
  0x4562, 0x6259, 0x4f4d, 0x4274, 0x3c7a, 0x3833, 0x6a66, 0x564a,
  0x6a65, 0x554b, 0x3644, 0x4035, 0x572c, 0x6a67, 0x393a, 0x487c,
  0x5853, 0x6a5e, 0x5738, 0x5479, 0x545e, 0x584d, 0x4944, 0x532e,
  0x6a61, 0x4a6a, 0x3853, 0x545f, 0x384f, 0x5554, 0x4777, 0x7475,
  0x3c79, 0x533b, 0x7544, 0x754f, 0x7567, 0x754e, 0x753b, 0x336c,
  0x7552, 0x543e, 0x755c, 0x7548, 0x7559, 0x7551, 0x7566, 0x345a,
  0x7572, 0x756f, 0x477b, 0x3335, 0x547e, 0x396c, 0x3e7c, 0x5079,
  0x696d, 0x696e, 0x486d, 0x6975, 0x6974, 0x696f, 0x5661, 0x6972,
  0x6977, 0x6970, 0x6973, 0x6978, 0x3d4f, 0x697b, 0x697a, 0x5458,
  0x6979, 0x697c, 0x3828, 0x4761, 0x413e, 0x6a22, 0x3b54, 0x697e,
  0x6a21, 0x3975, 0x697d, 0x3132, 0x4256, 0x3c2d, 0x6a23, 0x4a64,
  0x3778, 0x5537, 0x535f, 0x6c31, 0x4f3d, 0x542f, 0x6a24, 0x572a,
  0x555e, 0x3d4e, 0x6a25, 0x3a64, 0x604e, 0x6976, 0x6971, 0x306c,
  0x3447, 0x3168, 0x3167, 0x4529, 0x783c, 0x6549, 0x5562, 0x412c,
  0x3d78, 0x544b, 0x397d, 0x346f, 0x4e25, 0x5137, 0x355d, 0x5436,
  0x4a4a, 0x3359, 0x4728, 0x5121, 0x5245, 0x4149, 0x4275, 0x3b39,
  0x6547, 0x315f, 0x425f, 0x654e, 0x7879, 0x5b23, 0x534a, 0x5b29,
  0x4f67, 0x575e, 0x5a79, 0x5447, 0x354b, 0x5623, 0x415a, 0x3526,
  0x5a7e, 0x5b26, 0x5a77, 0x5b2a, 0x544d, 0x3373, 0x523d, 0x3d34,
  0x4470, 0x5046, 0x7527, 0x7526, 0x4a4d, 0x784e, 0x6e44, 0x6e45,
  0x6e46, 0x6e49, 0x6e48, 0x3624, 0x6e47, 0x556b, 0x3576, 0x6e4c,
  0x6e4b, 0x3730, 0x6e4e, 0x6e4a, 0x6e4f, 0x4725, 0x6e59, 0x6e55,
  0x6e57, 0x6e50, 0x4446, 0x365b, 0x3933, 0x6e54, 0x6e53, 0x332e,
  0x4525, 0x3e7b, 0x3846, 0x6e58, 0x6e51, 0x6e56, 0x6e6a, 0x6e66,
  0x6e5d, 0x4165, 0x6e5c, 0x6e60, 0x6e6b, 0x6e5a, 0x6e5f, 0x534b,
  0x6e64, 0x3c58, 0x6e52, 0x6e68, 0x6e67, 0x6e69, 0x322c, 0x6e5e,
  0x472f, 0x432d, 0x4726, 0x6e61, 0x3227, 0x6e5b, 0x6e62, 0x6e63,
  0x3d42, 0x6e6f, 0x3875, 0x6e7e, 0x5278, 0x6f25, 0x4d2d, 0x4f33,
  0x6e7d, 0x6e79, 0x437a, 0x6f22, 0x4f4e, 0x6e6e, 0x6f28, 0x523f,
  0x6e77, 0x6f27, 0x6e7b, 0x6e70, 0x6f24, 0x6e6d, 0x6e76, 0x4f7a,
  0x5062, 0x4c60, 0x6f31, 0x4241, 0x6f36, 0x503f, 0x3135, 0x6e7a,
  0x6e72, 0x3766, 0x6f32, 0x6f37, 0x6e74, 0x337a, 0x6f2d, 0x6f38,
  0x6f30, 0x464c, 0x4871, 0x6e71, 0x6f2f, 0x6f2e, 0x6f2b, 0x6f33,
  0x3e62, 0x3856, 0x6f3e, 0x6f3a, 0x6f42, 0x6f43, 0x5736, 0x6f39,
  0x6f3f, 0x3438, 0x6f45, 0x6f23, 0x6f3c, 0x6f44, 0x3627, 0x472e,
  0x3d75, 0x432a, 0x4e7d, 0x6f40, 0x346d, 0x423c, 0x434c, 0x7823,
  0x6f2a, 0x6f3d, 0x4f47, 0x6f41, 0x6e4d, 0x6f47, 0x3978, 0x3646,
  0x6f49, 0x5521, 0x364d, 0x6f4a, 0x6f46, 0x6f3b, 0x4742, 0x6f4c,
  0x3c7c, 0x6f48, 0x5560, 0x6f71, 0x433e, 0x6f4d, 0x6f51, 0x3077,
  0x4b78, 0x6f53, 0x4e59, 0x5d76, 0x6f56, 0x6e78, 0x6f21, 0x6f4b,
  0x3864, 0x5572, 0x6f57, 0x4478, 0x6f58, 0x6f54, 0x6f55, 0x6f5f,
  0x6f60, 0x4134, 0x6f52, 0x6f5d, 0x6f61, 0x6f2c, 0x6f4f, 0x6f5b,
  0x6f5c, 0x6f5e, 0x3279, 0x3e35, 0x6f5a, 0x6f4e, 0x7649, 0x6e7c,
  0x6f64, 0x6f6a, 0x6e73, 0x6f26, 0x414d, 0x6f29, 0x6f66, 0x6f62,
  0x5653, 0x6f6b, 0x6f63, 0x6f68, 0x6f34, 0x6f35, 0x6f50, 0x412d,
  0x6f6d, 0x4058, 0x4c7a, 0x6e6c, 0x6e75, 0x6f6e, 0x567d, 0x6f6c,
  0x6f59, 0x3c78, 0x6f6f, 0x6e65, 0x6f70, 0x6f65, 0x6f67, 0x543f,
  0x4f62, 0x4477, 0x6f69, 0x4260, 0x576a, 0x7647, 0x5464, 0x3324,
  0x4345, 0x6345, 0x4941, 0x6346, 0x3155, 0x3f2a, 0x634a, 0x6348,
  0x4872, 0x4f50, 0x3c64, 0x6349, 0x5522, 0x3a52, 0x3873, 0x7839,
  0x3727, 0x396b, 0x4376, 0x634d, 0x634f, 0x634c, 0x5444, 0x6351,
  0x514b, 0x5156, 0x6355, 0x6354, 0x6350, 0x6353, 0x6356, 0x7822,
  0x6347, 0x402b, 0x6357, 0x403b, 0x6359, 0x6358, 0x635a, 0x3433,
  0x3958, 0x635b, 0x327b, 0x785b, 0x634b, 0x5a6a, 0x4942, 0x5573,
  0x5275, 0x3342, 0x423d, 0x5174, 0x3653, 0x3d57, 0x5449, 0x3c4a,
  0x4b66, 0x4f55, 0x527e, 0x4224, 0x4125, 0x7922, 0x4b64, 0x4b2b,
  0x337b, 0x5453, 0x406b, 0x4451, 0x5446, 0x3567, 0x4e6d, 0x762b,
  0x7628, 0x7630, 0x4169, 0x7626, 0x584c, 0x392e, 0x7864, 0x7733,
  0x7732, 0x7861, 0x7735, 0x4e24, 0x484d, 0x3a2b, 0x6838, 0x683a,
  0x6839, 0x4f6c, 0x5233, 0x3625, 0x476a, 0x4f6e, 0x4b33, 0x717c,
  0x506b, 0x676f, 0x4b4c, 0x717d, 0x717e, 0x5424, 0x4d67, 0x3064,
  0x3659, 0x4644, 0x416c, 0x7222, 0x7221, 0x5243, 0x7224, 0x4d37,
  0x3c55, 0x7225, 0x3e31, 0x4635, 0x4d47, 0x3f45, 0x4c62, 0x366e,
  0x7226, 0x7227, 0x5155, 0x5438, 0x722a, 0x355f, 0x4060, 0x7229,
  0x722b, 0x394b, 0x327c, 0x722c, 0x4f54, 0x722d, 0x422d, 0x7228,
  0x4827, 0x3767, 0x6c29, 0x6c2a, 0x786c, 0x7837, 0x6c2b, 0x6c2c,
  0x462e, 0x6c2d, 0x6c2e, 0x3749, 0x623b, 0x783f, 0x623d, 0x623f,
  0x6240, 0x6241, 0x3739, 0x527b, 0x6242, 0x4b47, 0x3125, 0x4a4e,
  0x3d48, 0x317d, 0x6243, 0x5178, 0x367c, 0x6244, 0x4459, 0x3676,
  0x5360, 0x6246, 0x3d24, 0x4f5a, 0x395d, 0x623c, 0x6247, 0x623e,
  0x4173, 0x6248, 0x6249, 0x4278, 0x624a, 0x624b, 0x624c, 0x4021,
  0x624d, 0x3c22, 0x4844, 0x774f, 0x7750, 0x3276, 0x624e, 0x426d,
  0x5426, 0x376b, 0x4d54, 0x335b, 0x5131, 0x3235, 0x5724, 0x6665,
  0x3e54, 0x6660, 0x3c5d, 0x6666, 0x6662, 0x4a3b, 0x4d55, 0x6661,
  0x426e, 0x6669, 0x3a27, 0x4266, 0x3f25, 0x3352, 0x666d, 0x666c,
  0x466f, 0x666b, 0x6670, 0x462d, 0x6539, 0x666f, 0x6672, 0x4c5a,
  0x6663, 0x4927, 0x6673, 0x4262, 0x5d6b, 0x6671, 0x666e, 0x6674,
  0x6675, 0x477d, 0x6668, 0x6667, 0x6676, 0x3d3e, 0x5169, 0x3e2a,
  0x6664, 0x5668, 0x423f, 0x6678, 0x6677, 0x666a, 0x3039, 0x7743,
  0x787e, 0x4c65, 0x7746, 0x7745, 0x7831, 0x4b49, 0x783a, 0x7876,
  0x775e, 0x3637, 0x4456, 0x6352, 0x634e, 0x5374, 0x774b, 0x774a,
  0x5363, 0x4233, 0x7650, 0x764f, 0x7651, 0x7652, 0x7653, 0x7658,
  0x312b, 0x7656, 0x765a, 0x765f, 0x765c, 0x765b, 0x765e, 0x7659,
  0x4f4a, 0x7667, 0x7661, 0x7669, 0x4070, 0x7668, 0x7676, 0x766b,
  0x7674, 0x7671, 0x766e, 0x7672, 0x766f, 0x7670, 0x3e28, 0x766c,
  0x766d, 0x7673, 0x7675, 0x766a, 0x767d, 0x7678, 0x767c, 0x767a,
  0x7679, 0x767b, 0x487a, 0x767e, 0x7665, 0x7724, 0x7723, 0x7725,
  0x7722, 0x7663, 0x7721, 0x7726, 0x772a, 0x7666, 0x7664, 0x7729,
  0x7727, 0x772b, 0x7728, 0x316e, 0x772e, 0x772d, 0x772c, 0x415b,
  0x7660, 0x7677, 0x7657, 0x772f, 0x765d, 0x7654, 0x7662, 0x4471,
  0x702f, 0x596c, 0x376f, 0x4379, 0x7030, 0x7032, 0x7031, 0x513b,
  0x4d52, 0x5427, 0x7036, 0x7037, 0x7033, 0x516c, 0x513c, 0x7039,
  0x703b, 0x3a68, 0x386b, 0x703c, 0x3e69, 0x7041, 0x703e, 0x7043,
  0x366c, 0x7040, 0x7044, 0x7046, 0x4574, 0x7047, 0x4835, 0x7034,
  0x7048, 0x7045, 0x7049, 0x704a, 0x704c, 0x704d, 0x5d3a, 0x3a57,
  0x773d, 0x704f, 0x704b, 0x704e, 0x3c26, 0x7051, 0x4538, 0x703a,
  0x7052, 0x7038, 0x7054, 0x7053, 0x7055, 0x7042, 0x7056, 0x5325,
  0x7058, 0x7057, 0x7035, 0x7050, 0x7059, 0x703f, 0x703d, 0x7852,
  0x7874, 0x753a, 0x3c6f, 0x514e, 0x4076, 0x4273, 0x746f, 0x7865,
  0x7859, 0x4334, 0x5964, 0x3563, 0x3533, 0x7775, 0x7854, 0x7772,
  0x763c, 0x763d, 0x763e, 0x782e, 0x466b, 0x552b, 0x6c34, 0x335d,
  0x7633, 0x7635, 0x7637, 0x7634, 0x7636, 0x4164, 0x782a, 0x7638,
  0x763a, 0x7639, 0x4823, 0x763b, 0x417a, 0x4553, 0x3928, 0x6d68,
  0x396a, 0x2672, 0x2674, 0x2675, 0x2660, 0x2661, 0x2670, 0x2671,
  0x2662, 0x2663, 0x266e, 0x266f, 0x2666, 0x2667, 0x2664, 0x2665,
  0x2668, 0x2669, 0x266a, 0x266b,
};

static const Summary16 gb12345ext_uni2indx_page01[23] = {
  /* 0x0100 */
  {    0, 0x0000 }, {    0, 0x0000 }, {    0, 0x0000 }, {    0, 0x0000 },
  {    0, 0x0110 }, {    2, 0x0000 }, {    2, 0x0000 }, {    2, 0x0000 },
  {    2, 0x0000 }, {    2, 0x0000 }, {    2, 0x0000 }, {    2, 0x0000 },
  {    2, 0x0000 }, {    2, 0x0000 }, {    2, 0x0000 }, {    2, 0x0200 },
  /* 0x0200 */
  {    3, 0x0000 }, {    3, 0x0000 }, {    3, 0x0000 }, {    3, 0x0000 },
  {    3, 0x0000 }, {    3, 0x0002 }, {    4, 0x0002 },
};
static const Summary16 gb12345ext_uni2indx_page1e[4] = {
  /* 0x1e00 */
  {    5, 0x0000 }, {    5, 0x0000 }, {    5, 0x0000 }, {    5, 0x8000 },
};
static const Summary16 gb12345ext_uni2indx_page22[3] = {
  /* 0x2200 */
  {    6, 0x0000 }, {    6, 0x0000 }, {    6, 0x0020 },
};
static const Summary16 gb12345ext_uni2indx_page4e[1306] = {
  /* 0x4e00 */
  {    7, 0x0080 }, {    8, 0x0002 }, {    9, 0x0000 }, {    9, 0x0001 },
  {   10, 0x0100 }, {   11, 0x0000 }, {   11, 0x0000 }, {   11, 0x0000 },
  {   11, 0x0004 }, {   12, 0x4002 }, {   14, 0x0000 }, {   14, 0x0000 },
  {   14, 0x0040 }, {   15, 0x0000 }, {   15, 0x0000 }, {   15, 0x0080 },
  /* 0x4f00 */
  {   16, 0x0000 }, {   16, 0x0000 }, {   16, 0x0000 }, {   16, 0x0000 },
  {   16, 0x0000 }, {   16, 0x0200 }, {   17, 0x0000 }, {   17, 0x0000 },
  {   17, 0x0040 }, {   18, 0x0040 }, {   19, 0x0000 }, {   19, 0x0000 },
  {   19, 0x0004 }, {   20, 0x0000 }, {   20, 0x0001 }, {   21, 0x0000 },
  /* 0x5000 */
  {   21, 0x0a41 }, {   25, 0x0002 }, {   26, 0x0800 }, {   27, 0x0000 },
  {   27, 0x0200 }, {   28, 0x0000 }, {   28, 0x0000 }, {   28, 0x0030 },
  {   30, 0x0000 }, {   30, 0x0340 }, {   33, 0x2004 }, {   35, 0x40b8 },
  {   40, 0x0224 }, {   43, 0x4022 }, {   46, 0x0120 }, {   48, 0x0200 },
  /* 0x5100 */
  {   49, 0x0315 }, {   54, 0x8131 }, {   59, 0x0400 }, {   60, 0x1c84 },
  {   65, 0x0000 }, {   65, 0x0404 }, {   67, 0x0200 }, {   68, 0x0000 },
  {   68, 0x0000 }, {   68, 0x0000 }, {   68, 0x0000 }, {   68, 0x0004 },
  {   69, 0x2040 }, {   71, 0x0000 }, {   71, 0x2001 }, {   73, 0x0002 },
  /* 0x5200 */
  {   74, 0x0000 }, {   74, 0x0004 }, {   75, 0x0000 }, {   75, 0x0000 },
  {   75, 0x0890 }, {   78, 0x0800 }, {   79, 0x4000 }, {   80, 0x0030 },
  {   82, 0x3688 }, {   88, 0x0002 }, {   89, 0x0000 }, {   89, 0x0000 },
  {   89, 0x0002 }, {   90, 0x6a20 }, {   95, 0x0004 }, {   96, 0x0122 },
  /* 0x5300 */
  {   99, 0x0000 }, {   99, 0x0000 }, {   99, 0xa000 }, {  101, 0x0002 },
  {  102, 0x0001 }, {  103, 0x0010 }, {  104, 0x0000 }, {  104, 0x0000 },
  {  104, 0x0004 }, {  105, 0x0200 }, {  106, 0x2001 }, {  108, 0x0014 },
  {  110, 0x0008 }, {  111, 0x0000 }, {  111, 0x0404 }, {  113, 0x0041 },
  /* 0x5400 */
  {  115, 0x4000 }, {  116, 0x0000 }, {  116, 0x0000 }, {  116, 0x0000 },
  {  116, 0x0000 }, {  116, 0x0000 }, {  116, 0x0000 }, {  116, 0x0000 },
  {  116, 0x0000 }, {  116, 0x0000 }, {  116, 0x0000 }, {  116, 0x1000 },
  {  117, 0x0000 }, {  117, 0x0000 }, {  117, 0x0002 }, {  118, 0x0000 },
  /* 0x5500 */
  {  118, 0x0010 }, {  119, 0x0000 }, {  119, 0x0000 }, {  119, 0x0000 },
  {  119, 0x8000 }, {  120, 0x4008 }, {  122, 0x0000 }, {  122, 0x0000 },
  {  122, 0x0000 }, {  122, 0x0000 }, {  122, 0x5400 }, {  125, 0x0004 },
  {  126, 0x40c0 }, {  129, 0x0400 }, {  130, 0x0200 }, {  131, 0x0040 },
  /* 0x5600 */
  {  132, 0x2040 }, {  134, 0x10d0 }, {  138, 0xc200 }, {  141, 0x0121 },
  {  144, 0x0002 }, {  145, 0x2000 }, {  146, 0x8061 }, {  150, 0x0314 },
  {  154, 0x1081 }, {  157, 0x0220 }, {  159, 0x4140 }, {  162, 0x0058 },
  {  165, 0x1327 }, {  172, 0x0002 }, {  173, 0x0000 }, {  173, 0x0000 },
  /* 0x5700 */
  {  173, 0x2880 }, {  176, 0x014c }, {  180, 0x0000 }, {  180, 0x0000 },
  {  180, 0x0000 }, {  180, 0x0000 }, {  180, 0x0000 }, {  180, 0x0000 },
  {  180, 0x0000 }, {  180, 0x0000 }, {  180, 0x0000 }, {  180, 0x0000 },
  {  180, 0x0000 }, {  180, 0x0000 }, {  180, 0x0002 }, {  181, 0x0080 },
  /* 0x5800 */
  {  182, 0x0420 }, {  184, 0x2040 }, {  186, 0x8000 }, {  187, 0x0012 },
  {  189, 0x8c00 }, {  192, 0x0084 }, {  194, 0x0014 }, {  196, 0x0220 },
  {  198, 0x0400 }, {  199, 0x1000 }, {  200, 0x4000 }, {  201, 0x4808 },
  {  204, 0x0080 }, {  205, 0xc708 }, {  211, 0x8205 }, {  215, 0x2400 },
  /* 0x5900 */
  {  217, 0x0000 }, {  217, 0x0000 }, {  217, 0x0004 }, {  218, 0x4100 },
  {  220, 0x0000 }, {  220, 0x0000 }, {  220, 0x5600 }, {  224, 0x0000 },
  {  224, 0x0000 }, {  224, 0x2000 }, {  225, 0x0000 }, {  225, 0x0000 },
  {  225, 0x0000 }, {  225, 0x0000 }, {  225, 0x0000 }, {  225, 0x0000 },
  /* 0x5a00 */
  {  225, 0x0000 }, {  225, 0x0000 }, {  225, 0x0000 }, {  225, 0x0000 },
  {  225, 0x0002 }, {  226, 0x0000 }, {  226, 0x2040 }, {  228, 0x0000 },
  {  228, 0x0000 }, {  228, 0x0000 }, {  228, 0x0080 }, {  229, 0x2000 },
  {  230, 0x0000 }, {  230, 0x0080 }, {  231, 0x0000 }, {  231, 0x0820 },
  /* 0x5b00 */
  {  233, 0x1901 }, {  237, 0x0200 }, {  238, 0x0402 }, {  240, 0x0101 },
  {  242, 0x1000 }, {  243, 0x0000 }, {  243, 0x0800 }, {  244, 0x8100 },
  {  246, 0x0000 }, {  246, 0x0000 }, {  246, 0x0000 }, {  246, 0x0000 },
  {  246, 0x0000 }, {  246, 0x0000 }, {  246, 0x1ac4 }, {  252, 0x0060 },
  /* 0x5c00 */
  {  254, 0x6980 }, {  259, 0x0000 }, {  259, 0x0000 }, {  259, 0x0080 },
  {  260, 0x0000 }, {  260, 0x0000 }, {  260, 0x1114 }, {  264, 0x0000 },
  {  264, 0x0000 }, {  264, 0x0000 }, {  264, 0x0002 }, {  265, 0x0000 },
  {  265, 0x0000 }, {  265, 0x0000 }, {  265, 0x0000 }, {  265, 0x2050 },
  /* 0x5d00 */
  {  268, 0x2000 }, {  269, 0x0080 }, {  270, 0x1000 }, {  271, 0x0000 },
  {  271, 0x0000 }, {  271, 0x0001 }, {  272, 0x0000 }, {  272, 0x0000 },
  {  272, 0x0092 }, {  275, 0x0080 }, {  276, 0x0081 }, {  278, 0x1500 },
  {  281, 0x0800 }, {  282, 0x0014 }, {  284, 0x0000 }, {  284, 0x0001 },
  /* 0x5e00 */
  {  285, 0x0000 }, {  285, 0x0100 }, {  286, 0x0820 }, {  288, 0x0048 },
  {  290, 0x0009 }, {  292, 0x8180 }, {  295, 0x1808 }, {  298, 0xc204 },
  {  302, 0x0000 }, {  302, 0x0000 }, {  302, 0x0800 }, {  303, 0x0000 },
  {  303, 0x0000 }, {  303, 0x8000 }, {  304, 0x100f }, {  309, 0x0008 },
  /* 0x5f00 */
  {  310, 0x0000 }, {  310, 0x0000 }, {  310, 0x0000 }, {  310, 0x0028 },
  {  312, 0x5140 }, {  316, 0x0200 }, {  317, 0x0000 }, {  317, 0x0000 },
  {  317, 0x1000 }, {  318, 0x4002 }, {  320, 0x0201 }, {  322, 0x0200 },
  {  323, 0x0000 }, {  323, 0x0000 }, {  323, 0x0000 }, {  323, 0x0000 },
  /* 0x6000 */
  {  323, 0x0000 }, {  323, 0x0000 }, {  323, 0x0000 }, {  323, 0x0000 },
  {  323, 0x0000 }, {  323, 0x0000 }, {  323, 0x0000 }, {  323, 0x0000 },
  {  323, 0x0000 }, {  323, 0x0000 }, {  323, 0x0000 }, {  323, 0x0060 },
  {  325, 0x0000 }, {  325, 0x0000 }, {  325, 0x0002 }, {  326, 0x0806 },
  /* 0x6100 */
  {  329, 0x0000 }, {  329, 0x1800 }, {  331, 0x0000 }, {  331, 0xc090 },
  {  335, 0x0800 }, {  336, 0x8500 }, {  339, 0x4c18 }, {  344, 0x0048 },
  {  346, 0x0404 }, {  348, 0x0407 }, {  352, 0x4810 }, {  355, 0x0044 },
  {  357, 0x1280 }, {  360, 0xc000 }, {  362, 0x0108 }, {  364, 0x55c4 },
  /* 0x6200 */
  {  371, 0x0081 }, {  373, 0x0010 }, {  374, 0x0080 }, {  375, 0x0005 },
  {  377, 0x0000 }, {  377, 0x0002 }, {  378, 0x0000 }, {  378, 0x0000 },
  {  378, 0x0000 }, {  378, 0x0000 }, {  378, 0x0000 }, {  378, 0x0000 },
  {  378, 0x0000 }, {  378, 0x0000 }, {  378, 0x0000 }, {  378, 0x0000 },
  /* 0x6300 */
  {  378, 0x0000 }, {  378, 0x0000 }, {  378, 0x0000 }, {  378, 0x4000 },
  {  379, 0x0000 }, {  379, 0x0000 }, {  379, 0x4900 }, {  382, 0x0004 },
  {  383, 0x0018 }, {  385, 0x0000 }, {  385, 0x0000 }, {  385, 0x0000 },
  {  385, 0x0001 }, {  386, 0x0400 }, {  387, 0x4000 }, {  388, 0x0000 },
  /* 0x6400 */
  {  388, 0x2000 }, {  389, 0x0080 }, {  390, 0x0000 }, {  390, 0x0040 },
  {  391, 0x0000 }, {  391, 0x9002 }, {  394, 0x8000 }, {  395, 0x0848 },
  {  398, 0x0100 }, {  399, 0x8008 }, {  401, 0x0828 }, {  404, 0xc80c },
  {  409, 0x0c92 }, {  414, 0x0410 }, {  416, 0x9001 }, {  419, 0x5c97 },
  /* 0x6500 */
  {  428, 0x8050 }, {  431, 0x2a50 }, {  436, 0x141c }, {  441, 0x0000 },
  {  441, 0x0000 }, {  441, 0x0080 }, {  442, 0x0000 }, {  442, 0x0120 },
  {  444, 0x000c }, {  446, 0x00a0 }, {  448, 0x1000 }, {  449, 0x0080 },
  {  450, 0x0000 }, {  450, 0x0000 }, {  450, 0x0000 }, {  450, 0x0000 },
  /* 0x6600 */
  {  450, 0x0000 }, {  450, 0x0000 }, {  450, 0x0000 }, {  450, 0x0000 },
  {  450, 0x0004 }, {  451, 0x2000 }, {  452, 0x0000 }, {  452, 0x0000 },
  {  452, 0x0300 }, {  454, 0x0000 }, {  454, 0x0804 }, {  456, 0x0000 },
  {  456, 0x02d0 }, {  460, 0x0040 }, {  461, 0x1001 }, {  463, 0x0100 },
  /* 0x6700 */
  {  464, 0x0008 }, {  465, 0x0000 }, {  465, 0x8080 }, {  467, 0x0010 },
  {  468, 0x0000 }, {  468, 0x0000 }, {  468, 0x0000 }, {  468, 0x4002 },
  {  470, 0x0000 }, {  470, 0x0000 }, {  470, 0x0000 }, {  470, 0x0000 },
  {  470, 0x0000 }, {  470, 0x0000 }, {  470, 0x0000 }, {  470, 0x0000 },
  /* 0x6800 */
  {  470, 0x0000 }, {  470, 0x0000 }, {  470, 0x0000 }, {  470, 0x0000 },
  {  470, 0x0000 }, {  470, 0x0000 }, {  470, 0x0000 }, {  470, 0x0000 },
  {  470, 0x0000 }, {  470, 0xa100 }, {  473, 0x0000 }, {  473, 0x0000 },
  {  473, 0x0000 }, {  473, 0x80c0 }, {  476, 0x0080 }, {  477, 0x0000 },
  /* 0x6900 */
  {  477, 0x8000 }, {  478, 0x0000 }, {  478, 0x0000 }, {  478, 0x0000 },
  {  478, 0x0400 }, {  479, 0x0008 }, {  480, 0x2100 }, {  482, 0x0020 },
  {  483, 0x0000 }, {  483, 0x0000 }, {  483, 0x4400 }, {  485, 0x8000 },
  {  486, 0x2800 }, {  488, 0x0000 }, {  488, 0x0080 }, {  489, 0x0008 },
  /* 0x6a00 */
  {  490, 0x0026 }, {  493, 0x4208 }, {  496, 0x0008 }, {  497, 0x0700 },
  {  500, 0x0900 }, {  502, 0x8000 }, {  503, 0x0004 }, {  504, 0x0000 },
  {  504, 0x0200 }, {  505, 0x1010 }, {  507, 0x800c }, {  510, 0x0908 },
  {  513, 0x0008 }, {  514, 0xec08 }, {  520, 0x1580 }, {  524, 0x0908 },
  /* 0x6b00 */
  {  527, 0x8410 }, {  530, 0x4044 }, {  533, 0x0000 }, {  533, 0x2000 },
  {  534, 0x0000 }, {  534, 0x8001 }, {  536, 0x0002 }, {  537, 0x0184 },
  {  540, 0x0000 }, {  540, 0x4100 }, {  542, 0xc810 }, {  546, 0x0c04 },
  {  549, 0x0040 }, {  550, 0x0000 }, {  550, 0x0000 }, {  550, 0x8000 },
  /* 0x6c00 */
  {  551, 0x1100 }, {  553, 0x0000 }, {  553, 0x1808 }, {  556, 0x0000 },
  {  556, 0x0000 }, {  556, 0x0000 }, {  556, 0x0000 }, {  556, 0x0000 },
  {  556, 0x0000 }, {  556, 0x0000 }, {  556, 0x0000 }, {  556, 0x0000 },
  {  556, 0x0000 }, {  556, 0x0000 }, {  556, 0x0000 }, {  556, 0x0000 },
  /* 0x6d00 */
  {  556, 0x0000 }, {  556, 0x0000 }, {  556, 0x0000 }, {  556, 0x0000 },
  {  556, 0x0000 }, {  556, 0x0000 }, {  556, 0x0000 }, {  556, 0x0200 },
  {  557, 0x0084 }, {  559, 0x0000 }, {  559, 0x0000 }, {  559, 0x0000 },
  {  559, 0x0001 }, {  560, 0x0000 }, {  560, 0x0400 }, {  561, 0x0460 },
  /* 0x6e00 */
  {  564, 0x0000 }, {  564, 0x0000 }, {  564, 0x1040 }, {  566, 0x4000 },
  {  567, 0x0000 }, {  567, 0x4000 }, {  568, 0x8000 }, {  569, 0x0000 },
  {  569, 0x0000 }, {  569, 0x2040 }, {  571, 0x0000 }, {  571, 0x0000 },
  {  571, 0x5030 }, {  575, 0x0000 }, {  575, 0x9000 }, {  577, 0x8184 },
  /* 0x6f00 */
  {  581, 0x0002 }, {  582, 0x0400 }, {  583, 0x100c }, {  586, 0x8104 },
  {  589, 0x0002 }, {  590, 0x0212 }, {  593, 0x8010 }, {  595, 0x8081 },
  {  598, 0x00c1 }, {  601, 0x0080 }, {  602, 0x4211 }, {  606, 0x0002 },
  {  607, 0x000a }, {  609, 0x8920 }, {  613, 0x0810 }, {  615, 0x5403 },
  /* 0x7000 */
  {  620, 0x8a60 }, {  625, 0xa120 }, {  629, 0x0181 }, {  632, 0x4005 },
  {  635, 0x0018 }, {  637, 0x2122 }, {  641, 0x0098 }, {  644, 0x0000 },
  {  644, 0x0000 }, {  644, 0x0000 }, {  644, 0x0000 }, {  644, 0x0000 },
  {  644, 0x8000 }, {  645, 0x0000 }, {  645, 0x0000 }, {  645, 0x0010 },
  /* 0x7100 */
  {  646, 0x0000 }, {  646, 0x0000 }, {  646, 0x0002 }, {  647, 0x0000 },
  {  647, 0x0200 }, {  648, 0x0004 }, {  649, 0x1204 }, {  652, 0x0000 },
  {  652, 0x0000 }, {  652, 0x0084 }, {  654, 0x0000 }, {  654, 0x4002 },
  {  656, 0x0102 }, {  658, 0x9204 }, {  662, 0x2040 }, {  664, 0x5010 },
  /* 0x7200 */
  {  667, 0x2000 }, {  668, 0x0801 }, {  670, 0x0000 }, {  670, 0x4404 },
  {  673, 0x0000 }, {  673, 0x0100 }, {  674, 0x0000 }, {  674, 0x2000 },
  {  675, 0x0000 }, {  675, 0x0040 }, {  676, 0x0084 }, {  678, 0x0000 },
  {  678, 0x0001 }, {  679, 0x0000 }, {  679, 0x0000 }, {  679, 0x2200 },
  /* 0x7300 */
  {  681, 0x0000 }, {  681, 0x0000 }, {  681, 0x0000 }, {  681, 0x0840 },
  {  683, 0x0032 }, {  686, 0x0000 }, {  686, 0x0d00 }, {  689, 0x1da5 },
  {  697, 0x0001 }, {  698, 0x0000 }, {  698, 0x0000 }, {  698, 0x0000 },
  {  698, 0x0000 }, {  698, 0x0000 }, {  698, 0x0000 }, {  698, 0x4000 },
  /* 0x7400 */
  {  699, 0x0000 }, {  699, 0x0000 }, {  699, 0x0000 }, {  699, 0x8000 },
  {  700, 0x0800 }, {  701, 0x0000 }, {  701, 0x0608 }, {  704, 0x0000 },
  {  704, 0x0200 }, {  705, 0x0000 }, {  705, 0x0048 }, {  707, 0x2001 },
  {  709, 0x8400 }, {  711, 0x0410 }, {  713, 0x0000 }, {  713, 0x0000 },
  /* 0x7500 */
  {  713, 0x1000 }, {  714, 0x0000 }, {  714, 0x0008 }, {  715, 0x0000 },
  {  715, 0x0000 }, {  715, 0x2000 }, {  716, 0x0804 }, {  718, 0x0040 },
  {  719, 0x0080 }, {  720, 0x0000 }, {  720, 0x0000 }, {  720, 0x0000 },
  {  720, 0x0080 }, {  721, 0x0200 }, {  722, 0x0000 }, {  722, 0x0000 },
  /* 0x7600 */
  {  722, 0x2804 }, {  725, 0x4000 }, {  726, 0x0082 }, {  728, 0x0800 },
  {  729, 0x02c4 }, {  733, 0x8100 }, {  735, 0x72b4 }, {  743, 0x1007 },
  {  747, 0x0000 }, {  747, 0x0400 }, {  748, 0x0000 }, {  748, 0x0500 },
  {  750, 0x0000 }, {  750, 0x4000 }, {  751, 0x009a }, {  755, 0x0000 },
  /* 0x7700 */
  {  755, 0x0000 }, {  755, 0x0000 }, {  755, 0x0000 }, {  755, 0x0000 },
  {  755, 0x8000 }, {  756, 0x4000 }, {  757, 0x0000 }, {  757, 0x0000 },
  {  757, 0x0000 }, {  757, 0x4100 }, {  759, 0x2000 }, {  760, 0x1000 },
  {  761, 0x0080 }, {  762, 0x0400 }, {  763, 0x8000 }, {  764, 0x0000 },
  /* 0x7800 */
  {  764, 0x0000 }, {  764, 0x0000 }, {  764, 0x0000 }, {  764, 0x0000 },
  {  764, 0x0008 }, {  765, 0x0000 }, {  765, 0xc110 }, {  769, 0x0000 },
  {  769, 0x0000 }, {  769, 0x0000 }, {  769, 0x2200 }, {  771, 0x1500 },
  {  774, 0x0000 }, {  774, 0x0400 }, {  775, 0x8088 }, {  778, 0x2000 },
  /* 0x7900 */
  {  779, 0x4000 }, {  780, 0x0200 }, {  781, 0x1c40 }, {  785, 0x0002 },
  {  786, 0x0000 }, {  786, 0x0000 }, {  786, 0x0000 }, {  786, 0x0000 },
  {  786, 0x6000 }, {  788, 0x0000 }, {  788, 0x4440 }, {  791, 0x0003 },
  {  793, 0x2000 }, {  794, 0x0000 }, {  794, 0x0000 }, {  794, 0x0000 },
  /* 0x7a00 */
  {  794, 0x0000 }, {  794, 0x0000 }, {  794, 0x4000 }, {  795, 0x0002 },
  {  796, 0x7001 }, {  800, 0x0000 }, {  800, 0x0a06 }, {  804, 0x0000 },
  {  804, 0x0000 }, {  804, 0x0000 }, {  804, 0x4600 }, {  807, 0x0440 },
  {  809, 0x05b0 }, {  814, 0x0000 }, {  814, 0x0400 }, {  815, 0x0040 },
  /* 0x7b00 */
  {  816, 0x0000 }, {  816, 0x0000 }, {  816, 0x0000 }, {  816, 0x0000 },
  {  816, 0x0040 }, {  817, 0x0002 }, {  818, 0x0080 }, {  819, 0x0000 },
  {  819, 0x0800 }, {  820, 0x0000 }, {  820, 0x0000 }, {  820, 0x0000 },
  {  820, 0x0a11 }, {  824, 0x0000 }, {  824, 0x0210 }, {  826, 0x0008 },
  /* 0x7c00 */
  {  827, 0x2001 }, {  829, 0x4000 }, {  830, 0x080a }, {  833, 0x6000 },
  {  835, 0x1008 }, {  837, 0x9000 }, {  839, 0x5611 }, {  845, 0x0004 },
  {  846, 0x0000 }, {  846, 0x0000 }, {  846, 0x0000 }, {  846, 0x0000 },
  {  846, 0x0000 }, {  846, 0x6000 }, {  848, 0x0080 }, {  849, 0x4255 },
  /* 0x7d00 */
  {  855, 0x2bf5 }, {  865, 0x1eb9 }, {  874, 0x0002 }, {  875, 0x960f },
  {  883, 0x4055 }, {  888, 0x6001 }, {  891, 0x0146 }, {  895, 0x024e },
  {  900, 0x834a }, {  906, 0x5008 }, {  909, 0x380c }, {  914, 0xef1f },
  {  926, 0x0c90 }, {  930, 0x6396 }, {  938, 0x934b }, {  946, 0x0a56 },
  /* 0x7e00 */
  {  952, 0x0f00 }, {  956, 0xe803 }, {  962, 0x6888 }, {  967, 0x62f6 },
  {  976, 0x0060 }, {  978, 0x4434 }, {  983, 0xee04 }, {  990, 0x7209 },
  {  996, 0xb500 }, { 1001, 0x1158 }, { 1006, 0x0000 }, { 1006, 0x0000 },
  { 1006, 0x0000 }, { 1006, 0x0000 }, { 1006, 0x0000 }, { 1006, 0x0000 },
  /* 0x7f00 */
  { 1006, 0x0000 }, { 1006, 0x0000 }, { 1006, 0x0000 }, { 1006, 0x0000 },
  { 1006, 0x5000 }, { 1008, 0x0000 }, { 1008, 0x0000 }, { 1008, 0x0081 },
  { 1010, 0x0160 }, { 1013, 0x0000 }, { 1013, 0x0220 }, { 1015, 0x0000 },
  { 1015, 0x0000 }, { 1015, 0x0004 }, { 1016, 0x0000 }, { 1016, 0x0200 },
  /* 0x8000 */
  { 1017, 0x0000 }, { 1017, 0x0000 }, { 1017, 0x5000 }, { 1019, 0x0000 },
  { 1019, 0x0000 }, { 1019, 0x4040 }, { 1021, 0x8000 }, { 1022, 0x62ed },
  { 1031, 0x0020 }, { 1032, 0x0000 }, { 1032, 0x0000 }, { 1032, 0x0000 },
  { 1032, 0x0000 }, { 1032, 0x0000 }, { 1032, 0x0000 }, { 1032, 0x0000 },
  /* 0x8100 */
  { 1032, 0x0020 }, { 1033, 0x0800 }, { 1034, 0x0000 }, { 1034, 0x0200 },
  { 1035, 0x4400 }, { 1037, 0x0040 }, { 1038, 0x0842 }, { 1041, 0x0100 },
  { 1042, 0x0000 }, { 1042, 0x0400 }, { 1043, 0x0201 }, { 1045, 0xe000 },
  { 1048, 0xa200 }, { 1051, 0x8500 }, { 1054, 0x0101 }, { 1056, 0x0400 },
  /* 0x8200 */
  { 1057, 0x2780 }, { 1062, 0x0000 }, { 1062, 0x0000 }, { 1062, 0x0000 },
  { 1062, 0x0000 }, { 1062, 0x0200 }, { 1063, 0x0850 }, { 1066, 0x0082 },
  { 1068, 0x0000 }, { 1068, 0x0000 }, { 1068, 0x0000 }, { 1068, 0x0800 },
  { 1069, 0x0000 }, { 1069, 0x0000 }, { 1069, 0x0080 }, { 1070, 0x0200 },
  /* 0x8300 */
  { 1071, 0x0008 }, { 1072, 0x0000 }, { 1072, 0x0000 }, { 1072, 0x0000 },
  { 1072, 0x0000 }, { 1072, 0x0001 }, { 1073, 0x0000 }, { 1073, 0x0000 },
  { 1073, 0x0400 }, { 1074, 0x0040 }, { 1075, 0x0084 }, { 1077, 0x0000 },
  { 1077, 0x0000 }, { 1077, 0x0000 }, { 1077, 0x8000 }, { 1078, 0x0000 },
  /* 0x8400 */
  { 1078, 0x0480 }, { 1080, 0x0000 }, { 1080, 0x1000 }, { 1081, 0x0020 },
  { 1082, 0x0200 }, { 1083, 0x0004 }, { 1084, 0x8050 }, { 1087, 0x0080 },
  { 1088, 0x0000 }, { 1088, 0x0018 }, { 1090, 0x0000 }, { 1090, 0x1000 },
  { 1091, 0x0801 }, { 1093, 0x0000 }, { 1093, 0xc000 }, { 1095, 0x2000 },
  /* 0x8500 */
  { 1096, 0x0000 }, { 1096, 0x4010 }, { 1098, 0x2048 }, { 1101, 0x0000 },
  { 1101, 0x4042 }, { 1104, 0x012c }, { 1108, 0x2604 }, { 1112, 0x0080 },
  { 1113, 0x1500 }, { 1116, 0x8012 }, { 1119, 0x0240 }, { 1121, 0x0400 },
  { 1122, 0x6000 }, { 1124, 0x2000 }, { 1125, 0x0420 }, { 1127, 0x0650 },
  /* 0x8600 */
  { 1131, 0x08d0 }, { 1135, 0x4400 }, { 1137, 0x2004 }, { 1139, 0x8400 },
  { 1141, 0x0000 }, { 1141, 0x9020 }, { 1144, 0x0080 }, { 1145, 0x0000 },
  { 1145, 0x0000 }, { 1145, 0x0000 }, { 1145, 0x0000 }, { 1145, 0x0000 },
  { 1145, 0x0000 }, { 1145, 0x0000 }, { 1145, 0x0000 }, { 1145, 0x0400 },
  /* 0x8700 */
  { 1146, 0x0040 }, { 1147, 0x0000 }, { 1147, 0x0002 }, { 1148, 0x0000 },
  { 1148, 0x0000 }, { 1148, 0x0020 }, { 1149, 0x0040 }, { 1150, 0x0100 },
  { 1151, 0x0010 }, { 1152, 0x4000 }, { 1153, 0x0004 }, { 1154, 0x0800 },
  { 1155, 0x4110 }, { 1158, 0x0000 }, { 1158, 0x9008 }, { 1161, 0x0844 },
  /* 0x8800 */
  { 1164, 0x0060 }, { 1166, 0x8003 }, { 1169, 0x0008 }, { 1170, 0x0842 },
  { 1173, 0x0440 }, { 1175, 0x2808 }, { 1178, 0x0000 }, { 1178, 0x0200 },
  { 1179, 0x0000 }, { 1179, 0x0000 }, { 1179, 0x0000 }, { 1179, 0x0000 },
  { 1179, 0x8400 }, { 1181, 0x3000 }, { 1183, 0x0000 }, { 1183, 0x2000 },
  /* 0x8900 */
  { 1184, 0x0080 }, { 1185, 0x0000 }, { 1185, 0x0000 }, { 1185, 0x090c },
  { 1189, 0x0080 }, { 1190, 0x2040 }, { 1192, 0x9411 }, { 1197, 0x0004 },
  { 1198, 0x8800 }, { 1200, 0x0148 }, { 1203, 0x9442 }, { 1208, 0xa484 },
  { 1213, 0x0001 }, { 1214, 0x0000 }, { 1214, 0x0000 }, { 1214, 0x0150 },
  /* 0x8a00 */
  { 1217, 0x550e }, { 1224, 0xa969 }, { 1232, 0x2428 }, { 1236, 0x0452 },
  { 1240, 0x4042 }, { 1243, 0x4935 }, { 1250, 0x7a4e }, { 1259, 0x902f },
  { 1266, 0x20f0 }, { 1271, 0x4526 }, { 1277, 0x117b }, { 1285, 0x9245 },
  { 1291, 0xaa94 }, { 1298, 0x58c6 }, { 1305, 0x68d4 }, { 1312, 0x55ca },
  /* 0x8b00 */
  { 1320, 0x4437 }, { 1327, 0x2ed1 }, { 1335, 0x3902 }, { 1340, 0x4208 },
  { 1343, 0xc200 }, { 1346, 0x1740 }, { 1351, 0x8800 }, { 1353, 0x2091 },
  { 1357, 0x4401 }, { 1360, 0x506c }, { 1366, 0x0000 }, { 1366, 0x0000 },
  { 1366, 0x0000 }, { 1366, 0x0000 }, { 1366, 0x0000 }, { 1366, 0x0000 },
  /* 0x8c00 */
  { 1366, 0x0000 }, { 1366, 0x0000 }, { 1366, 0x0000 }, { 1366, 0x0000 },
  { 1366, 0x0100 }, { 1367, 0x0001 }, { 1368, 0x0000 }, { 1368, 0x0000 },
  { 1368, 0x0000 }, { 1368, 0x6000 }, { 1370, 0x9f87 }, { 1380, 0xbddd },
  { 1392, 0x05bf }, { 1401, 0x542e }, { 1408, 0x3cdf }, { 1419, 0x7c10 },
  /* 0x8d00 */
  { 1425, 0xad30 }, { 1432, 0x1841 }, { 1436, 0x0000 }, { 1436, 0x0000 },
  { 1436, 0x0000 }, { 1436, 0x0000 }, { 1436, 0x0000 }, { 1436, 0x0000 },
  { 1436, 0x0000 }, { 1436, 0x0220 }, { 1438, 0x0100 }, { 1439, 0x0004 },
  { 1440, 0x0000 }, { 1440, 0x0000 }, { 1440, 0x0000 }, { 1440, 0x0000 },
  /* 0x8e00 */
  { 1440, 0x0000 }, { 1440, 0x0001 }, { 1441, 0x0000 }, { 1441, 0x0010 },
  { 1442, 0x1000 }, { 1443, 0x0020 }, { 1444, 0x0008 }, { 1445, 0x0400 },
  { 1446, 0x2e00 }, { 1450, 0x040e }, { 1454, 0x0462 }, { 1458, 0x0000 },
  { 1458, 0x3c01 }, { 1463, 0x8814 }, { 1467, 0x0810 }, { 1469, 0x5f04 },
  /* 0x8f00 */
  { 1476, 0x06a8 }, { 1481, 0xf834 }, { 1489, 0x8660 }, { 1494, 0xc908 },
  { 1499, 0x6274 }, { 1506, 0x8010 }, { 1508, 0x0016 }, { 1511, 0x0000 },
  { 1511, 0x0000 }, { 1511, 0x0000 }, { 1511, 0xe040 }, { 1515, 0x0004 },
  { 1516, 0x0000 }, { 1516, 0x0000 }, { 1516, 0x0000 }, { 1516, 0x0010 },
  /* 0x9000 */
  { 1517, 0x0000 }, { 1517, 0x0220 }, { 1519, 0x0008 }, { 1520, 0x0004 },
  { 1521, 0x4800 }, { 1523, 0x5030 }, { 1527, 0x0201 }, { 1529, 0x1584 },
  { 1534, 0x8492 }, { 1539, 0x0001 }, { 1540, 0x0000 }, { 1540, 0x0000 },
  { 1540, 0x0002 }, { 1541, 0x8000 }, { 1542, 0x0000 }, { 1542, 0x0020 },
  /* 0x9100 */
  { 1543, 0x0240 }, { 1545, 0x0054 }, { 1548, 0x2080 }, { 1550, 0x0455 },
  { 1555, 0x0100 }, { 1556, 0x0000 }, { 1556, 0x0000 }, { 1556, 0x0000 },
  { 1556, 0x0000 }, { 1556, 0x1040 }, { 1558, 0x1800 }, { 1560, 0x0000 },
  { 1560, 0x182b }, { 1566, 0x23bc }, { 1574, 0x0298 }, { 1578, 0x06a0 },
  /* 0x9200 */
  { 1582, 0x6313 }, { 1589, 0x4033 }, { 1594, 0x40e8 }, { 1599, 0xe799 },
  { 1609, 0x2321 }, { 1614, 0x4ca2 }, { 1620, 0x3044 }, { 1624, 0x8d00 },
  { 1628, 0x0029 }, { 1631, 0x154a }, { 1637, 0x1f69 }, { 1646, 0x1a82 },
  { 1651, 0x90aa }, { 1657, 0xa004 }, { 1660, 0xe578 }, { 1669, 0x1143 },
  /* 0x9300 */
  { 1674, 0x01c2 }, { 1678, 0x8f25 }, { 1686, 0xc945 }, { 1693, 0x014c },
  { 1697, 0x28cb }, { 1704, 0x0910 }, { 1707, 0x1230 }, { 1711, 0x4461 },
  { 1716, 0x0494 }, { 1720, 0x0140 }, { 1722, 0x56cc }, { 1730, 0x8129 },
  { 1735, 0x3188 }, { 1740, 0xf182 }, { 1747, 0x0116 }, { 1751, 0x02a0 },
  /* 0x9400 */
  { 1754, 0x0808 }, { 1756, 0x231d }, { 1763, 0x41c1 }, { 1768, 0x852c },
  { 1774, 0x1410 }, { 1777, 0x0014 }, { 1779, 0x2029 }, { 1783, 0xf285 },
  { 1791, 0x0000 }, { 1791, 0x0000 }, { 1791, 0x0000 }, { 1791, 0x0000 },
  { 1791, 0x0000 }, { 1791, 0x0000 }, { 1791, 0x0000 }, { 1791, 0x0000 },
  /* 0x9500 */
  { 1791, 0x0000 }, { 1791, 0x0000 }, { 1791, 0x0000 }, { 1791, 0x0000 },
  { 1791, 0x0000 }, { 1791, 0x0000 }, { 1791, 0x0000 }, { 1791, 0x0080 },
  { 1792, 0xda4d }, { 1801, 0x011a }, { 1805, 0x3b3a }, { 1814, 0xfa44 },
  { 1822, 0x1d48 }, { 1828, 0x5071 }, { 1834, 0x0026 }, { 1837, 0x0000 },
  /* 0x9600 */
  { 1837, 0x0000 }, { 1837, 0x0000 }, { 1837, 0x0000 }, { 1837, 0x0000 },
  { 1837, 0x0000 }, { 1837, 0x2100 }, { 1839, 0x0008 }, { 1840, 0x2109 },
  { 1844, 0x4400 }, { 1846, 0x0820 }, { 1848, 0x0500 }, { 1850, 0x0912 },
  { 1854, 0x0000 }, { 1854, 0x1a40 }, { 1858, 0x000c }, { 1860, 0x0804 },
  /* 0x9700 */
  { 1862, 0x0000 }, { 1862, 0x0000 }, { 1862, 0x0080 }, { 1863, 0x2000 },
  { 1864, 0x0114 }, { 1867, 0x0400 }, { 1868, 0x0100 }, { 1869, 0x0000 },
  { 1869, 0x8000 }, { 1870, 0x0000 }, { 1870, 0x0040 }, { 1871, 0x2000 },
  { 1872, 0x1a48 }, { 1877, 0x5208 }, { 1881, 0x0000 }, { 1881, 0x8000 },
  /* 0x9800 */
  { 1882, 0xd5ee }, { 1893, 0x118f }, { 1900, 0x2052 }, { 1904, 0x2981 },
  { 1909, 0x7040 }, { 1913, 0x4b18 }, { 1919, 0x98a4 }, { 1925, 0x001b },
  { 1929, 0x0000 }, { 1929, 0x0000 }, { 1929, 0xc100 }, { 1932, 0x104a },
  { 1936, 0x0150 }, { 1939, 0x0800 }, { 1940, 0xae05 }, { 1947, 0x7014 },
  /* 0x9900 */
  { 1952, 0x1628 }, { 1957, 0x490e }, { 1963, 0x0102 }, { 1965, 0xd088 },
  { 1970, 0x1f28 }, { 1977, 0x5086 }, { 1982, 0x0004 }, { 1983, 0x0000 },
  { 1983, 0x0000 }, { 1983, 0x0000 }, { 1983, 0x7000 }, { 1986, 0x001a },
  { 1989, 0x0002 }, { 1990, 0xab37 }, { 2000, 0x2006 }, { 2003, 0x8002 },
  /* 0x9a00 */
  { 2005, 0xe022 }, { 2010, 0x0240 }, { 2012, 0x6800 }, { 2015, 0x41c1 },
  { 2020, 0xa43f }, { 2029, 0x8ca0 }, { 2034, 0x0434 }, { 2038, 0x0000 },
  { 2038, 0x0000 }, { 2038, 0x0000 }, { 2038, 0x8000 }, { 2039, 0x0000 },
  { 2039, 0x8000 }, { 2040, 0x0074 }, { 2044, 0x4000 }, { 2045, 0x0000 },
  /* 0x9b00 */
  { 2045, 0x2040 }, { 2047, 0x0400 }, { 2048, 0x42a4 }, { 2053, 0x0002 },
  { 2054, 0x4000 }, { 2055, 0x0500 }, { 2057, 0x8000 }, { 2058, 0x0090 },
  { 2060, 0x400a }, { 2063, 0x6407 }, { 2069, 0x6c00 }, { 2073, 0x0000 },
  { 2073, 0x0683 }, { 2078, 0x2850 }, { 2082, 0x0d96 }, { 2089, 0xa011 },
  /* 0x9c00 */
  { 2093, 0x2300 }, { 2096, 0x000d }, { 2099, 0x2329 }, { 2105, 0x4aae },
  { 2113, 0x0320 }, { 2116, 0xa1d4 }, { 2123, 0x2080 }, { 2125, 0x0500 },
  { 2127, 0x0000 }, { 2127, 0x0000 }, { 2127, 0x0000 }, { 2127, 0x0000 },
  { 2127, 0x0000 }, { 2127, 0x0000 }, { 2127, 0x1220 }, { 2130, 0x0058 },
  /* 0x9d00 */
  { 2133, 0x02c0 }, { 2136, 0xa820 }, { 2140, 0x8148 }, { 2144, 0x8801 },
  { 2147, 0x0004 }, { 2148, 0x300e }, { 2153, 0x9403 }, { 2158, 0x0004 },
  { 2159, 0x0280 }, { 2161, 0x0508 }, { 2164, 0x8220 }, { 2167, 0x1810 },
  { 2170, 0x0015 }, { 2173, 0x0688 }, { 2177, 0x8060 }, { 2180, 0x070c },
  /* 0x9e00 */
  { 2185, 0x1000 }, { 2186, 0x6c20 }, { 2191, 0x0000 }, { 2191, 0x0000 },
  { 2191, 0x0000 }, { 2191, 0x0000 }, { 2191, 0x0000 }, { 2191, 0x3620 },
  { 2196, 0x0000 }, { 2196, 0x0080 }, { 2197, 0x8220 }, { 2200, 0x2020 },
  { 2202, 0x1000 }, { 2203, 0x4000 }, { 2204, 0x0100 }, { 2205, 0xa094 },
  /* 0x9f00 */
  { 2210, 0x0200 }, { 2211, 0x0020 }, { 2212, 0x0000 }, { 2212, 0x0000 },
  { 2212, 0x8c00 }, { 2215, 0x9214 }, { 2220, 0x144b }, { 2226, 0x0084 },
  { 2228, 0x2000 }, { 2229, 0x1031 },
};
static const Summary16 gb12345ext_uni2indx_pagefe[5] = {
  /* 0xfe00 */
  { 2233, 0x0000 }, { 2233, 0x0000 }, { 2233, 0x0000 }, { 2233, 0xfffa },
  { 2247, 0x001f },
};

static int
gb12345ext_wctomb (conv_t conv, unsigned char *r, ucs4_t wc, int n)
{
  if (n >= 2) {
    const Summary16 *summary = NULL;
    if (wc >= 0x0100 && wc < 0x0270)
      summary = &gb12345ext_uni2indx_page01[(wc>>4)-0x010];
    else if (wc >= 0x1e00 && wc < 0x1e40)
      summary = &gb12345ext_uni2indx_page1e[(wc>>4)-0x1e0];
    else if (wc >= 0x2200 && wc < 0x2230)
      summary = &gb12345ext_uni2indx_page22[(wc>>4)-0x220];
    else if (wc >= 0x4e00 && wc < 0x9fa0)
      summary = &gb12345ext_uni2indx_page4e[(wc>>4)-0x4e0];
    else if (wc >= 0xfe00 && wc < 0xfe50)
      summary = &gb12345ext_uni2indx_pagefe[(wc>>4)-0xfe0];
    if (summary) {
      unsigned short used = summary->used;
      unsigned int i = wc & 0x0f;
      if (used & ((unsigned short) 1 << i)) {
        unsigned short c;
        /* Keep in `used' only the bits 0..i-1. */
        used &= ((unsigned short) 1 << i) - 1;
        /* Add `summary->indx' and the number of bits set in `used'. */
        used = (used & 0x5555) + ((used & 0xaaaa) >> 1);
        used = (used & 0x3333) + ((used & 0xcccc) >> 2);
        used = (used & 0x0f0f) + ((used & 0xf0f0) >> 4);
        used = (used & 0x00ff) + (used >> 8);
        c = gb12345ext_2charset[summary->indx + used];
        r[0] = (c >> 8); r[1] = (c & 0xff);
        return 2;
      }
    }
    return RET_ILUNI;
  }
  return RET_TOOSMALL;
}
