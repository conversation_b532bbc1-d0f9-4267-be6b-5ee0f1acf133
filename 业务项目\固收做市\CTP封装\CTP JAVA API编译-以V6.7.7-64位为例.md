## 1\. 准备工作

- 从CTP官网上下载`CTP API`。64位的API文件包清单如下：
- ![](../../attachments/CTP%20JAVA%20API编译-以V6.7.7-64位为例/file-20250213143035049.png)

- 安装Swig软件，当前所用的Swig是`swigwin-4.3`版本，[点击下载]([SWIG - Browse Files at SourceForge.net](https://sourceforge.net/projects/swig/files/))。安装后将swig路径添加到系统环境变量。
- 安装1.8版本JDK，注意要安装64位版本，并将环境变量配置好。 
- 下载`libiconv`库。这个库主要适用于字节编码转换，因为CTP的结算单信息是GB2312编码，而Java采用UTF-8编码，如果不进行字节转换，得到的结算单信息中的中文将会是乱码。 到[github libiconv](https://github.com/nicai0609/Libiconv)下载编译好的。
- 安装vs2013，主要用于生成包装dll。
- Linux上安装1.8版本JDK,64位版本,配置JAVA_HOME环境变量

## 2\. 通过Swig得到jar包
新建一个文件夹javactp,并将Ctp包下的win64的文件复制到javactp目录
![](../../attachments/CTP%20JAVA%20API编译-以V6.7.7-64位为例/file-20250213144403520.png)

在文件夹`javactp`内，新建文件`thosttraderapi.i`，内容如下

```
%module(directors="1") thosttraderapi 
%include "various.i"
%apply char **STRING_ARRAY { char *ppInstrumentID[] }
%{ 
#include "ThostFtdcMdApi.h"
#include "ThostFtdcTraderApi.h"
#include "iconv.h"
%}

%typemap(out) char[ANY], char[] {
    if ($1) {
        iconv_t cd = iconv_open("utf-8", "gb2312");
        if (cd != reinterpret_cast<iconv_t>(-1)) {
            char buf[4096] = {};
            char **in = &$1;
            char *out = buf;
            size_t inlen = strlen($1), outlen = 4096;

            if (iconv(cd, in, &inlen, &out, &outlen) != static_cast<size_t>(-1))
                $result = JCALL1(NewStringUTF, jenv, (const char *)buf);
            iconv_close(cd);
        }
    }
}

%feature("director") CThostFtdcMdSpi;
%ignore THOST_FTDC_VTC_BankBankToFuture;
%ignore THOST_FTDC_VTC_BankFutureToBank;
%ignore THOST_FTDC_VTC_FutureBankToFuture;
%ignore THOST_FTDC_VTC_FutureFutureToBank;
%ignore THOST_FTDC_FTC_BankLaunchBankToBroker;
%ignore THOST_FTDC_FTC_BrokerLaunchBankToBroker;
%ignore THOST_FTDC_FTC_BankLaunchBrokerToBank;
%ignore THOST_FTDC_FTC_BrokerLaunchBrokerToBank;
%include "ThostFtdcUserApiDataType.h"
%include "ThostFtdcUserApiStruct.h" 
%include "ThostFtdcMdApi.h"
%feature("director") CThostFtdcTraderSpi;
%include "ThostFtdcTraderApi.h"

```

这是一个接口文件，用于告诉swig为哪些类和方法创建接口。然后在当前目录内建立文件夹`src`，`wrap`，和`ctp`，在`ctp`文件夹内建立文件夹`thostmduserapi`。文件夹结构图如下：
![](../../attachments/CTP%20JAVA%20API编译-以V6.7.7-64位为例/file-*****************.png)
复制libiconv下的`win64`目录下的文件到`javactp`目录
![](../../attachments/CTP%20JAVA%20API编译-以V6.7.7-64位为例/file-*****************.png)
复制swig的文件swig/Lib/java/various.i到`javactp`目录
打开`windows cmd`工具，`cd`到当前目录下即java ctp。 在`cmd`中运行命令

```
swig.exe -c++ -java -package ctp.thostmduserapi -outdir src -o thostmduserapi_wrap.cpp thosttraderapi.i
```

中间可能会报一次`warning 514:...`警告，直接忽略。等到运行完成后，可以看到当前目录下生成了两个文件
![](../../attachments/CTP%20JAVA%20API编译-以V6.7.7-64位为例/file-*****************.png)


这两个文件是用于包装原来`C++`接口的文件，下面要用。打开`src`文件夹，可以看到这时在里面生成了一系列方法的java文件，如下：

> CThostFtdcAccountregisterField.java  
> CThostFtdcAuthenticationInfoField.java  
> … … …  
> thosttradeapiJNI.java

在`cmd`中`cd`到`src`文件夹底下，运行命令

```
javac *.java
1
```

运行结束之后可以看到生成了等量的class文件，将所有的class文件拷贝到`\ctp\thostmduserapi\`文件夹中，`cmd`下`cd`到`javactp`目录下，运行命令

```
jar cf thostmduserapi.jar ctp
1
```

这样我们就在当前文件夹下得到了jar包`thostmduserapi.jar`。
![](../../attachments/CTP%20JAVA%20API编译-以V6.7.7-64位为例/file-*****************.png)
## 3\. 通过C++得到java可调用的windows64 dll动态库

接下来在`wrap`文件夹中，建立一个C++工程，工程为Win32控制台应用程序，工程名为`thostmduserapi_wrap`，点下一步，工程的应用程序类型选`DLL`，附加选项选空项目。另外建好项目后，在工程属性-c/c+±代码生成-运行库中选多线程(/MT)。步骤图如下：  
1）

![](https://i-blog.csdnimg.cn/blog_migrate/2fd96df41f273d6ca35081477f2777a6.png)

2）

![](https://i-blog.csdnimg.cn/blog_migrate/4abf2891be19412194816af686b1b891.png)

3）

![](https://i-blog.csdnimg.cn/blog_migrate/796edf6e8632f90cbb46b85681171b4f.png)

配置编译输出为win64,在工程属性
![](../../attachments/CTP%20JAVA%20API编译-以V6.7.7-64位为例/file-20250213151013215.png)

![](../../attachments/CTP%20JAVA%20API编译-以V6.7.7-64位为例/file-20250213151042877.png)
完成之后，将如下文件拷贝到工程源代码文件夹下：
![](../../attachments/CTP%20JAVA%20API编译-以V6.7.7-64位为例/file-20250213151328673.png)


下面还要做几步：

- 将你安装jdk目录`\Java\jdk1.8.0_111\include`下的`jni.h`和`win32`文件夹下的`jni_md.h, jawt_md.h`一共三个文件拷贝到安装vs的include目录底下`\Microsoft Visual Studio 12.0\VC\include`。这是因为`thostmduserapi_wrap.cpp`文件中包含了`<jni.h>`,这是用于生成`Java`可调用接口的库文件。

然后选择release版编译。我们按F7编译，在`\wrap\thostmduserapi_wrap\Release`目录底下可见`thostmduserapi_wrap.dll`动态库文件，说明编译成功，这样`CTP Java API`就编译成功了。
![](../../attachments/CTP%20JAVA%20API编译-以V6.7.7-64位为例/file-20250213151616355.png)
## 4\. 编译Linux64 动态库
Linux的java包与windows相同
新建文件夹ctp677

将Ctp文件的linux64的文件复制到`ctp677`目录下
![](../../attachments/CTP%20JAVA%20API编译-以V6.7.7-64位为例/file-20250213153426150.png)
复制libiconv下的`linux`目录下的文件到`ctp677`目录
![](../../attachments/CTP%20JAVA%20API编译-以V6.7.7-64位为例/file-20250213153623971.png)

将上面得到windows下的`thostmduserapi_wrap.cpp`和`thostmduserapi_wrap.h`两个文件拷贝`ctp677`目录
![](../../attachments/CTP%20JAVA%20API编译-以V6.7.7-64位为例/file-20250213153903554.png)

在`ctp677`目录新建makefile文件，内容如下
```
OBJS=thostmduserapi_wrap.o
INCLUDE=-I./ -I${JAVA_HOME}/include -I${JAVA_HOME}/include/linux
TARGET=libthostmduserapi_wrap.so
CPPFLAG=-shared -fPIC
CC=g++
LDLIB=-L. -lthostmduserapi_se -L. -lthosttraderapi_se
$(TARGET) : $(OBJS)
		$(CC) $(CPPFLAG) $(INCLUDE) -o $(TARGET) $(OBJS) $(LDLIB) ./libiconv.a                                                                              
$(OBJS) : %.o : %.cpp
		$(CC) -c -fPIC $(INCLUDE) $< -o $@ 
clean:
		-rm -f $(OBJS)
		-rm -f $(TARGET)
install:
		cp $(TARGET) /usr/lib

```

执行make命令，得到libthostmduserapi_wrap.so，编译成功
![](../../attachments/CTP%20JAVA%20API编译-以V6.7.7-64位为例/file-20250213154406928.png)

如果CTP的so文件不是以lib开头，在文件名前面加上lib。
## 5.最终文件
### windows
两个CTP的dll文件加上编译得到的dll文件
### Linux
两个CTP的so文件加上编译得到的libthostmduserapi_wrap.so
![](../../attachments/CTP%20JAVA%20API编译-以V6.7.7-64位为例/file-20250213154922717.png)
## 6.参考文档

[CTP JAVA API(JCTP)编译（利用Swig封装C++动态库）windows版-CSDN博客](https://blog.csdn.net/pjjing/article/details/53186394?spm=1001.2101.3001.4242.2&utm_relevant_index=2)
[CTP JAVA API(JCTP) 64位 2in1及CTP JAVA订阅全市场行情DEMO-CSDN博客](https://blog.csdn.net/pjjing/article/details/85063988)
[CTP JAVA_API（JCTP）编译（利用Swig封装C++动态库）linux版64位_linux ctp java 中文-CSDN博客](https://blog.csdn.net/pjjing/article/details/53187469?spm=1001.2101.3001.4242.3&utm_relevant_index=5)