CREATE TABLE ptms.manual_bond_entry
(
    EID       char(32)             NOT NULL COMMENT '主键UUID'
        PRIMARY KEY,
    EITIME    datetime             NOT NULL COMMENT '创建时间',
    EUTIME    datetime             NOT NULL COMMENT '更新时间',
    del       tinyint(1) DEFAULT 0 NOT NULL COMMENT '逻辑删除标志(0:未删除,1:已删除)',
    bondCode  varchar(20)          NOT NULL COMMENT '债券代码',
    bondName  varchar(100)         NOT NULL COMMENT '债券简称',
    groupType varchar(10)          NOT NULL COMMENT '组别(自营/做市)',
    entryTime datetime             NOT NULL COMMENT '入库时间',
    endTime   datetime             NOT NULL COMMENT '终止时间'
)
    ENGINE = InnoDB
    DEFAULT CHARSET = utf8mb4
    COLLATE = utf8mb4_0900_ai_ci COMMENT '手动入库债券表';

CREATE INDEX idx_bondCode
    ON ptms.manual_bond_entry (bondCode);

