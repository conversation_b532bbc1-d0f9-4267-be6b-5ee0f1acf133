/*
 * Copyright (C) 1999-2007 Free Software Foundation, Inc.
 * This file is part of the GNU LIBICONV Library.
 *
 * The GNU LIBICONV Library is free software; you can redistribute it
 * and/or modify it under the terms of the GNU Library General Public
 * License as published by the Free Software Foundation; either version 2
 * of the License, or (at your option) any later version.
 *
 * The GNU LIBICONV Library is distributed in the hope that it will be
 * useful, but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
 * Library General Public License for more details.
 *
 * You should have received a copy of the GNU Library General Public
 * License along with the GNU LIBICONV Library; see the file COPYING.LIB.
 * If not, write to the Free Software Foundation, Inc., 51 Franklin Street,
 * Fifth Floor, Boston, MA 02110-1301, USA.
 */

/*
 * KSC5601.1987-0, now KS X 1001:2002
 */

static const unsigned short ksc5601_2uni_page21[1115] = {
  /* 0x21 */
  0x3000, 0x3001, 0x3002, 0x00b7, 0x2025, 0x2026, 0x00a8, 0x3003,
  0x00ad, 0x2015, 0x2225, 0xff3c, 0x223c, 0x2018, 0x2019, 0x201c,
  0x201d, 0x3014, 0x3015, 0x3008, 0x3009, 0x300a, 0x300b, 0x300c,
  0x300d, 0x300e, 0x300f, 0x3010, 0x3011, 0x00b1, 0x00d7, 0x00f7,
  0x2260, 0x2264, 0x2265, 0x221e, 0x2234, 0x00b0, 0x2032, 0x2033,
  0x2103, 0x212b, 0xffe0, 0xffe1, 0xffe5, 0x2642, 0x2640, 0x2220,
  0x22a5, 0x2312, 0x2202, 0x2207, 0x2261, 0x2252, 0x00a7, 0x203b,
  0x2606, 0x2605, 0x25cb, 0x25cf, 0x25ce, 0x25c7, 0x25c6, 0x25a1,
  0x25a0, 0x25b3, 0x25b2, 0x25bd, 0x25bc, 0x2192, 0x2190, 0x2191,
  0x2193, 0x2194, 0x3013, 0x226a, 0x226b, 0x221a, 0x223d, 0x221d,
  0x2235, 0x222b, 0x222c, 0x2208, 0x220b, 0x2286, 0x2287, 0x2282,
  0x2283, 0x222a, 0x2229, 0x2227, 0x2228, 0xffe2,
  /* 0x22 */
  0x21d2, 0x21d4, 0x2200, 0x2203, 0x00b4, 0xff5e, 0x02c7, 0x02d8,
  0x02dd, 0x02da, 0x02d9, 0x00b8, 0x02db, 0x00a1, 0x00bf, 0x02d0,
  0x222e, 0x2211, 0x220f, 0x00a4, 0x2109, 0x2030, 0x25c1, 0x25c0,
  0x25b7, 0x25b6, 0x2664, 0x2660, 0x2661, 0x2665, 0x2667, 0x2663,
  0x2299, 0x25c8, 0x25a3, 0x25d0, 0x25d1, 0x2592, 0x25a4, 0x25a5,
  0x25a8, 0x25a7, 0x25a6, 0x25a9, 0x2668, 0x260f, 0x260e, 0x261c,
  0x261e, 0x00b6, 0x2020, 0x2021, 0x2195, 0x2197, 0x2199, 0x2196,
  0x2198, 0x266d, 0x2669, 0x266a, 0x266c, 0x327f, 0x321c, 0x2116,
  0x33c7, 0x2122, 0x33c2, 0x33d8, 0x2121, 0x20ac, 0x00ae, 0x327e,
  0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd,
  0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd,
  0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd,
  /* 0x23 */
  0xff01, 0xff02, 0xff03, 0xff04, 0xff05, 0xff06, 0xff07, 0xff08,
  0xff09, 0xff0a, 0xff0b, 0xff0c, 0xff0d, 0xff0e, 0xff0f, 0xff10,
  0xff11, 0xff12, 0xff13, 0xff14, 0xff15, 0xff16, 0xff17, 0xff18,
  0xff19, 0xff1a, 0xff1b, 0xff1c, 0xff1d, 0xff1e, 0xff1f, 0xff20,
  0xff21, 0xff22, 0xff23, 0xff24, 0xff25, 0xff26, 0xff27, 0xff28,
  0xff29, 0xff2a, 0xff2b, 0xff2c, 0xff2d, 0xff2e, 0xff2f, 0xff30,
  0xff31, 0xff32, 0xff33, 0xff34, 0xff35, 0xff36, 0xff37, 0xff38,
  0xff39, 0xff3a, 0xff3b, 0xffe6, 0xff3d, 0xff3e, 0xff3f, 0xff40,
  0xff41, 0xff42, 0xff43, 0xff44, 0xff45, 0xff46, 0xff47, 0xff48,
  0xff49, 0xff4a, 0xff4b, 0xff4c, 0xff4d, 0xff4e, 0xff4f, 0xff50,
  0xff51, 0xff52, 0xff53, 0xff54, 0xff55, 0xff56, 0xff57, 0xff58,
  0xff59, 0xff5a, 0xff5b, 0xff5c, 0xff5d, 0xffe3,
  /* 0x24 */
  0x3131, 0x3132, 0x3133, 0x3134, 0x3135, 0x3136, 0x3137, 0x3138,
  0x3139, 0x313a, 0x313b, 0x313c, 0x313d, 0x313e, 0x313f, 0x3140,
  0x3141, 0x3142, 0x3143, 0x3144, 0x3145, 0x3146, 0x3147, 0x3148,
  0x3149, 0x314a, 0x314b, 0x314c, 0x314d, 0x314e, 0x314f, 0x3150,
  0x3151, 0x3152, 0x3153, 0x3154, 0x3155, 0x3156, 0x3157, 0x3158,
  0x3159, 0x315a, 0x315b, 0x315c, 0x315d, 0x315e, 0x315f, 0x3160,
  0x3161, 0x3162, 0x3163, 0x3164, 0x3165, 0x3166, 0x3167, 0x3168,
  0x3169, 0x316a, 0x316b, 0x316c, 0x316d, 0x316e, 0x316f, 0x3170,
  0x3171, 0x3172, 0x3173, 0x3174, 0x3175, 0x3176, 0x3177, 0x3178,
  0x3179, 0x317a, 0x317b, 0x317c, 0x317d, 0x317e, 0x317f, 0x3180,
  0x3181, 0x3182, 0x3183, 0x3184, 0x3185, 0x3186, 0x3187, 0x3188,
  0x3189, 0x318a, 0x318b, 0x318c, 0x318d, 0x318e,
  /* 0x25 */
  0x2170, 0x2171, 0x2172, 0x2173, 0x2174, 0x2175, 0x2176, 0x2177,
  0x2178, 0x2179, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0x2160,
  0x2161, 0x2162, 0x2163, 0x2164, 0x2165, 0x2166, 0x2167, 0x2168,
  0x2169, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd,
  0x0391, 0x0392, 0x0393, 0x0394, 0x0395, 0x0396, 0x0397, 0x0398,
  0x0399, 0x039a, 0x039b, 0x039c, 0x039d, 0x039e, 0x039f, 0x03a0,
  0x03a1, 0x03a3, 0x03a4, 0x03a5, 0x03a6, 0x03a7, 0x03a8, 0x03a9,
  0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd,
  0x03b1, 0x03b2, 0x03b3, 0x03b4, 0x03b5, 0x03b6, 0x03b7, 0x03b8,
  0x03b9, 0x03ba, 0x03bb, 0x03bc, 0x03bd, 0x03be, 0x03bf, 0x03c0,
  0x03c1, 0x03c3, 0x03c4, 0x03c5, 0x03c6, 0x03c7, 0x03c8, 0x03c9,
  0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd,
  /* 0x26 */
  0x2500, 0x2502, 0x250c, 0x2510, 0x2518, 0x2514, 0x251c, 0x252c,
  0x2524, 0x2534, 0x253c, 0x2501, 0x2503, 0x250f, 0x2513, 0x251b,
  0x2517, 0x2523, 0x2533, 0x252b, 0x253b, 0x254b, 0x2520, 0x252f,
  0x2528, 0x2537, 0x253f, 0x251d, 0x2530, 0x2525, 0x2538, 0x2542,
  0x2512, 0x2511, 0x251a, 0x2519, 0x2516, 0x2515, 0x250e, 0x250d,
  0x251e, 0x251f, 0x2521, 0x2522, 0x2526, 0x2527, 0x2529, 0x252a,
  0x252d, 0x252e, 0x2531, 0x2532, 0x2535, 0x2536, 0x2539, 0x253a,
  0x253d, 0x253e, 0x2540, 0x2541, 0x2543, 0x2544, 0x2545, 0x2546,
  0x2547, 0x2548, 0x2549, 0x254a, 0xfffd, 0xfffd, 0xfffd, 0xfffd,
  0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd,
  0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd,
  0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd,
  /* 0x27 */
  0x3395, 0x3396, 0x3397, 0x2113, 0x3398, 0x33c4, 0x33a3, 0x33a4,
  0x33a5, 0x33a6, 0x3399, 0x339a, 0x339b, 0x339c, 0x339d, 0x339e,
  0x339f, 0x33a0, 0x33a1, 0x33a2, 0x33ca, 0x338d, 0x338e, 0x338f,
  0x33cf, 0x3388, 0x3389, 0x33c8, 0x33a7, 0x33a8, 0x33b0, 0x33b1,
  0x33b2, 0x33b3, 0x33b4, 0x33b5, 0x33b6, 0x33b7, 0x33b8, 0x33b9,
  0x3380, 0x3381, 0x3382, 0x3383, 0x3384, 0x33ba, 0x33bb, 0x33bc,
  0x33bd, 0x33be, 0x33bf, 0x3390, 0x3391, 0x3392, 0x3393, 0x3394,
  0x2126, 0x33c0, 0x33c1, 0x338a, 0x338b, 0x338c, 0x33d6, 0x33c5,
  0x33ad, 0x33ae, 0x33af, 0x33db, 0x33a9, 0x33aa, 0x33ab, 0x33ac,
  0x33dd, 0x33d0, 0x33d3, 0x33c3, 0x33c9, 0x33dc, 0x33c6, 0xfffd,
  0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd,
  0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd,
  /* 0x28 */
  0x00c6, 0x00d0, 0x00aa, 0x0126, 0xfffd, 0x0132, 0xfffd, 0x013f,
  0x0141, 0x00d8, 0x0152, 0x00ba, 0x00de, 0x0166, 0x014a, 0xfffd,
  0x3260, 0x3261, 0x3262, 0x3263, 0x3264, 0x3265, 0x3266, 0x3267,
  0x3268, 0x3269, 0x326a, 0x326b, 0x326c, 0x326d, 0x326e, 0x326f,
  0x3270, 0x3271, 0x3272, 0x3273, 0x3274, 0x3275, 0x3276, 0x3277,
  0x3278, 0x3279, 0x327a, 0x327b, 0x24d0, 0x24d1, 0x24d2, 0x24d3,
  0x24d4, 0x24d5, 0x24d6, 0x24d7, 0x24d8, 0x24d9, 0x24da, 0x24db,
  0x24dc, 0x24dd, 0x24de, 0x24df, 0x24e0, 0x24e1, 0x24e2, 0x24e3,
  0x24e4, 0x24e5, 0x24e6, 0x24e7, 0x24e8, 0x24e9, 0x2460, 0x2461,
  0x2462, 0x2463, 0x2464, 0x2465, 0x2466, 0x2467, 0x2468, 0x2469,
  0x246a, 0x246b, 0x246c, 0x246d, 0x246e, 0x00bd, 0x2153, 0x2154,
  0x00bc, 0x00be, 0x215b, 0x215c, 0x215d, 0x215e,
  /* 0x29 */
  0x00e6, 0x0111, 0x00f0, 0x0127, 0x0131, 0x0133, 0x0138, 0x0140,
  0x0142, 0x00f8, 0x0153, 0x00df, 0x00fe, 0x0167, 0x014b, 0x0149,
  0x3200, 0x3201, 0x3202, 0x3203, 0x3204, 0x3205, 0x3206, 0x3207,
  0x3208, 0x3209, 0x320a, 0x320b, 0x320c, 0x320d, 0x320e, 0x320f,
  0x3210, 0x3211, 0x3212, 0x3213, 0x3214, 0x3215, 0x3216, 0x3217,
  0x3218, 0x3219, 0x321a, 0x321b, 0x249c, 0x249d, 0x249e, 0x249f,
  0x24a0, 0x24a1, 0x24a2, 0x24a3, 0x24a4, 0x24a5, 0x24a6, 0x24a7,
  0x24a8, 0x24a9, 0x24aa, 0x24ab, 0x24ac, 0x24ad, 0x24ae, 0x24af,
  0x24b0, 0x24b1, 0x24b2, 0x24b3, 0x24b4, 0x24b5, 0x2474, 0x2475,
  0x2476, 0x2477, 0x2478, 0x2479, 0x247a, 0x247b, 0x247c, 0x247d,
  0x247e, 0x247f, 0x2480, 0x2481, 0x2482, 0x00b9, 0x00b2, 0x00b3,
  0x2074, 0x207f, 0x2081, 0x2082, 0x2083, 0x2084,
  /* 0x2a */
  0x3041, 0x3042, 0x3043, 0x3044, 0x3045, 0x3046, 0x3047, 0x3048,
  0x3049, 0x304a, 0x304b, 0x304c, 0x304d, 0x304e, 0x304f, 0x3050,
  0x3051, 0x3052, 0x3053, 0x3054, 0x3055, 0x3056, 0x3057, 0x3058,
  0x3059, 0x305a, 0x305b, 0x305c, 0x305d, 0x305e, 0x305f, 0x3060,
  0x3061, 0x3062, 0x3063, 0x3064, 0x3065, 0x3066, 0x3067, 0x3068,
  0x3069, 0x306a, 0x306b, 0x306c, 0x306d, 0x306e, 0x306f, 0x3070,
  0x3071, 0x3072, 0x3073, 0x3074, 0x3075, 0x3076, 0x3077, 0x3078,
  0x3079, 0x307a, 0x307b, 0x307c, 0x307d, 0x307e, 0x307f, 0x3080,
  0x3081, 0x3082, 0x3083, 0x3084, 0x3085, 0x3086, 0x3087, 0x3088,
  0x3089, 0x308a, 0x308b, 0x308c, 0x308d, 0x308e, 0x308f, 0x3090,
  0x3091, 0x3092, 0x3093, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd,
  0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd,
  /* 0x2b */
  0x30a1, 0x30a2, 0x30a3, 0x30a4, 0x30a5, 0x30a6, 0x30a7, 0x30a8,
  0x30a9, 0x30aa, 0x30ab, 0x30ac, 0x30ad, 0x30ae, 0x30af, 0x30b0,
  0x30b1, 0x30b2, 0x30b3, 0x30b4, 0x30b5, 0x30b6, 0x30b7, 0x30b8,
  0x30b9, 0x30ba, 0x30bb, 0x30bc, 0x30bd, 0x30be, 0x30bf, 0x30c0,
  0x30c1, 0x30c2, 0x30c3, 0x30c4, 0x30c5, 0x30c6, 0x30c7, 0x30c8,
  0x30c9, 0x30ca, 0x30cb, 0x30cc, 0x30cd, 0x30ce, 0x30cf, 0x30d0,
  0x30d1, 0x30d2, 0x30d3, 0x30d4, 0x30d5, 0x30d6, 0x30d7, 0x30d8,
  0x30d9, 0x30da, 0x30db, 0x30dc, 0x30dd, 0x30de, 0x30df, 0x30e0,
  0x30e1, 0x30e2, 0x30e3, 0x30e4, 0x30e5, 0x30e6, 0x30e7, 0x30e8,
  0x30e9, 0x30ea, 0x30eb, 0x30ec, 0x30ed, 0x30ee, 0x30ef, 0x30f0,
  0x30f1, 0x30f2, 0x30f3, 0x30f4, 0x30f5, 0x30f6, 0xfffd, 0xfffd,
  0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd,
  /* 0x2c */
  0x0410, 0x0411, 0x0412, 0x0413, 0x0414, 0x0415, 0x0401, 0x0416,
  0x0417, 0x0418, 0x0419, 0x041a, 0x041b, 0x041c, 0x041d, 0x041e,
  0x041f, 0x0420, 0x0421, 0x0422, 0x0423, 0x0424, 0x0425, 0x0426,
  0x0427, 0x0428, 0x0429, 0x042a, 0x042b, 0x042c, 0x042d, 0x042e,
  0x042f, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd,
  0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd,
  0x0430, 0x0431, 0x0432, 0x0433, 0x0434, 0x0435, 0x0451, 0x0436,
  0x0437, 0x0438, 0x0439, 0x043a, 0x043b, 0x043c, 0x043d, 0x043e,
  0x043f, 0x0440, 0x0441, 0x0442, 0x0443, 0x0444, 0x0445, 0x0446,
  0x0447, 0x0448, 0x0449, 0x044a, 0x044b, 0x044c, 0x044d, 0x044e,
  0x044f,
};
static const unsigned short ksc5601_2uni_page30[2350] = {
  /* 0x30 */
  0xac00, 0xac01, 0xac04, 0xac07, 0xac08, 0xac09, 0xac0a, 0xac10,
  0xac11, 0xac12, 0xac13, 0xac14, 0xac15, 0xac16, 0xac17, 0xac19,
  0xac1a, 0xac1b, 0xac1c, 0xac1d, 0xac20, 0xac24, 0xac2c, 0xac2d,
  0xac2f, 0xac30, 0xac31, 0xac38, 0xac39, 0xac3c, 0xac40, 0xac4b,
  0xac4d, 0xac54, 0xac58, 0xac5c, 0xac70, 0xac71, 0xac74, 0xac77,
  0xac78, 0xac7a, 0xac80, 0xac81, 0xac83, 0xac84, 0xac85, 0xac86,
  0xac89, 0xac8a, 0xac8b, 0xac8c, 0xac90, 0xac94, 0xac9c, 0xac9d,
  0xac9f, 0xaca0, 0xaca1, 0xaca8, 0xaca9, 0xacaa, 0xacac, 0xacaf,
  0xacb0, 0xacb8, 0xacb9, 0xacbb, 0xacbc, 0xacbd, 0xacc1, 0xacc4,
  0xacc8, 0xaccc, 0xacd5, 0xacd7, 0xace0, 0xace1, 0xace4, 0xace7,
  0xace8, 0xacea, 0xacec, 0xacef, 0xacf0, 0xacf1, 0xacf3, 0xacf5,
  0xacf6, 0xacfc, 0xacfd, 0xad00, 0xad04, 0xad06,
  /* 0x31 */
  0xad0c, 0xad0d, 0xad0f, 0xad11, 0xad18, 0xad1c, 0xad20, 0xad29,
  0xad2c, 0xad2d, 0xad34, 0xad35, 0xad38, 0xad3c, 0xad44, 0xad45,
  0xad47, 0xad49, 0xad50, 0xad54, 0xad58, 0xad61, 0xad63, 0xad6c,
  0xad6d, 0xad70, 0xad73, 0xad74, 0xad75, 0xad76, 0xad7b, 0xad7c,
  0xad7d, 0xad7f, 0xad81, 0xad82, 0xad88, 0xad89, 0xad8c, 0xad90,
  0xad9c, 0xad9d, 0xada4, 0xadb7, 0xadc0, 0xadc1, 0xadc4, 0xadc8,
  0xadd0, 0xadd1, 0xadd3, 0xaddc, 0xade0, 0xade4, 0xadf8, 0xadf9,
  0xadfc, 0xadff, 0xae00, 0xae01, 0xae08, 0xae09, 0xae0b, 0xae0d,
  0xae14, 0xae30, 0xae31, 0xae34, 0xae37, 0xae38, 0xae3a, 0xae40,
  0xae41, 0xae43, 0xae45, 0xae46, 0xae4a, 0xae4c, 0xae4d, 0xae4e,
  0xae50, 0xae54, 0xae56, 0xae5c, 0xae5d, 0xae5f, 0xae60, 0xae61,
  0xae65, 0xae68, 0xae69, 0xae6c, 0xae70, 0xae78,
  /* 0x32 */
  0xae79, 0xae7b, 0xae7c, 0xae7d, 0xae84, 0xae85, 0xae8c, 0xaebc,
  0xaebd, 0xaebe, 0xaec0, 0xaec4, 0xaecc, 0xaecd, 0xaecf, 0xaed0,
  0xaed1, 0xaed8, 0xaed9, 0xaedc, 0xaee8, 0xaeeb, 0xaeed, 0xaef4,
  0xaef8, 0xaefc, 0xaf07, 0xaf08, 0xaf0d, 0xaf10, 0xaf2c, 0xaf2d,
  0xaf30, 0xaf32, 0xaf34, 0xaf3c, 0xaf3d, 0xaf3f, 0xaf41, 0xaf42,
  0xaf43, 0xaf48, 0xaf49, 0xaf50, 0xaf5c, 0xaf5d, 0xaf64, 0xaf65,
  0xaf79, 0xaf80, 0xaf84, 0xaf88, 0xaf90, 0xaf91, 0xaf95, 0xaf9c,
  0xafb8, 0xafb9, 0xafbc, 0xafc0, 0xafc7, 0xafc8, 0xafc9, 0xafcb,
  0xafcd, 0xafce, 0xafd4, 0xafdc, 0xafe8, 0xafe9, 0xaff0, 0xaff1,
  0xaff4, 0xaff8, 0xb000, 0xb001, 0xb004, 0xb00c, 0xb010, 0xb014,
  0xb01c, 0xb01d, 0xb028, 0xb044, 0xb045, 0xb048, 0xb04a, 0xb04c,
  0xb04e, 0xb053, 0xb054, 0xb055, 0xb057, 0xb059,
  /* 0x33 */
  0xb05d, 0xb07c, 0xb07d, 0xb080, 0xb084, 0xb08c, 0xb08d, 0xb08f,
  0xb091, 0xb098, 0xb099, 0xb09a, 0xb09c, 0xb09f, 0xb0a0, 0xb0a1,
  0xb0a2, 0xb0a8, 0xb0a9, 0xb0ab, 0xb0ac, 0xb0ad, 0xb0ae, 0xb0af,
  0xb0b1, 0xb0b3, 0xb0b4, 0xb0b5, 0xb0b8, 0xb0bc, 0xb0c4, 0xb0c5,
  0xb0c7, 0xb0c8, 0xb0c9, 0xb0d0, 0xb0d1, 0xb0d4, 0xb0d8, 0xb0e0,
  0xb0e5, 0xb108, 0xb109, 0xb10b, 0xb10c, 0xb110, 0xb112, 0xb113,
  0xb118, 0xb119, 0xb11b, 0xb11c, 0xb11d, 0xb123, 0xb124, 0xb125,
  0xb128, 0xb12c, 0xb134, 0xb135, 0xb137, 0xb138, 0xb139, 0xb140,
  0xb141, 0xb144, 0xb148, 0xb150, 0xb151, 0xb154, 0xb155, 0xb158,
  0xb15c, 0xb160, 0xb178, 0xb179, 0xb17c, 0xb180, 0xb182, 0xb188,
  0xb189, 0xb18b, 0xb18d, 0xb192, 0xb193, 0xb194, 0xb198, 0xb19c,
  0xb1a8, 0xb1cc, 0xb1d0, 0xb1d4, 0xb1dc, 0xb1dd,
  /* 0x34 */
  0xb1df, 0xb1e8, 0xb1e9, 0xb1ec, 0xb1f0, 0xb1f9, 0xb1fb, 0xb1fd,
  0xb204, 0xb205, 0xb208, 0xb20b, 0xb20c, 0xb214, 0xb215, 0xb217,
  0xb219, 0xb220, 0xb234, 0xb23c, 0xb258, 0xb25c, 0xb260, 0xb268,
  0xb269, 0xb274, 0xb275, 0xb27c, 0xb284, 0xb285, 0xb289, 0xb290,
  0xb291, 0xb294, 0xb298, 0xb299, 0xb29a, 0xb2a0, 0xb2a1, 0xb2a3,
  0xb2a5, 0xb2a6, 0xb2aa, 0xb2ac, 0xb2b0, 0xb2b4, 0xb2c8, 0xb2c9,
  0xb2cc, 0xb2d0, 0xb2d2, 0xb2d8, 0xb2d9, 0xb2db, 0xb2dd, 0xb2e2,
  0xb2e4, 0xb2e5, 0xb2e6, 0xb2e8, 0xb2eb, 0xb2ec, 0xb2ed, 0xb2ee,
  0xb2ef, 0xb2f3, 0xb2f4, 0xb2f5, 0xb2f7, 0xb2f8, 0xb2f9, 0xb2fa,
  0xb2fb, 0xb2ff, 0xb300, 0xb301, 0xb304, 0xb308, 0xb310, 0xb311,
  0xb313, 0xb314, 0xb315, 0xb31c, 0xb354, 0xb355, 0xb356, 0xb358,
  0xb35b, 0xb35c, 0xb35e, 0xb35f, 0xb364, 0xb365,
  /* 0x35 */
  0xb367, 0xb369, 0xb36b, 0xb36e, 0xb370, 0xb371, 0xb374, 0xb378,
  0xb380, 0xb381, 0xb383, 0xb384, 0xb385, 0xb38c, 0xb390, 0xb394,
  0xb3a0, 0xb3a1, 0xb3a8, 0xb3ac, 0xb3c4, 0xb3c5, 0xb3c8, 0xb3cb,
  0xb3cc, 0xb3ce, 0xb3d0, 0xb3d4, 0xb3d5, 0xb3d7, 0xb3d9, 0xb3db,
  0xb3dd, 0xb3e0, 0xb3e4, 0xb3e8, 0xb3fc, 0xb410, 0xb418, 0xb41c,
  0xb420, 0xb428, 0xb429, 0xb42b, 0xb434, 0xb450, 0xb451, 0xb454,
  0xb458, 0xb460, 0xb461, 0xb463, 0xb465, 0xb46c, 0xb480, 0xb488,
  0xb49d, 0xb4a4, 0xb4a8, 0xb4ac, 0xb4b5, 0xb4b7, 0xb4b9, 0xb4c0,
  0xb4c4, 0xb4c8, 0xb4d0, 0xb4d5, 0xb4dc, 0xb4dd, 0xb4e0, 0xb4e3,
  0xb4e4, 0xb4e6, 0xb4ec, 0xb4ed, 0xb4ef, 0xb4f1, 0xb4f8, 0xb514,
  0xb515, 0xb518, 0xb51b, 0xb51c, 0xb524, 0xb525, 0xb527, 0xb528,
  0xb529, 0xb52a, 0xb530, 0xb531, 0xb534, 0xb538,
  /* 0x36 */
  0xb540, 0xb541, 0xb543, 0xb544, 0xb545, 0xb54b, 0xb54c, 0xb54d,
  0xb550, 0xb554, 0xb55c, 0xb55d, 0xb55f, 0xb560, 0xb561, 0xb5a0,
  0xb5a1, 0xb5a4, 0xb5a8, 0xb5aa, 0xb5ab, 0xb5b0, 0xb5b1, 0xb5b3,
  0xb5b4, 0xb5b5, 0xb5bb, 0xb5bc, 0xb5bd, 0xb5c0, 0xb5c4, 0xb5cc,
  0xb5cd, 0xb5cf, 0xb5d0, 0xb5d1, 0xb5d8, 0xb5ec, 0xb610, 0xb611,
  0xb614, 0xb618, 0xb625, 0xb62c, 0xb634, 0xb648, 0xb664, 0xb668,
  0xb69c, 0xb69d, 0xb6a0, 0xb6a4, 0xb6ab, 0xb6ac, 0xb6b1, 0xb6d4,
  0xb6f0, 0xb6f4, 0xb6f8, 0xb700, 0xb701, 0xb705, 0xb728, 0xb729,
  0xb72c, 0xb72f, 0xb730, 0xb738, 0xb739, 0xb73b, 0xb744, 0xb748,
  0xb74c, 0xb754, 0xb755, 0xb760, 0xb764, 0xb768, 0xb770, 0xb771,
  0xb773, 0xb775, 0xb77c, 0xb77d, 0xb780, 0xb784, 0xb78c, 0xb78d,
  0xb78f, 0xb790, 0xb791, 0xb792, 0xb796, 0xb797,
  /* 0x37 */
  0xb798, 0xb799, 0xb79c, 0xb7a0, 0xb7a8, 0xb7a9, 0xb7ab, 0xb7ac,
  0xb7ad, 0xb7b4, 0xb7b5, 0xb7b8, 0xb7c7, 0xb7c9, 0xb7ec, 0xb7ed,
  0xb7f0, 0xb7f4, 0xb7fc, 0xb7fd, 0xb7ff, 0xb800, 0xb801, 0xb807,
  0xb808, 0xb809, 0xb80c, 0xb810, 0xb818, 0xb819, 0xb81b, 0xb81d,
  0xb824, 0xb825, 0xb828, 0xb82c, 0xb834, 0xb835, 0xb837, 0xb838,
  0xb839, 0xb840, 0xb844, 0xb851, 0xb853, 0xb85c, 0xb85d, 0xb860,
  0xb864, 0xb86c, 0xb86d, 0xb86f, 0xb871, 0xb878, 0xb87c, 0xb88d,
  0xb8a8, 0xb8b0, 0xb8b4, 0xb8b8, 0xb8c0, 0xb8c1, 0xb8c3, 0xb8c5,
  0xb8cc, 0xb8d0, 0xb8d4, 0xb8dd, 0xb8df, 0xb8e1, 0xb8e8, 0xb8e9,
  0xb8ec, 0xb8f0, 0xb8f8, 0xb8f9, 0xb8fb, 0xb8fd, 0xb904, 0xb918,
  0xb920, 0xb93c, 0xb93d, 0xb940, 0xb944, 0xb94c, 0xb94f, 0xb951,
  0xb958, 0xb959, 0xb95c, 0xb960, 0xb968, 0xb969,
  /* 0x38 */
  0xb96b, 0xb96d, 0xb974, 0xb975, 0xb978, 0xb97c, 0xb984, 0xb985,
  0xb987, 0xb989, 0xb98a, 0xb98d, 0xb98e, 0xb9ac, 0xb9ad, 0xb9b0,
  0xb9b4, 0xb9bc, 0xb9bd, 0xb9bf, 0xb9c1, 0xb9c8, 0xb9c9, 0xb9cc,
  0xb9ce, 0xb9cf, 0xb9d0, 0xb9d1, 0xb9d2, 0xb9d8, 0xb9d9, 0xb9db,
  0xb9dd, 0xb9de, 0xb9e1, 0xb9e3, 0xb9e4, 0xb9e5, 0xb9e8, 0xb9ec,
  0xb9f4, 0xb9f5, 0xb9f7, 0xb9f8, 0xb9f9, 0xb9fa, 0xba00, 0xba01,
  0xba08, 0xba15, 0xba38, 0xba39, 0xba3c, 0xba40, 0xba42, 0xba48,
  0xba49, 0xba4b, 0xba4d, 0xba4e, 0xba53, 0xba54, 0xba55, 0xba58,
  0xba5c, 0xba64, 0xba65, 0xba67, 0xba68, 0xba69, 0xba70, 0xba71,
  0xba74, 0xba78, 0xba83, 0xba84, 0xba85, 0xba87, 0xba8c, 0xbaa8,
  0xbaa9, 0xbaab, 0xbaac, 0xbab0, 0xbab2, 0xbab8, 0xbab9, 0xbabb,
  0xbabd, 0xbac4, 0xbac8, 0xbad8, 0xbad9, 0xbafc,
  /* 0x39 */
  0xbb00, 0xbb04, 0xbb0d, 0xbb0f, 0xbb11, 0xbb18, 0xbb1c, 0xbb20,
  0xbb29, 0xbb2b, 0xbb34, 0xbb35, 0xbb36, 0xbb38, 0xbb3b, 0xbb3c,
  0xbb3d, 0xbb3e, 0xbb44, 0xbb45, 0xbb47, 0xbb49, 0xbb4d, 0xbb4f,
  0xbb50, 0xbb54, 0xbb58, 0xbb61, 0xbb63, 0xbb6c, 0xbb88, 0xbb8c,
  0xbb90, 0xbba4, 0xbba8, 0xbbac, 0xbbb4, 0xbbb7, 0xbbc0, 0xbbc4,
  0xbbc8, 0xbbd0, 0xbbd3, 0xbbf8, 0xbbf9, 0xbbfc, 0xbbff, 0xbc00,
  0xbc02, 0xbc08, 0xbc09, 0xbc0b, 0xbc0c, 0xbc0d, 0xbc0f, 0xbc11,
  0xbc14, 0xbc15, 0xbc16, 0xbc17, 0xbc18, 0xbc1b, 0xbc1c, 0xbc1d,
  0xbc1e, 0xbc1f, 0xbc24, 0xbc25, 0xbc27, 0xbc29, 0xbc2d, 0xbc30,
  0xbc31, 0xbc34, 0xbc38, 0xbc40, 0xbc41, 0xbc43, 0xbc44, 0xbc45,
  0xbc49, 0xbc4c, 0xbc4d, 0xbc50, 0xbc5d, 0xbc84, 0xbc85, 0xbc88,
  0xbc8b, 0xbc8c, 0xbc8e, 0xbc94, 0xbc95, 0xbc97,
  /* 0x3a */
  0xbc99, 0xbc9a, 0xbca0, 0xbca1, 0xbca4, 0xbca7, 0xbca8, 0xbcb0,
  0xbcb1, 0xbcb3, 0xbcb4, 0xbcb5, 0xbcbc, 0xbcbd, 0xbcc0, 0xbcc4,
  0xbccd, 0xbccf, 0xbcd0, 0xbcd1, 0xbcd5, 0xbcd8, 0xbcdc, 0xbcf4,
  0xbcf5, 0xbcf6, 0xbcf8, 0xbcfc, 0xbd04, 0xbd05, 0xbd07, 0xbd09,
  0xbd10, 0xbd14, 0xbd24, 0xbd2c, 0xbd40, 0xbd48, 0xbd49, 0xbd4c,
  0xbd50, 0xbd58, 0xbd59, 0xbd64, 0xbd68, 0xbd80, 0xbd81, 0xbd84,
  0xbd87, 0xbd88, 0xbd89, 0xbd8a, 0xbd90, 0xbd91, 0xbd93, 0xbd95,
  0xbd99, 0xbd9a, 0xbd9c, 0xbda4, 0xbdb0, 0xbdb8, 0xbdd4, 0xbdd5,
  0xbdd8, 0xbddc, 0xbde9, 0xbdf0, 0xbdf4, 0xbdf8, 0xbe00, 0xbe03,
  0xbe05, 0xbe0c, 0xbe0d, 0xbe10, 0xbe14, 0xbe1c, 0xbe1d, 0xbe1f,
  0xbe44, 0xbe45, 0xbe48, 0xbe4c, 0xbe4e, 0xbe54, 0xbe55, 0xbe57,
  0xbe59, 0xbe5a, 0xbe5b, 0xbe60, 0xbe61, 0xbe64,
  /* 0x3b */
  0xbe68, 0xbe6a, 0xbe70, 0xbe71, 0xbe73, 0xbe74, 0xbe75, 0xbe7b,
  0xbe7c, 0xbe7d, 0xbe80, 0xbe84, 0xbe8c, 0xbe8d, 0xbe8f, 0xbe90,
  0xbe91, 0xbe98, 0xbe99, 0xbea8, 0xbed0, 0xbed1, 0xbed4, 0xbed7,
  0xbed8, 0xbee0, 0xbee3, 0xbee4, 0xbee5, 0xbeec, 0xbf01, 0xbf08,
  0xbf09, 0xbf18, 0xbf19, 0xbf1b, 0xbf1c, 0xbf1d, 0xbf40, 0xbf41,
  0xbf44, 0xbf48, 0xbf50, 0xbf51, 0xbf55, 0xbf94, 0xbfb0, 0xbfc5,
  0xbfcc, 0xbfcd, 0xbfd0, 0xbfd4, 0xbfdc, 0xbfdf, 0xbfe1, 0xc03c,
  0xc051, 0xc058, 0xc05c, 0xc060, 0xc068, 0xc069, 0xc090, 0xc091,
  0xc094, 0xc098, 0xc0a0, 0xc0a1, 0xc0a3, 0xc0a5, 0xc0ac, 0xc0ad,
  0xc0af, 0xc0b0, 0xc0b3, 0xc0b4, 0xc0b5, 0xc0b6, 0xc0bc, 0xc0bd,
  0xc0bf, 0xc0c0, 0xc0c1, 0xc0c5, 0xc0c8, 0xc0c9, 0xc0cc, 0xc0d0,
  0xc0d8, 0xc0d9, 0xc0db, 0xc0dc, 0xc0dd, 0xc0e4,
  /* 0x3c */
  0xc0e5, 0xc0e8, 0xc0ec, 0xc0f4, 0xc0f5, 0xc0f7, 0xc0f9, 0xc100,
  0xc104, 0xc108, 0xc110, 0xc115, 0xc11c, 0xc11d, 0xc11e, 0xc11f,
  0xc120, 0xc123, 0xc124, 0xc126, 0xc127, 0xc12c, 0xc12d, 0xc12f,
  0xc130, 0xc131, 0xc136, 0xc138, 0xc139, 0xc13c, 0xc140, 0xc148,
  0xc149, 0xc14b, 0xc14c, 0xc14d, 0xc154, 0xc155, 0xc158, 0xc15c,
  0xc164, 0xc165, 0xc167, 0xc168, 0xc169, 0xc170, 0xc174, 0xc178,
  0xc185, 0xc18c, 0xc18d, 0xc18e, 0xc190, 0xc194, 0xc196, 0xc19c,
  0xc19d, 0xc19f, 0xc1a1, 0xc1a5, 0xc1a8, 0xc1a9, 0xc1ac, 0xc1b0,
  0xc1bd, 0xc1c4, 0xc1c8, 0xc1cc, 0xc1d4, 0xc1d7, 0xc1d8, 0xc1e0,
  0xc1e4, 0xc1e8, 0xc1f0, 0xc1f1, 0xc1f3, 0xc1fc, 0xc1fd, 0xc200,
  0xc204, 0xc20c, 0xc20d, 0xc20f, 0xc211, 0xc218, 0xc219, 0xc21c,
  0xc21f, 0xc220, 0xc228, 0xc229, 0xc22b, 0xc22d,
  /* 0x3d */
  0xc22f, 0xc231, 0xc232, 0xc234, 0xc248, 0xc250, 0xc251, 0xc254,
  0xc258, 0xc260, 0xc265, 0xc26c, 0xc26d, 0xc270, 0xc274, 0xc27c,
  0xc27d, 0xc27f, 0xc281, 0xc288, 0xc289, 0xc290, 0xc298, 0xc29b,
  0xc29d, 0xc2a4, 0xc2a5, 0xc2a8, 0xc2ac, 0xc2ad, 0xc2b4, 0xc2b5,
  0xc2b7, 0xc2b9, 0xc2dc, 0xc2dd, 0xc2e0, 0xc2e3, 0xc2e4, 0xc2eb,
  0xc2ec, 0xc2ed, 0xc2ef, 0xc2f1, 0xc2f6, 0xc2f8, 0xc2f9, 0xc2fb,
  0xc2fc, 0xc300, 0xc308, 0xc309, 0xc30c, 0xc30d, 0xc313, 0xc314,
  0xc315, 0xc318, 0xc31c, 0xc324, 0xc325, 0xc328, 0xc329, 0xc345,
  0xc368, 0xc369, 0xc36c, 0xc370, 0xc372, 0xc378, 0xc379, 0xc37c,
  0xc37d, 0xc384, 0xc388, 0xc38c, 0xc3c0, 0xc3d8, 0xc3d9, 0xc3dc,
  0xc3df, 0xc3e0, 0xc3e2, 0xc3e8, 0xc3e9, 0xc3ed, 0xc3f4, 0xc3f5,
  0xc3f8, 0xc408, 0xc410, 0xc424, 0xc42c, 0xc430,
  /* 0x3e */
  0xc434, 0xc43c, 0xc43d, 0xc448, 0xc464, 0xc465, 0xc468, 0xc46c,
  0xc474, 0xc475, 0xc479, 0xc480, 0xc494, 0xc49c, 0xc4b8, 0xc4bc,
  0xc4e9, 0xc4f0, 0xc4f1, 0xc4f4, 0xc4f8, 0xc4fa, 0xc4ff, 0xc500,
  0xc501, 0xc50c, 0xc510, 0xc514, 0xc51c, 0xc528, 0xc529, 0xc52c,
  0xc530, 0xc538, 0xc539, 0xc53b, 0xc53d, 0xc544, 0xc545, 0xc548,
  0xc549, 0xc54a, 0xc54c, 0xc54d, 0xc54e, 0xc553, 0xc554, 0xc555,
  0xc557, 0xc558, 0xc559, 0xc55d, 0xc55e, 0xc560, 0xc561, 0xc564,
  0xc568, 0xc570, 0xc571, 0xc573, 0xc574, 0xc575, 0xc57c, 0xc57d,
  0xc580, 0xc584, 0xc587, 0xc58c, 0xc58d, 0xc58f, 0xc591, 0xc595,
  0xc597, 0xc598, 0xc59c, 0xc5a0, 0xc5a9, 0xc5b4, 0xc5b5, 0xc5b8,
  0xc5b9, 0xc5bb, 0xc5bc, 0xc5bd, 0xc5be, 0xc5c4, 0xc5c5, 0xc5c6,
  0xc5c7, 0xc5c8, 0xc5c9, 0xc5ca, 0xc5cc, 0xc5ce,
  /* 0x3f */
  0xc5d0, 0xc5d1, 0xc5d4, 0xc5d8, 0xc5e0, 0xc5e1, 0xc5e3, 0xc5e5,
  0xc5ec, 0xc5ed, 0xc5ee, 0xc5f0, 0xc5f4, 0xc5f6, 0xc5f7, 0xc5fc,
  0xc5fd, 0xc5fe, 0xc5ff, 0xc600, 0xc601, 0xc605, 0xc606, 0xc607,
  0xc608, 0xc60c, 0xc610, 0xc618, 0xc619, 0xc61b, 0xc61c, 0xc624,
  0xc625, 0xc628, 0xc62c, 0xc62d, 0xc62e, 0xc630, 0xc633, 0xc634,
  0xc635, 0xc637, 0xc639, 0xc63b, 0xc640, 0xc641, 0xc644, 0xc648,
  0xc650, 0xc651, 0xc653, 0xc654, 0xc655, 0xc65c, 0xc65d, 0xc660,
  0xc66c, 0xc66f, 0xc671, 0xc678, 0xc679, 0xc67c, 0xc680, 0xc688,
  0xc689, 0xc68b, 0xc68d, 0xc694, 0xc695, 0xc698, 0xc69c, 0xc6a4,
  0xc6a5, 0xc6a7, 0xc6a9, 0xc6b0, 0xc6b1, 0xc6b4, 0xc6b8, 0xc6b9,
  0xc6ba, 0xc6c0, 0xc6c1, 0xc6c3, 0xc6c5, 0xc6cc, 0xc6cd, 0xc6d0,
  0xc6d4, 0xc6dc, 0xc6dd, 0xc6e0, 0xc6e1, 0xc6e8,
  /* 0x40 */
  0xc6e9, 0xc6ec, 0xc6f0, 0xc6f8, 0xc6f9, 0xc6fd, 0xc704, 0xc705,
  0xc708, 0xc70c, 0xc714, 0xc715, 0xc717, 0xc719, 0xc720, 0xc721,
  0xc724, 0xc728, 0xc730, 0xc731, 0xc733, 0xc735, 0xc737, 0xc73c,
  0xc73d, 0xc740, 0xc744, 0xc74a, 0xc74c, 0xc74d, 0xc74f, 0xc751,
  0xc752, 0xc753, 0xc754, 0xc755, 0xc756, 0xc757, 0xc758, 0xc75c,
  0xc760, 0xc768, 0xc76b, 0xc774, 0xc775, 0xc778, 0xc77c, 0xc77d,
  0xc77e, 0xc783, 0xc784, 0xc785, 0xc787, 0xc788, 0xc789, 0xc78a,
  0xc78e, 0xc790, 0xc791, 0xc794, 0xc796, 0xc797, 0xc798, 0xc79a,
  0xc7a0, 0xc7a1, 0xc7a3, 0xc7a4, 0xc7a5, 0xc7a6, 0xc7ac, 0xc7ad,
  0xc7b0, 0xc7b4, 0xc7bc, 0xc7bd, 0xc7bf, 0xc7c0, 0xc7c1, 0xc7c8,
  0xc7c9, 0xc7cc, 0xc7ce, 0xc7d0, 0xc7d8, 0xc7dd, 0xc7e4, 0xc7e8,
  0xc7ec, 0xc800, 0xc801, 0xc804, 0xc808, 0xc80a,
  /* 0x41 */
  0xc810, 0xc811, 0xc813, 0xc815, 0xc816, 0xc81c, 0xc81d, 0xc820,
  0xc824, 0xc82c, 0xc82d, 0xc82f, 0xc831, 0xc838, 0xc83c, 0xc840,
  0xc848, 0xc849, 0xc84c, 0xc84d, 0xc854, 0xc870, 0xc871, 0xc874,
  0xc878, 0xc87a, 0xc880, 0xc881, 0xc883, 0xc885, 0xc886, 0xc887,
  0xc88b, 0xc88c, 0xc88d, 0xc894, 0xc89d, 0xc89f, 0xc8a1, 0xc8a8,
  0xc8bc, 0xc8bd, 0xc8c4, 0xc8c8, 0xc8cc, 0xc8d4, 0xc8d5, 0xc8d7,
  0xc8d9, 0xc8e0, 0xc8e1, 0xc8e4, 0xc8f5, 0xc8fc, 0xc8fd, 0xc900,
  0xc904, 0xc905, 0xc906, 0xc90c, 0xc90d, 0xc90f, 0xc911, 0xc918,
  0xc92c, 0xc934, 0xc950, 0xc951, 0xc954, 0xc958, 0xc960, 0xc961,
  0xc963, 0xc96c, 0xc970, 0xc974, 0xc97c, 0xc988, 0xc989, 0xc98c,
  0xc990, 0xc998, 0xc999, 0xc99b, 0xc99d, 0xc9c0, 0xc9c1, 0xc9c4,
  0xc9c7, 0xc9c8, 0xc9ca, 0xc9d0, 0xc9d1, 0xc9d3,
  /* 0x42 */
  0xc9d5, 0xc9d6, 0xc9d9, 0xc9da, 0xc9dc, 0xc9dd, 0xc9e0, 0xc9e2,
  0xc9e4, 0xc9e7, 0xc9ec, 0xc9ed, 0xc9ef, 0xc9f0, 0xc9f1, 0xc9f8,
  0xc9f9, 0xc9fc, 0xca00, 0xca08, 0xca09, 0xca0b, 0xca0c, 0xca0d,
  0xca14, 0xca18, 0xca29, 0xca4c, 0xca4d, 0xca50, 0xca54, 0xca5c,
  0xca5d, 0xca5f, 0xca60, 0xca61, 0xca68, 0xca7d, 0xca84, 0xca98,
  0xcabc, 0xcabd, 0xcac0, 0xcac4, 0xcacc, 0xcacd, 0xcacf, 0xcad1,
  0xcad3, 0xcad8, 0xcad9, 0xcae0, 0xcaec, 0xcaf4, 0xcb08, 0xcb10,
  0xcb14, 0xcb18, 0xcb20, 0xcb21, 0xcb41, 0xcb48, 0xcb49, 0xcb4c,
  0xcb50, 0xcb58, 0xcb59, 0xcb5d, 0xcb64, 0xcb78, 0xcb79, 0xcb9c,
  0xcbb8, 0xcbd4, 0xcbe4, 0xcbe7, 0xcbe9, 0xcc0c, 0xcc0d, 0xcc10,
  0xcc14, 0xcc1c, 0xcc1d, 0xcc21, 0xcc22, 0xcc27, 0xcc28, 0xcc29,
  0xcc2c, 0xcc2e, 0xcc30, 0xcc38, 0xcc39, 0xcc3b,
  /* 0x43 */
  0xcc3c, 0xcc3d, 0xcc3e, 0xcc44, 0xcc45, 0xcc48, 0xcc4c, 0xcc54,
  0xcc55, 0xcc57, 0xcc58, 0xcc59, 0xcc60, 0xcc64, 0xcc66, 0xcc68,
  0xcc70, 0xcc75, 0xcc98, 0xcc99, 0xcc9c, 0xcca0, 0xcca8, 0xcca9,
  0xccab, 0xccac, 0xccad, 0xccb4, 0xccb5, 0xccb8, 0xccbc, 0xccc4,
  0xccc5, 0xccc7, 0xccc9, 0xccd0, 0xccd4, 0xcce4, 0xccec, 0xccf0,
  0xcd01, 0xcd08, 0xcd09, 0xcd0c, 0xcd10, 0xcd18, 0xcd19, 0xcd1b,
  0xcd1d, 0xcd24, 0xcd28, 0xcd2c, 0xcd39, 0xcd5c, 0xcd60, 0xcd64,
  0xcd6c, 0xcd6d, 0xcd6f, 0xcd71, 0xcd78, 0xcd88, 0xcd94, 0xcd95,
  0xcd98, 0xcd9c, 0xcda4, 0xcda5, 0xcda7, 0xcda9, 0xcdb0, 0xcdc4,
  0xcdcc, 0xcdd0, 0xcde8, 0xcdec, 0xcdf0, 0xcdf8, 0xcdf9, 0xcdfb,
  0xcdfd, 0xce04, 0xce08, 0xce0c, 0xce14, 0xce19, 0xce20, 0xce21,
  0xce24, 0xce28, 0xce30, 0xce31, 0xce33, 0xce35,
  /* 0x44 */
  0xce58, 0xce59, 0xce5c, 0xce5f, 0xce60, 0xce61, 0xce68, 0xce69,
  0xce6b, 0xce6d, 0xce74, 0xce75, 0xce78, 0xce7c, 0xce84, 0xce85,
  0xce87, 0xce89, 0xce90, 0xce91, 0xce94, 0xce98, 0xcea0, 0xcea1,
  0xcea3, 0xcea4, 0xcea5, 0xceac, 0xcead, 0xcec1, 0xcee4, 0xcee5,
  0xcee8, 0xceeb, 0xceec, 0xcef4, 0xcef5, 0xcef7, 0xcef8, 0xcef9,
  0xcf00, 0xcf01, 0xcf04, 0xcf08, 0xcf10, 0xcf11, 0xcf13, 0xcf15,
  0xcf1c, 0xcf20, 0xcf24, 0xcf2c, 0xcf2d, 0xcf2f, 0xcf30, 0xcf31,
  0xcf38, 0xcf54, 0xcf55, 0xcf58, 0xcf5c, 0xcf64, 0xcf65, 0xcf67,
  0xcf69, 0xcf70, 0xcf71, 0xcf74, 0xcf78, 0xcf80, 0xcf85, 0xcf8c,
  0xcfa1, 0xcfa8, 0xcfb0, 0xcfc4, 0xcfe0, 0xcfe1, 0xcfe4, 0xcfe8,
  0xcff0, 0xcff1, 0xcff3, 0xcff5, 0xcffc, 0xd000, 0xd004, 0xd011,
  0xd018, 0xd02d, 0xd034, 0xd035, 0xd038, 0xd03c,
  /* 0x45 */
  0xd044, 0xd045, 0xd047, 0xd049, 0xd050, 0xd054, 0xd058, 0xd060,
  0xd06c, 0xd06d, 0xd070, 0xd074, 0xd07c, 0xd07d, 0xd081, 0xd0a4,
  0xd0a5, 0xd0a8, 0xd0ac, 0xd0b4, 0xd0b5, 0xd0b7, 0xd0b9, 0xd0c0,
  0xd0c1, 0xd0c4, 0xd0c8, 0xd0c9, 0xd0d0, 0xd0d1, 0xd0d3, 0xd0d4,
  0xd0d5, 0xd0dc, 0xd0dd, 0xd0e0, 0xd0e4, 0xd0ec, 0xd0ed, 0xd0ef,
  0xd0f0, 0xd0f1, 0xd0f8, 0xd10d, 0xd130, 0xd131, 0xd134, 0xd138,
  0xd13a, 0xd140, 0xd141, 0xd143, 0xd144, 0xd145, 0xd14c, 0xd14d,
  0xd150, 0xd154, 0xd15c, 0xd15d, 0xd15f, 0xd161, 0xd168, 0xd16c,
  0xd17c, 0xd184, 0xd188, 0xd1a0, 0xd1a1, 0xd1a4, 0xd1a8, 0xd1b0,
  0xd1b1, 0xd1b3, 0xd1b5, 0xd1ba, 0xd1bc, 0xd1c0, 0xd1d8, 0xd1f4,
  0xd1f8, 0xd207, 0xd209, 0xd210, 0xd22c, 0xd22d, 0xd230, 0xd234,
  0xd23c, 0xd23d, 0xd23f, 0xd241, 0xd248, 0xd25c,
  /* 0x46 */
  0xd264, 0xd280, 0xd281, 0xd284, 0xd288, 0xd290, 0xd291, 0xd295,
  0xd29c, 0xd2a0, 0xd2a4, 0xd2ac, 0xd2b1, 0xd2b8, 0xd2b9, 0xd2bc,
  0xd2bf, 0xd2c0, 0xd2c2, 0xd2c8, 0xd2c9, 0xd2cb, 0xd2d4, 0xd2d8,
  0xd2dc, 0xd2e4, 0xd2e5, 0xd2f0, 0xd2f1, 0xd2f4, 0xd2f8, 0xd300,
  0xd301, 0xd303, 0xd305, 0xd30c, 0xd30d, 0xd30e, 0xd310, 0xd314,
  0xd316, 0xd31c, 0xd31d, 0xd31f, 0xd320, 0xd321, 0xd325, 0xd328,
  0xd329, 0xd32c, 0xd330, 0xd338, 0xd339, 0xd33b, 0xd33c, 0xd33d,
  0xd344, 0xd345, 0xd37c, 0xd37d, 0xd380, 0xd384, 0xd38c, 0xd38d,
  0xd38f, 0xd390, 0xd391, 0xd398, 0xd399, 0xd39c, 0xd3a0, 0xd3a8,
  0xd3a9, 0xd3ab, 0xd3ad, 0xd3b4, 0xd3b8, 0xd3bc, 0xd3c4, 0xd3c5,
  0xd3c8, 0xd3c9, 0xd3d0, 0xd3d8, 0xd3e1, 0xd3e3, 0xd3ec, 0xd3ed,
  0xd3f0, 0xd3f4, 0xd3fc, 0xd3fd, 0xd3ff, 0xd401,
  /* 0x47 */
  0xd408, 0xd41d, 0xd440, 0xd444, 0xd45c, 0xd460, 0xd464, 0xd46d,
  0xd46f, 0xd478, 0xd479, 0xd47c, 0xd47f, 0xd480, 0xd482, 0xd488,
  0xd489, 0xd48b, 0xd48d, 0xd494, 0xd4a9, 0xd4cc, 0xd4d0, 0xd4d4,
  0xd4dc, 0xd4df, 0xd4e8, 0xd4ec, 0xd4f0, 0xd4f8, 0xd4fb, 0xd4fd,
  0xd504, 0xd508, 0xd50c, 0xd514, 0xd515, 0xd517, 0xd53c, 0xd53d,
  0xd540, 0xd544, 0xd54c, 0xd54d, 0xd54f, 0xd551, 0xd558, 0xd559,
  0xd55c, 0xd560, 0xd565, 0xd568, 0xd569, 0xd56b, 0xd56d, 0xd574,
  0xd575, 0xd578, 0xd57c, 0xd584, 0xd585, 0xd587, 0xd588, 0xd589,
  0xd590, 0xd5a5, 0xd5c8, 0xd5c9, 0xd5cc, 0xd5d0, 0xd5d2, 0xd5d8,
  0xd5d9, 0xd5db, 0xd5dd, 0xd5e4, 0xd5e5, 0xd5e8, 0xd5ec, 0xd5f4,
  0xd5f5, 0xd5f7, 0xd5f9, 0xd600, 0xd601, 0xd604, 0xd608, 0xd610,
  0xd611, 0xd613, 0xd614, 0xd615, 0xd61c, 0xd620,
  /* 0x48 */
  0xd624, 0xd62d, 0xd638, 0xd639, 0xd63c, 0xd640, 0xd645, 0xd648,
  0xd649, 0xd64b, 0xd64d, 0xd651, 0xd654, 0xd655, 0xd658, 0xd65c,
  0xd667, 0xd669, 0xd670, 0xd671, 0xd674, 0xd683, 0xd685, 0xd68c,
  0xd68d, 0xd690, 0xd694, 0xd69d, 0xd69f, 0xd6a1, 0xd6a8, 0xd6ac,
  0xd6b0, 0xd6b9, 0xd6bb, 0xd6c4, 0xd6c5, 0xd6c8, 0xd6cc, 0xd6d1,
  0xd6d4, 0xd6d7, 0xd6d9, 0xd6e0, 0xd6e4, 0xd6e8, 0xd6f0, 0xd6f5,
  0xd6fc, 0xd6fd, 0xd700, 0xd704, 0xd711, 0xd718, 0xd719, 0xd71c,
  0xd720, 0xd728, 0xd729, 0xd72b, 0xd72d, 0xd734, 0xd735, 0xd738,
  0xd73c, 0xd744, 0xd747, 0xd749, 0xd750, 0xd751, 0xd754, 0xd756,
  0xd757, 0xd758, 0xd759, 0xd760, 0xd761, 0xd763, 0xd765, 0xd769,
  0xd76c, 0xd770, 0xd774, 0xd77c, 0xd77d, 0xd781, 0xd788, 0xd789,
  0xd78c, 0xd790, 0xd798, 0xd799, 0xd79b, 0xd79d,
};
static const unsigned short ksc5601_2uni_page4a[4888] = {
  /* 0x4a */
  0x4f3d, 0x4f73, 0x5047, 0x50f9, 0x52a0, 0x53ef, 0x5475, 0x54e5,
  0x5609, 0x5ac1, 0x5bb6, 0x6687, 0x67b6, 0x67b7, 0x67ef, 0x6b4c,
  0x73c2, 0x75c2, 0x7a3c, 0x82db, 0x8304, 0x8857, 0x8888, 0x8a36,
  0x8cc8, 0x8dcf, 0x8efb, 0x8fe6, 0x99d5, 0x523b, 0x5374, 0x5404,
  0x606a, 0x6164, 0x6bbc, 0x73cf, 0x811a, 0x89ba, 0x89d2, 0x95a3,
  0x4f83, 0x520a, 0x58be, 0x5978, 0x59e6, 0x5e72, 0x5e79, 0x61c7,
  0x63c0, 0x6746, 0x67ec, 0x687f, 0x6f97, 0x764e, 0x770b, 0x78f5,
  0x7a08, 0x7aff, 0x7c21, 0x809d, 0x826e, 0x8271, 0x8aeb, 0x9593,
  0x4e6b, 0x559d, 0x66f7, 0x6e34, 0x78a3, 0x7aed, 0x845b, 0x8910,
  0x874e, 0x97a8, 0x52d8, 0x574e, 0x582a, 0x5d4c, 0x611f, 0x61be,
  0x6221, 0x6562, 0x67d1, 0x6a44, 0x6e1b, 0x7518, 0x75b3, 0x76e3,
  0x77b0, 0x7d3a, 0x90af, 0x9451, 0x9452, 0x9f95,
  /* 0x4b */
  0x5323, 0x5cac, 0x7532, 0x80db, 0x9240, 0x9598, 0x525b, 0x5808,
  0x59dc, 0x5ca1, 0x5d17, 0x5eb7, 0x5f3a, 0x5f4a, 0x6177, 0x6c5f,
  0x757a, 0x7586, 0x7ce0, 0x7d73, 0x7db1, 0x7f8c, 0x8154, 0x8221,
  0x8591, 0x8941, 0x8b1b, 0x92fc, 0x964d, 0x9c47, 0x4ecb, 0x4ef7,
  0x500b, 0x51f1, 0x584f, 0x6137, 0x613e, 0x6168, 0x6539, 0x69ea,
  0x6f11, 0x75a5, 0x7686, 0x76d6, 0x7b87, 0x82a5, 0x84cb, 0xf900,
  0x93a7, 0x958b, 0x5580, 0x5ba2, 0x5751, 0xf901, 0x7cb3, 0x7fb9,
  0x91b5, 0x5028, 0x53bb, 0x5c45, 0x5de8, 0x62d2, 0x636e, 0x64da,
  0x64e7, 0x6e20, 0x70ac, 0x795b, 0x8ddd, 0x8e1e, 0xf902, 0x907d,
  0x9245, 0x92f8, 0x4e7e, 0x4ef6, 0x5065, 0x5dfe, 0x5efa, 0x6106,
  0x6957, 0x8171, 0x8654, 0x8e47, 0x9375, 0x9a2b, 0x4e5e, 0x5091,
  0x6770, 0x6840, 0x5109, 0x528d, 0x5292, 0x6aa2,
  /* 0x4c */
  0x77bc, 0x9210, 0x9ed4, 0x52ab, 0x602f, 0x8ff2, 0x5048, 0x61a9,
  0x63ed, 0x64ca, 0x683c, 0x6a84, 0x6fc0, 0x8188, 0x89a1, 0x9694,
  0x5805, 0x727d, 0x72ac, 0x7504, 0x7d79, 0x7e6d, 0x80a9, 0x898b,
  0x8b74, 0x9063, 0x9d51, 0x6289, 0x6c7a, 0x6f54, 0x7d50, 0x7f3a,
  0x8a23, 0x517c, 0x614a, 0x7b9d, 0x8b19, 0x9257, 0x938c, 0x4eac,
  0x4fd3, 0x501e, 0x50be, 0x5106, 0x52c1, 0x52cd, 0x537f, 0x5770,
  0x5883, 0x5e9a, 0x5f91, 0x6176, 0x61ac, 0x64ce, 0x656c, 0x666f,
  0x66bb, 0x66f4, 0x6897, 0x6d87, 0x7085, 0x70f1, 0x749f, 0x74a5,
  0x74ca, 0x75d9, 0x786c, 0x78ec, 0x7adf, 0x7af6, 0x7d45, 0x7d93,
  0x8015, 0x803f, 0x811b, 0x8396, 0x8b66, 0x8f15, 0x9015, 0x93e1,
  0x9803, 0x9838, 0x9a5a, 0x9be8, 0x4fc2, 0x5553, 0x583a, 0x5951,
  0x5b63, 0x5c46, 0x60b8, 0x6212, 0x6842, 0x68b0,
  /* 0x4d */
  0x68e8, 0x6eaa, 0x754c, 0x7678, 0x78ce, 0x7a3d, 0x7cfb, 0x7e6b,
  0x7e7c, 0x8a08, 0x8aa1, 0x8c3f, 0x968e, 0x9dc4, 0x53e4, 0x53e9,
  0x544a, 0x5471, 0x56fa, 0x59d1, 0x5b64, 0x5c3b, 0x5eab, 0x62f7,
  0x6537, 0x6545, 0x6572, 0x66a0, 0x67af, 0x69c1, 0x6cbd, 0x75fc,
  0x7690, 0x777e, 0x7a3f, 0x7f94, 0x8003, 0x80a1, 0x818f, 0x82e6,
  0x82fd, 0x83f0, 0x85c1, 0x8831, 0x88b4, 0x8aa5, 0xf903, 0x8f9c,
  0x932e, 0x96c7, 0x9867, 0x9ad8, 0x9f13, 0x54ed, 0x659b, 0x66f2,
  0x688f, 0x7a40, 0x8c37, 0x9d60, 0x56f0, 0x5764, 0x5d11, 0x6606,
  0x68b1, 0x68cd, 0x6efe, 0x7428, 0x889e, 0x9be4, 0x6c68, 0xf904,
  0x9aa8, 0x4f9b, 0x516c, 0x5171, 0x529f, 0x5b54, 0x5de5, 0x6050,
  0x606d, 0x62f1, 0x63a7, 0x653b, 0x73d9, 0x7a7a, 0x86a3, 0x8ca2,
  0x978f, 0x4e32, 0x5be1, 0x6208, 0x679c, 0x74dc,
  /* 0x4e */
  0x79d1, 0x83d3, 0x8a87, 0x8ab2, 0x8de8, 0x904e, 0x934b, 0x9846,
  0x5ed3, 0x69e8, 0x85ff, 0x90ed, 0xf905, 0x51a0, 0x5b98, 0x5bec,
  0x6163, 0x68fa, 0x6b3e, 0x704c, 0x742f, 0x74d8, 0x7ba1, 0x7f50,
  0x83c5, 0x89c0, 0x8cab, 0x95dc, 0x9928, 0x522e, 0x605d, 0x62ec,
  0x9002, 0x4f8a, 0x5149, 0x5321, 0x58d9, 0x5ee3, 0x66e0, 0x6d38,
  0x709a, 0x72c2, 0x73d6, 0x7b50, 0x80f1, 0x945b, 0x5366, 0x639b,
  0x7f6b, 0x4e56, 0x5080, 0x584a, 0x58de, 0x602a, 0x6127, 0x62d0,
  0x69d0, 0x9b41, 0x5b8f, 0x7d18, 0x80b1, 0x8f5f, 0x4ea4, 0x50d1,
  0x54ac, 0x55ac, 0x5b0c, 0x5da0, 0x5de7, 0x652a, 0x654e, 0x6821,
  0x6a4b, 0x72e1, 0x768e, 0x77ef, 0x7d5e, 0x7ff9, 0x81a0, 0x854e,
  0x86df, 0x8f03, 0x8f4e, 0x90ca, 0x9903, 0x9a55, 0x9bab, 0x4e18,
  0x4e45, 0x4e5d, 0x4ec7, 0x4ff1, 0x5177, 0x52fe,
  /* 0x4f */
  0x5340, 0x53e3, 0x53e5, 0x548e, 0x5614, 0x5775, 0x57a2, 0x5bc7,
  0x5d87, 0x5ed0, 0x61fc, 0x62d8, 0x6551, 0x67b8, 0x67e9, 0x69cb,
  0x6b50, 0x6bc6, 0x6bec, 0x6c42, 0x6e9d, 0x7078, 0x72d7, 0x7396,
  0x7403, 0x77bf, 0x77e9, 0x7a76, 0x7d7f, 0x8009, 0x81fc, 0x8205,
  0x820a, 0x82df, 0x8862, 0x8b33, 0x8cfc, 0x8ec0, 0x9011, 0x90b1,
  0x9264, 0x92b6, 0x99d2, 0x9a45, 0x9ce9, 0x9dd7, 0x9f9c, 0x570b,
  0x5c40, 0x83ca, 0x97a0, 0x97ab, 0x9eb4, 0x541b, 0x7a98, 0x7fa4,
  0x88d9, 0x8ecd, 0x90e1, 0x5800, 0x5c48, 0x6398, 0x7a9f, 0x5bae,
  0x5f13, 0x7a79, 0x7aae, 0x828e, 0x8eac, 0x5026, 0x5238, 0x52f8,
  0x5377, 0x5708, 0x62f3, 0x6372, 0x6b0a, 0x6dc3, 0x7737, 0x53a5,
  0x7357, 0x8568, 0x8e76, 0x95d5, 0x673a, 0x6ac3, 0x6f70, 0x8a6d,
  0x8ecc, 0x994b, 0xf906, 0x6677, 0x6b78, 0x8cb4,
  /* 0x50 */
  0x9b3c, 0xf907, 0x53eb, 0x572d, 0x594e, 0x63c6, 0x69fb, 0x73ea,
  0x7845, 0x7aba, 0x7ac5, 0x7cfe, 0x8475, 0x898f, 0x8d73, 0x9035,
  0x95a8, 0x52fb, 0x5747, 0x7547, 0x7b60, 0x83cc, 0x921e, 0xf908,
  0x6a58, 0x514b, 0x524b, 0x5287, 0x621f, 0x68d8, 0x6975, 0x9699,
  0x50c5, 0x52a4, 0x52e4, 0x61c3, 0x65a4, 0x6839, 0x69ff, 0x747e,
  0x7b4b, 0x82b9, 0x83eb, 0x89b2, 0x8b39, 0x8fd1, 0x9949, 0xf909,
  0x4eca, 0x5997, 0x64d2, 0x6611, 0x6a8e, 0x7434, 0x7981, 0x79bd,
  0x82a9, 0x887e, 0x887f, 0x895f, 0xf90a, 0x9326, 0x4f0b, 0x53ca,
  0x6025, 0x6271, 0x6c72, 0x7d1a, 0x7d66, 0x4e98, 0x5162, 0x77dc,
  0x80af, 0x4f01, 0x4f0e, 0x5176, 0x5180, 0x55dc, 0x5668, 0x573b,
  0x57fa, 0x57fc, 0x5914, 0x5947, 0x5993, 0x5bc4, 0x5c90, 0x5d0e,
  0x5df1, 0x5e7e, 0x5fcc, 0x6280, 0x65d7, 0x65e3,
  /* 0x51 */
  0x671e, 0x671f, 0x675e, 0x68cb, 0x68c4, 0x6a5f, 0x6b3a, 0x6c23,
  0x6c7d, 0x6c82, 0x6dc7, 0x7398, 0x7426, 0x742a, 0x7482, 0x74a3,
  0x7578, 0x757f, 0x7881, 0x78ef, 0x7941, 0x7947, 0x7948, 0x797a,
  0x7b95, 0x7d00, 0x7dba, 0x7f88, 0x8006, 0x802d, 0x808c, 0x8a18,
  0x8b4f, 0x8c48, 0x8d77, 0x9321, 0x9324, 0x98e2, 0x9951, 0x9a0e,
  0x9a0f, 0x9a65, 0x9e92, 0x7dca, 0x4f76, 0x5409, 0x62ee, 0x6854,
  0x91d1, 0x55ab, 0x513a, 0xf90b, 0xf90c, 0x5a1c, 0x61e6, 0xf90d,
  0x62cf, 0x62ff, 0xf90e, 0xf90f, 0xf910, 0xf911, 0xf912, 0xf913,
  0x90a3, 0xf914, 0xf915, 0xf916, 0xf917, 0xf918, 0x8afe, 0xf919,
  0xf91a, 0xf91b, 0xf91c, 0x6696, 0xf91d, 0x7156, 0xf91e, 0xf91f,
  0x96e3, 0xf920, 0x634f, 0x637a, 0x5357, 0xf921, 0x678f, 0x6960,
  0x6e73, 0xf922, 0x7537, 0xf923, 0xf924, 0xf925,
  /* 0x52 */
  0x7d0d, 0xf926, 0xf927, 0x8872, 0x56ca, 0x5a18, 0xf928, 0xf929,
  0xf92a, 0xf92b, 0xf92c, 0x4e43, 0xf92d, 0x5167, 0x5948, 0x67f0,
  0x8010, 0xf92e, 0x5973, 0x5e74, 0x649a, 0x79ca, 0x5ff5, 0x606c,
  0x62c8, 0x637b, 0x5be7, 0x5bd7, 0x52aa, 0xf92f, 0x5974, 0x5f29,
  0x6012, 0xf930, 0xf931, 0xf932, 0x7459, 0xf933, 0xf934, 0xf935,
  0xf936, 0xf937, 0xf938, 0x99d1, 0xf939, 0xf93a, 0xf93b, 0xf93c,
  0xf93d, 0xf93e, 0xf93f, 0xf940, 0xf941, 0xf942, 0xf943, 0x6fc3,
  0xf944, 0xf945, 0x81bf, 0x8fb2, 0x60f1, 0xf946, 0xf947, 0x8166,
  0xf948, 0xf949, 0x5c3f, 0xf94a, 0xf94b, 0xf94c, 0xf94d, 0xf94e,
  0xf94f, 0xf950, 0xf951, 0x5ae9, 0x8a25, 0x677b, 0x7d10, 0xf952,
  0xf953, 0xf954, 0xf955, 0xf956, 0xf957, 0x80fd, 0xf958, 0xf959,
  0x5c3c, 0x6ce5, 0x533f, 0x6eba, 0x591a, 0x8336,
  /* 0x53 */
  0x4e39, 0x4eb6, 0x4f46, 0x55ae, 0x5718, 0x58c7, 0x5f56, 0x65b7,
  0x65e6, 0x6a80, 0x6bb5, 0x6e4d, 0x77ed, 0x7aef, 0x7c1e, 0x7dde,
  0x86cb, 0x8892, 0x9132, 0x935b, 0x64bb, 0x6fbe, 0x737a, 0x75b8,
  0x9054, 0x5556, 0x574d, 0x61ba, 0x64d4, 0x66c7, 0x6de1, 0x6e5b,
  0x6f6d, 0x6fb9, 0x75f0, 0x8043, 0x81bd, 0x8541, 0x8983, 0x8ac7,
  0x8b5a, 0x931f, 0x6c93, 0x7553, 0x7b54, 0x8e0f, 0x905d, 0x5510,
  0x5802, 0x5858, 0x5e62, 0x6207, 0x649e, 0x68e0, 0x7576, 0x7cd6,
  0x87b3, 0x9ee8, 0x4ee3, 0x5788, 0x576e, 0x5927, 0x5c0d, 0x5cb1,
  0x5e36, 0x5f85, 0x6234, 0x64e1, 0x73b3, 0x81fa, 0x888b, 0x8cb8,
  0x968a, 0x9edb, 0x5b85, 0x5fb7, 0x60b3, 0x5012, 0x5200, 0x5230,
  0x5716, 0x5835, 0x5857, 0x5c0e, 0x5c60, 0x5cf6, 0x5d8b, 0x5ea6,
  0x5f92, 0x60bc, 0x6311, 0x6389, 0x6417, 0x6843,
  /* 0x54 */
  0x68f9, 0x6ac2, 0x6dd8, 0x6e21, 0x6ed4, 0x6fe4, 0x71fe, 0x76dc,
  0x7779, 0x79b1, 0x7a3b, 0x8404, 0x89a9, 0x8ced, 0x8df3, 0x8e48,
  0x9003, 0x9014, 0x9053, 0x90fd, 0x934d, 0x9676, 0x97dc, 0x6bd2,
  0x7006, 0x7258, 0x72a2, 0x7368, 0x7763, 0x79bf, 0x7be4, 0x7e9b,
  0x8b80, 0x58a9, 0x60c7, 0x6566, 0x65fd, 0x66be, 0x6c8c, 0x711e,
  0x71c9, 0x8c5a, 0x9813, 0x4e6d, 0x7a81, 0x4edd, 0x51ac, 0x51cd,
  0x52d5, 0x540c, 0x61a7, 0x6771, 0x6850, 0x68df, 0x6d1e, 0x6f7c,
  0x75bc, 0x77b3, 0x7ae5, 0x80f4, 0x8463, 0x9285, 0x515c, 0x6597,
  0x675c, 0x6793, 0x75d8, 0x7ac7, 0x8373, 0xf95a, 0x8c46, 0x9017,
  0x982d, 0x5c6f, 0x81c0, 0x829a, 0x9041, 0x906f, 0x920d, 0x5f97,
  0x5d9d, 0x6a59, 0x71c8, 0x767b, 0x7b49, 0x85e4, 0x8b04, 0x9127,
  0x9a30, 0x5587, 0x61f6, 0xf95b, 0x7669, 0x7f85,
  /* 0x55 */
  0x863f, 0x87ba, 0x88f8, 0x908f, 0xf95c, 0x6d1b, 0x70d9, 0x73de,
  0x7d61, 0x843d, 0xf95d, 0x916a, 0x99f1, 0xf95e, 0x4e82, 0x5375,
  0x6b04, 0x6b12, 0x703e, 0x721b, 0x862d, 0x9e1e, 0x524c, 0x8fa3,
  0x5d50, 0x64e5, 0x652c, 0x6b16, 0x6feb, 0x7c43, 0x7e9c, 0x85cd,
  0x8964, 0x89bd, 0x62c9, 0x81d8, 0x881f, 0x5eca, 0x6717, 0x6d6a,
  0x72fc, 0x7405, 0x746f, 0x8782, 0x90de, 0x4f86, 0x5d0d, 0x5fa0,
  0x840a, 0x51b7, 0x63a0, 0x7565, 0x4eae, 0x5006, 0x5169, 0x51c9,
  0x6881, 0x6a11, 0x7cae, 0x7cb1, 0x7ce7, 0x826f, 0x8ad2, 0x8f1b,
  0x91cf, 0x4fb6, 0x5137, 0x52f5, 0x5442, 0x5eec, 0x616e, 0x623e,
  0x65c5, 0x6ada, 0x6ffe, 0x792a, 0x85dc, 0x8823, 0x95ad, 0x9a62,
  0x9a6a, 0x9e97, 0x9ece, 0x529b, 0x66c6, 0x6b77, 0x701d, 0x792b,
  0x8f62, 0x9742, 0x6190, 0x6200, 0x6523, 0x6f23,
  /* 0x56 */
  0x7149, 0x7489, 0x7df4, 0x806f, 0x84ee, 0x8f26, 0x9023, 0x934a,
  0x51bd, 0x5217, 0x52a3, 0x6d0c, 0x70c8, 0x88c2, 0x5ec9, 0x6582,
  0x6bae, 0x6fc2, 0x7c3e, 0x7375, 0x4ee4, 0x4f36, 0x56f9, 0xf95f,
  0x5cba, 0x5dba, 0x601c, 0x73b2, 0x7b2d, 0x7f9a, 0x7fce, 0x8046,
  0x901e, 0x9234, 0x96f6, 0x9748, 0x9818, 0x9f61, 0x4f8b, 0x6fa7,
  0x79ae, 0x91b4, 0x96b7, 0x52de, 0xf960, 0x6488, 0x64c4, 0x6ad3,
  0x6f5e, 0x7018, 0x7210, 0x76e7, 0x8001, 0x8606, 0x865c, 0x8def,
  0x8f05, 0x9732, 0x9b6f, 0x9dfa, 0x9e75, 0x788c, 0x797f, 0x7da0,
  0x83c9, 0x9304, 0x9e7f, 0x9e93, 0x8ad6, 0x58df, 0x5f04, 0x6727,
  0x7027, 0x74cf, 0x7c60, 0x807e, 0x5121, 0x7028, 0x7262, 0x78ca,
  0x8cc2, 0x8cda, 0x8cf4, 0x96f7, 0x4e86, 0x50da, 0x5bee, 0x5ed6,
  0x6599, 0x71ce, 0x7642, 0x77ad, 0x804a, 0x84fc,
  /* 0x57 */
  0x907c, 0x9b27, 0x9f8d, 0x58d8, 0x5a41, 0x5c62, 0x6a13, 0x6dda,
  0x6f0f, 0x763b, 0x7d2f, 0x7e37, 0x851e, 0x8938, 0x93e4, 0x964b,
  0x5289, 0x65d2, 0x67f3, 0x69b4, 0x6d41, 0x6e9c, 0x700f, 0x7409,
  0x7460, 0x7559, 0x7624, 0x786b, 0x8b2c, 0x985e, 0x516d, 0x622e,
  0x9678, 0x4f96, 0x502b, 0x5d19, 0x6dea, 0x7db8, 0x8f2a, 0x5f8b,
  0x6144, 0x6817, 0xf961, 0x9686, 0x52d2, 0x808b, 0x51dc, 0x51cc,
  0x695e, 0x7a1c, 0x7dbe, 0x83f1, 0x9675, 0x4fda, 0x5229, 0x5398,
  0x540f, 0x550e, 0x5c65, 0x60a7, 0x674e, 0x68a8, 0x6d6c, 0x7281,
  0x72f8, 0x7406, 0x7483, 0xf962, 0x75e2, 0x7c6c, 0x7f79, 0x7fb8,
  0x8389, 0x88cf, 0x88e1, 0x91cc, 0x91d0, 0x96e2, 0x9bc9, 0x541d,
  0x6f7e, 0x71d0, 0x7498, 0x85fa, 0x8eaa, 0x96a3, 0x9c57, 0x9e9f,
  0x6797, 0x6dcb, 0x7433, 0x81e8, 0x9716, 0x782c,
  /* 0x58 */
  0x7acb, 0x7b20, 0x7c92, 0x6469, 0x746a, 0x75f2, 0x78bc, 0x78e8,
  0x99ac, 0x9b54, 0x9ebb, 0x5bde, 0x5e55, 0x6f20, 0x819c, 0x83ab,
  0x9088, 0x4e07, 0x534d, 0x5a29, 0x5dd2, 0x5f4e, 0x6162, 0x633d,
  0x6669, 0x66fc, 0x6eff, 0x6f2b, 0x7063, 0x779e, 0x842c, 0x8513,
  0x883b, 0x8f13, 0x9945, 0x9c3b, 0x551c, 0x62b9, 0x672b, 0x6cab,
  0x8309, 0x896a, 0x977a, 0x4ea1, 0x5984, 0x5fd8, 0x5fd9, 0x671b,
  0x7db2, 0x7f54, 0x8292, 0x832b, 0x83bd, 0x8f1e, 0x9099, 0x57cb,
  0x59b9, 0x5a92, 0x5bd0, 0x6627, 0x679a, 0x6885, 0x6bcf, 0x7164,
  0x7f75, 0x8cb7, 0x8ce3, 0x9081, 0x9b45, 0x8108, 0x8c8a, 0x964c,
  0x9a40, 0x9ea5, 0x5b5f, 0x6c13, 0x731b, 0x76f2, 0x76df, 0x840c,
  0x51aa, 0x8993, 0x514d, 0x5195, 0x52c9, 0x68c9, 0x6c94, 0x7704,
  0x7720, 0x7dbf, 0x7dec, 0x9762, 0x9eb5, 0x6ec5,
  /* 0x59 */
  0x8511, 0x51a5, 0x540d, 0x547d, 0x660e, 0x669d, 0x6927, 0x6e9f,
  0x76bf, 0x7791, 0x8317, 0x84c2, 0x879f, 0x9169, 0x9298, 0x9cf4,
  0x8882, 0x4fae, 0x5192, 0x52df, 0x59c6, 0x5e3d, 0x6155, 0x6478,
  0x6479, 0x66ae, 0x67d0, 0x6a21, 0x6bcd, 0x6bdb, 0x725f, 0x7261,
  0x7441, 0x7738, 0x77db, 0x8017, 0x82bc, 0x8305, 0x8b00, 0x8b28,
  0x8c8c, 0x6728, 0x6c90, 0x7267, 0x76ee, 0x7766, 0x7a46, 0x9da9,
  0x6b7f, 0x6c92, 0x5922, 0x6726, 0x8499, 0x536f, 0x5893, 0x5999,
  0x5edf, 0x63cf, 0x6634, 0x6773, 0x6e3a, 0x732b, 0x7ad7, 0x82d7,
  0x9328, 0x52d9, 0x5deb, 0x61ae, 0x61cb, 0x620a, 0x62c7, 0x64ab,
  0x65e0, 0x6959, 0x6b66, 0x6bcb, 0x7121, 0x73f7, 0x755d, 0x7e46,
  0x821e, 0x8302, 0x856a, 0x8aa3, 0x8cbf, 0x9727, 0x9d61, 0x58a8,
  0x9ed8, 0x5011, 0x520e, 0x543b, 0x554f, 0x6587,
  /* 0x5a */
  0x6c76, 0x7d0a, 0x7d0b, 0x805e, 0x868a, 0x9580, 0x96ef, 0x52ff,
  0x6c95, 0x7269, 0x5473, 0x5a9a, 0x5c3e, 0x5d4b, 0x5f4c, 0x5fae,
  0x672a, 0x68b6, 0x6963, 0x6e3c, 0x6e44, 0x7709, 0x7c73, 0x7f8e,
  0x8587, 0x8b0e, 0x8ff7, 0x9761, 0x9ef4, 0x5cb7, 0x60b6, 0x610d,
  0x61ab, 0x654f, 0x65fb, 0x65fc, 0x6c11, 0x6cef, 0x739f, 0x73c9,
  0x7de1, 0x9594, 0x5bc6, 0x871c, 0x8b10, 0x525d, 0x535a, 0x62cd,
  0x640f, 0x64b2, 0x6734, 0x6a38, 0x6cca, 0x73c0, 0x749e, 0x7b94,
  0x7c95, 0x7e1b, 0x818a, 0x8236, 0x8584, 0x8feb, 0x96f9, 0x99c1,
  0x4f34, 0x534a, 0x53cd, 0x53db, 0x62cc, 0x642c, 0x6500, 0x6591,
  0x69c3, 0x6cee, 0x6f58, 0x73ed, 0x7554, 0x7622, 0x76e4, 0x76fc,
  0x78d0, 0x78fb, 0x792c, 0x7d46, 0x822c, 0x87e0, 0x8fd4, 0x9812,
  0x98ef, 0x52c3, 0x62d4, 0x64a5, 0x6e24, 0x6f51,
  /* 0x5b */
  0x767c, 0x8dcb, 0x91b1, 0x9262, 0x9aee, 0x9b43, 0x5023, 0x508d,
  0x574a, 0x59a8, 0x5c28, 0x5e47, 0x5f77, 0x623f, 0x653e, 0x65b9,
  0x65c1, 0x6609, 0x678b, 0x699c, 0x6ec2, 0x78c5, 0x7d21, 0x80aa,
  0x8180, 0x822b, 0x82b3, 0x84a1, 0x868c, 0x8a2a, 0x8b17, 0x90a6,
  0x9632, 0x9f90, 0x500d, 0x4ff3, 0xf963, 0x57f9, 0x5f98, 0x62dc,
  0x6392, 0x676f, 0x6e43, 0x7119, 0x76c3, 0x80cc, 0x80da, 0x88f4,
  0x88f5, 0x8919, 0x8ce0, 0x8f29, 0x914d, 0x966a, 0x4f2f, 0x4f70,
  0x5e1b, 0x67cf, 0x6822, 0x767d, 0x767e, 0x9b44, 0x5e61, 0x6a0a,
  0x7169, 0x71d4, 0x756a, 0xf964, 0x7e41, 0x8543, 0x85e9, 0x98dc,
  0x4f10, 0x7b4f, 0x7f70, 0x95a5, 0x51e1, 0x5e06, 0x68b5, 0x6c3e,
  0x6c4e, 0x6cdb, 0x72af, 0x7bc4, 0x8303, 0x6cd5, 0x743a, 0x50fb,
  0x5288, 0x58c1, 0x64d8, 0x6a97, 0x74a7, 0x7656,
  /* 0x5c */
  0x78a7, 0x8617, 0x95e2, 0x9739, 0xf965, 0x535e, 0x5f01, 0x8b8a,
  0x8fa8, 0x8faf, 0x908a, 0x5225, 0x77a5, 0x9c49, 0x9f08, 0x4e19,
  0x5002, 0x5175, 0x5c5b, 0x5e77, 0x661e, 0x663a, 0x67c4, 0x68c5,
  0x70b3, 0x7501, 0x75c5, 0x79c9, 0x7add, 0x8f27, 0x9920, 0x9a08,
  0x4fdd, 0x5821, 0x5831, 0x5bf6, 0x666e, 0x6b65, 0x6d11, 0x6e7a,
  0x6f7d, 0x73e4, 0x752b, 0x83e9, 0x88dc, 0x8913, 0x8b5c, 0x8f14,
  0x4f0f, 0x50d5, 0x5310, 0x535c, 0x5b93, 0x5fa9, 0x670d, 0x798f,
  0x8179, 0x832f, 0x8514, 0x8907, 0x8986, 0x8f39, 0x8f3b, 0x99a5,
  0x9c12, 0x672c, 0x4e76, 0x4ff8, 0x5949, 0x5c01, 0x5cef, 0x5cf0,
  0x6367, 0x68d2, 0x70fd, 0x71a2, 0x742b, 0x7e2b, 0x84ec, 0x8702,
  0x9022, 0x92d2, 0x9cf3, 0x4e0d, 0x4ed8, 0x4fef, 0x5085, 0x5256,
  0x526f, 0x5426, 0x5490, 0x57e0, 0x592b, 0x5a66,
  /* 0x5d */
  0x5b5a, 0x5b75, 0x5bcc, 0x5e9c, 0xf966, 0x6276, 0x6577, 0x65a7,
  0x6d6e, 0x6ea5, 0x7236, 0x7b26, 0x7c3f, 0x7f36, 0x8150, 0x8151,
  0x819a, 0x8240, 0x8299, 0x83a9, 0x8a03, 0x8ca0, 0x8ce6, 0x8cfb,
  0x8d74, 0x8dba, 0x90e8, 0x91dc, 0x961c, 0x9644, 0x99d9, 0x9ce7,
  0x5317, 0x5206, 0x5429, 0x5674, 0x58b3, 0x5954, 0x596e, 0x5fff,
  0x61a4, 0x626e, 0x6610, 0x6c7e, 0x711a, 0x76c6, 0x7c89, 0x7cde,
  0x7d1b, 0x82ac, 0x8cc1, 0x96f0, 0xf967, 0x4f5b, 0x5f17, 0x5f7f,
  0x62c2, 0x5d29, 0x670b, 0x68da, 0x787c, 0x7e43, 0x9d6c, 0x4e15,
  0x5099, 0x5315, 0x532a, 0x5351, 0x5983, 0x5a62, 0x5e87, 0x60b2,
  0x618a, 0x6249, 0x6279, 0x6590, 0x6787, 0x69a7, 0x6bd4, 0x6bd6,
  0x6bd7, 0x6bd8, 0x6cb8, 0xf968, 0x7435, 0x75fa, 0x7812, 0x7891,
  0x79d5, 0x79d8, 0x7c83, 0x7dcb, 0x7fe1, 0x80a5,
  /* 0x5e */
  0x813e, 0x81c2, 0x83f2, 0x871a, 0x88e8, 0x8ab9, 0x8b6c, 0x8cbb,
  0x9119, 0x975e, 0x98db, 0x9f3b, 0x56ac, 0x5b2a, 0x5f6c, 0x658c,
  0x6ab3, 0x6baf, 0x6d5c, 0x6ff1, 0x7015, 0x725d, 0x73ad, 0x8ca7,
  0x8cd3, 0x983b, 0x6191, 0x6c37, 0x8058, 0x9a01, 0x4e4d, 0x4e8b,
  0x4e9b, 0x4ed5, 0x4f3a, 0x4f3c, 0x4f7f, 0x4fdf, 0x50ff, 0x53f2,
  0x53f8, 0x5506, 0x55e3, 0x56db, 0x58eb, 0x5962, 0x5a11, 0x5beb,
  0x5bfa, 0x5c04, 0x5df3, 0x5e2b, 0x5f99, 0x601d, 0x6368, 0x659c,
  0x65af, 0x67f6, 0x67fb, 0x68ad, 0x6b7b, 0x6c99, 0x6cd7, 0x6e23,
  0x7009, 0x7345, 0x7802, 0x793e, 0x7940, 0x7960, 0x79c1, 0x7be9,
  0x7d17, 0x7d72, 0x8086, 0x820d, 0x838e, 0x84d1, 0x86c7, 0x88df,
  0x8a50, 0x8a5e, 0x8b1d, 0x8cdc, 0x8d66, 0x8fad, 0x90aa, 0x98fc,
  0x99df, 0x9e9d, 0x524a, 0xf969, 0x6714, 0xf96a,
  /* 0x5f */
  0x5098, 0x522a, 0x5c71, 0x6563, 0x6c55, 0x73ca, 0x7523, 0x759d,
  0x7b97, 0x849c, 0x9178, 0x9730, 0x4e77, 0x6492, 0x6bba, 0x715e,
  0x85a9, 0x4e09, 0xf96b, 0x6749, 0x68ee, 0x6e17, 0x829f, 0x8518,
  0x886b, 0x63f7, 0x6f81, 0x9212, 0x98af, 0x4e0a, 0x50b7, 0x50cf,
  0x511f, 0x5546, 0x55aa, 0x5617, 0x5b40, 0x5c19, 0x5ce0, 0x5e38,
  0x5e8a, 0x5ea0, 0x5ec2, 0x60f3, 0x6851, 0x6a61, 0x6e58, 0x723d,
  0x7240, 0x72c0, 0x76f8, 0x7965, 0x7bb1, 0x7fd4, 0x88f3, 0x89f4,
  0x8a73, 0x8c61, 0x8cde, 0x971c, 0x585e, 0x74bd, 0x8cfd, 0x55c7,
  0xf96c, 0x7a61, 0x7d22, 0x8272, 0x7272, 0x751f, 0x7525, 0xf96d,
  0x7b19, 0x5885, 0x58fb, 0x5dbc, 0x5e8f, 0x5eb6, 0x5f90, 0x6055,
  0x6292, 0x637f, 0x654d, 0x6691, 0x66d9, 0x66f8, 0x6816, 0x68f2,
  0x7280, 0x745e, 0x7b6e, 0x7d6e, 0x7dd6, 0x7f72,
  /* 0x60 */
  0x80e5, 0x8212, 0x85af, 0x897f, 0x8a93, 0x901d, 0x92e4, 0x9ecd,
  0x9f20, 0x5915, 0x596d, 0x5e2d, 0x60dc, 0x6614, 0x6673, 0x6790,
  0x6c50, 0x6dc5, 0x6f5f, 0x77f3, 0x78a9, 0x84c6, 0x91cb, 0x932b,
  0x4ed9, 0x50ca, 0x5148, 0x5584, 0x5b0b, 0x5ba3, 0x6247, 0x657e,
  0x65cb, 0x6e32, 0x717d, 0x7401, 0x7444, 0x7487, 0x74bf, 0x766c,
  0x79aa, 0x7dda, 0x7e55, 0x7fa8, 0x817a, 0x81b3, 0x8239, 0x861a,
  0x87ec, 0x8a75, 0x8de3, 0x9078, 0x9291, 0x9425, 0x994d, 0x9bae,
  0x5368, 0x5c51, 0x6954, 0x6cc4, 0x6d29, 0x6e2b, 0x820c, 0x859b,
  0x893b, 0x8a2d, 0x8aaa, 0x96ea, 0x9f67, 0x5261, 0x66b9, 0x6bb2,
  0x7e96, 0x87fe, 0x8d0d, 0x9583, 0x965d, 0x651d, 0x6d89, 0x71ee,
  0xf96e, 0x57ce, 0x59d3, 0x5bac, 0x6027, 0x60fa, 0x6210, 0x661f,
  0x665f, 0x7329, 0x73f9, 0x76db, 0x7701, 0x7b6c,
  /* 0x61 */
  0x8056, 0x8072, 0x8165, 0x8aa0, 0x9192, 0x4e16, 0x52e2, 0x6b72,
  0x6d17, 0x7a05, 0x7b39, 0x7d30, 0xf96f, 0x8cb0, 0x53ec, 0x562f,
  0x5851, 0x5bb5, 0x5c0f, 0x5c11, 0x5de2, 0x6240, 0x6383, 0x6414,
  0x662d, 0x68b3, 0x6cbc, 0x6d88, 0x6eaf, 0x701f, 0x70a4, 0x71d2,
  0x7526, 0x758f, 0x758e, 0x7619, 0x7b11, 0x7be0, 0x7c2b, 0x7d20,
  0x7d39, 0x852c, 0x856d, 0x8607, 0x8a34, 0x900d, 0x9061, 0x90b5,
  0x92b7, 0x97f6, 0x9a37, 0x4fd7, 0x5c6c, 0x675f, 0x6d91, 0x7c9f,
  0x7e8c, 0x8b16, 0x8d16, 0x901f, 0x5b6b, 0x5dfd, 0x640d, 0x84c0,
  0x905c, 0x98e1, 0x7387, 0x5b8b, 0x609a, 0x677e, 0x6dde, 0x8a1f,
  0x8aa6, 0x9001, 0x980c, 0x5237, 0xf970, 0x7051, 0x788e, 0x9396,
  0x8870, 0x91d7, 0x4fee, 0x53d7, 0x55fd, 0x56da, 0x5782, 0x58fd,
  0x5ac2, 0x5b88, 0x5cab, 0x5cc0, 0x5e25, 0x6101,
  /* 0x62 */
  0x620d, 0x624b, 0x6388, 0x641c, 0x6536, 0x6578, 0x6a39, 0x6b8a,
  0x6c34, 0x6d19, 0x6f31, 0x71e7, 0x72e9, 0x7378, 0x7407, 0x74b2,
  0x7626, 0x7761, 0x79c0, 0x7a57, 0x7aea, 0x7cb9, 0x7d8f, 0x7dac,
  0x7e61, 0x7f9e, 0x8129, 0x8331, 0x8490, 0x84da, 0x85ea, 0x8896,
  0x8ab0, 0x8b90, 0x8f38, 0x9042, 0x9083, 0x916c, 0x9296, 0x92b9,
  0x968b, 0x96a7, 0x96a8, 0x96d6, 0x9700, 0x9808, 0x9996, 0x9ad3,
  0x9b1a, 0x53d4, 0x587e, 0x5919, 0x5b70, 0x5bbf, 0x6dd1, 0x6f5a,
  0x719f, 0x7421, 0x74b9, 0x8085, 0x83fd, 0x5de1, 0x5f87, 0x5faa,
  0x6042, 0x65ec, 0x6812, 0x696f, 0x6a53, 0x6b89, 0x6d35, 0x6df3,
  0x73e3, 0x76fe, 0x77ac, 0x7b4d, 0x7d14, 0x8123, 0x821c, 0x8340,
  0x84f4, 0x8563, 0x8a62, 0x8ac4, 0x9187, 0x931e, 0x9806, 0x99b4,
  0x620c, 0x8853, 0x8ff0, 0x9265, 0x5d07, 0x5d27,
  /* 0x63 */
  0x5d69, 0x745f, 0x819d, 0x8768, 0x6fd5, 0x62fe, 0x7fd2, 0x8936,
  0x8972, 0x4e1e, 0x4e58, 0x50e7, 0x52dd, 0x5347, 0x627f, 0x6607,
  0x7e69, 0x8805, 0x965e, 0x4f8d, 0x5319, 0x5636, 0x59cb, 0x5aa4,
  0x5c38, 0x5c4e, 0x5c4d, 0x5e02, 0x5f11, 0x6043, 0x65bd, 0x662f,
  0x6642, 0x67be, 0x67f4, 0x731c, 0x77e2, 0x793a, 0x7fc5, 0x8494,
  0x84cd, 0x8996, 0x8a66, 0x8a69, 0x8ae1, 0x8c55, 0x8c7a, 0x57f4,
  0x5bd4, 0x5f0f, 0x606f, 0x62ed, 0x690d, 0x6b96, 0x6e5c, 0x7184,
  0x7bd2, 0x8755, 0x8b58, 0x8efe, 0x98df, 0x98fe, 0x4f38, 0x4f81,
  0x4fe1, 0x547b, 0x5a20, 0x5bb8, 0x613c, 0x65b0, 0x6668, 0x71fc,
  0x7533, 0x795e, 0x7d33, 0x814e, 0x81e3, 0x8398, 0x85aa, 0x85ce,
  0x8703, 0x8a0a, 0x8eab, 0x8f9b, 0xf971, 0x8fc5, 0x5931, 0x5ba4,
  0x5be6, 0x6089, 0x5be9, 0x5c0b, 0x5fc3, 0x6c81,
  /* 0x64 */
  0xf972, 0x6df1, 0x700b, 0x751a, 0x82af, 0x8af6, 0x4ec0, 0x5341,
  0xf973, 0x96d9, 0x6c0f, 0x4e9e, 0x4fc4, 0x5152, 0x555e, 0x5a25,
  0x5ce8, 0x6211, 0x7259, 0x82bd, 0x83aa, 0x86fe, 0x8859, 0x8a1d,
  0x963f, 0x96c5, 0x9913, 0x9d09, 0x9d5d, 0x580a, 0x5cb3, 0x5dbd,
  0x5e44, 0x60e1, 0x6115, 0x63e1, 0x6a02, 0x6e25, 0x9102, 0x9354,
  0x984e, 0x9c10, 0x9f77, 0x5b89, 0x5cb8, 0x6309, 0x664f, 0x6848,
  0x773c, 0x96c1, 0x978d, 0x9854, 0x9b9f, 0x65a1, 0x8b01, 0x8ecb,
  0x95bc, 0x5535, 0x5ca9, 0x5dd6, 0x5eb5, 0x6697, 0x764c, 0x83f4,
  0x95c7, 0x58d3, 0x62bc, 0x72ce, 0x9d28, 0x4ef0, 0x592e, 0x600f,
  0x663b, 0x6b83, 0x79e7, 0x9d26, 0x5393, 0x54c0, 0x57c3, 0x5d16,
  0x611b, 0x66d6, 0x6daf, 0x788d, 0x827e, 0x9698, 0x9744, 0x5384,
  0x627c, 0x6396, 0x6db2, 0x7e0a, 0x814b, 0x984d,
  /* 0x65 */
  0x6afb, 0x7f4c, 0x9daf, 0x9e1a, 0x4e5f, 0x503b, 0x51b6, 0x591c,
  0x60f9, 0x63f6, 0x6930, 0x723a, 0x8036, 0xf974, 0x91ce, 0x5f31,
  0xf975, 0xf976, 0x7d04, 0x82e5, 0x846f, 0x84bb, 0x85e5, 0x8e8d,
  0xf977, 0x4f6f, 0xf978, 0xf979, 0x58e4, 0x5b43, 0x6059, 0x63da,
  0x6518, 0x656d, 0x6698, 0xf97a, 0x694a, 0x6a23, 0x6d0b, 0x7001,
  0x716c, 0x75d2, 0x760d, 0x79b3, 0x7a70, 0xf97b, 0x7f8a, 0xf97c,
  0x8944, 0xf97d, 0x8b93, 0x91c0, 0x967d, 0xf97e, 0x990a, 0x5704,
  0x5fa1, 0x65bc, 0x6f01, 0x7600, 0x79a6, 0x8a9e, 0x99ad, 0x9b5a,
  0x9f6c, 0x5104, 0x61b6, 0x6291, 0x6a8d, 0x81c6, 0x5043, 0x5830,
  0x5f66, 0x7109, 0x8a00, 0x8afa, 0x5b7c, 0x8616, 0x4ffa, 0x513c,
  0x56b4, 0x5944, 0x63a9, 0x6df9, 0x5daa, 0x696d, 0x5186, 0x4e88,
  0x4f59, 0xf97f, 0xf980, 0xf981, 0x5982, 0xf982,
  /* 0x66 */
  0xf983, 0x6b5f, 0x6c5d, 0xf984, 0x74b5, 0x7916, 0xf985, 0x8207,
  0x8245, 0x8339, 0x8f3f, 0x8f5d, 0xf986, 0x9918, 0xf987, 0xf988,
  0xf989, 0x4ea6, 0xf98a, 0x57df, 0x5f79, 0x6613, 0xf98b, 0xf98c,
  0x75ab, 0x7e79, 0x8b6f, 0xf98d, 0x9006, 0x9a5b, 0x56a5, 0x5827,
  0x59f8, 0x5a1f, 0x5bb4, 0xf98e, 0x5ef6, 0xf98f, 0xf990, 0x6350,
  0x633b, 0xf991, 0x693d, 0x6c87, 0x6cbf, 0x6d8e, 0x6d93, 0x6df5,
  0x6f14, 0xf992, 0x70df, 0x7136, 0x7159, 0xf993, 0x71c3, 0x71d5,
  0xf994, 0x784f, 0x786f, 0xf995, 0x7b75, 0x7de3, 0xf996, 0x7e2f,
  0xf997, 0x884d, 0x8edf, 0xf998, 0xf999, 0xf99a, 0x925b, 0xf99b,
  0x9cf6, 0xf99c, 0xf99d, 0xf99e, 0x6085, 0x6d85, 0xf99f, 0x71b1,
  0xf9a0, 0xf9a1, 0x95b1, 0x53ad, 0xf9a2, 0xf9a3, 0xf9a4, 0x67d3,
  0xf9a5, 0x708e, 0x7130, 0x7430, 0x8276, 0x82d2,
  /* 0x67 */
  0xf9a6, 0x95bb, 0x9ae5, 0x9e7d, 0x66c4, 0xf9a7, 0x71c1, 0x8449,
  0xf9a8, 0xf9a9, 0x584b, 0xf9aa, 0xf9ab, 0x5db8, 0x5f71, 0xf9ac,
  0x6620, 0x668e, 0x6979, 0x69ae, 0x6c38, 0x6cf3, 0x6e36, 0x6f41,
  0x6fda, 0x701b, 0x702f, 0x7150, 0x71df, 0x7370, 0xf9ad, 0x745b,
  0xf9ae, 0x74d4, 0x76c8, 0x7a4e, 0x7e93, 0xf9af, 0xf9b0, 0x82f1,
  0x8a60, 0x8fce, 0xf9b1, 0x9348, 0xf9b2, 0x9719, 0xf9b3, 0xf9b4,
  0x4e42, 0x502a, 0xf9b5, 0x5208, 0x53e1, 0x66f3, 0x6c6d, 0x6fca,
  0x730a, 0x777f, 0x7a62, 0x82ae, 0x85dd, 0x8602, 0xf9b6, 0x88d4,
  0x8a63, 0x8b7d, 0x8c6b, 0xf9b7, 0x92b3, 0xf9b8, 0x9713, 0x9810,
  0x4e94, 0x4f0d, 0x4fc9, 0x50b2, 0x5348, 0x543e, 0x5433, 0x55da,
  0x5862, 0x58ba, 0x5967, 0x5a1b, 0x5be4, 0x609f, 0xf9b9, 0x61ca,
  0x6556, 0x65ff, 0x6664, 0x68a7, 0x6c5a, 0x6fb3,
  /* 0x68 */
  0x70cf, 0x71ac, 0x7352, 0x7b7d, 0x8708, 0x8aa4, 0x9c32, 0x9f07,
  0x5c4b, 0x6c83, 0x7344, 0x7389, 0x923a, 0x6eab, 0x7465, 0x761f,
  0x7a69, 0x7e15, 0x860a, 0x5140, 0x58c5, 0x64c1, 0x74ee, 0x7515,
  0x7670, 0x7fc1, 0x9095, 0x96cd, 0x9954, 0x6e26, 0x74e6, 0x7aa9,
  0x7aaa, 0x81e5, 0x86d9, 0x8778, 0x8a1b, 0x5a49, 0x5b8c, 0x5b9b,
  0x68a1, 0x6900, 0x6d63, 0x73a9, 0x7413, 0x742c, 0x7897, 0x7de9,
  0x7feb, 0x8118, 0x8155, 0x839e, 0x8c4c, 0x962e, 0x9811, 0x66f0,
  0x5f80, 0x65fa, 0x6789, 0x6c6a, 0x738b, 0x502d, 0x5a03, 0x6b6a,
  0x77ee, 0x5916, 0x5d6c, 0x5dcd, 0x7325, 0x754f, 0xf9ba, 0xf9bb,
  0x50e5, 0x51f9, 0x582f, 0x592d, 0x5996, 0x59da, 0x5be5, 0xf9bc,
  0xf9bd, 0x5da2, 0x62d7, 0x6416, 0x6493, 0x64fe, 0xf9be, 0x66dc,
  0xf9bf, 0x6a48, 0xf9c0, 0x71ff, 0x7464, 0xf9c1,
  /* 0x69 */
  0x7a88, 0x7aaf, 0x7e47, 0x7e5e, 0x8000, 0x8170, 0xf9c2, 0x87ef,
  0x8981, 0x8b20, 0x9059, 0xf9c3, 0x9080, 0x9952, 0x617e, 0x6b32,
  0x6d74, 0x7e1f, 0x8925, 0x8fb1, 0x4fd1, 0x50ad, 0x5197, 0x52c7,
  0x57c7, 0x5889, 0x5bb9, 0x5eb8, 0x6142, 0x6995, 0x6d8c, 0x6e67,
  0x6eb6, 0x7194, 0x7462, 0x7528, 0x752c, 0x8073, 0x8338, 0x84c9,
  0x8e0a, 0x9394, 0x93de, 0xf9c4, 0x4e8e, 0x4f51, 0x5076, 0x512a,
  0x53c8, 0x53cb, 0x53f3, 0x5b87, 0x5bd3, 0x5c24, 0x611a, 0x6182,
  0x65f4, 0x725b, 0x7397, 0x7440, 0x76c2, 0x7950, 0x7991, 0x79b9,
  0x7d06, 0x7fbd, 0x828b, 0x85d5, 0x865e, 0x8fc2, 0x9047, 0x90f5,
  0x91ea, 0x9685, 0x96e8, 0x96e9, 0x52d6, 0x5f67, 0x65ed, 0x6631,
  0x682f, 0x715c, 0x7a36, 0x90c1, 0x980a, 0x4e91, 0xf9c5, 0x6a52,
  0x6b9e, 0x6f90, 0x7189, 0x8018, 0x82b8, 0x8553,
  /* 0x6a */
  0x904b, 0x9695, 0x96f2, 0x97fb, 0x851a, 0x9b31, 0x4e90, 0x718a,
  0x96c4, 0x5143, 0x539f, 0x54e1, 0x5713, 0x5712, 0x57a3, 0x5a9b,
  0x5ac4, 0x5bc3, 0x6028, 0x613f, 0x63f4, 0x6c85, 0x6d39, 0x6e72,
  0x6e90, 0x7230, 0x733f, 0x7457, 0x82d1, 0x8881, 0x8f45, 0x9060,
  0xf9c6, 0x9662, 0x9858, 0x9d1b, 0x6708, 0x8d8a, 0x925e, 0x4f4d,
  0x5049, 0x50de, 0x5371, 0x570d, 0x59d4, 0x5a01, 0x5c09, 0x6170,
  0x6690, 0x6e2d, 0x7232, 0x744b, 0x7def, 0x80c3, 0x840e, 0x8466,
  0x853f, 0x875f, 0x885b, 0x8918, 0x8b02, 0x9055, 0x97cb, 0x9b4f,
  0x4e73, 0x4f91, 0x5112, 0x516a, 0xf9c7, 0x552f, 0x55a9, 0x5b7a,
  0x5ba5, 0x5e7c, 0x5e7d, 0x5ebe, 0x60a0, 0x60df, 0x6108, 0x6109,
  0x63c4, 0x6538, 0x6709, 0xf9c8, 0x67d4, 0x67da, 0xf9c9, 0x6961,
  0x6962, 0x6cb9, 0x6d27, 0xf9ca, 0x6e38, 0xf9cb,
  /* 0x6b */
  0x6fe1, 0x7336, 0x7337, 0xf9cc, 0x745c, 0x7531, 0xf9cd, 0x7652,
  0xf9ce, 0xf9cf, 0x7dad, 0x81fe, 0x8438, 0x88d5, 0x8a98, 0x8adb,
  0x8aed, 0x8e30, 0x8e42, 0x904a, 0x903e, 0x907a, 0x9149, 0x91c9,
  0x936e, 0xf9d0, 0xf9d1, 0x5809, 0xf9d2, 0x6bd3, 0x8089, 0x80b2,
  0xf9d3, 0xf9d4, 0x5141, 0x596b, 0x5c39, 0xf9d5, 0xf9d6, 0x6f64,
  0x73a7, 0x80e4, 0x8d07, 0xf9d7, 0x9217, 0x958f, 0xf9d8, 0xf9d9,
  0xf9da, 0xf9db, 0x807f, 0x620e, 0x701c, 0x7d68, 0x878d, 0xf9dc,
  0x57a0, 0x6069, 0x6147, 0x6bb7, 0x8abe, 0x9280, 0x96b1, 0x4e59,
  0x541f, 0x6deb, 0x852d, 0x9670, 0x97f3, 0x98ee, 0x63d6, 0x6ce3,
  0x9091, 0x51dd, 0x61c9, 0x81ba, 0x9df9, 0x4f9d, 0x501a, 0x5100,
  0x5b9c, 0x610f, 0x61ff, 0x64ec, 0x6905, 0x6bc5, 0x7591, 0x77e3,
  0x7fa9, 0x8264, 0x858f, 0x87fb, 0x8863, 0x8abc,
  /* 0x6c */
  0x8b70, 0x91ab, 0x4e8c, 0x4ee5, 0x4f0a, 0xf9dd, 0xf9de, 0x5937,
  0x59e8, 0xf9df, 0x5df2, 0x5f1b, 0x5f5b, 0x6021, 0xf9e0, 0xf9e1,
  0xf9e2, 0xf9e3, 0x723e, 0x73e5, 0xf9e4, 0x7570, 0x75cd, 0xf9e5,
  0x79fb, 0xf9e6, 0x800c, 0x8033, 0x8084, 0x82e1, 0x8351, 0xf9e7,
  0xf9e8, 0x8cbd, 0x8cb3, 0x9087, 0xf9e9, 0xf9ea, 0x98f4, 0x990c,
  0xf9eb, 0xf9ec, 0x7037, 0x76ca, 0x7fca, 0x7fcc, 0x7ffc, 0x8b1a,
  0x4eba, 0x4ec1, 0x5203, 0x5370, 0xf9ed, 0x54bd, 0x56e0, 0x59fb,
  0x5bc5, 0x5f15, 0x5fcd, 0x6e6e, 0xf9ee, 0xf9ef, 0x7d6a, 0x8335,
  0xf9f0, 0x8693, 0x8a8d, 0xf9f1, 0x976d, 0x9777, 0xf9f2, 0xf9f3,
  0x4e00, 0x4f5a, 0x4f7e, 0x58f9, 0x65e5, 0x6ea2, 0x9038, 0x93b0,
  0x99b9, 0x4efb, 0x58ec, 0x598a, 0x59d9, 0x6041, 0xf9f4, 0xf9f5,
  0x7a14, 0xf9f6, 0x834f, 0x8cc3, 0x5165, 0x5344,
  /* 0x6d */
  0xf9f7, 0xf9f8, 0xf9f9, 0x4ecd, 0x5269, 0x5b55, 0x82bf, 0x4ed4,
  0x523a, 0x54a8, 0x59c9, 0x59ff, 0x5b50, 0x5b57, 0x5b5c, 0x6063,
  0x6148, 0x6ecb, 0x7099, 0x716e, 0x7386, 0x74f7, 0x75b5, 0x78c1,
  0x7d2b, 0x8005, 0x81ea, 0x8328, 0x8517, 0x85c9, 0x8aee, 0x8cc7,
  0x96cc, 0x4f5c, 0x52fa, 0x56bc, 0x65ab, 0x6628, 0x707c, 0x70b8,
  0x7235, 0x7dbd, 0x828d, 0x914c, 0x96c0, 0x9d72, 0x5b71, 0x68e7,
  0x6b98, 0x6f7a, 0x76de, 0x5c91, 0x66ab, 0x6f5b, 0x7bb4, 0x7c2a,
  0x8836, 0x96dc, 0x4e08, 0x4ed7, 0x5320, 0x5834, 0x58bb, 0x58ef,
  0x596c, 0x5c07, 0x5e33, 0x5e84, 0x5f35, 0x638c, 0x66b2, 0x6756,
  0x6a1f, 0x6aa3, 0x6b0c, 0x6f3f, 0x7246, 0xf9fa, 0x7350, 0x748b,
  0x7ae0, 0x7ca7, 0x8178, 0x81df, 0x81e7, 0x838a, 0x846c, 0x8523,
  0x8594, 0x85cf, 0x88dd, 0x8d13, 0x91ac, 0x9577,
  /* 0x6e */
  0x969c, 0x518d, 0x54c9, 0x5728, 0x5bb0, 0x624d, 0x6750, 0x683d,
  0x6893, 0x6e3d, 0x6ed3, 0x707d, 0x7e21, 0x88c1, 0x8ca1, 0x8f09,
  0x9f4b, 0x9f4e, 0x722d, 0x7b8f, 0x8acd, 0x931a, 0x4f47, 0x4f4e,
  0x5132, 0x5480, 0x59d0, 0x5e95, 0x62b5, 0x6775, 0x696e, 0x6a17,
  0x6cae, 0x6e1a, 0x72d9, 0x732a, 0x75bd, 0x7bb8, 0x7d35, 0x82e7,
  0x83f9, 0x8457, 0x85f7, 0x8a5b, 0x8caf, 0x8e87, 0x9019, 0x90b8,
  0x96ce, 0x9f5f, 0x52e3, 0x540a, 0x5ae1, 0x5bc2, 0x6458, 0x6575,
  0x6ef4, 0x72c4, 0xf9fb, 0x7684, 0x7a4d, 0x7b1b, 0x7c4d, 0x7e3e,
  0x7fdf, 0x837b, 0x8b2b, 0x8cca, 0x8d64, 0x8de1, 0x8e5f, 0x8fea,
  0x8ff9, 0x9069, 0x93d1, 0x4f43, 0x4f7a, 0x50b3, 0x5168, 0x5178,
  0x524d, 0x526a, 0x5861, 0x587c, 0x5960, 0x5c08, 0x5c55, 0x5edb,
  0x609b, 0x6230, 0x6813, 0x6bbf, 0x6c08, 0x6fb1,
  /* 0x6f */
  0x714e, 0x7420, 0x7530, 0x7538, 0x7551, 0x7672, 0x7b4c, 0x7b8b,
  0x7bad, 0x7bc6, 0x7e8f, 0x8a6e, 0x8f3e, 0x8f49, 0x923f, 0x9293,
  0x9322, 0x942b, 0x96fb, 0x985a, 0x986b, 0x991e, 0x5207, 0x622a,
  0x6298, 0x6d59, 0x7664, 0x7aca, 0x7bc0, 0x7d76, 0x5360, 0x5cbe,
  0x5e97, 0x6f38, 0x70b9, 0x7c98, 0x9711, 0x9b8e, 0x9ede, 0x63a5,
  0x647a, 0x8776, 0x4e01, 0x4e95, 0x4ead, 0x505c, 0x5075, 0x5448,
  0x59c3, 0x5b9a, 0x5e40, 0x5ead, 0x5ef7, 0x5f81, 0x60c5, 0x633a,
  0x653f, 0x6574, 0x65cc, 0x6676, 0x6678, 0x67fe, 0x6968, 0x6a89,
  0x6b63, 0x6c40, 0x6dc0, 0x6de8, 0x6e1f, 0x6e5e, 0x701e, 0x70a1,
  0x738e, 0x73fd, 0x753a, 0x775b, 0x7887, 0x798e, 0x7a0b, 0x7a7d,
  0x7cbe, 0x7d8e, 0x8247, 0x8a02, 0x8aea, 0x8c9e, 0x912d, 0x914a,
  0x91d8, 0x9266, 0x92cc, 0x9320, 0x9706, 0x9756,
  /* 0x70 */
  0x975c, 0x9802, 0x9f0e, 0x5236, 0x5291, 0x557c, 0x5824, 0x5e1d,
  0x5f1f, 0x608c, 0x63d0, 0x68af, 0x6fdf, 0x796d, 0x7b2c, 0x81cd,
  0x85ba, 0x88fd, 0x8af8, 0x8e44, 0x918d, 0x9664, 0x969b, 0x973d,
  0x984c, 0x9f4a, 0x4fce, 0x5146, 0x51cb, 0x52a9, 0x5632, 0x5f14,
  0x5f6b, 0x63aa, 0x64cd, 0x65e9, 0x6641, 0x66fa, 0x66f9, 0x671d,
  0x689d, 0x68d7, 0x69fd, 0x6f15, 0x6f6e, 0x7167, 0x71e5, 0x722a,
  0x74aa, 0x773a, 0x7956, 0x795a, 0x79df, 0x7a20, 0x7a95, 0x7c97,
  0x7cdf, 0x7d44, 0x7e70, 0x8087, 0x85fb, 0x86a4, 0x8a54, 0x8abf,
  0x8d99, 0x8e81, 0x9020, 0x906d, 0x91e3, 0x963b, 0x96d5, 0x9ce5,
  0x65cf, 0x7c07, 0x8db3, 0x93c3, 0x5b58, 0x5c0a, 0x5352, 0x62d9,
  0x731d, 0x5027, 0x5b97, 0x5f9e, 0x60b0, 0x616b, 0x68d5, 0x6dd9,
  0x742e, 0x7a2e, 0x7d42, 0x7d9c, 0x7e31, 0x816b,
  /* 0x71 */
  0x8e2a, 0x8e35, 0x937e, 0x9418, 0x4f50, 0x5750, 0x5de6, 0x5ea7,
  0x632b, 0x7f6a, 0x4e3b, 0x4f4f, 0x4f8f, 0x505a, 0x59dd, 0x80c4,
  0x546a, 0x5468, 0x55fe, 0x594f, 0x5b99, 0x5dde, 0x5eda, 0x665d,
  0x6731, 0x67f1, 0x682a, 0x6ce8, 0x6d32, 0x6e4a, 0x6f8d, 0x70b7,
  0x73e0, 0x7587, 0x7c4c, 0x7d02, 0x7d2c, 0x7da2, 0x821f, 0x86db,
  0x8a3b, 0x8a85, 0x8d70, 0x8e8a, 0x8f33, 0x9031, 0x914e, 0x9152,
  0x9444, 0x99d0, 0x7af9, 0x7ca5, 0x4fca, 0x5101, 0x51c6, 0x57c8,
  0x5bef, 0x5cfb, 0x6659, 0x6a3d, 0x6d5a, 0x6e96, 0x6fec, 0x710c,
  0x756f, 0x7ae3, 0x8822, 0x9021, 0x9075, 0x96cb, 0x99ff, 0x8301,
  0x4e2d, 0x4ef2, 0x8846, 0x91cd, 0x537d, 0x6adb, 0x696b, 0x6c41,
  0x847a, 0x589e, 0x618e, 0x66fe, 0x62ef, 0x70dd, 0x7511, 0x75c7,
  0x7e52, 0x84b8, 0x8b49, 0x8d08, 0x4e4b, 0x53ea,
  /* 0x72 */
  0x54ab, 0x5730, 0x5740, 0x5fd7, 0x6301, 0x6307, 0x646f, 0x652f,
  0x65e8, 0x667a, 0x679d, 0x67b3, 0x6b62, 0x6c60, 0x6c9a, 0x6f2c,
  0x77e5, 0x7825, 0x7949, 0x7957, 0x7d19, 0x80a2, 0x8102, 0x81f3,
  0x829d, 0x82b7, 0x8718, 0x8a8c, 0xf9fc, 0x8d04, 0x8dbe, 0x9072,
  0x76f4, 0x7a19, 0x7a37, 0x7e54, 0x8077, 0x5507, 0x55d4, 0x5875,
  0x632f, 0x6422, 0x6649, 0x664b, 0x686d, 0x699b, 0x6b84, 0x6d25,
  0x6eb1, 0x73cd, 0x7468, 0x74a1, 0x755b, 0x75b9, 0x76e1, 0x771e,
  0x778b, 0x79e6, 0x7e09, 0x7e1d, 0x81fb, 0x852f, 0x8897, 0x8a3a,
  0x8cd1, 0x8eeb, 0x8fb0, 0x9032, 0x93ad, 0x9663, 0x9673, 0x9707,
  0x4f84, 0x53f1, 0x59ea, 0x5ac9, 0x5e19, 0x684e, 0x74c6, 0x75be,
  0x79e9, 0x7a92, 0x81a3, 0x86ed, 0x8cea, 0x8dcc, 0x8fed, 0x659f,
  0x6715, 0xf9fd, 0x57f7, 0x6f57, 0x7ddd, 0x8f2f,
  /* 0x73 */
  0x93f6, 0x96c6, 0x5fb5, 0x61f2, 0x6f84, 0x4e14, 0x4f98, 0x501f,
  0x53c9, 0x55df, 0x5d6f, 0x5dee, 0x6b21, 0x6b64, 0x78cb, 0x7b9a,
  0xf9fe, 0x8e49, 0x8eca, 0x906e, 0x6349, 0x643e, 0x7740, 0x7a84,
  0x932f, 0x947f, 0x9f6a, 0x64b0, 0x6faf, 0x71e6, 0x74a8, 0x74da,
  0x7ac4, 0x7c12, 0x7e82, 0x7cb2, 0x7e98, 0x8b9a, 0x8d0a, 0x947d,
  0x9910, 0x994c, 0x5239, 0x5bdf, 0x64e6, 0x672d, 0x7d2e, 0x50ed,
  0x53c3, 0x5879, 0x6158, 0x6159, 0x61fa, 0x65ac, 0x7ad9, 0x8b92,
  0x8b96, 0x5009, 0x5021, 0x5275, 0x5531, 0x5a3c, 0x5ee0, 0x5f70,
  0x6134, 0x655e, 0x660c, 0x6636, 0x66a2, 0x69cd, 0x6ec4, 0x6f32,
  0x7316, 0x7621, 0x7a93, 0x8139, 0x8259, 0x83d6, 0x84bc, 0x50b5,
  0x57f0, 0x5bc0, 0x5be8, 0x5f69, 0x63a1, 0x7826, 0x7db5, 0x83dc,
  0x8521, 0x91c7, 0x91f5, 0x518a, 0x67f5, 0x7b56,
  /* 0x74 */
  0x8cac, 0x51c4, 0x59bb, 0x60bd, 0x8655, 0x501c, 0xf9ff, 0x5254,
  0x5c3a, 0x617d, 0x621a, 0x62d3, 0x64f2, 0x65a5, 0x6ecc, 0x7620,
  0x810a, 0x8e60, 0x965f, 0x96bb, 0x4edf, 0x5343, 0x5598, 0x5929,
  0x5ddd, 0x64c5, 0x6cc9, 0x6dfa, 0x7394, 0x7a7f, 0x821b, 0x85a6,
  0x8ce4, 0x8e10, 0x9077, 0x91e7, 0x95e1, 0x9621, 0x97c6, 0x51f8,
  0x54f2, 0x5586, 0x5fb9, 0x64a4, 0x6f88, 0x7db4, 0x8f1f, 0x8f4d,
  0x9435, 0x50c9, 0x5c16, 0x6cbe, 0x6dfb, 0x751b, 0x77bb, 0x7c3d,
  0x7c64, 0x8a79, 0x8ac2, 0x581e, 0x59be, 0x5e16, 0x6377, 0x7252,
  0x758a, 0x776b, 0x8adc, 0x8cbc, 0x8f12, 0x5ef3, 0x6674, 0x6df8,
  0x807d, 0x83c1, 0x8acb, 0x9751, 0x9bd6, 0xfa00, 0x5243, 0x66ff,
  0x6d95, 0x6eef, 0x7de0, 0x8ae6, 0x902e, 0x905e, 0x9ad4, 0x521d,
  0x527f, 0x54e8, 0x6194, 0x6284, 0x62db, 0x68a2,
  /* 0x75 */
  0x6912, 0x695a, 0x6a35, 0x7092, 0x7126, 0x785d, 0x7901, 0x790e,
  0x79d2, 0x7a0d, 0x8096, 0x8278, 0x82d5, 0x8349, 0x8549, 0x8c82,
  0x8d85, 0x9162, 0x918b, 0x91ae, 0x4fc3, 0x56d1, 0x71ed, 0x77d7,
  0x8700, 0x89f8, 0x5bf8, 0x5fd6, 0x6751, 0x90a8, 0x53e2, 0x585a,
  0x5bf5, 0x60a4, 0x6181, 0x6460, 0x7e3d, 0x8070, 0x8525, 0x9283,
  0x64ae, 0x50ac, 0x5d14, 0x6700, 0x589c, 0x62bd, 0x63a8, 0x690e,
  0x6978, 0x6a1e, 0x6e6b, 0x76ba, 0x79cb, 0x82bb, 0x8429, 0x8acf,
  0x8da8, 0x8ffd, 0x9112, 0x914b, 0x919c, 0x9310, 0x9318, 0x939a,
  0x96db, 0x9a36, 0x9c0d, 0x4e11, 0x755c, 0x795d, 0x7afa, 0x7b51,
  0x7bc9, 0x7e2e, 0x84c4, 0x8e59, 0x8e74, 0x8ef8, 0x9010, 0x6625,
  0x693f, 0x7443, 0x51fa, 0x672e, 0x9edc, 0x5145, 0x5fe0, 0x6c96,
  0x87f2, 0x885d, 0x8877, 0x60b4, 0x81b5, 0x8403,
  /* 0x76 */
  0x8d05, 0x53d6, 0x5439, 0x5634, 0x5a36, 0x5c31, 0x708a, 0x7fe0,
  0x805a, 0x8106, 0x81ed, 0x8da3, 0x9189, 0x9a5f, 0x9df2, 0x5074,
  0x4ec4, 0x53a0, 0x60fb, 0x6e2c, 0x5c64, 0x4f88, 0x5024, 0x55e4,
  0x5cd9, 0x5e5f, 0x6065, 0x6894, 0x6cbb, 0x6dc4, 0x71be, 0x75d4,
  0x75f4, 0x7661, 0x7a1a, 0x7a49, 0x7dc7, 0x7dfb, 0x7f6e, 0x81f4,
  0x86a9, 0x8f1c, 0x96c9, 0x99b3, 0x9f52, 0x5247, 0x52c5, 0x98ed,
  0x89aa, 0x4e03, 0x67d2, 0x6f06, 0x4fb5, 0x5be2, 0x6795, 0x6c88,
  0x6d78, 0x741b, 0x7827, 0x91dd, 0x937c, 0x87c4, 0x79e4, 0x7a31,
  0x5feb, 0x4ed6, 0x54a4, 0x553e, 0x58ae, 0x59a5, 0x60f0, 0x6253,
  0x62d6, 0x6736, 0x6955, 0x8235, 0x9640, 0x99b1, 0x99dd, 0x502c,
  0x5353, 0x5544, 0x577c, 0xfa01, 0x6258, 0xfa02, 0x64e2, 0x666b,
  0x67dd, 0x6fc1, 0x6fef, 0x7422, 0x7438, 0x8a17,
  /* 0x77 */
  0x9438, 0x5451, 0x5606, 0x5766, 0x5f48, 0x619a, 0x6b4e, 0x7058,
  0x70ad, 0x7dbb, 0x8a95, 0x596a, 0x812b, 0x63a2, 0x7708, 0x803d,
  0x8caa, 0x5854, 0x642d, 0x69bb, 0x5b95, 0x5e11, 0x6e6f, 0xfa03,
  0x8569, 0x514c, 0x53f0, 0x592a, 0x6020, 0x614b, 0x6b86, 0x6c70,
  0x6cf0, 0x7b1e, 0x80ce, 0x82d4, 0x8dc6, 0x90b0, 0x98b1, 0xfa04,
  0x64c7, 0x6fa4, 0x6491, 0x6504, 0x514e, 0x5410, 0x571f, 0x8a0e,
  0x615f, 0x6876, 0xfa05, 0x75db, 0x7b52, 0x7d71, 0x901a, 0x5806,
  0x69cc, 0x817f, 0x892a, 0x9000, 0x9839, 0x5078, 0x5957, 0x59ac,
  0x6295, 0x900f, 0x9b2a, 0x615d, 0x7279, 0x95d6, 0x5761, 0x5a46,
  0x5df4, 0x628a, 0x64ad, 0x64fa, 0x6777, 0x6ce2, 0x6d3e, 0x722c,
  0x7436, 0x7834, 0x7f77, 0x82ad, 0x8ddb, 0x9817, 0x5224, 0x5742,
  0x677f, 0x7248, 0x74e3, 0x8ca9, 0x8fa6, 0x9211,
  /* 0x78 */
  0x962a, 0x516b, 0x53ed, 0x634c, 0x4f69, 0x5504, 0x6096, 0x6557,
  0x6c9b, 0x6d7f, 0x724c, 0x72fd, 0x7a17, 0x8987, 0x8c9d, 0x5f6d,
  0x6f8e, 0x70f9, 0x81a8, 0x610e, 0x4fbf, 0x504f, 0x6241, 0x7247,
  0x7bc7, 0x7de8, 0x7fe9, 0x904d, 0x97ad, 0x9a19, 0x8cb6, 0x576a,
  0x5e73, 0x67b0, 0x840d, 0x8a55, 0x5420, 0x5b16, 0x5e63, 0x5ee2,
  0x5f0a, 0x6583, 0x80ba, 0x853d, 0x9589, 0x965b, 0x4f48, 0x5305,
  0x530d, 0x530f, 0x5486, 0x54fa, 0x5703, 0x5e03, 0x6016, 0x629b,
  0x62b1, 0x6355, 0xfa06, 0x6ce1, 0x6d66, 0x75b1, 0x7832, 0x80de,
  0x812f, 0x82de, 0x8461, 0x84b2, 0x888d, 0x8912, 0x900b, 0x92ea,
  0x98fd, 0x9b91, 0x5e45, 0x66b4, 0x66dd, 0x7011, 0x7206, 0xfa07,
  0x4ff5, 0x527d, 0x5f6a, 0x6153, 0x6753, 0x6a19, 0x6f02, 0x74e2,
  0x7968, 0x8868, 0x8c79, 0x98c7, 0x98c4, 0x9a43,
  /* 0x79 */
  0x54c1, 0x7a1f, 0x6953, 0x8af7, 0x8c4a, 0x98a8, 0x99ae, 0x5f7c,
  0x62ab, 0x75b2, 0x76ae, 0x88ab, 0x907f, 0x9642, 0x5339, 0x5f3c,
  0x5fc5, 0x6ccc, 0x73cc, 0x7562, 0x758b, 0x7b46, 0x82fe, 0x999d,
  0x4e4f, 0x903c, 0x4e0b, 0x4f55, 0x53a6, 0x590f, 0x5ec8, 0x6630,
  0x6cb3, 0x7455, 0x8377, 0x8766, 0x8cc0, 0x9050, 0x971e, 0x9c15,
  0x58d1, 0x5b78, 0x8650, 0x8b14, 0x9db4, 0x5bd2, 0x6068, 0x608d,
  0x65f1, 0x6c57, 0x6f22, 0x6fa3, 0x701a, 0x7f55, 0x7ff0, 0x9591,
  0x9592, 0x9650, 0x97d3, 0x5272, 0x8f44, 0x51fd, 0x542b, 0x54b8,
  0x5563, 0x558a, 0x6abb, 0x6db5, 0x7dd8, 0x8266, 0x929c, 0x9677,
  0x9e79, 0x5408, 0x54c8, 0x76d2, 0x86e4, 0x95a4, 0x95d4, 0x965c,
  0x4ea2, 0x4f09, 0x59ee, 0x5ae6, 0x5df7, 0x6052, 0x6297, 0x676d,
  0x6841, 0x6c86, 0x6e2f, 0x7f38, 0x809b, 0x822a,
  /* 0x7a */
  0xfa08, 0xfa09, 0x9805, 0x4ea5, 0x5055, 0x54b3, 0x5793, 0x595a,
  0x5b69, 0x5bb3, 0x61c8, 0x6977, 0x6d77, 0x7023, 0x87f9, 0x89e3,
  0x8a72, 0x8ae7, 0x9082, 0x99ed, 0x9ab8, 0x52be, 0x6838, 0x5016,
  0x5e78, 0x674f, 0x8347, 0x884c, 0x4eab, 0x5411, 0x56ae, 0x73e6,
  0x9115, 0x97ff, 0x9909, 0x9957, 0x9999, 0x5653, 0x589f, 0x865b,
  0x8a31, 0x61b2, 0x6af6, 0x737b, 0x8ed2, 0x6b47, 0x96aa, 0x9a57,
  0x5955, 0x7200, 0x8d6b, 0x9769, 0x4fd4, 0x5cf4, 0x5f26, 0x61f8,
  0x665b, 0x6ceb, 0x70ab, 0x7384, 0x73b9, 0x73fe, 0x7729, 0x774d,
  0x7d43, 0x7d62, 0x7e23, 0x8237, 0x8852, 0xfa0a, 0x8ce2, 0x9249,
  0x986f, 0x5b51, 0x7a74, 0x8840, 0x9801, 0x5acc, 0x4fe0, 0x5354,
  0x593e, 0x5cfd, 0x633e, 0x6d79, 0x72f9, 0x8105, 0x8107, 0x83a2,
  0x92cf, 0x9830, 0x4ea8, 0x5144, 0x5211, 0x578b,
  /* 0x7b */
  0x5f62, 0x6cc2, 0x6ece, 0x7005, 0x7050, 0x70af, 0x7192, 0x73e9,
  0x7469, 0x834a, 0x87a2, 0x8861, 0x9008, 0x90a2, 0x93a3, 0x99a8,
  0x516e, 0x5f57, 0x60e0, 0x6167, 0x66b3, 0x8559, 0x8e4a, 0x91af,
  0x978b, 0x4e4e, 0x4e92, 0x547c, 0x58d5, 0x58fa, 0x597d, 0x5cb5,
  0x5f27, 0x6236, 0x6248, 0x660a, 0x6667, 0x6beb, 0x6d69, 0x6dcf,
  0x6e56, 0x6ef8, 0x6f94, 0x6fe0, 0x6fe9, 0x705d, 0x72d0, 0x7425,
  0x745a, 0x74e0, 0x7693, 0x795c, 0x7cca, 0x7e1e, 0x80e1, 0x82a6,
  0x846b, 0x84bf, 0x864e, 0x865f, 0x8774, 0x8b77, 0x8c6a, 0x93ac,
  0x9800, 0x9865, 0x60d1, 0x6216, 0x9177, 0x5a5a, 0x660f, 0x6df7,
  0x6e3e, 0x743f, 0x9b42, 0x5ffd, 0x60da, 0x7b0f, 0x54c4, 0x5f18,
  0x6c5e, 0x6cd3, 0x6d2a, 0x70d8, 0x7d05, 0x8679, 0x8a0c, 0x9d3b,
  0x5316, 0x548c, 0x5b05, 0x6a3a, 0x706b, 0x7575,
  /* 0x7c */
  0x798d, 0x79be, 0x82b1, 0x83ef, 0x8a71, 0x8b41, 0x8ca8, 0x9774,
  0xfa0b, 0x64f4, 0x652b, 0x78ba, 0x78bb, 0x7a6b, 0x4e38, 0x559a,
  0x5950, 0x5ba6, 0x5e7b, 0x60a3, 0x63db, 0x6b61, 0x6665, 0x6853,
  0x6e19, 0x7165, 0x74b0, 0x7d08, 0x9084, 0x9a69, 0x9c25, 0x6d3b,
  0x6ed1, 0x733e, 0x8c41, 0x95ca, 0x51f0, 0x5e4c, 0x5fa8, 0x604d,
  0x60f6, 0x6130, 0x614c, 0x6643, 0x6644, 0x69a5, 0x6cc1, 0x6e5f,
  0x6ec9, 0x6f62, 0x714c, 0x749c, 0x7687, 0x7bc1, 0x7c27, 0x8352,
  0x8757, 0x9051, 0x968d, 0x9ec3, 0x532f, 0x56de, 0x5efb, 0x5f8a,
  0x6062, 0x6094, 0x61f7, 0x6666, 0x6703, 0x6a9c, 0x6dee, 0x6fae,
  0x7070, 0x736a, 0x7e6a, 0x81be, 0x8334, 0x86d4, 0x8aa8, 0x8cc4,
  0x5283, 0x7372, 0x5b96, 0x6a6b, 0x9404, 0x54ee, 0x5686, 0x5b5d,
  0x6548, 0x6585, 0x66c9, 0x689f, 0x6d8d, 0x6dc6,
  /* 0x7d */
  0x723b, 0x80b4, 0x9175, 0x9a4d, 0x4faf, 0x5019, 0x539a, 0x540e,
  0x543c, 0x5589, 0x55c5, 0x5e3f, 0x5f8c, 0x673d, 0x7166, 0x73dd,
  0x9005, 0x52db, 0x52f3, 0x5864, 0x58ce, 0x7104, 0x718f, 0x71fb,
  0x85b0, 0x8a13, 0x6688, 0x85a8, 0x55a7, 0x6684, 0x714a, 0x8431,
  0x5349, 0x5599, 0x6bc1, 0x5f59, 0x5fbd, 0x63ee, 0x6689, 0x7147,
  0x8af1, 0x8f1d, 0x9ebe, 0x4f11, 0x643a, 0x70cb, 0x7566, 0x8667,
  0x6064, 0x8b4e, 0x9df8, 0x5147, 0x51f6, 0x5308, 0x6d36, 0x80f8,
  0x9ed1, 0x6615, 0x6b23, 0x7098, 0x75d5, 0x5403, 0x5c79, 0x7d07,
  0x8a16, 0x6b20, 0x6b3d, 0x6b46, 0x5438, 0x6070, 0x6d3d, 0x7fd5,
  0x8208, 0x50d6, 0x51de, 0x559c, 0x566b, 0x56cd, 0x59ec, 0x5b09,
  0x5e0c, 0x6199, 0x6198, 0x6231, 0x665e, 0x66e6, 0x7199, 0x71b9,
  0x71ba, 0x72a7, 0x79a7, 0x7a00, 0x7fb2, 0x8a70,
};

static int
ksc5601_mbtowc (conv_t conv, ucs4_t *pwc, const unsigned char *s, int n)
{
  unsigned char c1 = s[0];
  if ((c1 >= 0x21 && c1 <= 0x2c) || (c1 >= 0x30 && c1 <= 0x48) || (c1 >= 0x4a && c1 <= 0x7d)) {
    if (n >= 2) {
      unsigned char c2 = s[1];
      if (c2 >= 0x21 && c2 < 0x7f) {
        unsigned int i = 94 * (c1 - 0x21) + (c2 - 0x21);
        unsigned short wc = 0xfffd;
        if (i < 1410) {
          if (i < 1115)
            wc = ksc5601_2uni_page21[i];
        } else if (i < 3854) {
          if (i < 3760)
            wc = ksc5601_2uni_page30[i-1410];
        } else {
          if (i < 8742)
            wc = ksc5601_2uni_page4a[i-3854];
        }
        if (wc != 0xfffd) {
          *pwc = (ucs4_t) wc;
          return 2;
        }
      }
      return RET_ILSEQ;
    }
    return RET_TOOFEW(0);
  }
  return RET_ILSEQ;
}

static const unsigned short ksc5601_2charset[8227] = {
  0x222e, 0x2234, 0x2157, 0x2127, 0x2823, 0x2129, 0x2267, 0x2146,
  0x213e, 0x2977, 0x2978, 0x2225, 0x2252, 0x2124, 0x222c, 0x2976,
  0x282c, 0x2879, 0x2876, 0x287a, 0x222f, 0x2821, 0x2822, 0x213f,
  0x282a, 0x282d, 0x292c, 0x2921, 0x2923, 0x2140, 0x292a, 0x292d,
  0x2922, 0x2824, 0x2924, 0x2925, 0x2826, 0x2926, 0x2927, 0x2828,
  0x2928, 0x2829, 0x2929, 0x2930, 0x282f, 0x292f, 0x282b, 0x292b,
  0x282e, 0x292e, 0x2227, 0x2230, 0x2228, 0x222b, 0x222a, 0x222d,
  0x2229, 0x2541, 0x2542, 0x2543, 0x2544, 0x2545, 0x2546, 0x2547,
  0x2548, 0x2549, 0x254a, 0x254b, 0x254c, 0x254d, 0x254e, 0x254f,
  0x2550, 0x2551, 0x2552, 0x2553, 0x2554, 0x2555, 0x2556, 0x2557,
  0x2558, 0x2561, 0x2562, 0x2563, 0x2564, 0x2565, 0x2566, 0x2567,
  0x2568, 0x2569, 0x256a, 0x256b, 0x256c, 0x256d, 0x256e, 0x256f,
  0x2570, 0x2571, 0x2572, 0x2573, 0x2574, 0x2575, 0x2576, 0x2577,
  0x2578, 0x2c27, 0x2c21, 0x2c22, 0x2c23, 0x2c24, 0x2c25, 0x2c26,
  0x2c28, 0x2c29, 0x2c2a, 0x2c2b, 0x2c2c, 0x2c2d, 0x2c2e, 0x2c2f,
  0x2c30, 0x2c31, 0x2c32, 0x2c33, 0x2c34, 0x2c35, 0x2c36, 0x2c37,
  0x2c38, 0x2c39, 0x2c3a, 0x2c3b, 0x2c3c, 0x2c3d, 0x2c3e, 0x2c3f,
  0x2c40, 0x2c41, 0x2c51, 0x2c52, 0x2c53, 0x2c54, 0x2c55, 0x2c56,
  0x2c58, 0x2c59, 0x2c5a, 0x2c5b, 0x2c5c, 0x2c5d, 0x2c5e, 0x2c5f,
  0x2c60, 0x2c61, 0x2c62, 0x2c63, 0x2c64, 0x2c65, 0x2c66, 0x2c67,
  0x2c68, 0x2c69, 0x2c6a, 0x2c6b, 0x2c6c, 0x2c6d, 0x2c6e, 0x2c6f,
  0x2c70, 0x2c71, 0x2c57, 0x212a, 0x212e, 0x212f, 0x2130, 0x2131,
  0x2253, 0x2254, 0x2125, 0x2126, 0x2236, 0x2147, 0x2148, 0x2158,
  0x2979, 0x297a, 0x297b, 0x297c, 0x297d, 0x297e, 0x2266, 0x2149,
  0x2235, 0x2724, 0x2260, 0x2265, 0x2262, 0x2759, 0x214a, 0x2877,
  0x2878, 0x287b, 0x287c, 0x287d, 0x287e, 0x2530, 0x2531, 0x2532,
  0x2533, 0x2534, 0x2535, 0x2536, 0x2537, 0x2538, 0x2539, 0x2521,
  0x2522, 0x2523, 0x2524, 0x2525, 0x2526, 0x2527, 0x2528, 0x2529,
  0x252a, 0x2167, 0x2168, 0x2166, 0x2169, 0x216a, 0x2255, 0x2258,
  0x2256, 0x2259, 0x2257, 0x2221, 0x2222, 0x2223, 0x2153, 0x2224,
  0x2154, 0x2174, 0x2175, 0x2233, 0x2232, 0x216e, 0x2170, 0x2144,
  0x2150, 0x212b, 0x217c, 0x217d, 0x217b, 0x217a, 0x2172, 0x2173,
  0x2231, 0x2145, 0x2171, 0x212d, 0x216f, 0x2156, 0x2141, 0x2155,
  0x2142, 0x2143, 0x216c, 0x216d, 0x2178, 0x2179, 0x2176, 0x2177,
  0x2241, 0x2151, 0x2152, 0x2867, 0x2868, 0x2869, 0x286a, 0x286b,
  0x286c, 0x286d, 0x286e, 0x286f, 0x2870, 0x2871, 0x2872, 0x2873,
  0x2874, 0x2875, 0x2967, 0x2968, 0x2969, 0x296a, 0x296b, 0x296c,
  0x296d, 0x296e, 0x296f, 0x2970, 0x2971, 0x2972, 0x2973, 0x2974,
  0x2975, 0x294d, 0x294e, 0x294f, 0x2950, 0x2951, 0x2952, 0x2953,
  0x2954, 0x2955, 0x2956, 0x2957, 0x2958, 0x2959, 0x295a, 0x295b,
  0x295c, 0x295d, 0x295e, 0x295f, 0x2960, 0x2961, 0x2962, 0x2963,
  0x2964, 0x2965, 0x2966, 0x284d, 0x284e, 0x284f, 0x2850, 0x2851,
  0x2852, 0x2853, 0x2854, 0x2855, 0x2856, 0x2857, 0x2858, 0x2859,
  0x285a, 0x285b, 0x285c, 0x285d, 0x285e, 0x285f, 0x2860, 0x2861,
  0x2862, 0x2863, 0x2864, 0x2865, 0x2866, 0x2621, 0x262c, 0x2622,
  0x262d, 0x2623, 0x2648, 0x2647, 0x262e, 0x2624, 0x2642, 0x2641,
  0x262f, 0x2626, 0x2646, 0x2645, 0x2631, 0x2625, 0x2644, 0x2643,
  0x2630, 0x2627, 0x263c, 0x2649, 0x264a, 0x2637, 0x264b, 0x264c,
  0x2632, 0x2629, 0x263e, 0x264d, 0x264e, 0x2639, 0x264f, 0x2650,
  0x2634, 0x2628, 0x2651, 0x2652, 0x2638, 0x263d, 0x2653, 0x2654,
  0x2633, 0x262a, 0x2655, 0x2656, 0x263a, 0x263f, 0x2657, 0x2658,
  0x2635, 0x262b, 0x2659, 0x265a, 0x263b, 0x265b, 0x265c, 0x2640,
  0x265d, 0x265e, 0x265f, 0x2660, 0x2661, 0x2662, 0x2663, 0x2664,
  0x2636, 0x2246, 0x2161, 0x2160, 0x2243, 0x2247, 0x2248, 0x224b,
  0x224a, 0x2249, 0x224c, 0x2163, 0x2162, 0x223a, 0x2239, 0x2165,
  0x2164, 0x2238, 0x2237, 0x215f, 0x215e, 0x2242, 0x215b, 0x215d,
  0x215c, 0x2244, 0x2245, 0x215a, 0x2159, 0x224f, 0x224e, 0x2250,
  0x2251, 0x214f, 0x214e, 0x223c, 0x223d, 0x2240, 0x223b, 0x223e,
  0x223f, 0x224d, 0x225b, 0x225c, 0x225d, 0x225a, 0x2121, 0x2122,
  0x2123, 0x2128, 0x2134, 0x2135, 0x2136, 0x2137, 0x2138, 0x2139,
  0x213a, 0x213b, 0x213c, 0x213d, 0x216b, 0x2132, 0x2133, 0x2a21,
  0x2a22, 0x2a23, 0x2a24, 0x2a25, 0x2a26, 0x2a27, 0x2a28, 0x2a29,
  0x2a2a, 0x2a2b, 0x2a2c, 0x2a2d, 0x2a2e, 0x2a2f, 0x2a30, 0x2a31,
  0x2a32, 0x2a33, 0x2a34, 0x2a35, 0x2a36, 0x2a37, 0x2a38, 0x2a39,
  0x2a3a, 0x2a3b, 0x2a3c, 0x2a3d, 0x2a3e, 0x2a3f, 0x2a40, 0x2a41,
  0x2a42, 0x2a43, 0x2a44, 0x2a45, 0x2a46, 0x2a47, 0x2a48, 0x2a49,
  0x2a4a, 0x2a4b, 0x2a4c, 0x2a4d, 0x2a4e, 0x2a4f, 0x2a50, 0x2a51,
  0x2a52, 0x2a53, 0x2a54, 0x2a55, 0x2a56, 0x2a57, 0x2a58, 0x2a59,
  0x2a5a, 0x2a5b, 0x2a5c, 0x2a5d, 0x2a5e, 0x2a5f, 0x2a60, 0x2a61,
  0x2a62, 0x2a63, 0x2a64, 0x2a65, 0x2a66, 0x2a67, 0x2a68, 0x2a69,
  0x2a6a, 0x2a6b, 0x2a6c, 0x2a6d, 0x2a6e, 0x2a6f, 0x2a70, 0x2a71,
  0x2a72, 0x2a73, 0x2b21, 0x2b22, 0x2b23, 0x2b24, 0x2b25, 0x2b26,
  0x2b27, 0x2b28, 0x2b29, 0x2b2a, 0x2b2b, 0x2b2c, 0x2b2d, 0x2b2e,
  0x2b2f, 0x2b30, 0x2b31, 0x2b32, 0x2b33, 0x2b34, 0x2b35, 0x2b36,
  0x2b37, 0x2b38, 0x2b39, 0x2b3a, 0x2b3b, 0x2b3c, 0x2b3d, 0x2b3e,
  0x2b3f, 0x2b40, 0x2b41, 0x2b42, 0x2b43, 0x2b44, 0x2b45, 0x2b46,
  0x2b47, 0x2b48, 0x2b49, 0x2b4a, 0x2b4b, 0x2b4c, 0x2b4d, 0x2b4e,
  0x2b4f, 0x2b50, 0x2b51, 0x2b52, 0x2b53, 0x2b54, 0x2b55, 0x2b56,
  0x2b57, 0x2b58, 0x2b59, 0x2b5a, 0x2b5b, 0x2b5c, 0x2b5d, 0x2b5e,
  0x2b5f, 0x2b60, 0x2b61, 0x2b62, 0x2b63, 0x2b64, 0x2b65, 0x2b66,
  0x2b67, 0x2b68, 0x2b69, 0x2b6a, 0x2b6b, 0x2b6c, 0x2b6d, 0x2b6e,
  0x2b6f, 0x2b70, 0x2b71, 0x2b72, 0x2b73, 0x2b74, 0x2b75, 0x2b76,
  0x2421, 0x2422, 0x2423, 0x2424, 0x2425, 0x2426, 0x2427, 0x2428,
  0x2429, 0x242a, 0x242b, 0x242c, 0x242d, 0x242e, 0x242f, 0x2430,
  0x2431, 0x2432, 0x2433, 0x2434, 0x2435, 0x2436, 0x2437, 0x2438,
  0x2439, 0x243a, 0x243b, 0x243c, 0x243d, 0x243e, 0x243f, 0x2440,
  0x2441, 0x2442, 0x2443, 0x2444, 0x2445, 0x2446, 0x2447, 0x2448,
  0x2449, 0x244a, 0x244b, 0x244c, 0x244d, 0x244e, 0x244f, 0x2450,
  0x2451, 0x2452, 0x2453, 0x2454, 0x2455, 0x2456, 0x2457, 0x2458,
  0x2459, 0x245a, 0x245b, 0x245c, 0x245d, 0x245e, 0x245f, 0x2460,
  0x2461, 0x2462, 0x2463, 0x2464, 0x2465, 0x2466, 0x2467, 0x2468,
  0x2469, 0x246a, 0x246b, 0x246c, 0x246d, 0x246e, 0x246f, 0x2470,
  0x2471, 0x2472, 0x2473, 0x2474, 0x2475, 0x2476, 0x2477, 0x2478,
  0x2479, 0x247a, 0x247b, 0x247c, 0x247d, 0x247e, 0x2931, 0x2932,
  0x2933, 0x2934, 0x2935, 0x2936, 0x2937, 0x2938, 0x2939, 0x293a,
  0x293b, 0x293c, 0x293d, 0x293e, 0x293f, 0x2940, 0x2941, 0x2942,
  0x2943, 0x2944, 0x2945, 0x2946, 0x2947, 0x2948, 0x2949, 0x294a,
  0x294b, 0x294c, 0x225f, 0x2831, 0x2832, 0x2833, 0x2834, 0x2835,
  0x2836, 0x2837, 0x2838, 0x2839, 0x283a, 0x283b, 0x283c, 0x283d,
  0x283e, 0x283f, 0x2840, 0x2841, 0x2842, 0x2843, 0x2844, 0x2845,
  0x2846, 0x2847, 0x2848, 0x2849, 0x284a, 0x284b, 0x284c, 0x2268,
  0x225e, 0x2749, 0x274a, 0x274b, 0x274c, 0x274d, 0x273a, 0x273b,
  0x275c, 0x275d, 0x275e, 0x2736, 0x2737, 0x2738, 0x2754, 0x2755,
  0x2756, 0x2757, 0x2758, 0x2721, 0x2722, 0x2723, 0x2725, 0x272b,
  0x272c, 0x272d, 0x272e, 0x272f, 0x2730, 0x2731, 0x2732, 0x2733,
  0x2734, 0x2727, 0x2728, 0x2729, 0x272a, 0x273d, 0x273e, 0x2765,
  0x2766, 0x2767, 0x2768, 0x2761, 0x2762, 0x2763, 0x273f, 0x2740,
  0x2741, 0x2742, 0x2743, 0x2744, 0x2745, 0x2746, 0x2747, 0x2748,
  0x274e, 0x274f, 0x2750, 0x2751, 0x2752, 0x2753, 0x275a, 0x275b,
  0x2263, 0x276c, 0x2726, 0x2760, 0x276f, 0x2261, 0x273c, 0x276d,
  0x2735, 0x2739, 0x276a, 0x276b, 0x275f, 0x2264, 0x2764, 0x276e,
  0x2769, 0x6c69, 0x6f4b, 0x7652, 0x5832, 0x6d5b, 0x5f32, 0x5f3e,
  0x793b, 0x5c74, 0x7564, 0x7326, 0x5d60, 0x6126, 0x4e78, 0x5c30,
  0x632a, 0x7169, 0x4d7a, 0x7c2f, 0x5321, 0x712b, 0x6751, 0x522c,
  0x4e79, 0x717d, 0x5e3f, 0x7b3a, 0x7939, 0x4e52, 0x632b, 0x6b60,
  0x4e7a, 0x4b77, 0x6525, 0x4a61, 0x544c, 0x6a61, 0x5c63, 0x5f2d,
  0x4b6b, 0x552f, 0x5675, 0x6578, 0x5e40, 0x6c23, 0x694d, 0x6a27,
  0x6976, 0x7b3b, 0x6769, 0x6f4c, 0x5066, 0x5e41, 0x642c, 0x584c,
  0x7971, 0x4e5f, 0x7a24, 0x6632, 0x7a7b, 0x7a3d, 0x4c48, 0x6f4d,
  0x5555, 0x5322, 0x6c51, 0x6427, 0x6c52, 0x7631, 0x4e7b, 0x5051,
  0x4b3f, 0x6d24, 0x6d28, 0x5e42, 0x7662, 0x6d5c, 0x5c75, 0x6039,
  0x544e, 0x7435, 0x535b, 0x5635, 0x6c24, 0x6466, 0x716a, 0x4b6c,
  0x4b40, 0x6c72, 0x506a, 0x7972, 0x6c25, 0x505f, 0x676a, 0x506b,
  0x5c51, 0x5b69, 0x7d4c, 0x5b57, 0x5a61, 0x5636, 0x635f, 0x5e43,
  0x5e44, 0x4a21, 0x6e6c, 0x5323, 0x6e37, 0x784f, 0x6a48, 0x6e38,
  0x712c, 0x7125, 0x694e, 0x793c, 0x6579, 0x6c6a, 0x5d56, 0x6d42,
  0x7825, 0x653a, 0x5b58, 0x4a22, 0x514d, 0x6e6d, 0x6c6b, 0x5e45,
  0x6360, 0x4a49, 0x7269, 0x554e, 0x7636, 0x4e42, 0x5647, 0x6334,
  0x712d, 0x6a62, 0x5742, 0x7327, 0x4d6a, 0x6b6e, 0x5932, 0x7d25,
  0x7655, 0x5562, 0x7835, 0x4c75, 0x7535, 0x642d, 0x676b, 0x7155,
  0x703b, 0x6935, 0x4c49, 0x7a55, 0x6154, 0x5756, 0x5c41, 0x5e46,
  0x7a6f, 0x6361, 0x6173, 0x5c76, 0x4e7c, 0x5b44, 0x7871, 0x5c64,
  0x656f, 0x5c31, 0x5556, 0x735a, 0x4b41, 0x5b43, 0x597a, 0x536e,
  0x7a38, 0x7d26, 0x6b6f, 0x7426, 0x4c4a, 0x7328, 0x735b, 0x5b27,
  0x7637, 0x4f66, 0x7072, 0x4b5a, 0x6752, 0x5743, 0x7670, 0x685e,
  0x6526, 0x6567, 0x4a23, 0x4c27, 0x6a49, 0x7836, 0x7a25, 0x712e,
  0x6f4e, 0x4b6d, 0x7630, 0x6f4f, 0x694f, 0x775e, 0x4e53, 0x5c77,
  0x5b28, 0x4b78, 0x5f21, 0x5d61, 0x754a, 0x6936, 0x676c, 0x6e6e,
  0x7370, 0x5f3f, 0x4c4b, 0x5041, 0x7452, 0x603a, 0x5f40, 0x4e60,
  0x5c52, 0x7d6a, 0x5676, 0x6a4a, 0x6869, 0x632c, 0x7350, 0x4a24,
  0x5b78, 0x5e47, 0x6b70, 0x7156, 0x6562, 0x4c4c, 0x4b7b, 0x6a63,
  0x5f41, 0x566d, 0x6950, 0x6e39, 0x5563, 0x5153, 0x6570, 0x6834,
  0x6b43, 0x6a2a, 0x7a7c, 0x7576, 0x703c, 0x7d54, 0x603b, 0x4e43,
  0x503a, 0x773a, 0x5873, 0x774d, 0x642e, 0x545f, 0x5067, 0x6c7d,
  0x522e, 0x6e6f, 0x5557, 0x6a64, 0x7822, 0x4d6b, 0x573f, 0x7b31,
  0x4d6c, 0x5c32, 0x506c, 0x4e7d, 0x6e70, 0x4c42, 0x506d, 0x6577,
  0x737c, 0x6e22, 0x5933, 0x5874, 0x6937, 0x4e2e, 0x5922, 0x5871,
  0x544f, 0x6527, 0x5552, 0x5629, 0x7422, 0x7157, 0x5558, 0x703d,
  0x5750, 0x5450, 0x574f, 0x6b6a, 0x7d6b, 0x5b6d, 0x7c45, 0x4b42,
  0x7d55, 0x7448, 0x686a, 0x7573, 0x795e, 0x536f, 0x6c53, 0x5d42,
  0x6f37, 0x6754, 0x4a4a, 0x597b, 0x7a7d, 0x562a, 0x7478, 0x7777,
  0x5c2c, 0x5757, 0x5f22, 0x4e3e, 0x5370, 0x7024, 0x616c, 0x4f67,
  0x734b, 0x6d29, 0x4a3e, 0x746f, 0x764e, 0x5e7b, 0x503b, 0x5537,
  0x6e71, 0x7428, 0x5c78, 0x4b27, 0x5a4e, 0x6066, 0x6d25, 0x6e72,
  0x5c79, 0x795c, 0x735c, 0x7872, 0x7479, 0x7c71, 0x503c, 0x5b79,
  0x5731, 0x4b7c, 0x7025, 0x4b7d, 0x5574, 0x4d6d, 0x4a25, 0x562b,
  0x5042, 0x703e, 0x523d, 0x4c24, 0x7a36, 0x4c4d, 0x5a7a, 0x764f,
  0x6938, 0x5875, 0x4c4e, 0x574d, 0x5451, 0x696d, 0x4a6b, 0x5962,
  0x7d32, 0x632d, 0x564c, 0x5934, 0x6127, 0x6e53, 0x5043, 0x7d33,
  0x5564, 0x4f68, 0x6d43, 0x5032, 0x4e7e, 0x5a28, 0x7850, 0x7d56,
  0x7851, 0x7852, 0x5c53, 0x5d62, 0x7b79, 0x5d41, 0x6335, 0x6d5d,
  0x4e44, 0x4b21, 0x5d63, 0x7c5d, 0x792f, 0x527b, 0x4f21, 0x6428,
  0x7436, 0x6c7e, 0x632e, 0x676d, 0x7d41, 0x5a62, 0x5833, 0x5d64,
  0x706f, 0x7671, 0x7a70, 0x5175, 0x5a4f, 0x5c54, 0x5c26, 0x6f3f,
  0x4e4f, 0x6059, 0x5956, 0x6c54, 0x6a4b, 0x4a3f, 0x5530, 0x4f69,
  0x716d, 0x4c4f, 0x6478, 0x646d, 0x5758, 0x7d27, 0x6a2b, 0x7632,
  0x4f70, 0x793d, 0x6674, 0x4b5b, 0x7351, 0x6951, 0x7329, 0x5060,
  0x6952, 0x5a63, 0x6252, 0x7622, 0x6174, 0x5a64, 0x6755, 0x753f,
  0x4f22, 0x4d2f, 0x4f23, 0x4d30, 0x717e, 0x5023, 0x612f, 0x7823,
  0x4a26, 0x773b, 0x726a, 0x5e48, 0x6953, 0x5e49, 0x7d5e, 0x4a40,
  0x796a, 0x514e, 0x6e54, 0x5452, 0x5923, 0x7d28, 0x5759, 0x774e,
  0x7a3e, 0x4f56, 0x5770, 0x6b61, 0x7845, 0x5c7a, 0x5d43, 0x795f,
  0x676f, 0x7d65, 0x7623, 0x597c, 0x7d29, 0x676e, 0x5565, 0x6f50,
  0x4d31, 0x7722, 0x7132, 0x7131, 0x4d32, 0x5a2b, 0x4a27, 0x6362,
  0x7b3c, 0x5924, 0x6e3a, 0x7853, 0x7b7a, 0x4f24, 0x5c7b, 0x7663,
  0x6d2a, 0x7221, 0x4e61, 0x7a26, 0x7960, 0x6c56, 0x646e, 0x7921,
  0x7b6f, 0x796b, 0x6e23, 0x6a2c, 0x4a28, 0x747a, 0x4d56, 0x7c76,
  0x7449, 0x7854, 0x7826, 0x5e4a, 0x7246, 0x575a, 0x5350, 0x5845,
  0x6a66, 0x735d, 0x645a, 0x7664, 0x7672, 0x5f42, 0x597d, 0x4c76,
  0x533a, 0x642f, 0x7961, 0x7026, 0x4b53, 0x603c, 0x744a, 0x547a,
  0x7d2a, 0x7962, 0x7437, 0x7d42, 0x7c30, 0x7d6c, 0x4a62, 0x7d3d,
  0x6a67, 0x5f43, 0x5152, 0x4e62, 0x5324, 0x7d2b, 0x5f60, 0x7247,
  0x6770, 0x506e, 0x732a, 0x5e4b, 0x7638, 0x6175, 0x7133, 0x7723,
  0x4a29, 0x4f25, 0x5f44, 0x6130, 0x703f, 0x7624, 0x6336, 0x7a46,
  0x506f, 0x7d6d, 0x5d44, 0x7c77, 0x663f, 0x5e2d, 0x7a3f, 0x6571,
  0x6d44, 0x5225, 0x7d6e, 0x7536, 0x6176, 0x5e4c, 0x7c5e, 0x6c57,
  0x4d5d, 0x5637, 0x4d33, 0x7855, 0x6558, 0x4f6a, 0x4f50, 0x6a4c,
  0x6a2e, 0x6a2d, 0x5371, 0x5325, 0x774f, 0x6e24, 0x5024, 0x7222,
  0x5070, 0x7223, 0x7778, 0x5033, 0x5b29, 0x533b, 0x4a6c, 0x7126,
  0x4b55, 0x7767, 0x4d5e, 0x7724, 0x7840, 0x535d, 0x4c50, 0x4f26,
  0x7673, 0x6177, 0x535c, 0x7a7e, 0x7a27, 0x6b59, 0x4f27, 0x6a2f,
  0x646f, 0x6939, 0x7158, 0x5858, 0x6072, 0x6634, 0x5c7c, 0x7371,
  0x6350, 0x727b, 0x5b46, 0x5071, 0x5072, 0x4f5c, 0x5351, 0x4c31,
  0x7758, 0x4b28, 0x6b3c, 0x643e, 0x745c, 0x5c42, 0x7027, 0x6640,
  0x4a6d, 0x686b, 0x6568, 0x5c43, 0x6d5e, 0x5372, 0x4c77, 0x4e54,
  0x672b, 0x4b43, 0x6131, 0x7732, 0x5373, 0x5352, 0x7540, 0x5f5d,
  0x6e73, 0x6771, 0x7d34, 0x7248, 0x7352, 0x6e74, 0x6253, 0x4c51,
  0x5f6a, 0x693a, 0x5957, 0x754d, 0x7172, 0x7a47, 0x5978, 0x5442,
  0x7665, 0x5d45, 0x6772, 0x6d5f, 0x4a4b, 0x5b7a, 0x6835, 0x5326,
  0x7d35, 0x7949, 0x6462, 0x7b3d, 0x5724, 0x4e45, 0x4e55, 0x5666,
  0x653d, 0x5e4d, 0x6c73, 0x6d60, 0x6c6c, 0x7b3e, 0x5f6b, 0x6178,
  0x793e, 0x5073, 0x602a, 0x6862, 0x6254, 0x527d, 0x6528, 0x5953,
  0x535e, 0x7438, 0x773c, 0x5c7d, 0x686c, 0x6467, 0x6377, 0x6c28,
  0x7a71, 0x6572, 0x5074, 0x522f, 0x5c65, 0x5025, 0x7134, 0x7c31,
  0x4c78, 0x5d46, 0x7a51, 0x775f, 0x7a28, 0x6e75, 0x5e4e, 0x6773,
  0x772c, 0x6b44, 0x6d61, 0x602b, 0x5d47, 0x5233, 0x523f, 0x4a4c,
  0x7b3f, 0x657d, 0x5d65, 0x584d, 0x6c74, 0x5075, 0x686d, 0x5052,
  0x5958, 0x7666, 0x5b2a, 0x7760, 0x5859, 0x7423, 0x745d, 0x6f51,
  0x5935, 0x6d2b, 0x6337, 0x6e3b, 0x4d34, 0x6073, 0x6a4d, 0x6c75,
  0x686e, 0x4b29, 0x712f, 0x4a4d, 0x6c29, 0x726b, 0x7d6f, 0x7973,
  0x6641, 0x6c58, 0x6d2c, 0x6a4e, 0x685f, 0x5e4f, 0x5226, 0x6774,
  0x5156, 0x6642, 0x6363, 0x6430, 0x5834, 0x7625, 0x735e, 0x5725,
  0x7768, 0x6846, 0x7b66, 0x5d66, 0x5c7e, 0x585a, 0x5a2c, 0x6a30,
  0x6338, 0x4a2a, 0x6179, 0x6a31, 0x726c, 0x7a6e, 0x6e55, 0x7974,
  0x526c, 0x7b7b, 0x7d70, 0x603d, 0x4e63, 0x7846, 0x5e2e, 0x5f45,
  0x653e, 0x6d2d, 0x7a6a, 0x4d6e, 0x6d26, 0x6d2e, 0x706d, 0x5d21,
  0x6d2f, 0x7c78, 0x586b, 0x4c79, 0x4d35, 0x7a29, 0x615d, 0x6255,
  0x6d4f, 0x5d22, 0x794a, 0x6a68, 0x656d, 0x536b, 0x6954, 0x617a,
  0x644c, 0x6164, 0x6847, 0x4e5b, 0x5c55, 0x7735, 0x7c73, 0x7073,
  0x4e2f, 0x7135, 0x6f52, 0x6848, 0x6b71, 0x4b54, 0x603e, 0x6378,
  0x6a69, 0x7c32, 0x6074, 0x4f60, 0x6e25, 0x7a2a, 0x6643, 0x6132,
  0x4a2b, 0x6364, 0x693b, 0x6256, 0x7372, 0x6e56, 0x6a32, 0x5076,
  0x6c59, 0x5a4b, 0x4f28, 0x5d23, 0x585b, 0x794e, 0x6955, 0x6351,
  0x523c, 0x582c, 0x734c, 0x4d7b, 0x7656, 0x6775, 0x686f, 0x6379,
  0x523b, 0x7373, 0x637b, 0x5e50, 0x4e30, 0x5677, 0x7159, 0x7541,
  0x5c44, 0x753b, 0x5e51, 0x5c66, 0x5e52, 0x6d62, 0x6e76, 0x6a4f,
  0x706e, 0x637c, 0x535f, 0x5374, 0x6133, 0x6134, 0x7453, 0x5f46,
  0x6956, 0x5b2b, 0x7626, 0x6339, 0x6b45, 0x7429, 0x4d36, 0x5279,
  0x5a2d, 0x5263, 0x4f51, 0x4b5c, 0x4c7a, 0x4f5d, 0x6829, 0x633b,
  0x633a, 0x605a, 0x6e77, 0x5c33, 0x5375, 0x5726, 0x7635, 0x575b,
  0x6155, 0x546a, 0x5f23, 0x7d5f, 0x5077, 0x6d54, 0x4b2a, 0x645b,
  0x617b, 0x4b22, 0x5360, 0x643f, 0x7b40, 0x5a3e, 0x644d, 0x5639,
  0x6f40, 0x617c, 0x7639, 0x5f47, 0x6431, 0x5c67, 0x5c68, 0x7a56,
  0x5376, 0x715a, 0x7a72, 0x627d, 0x554f, 0x5078, 0x4d5f, 0x754b,
  0x6470, 0x4b2b, 0x5744, 0x627e, 0x5d5a, 0x5a2e, 0x4a6e, 0x5539,
  0x6321, 0x6863, 0x732b, 0x4f29, 0x5377, 0x5471, 0x4e64, 0x6872,
  0x6575, 0x672e, 0x563a, 0x5f6c, 0x6440, 0x6864, 0x5835, 0x645c,
  0x7439, 0x7136, 0x625e, 0x6135, 0x4d6f, 0x7127, 0x4e65, 0x4b5d,
  0x5963, 0x732c, 0x5079, 0x6c2b, 0x5e53, 0x7769, 0x7975, 0x615e,
  0x4b6e, 0x633c, 0x7856, 0x5b6e, 0x7d71, 0x7736, 0x745e, 0x726d,
  0x5b59, 0x7028, 0x617d, 0x5e54, 0x602c, 0x6d63, 0x5361, 0x5f48,
  0x5936, 0x7d2c, 0x6f53, 0x6441, 0x786b, 0x5b2c, 0x7c46, 0x582d,
  0x763a, 0x5b5f, 0x5353, 0x7847, 0x4a4e, 0x7841, 0x5234, 0x5c34,
  0x7a39, 0x4a4f, 0x7c33, 0x6a6a, 0x6a6b, 0x507a, 0x6d64, 0x5d67,
  0x5f49, 0x5f6d, 0x6e3c, 0x6f41, 0x4c52, 0x5d24, 0x5f4a, 0x5378,
  0x7128, 0x4d37, 0x6f54, 0x645d, 0x5f6e, 0x4b2c, 0x693c, 0x6a6c,
  0x5f4b, 0x793f, 0x562f, 0x5546, 0x4f2a, 0x4e29, 0x5678, 0x7137,
  0x6e78, 0x5959, 0x735f, 0x7848, 0x4e46, 0x5566, 0x7466, 0x6645,
  0x6f55, 0x4b6f, 0x7c5f, 0x5c27, 0x5667, 0x7849, 0x6352, 0x633d,
  0x4f61, 0x7040, 0x6c5a, 0x5d57, 0x7b70, 0x6c2c, 0x7029, 0x7a57,
  0x7b41, 0x5240, 0x6530, 0x6d65, 0x4b2d, 0x7930, 0x7725, 0x4b2e,
  0x5a2f, 0x5836, 0x5327, 0x7b32, 0x7d44, 0x6c2d, 0x7b21, 0x6569,
  0x696e, 0x7374, 0x7873, 0x7041, 0x5e2f, 0x7830, 0x7360, 0x672f,
  0x5b2d, 0x6635, 0x7928, 0x5d58, 0x6859, 0x6f56, 0x5362, 0x625f,
  0x7c60, 0x5748, 0x7d2d, 0x5f6f, 0x4c53, 0x5379, 0x5470, 0x5b47,
  0x5e55, 0x7074, 0x5550, 0x6559, 0x7c47, 0x5c56, 0x6260, 0x5a30,
  0x7323, 0x536c, 0x744b, 0x7d45, 0x637d, 0x7931, 0x507b, 0x6c5b,
  0x753c, 0x7224, 0x584e, 0x584f, 0x7577, 0x7661, 0x5237, 0x7b6c,
  0x5d48, 0x6468, 0x5241, 0x7857, 0x563b, 0x5e56, 0x773d, 0x6c2e,
  0x5061, 0x6075, 0x6a33, 0x4e56, 0x4c25, 0x6c76, 0x6261, 0x633e,
  0x7c48, 0x4d70, 0x7976, 0x5f70, 0x653f, 0x4e3f, 0x7c61, 0x6d30,
  0x7d51, 0x763b, 0x794f, 0x6b5a, 0x4a41, 0x5238, 0x4d71, 0x6353,
  0x7d66, 0x666d, 0x637a, 0x702a, 0x7950, 0x7c62, 0x7827, 0x6165,
  0x6e79, 0x6776, 0x6a6d, 0x7c34, 0x7542, 0x575c, 0x7075, 0x5d68,
  0x536d, 0x757c, 0x5a3f, 0x4c7b, 0x537a, 0x7424, 0x6f57, 0x5443,
  0x7b63, 0x7b6d, 0x602d, 0x6a6e, 0x7b33, 0x6442, 0x7667, 0x525d,
  0x5f4c, 0x7c49, 0x6529, 0x6076, 0x7633, 0x617e, 0x4b70, 0x6a6f,
  0x6a70, 0x5a40, 0x7834, 0x6b72, 0x6443, 0x6957, 0x6471, 0x4a6f,
  0x4e57, 0x7c4a, 0x7361, 0x4b44, 0x6365, 0x4b45, 0x6a34, 0x693d,
  0x5749, 0x6b5b, 0x6d31, 0x4c43, 0x773e, 0x7c4b, 0x7874, 0x5937,
  0x7353, 0x7354, 0x7764, 0x7751, 0x5837, 0x4e31, 0x4a42, 0x7b34,
  0x4b46, 0x7076, 0x5567, 0x6a50, 0x4c54, 0x4b2f, 0x742a, 0x692f,
  0x7543, 0x6958, 0x5d69, 0x7173, 0x557b, 0x5e3b, 0x747b, 0x7d73,
  0x7d72, 0x7726, 0x5d49, 0x5453, 0x4c28, 0x5a41, 0x4c55, 0x5964,
  0x7a4a, 0x6563, 0x533c, 0x4a70, 0x5044, 0x4a50, 0x7a2b, 0x6b6b,
  0x6778, 0x5965, 0x5157, 0x7324, 0x547b, 0x7c63, 0x7a58, 0x7355,
  0x4f2b, 0x6b73, 0x557c, 0x5354, 0x4d7c, 0x5966, 0x6279, 0x6221,
  0x6b54, 0x6077, 0x6432, 0x4c7c, 0x7b64, 0x742b, 0x503d, 0x4a71,
  0x6f38, 0x5740, 0x6e7a, 0x7d74, 0x5363, 0x7b42, 0x5568, 0x5b2e,
  0x6136, 0x7837, 0x603f, 0x7b43, 0x5d6a, 0x6222, 0x6e26, 0x7668,
  0x7675, 0x5d4a, 0x5062, 0x5d26, 0x5d6b, 0x6479, 0x632f, 0x507c,
  0x747c, 0x4c3c, 0x776a, 0x6564, 0x5f71, 0x7761, 0x7977, 0x6f39,
  0x7858, 0x7929, 0x7859, 0x6e3d, 0x5846, 0x6463, 0x754e, 0x5d59,
  0x5967, 0x5239, 0x5543, 0x5a65, 0x5a50, 0x5159, 0x4e58, 0x4b5e,
  0x742c, 0x5a7b, 0x7669, 0x6873, 0x4f2c, 0x7070, 0x747d, 0x5b48,
  0x4e40, 0x6354, 0x514f, 0x7175, 0x4d72, 0x4f6b, 0x4d38, 0x6326,
  0x515a, 0x7225, 0x7226, 0x644e, 0x537b, 0x7129, 0x7249, 0x6f58,
  0x6649, 0x5838, 0x7a73, 0x7335, 0x7824, 0x5173, 0x6648, 0x785a,
  0x5c69, 0x5e57, 0x4b5f, 0x4f6c, 0x745f, 0x5174, 0x523a, 0x5f72,
  0x6137, 0x6223, 0x537c, 0x6d66, 0x5b49, 0x647a, 0x4f5e, 0x4e50,
  0x5553, 0x7375, 0x772e, 0x6f48, 0x4d73, 0x754f, 0x6573, 0x7042,
  0x4a51, 0x6a71, 0x5026, 0x595a, 0x702b, 0x6b67, 0x6540, 0x7c35,
  0x6444, 0x4c29, 0x7d46, 0x6a35, 0x652a, 0x5f3a, 0x615f, 0x5a51,
  0x6138, 0x6874, 0x537d, 0x6224, 0x724a, 0x5a66, 0x7733, 0x7d4d,
  0x7336, 0x6e57, 0x7544, 0x5824, 0x7227, 0x5938, 0x5939, 0x6f49,
  0x564e, 0x774b, 0x5f2e, 0x6875, 0x5235, 0x5355, 0x744c, 0x5a7c,
  0x5968, 0x776b, 0x7549, 0x733c, 0x5a52, 0x5335, 0x6836, 0x564f,
  0x743a, 0x7749, 0x4c2a, 0x7043, 0x4c56, 0x5053, 0x533d, 0x5b7b,
  0x4b60, 0x5364, 0x7677, 0x553a, 0x734d, 0x4b61, 0x6b74, 0x742d,
  0x7c2a, 0x776c, 0x6876, 0x5a67, 0x774c, 0x6541, 0x606e, 0x557d,
  0x4e66, 0x7c2b, 0x553b, 0x7228, 0x6225, 0x4d39, 0x6a72, 0x4b47,
  0x4d74, 0x5b2f, 0x6f59, 0x4d3a, 0x7c79, 0x5f73, 0x4e67, 0x5a42,
  0x4f2d, 0x6779, 0x7828, 0x7362, 0x4a72, 0x5f24, 0x5444, 0x4c57,
  0x6542, 0x4d3b, 0x6f5a, 0x6e58, 0x5d27, 0x6226, 0x6040, 0x5630,
  0x784a, 0x7c7a, 0x597e, 0x5e30, 0x5d6c, 0x5a68, 0x5460, 0x5679,
  0x4d57, 0x5e58, 0x7278, 0x6456, 0x5045, 0x742e, 0x5d28, 0x6d45,
  0x7356, 0x5e59, 0x6366, 0x5328, 0x5b30, 0x655a, 0x633f, 0x5b31,
  0x5569, 0x6041, 0x6f5b, 0x7069, 0x5732, 0x507d, 0x5969, 0x507e,
  0x6c6d, 0x5329, 0x7229, 0x7044, 0x6262, 0x696f, 0x7951, 0x6959,
  0x685a, 0x5a43, 0x5a44, 0x5445, 0x677a, 0x4d60, 0x6330, 0x5b32,
  0x7b44, 0x7363, 0x5925, 0x7b67, 0x5d4b, 0x5054, 0x6636, 0x602e,
  0x7d5a, 0x5c35, 0x6078, 0x6731, 0x7570, 0x585c, 0x6d46, 0x6139,
  0x6340, 0x7940, 0x6970, 0x595b, 0x7364, 0x5c36, 0x6469, 0x7045,
  0x6341, 0x7c4c, 0x7c4d, 0x724b, 0x724c, 0x644f, 0x715b, 0x7a59,
  0x7138, 0x7d75, 0x6079, 0x677b, 0x7c37, 0x7c64, 0x7b45, 0x6367,
  0x5839, 0x7678, 0x5c45, 0x4c58, 0x602f, 0x7467, 0x6f5c, 0x4f7c,
  0x6f5d, 0x722a, 0x7d3e, 0x4a2c, 0x7d3b, 0x7d47, 0x6732, 0x6a51,
  0x5f74, 0x516c, 0x645e, 0x6543, 0x5926, 0x4d3c, 0x7365, 0x6d55,
  0x593a, 0x6d67, 0x7b35, 0x786c, 0x6067, 0x4c59, 0x5446, 0x6725,
  0x5575, 0x533e, 0x7c7b, 0x6472, 0x5f75, 0x6878, 0x786d, 0x4e47,
  0x7d76, 0x6858, 0x4d58, 0x6756, 0x4c5a, 0x4a63, 0x5f76, 0x7047,
  0x7046, 0x583a, 0x7174, 0x7470, 0x754c, 0x7c65, 0x6a45, 0x6a73,
  0x5d5b, 0x5c57, 0x5e7d, 0x7279, 0x5547, 0x5850, 0x7048, 0x5121,
  0x5122, 0x5954, 0x5668, 0x594a, 0x5a31, 0x5847, 0x5c62, 0x734e,
  0x7574, 0x7139, 0x5a53, 0x766a, 0x4f75, 0x7d2e, 0x4a52, 0x5f34,
  0x575d, 0x7a3a, 0x6e27, 0x753d, 0x7875, 0x6d68, 0x5461, 0x5123,
  0x6156, 0x7978, 0x5b4a, 0x4b79, 0x5454, 0x595c, 0x6e3e, 0x776d,
  0x526e, 0x6166, 0x7779, 0x5d6d, 0x685b, 0x5b33, 0x5177, 0x6030,
  0x5462, 0x7657, 0x5779, 0x585d, 0x4d7d, 0x722b, 0x4d3d, 0x7842,
  0x722c, 0x4a2d, 0x4a2e, 0x4f2e, 0x6342, 0x5c37, 0x5b5a, 0x593b,
  0x4a73, 0x7653, 0x6678, 0x6a75, 0x6a76, 0x7679, 0x4f2f, 0x4a53,
  0x4a2f, 0x5230, 0x713a, 0x5733, 0x6343, 0x737d, 0x5e5a, 0x5e5b,
  0x6f5e, 0x6263, 0x6e7b, 0x5f77, 0x574a, 0x4e68, 0x5b5b, 0x713b,
  0x6971, 0x7a37, 0x5046, 0x4c2b, 0x6e28, 0x4b7a, 0x7979, 0x4c7d,
  0x537e, 0x6450, 0x726e, 0x5455, 0x5f4d, 0x7c38, 0x5150, 0x724d,
  0x7752, 0x4a54, 0x5559, 0x585e, 0x4d59, 0x6e29, 0x763c, 0x4c5b,
  0x7049, 0x7c7c, 0x6849, 0x747e, 0x677c, 0x575e, 0x5e5c, 0x702c,
  0x4c7e, 0x4d61, 0x613a, 0x5b6f, 0x5a32, 0x5125, 0x5c38, 0x5876,
  0x5124, 0x4d62, 0x5c6a, 0x7077, 0x704a, 0x503e, 0x5d5c, 0x5456,
  0x5356, 0x6d50, 0x4d21, 0x5f35, 0x5f78, 0x5421, 0x4e32, 0x684a,
  0x6b75, 0x6355, 0x7550, 0x7521, 0x5927, 0x652b, 0x664b, 0x7571,
  0x6545, 0x7923, 0x605b, 0x766b, 0x4b71, 0x596a, 0x7522, 0x5751,
  0x5178, 0x6a78, 0x6a79, 0x5a33, 0x6f5f, 0x716f, 0x6576, 0x6e3f,
  0x6264, 0x503f, 0x7a2c, 0x7551, 0x6733, 0x693e, 0x724e, 0x5b34,
  0x7c4e, 0x5d6e, 0x6734, 0x5734, 0x7734, 0x4d3e, 0x5a69, 0x4f30,
  0x7759, 0x7366, 0x4e59, 0x4e2a, 0x4b48, 0x5027, 0x704b, 0x5047,
  0x6445, 0x5b60, 0x555a, 0x5727, 0x6e40, 0x7876, 0x7552, 0x6d69,
  0x593c, 0x6546, 0x7523, 0x5a54, 0x6227, 0x7b7c, 0x715c, 0x4a74,
  0x687a, 0x4e69, 0x6978, 0x6265, 0x5039, 0x5472, 0x5126, 0x5f4e,
  0x7c74, 0x532a, 0x4c2c, 0x6f60, 0x6565, 0x5055, 0x5b7c, 0x7c66,
  0x4b7e, 0x6d6a, 0x5e31, 0x7963, 0x5422, 0x4f76, 0x5650, 0x556a,
  0x716e, 0x7a4b, 0x6521, 0x5531, 0x4f6d, 0x6d6b, 0x5532, 0x553c,
  0x7d62, 0x732d, 0x7d5b, 0x6930, 0x5127, 0x7d63, 0x4e33, 0x7d64,
  0x7a4e, 0x4a30, 0x7727, 0x4f31, 0x6622, 0x7c36, 0x722d, 0x6f61,
  0x732e, 0x5c46, 0x596b, 0x6860, 0x6128, 0x5576, 0x4f7d, 0x5e5d,
  0x5951, 0x646a, 0x724f, 0x773f, 0x6266, 0x6228, 0x6356, 0x6d51,
  0x6979, 0x5631, 0x5e32, 0x6068, 0x532b, 0x6b5c, 0x5f2f, 0x4a43,
  0x6e7c, 0x7d43, 0x6b76, 0x4f32, 0x596c, 0x593d, 0x585f, 0x5438,
  0x6b3e, 0x5d6f, 0x5d70, 0x5d71, 0x5d72, 0x593e, 0x7b46, 0x4f33,
  0x6e7d, 0x642b, 0x5a45, 0x586c, 0x5128, 0x6229, 0x5e3c, 0x6735,
  0x5b70, 0x6f62, 0x7170, 0x4f34, 0x5b71, 0x6031, 0x5f25, 0x7952,
  0x677d, 0x6623, 0x7b71, 0x4b30, 0x722e, 0x4d67, 0x685c, 0x6757,
  0x7740, 0x5063, 0x5a21, 0x4c3d, 0x5129, 0x5d4c, 0x637e, 0x512a,
  0x682a, 0x6a36, 0x797a, 0x664c, 0x7658, 0x5447, 0x594b, 0x5952,
  0x534b, 0x5877, 0x5a29, 0x7578, 0x5e5e, 0x722f, 0x7829, 0x5848,
  0x6e41, 0x7941, 0x5d73, 0x6a7a, 0x763d, 0x613b, 0x4d3f, 0x7454,
  0x664d, 0x7c4f, 0x7b22, 0x605c, 0x743b, 0x5a55, 0x7932, 0x7b72,
  0x5b76, 0x5e5f, 0x5b72, 0x785c, 0x776e, 0x6b68, 0x527a, 0x713c,
  0x7a5a, 0x5a6a, 0x5a46, 0x7741, 0x6736, 0x6547, 0x562c, 0x5c47,
  0x6129, 0x622a, 0x5526, 0x5457, 0x7250, 0x6a7b, 0x605d, 0x7b73,
  0x713d, 0x6267, 0x7d57, 0x4e48, 0x6a37, 0x7c40, 0x7d67, 0x776f,
  0x5735, 0x6f3a, 0x715d, 0x5e33, 0x684b, 0x785d, 0x7b47, 0x5548,
  0x575f, 0x5d29, 0x6931, 0x7a2d, 0x7659, 0x7a74, 0x782a, 0x666e,
  0x4c5c, 0x613c, 0x606f, 0x693f, 0x7c7d, 0x664e, 0x6157, 0x664f,
  0x7471, 0x6473, 0x647b, 0x7964, 0x6f63, 0x4f6e, 0x763e, 0x6032,
  0x7c7e, 0x512b, 0x577a, 0x7b48, 0x6257, 0x5423, 0x7078, 0x5728,
  0x6167, 0x533f, 0x6f64, 0x5745, 0x6b62, 0x7c67, 0x6422, 0x6268,
  0x6650, 0x7b68, 0x7468, 0x6574, 0x743c, 0x7455, 0x5f36, 0x7c39,
  0x6e42, 0x4a75, 0x6f65, 0x4b62, 0x5424, 0x5e60, 0x5a7d, 0x6446,
  0x683e, 0x605e, 0x7634, 0x6a52, 0x797b, 0x6042, 0x4a64, 0x6737,
  0x6a7d, 0x595d, 0x5a34, 0x6e2a, 0x7b69, 0x5b4b, 0x5a35, 0x713e,
  0x532c, 0x7b49, 0x5f4f, 0x5340, 0x6357, 0x6f66, 0x7c50, 0x6940,
  0x7553, 0x6c5c, 0x7737, 0x6a38, 0x5179, 0x5c48, 0x6a39, 0x715e,
  0x5736, 0x4f35, 0x5928, 0x6c6e, 0x5d2a, 0x4d22, 0x682e, 0x613d,
  0x7251, 0x6941, 0x527c, 0x5b35, 0x7367, 0x587e, 0x7c51, 0x6d32,
  0x742f, 0x7b23, 0x7c41, 0x6e2b, 0x5425, 0x7472, 0x6e59, 0x7b4a,
  0x4d63, 0x583b, 0x655b, 0x7877, 0x7654, 0x5729, 0x4b49, 0x6651,
  0x704c, 0x582e, 0x7953, 0x557e, 0x583c, 0x7230, 0x622b, 0x7368,
  0x6f42, 0x6d6c, 0x6738, 0x5a7e, 0x4c3e, 0x727c, 0x5a6b, 0x6258,
  0x6d56, 0x5651, 0x6033, 0x7c52, 0x6b48, 0x5341, 0x704d, 0x4f77,
  0x6d52, 0x5458, 0x5c49, 0x5771, 0x5f3b, 0x7325, 0x744d, 0x713f,
  0x7831, 0x697a, 0x7b4b, 0x4a55, 0x7954, 0x774a, 0x5648, 0x7c68,
  0x733d, 0x6e7e, 0x677e, 0x5342, 0x5336, 0x4c2d, 0x767a, 0x5632,
  0x5258, 0x6758, 0x6325, 0x6739, 0x702d, 0x7b4c, 0x6b21, 0x5426,
  0x7b4d, 0x553d, 0x715f, 0x767b, 0x5e34, 0x556b, 0x6548, 0x7b24,
  0x5439, 0x5e61, 0x6423, 0x5737, 0x786e, 0x5e35, 0x5652, 0x7955,
  0x673a, 0x6b55, 0x5577, 0x6f67, 0x613e, 0x7a2e, 0x5669, 0x566e,
  0x673b, 0x6c4b, 0x5533, 0x4e34, 0x7b25, 0x616e, 0x7728, 0x7b4e,
  0x583d, 0x7b7d, 0x7c69, 0x4f36, 0x6d47, 0x6e2c, 0x4c5d, 0x7627,
  0x667a, 0x7524, 0x7d5c, 0x6d33, 0x4e49, 0x6f68, 0x613f, 0x7a5b,
  0x4b63, 0x7729, 0x7b26, 0x5c39, 0x7140, 0x6d48, 0x6f43, 0x562d,
  0x7d4e, 0x6821, 0x7b74, 0x5527, 0x7176, 0x6653, 0x4c5e, 0x7832,
  0x5c6b, 0x7d36, 0x656a, 0x7160, 0x5b4c, 0x5d4d, 0x5448, 0x596d,
  0x7525, 0x667b, 0x6654, 0x7d48, 0x5621, 0x7d3f, 0x7c53, 0x6f21,
  0x673c, 0x516e, 0x6655, 0x6972, 0x5f30, 0x5860, 0x7c3a, 0x7d2f,
  0x704e, 0x5b61, 0x6549, 0x6d34, 0x6043, 0x6358, 0x697b, 0x6a28,
  0x7d37, 0x7b27, 0x6942, 0x7d77, 0x6259, 0x5c6c, 0x6822, 0x6670,
  0x7d78, 0x7d79, 0x763f, 0x6727, 0x6657, 0x5473, 0x5449, 0x567a,
  0x5772, 0x6140, 0x5b62, 0x6658, 0x673d, 0x704f, 0x733e, 0x622c,
  0x7537, 0x6070, 0x7d38, 0x6368, 0x5427, 0x687c, 0x7a52, 0x786f,
  0x5653, 0x5534, 0x7050, 0x7770, 0x6e33, 0x6a3a, 0x6a53, 0x6d49,
  0x5d2b, 0x652c, 0x7d21, 0x5f50, 0x6c33, 0x5f51, 0x6d6d, 0x7838,
  0x777a, 0x782b, 0x7460, 0x543a, 0x6433, 0x695a, 0x5e36, 0x593f,
  0x5940, 0x566f, 0x594c, 0x5a2a, 0x5f65, 0x7765, 0x4c32, 0x5f79,
  0x5760, 0x543b, 0x7d7a, 0x4c33, 0x5b73, 0x5f52, 0x4e4a, 0x6e5a,
  0x6464, 0x7b4f, 0x4f37, 0x6e43, 0x4e6a, 0x622d, 0x5761, 0x7a75,
  0x5549, 0x782c, 0x6759, 0x7369, 0x586d, 0x6344, 0x7071, 0x6865,
  0x607a, 0x6e44, 0x595e, 0x6b22, 0x6b23, 0x7c42, 0x6a3b, 0x682b,
  0x5e62, 0x6d6f, 0x6823, 0x4f71, 0x543c, 0x7c6a, 0x673e, 0x7c72,
  0x5634, 0x622e, 0x5337, 0x7a4c, 0x7a5c, 0x6d35, 0x6163, 0x682c,
  0x685d, 0x6f69, 0x743d, 0x4f38, 0x695b, 0x512c, 0x5a47, 0x6b49,
  0x684c, 0x5e37, 0x563c, 0x5365, 0x7a5d, 0x5a56, 0x4a31, 0x5a48,
  0x5f26, 0x7933, 0x7252, 0x4a44, 0x4e4b, 0x4d75, 0x7d30, 0x5528,
  0x7141, 0x6269, 0x5c4a, 0x6c34, 0x7a40, 0x7b28, 0x5028, 0x5a6c,
  0x596e, 0x607b, 0x6f6a, 0x7a5e, 0x6044, 0x4f39, 0x554a, 0x5762,
  0x622f, 0x5738, 0x684d, 0x765a, 0x6f22, 0x625a, 0x767c, 0x7b50,
  0x512d, 0x4d64, 0x512e, 0x5c6d, 0x684e, 0x7079, 0x4e35, 0x667c,
  0x577b, 0x5056, 0x5d75, 0x7771, 0x767d, 0x5b77, 0x7b6a, 0x695c,
  0x5941, 0x7572, 0x6045, 0x6a54, 0x7942, 0x6a3c, 0x5245, 0x7b51,
  0x6740, 0x6b25, 0x5f7a, 0x6322, 0x5739, 0x6943, 0x687d, 0x682f,
  0x7253, 0x7b29, 0x5825, 0x554b, 0x5048, 0x512f, 0x5763, 0x6046,
  0x5622, 0x6d70, 0x5773, 0x7c54, 0x5a57, 0x4c5f, 0x7254, 0x5130,
  0x4c60, 0x5b7d, 0x733f, 0x7051, 0x7c3b, 0x6230, 0x6625, 0x625b,
  0x5f5e, 0x6047, 0x726f, 0x4c61, 0x566a, 0x6742, 0x4e36, 0x7340,
  0x4d7e, 0x7b52, 0x7878, 0x777b, 0x683f, 0x6837, 0x6d36, 0x5c3a,
  0x4c34, 0x7177, 0x6838, 0x4a76, 0x6424, 0x7456, 0x5f66, 0x5f27,
  0x5f67, 0x6141, 0x6944, 0x5c4b, 0x6945, 0x6f23, 0x6b26, 0x4b23,
  0x6369, 0x517b, 0x6f24, 0x6f6b, 0x5034, 0x4d23, 0x6866, 0x6f25,
  0x534c, 0x5a6d, 0x573a, 0x7255, 0x7565, 0x596f, 0x7934, 0x5554,
  0x7d4f, 0x5b63, 0x7161, 0x6c36, 0x7b7e, 0x5357, 0x5131, 0x4b31,
  0x5132, 0x4b32, 0x7142, 0x7461, 0x7935, 0x6143, 0x6142, 0x6b77,
  0x5f28, 0x4b4a, 0x6639, 0x785e, 0x792a, 0x4a77, 0x6d37, 0x5338,
  0x7256, 0x5459, 0x6e45, 0x7270, 0x4a32, 0x5c3b, 0x7178, 0x6c37,
  0x654a, 0x7640, 0x7d5d, 0x5463, 0x4c62, 0x7754, 0x5765, 0x5343,
  0x5826, 0x7641, 0x5d76, 0x4d40, 0x655c, 0x654b, 0x6144, 0x6830,
  0x7430, 0x736a, 0x5a6e, 0x573b, 0x6231, 0x572a, 0x567b, 0x645f,
  0x4a56, 0x6b28, 0x5b7e, 0x7642, 0x6f3b, 0x547d, 0x6048, 0x6839,
  0x6f26, 0x4d24, 0x5474, 0x5b21, 0x5b5c, 0x5b5d, 0x6e5c, 0x4b4b,
  0x7c55, 0x4e6b, 0x4d41, 0x7b53, 0x792b, 0x7554, 0x5929, 0x695d,
  0x5b4d, 0x5d4e, 0x6743, 0x6c4c, 0x796c, 0x4b4c, 0x607c, 0x5428,
  0x6d53, 0x586f, 0x7257, 0x4a78, 0x5a6f, 0x5654, 0x594d, 0x586e,
  0x7241, 0x5f53, 0x5a70, 0x626a, 0x607d, 0x5878, 0x772f, 0x5a36,
  0x4a57, 0x7258, 0x5879, 0x7a5f, 0x4f6f, 0x5942, 0x7052, 0x6451,
  0x7337, 0x7a60, 0x6f6c, 0x6232, 0x543d, 0x594e, 0x7462, 0x5429,
  0x4d42, 0x675a, 0x7259, 0x592a, 0x583e, 0x5c2d, 0x626b, 0x567c,
  0x4a79, 0x545a, 0x7457, 0x4c21, 0x4f3a, 0x7538, 0x5943, 0x5068,
  0x6345, 0x6b78, 0x7231, 0x4f3b, 0x532d, 0x6861, 0x4e6c, 0x6034,
  0x5e63, 0x5d77, 0x7232, 0x7376, 0x765b, 0x577e, 0x785f, 0x7772,
  0x5029, 0x665a, 0x7526, 0x573c, 0x4c63, 0x665b, 0x5d5d, 0x5133,
  0x6f6d, 0x565e, 0x6474, 0x616f, 0x5d78, 0x684f, 0x4a65, 0x5c21,
  0x6035, 0x7c2c, 0x7c2d, 0x5827, 0x6d38, 0x5b36, 0x5670, 0x732f,
  0x4d25, 0x5a71, 0x5828, 0x4c64, 0x5134, 0x4a58, 0x5a72, 0x7527,
  0x7528, 0x6626, 0x556c, 0x5578, 0x5a73, 0x6346, 0x5e64, 0x5e65,
  0x5135, 0x5136, 0x5137, 0x7233, 0x695e, 0x7053, 0x7234, 0x7054,
  0x4b64, 0x7b54, 0x7566, 0x636a, 0x5e66, 0x5f54, 0x7879, 0x702e,
  0x5138, 0x565f, 0x5057, 0x7c21, 0x6f6e, 0x5c58, 0x695f, 0x655d,
  0x7d7b, 0x6049, 0x5649, 0x542a, 0x654c, 0x6960, 0x5058, 0x7c22,
  0x543e, 0x6233, 0x5e67, 0x5c3c, 0x5236, 0x7555, 0x4e21, 0x7529,
  0x5d79, 0x5d7a, 0x7055, 0x765f, 0x725a, 0x646b, 0x7271, 0x6c39,
  0x7d7c, 0x612a, 0x4a59, 0x6f6f, 0x752a, 0x6c79, 0x782d, 0x7242,
  0x7643, 0x5752, 0x7922, 0x7056, 0x707a, 0x7660, 0x6973, 0x7243,
  0x542b, 0x4a33, 0x4d26, 0x4d43, 0x4d5a, 0x594f, 0x7644, 0x6e5d,
  0x6744, 0x6234, 0x5f62, 0x675b, 0x6831, 0x7c2e, 0x654d, 0x7a6b,
  0x4f3c, 0x4f62, 0x4d76, 0x6f70, 0x743e, 0x544d, 0x7338, 0x6921,
  0x7272, 0x736b, 0x7057, 0x4f57, 0x4f5f, 0x6840, 0x6841, 0x4f63,
  0x6922, 0x502a, 0x7341, 0x502b, 0x5464, 0x6f3c, 0x5821, 0x595f,
  0x7357, 0x5c3d, 0x4c65, 0x6d71, 0x7162, 0x545b, 0x6235, 0x4a66,
  0x532e, 0x4c66, 0x7153, 0x7567, 0x4a5a, 0x7b6e, 0x6145, 0x5f69,
  0x6e5e, 0x7742, 0x5822, 0x5d2c, 0x702f, 0x563d, 0x612b, 0x7936,
  0x5475, 0x5049, 0x6f27, 0x626c, 0x5b6a, 0x4e4c, 0x7568, 0x7755,
  0x534d, 0x737e, 0x5035, 0x607e, 0x5f7b, 0x665d, 0x6824, 0x4b4d,
  0x6f28, 0x6e34, 0x5a58, 0x5139, 0x5f29, 0x7330, 0x4c44, 0x4e37,
  0x6f29, 0x5f55, 0x6d57, 0x6e46, 0x6f3d, 0x7c56, 0x5b74, 0x6f2a,
  0x7839, 0x7569, 0x6359, 0x6146, 0x543f, 0x5e68, 0x706a, 0x7342,
  0x532f, 0x4a5b, 0x7c57, 0x6d58, 0x6147, 0x7458, 0x5633, 0x5d2d,
  0x553e, 0x7143, 0x6e5f, 0x566b, 0x7459, 0x5766, 0x5a37, 0x5d7b,
  0x5d4f, 0x5823, 0x5a59, 0x7058, 0x6f44, 0x6158, 0x7154, 0x6d72,
  0x555b, 0x555c, 0x7344, 0x4b57, 0x6236, 0x6f71, 0x7b55, 0x5358,
  0x5d50, 0x7059, 0x4b33, 0x555d, 0x4d27, 0x502c, 0x513a, 0x7144,
  0x6533, 0x7b75, 0x6961, 0x7d60, 0x7c3c, 0x5a22, 0x5a23, 0x5221,
  0x526f, 0x626d, 0x5e69, 0x4e5c, 0x7235, 0x5064, 0x5d51, 0x6148,
  0x5b37, 0x5f63, 0x6d39, 0x7145, 0x734f, 0x572b, 0x612c, 0x636b,
  0x6e47, 0x6149, 0x4a7a, 0x707b, 0x7a61, 0x705a, 0x4c67, 0x5a74,
  0x4c3f, 0x4e6d, 0x5529, 0x7a62, 0x5065, 0x6b56, 0x6c5f, 0x5f7c,
  0x7756, 0x5e6a, 0x4b34, 0x6f3e, 0x4c35, 0x4f3d, 0x6f72, 0x6237,
  0x4c68, 0x707c, 0x5660, 0x7146, 0x6238, 0x6b2b, 0x4b35, 0x5851,
  0x744e, 0x7377, 0x5746, 0x513b, 0x772a, 0x6d4a, 0x5753, 0x587a,
  0x7645, 0x514c, 0x5d7c, 0x5f7d, 0x7965, 0x604a, 0x727d, 0x5330,
  0x7473, 0x5a49, 0x665e, 0x783a, 0x6850, 0x587b, 0x6a55, 0x5623,
  0x7646, 0x725b, 0x647c, 0x6832, 0x5a5a, 0x725c, 0x7b56, 0x6932,
  0x6e2d, 0x7a63, 0x5c6e, 0x756a, 0x6660, 0x707d, 0x572c, 0x7545,
  0x6e60, 0x5b65, 0x5d5e, 0x5970, 0x6923, 0x7179, 0x7244, 0x604b,
  0x6924, 0x6239, 0x6331, 0x7c6b, 0x4d28, 0x4c36, 0x705b, 0x663a,
  0x4d29, 0x7343, 0x6159, 0x6f2b, 0x6745, 0x6069, 0x7345, 0x5440,
  0x553f, 0x5d2e, 0x797c, 0x4c40, 0x6522, 0x4e38, 0x5852, 0x7956,
  0x712a, 0x4e51, 0x7647, 0x5b6b, 0x5f7e, 0x5861, 0x7773, 0x5767,
  0x547e, 0x513c, 0x654f, 0x4b36, 0x5a38, 0x4d44, 0x563e, 0x623a,
  0x4f58, 0x604c, 0x6b79, 0x7d7d, 0x5768, 0x4b58, 0x6962, 0x683a,
  0x6347, 0x6c4d, 0x6c4e, 0x563f, 0x6327, 0x5f56, 0x7d68, 0x6e61,
  0x7628, 0x5d7d, 0x783b, 0x6851, 0x7957, 0x4e6e, 0x6c4f, 0x6925,
  0x5655, 0x4d45, 0x6d3a, 0x513d, 0x4f3e, 0x6c3b, 0x5231, 0x4c69,
  0x5944, 0x697c, 0x513e, 0x6c3c, 0x652d, 0x7730, 0x4c6a, 0x5344,
  0x5640, 0x567d, 0x6121, 0x5e3d, 0x7629, 0x5a24, 0x5624, 0x7546,
  0x6122, 0x6946, 0x7245, 0x7469, 0x566c, 0x6b53, 0x6c3d, 0x625c,
  0x5e6b, 0x705c, 0x6b3f, 0x574e, 0x513f, 0x752b, 0x797d, 0x4a5c,
  0x4d46, 0x7236, 0x5d7e, 0x4c37, 0x5b38, 0x5069, 0x4e5d, 0x6b40,
  0x7d22, 0x784b, 0x6a56, 0x7130, 0x5b4e, 0x7743, 0x5b4f, 0x4b24,
  0x7860, 0x7b57, 0x6b4a, 0x6021, 0x4e4d, 0x545c, 0x7d58, 0x5276,
  0x7237, 0x7a76, 0x762a, 0x7a77, 0x5866, 0x7431, 0x6852, 0x4a45,
  0x4c6b, 0x626e, 0x623b, 0x772d, 0x7861, 0x736c, 0x5e21, 0x647d,
  0x636c, 0x5d2f, 0x5d30, 0x4b37, 0x6853, 0x6123, 0x5260, 0x707e,
  0x6926, 0x4b72, 0x6d73, 0x5c59, 0x604d, 0x775a, 0x5b39, 0x4c2e,
  0x5a5b, 0x4d47, 0x5d31, 0x582f, 0x6323, 0x4e6f, 0x7273, 0x7833,
  0x604e, 0x757d, 0x6b6c, 0x5345, 0x7c6c, 0x525b, 0x546b, 0x5e22,
  0x6566, 0x7030, 0x5544, 0x6d74, 0x636d, 0x6842, 0x6d75, 0x577c,
  0x6d3b, 0x762b, 0x7238, 0x7648, 0x5366, 0x725d, 0x4f3f, 0x6b2c,
  0x4f40, 0x6628, 0x7d69, 0x4f41, 0x605f, 0x5e6c, 0x6022, 0x743f,
  0x626f, 0x5971, 0x7147, 0x4b38, 0x797e, 0x5b3a, 0x5a75, 0x766c,
  0x5a5c, 0x7a64, 0x604f, 0x5d32, 0x6629, 0x6f73, 0x736d, 0x6b7a,
  0x7966, 0x4a5d, 0x555e, 0x4a5e, 0x5f64, 0x667d, 0x752c, 0x6475,
  0x6963, 0x6d4b, 0x4f64, 0x5853, 0x5d33, 0x546c, 0x7239, 0x5f37,
  0x4b4e, 0x7b58, 0x5059, 0x5d52, 0x7774, 0x675c, 0x6425, 0x7c23,
  0x5b3b, 0x723a, 0x697d, 0x504a, 0x7556, 0x5945, 0x6434, 0x6d27,
  0x6a3d, 0x667e, 0x7744, 0x752d, 0x5960, 0x4a34, 0x7862, 0x4f42,
  0x6c3e, 0x6534, 0x4d48, 0x6e48, 0x6748, 0x4d49, 0x7937, 0x7168,
  0x5972, 0x5b75, 0x4a35, 0x5946, 0x5849, 0x592b, 0x6d3c, 0x5854,
  0x5c5a, 0x623c, 0x7c6d, 0x6c60, 0x527e, 0x6947, 0x662a, 0x6270,
  0x7a3b, 0x752e, 0x7b2a, 0x6c7b, 0x6c3f, 0x7c58, 0x5465, 0x7943,
  0x6e62, 0x5769, 0x6d76, 0x5e6d, 0x4c6c, 0x636e, 0x6854, 0x7a78,
  0x5d34, 0x6435, 0x5830, 0x5855, 0x746a, 0x4e39, 0x5661, 0x4f52,
  0x5036, 0x4e22, 0x736e, 0x7378, 0x5c4c, 0x504b, 0x7c24, 0x4d4a,
  0x5754, 0x5e23, 0x6460, 0x6e49, 0x625d, 0x757e, 0x542c, 0x5551,
  0x5870, 0x7843, 0x6a57, 0x7557, 0x583f, 0x7d40, 0x6b2d, 0x552a,
  0x6728, 0x6e4a, 0x4a67, 0x7863, 0x545d, 0x6a58, 0x7b59, 0x6d77,
  0x6535, 0x502d, 0x7171, 0x623d, 0x6348, 0x5955, 0x5f2a, 0x5b3c,
  0x7864, 0x717a, 0x6536, 0x736f, 0x7b5a, 0x6160, 0x592c, 0x756b,
  0x6036, 0x6948, 0x4b4f, 0x6349, 0x5e6e, 0x623e, 0x5c6f, 0x5625,
  0x6271, 0x567e, 0x5921, 0x5840, 0x5c5b, 0x6d3d, 0x5f38, 0x6a25,
  0x572d, 0x7379, 0x6d78, 0x7547, 0x614a, 0x6b63, 0x725e, 0x784c,
  0x6a59, 0x5346, 0x5b66, 0x752f, 0x4e70, 0x697e, 0x7b36, 0x6272,
  0x4f72, 0x7739, 0x5973, 0x614b, 0x5a5d, 0x5a39, 0x6b7b, 0x4b39,
  0x6d79, 0x6060, 0x7440, 0x7d3c, 0x5f31, 0x636f, 0x6023, 0x7d39,
  0x7031, 0x4d4b, 0x6d3e, 0x5540, 0x6370, 0x6d7a, 0x6964, 0x556d,
  0x675d, 0x5476, 0x6537, 0x5b67, 0x623f, 0x6e4b, 0x5774, 0x705d,
  0x4e2b, 0x675e, 0x5656, 0x614c, 0x6833, 0x656e, 0x5c22, 0x6050,
  0x5535, 0x5521, 0x7b5b, 0x794b, 0x4b73, 0x7425, 0x7a48, 0x5657,
  0x6965, 0x7b5c, 0x7d50, 0x7b76, 0x5a25, 0x5b3d, 0x6c62, 0x4d77,
  0x705e, 0x7649, 0x5e6f, 0x5331, 0x7c6e, 0x6843, 0x7148, 0x4e71,
  0x796d, 0x7274, 0x6436, 0x7539, 0x5c70, 0x6371, 0x6825, 0x723b,
  0x5e24, 0x5a4c, 0x4a69, 0x635a, 0x7c59, 0x6a5a, 0x7944, 0x6324,
  0x7b5d, 0x6f4a, 0x6844, 0x554c, 0x6b57, 0x592d, 0x7b2b, 0x5359,
  0x5522, 0x765e, 0x5a76, 0x6051, 0x6928, 0x7579, 0x7a2f, 0x6b7c,
  0x606a, 0x6332, 0x5545, 0x7163, 0x556e, 0x4d4c, 0x6d59, 0x5841,
  0x7a6c, 0x716b, 0x7a3c, 0x6662, 0x7a65, 0x627a, 0x4a36, 0x6437,
  0x6a5b, 0x757a, 0x7b2c, 0x4f43, 0x6b7d, 0x787a, 0x5f39, 0x6171,
  0x5224, 0x757b, 0x505a, 0x505b, 0x6a3e, 0x5931, 0x4a37, 0x5367,
  0x7865, 0x5332, 0x6240, 0x725f, 0x4d65, 0x792c, 0x4d4d, 0x6e2e,
  0x562e, 0x576a, 0x6760, 0x6b2e, 0x4f59, 0x5c4d, 0x6d7b, 0x5e70,
  0x576b, 0x5e25, 0x5f57, 0x5b50, 0x5b51, 0x5523, 0x7032, 0x5c5c,
  0x4a68, 0x7866, 0x5c4e, 0x6a5c, 0x5b52, 0x6933, 0x775b, 0x6328,
  0x572e, 0x6061, 0x4b3a, 0x6551, 0x505c, 0x5541, 0x584a, 0x6329,
  0x6024, 0x6929, 0x5347, 0x5c5d, 0x782e, 0x4c38, 0x502e, 0x5872,
  0x634a, 0x4c2f, 0x542d, 0x7651, 0x504c, 0x4a46, 0x5542, 0x4e3a,
  0x4a47, 0x7a30, 0x5f58, 0x753a, 0x656b, 0x6f74, 0x5d35, 0x4d2a,
  0x6372, 0x7b77, 0x7750, 0x7d3a, 0x7d61, 0x767e, 0x5140, 0x6845,
  0x6438, 0x6168, 0x4c41, 0x526d, 0x5b3e, 0x6062, 0x7a49, 0x614d,
  0x4a38, 0x7260, 0x7149, 0x5e71, 0x705f, 0x7844, 0x6e4c, 0x5e72,
  0x6749, 0x6273, 0x6761, 0x634b, 0x634c, 0x4f78, 0x6f2c, 0x7d7e,
  0x7c25, 0x7a31, 0x5f59, 0x6052, 0x745a, 0x714a, 0x4e23, 0x723c,
  0x6c63, 0x6025, 0x772b, 0x6b2f, 0x655e, 0x6124, 0x4d2b, 0x5974,
  0x6826, 0x4d4e, 0x6169, 0x7c6f, 0x6063, 0x6241, 0x4e24, 0x5e26,
  0x6b7e, 0x6b5d, 0x7060, 0x745b, 0x6274, 0x5348, 0x746b, 0x6e35,
  0x7558, 0x555f, 0x5665, 0x6b30, 0x7463, 0x634d, 0x7474, 0x7a32,
  0x6f75, 0x4a5f, 0x6b31, 0x6d3f, 0x7d49, 0x6426, 0x7924, 0x7033,
  0x656c, 0x5167, 0x5947, 0x6457, 0x6a5d, 0x5477, 0x5a3a, 0x5a4d,
  0x794c, 0x615a, 0x5b3f, 0x4c45, 0x6c50, 0x4b3b, 0x5e73, 0x692a,
  0x5948, 0x6e63, 0x573d, 0x4f44, 0x504d, 0x7c26, 0x717b, 0x7d52,
  0x5141, 0x635b, 0x5349, 0x5c4f, 0x4c6d, 0x5e27, 0x663b, 0x6c21,
  0x4c39, 0x7b5e, 0x6762, 0x5441, 0x5c28, 0x6242, 0x7358, 0x6553,
  0x7359, 0x7346, 0x4d5b, 0x4d2c, 0x7c43, 0x5467, 0x5142, 0x7925,
  0x6855, 0x634e, 0x544a, 0x5f5a, 0x7b5f, 0x6763, 0x787b, 0x634f,
  0x7530, 0x5867, 0x5949, 0x782f, 0x6f76, 0x5d36, 0x6e2f, 0x4d78,
  0x5e38, 0x7c27, 0x777c, 0x7731, 0x4e3b, 0x7421, 0x6e4d, 0x612e,
  0x6c43, 0x4f7e, 0x783f, 0x5862, 0x5368, 0x5e28, 0x7464, 0x6c42,
  0x5975, 0x7945, 0x5d53, 0x5671, 0x6c7c, 0x7c70, 0x6d40, 0x4a39,
  0x6e64, 0x7261, 0x5e39, 0x5672, 0x5e74, 0x5f5b, 0x5b53, 0x7a67,
  0x5863, 0x7441, 0x5d37, 0x7275, 0x542e, 0x5673, 0x5d38, 0x4f45,
  0x5f5f, 0x723e, 0x7621, 0x6b4b, 0x717c, 0x7347, 0x606b, 0x6d7c,
  0x615b, 0x6e65, 0x5e75, 0x7a53, 0x714b, 0x502f, 0x5d39, 0x5143,
  0x7531, 0x6a46, 0x7061, 0x762c, 0x7559, 0x706b, 0x5d3a, 0x723f,
  0x7745, 0x5b22, 0x7276, 0x4a3a, 0x7775, 0x4b65, 0x6e66, 0x6053,
  0x4e25, 0x5658, 0x542f, 0x6949, 0x534e, 0x7442, 0x4b66, 0x7121,
  0x6b32, 0x7122, 0x6b33, 0x7034, 0x4b74, 0x5430, 0x7332, 0x7b37,
  0x756c, 0x6e67, 0x7432, 0x756d, 0x4f73, 0x7062, 0x6e4e, 0x714c,
  0x6538, 0x5775, 0x6373, 0x4f65, 0x4f46, 0x7333, 0x6458, 0x4f79,
  0x4f5a, 0x7a4d, 0x6663, 0x7262, 0x756e, 0x4a3b, 0x635c, 0x4e72,
  0x5659, 0x6e30, 0x7465, 0x5842, 0x5c50, 0x4c6e, 0x5560, 0x764a,
  0x7d4a, 0x5856, 0x744f, 0x5626, 0x5c3e, 0x5b54, 0x5747, 0x727e,
  0x714d, 0x6243, 0x5c5e, 0x5c5f, 0x6f2d, 0x662b, 0x795d, 0x6a3f,
  0x6f2e, 0x7450, 0x4e73, 0x662c, 0x4e5e, 0x5579, 0x6374, 0x4d50,
  0x5538, 0x777d, 0x5c29, 0x5e76, 0x5c2a, 0x7263, 0x6934, 0x525c,
  0x6966, 0x6376, 0x674a, 0x504e, 0x5a77, 0x4a3c, 0x6e68, 0x5a5e,
  0x7277, 0x627b, 0x4c26, 0x5a3b, 0x6e69, 0x755a, 0x775c, 0x616a,
  0x4e41, 0x5431, 0x7d31, 0x663d, 0x7b2d, 0x7867, 0x614e, 0x7762,
  0x756f, 0x4f47, 0x5432, 0x4c6f, 0x5468, 0x6e4f, 0x7757, 0x6026,
  0x5641, 0x615c, 0x7063, 0x7164, 0x5c71, 0x5627, 0x7475, 0x714e,
  0x7264, 0x5030, 0x6c6f, 0x793a, 0x6b35, 0x546d, 0x6244, 0x6967,
  0x6b34, 0x6a21, 0x783c, 0x4e26, 0x7946, 0x7c5a, 0x5433, 0x5339,
  0x6a5e, 0x692b, 0x6161, 0x534f, 0x7476, 0x6a40, 0x614f, 0x4c3a,
  0x6e6a, 0x7064, 0x7334, 0x546e, 0x7240, 0x7165, 0x7443, 0x6054,
  0x6b36, 0x5721, 0x4b68, 0x792d, 0x692d, 0x5864, 0x7a33, 0x6245,
  0x7c3d, 0x6c44, 0x5831, 0x5c2b, 0x5524, 0x6b69, 0x683b, 0x5857,
  0x7b2e, 0x5161, 0x5b40, 0x753e, 0x5e77, 0x4a7b, 0x7746, 0x4f48,
  0x6150, 0x6e50, 0x6974, 0x4e74, 0x554d, 0x4f5b, 0x5d3b, 0x4e2c,
  0x6968, 0x5434, 0x6447, 0x755b, 0x7a41, 0x5e29, 0x5478, 0x6f77,
  0x5333, 0x6b37, 0x6f78, 0x755c, 0x6d4c, 0x5b55, 0x714f, 0x7150,
  0x7532, 0x592e, 0x552c, 0x6246, 0x7d23, 0x7b65, 0x5f2b, 0x6275,
  0x762d, 0x7533, 0x7035, 0x6125, 0x755d, 0x6c22, 0x6d7d, 0x7534,
  0x7b38, 0x5b23, 0x564a, 0x4b59, 0x6554, 0x737a, 0x6b38, 0x6037,
  0x576c, 0x716c, 0x652f, 0x5561, 0x576d, 0x5151, 0x6172, 0x6f79,
  0x5d3c, 0x765c, 0x7065, 0x7444, 0x6969, 0x737b, 0x546f, 0x4c22,
  0x777e, 0x5f3c, 0x6b4d, 0x5037, 0x5642, 0x682d, 0x6f2f, 0x4b25,
  0x4b69, 0x7a68, 0x4c46, 0x6667, 0x6a47, 0x5b24, 0x4f49, 0x627c,
  0x6f7a, 0x6b5e, 0x7548, 0x545e, 0x6055, 0x6f30, 0x6247, 0x592f,
  0x7967, 0x6765, 0x4f4a, 0x6151, 0x6248, 0x6f7b, 0x7a79, 0x5c72,
  0x6027, 0x7868, 0x4b6a, 0x4b3c, 0x5662, 0x755e, 0x755f, 0x6e36,
  0x6276, 0x534a, 0x6f7c, 0x5144, 0x6f31, 0x5145, 0x505e, 0x5961,
  0x6038, 0x4d51, 0x7339, 0x674c, 0x5628, 0x4e27, 0x5435, 0x6448,
  0x5334, 0x6b39, 0x4b75, 0x765d, 0x7123, 0x4c47, 0x694a, 0x6170,
  0x7560, 0x7b2f, 0x4b51, 0x7b60, 0x7265, 0x6c70, 0x706c, 0x6e6b,
  0x694b, 0x4c70, 0x572f, 0x7321, 0x7c75, 0x7124, 0x6056, 0x6f32,
  0x7451, 0x7721, 0x7151, 0x4a7c, 0x4a7d, 0x4e4e, 0x7348, 0x733a,
  0x6d7e, 0x5a26, 0x606c, 0x784d, 0x4b52, 0x6b4e, 0x7958, 0x7959,
  0x4a60, 0x5a4a, 0x4b26, 0x4a48, 0x796e, 0x5b6c, 0x5031, 0x556f,
  0x6673, 0x6722, 0x6459, 0x6461, 0x7c44, 0x796f, 0x4f74, 0x7766,
  0x4e3c, 0x7445, 0x5c23, 0x5d3d, 0x7446, 0x7821, 0x6856, 0x5b41,
  0x7066, 0x6439, 0x766d, 0x792e, 0x5d3e, 0x5730, 0x5868, 0x4b3d,
  0x795a, 0x784e, 0x7970, 0x606d, 0x6333, 0x7433, 0x6a42, 0x7266,
  0x7036, 0x5b56, 0x6b64, 0x7267, 0x5755, 0x5436, 0x7968, 0x5741,
  0x6555, 0x696a, 0x574c, 0x5369, 0x6249, 0x7c5b, 0x4d2d, 0x4c30,
  0x6a22, 0x6476, 0x5040, 0x7037, 0x6e21, 0x5776, 0x624a, 0x624b,
  0x7a4f, 0x6b5f, 0x564b, 0x7434, 0x6d4d, 0x6452, 0x6a29, 0x643a,
  0x7322, 0x4d52, 0x764b, 0x7166, 0x6d41, 0x683c, 0x6e51, 0x7067,
  0x624c, 0x642a, 0x7561, 0x6d5a, 0x576e, 0x5171, 0x696b, 0x696c,
  0x6064, 0x5a27, 0x5d54, 0x6a23, 0x5643, 0x5674, 0x5a5f, 0x6f33,
  0x624d, 0x6f7d, 0x7268, 0x6f45, 0x6767, 0x577d, 0x674e, 0x5f5c,
  0x7947, 0x5976, 0x5f2c, 0x565a, 0x5c24, 0x7038, 0x557a, 0x6477,
  0x5644, 0x746c, 0x6f7e, 0x7021, 0x5e2a, 0x5a3c, 0x587c, 0x7a54,
  0x6c65, 0x7c28, 0x6c66, 0x584b, 0x7b39, 0x6453, 0x4d79, 0x4f53,
  0x4a6a, 0x4f54, 0x783d, 0x7447, 0x6a5f, 0x795b, 0x5437, 0x6b65,
  0x6152, 0x6a24, 0x7a42, 0x7b61, 0x7a6d, 0x7022, 0x4c71, 0x7a23,
  0x6277, 0x624e, 0x6975, 0x616b, 0x6768, 0x6857, 0x5a78, 0x544b,
  0x7776, 0x5645, 0x5469, 0x7a7a, 0x4c72, 0x775d, 0x5e3a, 0x4e28,
  0x7039, 0x647e, 0x6449, 0x6454, 0x6a43, 0x6f34, 0x573e, 0x7b62,
  0x4d53, 0x6f35, 0x7a69, 0x7926, 0x5f3d, 0x7747, 0x787d, 0x787c,
  0x5e2b, 0x5b68, 0x635d, 0x6162, 0x5146, 0x7650, 0x6b66, 0x5a79,
  0x6c47, 0x5e78, 0x7869, 0x635e, 0x4e75, 0x7a43, 0x6557, 0x6c48,
  0x7349, 0x643b, 0x662e, 0x6f36, 0x5c3f, 0x4e3d, 0x5843, 0x504f,
  0x4f7a, 0x734a, 0x6057, 0x5147, 0x692e, 0x683d, 0x7a44, 0x624f,
  0x7a45, 0x7938, 0x5c60, 0x7b30, 0x5829, 0x655f, 0x7927, 0x766e,
  0x764c, 0x6278, 0x6c71, 0x5a60, 0x7152, 0x524c, 0x4f4b, 0x4a3d,
  0x5d3f, 0x766f, 0x5e79, 0x7a34, 0x552d, 0x7167, 0x5e3e, 0x5c40,
  0x5148, 0x5149, 0x783e, 0x4b76, 0x5479, 0x7562, 0x6153, 0x5869,
  0x787e, 0x4f4c, 0x7d24, 0x4e76, 0x7a50, 0x4c73, 0x663e, 0x762e,
  0x5570, 0x514a, 0x7c3e, 0x5571, 0x4d69, 0x7a35, 0x6250, 0x7477,
  0x4d54, 0x6723, 0x5b25, 0x6251, 0x5722, 0x7763, 0x6a26, 0x5021,
  0x4e5a, 0x7b6b, 0x5b26, 0x5b5e, 0x5865, 0x6a60, 0x582a, 0x6560,
  0x565b, 0x6f46, 0x786a, 0x6455, 0x4e77, 0x6058, 0x576f, 0x746d,
  0x4d66, 0x4c74, 0x7563, 0x644a, 0x5c61, 0x7948, 0x7c3f, 0x6827,
  0x5844, 0x4b3e, 0x5c2e, 0x5777, 0x7068, 0x5d40, 0x4f4d, 0x5c73,
  0x5930, 0x6669, 0x643c, 0x6a44, 0x646c, 0x6465, 0x7b78, 0x4c3b,
  0x643d, 0x4d5c, 0x5977, 0x5d5f, 0x6d4e, 0x5950, 0x6523, 0x794d,
  0x4d2e, 0x4f4e, 0x762f, 0x7d53, 0x6b6d, 0x565c, 0x6524, 0x5536,
  0x565d, 0x7969, 0x6724, 0x5663, 0x514b, 0x5664, 0x5572, 0x5e7a,
  0x5778, 0x586a, 0x4f55, 0x587d, 0x582b, 0x7d4b, 0x7c5c, 0x6028,
  0x5573, 0x7d59, 0x4c23, 0x5979, 0x536a, 0x7575, 0x6f47, 0x535a,
  0x5a3d, 0x6828, 0x5c2f, 0x7023, 0x4d55, 0x6029, 0x5e2c, 0x703a,
  0x6e31, 0x6e32, 0x764d, 0x6e52, 0x5646, 0x6065, 0x733b, 0x6561,
  0x644b, 0x5723, 0x5b42, 0x4a7e, 0x4f4f, 0x3021, 0x3022, 0x3023,
  0x3024, 0x3025, 0x3026, 0x3027, 0x3028, 0x3029, 0x302a, 0x302b,
  0x302c, 0x302d, 0x302e, 0x302f, 0x3030, 0x3031, 0x3032, 0x3033,
  0x3034, 0x3035, 0x3036, 0x3037, 0x3038, 0x3039, 0x303a, 0x303b,
  0x303c, 0x303d, 0x303e, 0x303f, 0x3040, 0x3041, 0x3042, 0x3043,
  0x3044, 0x3045, 0x3046, 0x3047, 0x3048, 0x3049, 0x304a, 0x304b,
  0x304c, 0x304d, 0x304e, 0x304f, 0x3050, 0x3051, 0x3052, 0x3053,
  0x3054, 0x3055, 0x3056, 0x3057, 0x3058, 0x3059, 0x305a, 0x305b,
  0x305c, 0x305d, 0x305e, 0x305f, 0x3060, 0x3061, 0x3062, 0x3063,
  0x3064, 0x3065, 0x3066, 0x3067, 0x3068, 0x3069, 0x306a, 0x306b,
  0x306c, 0x306d, 0x306e, 0x306f, 0x3070, 0x3071, 0x3072, 0x3073,
  0x3074, 0x3075, 0x3076, 0x3077, 0x3078, 0x3079, 0x307a, 0x307b,
  0x307c, 0x307d, 0x307e, 0x3121, 0x3122, 0x3123, 0x3124, 0x3125,
  0x3126, 0x3127, 0x3128, 0x3129, 0x312a, 0x312b, 0x312c, 0x312d,
  0x312e, 0x312f, 0x3130, 0x3131, 0x3132, 0x3133, 0x3134, 0x3135,
  0x3136, 0x3137, 0x3138, 0x3139, 0x313a, 0x313b, 0x313c, 0x313d,
  0x313e, 0x313f, 0x3140, 0x3141, 0x3142, 0x3143, 0x3144, 0x3145,
  0x3146, 0x3147, 0x3148, 0x3149, 0x314a, 0x314b, 0x314c, 0x314d,
  0x314e, 0x314f, 0x3150, 0x3151, 0x3152, 0x3153, 0x3154, 0x3155,
  0x3156, 0x3157, 0x3158, 0x3159, 0x315a, 0x315b, 0x315c, 0x315d,
  0x315e, 0x315f, 0x3160, 0x3161, 0x3162, 0x3163, 0x3164, 0x3165,
  0x3166, 0x3167, 0x3168, 0x3169, 0x316a, 0x316b, 0x316c, 0x316d,
  0x316e, 0x316f, 0x3170, 0x3171, 0x3172, 0x3173, 0x3174, 0x3175,
  0x3176, 0x3177, 0x3178, 0x3179, 0x317a, 0x317b, 0x317c, 0x317d,
  0x317e, 0x3221, 0x3222, 0x3223, 0x3224, 0x3225, 0x3226, 0x3227,
  0x3228, 0x3229, 0x322a, 0x322b, 0x322c, 0x322d, 0x322e, 0x322f,
  0x3230, 0x3231, 0x3232, 0x3233, 0x3234, 0x3235, 0x3236, 0x3237,
  0x3238, 0x3239, 0x323a, 0x323b, 0x323c, 0x323d, 0x323e, 0x323f,
  0x3240, 0x3241, 0x3242, 0x3243, 0x3244, 0x3245, 0x3246, 0x3247,
  0x3248, 0x3249, 0x324a, 0x324b, 0x324c, 0x324d, 0x324e, 0x324f,
  0x3250, 0x3251, 0x3252, 0x3253, 0x3254, 0x3255, 0x3256, 0x3257,
  0x3258, 0x3259, 0x325a, 0x325b, 0x325c, 0x325d, 0x325e, 0x325f,
  0x3260, 0x3261, 0x3262, 0x3263, 0x3264, 0x3265, 0x3266, 0x3267,
  0x3268, 0x3269, 0x326a, 0x326b, 0x326c, 0x326d, 0x326e, 0x326f,
  0x3270, 0x3271, 0x3272, 0x3273, 0x3274, 0x3275, 0x3276, 0x3277,
  0x3278, 0x3279, 0x327a, 0x327b, 0x327c, 0x327d, 0x327e, 0x3321,
  0x3322, 0x3323, 0x3324, 0x3325, 0x3326, 0x3327, 0x3328, 0x3329,
  0x332a, 0x332b, 0x332c, 0x332d, 0x332e, 0x332f, 0x3330, 0x3331,
  0x3332, 0x3333, 0x3334, 0x3335, 0x3336, 0x3337, 0x3338, 0x3339,
  0x333a, 0x333b, 0x333c, 0x333d, 0x333e, 0x333f, 0x3340, 0x3341,
  0x3342, 0x3343, 0x3344, 0x3345, 0x3346, 0x3347, 0x3348, 0x3349,
  0x334a, 0x334b, 0x334c, 0x334d, 0x334e, 0x334f, 0x3350, 0x3351,
  0x3352, 0x3353, 0x3354, 0x3355, 0x3356, 0x3357, 0x3358, 0x3359,
  0x335a, 0x335b, 0x335c, 0x335d, 0x335e, 0x335f, 0x3360, 0x3361,
  0x3362, 0x3363, 0x3364, 0x3365, 0x3366, 0x3367, 0x3368, 0x3369,
  0x336a, 0x336b, 0x336c, 0x336d, 0x336e, 0x336f, 0x3370, 0x3371,
  0x3372, 0x3373, 0x3374, 0x3375, 0x3376, 0x3377, 0x3378, 0x3379,
  0x337a, 0x337b, 0x337c, 0x337d, 0x337e, 0x3421, 0x3422, 0x3423,
  0x3424, 0x3425, 0x3426, 0x3427, 0x3428, 0x3429, 0x342a, 0x342b,
  0x342c, 0x342d, 0x342e, 0x342f, 0x3430, 0x3431, 0x3432, 0x3433,
  0x3434, 0x3435, 0x3436, 0x3437, 0x3438, 0x3439, 0x343a, 0x343b,
  0x343c, 0x343d, 0x343e, 0x343f, 0x3440, 0x3441, 0x3442, 0x3443,
  0x3444, 0x3445, 0x3446, 0x3447, 0x3448, 0x3449, 0x344a, 0x344b,
  0x344c, 0x344d, 0x344e, 0x344f, 0x3450, 0x3451, 0x3452, 0x3453,
  0x3454, 0x3455, 0x3456, 0x3457, 0x3458, 0x3459, 0x345a, 0x345b,
  0x345c, 0x345d, 0x345e, 0x345f, 0x3460, 0x3461, 0x3462, 0x3463,
  0x3464, 0x3465, 0x3466, 0x3467, 0x3468, 0x3469, 0x346a, 0x346b,
  0x346c, 0x346d, 0x346e, 0x346f, 0x3470, 0x3471, 0x3472, 0x3473,
  0x3474, 0x3475, 0x3476, 0x3477, 0x3478, 0x3479, 0x347a, 0x347b,
  0x347c, 0x347d, 0x347e, 0x3521, 0x3522, 0x3523, 0x3524, 0x3525,
  0x3526, 0x3527, 0x3528, 0x3529, 0x352a, 0x352b, 0x352c, 0x352d,
  0x352e, 0x352f, 0x3530, 0x3531, 0x3532, 0x3533, 0x3534, 0x3535,
  0x3536, 0x3537, 0x3538, 0x3539, 0x353a, 0x353b, 0x353c, 0x353d,
  0x353e, 0x353f, 0x3540, 0x3541, 0x3542, 0x3543, 0x3544, 0x3545,
  0x3546, 0x3547, 0x3548, 0x3549, 0x354a, 0x354b, 0x354c, 0x354d,
  0x354e, 0x354f, 0x3550, 0x3551, 0x3552, 0x3553, 0x3554, 0x3555,
  0x3556, 0x3557, 0x3558, 0x3559, 0x355a, 0x355b, 0x355c, 0x355d,
  0x355e, 0x355f, 0x3560, 0x3561, 0x3562, 0x3563, 0x3564, 0x3565,
  0x3566, 0x3567, 0x3568, 0x3569, 0x356a, 0x356b, 0x356c, 0x356d,
  0x356e, 0x356f, 0x3570, 0x3571, 0x3572, 0x3573, 0x3574, 0x3575,
  0x3576, 0x3577, 0x3578, 0x3579, 0x357a, 0x357b, 0x357c, 0x357d,
  0x357e, 0x3621, 0x3622, 0x3623, 0x3624, 0x3625, 0x3626, 0x3627,
  0x3628, 0x3629, 0x362a, 0x362b, 0x362c, 0x362d, 0x362e, 0x362f,
  0x3630, 0x3631, 0x3632, 0x3633, 0x3634, 0x3635, 0x3636, 0x3637,
  0x3638, 0x3639, 0x363a, 0x363b, 0x363c, 0x363d, 0x363e, 0x363f,
  0x3640, 0x3641, 0x3642, 0x3643, 0x3644, 0x3645, 0x3646, 0x3647,
  0x3648, 0x3649, 0x364a, 0x364b, 0x364c, 0x364d, 0x364e, 0x364f,
  0x3650, 0x3651, 0x3652, 0x3653, 0x3654, 0x3655, 0x3656, 0x3657,
  0x3658, 0x3659, 0x365a, 0x365b, 0x365c, 0x365d, 0x365e, 0x365f,
  0x3660, 0x3661, 0x3662, 0x3663, 0x3664, 0x3665, 0x3666, 0x3667,
  0x3668, 0x3669, 0x366a, 0x366b, 0x366c, 0x366d, 0x366e, 0x366f,
  0x3670, 0x3671, 0x3672, 0x3673, 0x3674, 0x3675, 0x3676, 0x3677,
  0x3678, 0x3679, 0x367a, 0x367b, 0x367c, 0x367d, 0x367e, 0x3721,
  0x3722, 0x3723, 0x3724, 0x3725, 0x3726, 0x3727, 0x3728, 0x3729,
  0x372a, 0x372b, 0x372c, 0x372d, 0x372e, 0x372f, 0x3730, 0x3731,
  0x3732, 0x3733, 0x3734, 0x3735, 0x3736, 0x3737, 0x3738, 0x3739,
  0x373a, 0x373b, 0x373c, 0x373d, 0x373e, 0x373f, 0x3740, 0x3741,
  0x3742, 0x3743, 0x3744, 0x3745, 0x3746, 0x3747, 0x3748, 0x3749,
  0x374a, 0x374b, 0x374c, 0x374d, 0x374e, 0x374f, 0x3750, 0x3751,
  0x3752, 0x3753, 0x3754, 0x3755, 0x3756, 0x3757, 0x3758, 0x3759,
  0x375a, 0x375b, 0x375c, 0x375d, 0x375e, 0x375f, 0x3760, 0x3761,
  0x3762, 0x3763, 0x3764, 0x3765, 0x3766, 0x3767, 0x3768, 0x3769,
  0x376a, 0x376b, 0x376c, 0x376d, 0x376e, 0x376f, 0x3770, 0x3771,
  0x3772, 0x3773, 0x3774, 0x3775, 0x3776, 0x3777, 0x3778, 0x3779,
  0x377a, 0x377b, 0x377c, 0x377d, 0x377e, 0x3821, 0x3822, 0x3823,
  0x3824, 0x3825, 0x3826, 0x3827, 0x3828, 0x3829, 0x382a, 0x382b,
  0x382c, 0x382d, 0x382e, 0x382f, 0x3830, 0x3831, 0x3832, 0x3833,
  0x3834, 0x3835, 0x3836, 0x3837, 0x3838, 0x3839, 0x383a, 0x383b,
  0x383c, 0x383d, 0x383e, 0x383f, 0x3840, 0x3841, 0x3842, 0x3843,
  0x3844, 0x3845, 0x3846, 0x3847, 0x3848, 0x3849, 0x384a, 0x384b,
  0x384c, 0x384d, 0x384e, 0x384f, 0x3850, 0x3851, 0x3852, 0x3853,
  0x3854, 0x3855, 0x3856, 0x3857, 0x3858, 0x3859, 0x385a, 0x385b,
  0x385c, 0x385d, 0x385e, 0x385f, 0x3860, 0x3861, 0x3862, 0x3863,
  0x3864, 0x3865, 0x3866, 0x3867, 0x3868, 0x3869, 0x386a, 0x386b,
  0x386c, 0x386d, 0x386e, 0x386f, 0x3870, 0x3871, 0x3872, 0x3873,
  0x3874, 0x3875, 0x3876, 0x3877, 0x3878, 0x3879, 0x387a, 0x387b,
  0x387c, 0x387d, 0x387e, 0x3921, 0x3922, 0x3923, 0x3924, 0x3925,
  0x3926, 0x3927, 0x3928, 0x3929, 0x392a, 0x392b, 0x392c, 0x392d,
  0x392e, 0x392f, 0x3930, 0x3931, 0x3932, 0x3933, 0x3934, 0x3935,
  0x3936, 0x3937, 0x3938, 0x3939, 0x393a, 0x393b, 0x393c, 0x393d,
  0x393e, 0x393f, 0x3940, 0x3941, 0x3942, 0x3943, 0x3944, 0x3945,
  0x3946, 0x3947, 0x3948, 0x3949, 0x394a, 0x394b, 0x394c, 0x394d,
  0x394e, 0x394f, 0x3950, 0x3951, 0x3952, 0x3953, 0x3954, 0x3955,
  0x3956, 0x3957, 0x3958, 0x3959, 0x395a, 0x395b, 0x395c, 0x395d,
  0x395e, 0x395f, 0x3960, 0x3961, 0x3962, 0x3963, 0x3964, 0x3965,
  0x3966, 0x3967, 0x3968, 0x3969, 0x396a, 0x396b, 0x396c, 0x396d,
  0x396e, 0x396f, 0x3970, 0x3971, 0x3972, 0x3973, 0x3974, 0x3975,
  0x3976, 0x3977, 0x3978, 0x3979, 0x397a, 0x397b, 0x397c, 0x397d,
  0x397e, 0x3a21, 0x3a22, 0x3a23, 0x3a24, 0x3a25, 0x3a26, 0x3a27,
  0x3a28, 0x3a29, 0x3a2a, 0x3a2b, 0x3a2c, 0x3a2d, 0x3a2e, 0x3a2f,
  0x3a30, 0x3a31, 0x3a32, 0x3a33, 0x3a34, 0x3a35, 0x3a36, 0x3a37,
  0x3a38, 0x3a39, 0x3a3a, 0x3a3b, 0x3a3c, 0x3a3d, 0x3a3e, 0x3a3f,
  0x3a40, 0x3a41, 0x3a42, 0x3a43, 0x3a44, 0x3a45, 0x3a46, 0x3a47,
  0x3a48, 0x3a49, 0x3a4a, 0x3a4b, 0x3a4c, 0x3a4d, 0x3a4e, 0x3a4f,
  0x3a50, 0x3a51, 0x3a52, 0x3a53, 0x3a54, 0x3a55, 0x3a56, 0x3a57,
  0x3a58, 0x3a59, 0x3a5a, 0x3a5b, 0x3a5c, 0x3a5d, 0x3a5e, 0x3a5f,
  0x3a60, 0x3a61, 0x3a62, 0x3a63, 0x3a64, 0x3a65, 0x3a66, 0x3a67,
  0x3a68, 0x3a69, 0x3a6a, 0x3a6b, 0x3a6c, 0x3a6d, 0x3a6e, 0x3a6f,
  0x3a70, 0x3a71, 0x3a72, 0x3a73, 0x3a74, 0x3a75, 0x3a76, 0x3a77,
  0x3a78, 0x3a79, 0x3a7a, 0x3a7b, 0x3a7c, 0x3a7d, 0x3a7e, 0x3b21,
  0x3b22, 0x3b23, 0x3b24, 0x3b25, 0x3b26, 0x3b27, 0x3b28, 0x3b29,
  0x3b2a, 0x3b2b, 0x3b2c, 0x3b2d, 0x3b2e, 0x3b2f, 0x3b30, 0x3b31,
  0x3b32, 0x3b33, 0x3b34, 0x3b35, 0x3b36, 0x3b37, 0x3b38, 0x3b39,
  0x3b3a, 0x3b3b, 0x3b3c, 0x3b3d, 0x3b3e, 0x3b3f, 0x3b40, 0x3b41,
  0x3b42, 0x3b43, 0x3b44, 0x3b45, 0x3b46, 0x3b47, 0x3b48, 0x3b49,
  0x3b4a, 0x3b4b, 0x3b4c, 0x3b4d, 0x3b4e, 0x3b4f, 0x3b50, 0x3b51,
  0x3b52, 0x3b53, 0x3b54, 0x3b55, 0x3b56, 0x3b57, 0x3b58, 0x3b59,
  0x3b5a, 0x3b5b, 0x3b5c, 0x3b5d, 0x3b5e, 0x3b5f, 0x3b60, 0x3b61,
  0x3b62, 0x3b63, 0x3b64, 0x3b65, 0x3b66, 0x3b67, 0x3b68, 0x3b69,
  0x3b6a, 0x3b6b, 0x3b6c, 0x3b6d, 0x3b6e, 0x3b6f, 0x3b70, 0x3b71,
  0x3b72, 0x3b73, 0x3b74, 0x3b75, 0x3b76, 0x3b77, 0x3b78, 0x3b79,
  0x3b7a, 0x3b7b, 0x3b7c, 0x3b7d, 0x3b7e, 0x3c21, 0x3c22, 0x3c23,
  0x3c24, 0x3c25, 0x3c26, 0x3c27, 0x3c28, 0x3c29, 0x3c2a, 0x3c2b,
  0x3c2c, 0x3c2d, 0x3c2e, 0x3c2f, 0x3c30, 0x3c31, 0x3c32, 0x3c33,
  0x3c34, 0x3c35, 0x3c36, 0x3c37, 0x3c38, 0x3c39, 0x3c3a, 0x3c3b,
  0x3c3c, 0x3c3d, 0x3c3e, 0x3c3f, 0x3c40, 0x3c41, 0x3c42, 0x3c43,
  0x3c44, 0x3c45, 0x3c46, 0x3c47, 0x3c48, 0x3c49, 0x3c4a, 0x3c4b,
  0x3c4c, 0x3c4d, 0x3c4e, 0x3c4f, 0x3c50, 0x3c51, 0x3c52, 0x3c53,
  0x3c54, 0x3c55, 0x3c56, 0x3c57, 0x3c58, 0x3c59, 0x3c5a, 0x3c5b,
  0x3c5c, 0x3c5d, 0x3c5e, 0x3c5f, 0x3c60, 0x3c61, 0x3c62, 0x3c63,
  0x3c64, 0x3c65, 0x3c66, 0x3c67, 0x3c68, 0x3c69, 0x3c6a, 0x3c6b,
  0x3c6c, 0x3c6d, 0x3c6e, 0x3c6f, 0x3c70, 0x3c71, 0x3c72, 0x3c73,
  0x3c74, 0x3c75, 0x3c76, 0x3c77, 0x3c78, 0x3c79, 0x3c7a, 0x3c7b,
  0x3c7c, 0x3c7d, 0x3c7e, 0x3d21, 0x3d22, 0x3d23, 0x3d24, 0x3d25,
  0x3d26, 0x3d27, 0x3d28, 0x3d29, 0x3d2a, 0x3d2b, 0x3d2c, 0x3d2d,
  0x3d2e, 0x3d2f, 0x3d30, 0x3d31, 0x3d32, 0x3d33, 0x3d34, 0x3d35,
  0x3d36, 0x3d37, 0x3d38, 0x3d39, 0x3d3a, 0x3d3b, 0x3d3c, 0x3d3d,
  0x3d3e, 0x3d3f, 0x3d40, 0x3d41, 0x3d42, 0x3d43, 0x3d44, 0x3d45,
  0x3d46, 0x3d47, 0x3d48, 0x3d49, 0x3d4a, 0x3d4b, 0x3d4c, 0x3d4d,
  0x3d4e, 0x3d4f, 0x3d50, 0x3d51, 0x3d52, 0x3d53, 0x3d54, 0x3d55,
  0x3d56, 0x3d57, 0x3d58, 0x3d59, 0x3d5a, 0x3d5b, 0x3d5c, 0x3d5d,
  0x3d5e, 0x3d5f, 0x3d60, 0x3d61, 0x3d62, 0x3d63, 0x3d64, 0x3d65,
  0x3d66, 0x3d67, 0x3d68, 0x3d69, 0x3d6a, 0x3d6b, 0x3d6c, 0x3d6d,
  0x3d6e, 0x3d6f, 0x3d70, 0x3d71, 0x3d72, 0x3d73, 0x3d74, 0x3d75,
  0x3d76, 0x3d77, 0x3d78, 0x3d79, 0x3d7a, 0x3d7b, 0x3d7c, 0x3d7d,
  0x3d7e, 0x3e21, 0x3e22, 0x3e23, 0x3e24, 0x3e25, 0x3e26, 0x3e27,
  0x3e28, 0x3e29, 0x3e2a, 0x3e2b, 0x3e2c, 0x3e2d, 0x3e2e, 0x3e2f,
  0x3e30, 0x3e31, 0x3e32, 0x3e33, 0x3e34, 0x3e35, 0x3e36, 0x3e37,
  0x3e38, 0x3e39, 0x3e3a, 0x3e3b, 0x3e3c, 0x3e3d, 0x3e3e, 0x3e3f,
  0x3e40, 0x3e41, 0x3e42, 0x3e43, 0x3e44, 0x3e45, 0x3e46, 0x3e47,
  0x3e48, 0x3e49, 0x3e4a, 0x3e4b, 0x3e4c, 0x3e4d, 0x3e4e, 0x3e4f,
  0x3e50, 0x3e51, 0x3e52, 0x3e53, 0x3e54, 0x3e55, 0x3e56, 0x3e57,
  0x3e58, 0x3e59, 0x3e5a, 0x3e5b, 0x3e5c, 0x3e5d, 0x3e5e, 0x3e5f,
  0x3e60, 0x3e61, 0x3e62, 0x3e63, 0x3e64, 0x3e65, 0x3e66, 0x3e67,
  0x3e68, 0x3e69, 0x3e6a, 0x3e6b, 0x3e6c, 0x3e6d, 0x3e6e, 0x3e6f,
  0x3e70, 0x3e71, 0x3e72, 0x3e73, 0x3e74, 0x3e75, 0x3e76, 0x3e77,
  0x3e78, 0x3e79, 0x3e7a, 0x3e7b, 0x3e7c, 0x3e7d, 0x3e7e, 0x3f21,
  0x3f22, 0x3f23, 0x3f24, 0x3f25, 0x3f26, 0x3f27, 0x3f28, 0x3f29,
  0x3f2a, 0x3f2b, 0x3f2c, 0x3f2d, 0x3f2e, 0x3f2f, 0x3f30, 0x3f31,
  0x3f32, 0x3f33, 0x3f34, 0x3f35, 0x3f36, 0x3f37, 0x3f38, 0x3f39,
  0x3f3a, 0x3f3b, 0x3f3c, 0x3f3d, 0x3f3e, 0x3f3f, 0x3f40, 0x3f41,
  0x3f42, 0x3f43, 0x3f44, 0x3f45, 0x3f46, 0x3f47, 0x3f48, 0x3f49,
  0x3f4a, 0x3f4b, 0x3f4c, 0x3f4d, 0x3f4e, 0x3f4f, 0x3f50, 0x3f51,
  0x3f52, 0x3f53, 0x3f54, 0x3f55, 0x3f56, 0x3f57, 0x3f58, 0x3f59,
  0x3f5a, 0x3f5b, 0x3f5c, 0x3f5d, 0x3f5e, 0x3f5f, 0x3f60, 0x3f61,
  0x3f62, 0x3f63, 0x3f64, 0x3f65, 0x3f66, 0x3f67, 0x3f68, 0x3f69,
  0x3f6a, 0x3f6b, 0x3f6c, 0x3f6d, 0x3f6e, 0x3f6f, 0x3f70, 0x3f71,
  0x3f72, 0x3f73, 0x3f74, 0x3f75, 0x3f76, 0x3f77, 0x3f78, 0x3f79,
  0x3f7a, 0x3f7b, 0x3f7c, 0x3f7d, 0x3f7e, 0x4021, 0x4022, 0x4023,
  0x4024, 0x4025, 0x4026, 0x4027, 0x4028, 0x4029, 0x402a, 0x402b,
  0x402c, 0x402d, 0x402e, 0x402f, 0x4030, 0x4031, 0x4032, 0x4033,
  0x4034, 0x4035, 0x4036, 0x4037, 0x4038, 0x4039, 0x403a, 0x403b,
  0x403c, 0x403d, 0x403e, 0x403f, 0x4040, 0x4041, 0x4042, 0x4043,
  0x4044, 0x4045, 0x4046, 0x4047, 0x4048, 0x4049, 0x404a, 0x404b,
  0x404c, 0x404d, 0x404e, 0x404f, 0x4050, 0x4051, 0x4052, 0x4053,
  0x4054, 0x4055, 0x4056, 0x4057, 0x4058, 0x4059, 0x405a, 0x405b,
  0x405c, 0x405d, 0x405e, 0x405f, 0x4060, 0x4061, 0x4062, 0x4063,
  0x4064, 0x4065, 0x4066, 0x4067, 0x4068, 0x4069, 0x406a, 0x406b,
  0x406c, 0x406d, 0x406e, 0x406f, 0x4070, 0x4071, 0x4072, 0x4073,
  0x4074, 0x4075, 0x4076, 0x4077, 0x4078, 0x4079, 0x407a, 0x407b,
  0x407c, 0x407d, 0x407e, 0x4121, 0x4122, 0x4123, 0x4124, 0x4125,
  0x4126, 0x4127, 0x4128, 0x4129, 0x412a, 0x412b, 0x412c, 0x412d,
  0x412e, 0x412f, 0x4130, 0x4131, 0x4132, 0x4133, 0x4134, 0x4135,
  0x4136, 0x4137, 0x4138, 0x4139, 0x413a, 0x413b, 0x413c, 0x413d,
  0x413e, 0x413f, 0x4140, 0x4141, 0x4142, 0x4143, 0x4144, 0x4145,
  0x4146, 0x4147, 0x4148, 0x4149, 0x414a, 0x414b, 0x414c, 0x414d,
  0x414e, 0x414f, 0x4150, 0x4151, 0x4152, 0x4153, 0x4154, 0x4155,
  0x4156, 0x4157, 0x4158, 0x4159, 0x415a, 0x415b, 0x415c, 0x415d,
  0x415e, 0x415f, 0x4160, 0x4161, 0x4162, 0x4163, 0x4164, 0x4165,
  0x4166, 0x4167, 0x4168, 0x4169, 0x416a, 0x416b, 0x416c, 0x416d,
  0x416e, 0x416f, 0x4170, 0x4171, 0x4172, 0x4173, 0x4174, 0x4175,
  0x4176, 0x4177, 0x4178, 0x4179, 0x417a, 0x417b, 0x417c, 0x417d,
  0x417e, 0x4221, 0x4222, 0x4223, 0x4224, 0x4225, 0x4226, 0x4227,
  0x4228, 0x4229, 0x422a, 0x422b, 0x422c, 0x422d, 0x422e, 0x422f,
  0x4230, 0x4231, 0x4232, 0x4233, 0x4234, 0x4235, 0x4236, 0x4237,
  0x4238, 0x4239, 0x423a, 0x423b, 0x423c, 0x423d, 0x423e, 0x423f,
  0x4240, 0x4241, 0x4242, 0x4243, 0x4244, 0x4245, 0x4246, 0x4247,
  0x4248, 0x4249, 0x424a, 0x424b, 0x424c, 0x424d, 0x424e, 0x424f,
  0x4250, 0x4251, 0x4252, 0x4253, 0x4254, 0x4255, 0x4256, 0x4257,
  0x4258, 0x4259, 0x425a, 0x425b, 0x425c, 0x425d, 0x425e, 0x425f,
  0x4260, 0x4261, 0x4262, 0x4263, 0x4264, 0x4265, 0x4266, 0x4267,
  0x4268, 0x4269, 0x426a, 0x426b, 0x426c, 0x426d, 0x426e, 0x426f,
  0x4270, 0x4271, 0x4272, 0x4273, 0x4274, 0x4275, 0x4276, 0x4277,
  0x4278, 0x4279, 0x427a, 0x427b, 0x427c, 0x427d, 0x427e, 0x4321,
  0x4322, 0x4323, 0x4324, 0x4325, 0x4326, 0x4327, 0x4328, 0x4329,
  0x432a, 0x432b, 0x432c, 0x432d, 0x432e, 0x432f, 0x4330, 0x4331,
  0x4332, 0x4333, 0x4334, 0x4335, 0x4336, 0x4337, 0x4338, 0x4339,
  0x433a, 0x433b, 0x433c, 0x433d, 0x433e, 0x433f, 0x4340, 0x4341,
  0x4342, 0x4343, 0x4344, 0x4345, 0x4346, 0x4347, 0x4348, 0x4349,
  0x434a, 0x434b, 0x434c, 0x434d, 0x434e, 0x434f, 0x4350, 0x4351,
  0x4352, 0x4353, 0x4354, 0x4355, 0x4356, 0x4357, 0x4358, 0x4359,
  0x435a, 0x435b, 0x435c, 0x435d, 0x435e, 0x435f, 0x4360, 0x4361,
  0x4362, 0x4363, 0x4364, 0x4365, 0x4366, 0x4367, 0x4368, 0x4369,
  0x436a, 0x436b, 0x436c, 0x436d, 0x436e, 0x436f, 0x4370, 0x4371,
  0x4372, 0x4373, 0x4374, 0x4375, 0x4376, 0x4377, 0x4378, 0x4379,
  0x437a, 0x437b, 0x437c, 0x437d, 0x437e, 0x4421, 0x4422, 0x4423,
  0x4424, 0x4425, 0x4426, 0x4427, 0x4428, 0x4429, 0x442a, 0x442b,
  0x442c, 0x442d, 0x442e, 0x442f, 0x4430, 0x4431, 0x4432, 0x4433,
  0x4434, 0x4435, 0x4436, 0x4437, 0x4438, 0x4439, 0x443a, 0x443b,
  0x443c, 0x443d, 0x443e, 0x443f, 0x4440, 0x4441, 0x4442, 0x4443,
  0x4444, 0x4445, 0x4446, 0x4447, 0x4448, 0x4449, 0x444a, 0x444b,
  0x444c, 0x444d, 0x444e, 0x444f, 0x4450, 0x4451, 0x4452, 0x4453,
  0x4454, 0x4455, 0x4456, 0x4457, 0x4458, 0x4459, 0x445a, 0x445b,
  0x445c, 0x445d, 0x445e, 0x445f, 0x4460, 0x4461, 0x4462, 0x4463,
  0x4464, 0x4465, 0x4466, 0x4467, 0x4468, 0x4469, 0x446a, 0x446b,
  0x446c, 0x446d, 0x446e, 0x446f, 0x4470, 0x4471, 0x4472, 0x4473,
  0x4474, 0x4475, 0x4476, 0x4477, 0x4478, 0x4479, 0x447a, 0x447b,
  0x447c, 0x447d, 0x447e, 0x4521, 0x4522, 0x4523, 0x4524, 0x4525,
  0x4526, 0x4527, 0x4528, 0x4529, 0x452a, 0x452b, 0x452c, 0x452d,
  0x452e, 0x452f, 0x4530, 0x4531, 0x4532, 0x4533, 0x4534, 0x4535,
  0x4536, 0x4537, 0x4538, 0x4539, 0x453a, 0x453b, 0x453c, 0x453d,
  0x453e, 0x453f, 0x4540, 0x4541, 0x4542, 0x4543, 0x4544, 0x4545,
  0x4546, 0x4547, 0x4548, 0x4549, 0x454a, 0x454b, 0x454c, 0x454d,
  0x454e, 0x454f, 0x4550, 0x4551, 0x4552, 0x4553, 0x4554, 0x4555,
  0x4556, 0x4557, 0x4558, 0x4559, 0x455a, 0x455b, 0x455c, 0x455d,
  0x455e, 0x455f, 0x4560, 0x4561, 0x4562, 0x4563, 0x4564, 0x4565,
  0x4566, 0x4567, 0x4568, 0x4569, 0x456a, 0x456b, 0x456c, 0x456d,
  0x456e, 0x456f, 0x4570, 0x4571, 0x4572, 0x4573, 0x4574, 0x4575,
  0x4576, 0x4577, 0x4578, 0x4579, 0x457a, 0x457b, 0x457c, 0x457d,
  0x457e, 0x4621, 0x4622, 0x4623, 0x4624, 0x4625, 0x4626, 0x4627,
  0x4628, 0x4629, 0x462a, 0x462b, 0x462c, 0x462d, 0x462e, 0x462f,
  0x4630, 0x4631, 0x4632, 0x4633, 0x4634, 0x4635, 0x4636, 0x4637,
  0x4638, 0x4639, 0x463a, 0x463b, 0x463c, 0x463d, 0x463e, 0x463f,
  0x4640, 0x4641, 0x4642, 0x4643, 0x4644, 0x4645, 0x4646, 0x4647,
  0x4648, 0x4649, 0x464a, 0x464b, 0x464c, 0x464d, 0x464e, 0x464f,
  0x4650, 0x4651, 0x4652, 0x4653, 0x4654, 0x4655, 0x4656, 0x4657,
  0x4658, 0x4659, 0x465a, 0x465b, 0x465c, 0x465d, 0x465e, 0x465f,
  0x4660, 0x4661, 0x4662, 0x4663, 0x4664, 0x4665, 0x4666, 0x4667,
  0x4668, 0x4669, 0x466a, 0x466b, 0x466c, 0x466d, 0x466e, 0x466f,
  0x4670, 0x4671, 0x4672, 0x4673, 0x4674, 0x4675, 0x4676, 0x4677,
  0x4678, 0x4679, 0x467a, 0x467b, 0x467c, 0x467d, 0x467e, 0x4721,
  0x4722, 0x4723, 0x4724, 0x4725, 0x4726, 0x4727, 0x4728, 0x4729,
  0x472a, 0x472b, 0x472c, 0x472d, 0x472e, 0x472f, 0x4730, 0x4731,
  0x4732, 0x4733, 0x4734, 0x4735, 0x4736, 0x4737, 0x4738, 0x4739,
  0x473a, 0x473b, 0x473c, 0x473d, 0x473e, 0x473f, 0x4740, 0x4741,
  0x4742, 0x4743, 0x4744, 0x4745, 0x4746, 0x4747, 0x4748, 0x4749,
  0x474a, 0x474b, 0x474c, 0x474d, 0x474e, 0x474f, 0x4750, 0x4751,
  0x4752, 0x4753, 0x4754, 0x4755, 0x4756, 0x4757, 0x4758, 0x4759,
  0x475a, 0x475b, 0x475c, 0x475d, 0x475e, 0x475f, 0x4760, 0x4761,
  0x4762, 0x4763, 0x4764, 0x4765, 0x4766, 0x4767, 0x4768, 0x4769,
  0x476a, 0x476b, 0x476c, 0x476d, 0x476e, 0x476f, 0x4770, 0x4771,
  0x4772, 0x4773, 0x4774, 0x4775, 0x4776, 0x4777, 0x4778, 0x4779,
  0x477a, 0x477b, 0x477c, 0x477d, 0x477e, 0x4821, 0x4822, 0x4823,
  0x4824, 0x4825, 0x4826, 0x4827, 0x4828, 0x4829, 0x482a, 0x482b,
  0x482c, 0x482d, 0x482e, 0x482f, 0x4830, 0x4831, 0x4832, 0x4833,
  0x4834, 0x4835, 0x4836, 0x4837, 0x4838, 0x4839, 0x483a, 0x483b,
  0x483c, 0x483d, 0x483e, 0x483f, 0x4840, 0x4841, 0x4842, 0x4843,
  0x4844, 0x4845, 0x4846, 0x4847, 0x4848, 0x4849, 0x484a, 0x484b,
  0x484c, 0x484d, 0x484e, 0x484f, 0x4850, 0x4851, 0x4852, 0x4853,
  0x4854, 0x4855, 0x4856, 0x4857, 0x4858, 0x4859, 0x485a, 0x485b,
  0x485c, 0x485d, 0x485e, 0x485f, 0x4860, 0x4861, 0x4862, 0x4863,
  0x4864, 0x4865, 0x4866, 0x4867, 0x4868, 0x4869, 0x486a, 0x486b,
  0x486c, 0x486d, 0x486e, 0x486f, 0x4870, 0x4871, 0x4872, 0x4873,
  0x4874, 0x4875, 0x4876, 0x4877, 0x4878, 0x4879, 0x487a, 0x487b,
  0x487c, 0x487d, 0x487e, 0x4b50, 0x4b56, 0x4b67, 0x4d4f, 0x4d68,
  0x4e2d, 0x4f7b, 0x5022, 0x5038, 0x5050, 0x505d, 0x5154, 0x5155,
  0x5158, 0x515b, 0x515c, 0x515d, 0x515e, 0x515f, 0x5160, 0x5162,
  0x5163, 0x5164, 0x5165, 0x5166, 0x5168, 0x5169, 0x516a, 0x516b,
  0x516d, 0x516f, 0x5170, 0x5172, 0x5176, 0x517a, 0x517c, 0x517d,
  0x517e, 0x5222, 0x5223, 0x5227, 0x5228, 0x5229, 0x522a, 0x522b,
  0x522d, 0x5232, 0x523e, 0x5242, 0x5243, 0x5244, 0x5246, 0x5247,
  0x5248, 0x5249, 0x524a, 0x524b, 0x524d, 0x524e, 0x524f, 0x5250,
  0x5251, 0x5252, 0x5253, 0x5254, 0x5255, 0x5256, 0x5257, 0x5259,
  0x525a, 0x525e, 0x525f, 0x5261, 0x5262, 0x5264, 0x5265, 0x5266,
  0x5267, 0x5268, 0x5269, 0x526a, 0x526b, 0x5270, 0x5271, 0x5272,
  0x5273, 0x5274, 0x5275, 0x5277, 0x5278, 0x5466, 0x547c, 0x5525,
  0x552b, 0x552e, 0x5638, 0x564d, 0x574b, 0x5764, 0x5b45, 0x5b64,
  0x5c25, 0x5d25, 0x5d55, 0x5d74, 0x5e7c, 0x5e7e, 0x5f33, 0x5f61,
  0x5f68, 0x6071, 0x612d, 0x616d, 0x6375, 0x6421, 0x6429, 0x652e,
  0x6531, 0x6532, 0x6539, 0x653b, 0x653c, 0x6544, 0x654e, 0x6550,
  0x6552, 0x6556, 0x657a, 0x657b, 0x657c, 0x657e, 0x6621, 0x6624,
  0x6627, 0x662d, 0x662f, 0x6630, 0x6631, 0x6633, 0x6637, 0x6638,
  0x663c, 0x6644, 0x6646, 0x6647, 0x664a, 0x6652, 0x6656, 0x6659,
  0x665c, 0x665f, 0x6661, 0x6664, 0x6665, 0x6666, 0x6668, 0x666a,
  0x666b, 0x666c, 0x666f, 0x6671, 0x6672, 0x6675, 0x6676, 0x6677,
  0x6679, 0x6721, 0x6726, 0x6729, 0x672a, 0x672c, 0x672d, 0x6730,
  0x673f, 0x6741, 0x6746, 0x6747, 0x674b, 0x674d, 0x674f, 0x6750,
  0x6753, 0x675f, 0x6764, 0x6766, 0x6777, 0x6867, 0x6868, 0x6870,
  0x6871, 0x6877, 0x6879, 0x687b, 0x687e, 0x6927, 0x692c, 0x694c,
  0x6977, 0x6a41, 0x6a65, 0x6a74, 0x6a77, 0x6a7c, 0x6a7e, 0x6b24,
  0x6b27, 0x6b29, 0x6b2a, 0x6b3a, 0x6b3b, 0x6b3d, 0x6b41, 0x6b42,
  0x6b46, 0x6b47, 0x6b4c, 0x6b4f, 0x6b50, 0x6b51, 0x6b52, 0x6b58,
  0x6c26, 0x6c27, 0x6c2a, 0x6c2f, 0x6c30, 0x6c31, 0x6c32, 0x6c35,
  0x6c38, 0x6c3a, 0x6c40, 0x6c41, 0x6c45, 0x6c46, 0x6c49, 0x6c4a,
  0x6c55, 0x6c5d, 0x6c5e, 0x6c61, 0x6c64, 0x6c67, 0x6c68, 0x6c77,
  0x6c78, 0x6c7a, 0x6d21, 0x6d22, 0x6d23, 0x6d6e, 0x6e5b, 0x723d,
  0x727a, 0x7331, 0x7427, 0x746e, 0x7674, 0x7676, 0x7738, 0x7748,
  0x7753, 0x785b, 0x7870, 0x7a21, 0x7a22, 0x7a66, 0x7c29, 0x2321,
  0x2322, 0x2323, 0x2324, 0x2325, 0x2326, 0x2327, 0x2328, 0x2329,
  0x232a, 0x232b, 0x232c, 0x232d, 0x232e, 0x232f, 0x2330, 0x2331,
  0x2332, 0x2333, 0x2334, 0x2335, 0x2336, 0x2337, 0x2338, 0x2339,
  0x233a, 0x233b, 0x233c, 0x233d, 0x233e, 0x233f, 0x2340, 0x2341,
  0x2342, 0x2343, 0x2344, 0x2345, 0x2346, 0x2347, 0x2348, 0x2349,
  0x234a, 0x234b, 0x234c, 0x234d, 0x234e, 0x234f, 0x2350, 0x2351,
  0x2352, 0x2353, 0x2354, 0x2355, 0x2356, 0x2357, 0x2358, 0x2359,
  0x235a, 0x235b, 0x212c, 0x235d, 0x235e, 0x235f, 0x2360, 0x2361,
  0x2362, 0x2363, 0x2364, 0x2365, 0x2366, 0x2367, 0x2368, 0x2369,
  0x236a, 0x236b, 0x236c, 0x236d, 0x236e, 0x236f, 0x2370, 0x2371,
  0x2372, 0x2373, 0x2374, 0x2375, 0x2376, 0x2377, 0x2378, 0x2379,
  0x237a, 0x237b, 0x237c, 0x237d, 0x2226, 0x214b, 0x214c, 0x217e,
  0x237e, 0x214d, 0x235c,
};

static const Summary16 ksc5601_uni2indx_page00[70] = {
  /* 0x0000 */
  {    0, 0x0000 }, {    0, 0x0000 }, {    0, 0x0000 }, {    0, 0x0000 },
  {    0, 0x0000 }, {    0, 0x0000 }, {    0, 0x0000 }, {    0, 0x0000 },
  {    0, 0x0000 }, {    0, 0x0000 }, {    0, 0x6592 }, {    7, 0xf7df },
  {   21, 0x0040 }, {   22, 0xc181 }, {   27, 0x0040 }, {   28, 0x4181 },
  /* 0x0100 */
  {   32, 0x0000 }, {   32, 0x0002 }, {   33, 0x00c0 }, {   35, 0x810e },
  {   40, 0x0e07 }, {   46, 0x000c }, {   48, 0x00c0 }, {   50, 0x0000 },
  {   50, 0x0000 }, {   50, 0x0000 }, {   50, 0x0000 }, {   50, 0x0000 },
  {   50, 0x0000 }, {   50, 0x0000 }, {   50, 0x0000 }, {   50, 0x0000 },
  /* 0x0200 */
  {   50, 0x0000 }, {   50, 0x0000 }, {   50, 0x0000 }, {   50, 0x0000 },
  {   50, 0x0000 }, {   50, 0x0000 }, {   50, 0x0000 }, {   50, 0x0000 },
  {   50, 0x0000 }, {   50, 0x0000 }, {   50, 0x0000 }, {   50, 0x0000 },
  {   50, 0x0080 }, {   51, 0x2f01 }, {   57, 0x0000 }, {   57, 0x0000 },
  /* 0x0300 */
  {   57, 0x0000 }, {   57, 0x0000 }, {   57, 0x0000 }, {   57, 0x0000 },
  {   57, 0x0000 }, {   57, 0x0000 }, {   57, 0x0000 }, {   57, 0x0000 },
  {   57, 0x0000 }, {   57, 0xfffe }, {   72, 0x03fb }, {   81, 0xfffe },
  {   96, 0x03fb }, {  105, 0x0000 }, {  105, 0x0000 }, {  105, 0x0000 },
  /* 0x0400 */
  {  105, 0x0002 }, {  106, 0xffff }, {  122, 0xffff }, {  138, 0xffff },
  {  154, 0xffff }, {  170, 0x0002 },
};
static const Summary16 ksc5601_uni2indx_page20[103] = {
  /* 0x2000 */
  {  171, 0x0000 }, {  171, 0x3320 }, {  176, 0x0063 }, {  180, 0x080d },
  {  184, 0x0000 }, {  184, 0x0000 }, {  184, 0x0000 }, {  184, 0x8010 },
  {  186, 0x001e }, {  190, 0x0000 }, {  190, 0x1000 }, {  191, 0x0000 },
  {  191, 0x0000 }, {  191, 0x0000 }, {  191, 0x0000 }, {  191, 0x0000 },
  /* 0x2100 */
  {  191, 0x0208 }, {  193, 0x0048 }, {  195, 0x0846 }, {  199, 0x0000 },
  {  199, 0x0000 }, {  199, 0x7818 }, {  205, 0x03ff }, {  215, 0x03ff },
  {  225, 0x0000 }, {  225, 0x03ff }, {  235, 0x0000 }, {  235, 0x0000 },
  {  235, 0x0000 }, {  235, 0x0014 }, {  237, 0x0000 }, {  237, 0x0000 },
  /* 0x2200 */
  {  237, 0x898d }, {  244, 0x6402 }, {  248, 0x5fa1 }, {  257, 0x3030 },
  {  261, 0x0000 }, {  261, 0x0004 }, {  262, 0x0c33 }, {  268, 0x0000 },
  {  268, 0x00cc }, {  272, 0x0200 }, {  273, 0x0020 }, {  274, 0x0000 },
  {  274, 0x0000 }, {  274, 0x0000 }, {  274, 0x0000 }, {  274, 0x0000 },
  /* 0x2300 */
  {  274, 0x0000 }, {  274, 0x0004 }, {  275, 0x0000 }, {  275, 0x0000 },
  {  275, 0x0000 }, {  275, 0x0000 }, {  275, 0x0000 }, {  275, 0x0000 },
  {  275, 0x0000 }, {  275, 0x0000 }, {  275, 0x0000 }, {  275, 0x0000 },
  {  275, 0x0000 }, {  275, 0x0000 }, {  275, 0x0000 }, {  275, 0x0000 },
  /* 0x2400 */
  {  275, 0x0000 }, {  275, 0x0000 }, {  275, 0x0000 }, {  275, 0x0000 },
  {  275, 0x0000 }, {  275, 0x0000 }, {  275, 0x7fff }, {  290, 0xfff0 },
  {  302, 0x0007 }, {  305, 0xf000 }, {  309, 0xffff }, {  325, 0x003f },
  {  331, 0x0000 }, {  331, 0xffff }, {  347, 0x03ff }, {  357, 0x0000 },
  /* 0x2500 */
  {  357, 0xf00f }, {  365, 0xffff }, {  381, 0xffff }, {  397, 0xffff },
  {  413, 0x0fff }, {  425, 0x0000 }, {  425, 0x0000 }, {  425, 0x0000 },
  {  425, 0x0000 }, {  425, 0x0004 }, {  426, 0x03fb }, {  435, 0x30cc },
  {  441, 0xc9c3 }, {  449, 0x0003 }, {  451, 0x0000 }, {  451, 0x0000 },
  /* 0x2600 */
  {  451, 0xc060 }, {  455, 0x5000 }, {  457, 0x0000 }, {  457, 0x0000 },
  {  457, 0x0005 }, {  459, 0x0000 }, {  459, 0x37bb },
};
static const Summary16 ksc5601_uni2indx_page30[62] = {
  /* 0x3000 */
  {  470, 0xff0f }, {  482, 0x003b }, {  487, 0x0000 }, {  487, 0x0000 },
  {  487, 0xfffe }, {  502, 0xffff }, {  518, 0xffff }, {  534, 0xffff },
  {  550, 0xffff }, {  566, 0x000f }, {  570, 0xfffe }, {  585, 0xffff },
  {  601, 0xffff }, {  617, 0xffff }, {  633, 0xffff }, {  649, 0x007f },
  /* 0x3100 */
  {  656, 0x0000 }, {  656, 0x0000 }, {  656, 0x0000 }, {  656, 0xfffe },
  {  671, 0xffff }, {  687, 0xffff }, {  703, 0xffff }, {  719, 0xffff },
  {  735, 0x7fff }, {  750, 0x0000 }, {  750, 0x0000 }, {  750, 0x0000 },
  {  750, 0x0000 }, {  750, 0x0000 }, {  750, 0x0000 }, {  750, 0x0000 },
  /* 0x3200 */
  {  750, 0xffff }, {  766, 0x1fff }, {  779, 0x0000 }, {  779, 0x0000 },
  {  779, 0x0000 }, {  779, 0x0000 }, {  779, 0xffff }, {  795, 0xcfff },
  {  809, 0x0000 }, {  809, 0x0000 }, {  809, 0x0000 }, {  809, 0x0000 },
  {  809, 0x0000 }, {  809, 0x0000 }, {  809, 0x0000 }, {  809, 0x0000 },
  /* 0x3300 */
  {  809, 0x0000 }, {  809, 0x0000 }, {  809, 0x0000 }, {  809, 0x0000 },
  {  809, 0x0000 }, {  809, 0x0000 }, {  809, 0x0000 }, {  809, 0x0000 },
  {  809, 0xff1f }, {  822, 0xffff }, {  838, 0xffff }, {  854, 0xffff },
  {  870, 0x87ff }, {  882, 0x3949 },
};
static const Summary16 ksc5601_uni2indx_page4e[1306] = {
  /* 0x4e00 */
  {  889, 0x2f8b }, {  898, 0x4372 }, {  905, 0x2000 }, {  906, 0x0b04 },
  {  910, 0xe82c }, {  917, 0xe340 }, {  923, 0x2800 }, {  925, 0x40c8 },
  {  929, 0x5944 }, {  935, 0x4937 }, {  943, 0x7976 }, {  953, 0x0440 },
  {  955, 0x2c93 }, {  962, 0xa3f0 }, {  970, 0x0038 }, {  973, 0x08c5 },
  /* 0x4f00 */
  {  978, 0xee02 }, {  985, 0x0003 }, {  987, 0x8000 }, {  988, 0x3550 },
  {  994, 0xe1c8 }, { 1001, 0x1e23 }, { 1008, 0x8200 }, { 1010, 0xc449 },
  { 1016, 0xad5a }, { 1025, 0x2942 }, { 1030, 0xc000 }, { 1032, 0x8060 },
  { 1035, 0x461c }, { 1041, 0xa49a }, { 1048, 0xc003 }, { 1052, 0x052a },
  /* 0x5000 */
  { 1057, 0x2a44 }, { 1062, 0xd646 }, { 1070, 0x3dda }, { 1080, 0x0800 },
  { 1081, 0x8388 }, { 1086, 0x1420 }, { 1089, 0x0020 }, { 1090, 0x0170 },
  { 1094, 0x2021 }, { 1097, 0x0302 }, { 1100, 0x3000 }, { 1102, 0x40ac },
  { 1107, 0x8620 }, { 1111, 0x4462 }, { 1116, 0x20a0 }, { 1119, 0x8a00 },
  /* 0x5100 */
  { 1122, 0x0253 }, { 1127, 0x8004 }, { 1129, 0x0402 }, { 1131, 0x1484 },
  { 1135, 0x7bfb }, { 1148, 0x1004 }, { 1150, 0x7fa4 }, { 1160, 0x11e2 },
  { 1166, 0x2441 }, { 1170, 0x00a4 }, { 1173, 0x1421 }, { 1177, 0x20c0 },
  { 1180, 0x3a50 }, { 1186, 0x7000 }, { 1189, 0x0002 }, { 1190, 0x2743 },
  /* 0x5200 */
  { 1197, 0x45c9 }, { 1204, 0x2082 }, { 1207, 0x4630 }, { 1212, 0x0fc1 },
  { 1219, 0x3c88 }, { 1225, 0x2850 }, { 1229, 0x8602 }, { 1233, 0xa024 },
  { 1237, 0x2388 }, { 1242, 0x8806 }, { 1246, 0x0e19 }, { 1252, 0x4000 },
  { 1253, 0x22aa }, { 1259, 0xeb64 }, { 1268, 0x001c }, { 1271, 0xcd28 },
  /* 0x5300 */
  { 1278, 0xa120 }, { 1282, 0x02e1 }, { 1287, 0x840b }, { 1292, 0x8200 },
  { 1294, 0x279b }, { 1303, 0x549e }, { 1311, 0x8141 }, { 1315, 0xa0b3 },
  { 1322, 0x0010 }, { 1323, 0x8508 }, { 1327, 0x2061 }, { 1331, 0x0800 },
  { 1332, 0x2f08 }, { 1338, 0x08d0 }, { 1342, 0xbe3e }, { 1353, 0x010f },
  /* 0x5400 */
  { 1358, 0xf718 }, { 1367, 0xa803 }, { 1372, 0x0a41 }, { 1376, 0x5b08 },
  { 1382, 0x0504 }, { 1385, 0x0002 }, { 1386, 0x0500 }, { 1388, 0x382a },
  { 1394, 0x5041 }, { 1398, 0x0001 }, { 1399, 0x1910 }, { 1403, 0x2108 },
  { 1406, 0x0313 }, { 1411, 0x0000 }, { 1411, 0x6122 }, { 1416, 0x0404 },
  /* 0x5500 */
  { 1418, 0x40d0 }, { 1422, 0x1001 }, { 1424, 0x8000 }, { 1425, 0x4022 },
  { 1428, 0x8050 }, { 1431, 0x4048 }, { 1434, 0x0008 }, { 1435, 0x1000 },
  { 1436, 0x06d1 }, { 1442, 0x3700 }, { 1447, 0x5e80 }, { 1453, 0x0000 },
  { 1453, 0x00a0 }, { 1455, 0x9410 }, { 1459, 0x0018 }, { 1461, 0x6000 },
  /* 0x5600 */
  { 1463, 0x0240 }, { 1465, 0x0090 }, { 1467, 0x8000 }, { 1468, 0x0054 },
  { 1471, 0x0000 }, { 1471, 0x0008 }, { 1472, 0x0900 }, { 1474, 0x0010 },
  { 1475, 0x0040 }, { 1476, 0x0000 }, { 1476, 0x5020 }, { 1479, 0x1010 },
  { 1481, 0x2400 }, { 1483, 0x4c02 }, { 1487, 0x0001 }, { 1488, 0x0601 },
  /* 0x5700 */
  { 1491, 0x2918 }, { 1496, 0x814c }, { 1501, 0x2100 }, { 1503, 0x0801 },
  { 1505, 0x6485 }, { 1511, 0x0003 }, { 1513, 0x4452 }, { 1518, 0x1021 },
  { 1521, 0x0904 }, { 1524, 0x0008 }, { 1525, 0x000d }, { 1528, 0x0000 },
  { 1528, 0x4988 }, { 1533, 0x8000 }, { 1534, 0x0001 }, { 1535, 0x1691 },
  /* 0x5800 */
  { 1541, 0x0765 }, { 1548, 0x4000 }, { 1549, 0x8492 }, { 1554, 0x0433 },
  { 1559, 0x8c00 }, { 1562, 0x4592 }, { 1568, 0x0016 }, { 1571, 0x5220 },
  { 1575, 0x0228 }, { 1578, 0xd008 }, { 1582, 0x4300 }, { 1585, 0x4c08 },
  { 1589, 0x40a2 }, { 1593, 0xc32a }, { 1600, 0x9810 }, { 1604, 0x2e00 },
  /* 0x5900 */
  { 1608, 0x8000 }, { 1609, 0x1670 }, { 1615, 0x6e84 }, { 1622, 0x4082 },
  { 1625, 0xc390 }, { 1631, 0x04b3 }, { 1637, 0x7c85 }, { 1645, 0x2118 },
  { 1649, 0x041c }, { 1653, 0x02c8 }, { 1657, 0x1120 }, { 1660, 0x4a00 },
  { 1663, 0x0a48 }, { 1667, 0x361b }, { 1675, 0x5540 }, { 1680, 0x8900 },
  /* 0x5a00 */
  { 1683, 0x000a }, { 1685, 0x9902 }, { 1690, 0x0221 }, { 1693, 0x1040 },
  { 1695, 0x0242 }, { 1698, 0x0400 }, { 1699, 0x0044 }, { 1701, 0x0000 },
  { 1701, 0x0000 }, { 1701, 0x0c04 }, { 1704, 0x0010 }, { 1705, 0x0000 },
  { 1705, 0x1216 }, { 1710, 0x0000 }, { 1710, 0x0242 }, { 1713, 0x0000 },
  /* 0x5b00 */
  { 1713, 0x1a20 }, { 1717, 0x0040 }, { 1718, 0x0400 }, { 1719, 0x0000 },
  { 1719, 0x0009 }, { 1721, 0xb5b3 }, { 1731, 0x0a18 }, { 1735, 0x1523 },
  { 1741, 0x9ba0 }, { 1748, 0x1fe8 }, { 1757, 0x507c }, { 1764, 0x8379 },
  { 1772, 0x10fd }, { 1780, 0xc09d }, { 1787, 0xdbf6 }, { 1799, 0x0560 },
  /* 0x5c00 */
  { 1803, 0xef92 }, { 1813, 0x0242 }, { 1816, 0x0110 }, { 1818, 0xdf02 },
  { 1826, 0x6961 }, { 1833, 0x0822 }, { 1836, 0x9035 }, { 1842, 0x0202 },
  { 1844, 0x0000 }, { 1844, 0x0003 }, { 1846, 0x1a02 }, { 1850, 0x45aa },
  { 1857, 0x0001 }, { 1858, 0x0200 }, { 1859, 0x8101 }, { 1862, 0x2851 },
  /* 0x5d00 */
  { 1867, 0x6080 }, { 1870, 0x02d2 }, { 1875, 0x0280 }, { 1877, 0x0000 },
  { 1877, 0x1800 }, { 1879, 0x0001 }, { 1880, 0x9200 }, { 1883, 0x0000 },
  { 1883, 0x0880 }, { 1885, 0x2000 }, { 1886, 0x0405 }, { 1889, 0x3500 },
  { 1893, 0x2000 }, { 1894, 0x6044 }, { 1898, 0x49e6 }, { 1906, 0x609e },
  /* 0x5e00 */
  { 1913, 0x104c }, { 1917, 0x2a42 }, { 1922, 0x2820 }, { 1925, 0xa148 },
  { 1930, 0x10b1 }, { 1935, 0x8020 }, { 1937, 0x000e }, { 1940, 0x7b9c },
  { 1950, 0x8490 }, { 1954, 0x14a0 }, { 1958, 0x28c1 }, { 1963, 0x41e0 },
  { 1968, 0x0704 }, { 1972, 0x8c49 }, { 1978, 0x100d }, { 1982, 0x0cc8 },
  /* 0x5f00 */
  { 1987, 0x8412 }, { 1991, 0x89ba }, { 1999, 0x02c0 }, { 2002, 0x1422 },
  { 2006, 0x5500 }, { 2010, 0x0ac0 }, { 2014, 0x3ec4 }, { 2022, 0x9283 },
  { 2028, 0x1ca3 }, { 2035, 0x4387 }, { 2042, 0x4703 }, { 2048, 0x22a0 },
  { 2052, 0x3028 }, { 2056, 0x03c0 }, { 2060, 0x0801 }, { 2062, 0xa020 },
  /* 0x6000 */
  { 2065, 0x8000 }, { 2066, 0x3044 }, { 2070, 0x85a3 }, { 2077, 0x0000 },
  { 2077, 0x200e }, { 2081, 0x2225 }, { 2086, 0xb73c }, { 2096, 0x0001 },
  { 2097, 0x3220 }, { 2101, 0x8c50 }, { 2106, 0x0099 }, { 2110, 0x315d },
  { 2118, 0x00a0 }, { 2120, 0x9402 }, { 2124, 0x0003 }, { 2126, 0x0e4b },
  /* 0x6100 */
  { 2133, 0xe342 }, { 2140, 0x8c20 }, { 2144, 0x0080 }, { 2145, 0xd091 },
  { 2151, 0x1d94 }, { 2158, 0xa328 }, { 2164, 0x499c }, { 2171, 0x60c1 },
  { 2176, 0x4406 }, { 2180, 0x0713 }, { 2186, 0x5a90 }, { 2192, 0x4444 },
  { 2196, 0x0f88 }, { 2202, 0x0000 }, { 2202, 0x0040 }, { 2203, 0x95c4 },
  /* 0x6200 */
  { 2210, 0x7581 }, { 2217, 0x8447 }, { 2223, 0x4402 }, { 2226, 0xc053 },
  { 2232, 0x2b83 }, { 2239, 0x0108 }, { 2241, 0x4000 }, { 2242, 0x9242 },
  { 2247, 0x0611 }, { 2251, 0x09a6 }, { 2257, 0x0800 }, { 2258, 0x3222 },
  { 2263, 0xb384 }, { 2270, 0x1bdd }, { 2280, 0xf000 }, { 2284, 0xc08a },
  /* 0x6300 */
  { 2289, 0x0282 }, { 2292, 0x0002 }, { 2293, 0x8800 }, { 2295, 0x6c00 },
  { 2299, 0x9200 }, { 2302, 0x0021 }, { 2304, 0x4180 }, { 2307, 0x8c84 },
  { 2312, 0x1308 }, { 2316, 0x0944 }, { 2320, 0x07a7 }, { 2328, 0x0000 },
  { 2328, 0x8051 }, { 2332, 0x0c41 }, { 2336, 0x6002 }, { 2339, 0x00d0 },
  /* 0x6400 */
  { 2342, 0xa000 }, { 2344, 0x10d0 }, { 2348, 0x3004 }, { 2351, 0x4400 },
  { 2353, 0x0000 }, { 2353, 0x0100 }, { 2354, 0x8201 }, { 2357, 0x0700 },
  { 2360, 0x0100 }, { 2361, 0x440e }, { 2366, 0x6830 }, { 2371, 0x0805 },
  { 2374, 0x64b2 }, { 2381, 0x0514 }, { 2385, 0x10e6 }, { 2391, 0x4414 },
  /* 0x6500 */
  { 2395, 0x0011 }, { 2397, 0x2100 }, { 2399, 0x9c08 }, { 2404, 0xcbc0 },
  { 2411, 0xe120 }, { 2416, 0x40c2 }, { 2420, 0x304c }, { 2425, 0x41b4 },
  { 2431, 0x10ac }, { 2436, 0x9a83 }, { 2443, 0x98b2 }, { 2450, 0x3281 },
  { 2455, 0x9822 }, { 2460, 0x0084 }, { 2462, 0x3369 }, { 2470, 0xbc12 },
  /* 0x6600 */
  { 2477, 0xd6c0 }, { 2484, 0xc03b }, { 2491, 0xa1a1 }, { 2497, 0x0c53 },
  { 2503, 0x8a1e }, { 2510, 0xea00 }, { 2515, 0xcbf0 }, { 2524, 0x05d8 },
  { 2530, 0x4390 }, { 2535, 0x21c3 }, { 2541, 0x4805 }, { 2545, 0x4a1c },
  { 2551, 0x02d0 }, { 2555, 0x3240 }, { 2559, 0x0041 }, { 2561, 0xd79d },
  /* 0x6700 */
  { 2572, 0x2b09 }, { 2578, 0xe8b0 }, { 2585, 0x7dc0 }, { 2593, 0x2452 },
  { 2598, 0xc240 }, { 2602, 0xd04b }, { 2609, 0xa000 }, { 2611, 0xc8ab },
  { 2619, 0x8a80 }, { 2623, 0x34a9 }, { 2630, 0x8000 }, { 2631, 0x41c9 },
  { 2637, 0x8010 }, { 2639, 0x241f }, { 2646, 0x9200 }, { 2649, 0x487b },
  /* 0x6800 */
  { 2657, 0x0000 }, { 2657, 0x00cc }, { 2661, 0x8406 }, { 2665, 0x3300 },
  { 2669, 0x410f }, { 2675, 0x001b }, { 2679, 0x2000 }, { 2680, 0x8040 },
  { 2682, 0x8022 }, { 2685, 0xa098 }, { 2690, 0xa186 }, { 2696, 0x006b },
  { 2701, 0x2a30 }, { 2706, 0x85a4 }, { 2712, 0x4181 }, { 2716, 0x0604 },
  /* 0x6900 */
  { 2719, 0x6021 }, { 2723, 0x0004 }, { 2724, 0x0080 }, { 2725, 0xa001 },
  { 2728, 0x0400 }, { 2729, 0x46b8 }, { 2736, 0xe90f }, { 2745, 0x03a0 },
  { 2749, 0x0000 }, { 2749, 0x1820 }, { 2752, 0x40a0 }, { 2755, 0x0810 },
  { 2757, 0x380a }, { 2762, 0x0001 }, { 2763, 0x0500 }, { 2765, 0xa800 },
  /* 0x6a00 */
  { 2768, 0x0404 }, { 2770, 0xc28a }, { 2776, 0x000a }, { 2778, 0x2720 },
  { 2783, 0x0910 }, { 2786, 0x830c }, { 2791, 0x0802 }, { 2793, 0x0000 },
  { 2793, 0x6211 }, { 2798, 0x1080 }, { 2800, 0x000c }, { 2802, 0x0808 },
  { 2804, 0x000c }, { 2806, 0x0c08 }, { 2809, 0x0000 }, { 2809, 0x0840 },
  /* 0x6b00 */
  { 2811, 0x1410 }, { 2814, 0x0044 }, { 2816, 0x000b }, { 2819, 0x6404 },
  { 2823, 0x50c0 }, { 2827, 0x8001 }, { 2829, 0x047e }, { 2836, 0x8984 },
  { 2841, 0x0658 }, { 2846, 0x4140 }, { 2849, 0xc000 }, { 2851, 0x94a4 },
  { 2857, 0xa862 }, { 2863, 0x09dc }, { 2870, 0x1800 }, { 2872, 0x0000 },
  /* 0x6c00 */
  { 2872, 0x8100 }, { 2874, 0x000a }, { 2876, 0x0008 }, { 2877, 0x4190 },
  { 2881, 0x4007 }, { 2885, 0xe4a1 }, { 2892, 0x2501 }, { 2896, 0x6445 },
  { 2902, 0x11ee }, { 2910, 0x0e7d }, { 2919, 0x4800 }, { 2921, 0xfb08 },
  { 2929, 0x1616 }, { 2935, 0x08a8 }, { 2939, 0xc92e }, { 2947, 0x0009 },
  /* 0x6d00 */
  { 2949, 0x1800 }, { 2951, 0x4a82 }, { 2956, 0x06a0 }, { 2960, 0x6b64 },
  { 2968, 0x0002 }, { 2969, 0x1600 }, { 2972, 0x5648 }, { 2978, 0x8390 },
  { 2983, 0x73a0 }, { 2990, 0x002a }, { 2993, 0x8000 }, { 2994, 0x0024 },
  { 2996, 0x88f9 }, { 3004, 0x4702 }, { 3009, 0x4d02 }, { 3014, 0x0faa },
  /* 0x6e00 */
  { 3022, 0x0000 }, { 3022, 0x8e80 }, { 3027, 0xb87b }, { 3037, 0x7554 },
  { 3045, 0x2418 }, { 3049, 0xd940 }, { 3055, 0xc880 }, { 3059, 0x040c },
  { 3062, 0x0000 }, { 3062, 0xb041 }, { 3067, 0x8c24 }, { 3072, 0x0442 },
  { 3075, 0x5a34 }, { 3082, 0x001a }, { 3085, 0x8000 }, { 3086, 0xc110 },
  /* 0x6f00 */
  { 3090, 0x8046 }, { 3094, 0x0032 }, { 3097, 0x180d }, { 3102, 0x8106 },
  { 3106, 0x0002 }, { 3107, 0xcd92 }, { 3115, 0x6014 }, { 3119, 0x7401 },
  { 3124, 0x6112 }, { 3129, 0x0091 }, { 3132, 0xc098 }, { 3137, 0x420a },
  { 3141, 0x040f }, { 3146, 0x8420 }, { 3149, 0x9a13 }, { 3156, 0x4002 },
  /* 0x7000 */
  { 3158, 0x8a62 }, { 3164, 0xfd22 }, { 3173, 0x8188 }, { 3177, 0x4080 },
  { 3179, 0x1000 }, { 3180, 0x2103 }, { 3184, 0x0808 }, { 3186, 0x3101 },
  { 3190, 0x4420 }, { 3193, 0x0704 }, { 3197, 0xb812 }, { 3203, 0x0388 },
  { 3207, 0x8900 }, { 3210, 0xa300 }, { 3214, 0x0000 }, { 3214, 0x2202 },
  /* 0x7100 */
  { 3217, 0x1210 }, { 3220, 0x4600 }, { 3223, 0x0042 }, { 3225, 0x0041 },
  { 3227, 0x5680 }, { 3232, 0x5241 }, { 3237, 0x52f0 }, { 3244, 0x2000 },
  { 3245, 0x8610 }, { 3249, 0x8214 }, { 3253, 0x1004 }, { 3255, 0x4602 },
  { 3259, 0x430a }, { 3264, 0x8035 }, { 3269, 0x60e0 }, { 3274, 0xd800 },
  /* 0x7200 */
  { 3278, 0x0041 }, { 3280, 0x0801 }, { 3282, 0x3400 }, { 3285, 0x6c65 },
  { 3293, 0x11c1 }, { 3298, 0xab04 }, { 3304, 0x0286 }, { 3308, 0x2204 },
  { 3311, 0x0003 }, { 3313, 0x0000 }, { 3313, 0x9084 }, { 3317, 0x0000 },
  { 3317, 0x4015 }, { 3321, 0x0281 }, { 3324, 0x0202 }, { 3326, 0x3300 },
  /* 0x7300 */
  { 3330, 0x0400 }, { 3331, 0x3840 }, { 3335, 0x0e20 }, { 3339, 0xc0c0 },
  { 3343, 0x0030 }, { 3345, 0x0085 }, { 3348, 0x0500 }, { 3350, 0x0d25 },
  { 3356, 0x4ad0 }, { 3362, 0x81d0 }, { 3367, 0x2280 }, { 3370, 0x020c },
  { 3373, 0xb605 }, { 3380, 0x6240 }, { 3384, 0x2679 }, { 3392, 0x6280 },
  /* 0x7400 */
  { 3396, 0x02ea }, { 3402, 0x0808 }, { 3404, 0xdd67 }, { 3415, 0x8579 },
  { 3423, 0x081b }, { 3428, 0xdea0 }, { 3436, 0x8735 }, { 3444, 0x4000 },
  { 3445, 0x0a8c }, { 3450, 0xd100 }, { 3454, 0x05aa }, { 3460, 0xa225 },
  { 3466, 0x8440 }, { 3469, 0x1510 }, { 3473, 0x404d }, { 3478, 0x0080 },
  /* 0x7500 */
  { 3479, 0x0012 }, { 3481, 0x8d22 }, { 3487, 0x1968 }, { 3493, 0x058f },
  { 3500, 0x9080 }, { 3503, 0x3a1a }, { 3510, 0x8464 }, { 3515, 0x8561 },
  { 3521, 0xccc0 }, { 3527, 0x2002 }, { 3529, 0x0820 }, { 3531, 0x732e },
  { 3540, 0x20a4 }, { 3544, 0x0b34 }, { 3550, 0x0004 }, { 3551, 0x1415 },
  /* 0x7600 */
  { 3556, 0x2001 }, { 3558, 0x8200 }, { 3560, 0x0057 }, { 3565, 0x0800 },
  { 3566, 0x5004 }, { 3569, 0x0044 }, { 3571, 0x1212 }, { 3575, 0x7905 },
  { 3582, 0x40d0 }, { 3586, 0x0009 }, { 3588, 0x4000 }, { 3589, 0x8400 },
  { 3591, 0x054c }, { 3596, 0xd844 }, { 3602, 0x409a }, { 3607, 0x5114 },
  /* 0x7700 */
  { 3612, 0x0b12 }, { 3617, 0x4000 }, { 3618, 0x0201 }, { 3620, 0x1580 },
  { 3624, 0x2001 }, { 3626, 0x0800 }, { 3627, 0x084a }, { 3631, 0xc200 },
  { 3634, 0x0800 }, { 3635, 0x4002 }, { 3637, 0x3020 }, { 3640, 0x9809 },
  { 3645, 0x0000 }, { 3645, 0x1880 }, { 3648, 0xe22c }, { 3655, 0x0008 },
  /* 0x7800 */
  { 3656, 0x0004 }, { 3657, 0x0004 }, { 3658, 0x10e0 }, { 3662, 0x0014 },
  { 3664, 0x8020 }, { 3666, 0x2000 }, { 3667, 0x9800 }, { 3670, 0x1000 },
  { 3671, 0x7082 }, { 3676, 0x0082 }, { 3678, 0x0288 }, { 3681, 0x1c00 },
  { 3684, 0x4c22 }, { 3689, 0x0001 }, { 3690, 0x9100 }, { 3693, 0x0820 },
  /* 0x7900 */
  { 3695, 0x4002 }, { 3697, 0x0040 }, { 3698, 0x1c00 }, { 3701, 0x4400 },
  { 3703, 0x0383 }, { 3708, 0x7cc1 }, { 3716, 0x2121 }, { 3720, 0x8400 },
  { 3722, 0xe002 }, { 3726, 0x0002 }, { 3727, 0x44c0 }, { 3731, 0xe20a },
  { 3737, 0x0e03 }, { 3742, 0x8126 }, { 3747, 0x02d0 }, { 3751, 0x0800 },
  /* 0x7a00 */
  { 3752, 0x2921 }, { 3757, 0x9690 }, { 3763, 0x4001 }, { 3765, 0xb8c2 },
  { 3772, 0x6241 }, { 3777, 0x0080 }, { 3778, 0x0a06 }, { 3782, 0xa651 },
  { 3789, 0x0112 }, { 3792, 0x812c }, { 3797, 0xc600 }, { 3801, 0x0400 },
  { 3802, 0x0cb0 }, { 3807, 0xa280 }, { 3811, 0xa429 }, { 3817, 0x8640 },
  /* 0x7b00 */
  { 3821, 0x8000 }, { 3822, 0x4a02 }, { 3826, 0x3041 }, { 3830, 0x0200 },
  { 3831, 0xba40 }, { 3837, 0x0057 }, { 3842, 0x5001 }, { 3845, 0x2020 },
  { 3847, 0x8880 }, { 3850, 0x24b0 }, { 3855, 0x2002 }, { 3857, 0x0112 },
  { 3860, 0x02d3 }, { 3866, 0x0004 }, { 3867, 0x0211 }, { 3870, 0x0000 },
  /* 0x7c00 */
  { 3870, 0x0080 }, { 3871, 0x4004 }, { 3873, 0x0c82 }, { 3877, 0xe000 },
  { 3880, 0x3008 }, { 3883, 0x0000 }, { 3883, 0x1011 }, { 3886, 0x0008 },
  { 3887, 0x0208 }, { 3889, 0x81a4 }, { 3894, 0x40a0 }, { 3897, 0x420e },
  { 3902, 0x0400 }, { 3903, 0xc040 }, { 3906, 0x0081 }, { 3908, 0x4800 },
  /* 0x7d00 */
  { 3910, 0x2df5 }, { 3920, 0x0f91 }, { 3927, 0xd807 }, { 3934, 0x0629 },
  { 3939, 0x007c }, { 3944, 0x4001 }, { 3946, 0x4546 }, { 3952, 0x824e },
  { 3958, 0xc000 }, { 3960, 0x1008 }, { 3962, 0x3005 }, { 3966, 0xed36 },
  { 3976, 0x0c80 }, { 3979, 0x6540 }, { 3984, 0x930b }, { 3991, 0x0810 },
  /* 0x7e00 */
  { 3993, 0x0600 }, { 3995, 0xe820 }, { 4000, 0xc80a }, { 4005, 0x6082 },
  { 4009, 0x00ca }, { 4013, 0x4034 }, { 4017, 0x2e02 }, { 4022, 0x1201 },
  { 4025, 0x9004 }, { 4028, 0x1948 }, { 4033, 0x0000 }, { 4033, 0x0000 },
  { 4033, 0x0000 }, { 4033, 0x0000 }, { 4033, 0x0000 }, { 4033, 0x0000 },
  /* 0x7f00 */
  { 4033, 0x0000 }, { 4033, 0x0000 }, { 4033, 0x0000 }, { 4033, 0x0540 },
  { 4036, 0x1000 }, { 4037, 0x0031 }, { 4040, 0x4c00 }, { 4043, 0x02a5 },
  { 4048, 0x5520 }, { 4053, 0x4410 }, { 4056, 0x0310 }, { 4059, 0x2304 },
  { 4063, 0x5422 }, { 4068, 0x8034 }, { 4072, 0x0a03 }, { 4076, 0x1201 },
  /* 0x8000 */
  { 4079, 0x126b }, { 4086, 0x01a1 }, { 4090, 0x2000 }, { 4091, 0xa048 },
  { 4095, 0x0448 }, { 4098, 0x4540 }, { 4102, 0x8000 }, { 4103, 0xe08d },
  { 4110, 0x1af0 }, { 4117, 0x2840 }, { 4120, 0x8626 }, { 4126, 0x0416 },
  { 4130, 0x5018 }, { 4134, 0x4c00 }, { 4137, 0x0032 }, { 4140, 0x2112 },
  /* 0x8100 */
  { 4144, 0x05e4 }, { 4150, 0x0d00 }, { 4153, 0x8a08 }, { 4157, 0x4200 },
  { 4159, 0x4800 }, { 4161, 0x0033 }, { 4165, 0x0860 }, { 4168, 0x8703 },
  { 4174, 0x8501 }, { 4178, 0x3400 }, { 4181, 0x0109 }, { 4184, 0xe428 },
  { 4190, 0x2045 }, { 4194, 0x8100 }, { 4196, 0x25a8 }, { 4202, 0x5c18 },
  /* 0x8200 */
  { 4208, 0x35a0 }, { 4214, 0xd804 }, { 4219, 0x1c02 }, { 4223, 0x02e0 },
  { 4227, 0x00a1 }, { 4230, 0x0200 }, { 4231, 0xc050 }, { 4235, 0x4146 },
  { 4240, 0x6800 }, { 4243, 0xa604 }, { 4248, 0xf260 }, { 4255, 0xbb8a },
  { 4264, 0x0000 }, { 4264, 0xc8b6 }, { 4272, 0x00e2 }, { 4276, 0x6002 },
  /* 0x8300 */
  { 4279, 0x023e }, { 4285, 0x0080 }, { 4286, 0x8900 }, { 4289, 0x0372 },
  { 4295, 0x8681 }, { 4300, 0x0006 }, { 4302, 0x0000 }, { 4302, 0x0888 },
  { 4305, 0x4600 }, { 4308, 0x4140 }, { 4311, 0x0e04 }, { 4315, 0x2000 },
  { 4316, 0x1622 }, { 4321, 0x1048 }, { 4324, 0x8a00 }, { 4327, 0x2217 },
  /* 0x8400 */
  { 4333, 0x7418 }, { 4339, 0x0000 }, { 4339, 0x1200 }, { 4341, 0x2102 },
  { 4344, 0x0200 }, { 4345, 0x0880 }, { 4347, 0x984a }, { 4353, 0x0420 },
  { 4355, 0x0000 }, { 4355, 0x1211 }, { 4359, 0x0002 }, { 4360, 0x9904 },
  { 4365, 0x2a55 }, { 4372, 0x0402 }, { 4374, 0x5000 }, { 4376, 0x1010 },
  /* 0x8500 */
  { 4378, 0x0000 }, { 4378, 0x459a }, { 4385, 0xb02a }, { 4391, 0xa000 },
  { 4393, 0x420a }, { 4397, 0x0208 }, { 4399, 0x2708 }, { 4404, 0x0000 },
  { 4404, 0x8090 }, { 4407, 0x0812 }, { 4410, 0x8740 }, { 4415, 0x0401 },
  { 4417, 0xe202 }, { 4422, 0x3020 }, { 4425, 0x0630 }, { 4429, 0x8c80 },
  /* 0x8600 */
  { 4433, 0x04c4 }, { 4437, 0x04c0 }, { 4440, 0x2000 }, { 4441, 0x8000 },
  { 4442, 0x4000 }, { 4443, 0xd831 }, { 4450, 0x0080 }, { 4451, 0x0200 },
  { 4452, 0x1400 }, { 4454, 0x0008 }, { 4455, 0x0218 }, { 4458, 0x0000 },
  { 4458, 0x0880 }, { 4460, 0x8a10 }, { 4464, 0x2010 }, { 4466, 0x4000 },
  /* 0x8700 */
  { 4467, 0x010d }, { 4471, 0x1500 }, { 4474, 0x0000 }, { 4474, 0x0000 },
  { 4474, 0x4000 }, { 4475, 0x80a0 }, { 4478, 0x0140 }, { 4480, 0x0150 },
  { 4483, 0x2004 }, { 4485, 0x8000 }, { 4486, 0x0004 }, { 4487, 0x0408 },
  { 4489, 0x0010 }, { 4490, 0x0000 }, { 4490, 0x9001 }, { 4493, 0x4a04 },
  /* 0x8800 */
  { 4497, 0x0020 }, { 4498, 0x8000 }, { 4499, 0x000c }, { 4501, 0x0842 },
  { 4504, 0x3041 }, { 4508, 0x2a8c }, { 4514, 0x090e }, { 4519, 0xc085 },
  { 4524, 0x2906 }, { 4529, 0x40c4 }, { 4533, 0x0800 }, { 4534, 0x0010 },
  { 4535, 0x8006 }, { 4538, 0xb230 }, { 4544, 0x0102 }, { 4546, 0x2138 },
  /* 0x8900 */
  { 4551, 0x0080 }, { 4552, 0x030d }, { 4557, 0x0420 }, { 4559, 0x0940 },
  { 4562, 0x0012 }, { 4564, 0x8000 }, { 4565, 0x0410 }, { 4567, 0x8004 },
  { 4569, 0x88ca }, { 4575, 0x0048 }, { 4577, 0x0602 }, { 4580, 0x2404 },
  { 4583, 0x0001 }, { 4584, 0x0004 }, { 4585, 0x0008 }, { 4586, 0x0110 },
  /* 0x8a00 */
  { 4588, 0x550d }, { 4595, 0xa9c8 }, { 4602, 0x2428 }, { 4606, 0x0c52 },
  { 4611, 0x0000 }, { 4611, 0x4831 }, { 4616, 0x624d }, { 4623, 0x022f },
  { 4629, 0x30a0 }, { 4633, 0x4128 }, { 4637, 0x057b }, { 4645, 0xd205 },
  { 4651, 0xa894 }, { 4657, 0x1844 }, { 4661, 0x6cc2 }, { 4668, 0x45c2 },
  /* 0x8b00 */
  { 4674, 0x4017 }, { 4679, 0x2ed1 }, { 4687, 0x1901 }, { 4691, 0x0208 },
  { 4693, 0xc202 }, { 4697, 0x1500 }, { 4700, 0x9040 }, { 4703, 0x2091 },
  { 4707, 0x0401 }, { 4709, 0x044d }, { 4714, 0x0000 }, { 4714, 0x0000 },
  { 4714, 0x0000 }, { 4714, 0x0000 }, { 4714, 0x0000 }, { 4714, 0x0000 },
  /* 0x8c00 */
  { 4714, 0x0000 }, { 4714, 0x0000 }, { 4714, 0x0000 }, { 4714, 0x8080 },
  { 4716, 0x1542 }, { 4721, 0x0420 }, { 4723, 0x0c02 }, { 4726, 0x0600 },
  { 4728, 0x1404 }, { 4731, 0x6000 }, { 4733, 0x9f87 }, { 4743, 0xb9d9 },
  { 4753, 0x059f }, { 4761, 0x540a }, { 4766, 0x245d }, { 4773, 0x3810 },
  /* 0x8d00 */
  { 4777, 0x25b0 }, { 4783, 0x0048 }, { 4785, 0x0000 }, { 4785, 0x0000 },
  { 4785, 0x0000 }, { 4785, 0x0000 }, { 4785, 0x0850 }, { 4788, 0x0099 },
  { 4792, 0x0420 }, { 4794, 0x0200 }, { 4795, 0x0108 }, { 4797, 0x4408 },
  { 4800, 0x9840 }, { 4804, 0x2800 }, { 4806, 0x810a }, { 4810, 0x0008 },
  /* 0x8e00 */
  { 4811, 0x8400 }, { 4813, 0x4001 }, { 4815, 0x0400 }, { 4816, 0x0021 },
  { 4818, 0x0794 }, { 4824, 0x8200 }, { 4826, 0x0001 }, { 4827, 0x0050 },
  { 4829, 0x2482 }, { 4833, 0x0000 }, { 4833, 0x1c00 }, { 4836, 0x0000 },
  { 4836, 0x3c01 }, { 4841, 0x8004 }, { 4843, 0x0800 }, { 4844, 0x4900 },
  /* 0x8f00 */
  { 4847, 0x0228 }, { 4850, 0xf83c }, { 4859, 0x86c0 }, { 4864, 0xcb08 },
  { 4870, 0x6230 }, { 4875, 0xa000 }, { 4877, 0x0004 }, { 4878, 0x0000 },
  { 4878, 0x0000 }, { 4878, 0x1800 }, { 4880, 0xa148 }, { 4885, 0x0007 },
  { 4888, 0x4024 }, { 4891, 0x0012 }, { 4893, 0x2c40 }, { 4897, 0x2285 },
  /* 0x9000 */
  { 4902, 0xa96f }, { 4912, 0xe6b3 }, { 4922, 0x400f }, { 4927, 0x5126 },
  { 4933, 0x6c86 }, { 4940, 0x723b }, { 4949, 0xe20b }, { 4956, 0xb5a4 },
  { 4964, 0x859f }, { 4973, 0x0222 }, { 4976, 0x854c }, { 4982, 0x0123 },
  { 4986, 0x0402 }, { 4988, 0x4000 }, { 4989, 0x2102 }, { 4992, 0x2020 },
  /* 0x9100 */
  { 4994, 0x0004 }, { 4995, 0x0224 }, { 4998, 0x2080 }, { 5000, 0x0004 },
  { 5001, 0x7e00 }, { 5007, 0x0004 }, { 5008, 0x1604 }, { 5012, 0x01a0 },
  { 5015, 0x2a80 }, { 5019, 0x1004 }, { 5021, 0xd800 }, { 5025, 0x0032 },
  { 5028, 0xfa81 }, { 5036, 0x3183 }, { 5042, 0x0488 }, { 5045, 0x0020 },
  /* 0x9200 */
  { 5046, 0x2000 }, { 5047, 0x4087 }, { 5052, 0x0000 }, { 5052, 0x8410 },
  { 5055, 0x0221 }, { 5058, 0x4880 }, { 5061, 0x0074 }, { 5065, 0x0000 },
  { 5065, 0x0029 }, { 5068, 0x114a }, { 5073, 0x0000 }, { 5073, 0x02c8 },
  { 5077, 0x9000 }, { 5079, 0x0004 }, { 5080, 0x0410 }, { 5082, 0x1100 },
  /* 0x9300 */
  { 5084, 0x0010 }, { 5085, 0xc501 }, { 5090, 0xc957 }, { 5099, 0x0000 },
  { 5099, 0x2d00 }, { 5103, 0x0810 }, { 5105, 0x4000 }, { 5106, 0x5020 },
  { 5109, 0x1000 }, { 5110, 0x0450 }, { 5113, 0x3088 }, { 5117, 0x0001 },
  { 5118, 0x0008 }, { 5119, 0x4002 }, { 5121, 0x0012 }, { 5123, 0x0040 },
  /* 0x9400 */
  { 5124, 0x0010 }, { 5125, 0x0100 }, { 5126, 0x0820 }, { 5128, 0x0120 },
  { 5130, 0x0010 }, { 5131, 0x0806 }, { 5134, 0x0000 }, { 5134, 0xa000 },
  { 5136, 0x0000 }, { 5136, 0x0000 }, { 5136, 0x0000 }, { 5136, 0x0000 },
  { 5136, 0x0000 }, { 5136, 0x0000 }, { 5136, 0x0000 }, { 5136, 0x0000 },
  /* 0x9500 */
  { 5136, 0x0000 }, { 5136, 0x0000 }, { 5136, 0x0000 }, { 5136, 0x0000 },
  { 5136, 0x0000 }, { 5136, 0x0000 }, { 5136, 0x0000 }, { 5136, 0x0080 },
  { 5137, 0x8a09 }, { 5142, 0x011e }, { 5147, 0x2138 }, { 5152, 0x1802 },
  { 5155, 0x0480 }, { 5157, 0x1070 }, { 5161, 0x0006 }, { 5163, 0x0000 },
  /* 0x9600 */
  { 5163, 0x0000 }, { 5163, 0x1000 }, { 5164, 0x4402 }, { 5167, 0x8804 },
  { 5170, 0x3815 }, { 5176, 0xf801 }, { 5182, 0x041c }, { 5186, 0x21e9 },
  { 5193, 0x6c60 }, { 5199, 0x1b30 }, { 5205, 0x0588 }, { 5209, 0x0882 },
  { 5212, 0x7af3 }, { 5223, 0x1a60 }, { 5228, 0x870c }, { 5234, 0x0ac5 },
  /* 0x9700 */
  { 5240, 0x00c1 }, { 5243, 0x524a }, { 5249, 0x0080 }, { 5250, 0x2205 },
  { 5254, 0x0114 }, { 5257, 0x5042 }, { 5261, 0x2206 }, { 5265, 0x0490 },
  { 5268, 0xa800 }, { 5271, 0x0000 }, { 5271, 0x2901 }, { 5275, 0x0000 },
  { 5275, 0x0840 }, { 5277, 0x1008 }, { 5279, 0x0000 }, { 5279, 0x8848 },
  /* 0x9800 */
  { 5283, 0x156f }, { 5292, 0x018f }, { 5298, 0x2000 }, { 5299, 0x0b01 },
  { 5303, 0x7040 }, { 5307, 0x4510 }, { 5311, 0x88a0 }, { 5315, 0x0000 },
  { 5315, 0x0000 }, { 5315, 0x0000 }, { 5315, 0x8100 }, { 5317, 0x0002 },
  { 5318, 0x0090 }, { 5320, 0x9800 }, { 5323, 0xe006 }, { 5328, 0x7010 },
  /* 0x9900 */
  { 5332, 0x1608 }, { 5336, 0x4109 }, { 5340, 0x0101 }, { 5342, 0x0000 },
  { 5342, 0x3a20 }, { 5347, 0x0096 }, { 5351, 0x0000 }, { 5351, 0x0000 },
  { 5351, 0x0000 }, { 5351, 0x2240 }, { 5354, 0x7120 }, { 5359, 0x021a },
  { 5363, 0x0002 }, { 5364, 0xa227 }, { 5371, 0x2000 }, { 5372, 0x8002 },
  /* 0x9a00 */
  { 5374, 0xc102 }, { 5378, 0x0200 }, { 5379, 0x0800 }, { 5380, 0x00c1 },
  { 5383, 0x2029 }, { 5387, 0x8ca0 }, { 5392, 0x0624 }, { 5396, 0x0000 },
  { 5396, 0x0000 }, { 5396, 0x0000 }, { 5396, 0x0100 }, { 5397, 0x0100 },
  { 5398, 0x0000 }, { 5398, 0x0118 }, { 5401, 0x4020 }, { 5403, 0x0000 },
  /* 0x9b00 */
  { 5403, 0x0000 }, { 5403, 0x0400 }, { 5404, 0x0480 }, { 5406, 0x1002 },
  { 5408, 0x803e }, { 5414, 0x0410 }, { 5416, 0x8000 }, { 5417, 0x0000 },
  { 5417, 0x4000 }, { 5418, 0x8002 }, { 5420, 0x4800 }, { 5422, 0x0000 },
  { 5422, 0x0200 }, { 5423, 0x0040 }, { 5424, 0x0110 }, { 5426, 0x0000 },
  /* 0x9c00 */
  { 5426, 0x2000 }, { 5427, 0x0025 }, { 5430, 0x0020 }, { 5431, 0x0804 },
  { 5433, 0x0280 }, { 5435, 0x0080 }, { 5436, 0x0000 }, { 5436, 0x0000 },
  { 5436, 0x0000 }, { 5436, 0x0000 }, { 5436, 0x0000 }, { 5436, 0x0000 },
  { 5436, 0x0000 }, { 5436, 0x0000 }, { 5436, 0x02a0 }, { 5439, 0x0058 },
  /* 0x9d00 */
  { 5442, 0x0200 }, { 5443, 0x0800 }, { 5444, 0x0140 }, { 5446, 0x0800 },
  { 5447, 0x0000 }, { 5447, 0x2002 }, { 5449, 0x1003 }, { 5452, 0x0004 },
  { 5453, 0x0000 }, { 5453, 0x0000 }, { 5453, 0x8200 }, { 5455, 0x0010 },
  { 5456, 0x0010 }, { 5457, 0x0080 }, { 5458, 0x0000 }, { 5458, 0x0704 },
  /* 0x9e00 */
  { 5462, 0x0000 }, { 5462, 0x4400 }, { 5464, 0x0000 }, { 5464, 0x0000 },
  { 5464, 0x0000 }, { 5464, 0x0000 }, { 5464, 0x0000 }, { 5464, 0xa220 },
  { 5468, 0x0000 }, { 5468, 0xa08c }, { 5473, 0x0020 }, { 5474, 0x4830 },
  { 5478, 0x6008 }, { 5481, 0x5912 }, { 5487, 0x0100 }, { 5488, 0x0010 },
  /* 0x9f00 */
  { 5489, 0x4180 }, { 5492, 0x0008 }, { 5493, 0x0001 }, { 5494, 0x0800 },
  { 5495, 0x4c00 }, { 5498, 0x8004 }, { 5500, 0x1482 }, { 5504, 0x0080 },
  { 5505, 0x2000 }, { 5506, 0x1021 },
};
static const Summary16 ksc5601_uni2indx_pageac[698] = {
  /* 0xac00 */
  { 5509, 0x0793 }, { 5516, 0x3eff }, { 5529, 0xb011 }, { 5534, 0x1303 },
  { 5539, 0x2801 }, { 5542, 0x1110 }, { 5545, 0x0000 }, { 5545, 0x0593 },
  { 5551, 0x1e7b }, { 5561, 0xb011 }, { 5566, 0x9703 }, { 5573, 0x3b01 },
  { 5579, 0x1112 }, { 5583, 0x00a0 }, { 5585, 0x9593 }, { 5593, 0x306b },
  /* 0xad00 */
  { 5600, 0xb051 }, { 5606, 0x1102 }, { 5609, 0x3201 }, { 5613, 0x1130 },
  { 5617, 0x02b0 }, { 5621, 0x0111 }, { 5624, 0x300a }, { 5628, 0xb879 },
  { 5637, 0x1306 }, { 5642, 0x3001 }, { 5645, 0x0010 }, { 5646, 0x0080 },
  { 5647, 0x0113 }, { 5651, 0x100b }, { 5655, 0x0011 }, { 5657, 0x9300 },
  /* 0xae00 */
  { 5661, 0x2b03 }, { 5667, 0x0010 }, { 5668, 0x0000 }, { 5668, 0x0593 },
  { 5674, 0x746b }, { 5683, 0xb051 }, { 5689, 0x1323 }, { 5695, 0x3b01 },
  { 5701, 0x1030 }, { 5704, 0x0000 }, { 5704, 0x0000 }, { 5704, 0x7000 },
  { 5707, 0xb011 }, { 5712, 0x1303 }, { 5717, 0x2900 }, { 5720, 0x1110 },
  /* 0xaf00 */
  { 5723, 0x2180 }, { 5726, 0x0001 }, { 5727, 0x3000 }, { 5729, 0xb015 },
  { 5735, 0x030e }, { 5740, 0x3001 }, { 5743, 0x0030 }, { 5745, 0x0200 },
  { 5746, 0x0111 }, { 5749, 0x1023 }, { 5753, 0x0000 }, { 5753, 0x1300 },
  { 5756, 0x6b81 }, { 5763, 0x1010 }, { 5765, 0x0300 }, { 5767, 0x0113 },
  /* 0xb000 */
  { 5771, 0x1013 }, { 5775, 0x3011 }, { 5779, 0x0100 }, { 5780, 0x0000 },
  { 5780, 0x5530 }, { 5786, 0x22b8 }, { 5792, 0x0000 }, { 5792, 0x3000 },
  { 5794, 0xb011 }, { 5799, 0x9702 }, { 5805, 0xfb07 }, { 5815, 0x113a },
  { 5821, 0x03b0 }, { 5826, 0x0113 }, { 5830, 0x0021 }, { 5832, 0x0000 },
  /* 0xb100 */
  { 5832, 0x1b00 }, { 5836, 0x3b0d }, { 5844, 0x1138 }, { 5849, 0x03b0 },
  { 5854, 0x0113 }, { 5858, 0x1133 }, { 5864, 0x0001 }, { 5865, 0x1300 },
  { 5868, 0x2b05 }, { 5874, 0x111c }, { 5879, 0x0100 }, { 5880, 0x0000 },
  { 5880, 0x1000 }, { 5881, 0xb011 }, { 5886, 0x1300 }, { 5889, 0x2a01 },
  /* 0xb200 */
  { 5893, 0x1930 }, { 5898, 0x02b0 }, { 5902, 0x0001 }, { 5903, 0x1010 },
  { 5905, 0x0000 }, { 5905, 0x1100 }, { 5907, 0x0301 }, { 5910, 0x1030 },
  { 5913, 0x0230 }, { 5916, 0x0713 }, { 5922, 0x146b }, { 5929, 0x0011 },
  { 5931, 0x1300 }, { 5934, 0x2b05 }, { 5940, 0xf974 }, { 5950, 0x8fb8 },
  /* 0xb300 */
  { 5959, 0x0113 }, { 5963, 0x103b }, { 5969, 0x0000 }, { 5969, 0x0000 },
  { 5969, 0x0000 }, { 5969, 0xd970 }, { 5977, 0x4ab0 }, { 5983, 0x0113 },
  { 5987, 0x103b }, { 5993, 0x0011 }, { 5995, 0x1103 }, { 5999, 0x0000 },
  { 5999, 0x5930 }, { 6005, 0x2ab1 }, { 6012, 0x0111 }, { 6015, 0x1000 },
  /* 0xb400 */
  { 6016, 0x0000 }, { 6016, 0x1101 }, { 6019, 0x0b01 }, { 6023, 0x0010 },
  { 6024, 0x0000 }, { 6024, 0x0113 }, { 6028, 0x102b }, { 6033, 0x0000 },
  { 6033, 0x0101 }, { 6035, 0x2000 }, { 6036, 0x1110 }, { 6039, 0x02a0 },
  { 6042, 0x0111 }, { 6045, 0x3021 }, { 6049, 0xb059 }, { 6056, 0x0102 },
  /* 0xb500 */
  { 6058, 0x0000 }, { 6058, 0x1930 }, { 6063, 0x07b0 }, { 6069, 0x0113 },
  { 6073, 0x383b }, { 6081, 0xb011 }, { 6086, 0x0003 }, { 6088, 0x0000 },
  { 6088, 0x0000 }, { 6088, 0x0000 }, { 6088, 0x0d13 }, { 6094, 0x383b },
  { 6102, 0xb011 }, { 6107, 0x0103 }, { 6110, 0x1000 }, { 6111, 0x0000 },
  /* 0xb600 */
  { 6111, 0x0000 }, { 6111, 0x0113 }, { 6115, 0x1020 }, { 6117, 0x0010 },
  { 6118, 0x0100 }, { 6119, 0x0000 }, { 6119, 0x0110 }, { 6121, 0x0000 },
  { 6121, 0x0000 }, { 6121, 0x3000 }, { 6123, 0x1811 }, { 6127, 0x0002 },
  { 6128, 0x0000 }, { 6128, 0x0010 }, { 6129, 0x0000 }, { 6129, 0x0111 },
  /* 0xb700 */
  { 6132, 0x0023 }, { 6135, 0x0000 }, { 6135, 0x9300 }, { 6139, 0x0b01 },
  { 6143, 0x1110 }, { 6146, 0x0030 }, { 6148, 0x0111 }, { 6151, 0x302b },
  { 6157, 0xb011 }, { 6162, 0x13c7 }, { 6170, 0x3b01 }, { 6176, 0x0130 },
  { 6179, 0x0280 }, { 6181, 0x0000 }, { 6181, 0x3000 }, { 6183, 0xb011 },
  /* 0xb800 */
  { 6188, 0x1383 }, { 6194, 0x2b01 }, { 6199, 0x1130 }, { 6203, 0x03b0 },
  { 6208, 0x0011 }, { 6210, 0x300a }, { 6214, 0xb011 }, { 6219, 0x1102 },
  { 6222, 0x2000 }, { 6223, 0x0000 }, { 6223, 0x0100 }, { 6224, 0x0111 },
  { 6227, 0x102b }, { 6232, 0xa011 }, { 6236, 0x1302 }, { 6240, 0x2b01 },
  /* 0xb900 */
  { 6245, 0x0010 }, { 6246, 0x0100 }, { 6247, 0x0001 }, { 6248, 0x3000 },
  { 6250, 0x9011 }, { 6254, 0x1302 }, { 6258, 0x2b01 }, { 6263, 0x1130 },
  { 6267, 0x66b0 }, { 6274, 0x0000 }, { 6274, 0x3000 }, { 6276, 0xb011 },
  { 6281, 0xd302 }, { 6287, 0x6b07 }, { 6295, 0x113a }, { 6301, 0x07b0 },
  /* 0xba00 */
  { 6307, 0x0103 }, { 6310, 0x0020 }, { 6311, 0x0000 }, { 6311, 0x1300 },
  { 6314, 0x6b05 }, { 6321, 0x1138 }, { 6326, 0x03b0 }, { 6331, 0x0113 },
  { 6335, 0x10b8 }, { 6340, 0x0000 }, { 6340, 0x1b00 }, { 6344, 0x2b05 },
  { 6350, 0x0110 }, { 6352, 0x0300 }, { 6354, 0x0000 }, { 6354, 0x1000 },
  /* 0xbb00 */
  { 6355, 0xa011 }, { 6359, 0x1102 }, { 6362, 0x0a01 }, { 6365, 0x7970 },
  { 6373, 0xa2b0 }, { 6379, 0x0111 }, { 6382, 0x100a }, { 6385, 0x0000 },
  { 6385, 0x1100 }, { 6387, 0x0001 }, { 6388, 0x1110 }, { 6391, 0x0090 },
  { 6393, 0x0111 }, { 6396, 0x0009 }, { 6398, 0x0000 }, { 6398, 0x9300 },
  /* 0xbc00 */
  { 6402, 0xbb05 }, { 6410, 0xf9f2 }, { 6421, 0x22b0 }, { 6426, 0x0113 },
  { 6430, 0x323b }, { 6438, 0x2001 }, { 6440, 0x0000 }, { 6440, 0x0000 },
  { 6440, 0x5930 }, { 6446, 0x06b0 }, { 6451, 0x0193 }, { 6456, 0x303b },
  { 6463, 0xa011 }, { 6467, 0x1123 }, { 6472, 0x0000 }, { 6472, 0x1170 },
  /* 0xbd00 */
  { 6477, 0x02b0 }, { 6481, 0x0011 }, { 6483, 0x1010 }, { 6485, 0x0000 },
  { 6485, 0x1301 }, { 6489, 0x0301 }, { 6492, 0x0110 }, { 6494, 0x0000 },
  { 6494, 0x0793 }, { 6501, 0x162b }, { 6508, 0x0010 }, { 6509, 0x0101 },
  { 6511, 0x0000 }, { 6511, 0x1130 }, { 6515, 0x0200 }, { 6516, 0x0111 },
  /* 0xbe00 */
  { 6519, 0x3029 }, { 6524, 0xb011 }, { 6529, 0x0000 }, { 6529, 0x0000 },
  { 6529, 0x5130 }, { 6534, 0x0eb0 }, { 6540, 0x0513 }, { 6545, 0x383b },
  { 6553, 0xb011 }, { 6558, 0x0303 }, { 6562, 0x0100 }, { 6563, 0x0000 },
  { 6563, 0x0000 }, { 6563, 0x0193 }, { 6568, 0x1039 }, { 6573, 0x0000 },
  /* 0xbf00 */
  { 6573, 0x0302 }, { 6576, 0x3b00 }, { 6581, 0x0000 }, { 6581, 0x0000 },
  { 6581, 0x0113 }, { 6585, 0x0023 }, { 6588, 0x0000 }, { 6588, 0x0000 },
  { 6588, 0x0000 }, { 6588, 0x0010 }, { 6589, 0x0000 }, { 6589, 0x0001 },
  { 6590, 0x3020 }, { 6593, 0x9011 }, { 6597, 0x0002 }, { 6598, 0x0000 },
  /* 0xc000 */
  { 6598, 0x0000 }, { 6598, 0x0000 }, { 6598, 0x0000 }, { 6598, 0x1000 },
  { 6599, 0x0000 }, { 6599, 0x1102 }, { 6602, 0x0301 }, { 6605, 0x0000 },
  { 6605, 0x0000 }, { 6605, 0x0113 }, { 6609, 0xb02b }, { 6616, 0xb079 },
  { 6624, 0x1323 }, { 6630, 0x3b01 }, { 6636, 0x1130 }, { 6640, 0x02b0 },
  /* 0xc100 */
  { 6644, 0x0111 }, { 6647, 0xf021 }, { 6653, 0xb0d9 }, { 6661, 0x1343 },
  { 6667, 0x3b01 }, { 6673, 0x1130 }, { 6677, 0x03b0 }, { 6682, 0x0111 },
  { 6685, 0x7020 }, { 6689, 0xb051 }, { 6695, 0x1322 }, { 6700, 0x2001 },
  { 6702, 0x1110 }, { 6705, 0x0190 }, { 6708, 0x0111 }, { 6711, 0x300b },
  /* 0xc200 */
  { 6716, 0xb011 }, { 6721, 0x9302 }, { 6726, 0xab01 }, { 6732, 0x0016 },
  { 6735, 0x0100 }, { 6736, 0x0113 }, { 6740, 0x3021 }, { 6744, 0xb011 },
  { 6749, 0x0302 }, { 6752, 0x2901 }, { 6756, 0x3130 }, { 6761, 0x02b0 },
  { 6765, 0x0000 }, { 6765, 0x3000 }, { 6767, 0xb819 }, { 6774, 0x1b42 },
  /* 0xc300 */
  { 6780, 0x3301 }, { 6785, 0x1138 }, { 6790, 0x0330 }, { 6794, 0x0000 },
  { 6794, 0x0020 }, { 6795, 0x0000 }, { 6795, 0x1300 }, { 6798, 0x3305 },
  { 6804, 0x1110 }, { 6807, 0x0000 }, { 6807, 0x0000 }, { 6807, 0x0000 },
  { 6807, 0x0001 }, { 6808, 0x9300 }, { 6812, 0x2305 }, { 6817, 0x0130 },
  /* 0xc400 */
  { 6820, 0x0100 }, { 6821, 0x0001 }, { 6822, 0x1010 }, { 6824, 0x3011 },
  { 6828, 0x0100 }, { 6829, 0x0000 }, { 6829, 0x1130 }, { 6833, 0x0230 },
  { 6836, 0x0001 }, { 6837, 0x1010 }, { 6839, 0x0000 }, { 6839, 0x1100 },
  { 6841, 0x0000 }, { 6841, 0x0000 }, { 6841, 0x0200 }, { 6842, 0x8513 },
  /* 0xc500 */
  { 6848, 0x1003 }, { 6851, 0x1011 }, { 6854, 0x1300 }, { 6857, 0x2b01 },
  { 6862, 0x7730 }, { 6870, 0x63b8 }, { 6878, 0x0113 }, { 6882, 0x303b },
  { 6889, 0xb091 }, { 6895, 0x11a2 }, { 6900, 0x0201 }, { 6902, 0x7b30 },
  { 6910, 0x57f0 }, { 6919, 0x0113 }, { 6923, 0x702b }, { 6930, 0xf0d1 },
  /* 0xc600 */
  { 6938, 0x11e3 }, { 6945, 0x1b01 }, { 6950, 0x7130 }, { 6956, 0x0ab9 },
  { 6963, 0x0113 }, { 6967, 0x303b }, { 6974, 0x9001 }, { 6977, 0x1302 },
  { 6981, 0x2b01 }, { 6986, 0x1130 }, { 6990, 0x02b0 }, { 6994, 0x0713 },
  { 7000, 0x302b }, { 7006, 0x3011 }, { 7010, 0x1303 }, { 7015, 0x2301 },
  /* 0xc700 */
  { 7019, 0x1130 }, { 7023, 0x02b0 }, { 7027, 0x0113 }, { 7031, 0x30ab },
  { 7038, 0xb411 }, { 7044, 0x11fe }, { 7053, 0x0901 }, { 7056, 0x7130 },
  { 7062, 0x47b8 }, { 7070, 0x05d3 }, { 7077, 0x307b }, { 7085, 0xb011 },
  { 7090, 0x5303 }, { 7096, 0x2101 }, { 7099, 0x1110 }, { 7102, 0x0000 },
  /* 0xc800 */
  { 7102, 0x0513 }, { 7107, 0x306b }, { 7114, 0xb011 }, { 7119, 0x1102 },
  { 7122, 0x3301 }, { 7127, 0x0010 }, { 7128, 0x0000 }, { 7128, 0x0513 },
  { 7133, 0x38eb }, { 7142, 0xa010 }, { 7145, 0x0102 }, { 7147, 0x3000 },
  { 7149, 0x1110 }, { 7152, 0x02b0 }, { 7156, 0x0013 }, { 7159, 0x3020 },
  /* 0xc900 */
  { 7162, 0xb071 }, { 7169, 0x0102 }, { 7171, 0x1000 }, { 7172, 0x0010 },
  { 7173, 0x0000 }, { 7173, 0x0113 }, { 7177, 0x100b }, { 7181, 0x1011 },
  { 7184, 0x1300 }, { 7187, 0x2b01 }, { 7192, 0x0000 }, { 7192, 0x0000 },
  { 7192, 0x0593 }, { 7198, 0x366b }, { 7207, 0xb095 }, { 7214, 0x1303 },
  /* 0xca00 */
  { 7219, 0x3b01 }, { 7225, 0x0110 }, { 7227, 0x0200 }, { 7228, 0x0000 },
  { 7228, 0x3000 }, { 7230, 0xb011 }, { 7235, 0x0103 }, { 7238, 0x2000 },
  { 7239, 0x0010 }, { 7240, 0x0100 }, { 7241, 0x0000 }, { 7241, 0x3000 },
  { 7243, 0xb011 }, { 7248, 0x030a }, { 7252, 0x1001 }, { 7254, 0x0010 },
  /* 0xcb00 */
  { 7255, 0x0100 }, { 7256, 0x0111 }, { 7259, 0x0003 }, { 7261, 0x0000 },
  { 7261, 0x1302 }, { 7265, 0x2301 }, { 7269, 0x0010 }, { 7270, 0x0300 },
  { 7272, 0x0000 }, { 7272, 0x1000 }, { 7273, 0x0000 }, { 7273, 0x0100 },
  { 7274, 0x0000 }, { 7274, 0x0010 }, { 7275, 0x0290 }, { 7278, 0x0000 },
  /* 0xcc00 */
  { 7278, 0x3000 }, { 7280, 0x3011 }, { 7284, 0x5386 }, { 7291, 0x7b01 },
  { 7298, 0x1130 }, { 7302, 0x03b0 }, { 7307, 0x0151 }, { 7311, 0x0021 },
  { 7313, 0x0000 }, { 7313, 0x1300 }, { 7316, 0x3b01 }, { 7322, 0x1130 },
  { 7326, 0x02b0 }, { 7330, 0x0011 }, { 7332, 0x1010 }, { 7334, 0x0001 },
  /* 0xcd00 */
  { 7335, 0x1302 }, { 7339, 0x2b01 }, { 7344, 0x1110 }, { 7347, 0x0200 },
  { 7348, 0x0000 }, { 7348, 0x1000 }, { 7349, 0xb011 }, { 7354, 0x0102 },
  { 7356, 0x0100 }, { 7357, 0x1130 }, { 7361, 0x02b0 }, { 7365, 0x0001 },
  { 7366, 0x1010 }, { 7368, 0x0001 }, { 7369, 0x1100 }, { 7371, 0x2b01 },
  /* 0xce00 */
  { 7376, 0x1110 }, { 7379, 0x0210 }, { 7381, 0x0113 }, { 7385, 0x002b },
  { 7389, 0x0000 }, { 7389, 0x9300 }, { 7393, 0x2b03 }, { 7399, 0x1130 },
  { 7403, 0x02b0 }, { 7407, 0x0113 }, { 7411, 0x303b }, { 7418, 0x0000 },
  { 7418, 0x0002 }, { 7419, 0x0000 }, { 7419, 0x1930 }, { 7424, 0x03b0 },
  /* 0xcf00 */
  { 7429, 0x0113 }, { 7433, 0x102b }, { 7438, 0xb011 }, { 7443, 0x0103 },
  { 7446, 0x0000 }, { 7446, 0x1130 }, { 7450, 0x02b0 }, { 7454, 0x0113 },
  { 7458, 0x1021 }, { 7461, 0x0000 }, { 7461, 0x0102 }, { 7463, 0x0001 },
  { 7464, 0x0010 }, { 7465, 0x0000 }, { 7465, 0x0113 }, { 7469, 0x102b },
  /* 0xd000 */
  { 7474, 0x0011 }, { 7476, 0x0102 }, { 7478, 0x2000 }, { 7479, 0x1130 },
  { 7483, 0x02b0 }, { 7487, 0x0111 }, { 7490, 0x3001 }, { 7493, 0x3011 },
  { 7497, 0x0002 }, { 7498, 0x0000 }, { 7498, 0x1130 }, { 7502, 0x02b0 },
  { 7506, 0x0313 }, { 7511, 0x303b }, { 7518, 0xb011 }, { 7523, 0x0103 },
  /* 0xd100 */
  { 7526, 0x2000 }, { 7527, 0x0000 }, { 7527, 0x0000 }, { 7527, 0x0513 },
  { 7532, 0x303b }, { 7539, 0xb011 }, { 7544, 0x1102 }, { 7547, 0x1000 },
  { 7548, 0x0110 }, { 7550, 0x0000 }, { 7550, 0x0113 }, { 7554, 0x142b },
  { 7560, 0x0001 }, { 7561, 0x0100 }, { 7562, 0x0000 }, { 7562, 0x0110 },
  /* 0xd200 */
  { 7564, 0x0280 }, { 7566, 0x0001 }, { 7567, 0x3000 }, { 7569, 0xb011 },
  { 7574, 0x0102 }, { 7576, 0x1000 }, { 7577, 0x0010 }, { 7578, 0x0000 },
  { 7578, 0x0113 }, { 7582, 0x1023 }, { 7586, 0x1011 }, { 7589, 0x9302 },
  { 7594, 0x0b05 }, { 7599, 0x1110 }, { 7602, 0x0030 }, { 7604, 0x0113 },
  /* 0xd300 */
  { 7608, 0x702b }, { 7615, 0xb051 }, { 7621, 0x1323 }, { 7627, 0x3b01 },
  { 7633, 0x0030 }, { 7635, 0x0000 }, { 7635, 0x0000 }, { 7635, 0x3000 },
  { 7637, 0xb011 }, { 7642, 0x1303 }, { 7647, 0x2b01 }, { 7652, 0x1110 },
  { 7655, 0x0330 }, { 7659, 0x0101 }, { 7661, 0x300a }, { 7665, 0xb011 },
  /* 0xd400 */
  { 7670, 0x0102 }, { 7672, 0x2000 }, { 7673, 0x0000 }, { 7673, 0x0000 },
  { 7673, 0x0011 }, { 7675, 0x1000 }, { 7676, 0xa011 }, { 7680, 0x9300 },
  { 7684, 0x2b05 }, { 7690, 0x0010 }, { 7691, 0x0200 }, { 7692, 0x0000 },
  { 7692, 0x1000 }, { 7693, 0x9011 }, { 7697, 0x1100 }, { 7699, 0x2901 },
  /* 0xd500 */
  { 7703, 0x1110 }, { 7706, 0x00b0 }, { 7709, 0x0000 }, { 7709, 0x3000 },
  { 7711, 0xb011 }, { 7716, 0x1302 }, { 7720, 0x2b21 }, { 7726, 0x1130 },
  { 7730, 0x03b0 }, { 7735, 0x0001 }, { 7736, 0x0020 }, { 7737, 0x0000 },
  { 7737, 0x1300 }, { 7740, 0x2b05 }, { 7746, 0x1130 }, { 7750, 0x02b0 },
  /* 0xd600 */
  { 7754, 0x0113 }, { 7758, 0x103b }, { 7764, 0x2011 }, { 7767, 0x1300 },
  { 7770, 0x2b21 }, { 7776, 0x1132 }, { 7781, 0x0280 }, { 7783, 0x0013 },
  { 7786, 0x3028 }, { 7790, 0xa011 }, { 7794, 0x1102 }, { 7797, 0x0a01 },
  { 7800, 0x1130 }, { 7804, 0x0292 }, { 7808, 0x0111 }, { 7811, 0x3021 },
  /* 0xd700 */
  { 7815, 0x0011 }, { 7817, 0x1302 }, { 7821, 0x2b01 }, { 7826, 0x1130 },
  { 7830, 0x0290 }, { 7833, 0x03d3 }, { 7840, 0x122b }, { 7846, 0x3011 },
  { 7850, 0x1302 }, { 7854, 0x2b01 },
};
static const Summary16 ksc5601_uni2indx_pagef9[17] = {
  /* 0xf900 */
  { 7859, 0xffff }, { 7875, 0xffff }, { 7891, 0xffff }, { 7907, 0xffff },
  { 7923, 0xffff }, { 7939, 0xffff }, { 7955, 0xffff }, { 7971, 0xffff },
  { 7987, 0xffff }, { 8003, 0xffff }, { 8019, 0xffff }, { 8035, 0xffff },
  { 8051, 0xffff }, { 8067, 0xffff }, { 8083, 0xffff }, { 8099, 0xffff },
  /* 0xfa00 */
  { 8115, 0x0fff },
};
static const Summary16 ksc5601_uni2indx_pageff[15] = {
  /* 0xff00 */
  { 8127, 0xfffe }, { 8142, 0xffff }, { 8158, 0xffff }, { 8174, 0xffff },
  { 8190, 0xffff }, { 8206, 0x7fff }, { 8221, 0x0000 }, { 8221, 0x0000 },
  { 8221, 0x0000 }, { 8221, 0x0000 }, { 8221, 0x0000 }, { 8221, 0x0000 },
  { 8221, 0x0000 }, { 8221, 0x0000 }, { 8221, 0x006f },
};

static int
ksc5601_wctomb (conv_t conv, unsigned char *r, ucs4_t wc, int n)
{
  if (n >= 2) {
    const Summary16 *summary = NULL;
    if (wc >= 0x0000 && wc < 0x0460)
      summary = &ksc5601_uni2indx_page00[(wc>>4)];
    else if (wc >= 0x2000 && wc < 0x2670)
      summary = &ksc5601_uni2indx_page20[(wc>>4)-0x200];
    else if (wc >= 0x3000 && wc < 0x33e0)
      summary = &ksc5601_uni2indx_page30[(wc>>4)-0x300];
    else if (wc >= 0x4e00 && wc < 0x9fa0)
      summary = &ksc5601_uni2indx_page4e[(wc>>4)-0x4e0];
    else if (wc >= 0xac00 && wc < 0xd7a0)
      summary = &ksc5601_uni2indx_pageac[(wc>>4)-0xac0];
    else if (wc >= 0xf900 && wc < 0xfa10)
      summary = &ksc5601_uni2indx_pagef9[(wc>>4)-0xf90];
    else if (wc >= 0xff00 && wc < 0xfff0)
      summary = &ksc5601_uni2indx_pageff[(wc>>4)-0xff0];
    if (summary) {
      unsigned short used = summary->used;
      unsigned int i = wc & 0x0f;
      if (used & ((unsigned short) 1 << i)) {
        unsigned short c;
        /* Keep in `used' only the bits 0..i-1. */
        used &= ((unsigned short) 1 << i) - 1;
        /* Add `summary->indx' and the number of bits set in `used'. */
        used = (used & 0x5555) + ((used & 0xaaaa) >> 1);
        used = (used & 0x3333) + ((used & 0xcccc) >> 2);
        used = (used & 0x0f0f) + ((used & 0xf0f0) >> 4);
        used = (used & 0x00ff) + (used >> 8);
        c = ksc5601_2charset[summary->indx + used];
        r[0] = (c >> 8); r[1] = (c & 0xff);
        return 2;
      }
    }
    return RET_ILUNI;
  }
  return RET_TOOSMALL;
}
