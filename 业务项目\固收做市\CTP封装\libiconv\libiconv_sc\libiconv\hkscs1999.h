/*
 * Copyright (C) 1999-2006 Free Software Foundation, Inc.
 * This file is part of the GNU LIBICONV Library.
 *
 * The GNU LIBICONV Library is free software; you can redistribute it
 * and/or modify it under the terms of the GNU Library General Public
 * License as published by the Free Software Foundation; either version 2
 * of the License, or (at your option) any later version.
 *
 * The GNU LIBICONV Library is distributed in the hope that it will be
 * useful, but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
 * Library General Public License for more details.
 *
 * You should have received a copy of the GNU Library General Public
 * License along with the GNU LIBICONV Library; see the file COPYING.LIB.
 * If not, write to the Free Software Foundation, Inc., 51 Franklin Street,
 * Fifth Floor, Boston, MA 02110-1301, USA.
 */

/*
 * HKSCS:1999
 */

static const unsigned short hkscs1999_2uni_page88[627] = {
  /* 0x88 */
  0x06c0, 0x06c1, 0x06c2, 0x06c3, 0x06c4, 0x720c, 0x06c5, 0x71d1,
  0x71cd, 0x06c6, 0x06c7, 0x71cb, 0x8c68, 0x06c8, 0x71ca, 0x06c9,
  0x06ca, 0x06cb, 0x06cc, 0x720e, 0x06cd, 0x06ce, 0x0080, 0x0041,
  0x010d, 0x0040, 0x0092, 0x0049, 0x009a, 0x0048, 0x00cc, 0x0053,
  0x0111, 0x0052, 0x70fd, 0x02be, 0x70fd, 0x02c0, 0x004a, 0x0081,
  0x0061, 0x010e, 0x0060, 0x0151, 0x0093, 0x0069, 0x009b, 0x0068,
  0x00ab, 0x006d, 0x0110, 0x006c, 0x00cd, 0x0073, 0x0112, 0x0072,
  0x00eb, 0x007a, 0x0114, 0x0079, 0x0116, 0x0118, 0x011a, 0x011c,
  0x007c, 0x70fd, 0x02bf, 0x70fd, 0x02c1, 0x006a, 0x0161, 0x041a,
  0x041b, 0x70fd, 0x70fd, 0x70fd, 0x70fd, 0x70fd, 0x70fd, 0x70fd,
  0x70fd, 0x70fd, 0x70fd, 0x70fd, 0x70fd, 0x70fd, 0x70fd, 0x70fd,
  0x70fd, 0x70fd, 0x70fd, 0x70fd, 0x70fd, 0x70fd, 0x70fd, 0x70fd,
  0x70fd, 0x70fd, 0x70fd, 0x70fd, 0x70fd, 0x70fd, 0x70fd, 0x70fd,
  0x70fd, 0x70fd, 0x70fd, 0x70fd, 0x70fd, 0x70fd, 0x70fd, 0x70fd,
  0x70fd, 0x70fd, 0x70fd, 0x70fd, 0x70fd, 0x70fd, 0x70fd, 0x70fd,
  0x70fd, 0x70fd, 0x70fd, 0x70fd, 0x70fd, 0x70fd, 0x70fd, 0x70fd,
  0x70fd, 0x70fd, 0x70fd, 0x70fd, 0x70fd, 0x70fd, 0x70fd, 0x70fd,
  0x70fd, 0x70fd, 0x70fd, 0x70fd, 0x70fd, 0x70fd, 0x70fd, 0x70fd,
  0x70fd, 0x70fd, 0x70fd, 0x70fd, 0x70fd, 0x70fd, 0x70fd, 0x70fd,
  0x70fd, 0x70fd, 0x70fd, 0x70fd, 0x70fd,
  /* 0x89 */
  0xf029, 0x8005, 0x70fd, 0x374a, 0x70fd, 0x70fd, 0x207d, 0x40dd,
  0x6dce, 0x62df, 0x70fd, 0x70fd, 0xd275, 0x36d1, 0x215a, 0x2168,
  0x21e8, 0x2396, 0x23b4, 0x23dc, 0x2424, 0x24e1, 0x24e8, 0x257b,
  0x258e, 0x2611, 0x2618, 0x2922, 0x2b30, 0x2b44, 0x2b47, 0x2b72,
  0x2b74, 0x2da6, 0x2dde, 0x2ddf, 0x2eda, 0x30c6, 0x327b, 0x37c9,
  0x3a3e, 0x3a44, 0x3aa5, 0x3f8e, 0x42bc, 0x4735, 0x50a4, 0x50ac,
  0x50ba, 0x50c7, 0x50cf, 0x50df, 0x5106, 0x5137, 0x547a, 0x54cf,
  0x556f, 0x5b46, 0x5d3e, 0x5d62, 0x60a6, 0x60a7, 0x60ae, 0x4611,
  0x4efc, 0x4fcd, 0x3b86, 0x4cc9, 0x2467, 0x70fd, 0x70fd, 0x70fd,
  0x70fd, 0x628c, 0x4ab8, 0x625e, 0x52bc, 0x70fd, 0x5e4b, 0x52f6,
  0x78e7, 0x70fd, 0x70fd, 0x529f, 0x6f47, 0x1f8d, 0x6e49, 0x6e8c,
  0x1efe, 0xebb6, 0xcd4e, 0x6e8a, 0xee73, 0x0901, 0x70fd, 0x409a,
  0x543e, 0x4719, 0x70fd, 0x1c11, 0x6b6c, 0x6b8f, 0x7019, 0x4b87,
  0xdcac, 0x8a8a, 0x7650, 0x9526, 0x2064, 0x20c1, 0x20c0, 0x20c7,
  0x20ff, 0x212b, 0x2177, 0x078c, 0x21fd, 0x1148, 0x2243, 0x22c8,
  0x07bd, 0x07d3, 0x07e5, 0x23c6, 0x2b45, 0x241b, 0x243c, 0x2445,
  0x20c9, 0x24b9, 0x24d0, 0x2567, 0x0907, 0x25e9, 0x0891, 0x25f0,
  0x0893, 0x2602, 0x2663, 0x08ad, 0x08b2, 0x09c1, 0x26d3, 0x26e3,
  0x26f4, 0x26f9, 0x2710, 0x272f, 0x2758, 0x2763, 0x2768, 0x08d8,
  0x277f, 0x08e5, 0x08ff, 0x2817, 0x0905,
  /* 0x8a */
  0xd784, 0x2765, 0x70fd, 0x7b02, 0x7bd5, 0xb42b, 0x27d0, 0x96c6,
  0x0d2c, 0x7401, 0x5f86, 0xb138, 0xe665, 0x1353, 0xd97e, 0x497a,
  0x9638, 0x0d74, 0x1ad5, 0xda1d, 0xc7b2, 0xb16a, 0x371d, 0x7c3c,
  0x7e74, 0x7b95, 0x7fb4, 0x36cd, 0x5fbe, 0x7d56, 0x7acb, 0x7e24,
  0x96a9, 0xdad6, 0xaa13, 0x70fd, 0x7c06, 0xe9cd, 0xd9a9, 0x1af4,
  0xb227, 0x96c2, 0x6bb2, 0x0da7, 0xe6f4, 0x12ed, 0x0846, 0xb587,
  0xe754, 0xd3c8, 0x9744, 0x6dee, 0x6915, 0x70fd, 0x16d9, 0xbfe5,
  0x36f4, 0x2723, 0x974c, 0x95ca, 0x7f37, 0x0d3b, 0x7f2f, 0xc49a,
  0xc4d6, 0xd4a0, 0x372a, 0xb392, 0x7b03, 0x5fa8, 0x8de1, 0xe14c,
  0x7731, 0x70fd, 0x1b0b, 0x7269, 0x12fa, 0x2ab3, 0x978d, 0x70fd,
  0xaac8, 0x75bc, 0xbfd7, 0x7e0c, 0x7c56, 0x27b9, 0x13bb, 0x16ba,
  0x70fd, 0x1d74, 0x94e6, 0x7f5d, 0x53aa, 0x69f5, 0x7c5c, 0x35b9,
  0x0d3e, 0x9275, 0x5f00, 0x28e1, 0x36bc, 0x1143, 0x70fd, 0xf101,
  0x7cc9, 0x950f, 0x96c9, 0x70fd, 0x7f88, 0xa142, 0x0cd2, 0x0d46,
  0xdb1b, 0x08b8, 0xbdc9, 0x8d07, 0x2892, 0x7df1, 0x96b2, 0xe720,
  0x07fc, 0x3e7d, 0xb1bb, 0x70fd, 0x70fd, 0xd0f4, 0x988b, 0x8e48,
  0xf15b, 0xe24d, 0x7d3a, 0x7af4, 0xc5dc, 0x5193, 0x7f8f, 0x9303,
  0x9439, 0x093b, 0xb4a3, 0x7d4c, 0x7e4d, 0x7d6a, 0x1293, 0x7df0,
  0x7c07, 0x800f, 0x7d0c, 0x70fd, 0x7d6b, 0x7a69, 0x7c08, 0x7f80,
  0x7ffd, 0x12f9, 0x9196, 0x3672, 0x7e6d,
  /* 0x8b */
  0x9d34, 0xd1b9, 0x95ce, 0x7c3e, 0x7c3f, 0x9651, 0x9655, 0x0d58,
  0x7d58, 0x7f87, 0x7dee, 0xf132, 0xc890, 0xe252, 0xe2d9, 0xe24a,
  0x66aa, 0x270c, 0x54c4, 0x27f9, 0x70fd, 0xec83, 0x6d26, 0x6bb6,
  0xd29e, 0x97ee, 0x4340, 0x536d, 0x52ec, 0x2e5c, 0xc3b2, 0x5334,
  0x0ad7, 0x259f, 0xd97d, 0x62b6, 0x7dba, 0x7ccf, 0x7d37, 0x7dbb,
  0x091d, 0xb16b, 0x0949, 0x7b96, 0x28ef, 0x92b5, 0x7f89, 0x7cd0,
  0x7d38, 0x7f38, 0x8008, 0xda87, 0x8315, 0x7d39, 0xb1d0, 0x97a4,
  0x2c94, 0x7edd, 0x7ede, 0x7fb5, 0x7fb6, 0x29dc, 0x7cd1, 0xd214,
  0xdb4d, 0x7e75, 0x7d3b, 0xb47e, 0x0a43, 0x7e76, 0x8040, 0xb598,
  0xefbd, 0xae5a, 0x867a, 0xa6b7, 0xdafc, 0x2ad9, 0x24a8, 0x095a,
  0xb8bd, 0x4db2, 0x2da8, 0x1b00, 0x1dec, 0x6fa7, 0x1ce7, 0x6d1f,
  0x6c8d, 0xbb74, 0x9abd, 0x283b, 0x0932, 0x28c9, 0x2068, 0x2b42,
  0x8901, 0xf238, 0x6851, 0x7186, 0x209b, 0x20fb, 0x087e, 0x2e63,
  0x3191, 0x3204, 0x0c3a, 0x348c, 0x3775, 0x3dba, 0x3e75, 0x3e7a,
  0x426c, 0x442b, 0x206c, 0x44ad, 0xad69, 0x5152, 0x4b3b, 0x4ef9,
  0x5153, 0xc12a, 0x0801, 0x70fd, 0xc1cb, 0x5202, 0x5280, 0xc412,
  0xc711, 0x259d, 0x59e4, 0x5b41, 0xd3b2, 0x5d20, 0x5e5d, 0x6585,
  0x6678, 0x667f, 0x66e8, 0xe30f, 0x68e6, 0x6975, 0x69ce, 0x69de,
  0x6a63, 0xe790, 0x6d7c, 0x6e9f, 0x6f44, 0x3daf, 0x7047, 0x2077,
  0x7187, 0x671d, 0x3477, 0x65a2,
};
static const unsigned short hkscs1999_2uni_page8d[3140] = {
  /* 0x8d */
  0x70fd, 0x70fd, 0x70fd, 0x70fd, 0x70fd, 0x70fd, 0x70fd, 0x70fd,
  0x70fd, 0x70fd, 0x70fd, 0x70fd, 0x70fd, 0x70fd, 0x70fd, 0x70fd,
  0x70fd, 0x70fd, 0x70fd, 0x70fd, 0x70fd, 0x70fd, 0x70fd, 0x70fd,
  0x70fd, 0x70fd, 0x70fd, 0x70fd, 0x70fd, 0x70fd, 0x70fd, 0x70fd,
  0x2f7e, 0x2f88, 0x2f96, 0x10fc, 0x0b4f, 0x2fe4, 0x2ff9, 0x0b60,
  0x0b78, 0x3082, 0x30fd, 0x3165, 0x31c3, 0x0c48, 0x0c54, 0x0c7f,
  0x0c8d, 0x3317, 0x337d, 0x2f25, 0x0cc9, 0x33f7, 0x33f9, 0x340f,
  0x0cf8, 0x346c, 0x34d0, 0x3525, 0x3558, 0x0d38, 0x28f1, 0x0d43,
  0x3622, 0x363b, 0x3647, 0x369a, 0x0d8b, 0x3700, 0x2f55, 0x2861,
  0x701f, 0x0dd7, 0x37c6, 0x0dfd, 0x383f, 0x3893, 0x0e32, 0x38d2,
  0x0e62, 0x3956, 0x0e82, 0x39e4, 0x3a40, 0x0e98, 0x3a8a, 0x3ac4,
  0x0eb2, 0x0eb1, 0x0ebb, 0x3b49, 0x3b83, 0x445c, 0x3ba4, 0x3bdf,
  0x3bc5, 0x0efc, 0x3c16, 0x0f1d, 0x3ca5, 0x3cb4, 0x3cb1, 0x3cc2,
  0x0f2c, 0x3cd9, 0x0f32, 0x3ceb, 0x3cf5, 0x3d14, 0x3d36, 0x3dc1,
  0x3e01, 0x3e2a, 0x3eb5, 0x3eea, 0x0fcb, 0x3f42, 0x3f46, 0x3f66,
  0x3fc1, 0x0fef, 0x3fe4, 0x3ff1, 0x4015, 0x4018, 0x4029, 0x4086,
  0xdfc0, 0x40bb, 0x40e2, 0x40da, 0x6fff, 0x40e8, 0x40e9, 0x4124,
  0x4134, 0x1046, 0xa481, 0x4181, 0x41be, 0x106a, 0x1075, 0x43b7,
  0x2ed9, 0x108a, 0x422c, 0x1091, 0x4250, 0x4254, 0x426f, 0x427f,
  0x4289, 0x73e5, 0x16c1, 0x0931, 0x7d98,
  /* 0x8e */
  0xa417, 0x29fe, 0xcc13, 0x433e, 0xb920, 0x098e, 0x3be2, 0xe1e9,
  0x2db4, 0x4c49, 0xb9a1, 0xe659, 0x4c65, 0x4c7d, 0xba6c, 0x4cbb,
  0x4cb0, 0x4cc2, 0x4cc3, 0x43d1, 0xc30d, 0x14ca, 0x4cda, 0x4cdd,
  0x4cea, 0x14ef, 0x26f2, 0xbc01, 0x4d0b, 0x4d55, 0x4d29, 0xb5ce,
  0xbcfe, 0x4da2, 0x4d6f, 0x559c, 0xbbb4, 0xc9bf, 0x4dd0, 0x5621,
  0x4d92, 0x70fd, 0xbd20, 0x10ad, 0xbc65, 0x5692, 0x4dfa, 0x70fd,
  0x4e35, 0xbcc1, 0x4e44, 0x4e83, 0xad02, 0x4ea6, 0x38bd, 0xaab8,
  0x4ec9, 0x4ec7, 0x4ee6, 0x4e74, 0x4ef3, 0x4ef5, 0x70fd, 0x5067,
  0x181d, 0xcb84, 0x4f5d, 0xcc16, 0x468d, 0x4f89, 0x4fab, 0x4335,
  0x4fb3, 0x70fd, 0xa597, 0xbf69, 0x4fe4, 0x1013, 0x4ff5, 0x8639,
  0x4fe5, 0xdbed, 0x70fd, 0xc021, 0xc05a, 0x506e, 0x5092, 0x162b,
  0x656c, 0x5027, 0x5140, 0x5141, 0x5147, 0x4b36, 0xc150, 0x6ae1,
  0x5197, 0xc1d1, 0x51a3, 0x84a1, 0x7168, 0x185c, 0xa066, 0x1803,
  0xdbba, 0x51fa, 0xc309, 0x70fd, 0x5208, 0x521d, 0x70fd, 0x522f,
  0xedc7, 0xca03, 0x523b, 0x523c, 0x5261, 0x9214, 0x1c89, 0xc426,
  0xa363, 0xc4a8, 0x3965, 0x52a7, 0xe048, 0x5307, 0x531a, 0x2af0,
  0x91f6, 0x3ebf, 0xc318, 0xb2f8, 0x3727, 0x834a, 0x5418, 0x869e,
  0x3c93, 0xaee5, 0xaf15, 0x177a, 0x5429, 0x7a0d, 0xc812, 0xa2fe,
  0x2239, 0x83bd, 0x56e2, 0x5562, 0xc84a, 0xae27, 0x9e30, 0x85b3,
  0xa378, 0x54aa, 0x3b5b, 0xf2d4, 0x14db,
  /* 0x8f */
  0x574b, 0x54d0, 0x551a, 0x7cd6, 0x85f4, 0x0a01, 0x9afd, 0x9e5a,
  0x547b, 0x54e2, 0x5518, 0xa3cb, 0xcae3, 0xc845, 0xc8d7, 0x9ece,
  0x10bf, 0x551d, 0x282c, 0x5585, 0x180b, 0xcae5, 0x55ac, 0x70fd,
  0x55d3, 0x07be, 0xcc14, 0x3c97, 0x575a, 0x07d6, 0xcb82, 0x98ef,
  0x5658, 0xbbe4, 0x5671, 0x10d3, 0x17e4, 0x3ce7, 0x564a, 0xa275,
  0x4b58, 0x70fd, 0xc8d6, 0xcbb7, 0xcb83, 0x56de, 0x70fd, 0x5591,
  0x17a0, 0x5693, 0x56e4, 0xbc91, 0x1540, 0xbcc0, 0x1843, 0x5734,
  0x2d32, 0xcbd9, 0x1827, 0x5773, 0x1816, 0x39ff, 0x57d6, 0xdde5,
  0xddfb, 0x5781, 0xcdc8, 0x57c2, 0x8402, 0xce0d, 0xf2f2, 0x186a,
  0x57e8, 0x0988, 0x86e2, 0x2637, 0xcfda, 0x583e, 0x58f1, 0xee38,
  0x596e, 0x9627, 0x5931, 0x595a, 0x598f, 0x28a1, 0x582c, 0x3a96,
  0x190f, 0x59c5, 0x59c6, 0xd160, 0xa339, 0xd164, 0x59de, 0x5a1c,
  0x195b, 0x5a34, 0x5a35, 0x3601, 0x5a45, 0x4977, 0xd24f, 0x5b07,
  0x5b0a, 0x70fd, 0x70fd, 0x5b27, 0x5b3c, 0xe025, 0x5b67, 0xd424,
  0xd5bd, 0x5c1c, 0x4993, 0x62fe, 0x5c10, 0xd559, 0x4ce9, 0xd63a,
  0xa4cf, 0x1a13, 0xd638, 0x437c, 0x5c8c, 0x5c9f, 0xb630, 0xb6e5,
  0x5cbf, 0x5ccc, 0x5ccd, 0x5c29, 0xaefa, 0x5d10, 0x5d1b, 0x5c2f,
  0x851f, 0x1915, 0x59cf, 0x5ddb, 0xd754, 0xd78f, 0xf314, 0x0a65,
  0xd753, 0x5e16, 0xd798, 0xd7bd, 0x5e52, 0x5e43, 0x8750, 0x5e1b,
  0x425c, 0x5e51, 0xb089, 0x11d0, 0x70fd,
  /* 0x90 */
  0x5ee9, 0xd8c2, 0x7ed4, 0xae0a, 0x0ebc, 0xda7c, 0xce4c, 0x4ce7,
  0x5fed, 0x5ff6, 0x6003, 0x63d4, 0x6059, 0x606d, 0xdbe5, 0xdc52,
  0x60e5, 0x6403, 0xef9f, 0x7950, 0x60f3, 0x1c2a, 0xdfde, 0xdd3d,
  0xa33b, 0x3138, 0x9be2, 0x6139, 0xed94, 0xde3c, 0xdd01, 0x8ee5,
  0x0cc0, 0xcc17, 0x6177, 0xdd3c, 0xd5be, 0x61a1, 0xdd6c, 0xddcb,
  0x61e8, 0xde93, 0x6204, 0xde66, 0x61ee, 0x70fd, 0x6267, 0x0e30,
  0x62a9, 0x62c4, 0x4eac, 0xdf33, 0x8b09, 0x630e, 0x3edf, 0x6341,
  0x6362, 0xb739, 0x70fd, 0xe0c6, 0xa25b, 0xe10c, 0xb75b, 0x7bf1,
  0x642c, 0x646b, 0xe0e1, 0xe1eb, 0x428f, 0x2d03, 0xe0e2, 0xe0e5,
  0x1c65, 0x6344, 0xe1ec, 0xe239, 0xe1ff, 0x6473, 0x655b, 0x5ffc,
  0x6685, 0x66a6, 0x6526, 0x66a0, 0x41f6, 0x15b9, 0x917a, 0xde58,
  0x813c, 0xa3ae, 0x1cdf, 0x3e5c, 0x677b, 0x6796, 0x146c, 0x67a3,
  0xcc15, 0x341a, 0x67b6, 0x4af5, 0xe0e0, 0x67bd, 0x260c, 0x1ca1,
  0xc9f8, 0x7334, 0xc290, 0xe46f, 0xe4a5, 0xaf51, 0x8755, 0x9c8a,
  0x6831, 0x5802, 0x6836, 0x1d0f, 0x183d, 0x1885, 0xaf69, 0x4275,
  0x2d81, 0x681b, 0x70fd, 0xe595, 0x6857, 0x2d8a, 0xe5ab, 0x685f,
  0x6525, 0x2310, 0x9a37, 0x9a3c, 0x6889, 0x689f, 0x68b1, 0x68be,
  0x68c0, 0x68d2, 0x68e0, 0xb66c, 0x68ee, 0x461c, 0xe5f3, 0x70fd,
  0x68f5, 0xe5dd, 0xd47a, 0x1d91, 0x6934, 0x6933, 0x694b, 0x6966,
  0x0e4e, 0xceb5, 0x1051, 0x76b0, 0xa69c,
  /* 0x91 */
  0xb886, 0x69ca, 0x69b7, 0x69c8, 0x69c7, 0x1dbf, 0xca67, 0x8513,
  0x27f0, 0x69e1, 0x69e6, 0x69ec, 0x6478, 0x6a39, 0xaea9, 0x1e32,
  0xe7d7, 0xe885, 0x6af5, 0x6b0c, 0x6b3b, 0x6b10, 0x6b58, 0xb8a5,
  0x0a04, 0xe471, 0xea55, 0x6be0, 0x6be2, 0xea05, 0x6bf4, 0x1ece,
  0x6c14, 0x6c2d, 0xddc0, 0x2274, 0x6c34, 0xc768, 0x0c03, 0x99fd,
  0x6c50, 0x6c40, 0xeafe, 0x2c85, 0x86a3, 0x6c8e, 0xa78b, 0x6d02,
  0x6cff, 0x6d0c, 0xec28, 0x6e54, 0xed37, 0xeed2, 0xeeeb, 0xee21,
  0xee63, 0xef1f, 0x6dfe, 0x6e03, 0xee74, 0x6e8e, 0x3ac8, 0x6e44,
  0x8d9b, 0xeed3, 0xef60, 0x877b, 0xef73, 0x6db9, 0xedf9, 0xefb4,
  0x6f10, 0x6f15, 0x6f1e, 0x6f22, 0x1ff4, 0x6f2a, 0x6f2f, 0xa8a4,
  0x6f41, 0x0ea0, 0x0d25, 0x101d, 0x2172, 0x0afe, 0xe22b, 0x6f82,
  0x6f88, 0x1e56, 0x6524, 0xcae2, 0x6f97, 0x70fd, 0x6fb9, 0x28df,
  0x28ca, 0x6fc5, 0x6ab8, 0xe44b, 0x68f2, 0x567f, 0x6fe2, 0x6fe9,
  0x4cdc, 0x700e, 0x4416, 0x1e7e, 0xadf5, 0xae3b, 0x4377, 0xae78,
  0xa888, 0xaed1, 0x459e, 0xe1da, 0x873a, 0x4b9f, 0xdf7e, 0xe336,
  0x6469, 0x64f3, 0xe044, 0x63ec, 0x6481, 0x64cb, 0xdf6c, 0xa9f9,
  0x4417, 0x11eb, 0x4972, 0x4c43, 0x42d0, 0xa9b3, 0xa938, 0x437e,
  0x862f, 0x42a3, 0x86fe, 0x9e99, 0x11c7, 0x86c5, 0xb62f, 0x8638,
  0x0a62, 0x853b, 0x8679, 0x0a21, 0x85b4, 0x8711, 0xbecb, 0x0a63,
  0x8500, 0x299b, 0xaea5, 0x82be, 0x8168,
  /* 0x92 */
  0x8286, 0x8376, 0x5703, 0x9fa6, 0x70fd, 0x5655, 0xae14, 0xcea5,
  0xa3b1, 0xb6dc, 0xa43b, 0xcd92, 0x17f4, 0x9fee, 0xe91d, 0xcc66,
  0x3a39, 0x0a73, 0x0f55, 0x10e7, 0x2aac, 0x8762, 0x3a50, 0x1357,
  0xa03f, 0xa621, 0xa5cb, 0xa64f, 0xc961, 0x270b, 0x28de, 0xc471,
  0x28d2, 0x7e9f, 0x7a68, 0x7ccd, 0x64c6, 0xe113, 0x649c, 0x2138,
  0x236b, 0x0b59, 0xa976, 0x20fc, 0x7525, 0x743f, 0x218b, 0x21ca,
  0xb7d1, 0x2ca8, 0x726b, 0x748b, 0x0cd9, 0x73ca, 0x74d4, 0x0775,
  0x2169, 0x7380, 0xe3b3, 0x7335, 0x5c5a, 0x72cc, 0x20d8, 0x230d,
  0x234d, 0x21e2, 0x2143, 0xae8e, 0xa3ca, 0x2182, 0x226e, 0x22ac,
  0x22c1, 0x220c, 0x2225, 0x2298, 0x233c, 0x70fd, 0x70fd, 0x70fd,
  0x70fd, 0x4076, 0x9e95, 0xa3b9, 0xa3ff, 0x3fb2, 0x86c4, 0xa3c9,
  0x23e8, 0x2403, 0x7660, 0x17dd, 0x7563, 0x7552, 0x7551, 0x5eba,
  0xe09c, 0x770e, 0x2499, 0x24e4, 0x77f3, 0x2521, 0x70fd, 0x197a,
  0x438c, 0xa8cc, 0x7ae0, 0xae2c, 0x7fa4, 0x3c11, 0x7cdd, 0x70fd,
  0x11de, 0x4699, 0x4614, 0x4656, 0x4598, 0x1e4e, 0xaf3c, 0xa5cd,
  0x2610, 0x08c4, 0x440f, 0xa609, 0x27f4, 0x7405, 0x270d, 0x7a86,
  0x295d, 0x635d, 0x67f4, 0x6466, 0x2a1d, 0x29cd, 0x29bf, 0x097e,
  0x2b0b, 0x2cd9, 0xe046, 0x853a, 0x85af, 0x8550, 0x2c6c, 0x2bf8,
  0x638f, 0x2cbe, 0x2d0f, 0x2c52, 0xba06, 0x8833, 0x86a1, 0xa7d5,
  0x0a35, 0x3f45, 0x4643, 0x2c61, 0xbe03,
  /* 0x93 */
  0x2cc1, 0xe1d7, 0x74d3, 0x64e0, 0x468c, 0x81c3, 0x4305, 0x1c72,
  0x6508, 0xdffb, 0x64bd, 0x0ae0, 0x2e5e, 0x2ede, 0x309e, 0x3088,
  0x87d6, 0x87bc, 0xa1ee, 0x310d, 0x2d8f, 0x8743, 0x8744, 0x0a41,
  0x86e0, 0x0a1d, 0x853e, 0x0a13, 0x532a, 0xe047, 0x8a7a, 0x9db2,
  0xdfa8, 0x314c, 0x314e, 0x8767, 0x85eb, 0x2cab, 0x857b, 0x2d84,
  0x57d4, 0xd17d, 0x59e0, 0x32be, 0x9360, 0x912b, 0x321b, 0x11b8,
  0x90ef, 0x90fe, 0xe448, 0xccb3, 0x3400, 0x713e, 0x7146, 0x911b,
  0x33d9, 0x33d8, 0x32b5, 0x969b, 0x9707, 0xabd4, 0xe50d, 0x36b1,
  0xab65, 0x95aa, 0x0d69, 0x9562, 0x9d90, 0xe86a, 0x9878, 0x3577,
  0xf09b, 0x36f6, 0x3571, 0x3611, 0xae63, 0x9767, 0x34e4, 0x96a1,
  0x367b, 0x37ab, 0x3bb2, 0x0f34, 0x9a0e, 0x9c2d, 0xae09, 0x9c2b,
  0x274d, 0x9c60, 0x8719, 0xe5ff, 0x390e, 0x9c09, 0x9b33, 0x0e20,
  0x1490, 0xb704, 0xe122, 0xb70f, 0x853c, 0xb6db, 0xb625, 0x4aee,
  0x9a83, 0x866a, 0x9bb4, 0x07a4, 0x9b8f, 0x9b02, 0xa809, 0x38ce,
  0xca64, 0x38ab, 0x1e53, 0x3870, 0xd370, 0x8aab, 0x38a3, 0x9c52,
  0x9c61, 0x385e, 0xb972, 0x0c11, 0xa13a, 0xa0bc, 0x0ed9, 0xa0a2,
  0x9d3e, 0x46d0, 0x0ed6, 0x39cf, 0xab2a, 0x3af6, 0x3a5e, 0x0f04,
  0x3cfe, 0x0ba3, 0xa0d5, 0xa9c7, 0x3c73, 0x3c92, 0x3d09, 0x3d45,
  0x8752, 0x3751, 0x3ad8, 0x3c8c, 0x0f17, 0x3cba, 0x3d97, 0xa500,
  0xa25a, 0x64a0, 0x63f2, 0xe1ea, 0xe0cb,
  /* 0x94 */
  0x6389, 0xd8de, 0xdfdc, 0x6567, 0x3fe5, 0x410b, 0xae6c, 0x70fd,
  0xa4bf, 0x108f, 0x4004, 0xa57c, 0x2c7d, 0x400a, 0x2a87, 0x3f64,
  0x4a42, 0x433b, 0xa85a, 0xa7b6, 0x42f1, 0x4450, 0x4487, 0x4494,
  0xac4f, 0xac25, 0x23b9, 0xaf24, 0x766b, 0x467a, 0xa438, 0x9f5f,
  0xaeca, 0xad97, 0xbf21, 0x1206, 0x11b1, 0xaf5f, 0xe223, 0xa475,
  0x32e7, 0x11f3, 0x46cc, 0x463c, 0x6487, 0x4637, 0x179f, 0xcb2a,
  0x1851, 0x4783, 0x1263, 0xb099, 0xb0c6, 0x1258, 0x4755, 0x4873,
  0xf0c6, 0x0e59, 0x4668, 0xe0cc, 0xae2b, 0xae0e, 0x0e3b, 0x10cd,
  0xaece, 0x11ff, 0xae45, 0xad73, 0x62fa, 0x2972, 0x6442, 0xe0e3,
  0x86a4, 0x231f, 0xb4e1, 0xb4a7, 0x4978, 0x9bb2, 0x490e, 0x490f,
  0x497b, 0xab97, 0xa081, 0x0d9e, 0xad70, 0x4638, 0x469b, 0x11bf,
  0xaf3a, 0xaf47, 0x13c8, 0xaf16, 0xc0ae, 0x6407, 0xb701, 0x4a1e,
  0x4a8d, 0x4a88, 0x4ad2, 0x45d0, 0x4b59, 0xd281, 0xb863, 0x140e,
  0x70fd, 0x5696, 0x4ba5, 0x3c6d, 0xa43a, 0x4c3a, 0x4bf4, 0x146e,
  0x8526, 0x1432, 0x6335, 0x4bf1, 0x7c0c, 0xae0c, 0x7359, 0xa33a,
  0x85ae, 0x08d7, 0x27ab, 0x08b0, 0x09ea, 0x7294, 0x7acd, 0x4ce2,
  0x2c99, 0x91f5, 0xbaef, 0xbadc, 0x2c4d, 0x731b, 0x4af0, 0x2c6a,
  0xbbc6, 0x4cfe, 0x14f9, 0x4e5d, 0x4e6d, 0x1511, 0xbbb3, 0xbe3c,
  0xbe26, 0x4ecd, 0xae79, 0x85f0, 0x4e8e, 0x4e7c, 0x4eae, 0x3cf2,
  0x4fdc, 0x5007, 0x4fd3, 0x514e, 0xc121,
  /* 0x95 */
  0xc05c, 0xd648, 0x4f97, 0xbe02, 0x156a, 0xc8b5, 0x7856, 0x3a16,
  0x714e, 0x9ecf, 0x2a04, 0xc292, 0xc278, 0xade2, 0x51dd, 0x4d27,
  0x77ac, 0xbb29, 0xbd43, 0x4d0c, 0xbd8e, 0x6ae6, 0x5805, 0x6b63,
  0x3c5c, 0x9d7f, 0x0d22, 0xae77, 0xc3ed, 0x6b1f, 0xc3e0, 0x5680,
  0xce67, 0xca11, 0x17ea, 0x5337, 0x1702, 0x52c6, 0x5309, 0x5342,
  0xc574, 0x69c3, 0xc802, 0x5462, 0x5465, 0xc811, 0x5653, 0xcae7,
  0x57d0, 0xcf1b, 0x2cc6, 0x147f, 0x8680, 0x2d6b, 0x86e1, 0x2d24,
  0x8718, 0x5860, 0xf2fc, 0xa30f, 0x59ad, 0xd022, 0x2c42, 0x59ee,
  0x2185, 0x5a07, 0x5a3f, 0x5a66, 0x5ae5, 0x5acd, 0xb803, 0x5ad4,
  0xd2c5, 0xd2c4, 0xe1f5, 0xe1d9, 0xe19c, 0xdff9, 0x11ad, 0x56a3,
  0x19f5, 0x19cf, 0x0b32, 0x5bbd, 0x5b9c, 0xe608, 0x318d, 0x632b,
  0xa7c4, 0x3814, 0x4329, 0x42c4, 0x8685, 0x6ded, 0x5ddf, 0x5e29,
  0xd7dc, 0x2bda, 0x49c3, 0x2c30, 0x166e, 0x0a14, 0x5f6a, 0x5fe7,
  0xb009, 0x6070, 0x608a, 0x15f4, 0x3e98, 0x41bb, 0x8ee1, 0x1b9b,
  0x4179, 0x408b, 0x861a, 0x6ce9, 0x09f5, 0xadaf, 0x61fb, 0x70fd,
  0x27b1, 0x1c06, 0x62bb, 0x6504, 0xe04b, 0x1362, 0xe0fc, 0x6527,
  0xe21d, 0xe23b, 0x56e5, 0x5bab, 0x6699, 0x66a7, 0x6697, 0x6696,
  0xe2b4, 0x4645, 0x11c2, 0xad7f, 0xaec2, 0xa92a, 0x11e7, 0x9ba5,
  0x678f, 0xe3e7, 0xe366, 0xe365, 0x11cc, 0xae6d, 0xaef8, 0xa52e,
  0x4612, 0x466b, 0x11fc, 0x6841, 0xe470,
  /* 0x96 */
  0x3a87, 0x1d1d, 0xe453, 0xb91f, 0x70fd, 0x6468, 0xdf89, 0xe226,
  0xe12f, 0xc23e, 0x63ba, 0x2d51, 0x5ce9, 0x1c3c, 0x45f9, 0xa75b,
  0x689b, 0x6871, 0x6a38, 0x7de6, 0x3001, 0xe1c5, 0xaf32, 0x691f,
  0xe65a, 0x63f6, 0xe6d7, 0x62e5, 0x17c0, 0xe150, 0xaee7, 0xe164,
  0x69dc, 0xe045, 0x1200, 0x632a, 0x1c25, 0x5614, 0x6a3b, 0x6a4d,
  0xd606, 0x10fd, 0x6a9b, 0x1e2f, 0x6aaa, 0x6b5c, 0xe165, 0xb988,
  0x3ccf, 0x6b21, 0x2d3e, 0x6b2f, 0xe871, 0x1e50, 0xe8c8, 0x6abc,
  0x1e7d, 0x1e57, 0x647d, 0x2ab2, 0x81c2, 0x2a62, 0xae38, 0x83a8,
  0x4a44, 0x921f, 0xa338, 0x3b05, 0x107d, 0x6558, 0x0c67, 0x3390,
  0x9281, 0x946b, 0x3347, 0x6d4f, 0x6d53, 0x6d7b, 0x6d35, 0x6d10,
  0x6c7f, 0x6ccf, 0xebed, 0x6c9f, 0xef35, 0xee3e, 0x6da1, 0x1f6e,
  0xa644, 0x6e98, 0x1f70, 0x6d8c, 0xeef4, 0xee2d, 0xee33, 0xe8af,
  0x6e25, 0x56bd, 0xcb52, 0xcd1f, 0xc8c2, 0x57bc, 0x1833, 0xcae4,
  0xcbc4, 0xcb30, 0x5620, 0x57ae, 0xcb40, 0xa0d7, 0xbfa4, 0x4be2,
  0x9e9c, 0x9f40, 0x1c2d, 0xae5e, 0x1062, 0x64db, 0x63be, 0x6448,
  0x737f, 0x4ab9, 0x6377, 0x654d, 0x2224, 0x0780, 0x61a4, 0xb6dd,
  0x4a3d, 0x4a54, 0x4ab6, 0x4a4b, 0x8597, 0x9b49, 0xadc1, 0x09da,
  0x21b2, 0x41da, 0x41d9, 0x70fd, 0x421e, 0x2654, 0xa6f5, 0x29fb,
  0x2b33, 0x29ca, 0x6d96, 0x2a17, 0x4334, 0x07ef, 0xa6ec, 0x43eb,
  0xc980, 0xb2d7, 0x70fd, 0x85f5, 0xe049,
  /* 0x97 */
  0x334c, 0x2d0e, 0x2c4b, 0x15bc, 0xa9c8, 0x0a6c, 0x1e3b, 0xdffc,
  0x64bb, 0x64b8, 0x8716, 0x7ddd, 0x5672, 0xca00, 0x82d3, 0xa83a,
  0x9626, 0xa901, 0x2bd4, 0xa337, 0xc501, 0x4fa8, 0xc05b, 0xbfe4,
  0xae39, 0xae0b, 0xdffa, 0x63e5, 0x45e2, 0x11e9, 0x46b4, 0xe163,
  0x86df, 0x11e1, 0xaf33, 0x3d18, 0x45f3, 0x45fb, 0x11d6, 0xaebe,
  0xaf14, 0x8619, 0xaee6, 0x7467, 0x82e4, 0xae65, 0x4648, 0xad96,
  0x42a5, 0xadf6, 0x6384, 0x45e6, 0x645f, 0x75be, 0x6431, 0xe0ce,
  0xe016, 0x6486, 0xe1e7, 0xb755, 0x1c35, 0xe082, 0x436b, 0xadc3,
  0x7bbf, 0x28e4, 0x769a, 0x7aab, 0x7b78, 0x2742, 0x4bc4, 0x863a,
  0x4ffe, 0x8502, 0xaed0, 0x8692, 0x182e, 0x6501, 0x0a4a, 0xe0c0,
  0xae2d, 0x2bf0, 0x86ff, 0x86c3, 0xd084, 0x2ce1, 0x0a22, 0xa2db,
  0x09f0, 0x635f, 0x2cb9, 0xe081, 0x86a2, 0x6474, 0x0fcd, 0x79b4,
  0x1d56, 0x0cca, 0x2334, 0x1069, 0x104c, 0x825c, 0x4375, 0x15fb,
  0xda98, 0x400f, 0xe4a4, 0x17eb, 0x3f97, 0xd80f, 0x4267, 0x3eef,
  0x0fd6, 0xa52d, 0xa3ad, 0x4002, 0x410c, 0x106f, 0x74b5, 0x4751,
  0x09fc, 0x0808, 0x1980, 0x11da, 0x1b71, 0x2c04, 0x636e, 0x1c3e,
  0x6081, 0xe21c, 0xc900, 0x2a52, 0x2a08, 0x0a16, 0x8312, 0x42fe,
  0xa8a2, 0xaef1, 0x9963, 0x8170, 0x8f3d, 0x3af9, 0x3ba7, 0x8258,
  0x9de5, 0xd6b4, 0x9fdf, 0xe083, 0xa0d6, 0x9d3a, 0xb05f, 0x3c5a,
  0x9fad, 0xc9f7, 0x563e, 0x17df, 0x17ce,
  /* 0x98 */
  0xca66, 0xca91, 0xc9c2, 0xcd1e, 0x4117, 0xce49, 0x553d, 0x857a,
  0x55ed, 0xc9c0, 0xcd93, 0x861b, 0x2bc9, 0x2cc2, 0x85f3, 0x2ca1,
  0x2cb1, 0x8745, 0xa73c, 0x0a6d, 0x2c2f, 0x857c, 0x0a07, 0x438e,
  0x6490, 0x38da, 0xa7e5, 0x2cae, 0x2c6b, 0xa7d3, 0x3c6b, 0xa439,
  0xd276, 0xa99b, 0xa80a, 0x431d, 0xa799, 0xdfe1, 0x21f0, 0xca68,
  0x2f02, 0xaa0e, 0xd80d, 0xa8fd, 0x3c4c, 0xa796, 0x81c4, 0x42a6,
  0x4333, 0xa929, 0x10a5, 0x3f1f, 0xf1e5, 0xaecf, 0x5065, 0x2c2b,
  0x2f6f, 0x10f3, 0x319c, 0xaedd, 0x861f, 0x4fa4, 0x5626, 0x26c5,
  0xa1fa, 0x9c80, 0x72d4, 0x29be, 0x7815, 0x7699, 0x12e5, 0x8c1e,
  0xefb6, 0x4203, 0xe51b, 0x2fb0, 0x458f, 0x4ed3, 0xe059, 0xe5e0,
  0x2208, 0x51e7, 0x44cd, 0x4510, 0xd5f4, 0x4538, 0x4539, 0xb876,
  0x4541, 0x4548, 0x11a9, 0xd618, 0x61ac, 0x43f5, 0xad72, 0x45e1,
  0x53f6, 0x11ca, 0x490c, 0x11d1, 0x3ee2, 0x293d, 0x4619, 0x461e,
  0x461f, 0x11e2, 0x11f0, 0x11f4, 0x11fa, 0x46d3, 0x120e, 0x1253,
  0x4742, 0x476d, 0x4772, 0x478d, 0x127c, 0x47c8, 0x47dc, 0x12c0,
  0x484d, 0x12d7, 0x4874, 0x12dc, 0x487a, 0xb29c, 0x4388, 0x2863,
  0x5b00, 0x2aa9, 0x131d, 0x4943, 0x1339, 0x39a1, 0x1345, 0x091b,
  0x4998, 0x136a, 0x136f, 0x2e9e, 0x49be, 0x49cb, 0x2b32, 0x4a18,
  0x42b9, 0x4a1c, 0x13a8, 0x4a39, 0x4a47, 0x4a51, 0x4a66, 0x5648,
  0xb6b5, 0x4b33, 0x3a43, 0x4b32, 0x1403,
  /* 0x99 */
  0x1409, 0x4b91, 0x4b99, 0x60fb, 0x4c06, 0x60fc, 0x1467, 0x4c91,
  0x14b2, 0x4cbc, 0x5479, 0x14c4, 0x4ccf, 0x4cdb, 0x14cf, 0x2061,
  0x4d62, 0x4d6c, 0x4d7b, 0x4e12, 0x4e1b, 0x1560, 0x157a, 0x4e7b,
  0x4e9c, 0x158c, 0x4eb8, 0x1594, 0x4eed, 0x60d3, 0x42c0, 0x7b8f,
  0x4fcf, 0x4fd4, 0x4fd0, 0x4ffd, 0x51ae, 0x51b4, 0x449f, 0x1697,
  0x5220, 0x5225, 0x4d39, 0x522e, 0x5231, 0x5254, 0x10cc, 0x29f4,
  0x42a0, 0x52b7, 0x52e9, 0x16ed, 0x530c, 0x452a, 0x530e, 0x5312,
  0x4760, 0x5314, 0x1701, 0x0e79, 0x5356, 0x5359, 0x535a, 0x1713,
  0x2a7a, 0x537c, 0x5384, 0x1725, 0x5393, 0x172d, 0x53a5, 0x2a2f,
  0x53c1, 0x53e4, 0x5454, 0x178f, 0x54a6, 0x5476, 0x54ca, 0x54d8,
  0x54ff, 0x17b0, 0x5557, 0x6769, 0x3bca, 0x5605, 0x42f5, 0x5664,
  0x3323, 0x5688, 0x1804, 0x56be, 0x56e1, 0x56f8, 0x5710, 0x5738,
  0x5752, 0x183b, 0x576f, 0x5770, 0x57a0, 0x1877, 0x5832, 0x5852,
  0x5872, 0x58af, 0x6745, 0x590b, 0x1906, 0x1917, 0x5a2e, 0x5a7f,
  0x5aa4, 0x5ac7, 0x5b11, 0xd467, 0x5ba9, 0x5bb8, 0x5c14, 0x5c34,
  0x5d91, 0x5e14, 0x5e32, 0x5e5c, 0x1a98, 0x2a9f, 0x5f03, 0x1aed,
  0x212e, 0x5f7a, 0x2818, 0x2994, 0x5fb1, 0x2835, 0x5ff0, 0x1b37,
  0x600e, 0x6022, 0x6024, 0x602d, 0x6032, 0x60f7, 0x6101, 0x610a,
  0x610c, 0x6173, 0x6ac4, 0x1bad, 0x69e0, 0x6313, 0x1c1e, 0x6328,
  0x6358, 0x636b, 0x63b1, 0x63ae, 0x63bf,
  /* 0x9a */
  0x63e3, 0x63eb, 0x63f3, 0x63f4, 0x63fd, 0x6443, 0x6484, 0x64ad,
  0x1c45, 0x1c51, 0x6f3f, 0x6517, 0x2541, 0x651d, 0x652d, 0x653e,
  0x1c6a, 0x6554, 0x6579, 0x662d, 0x66a2, 0x1ca7, 0x66f4, 0x6733,
  0x1ce5, 0x39e0, 0x1d24, 0x6840, 0x1d35, 0x68b2, 0x68c2, 0x2894,
  0x1da4, 0x3328, 0x69b9, 0x1dd9, 0x69f1, 0x2a84, 0x6a0e, 0x6a19,
  0x23f4, 0x6a1c, 0x6a37, 0x6a42, 0x6a5d, 0x6a62, 0x1e30, 0x6ac5,
  0x1e5d, 0x6b3c, 0x6c0f, 0x4c83, 0x6c69, 0x6c81, 0x6cdd, 0x6cf1,
  0x6cf4, 0x1f2d, 0x6d20, 0x0aaf, 0x8902, 0x6dc9, 0x6d3a, 0x6f7e,
  0x2890, 0x6e13, 0x6e3d, 0x6e40, 0x6e7c, 0x65f6, 0x60f6, 0x6efb,
  0x6f2c, 0x6f31, 0x6f3d, 0x6f46, 0x65dc, 0x6f62, 0x6f71, 0x6f78,
  0x4cc8, 0x6fc4, 0x7194, 0x7377, 0x7460, 0x3b5a, 0x65c3, 0x2bec,
  0x7597, 0x2a80, 0x65c1, 0x0af9, 0x7655, 0x7695, 0x76f6, 0x84fa,
  0x2997, 0x4373, 0x79c2, 0x79cd, 0x7a7f, 0x26aa, 0xf1fb, 0x7a8b,
  0x26de, 0x7abb, 0x7afb, 0x7b13, 0x7b25, 0x7b3c, 0x3327, 0x7b4d,
  0x28ba, 0x7b75, 0x7b9d, 0x7bad, 0x7c2f, 0x7c72, 0x7c88, 0x3b95,
  0x6d2f, 0x5925, 0x7cc4, 0x7cce, 0x7d97, 0x7e50, 0x7ded, 0x7d33,
  0x2e60, 0x7e7c, 0x304b, 0x7f1c, 0x7f0f, 0x7f36, 0x395e, 0x7f3b,
  0x7f48, 0x7f56, 0x0987, 0x7f7f, 0x7f93, 0x7fef, 0x7ffb, 0x25a4,
  0x56ad, 0x81a3, 0x8235, 0x81f6, 0x5d01, 0x83f7, 0x8459, 0x8603,
  0x8607, 0x20b8, 0x42bb, 0x866d, 0x87aa,
  /* 0x9b */
  0x886d, 0x8885, 0x896a, 0x89b0, 0x89ec, 0x8b48, 0x3503, 0x8b55,
  0x8b95, 0x4398, 0x3a95, 0x8c85, 0x3c29, 0x0a08, 0x8ebc, 0x8f57,
  0x8f7a, 0x922a, 0x9371, 0x944f, 0x54fd, 0x9467, 0x9493, 0x9515,
  0x5b25, 0x9528, 0x60e0, 0x954e, 0x68b8, 0x957f, 0x6947, 0x6bbd,
  0x964c, 0x70fd, 0x9688, 0x96b7, 0xbbe8, 0x9708, 0x9712, 0x97b7,
  0x9795, 0x9842, 0x9934, 0x994c, 0x99b3, 0x99e6, 0x9c9f, 0x9d1e,
  0x31f1, 0x3888, 0x38ff, 0xd579, 0x9e67, 0x9ef3, 0x70fd, 0xae3a,
  0x70fd, 0x9f1a, 0xa016, 0x70fd, 0x7406, 0x2af5, 0x394e, 0x3b58,
  0xa1a7, 0xd1d7, 0xbf22, 0xa391, 0xa3f9, 0xd17e, 0x8cda, 0x1bd0,
  0x1d78, 0xa659, 0xe09a, 0xa82e, 0xa84d, 0xa57b, 0xa874, 0xa8d6,
  0xaec5, 0x764a, 0x2412, 0x7691, 0x2bdf, 0x8b28, 0x0efe, 0xa2bf,
  0xa944, 0xaa16, 0x29c8, 0xab74, 0x0cdb, 0xac2f, 0xdda8, 0xe949,
  0x0aa2, 0x8e03, 0x5cde, 0xe14e, 0x70fd, 0xacd2, 0xad7b, 0xae95,
  0x4409, 0xaf40, 0x7b38, 0x2ba5, 0xb225, 0xb2c6, 0x7779, 0x601a,
  0xb36c, 0x24cf, 0x297f, 0x4371, 0xb559, 0xb619, 0xa48a, 0xaf27,
  0x27fc, 0xb646, 0xb66e, 0xc892, 0x70fd, 0x07b3, 0xb6bf, 0xd1b2,
  0xb6de, 0x1a18, 0xb6e2, 0xb6e6, 0xb907, 0xadbf, 0xb95d, 0x22a6,
  0x083b, 0x9d0c, 0x70fd, 0xb9c3, 0x1a7c, 0xdf48, 0xbaee, 0xbb89,
  0xbc06, 0x8a50, 0x29e1, 0x4351, 0x70fd, 0xc002, 0xd6d2, 0x6196,
  0xc0b2, 0xb2da, 0x5ce2, 0xc282, 0xc2ca,
  /* 0x9c */
  0x2f9b, 0xc937, 0x70fd, 0xc304, 0x875c, 0x5c6a, 0xae76, 0xc308,
  0xa52f, 0xc352, 0x1e80, 0xc3ff, 0xc475, 0x921b, 0x6565, 0xb921,
  0x33d5, 0x2c67, 0xf28d, 0x70fd, 0x28f9, 0xaa61, 0xc4bc, 0x20aa,
  0xadb4, 0x6756, 0x3fcf, 0xc9fd, 0x0958, 0x5af7, 0xc559, 0xc62e,
  0xc291, 0xc61e, 0x70fd, 0xc687, 0x4d42, 0xe480, 0x7911, 0xc6e6,
  0x70fd, 0xc6f9, 0x4c45, 0x70fd, 0xc7ba, 0x6b26, 0xc7ed, 0x099f,
  0xc2e9, 0x7121, 0x4b83, 0xc7f4, 0xc89b, 0x2f6c, 0x9e19, 0x70fd,
  0xc8dd, 0x19d0, 0xc9e4, 0x473b, 0x59e5, 0xcaee, 0x2af6, 0x0a5c,
  0x90cd, 0xcd8b, 0xcecd, 0x0f94, 0xcf40, 0xcf45, 0x6381, 0x8dba,
  0xcf4b, 0x6430, 0xcfa6, 0xae50, 0x3e79, 0x659f, 0xd050, 0x7db8,
  0x59a7, 0x5a75, 0x9426, 0xdcb3, 0x85f1, 0x40b8, 0xaeaa, 0x8660,
  0x0ce4, 0x09f9, 0x70fd, 0x70fd, 0x183f, 0x38f6, 0xeaad, 0xe824,
  0x5ac3, 0xd30c, 0xd358, 0x2916, 0x13df, 0x844a, 0x0ce1, 0xa02f,
  0xd9a8, 0x8285, 0x43ad, 0x5566, 0xd4dd, 0xe568, 0x70fd, 0x1f77,
  0xcdef, 0xdfab, 0xd4fd, 0xd50a, 0xd60b, 0xd766, 0xa6ba, 0x4d43,
  0x4b7e, 0xd8c9, 0x41b5, 0xefdf, 0x3c43, 0xdb98, 0x25e2, 0xcb47,
  0x64bf, 0x3a76, 0x685d, 0xda2f, 0xd8e3, 0xc775, 0x82ad, 0x9baf,
  0xd908, 0x2fc5, 0xe230, 0xd943, 0x2955, 0x6923, 0xdf49, 0x2feb,
  0xae08, 0x37fe, 0x3c15, 0x2612, 0xaf25, 0xa4c1, 0x0f51, 0x3976,
  0xd950, 0xd9b4, 0xd9ee, 0x8c21, 0xda0f,
  /* 0x9d */
  0xda49, 0xda6f, 0xda9a, 0xdb86, 0xdbaf, 0xdc0a, 0x090a, 0xdca8,
  0xde2a, 0x1bfa, 0x3626, 0xdf56, 0x4a08, 0x6355, 0xdfb8, 0x16f2,
  0xdfe7, 0x16df, 0xdfe8, 0xe146, 0xe1d4, 0x2c38, 0xe209, 0x70fd,
  0xe405, 0xe4ac, 0x70fd, 0xe4d0, 0xe4fc, 0x10f7, 0xe51e, 0xaf4a,
  0x6110, 0x448f, 0x28cb, 0xe667, 0xe6e9, 0xe6b0, 0xe6b8, 0xe732,
  0xe851, 0xe8c9, 0xe8ea, 0xe943, 0xe9a8, 0xea0e, 0xeb1a, 0xeb5b,
  0x509f, 0xecb8, 0xece3, 0x1f64, 0x6647, 0xef93, 0x43a2, 0xefff,
  0x2011, 0x6152, 0xf0cb, 0x201c, 0x7b5c, 0x60fe, 0x2801, 0x60fa,
  0x8ff0, 0x60f9, 0xaf13, 0x1809, 0x507f, 0x4156, 0x3cf1, 0x212a,
  0x0824, 0xe12c, 0xd39d, 0x0a7a, 0x5fc0, 0x8635, 0xd8e4, 0xe16c,
  0xe199, 0xd53e, 0xc46f, 0x10eb, 0xd1d5, 0xa277, 0xb7b5, 0xba16,
  0x20da, 0xbe01, 0xc118, 0x28ff, 0x7d2d, 0x5f4e, 0x2dad, 0xa3c8,
  0xb05e, 0x361e, 0x70fd, 0x8636, 0x86bb, 0x3770, 0x286d, 0xbc4a,
  0x265a, 0xb5d1, 0x10c6, 0xeb58, 0x1f3d, 0x2862, 0x285e, 0x5149,
  0xbe58, 0x2bb5, 0xa2c0, 0x58f0, 0x205c, 0x7eaa, 0x7c09, 0x9fba,
  0x5317, 0x6dde, 0x5e58, 0x483b, 0x6d45, 0x484e, 0x49b9, 0x6445,
  0x2672, 0x5348, 0x54f7, 0x2865, 0x5332, 0x5618, 0x52bd, 0x282a,
  0x4b62, 0x2883, 0x2656, 0x7d5d, 0x090e, 0x2845, 0x2831, 0x3931,
  0xdb62, 0x096d, 0x4734, 0x2830, 0x27fa, 0x26d7, 0x27b2, 0x7b01,
  0x7b56, 0x3110, 0xb448, 0x7d36, 0x9662,
  /* 0x9e */
  0x7d62, 0x6f2b, 0x4f5a, 0x281e, 0x7f35, 0x34dd, 0x686d, 0x26d4,
  0x5e0d, 0x43f6, 0x6276, 0x363c, 0x35f9, 0x363e, 0x27a9, 0x9583,
  0x6d72, 0x98b3, 0x23da, 0x081f, 0x7c67, 0x23e7, 0x268d, 0x275e,
  0x2753, 0x4866, 0x5f6d, 0xc64a, 0x47b1, 0x52b6, 0x5984, 0x5906,
  0x5a47, 0x53b6, 0x561c, 0x7f81, 0x17ec, 0x4504, 0xac06, 0x2dd0,
  0x550b, 0xc653, 0x28bb, 0x91f4, 0xd72f, 0xa6e3, 0xd773, 0xcc10,
  0xcf76, 0x6270, 0x8099, 0x6308, 0xa2bc, 0xf1a9, 0x7d6c, 0x7db9,
  0x4466, 0x89e2, 0x1a4e, 0xb302, 0xd8b9, 0x7eab, 0x13fa, 0x6d5d,
  0x375f, 0x97a0, 0x1bf3, 0xaca0, 0xeb3c, 0x7eac, 0x7cca, 0x70fd,
  0xd123, 0x7ead, 0x70fd, 0xbf88, 0x8047, 0x43a3, 0x508e, 0x6dd0,
  0x205a, 0x2044, 0x08b7, 0x2d4d, 0x3ef2, 0x25a7, 0x09ec, 0x0d1c,
  0x25bd, 0x09e5, 0xab18, 0x2ada, 0xafae, 0x542d, 0x268b, 0x29ea,
  0xbad5, 0x78b9, 0x70fd, 0x0d92, 0x8fa5, 0x4574, 0xec6c, 0x1fc9,
  0x6ced, 0xa2be, 0xecf0, 0x1f1b, 0xb2e9, 0xe69e, 0xed5e, 0x565c,
  0xa336, 0xcf72, 0xc573, 0xa020, 0x356e, 0x4f25, 0xa437, 0xa3ac,
  0x0d6a, 0x6148, 0x250c, 0x1174, 0x09ba, 0x18e9, 0x754e, 0x4840,
  0x2d30, 0x7d76, 0x4a7a, 0xd86e, 0x2ae7, 0x13bf, 0x28bc, 0x6c8b,
  0x2fb4, 0x4854, 0xf074, 0x6f05, 0x1fa1, 0x70fd, 0x0b3b, 0x3359,
  0x9a5a, 0xa932, 0x70fd, 0x289d, 0x8169, 0x29e7, 0xade3, 0xebc6,
  0x2474, 0xcdee, 0x08ed, 0x70fd, 0x6dfc,
  /* 0x9f */
  0x4e56, 0x6c39, 0x2a1e, 0x85ac, 0x2e93, 0x3713, 0xe650, 0xc1b5,
  0xcea4, 0x586d, 0x7be8, 0xca62, 0xaf62, 0x7c31, 0x70fd, 0x243e,
  0x8b8f, 0x2fce, 0x6803, 0x8a91, 0x6f01, 0x618c, 0x4d1f, 0x6c02,
  0x2f11, 0x4da3, 0x34a8, 0x3575, 0x6bff, 0x4dcf, 0x6c2a, 0x4e7e,
  0x70fd, 0x4e42, 0x4e86, 0x6d15, 0x4dfc, 0x6c09, 0x70fd, 0x6d1b,
  0xadbe, 0x6fda, 0x27b3, 0x2e03, 0x223d, 0x6f18, 0x2232, 0x24a0,
  0x1106, 0x2511, 0x29a7, 0x2296, 0x2bf7, 0x3052, 0x68c8, 0x6e2b,
  0x609c, 0x26a9, 0x68b4, 0x6a40, 0x68ba, 0x256c, 0x3370, 0x3b6c,
  0x261a, 0x6d0a, 0x6d82, 0x1efb, 0x6741, 0x3bc0, 0x22e6, 0x4746,
  0x85ad, 0x6ada, 0x24b3, 0x70fd, 0x6259, 0x6781, 0x625c, 0x70fd,
  0x6251, 0xe397, 0x35bf, 0xca63, 0x3d0a, 0x2851, 0x628e, 0x477a,
  0x34c5, 0x74bc, 0x454f, 0x4e70, 0xbc21, 0xa2bd, 0x70fd, 0xad99,
  0x48d6, 0x6c9d, 0x206a, 0x7b94, 0x55be, 0x59c2, 0x70fd, 0x2e8a,
  0x3c00, 0x70fd, 0x29ba, 0x245f, 0x3035, 0x210e, 0x3e71, 0x72b2,
  0x2179, 0x26dc, 0x271a, 0x24da, 0x5ec2, 0x093e, 0x70fd, 0x0933,
  0x70fd, 0x3d92, 0x627c, 0x7025, 0x6c97, 0x692e, 0x69b4, 0x6bba,
  0x6f28, 0x6f04, 0x437a, 0x4d14, 0x70fd, 0x3e3a, 0x5998, 0x5178,
  0x70fd, 0x2860, 0xf14a, 0x5fb7, 0x6fd3, 0x70fd, 0x5f14, 0x5f8f,
  0x6e9c, 0x5f41, 0x34c2, 0xdbfd, 0x5f68, 0x5fb5, 0x4cd3, 0xaef7,
  0x4c3e, 0x4ad8, 0x3f2a, 0x5be7, 0x4807,
  /* 0xa0 */
  0xe05a, 0x6fa6, 0x3f0e, 0x5956, 0x47c3, 0xefb2, 0x4a53, 0xf200,
  0x5e4c, 0x44e2, 0x4571, 0x5cad, 0x4502, 0x46f1, 0x5e2b, 0xaf3b,
  0x57ef, 0x31fa, 0x5a20, 0x17b7, 0x70fd, 0x867b, 0xcb45, 0x70fd,
  0x5bfe, 0x905b, 0x70fd, 0x333d, 0x4867, 0x6bd7, 0x6dc4, 0x646e,
  0x6c8f, 0x5975, 0x70fd, 0x70fd, 0x5e37, 0x452c, 0x6821, 0x6cb0,
  0x0916, 0x44b2, 0x1ec7, 0x4e51, 0x6a4a, 0xc059, 0x3399, 0x1ec4,
  0x6f16, 0x33bd, 0x70fd, 0x299f, 0x33af, 0x34e6, 0x3479, 0x70fd,
  0x0d9c, 0x3422, 0x25ea, 0x9d35, 0x35a4, 0x3a42, 0x0912, 0x2f97,
  0xe1c2, 0x611a, 0xe339, 0x70fd, 0x2319, 0x8a06, 0x4b06, 0x2572,
  0x6738, 0x7dfb, 0x1365, 0x70fd, 0x49fe, 0x70fd, 0x4ec2, 0xbe9a,
  0x4eda, 0x4c2d, 0x5266, 0x5263, 0x4f4d, 0x4705, 0x46f2, 0x5b14,
  0x541a, 0x394c, 0x5262, 0xd086, 0x525b, 0x46f0, 0x5303, 0x4924,
  0x5b09, 0xc58c, 0x4753, 0xcc11, 0x5929, 0x594e, 0x53c8, 0x590c,
  0x5bc9, 0x5ded, 0x5cc3, 0x492b, 0x46f8, 0x56da, 0x0975, 0x3bf2,
  0x5ee6, 0x70fd, 0x5b29, 0x70fd, 0x3ff9, 0x5941, 0xa551, 0x46e7,
  0x10db, 0x4376, 0x32e4, 0x33dc, 0x0fd1, 0x70fd, 0x32b7, 0x70fd,
  0x5171, 0xe12d, 0x70fd, 0x3329, 0x1e3e, 0x2460, 0x0f58, 0xa287,
  0xbe57, 0xd1d6, 0xb6b1, 0x8784, 0x81be, 0xe883, 0xcb1c, 0xcded,
  0x2f01, 0xc0ad, 0xe00f, 0x9f77, 0x71ee, 0xc606, 0xb24e, 0x1862,
  0x2d5f, 0xc1cc, 0x6fd0, 0x6f26, 0xc12b,
};
static const unsigned short hkscs1999_2uni_pagec6[471] = {
  /* 0xc6 */
  0x70fd, 0x70fd, 0x70fd, 0x70fd, 0x70fd, 0x70fd, 0x70fd, 0x70fd,
  0x70fd, 0x70fd, 0x70fd, 0x70fd, 0x70fd, 0x70fd, 0x70fd, 0x70fd,
  0x70fd, 0x70fd, 0x70fd, 0x70fd, 0x70fd, 0x70fd, 0x70fd, 0x70fd,
  0x70fd, 0x70fd, 0x70fd, 0x70fd, 0x70fd, 0x70fd, 0x70fd, 0x70fd,
  0x70fd, 0x70fd, 0x70fd, 0x70fd, 0x70fd, 0x70fd, 0x70fd, 0x70fd,
  0x70fd, 0x70fd, 0x70fd, 0x70fd, 0x70fd, 0x70fd, 0x70fd, 0x70fd,
  0x70fd, 0x70fd, 0x70fd, 0x70fd, 0x70fd, 0x70fd, 0x70fd, 0x70fd,
  0x70fd, 0x70fd, 0x70fd, 0x70fd, 0x70fd, 0x70fd, 0x70fd, 0x0460,
  0x0461, 0x0462, 0x0463, 0x0464, 0x0465, 0x0466, 0x0467, 0x0468,
  0x0469, 0x0474, 0x0475, 0x0476, 0x0477, 0x0478, 0x0479, 0x047a,
  0x047b, 0x047c, 0x047d, 0x0370, 0x0371, 0x0372, 0x0373, 0x0374,
  0x0375, 0x0376, 0x0377, 0x0378, 0x0379, 0x2076, 0x207f, 0x20c5,
  0x20e0, 0x23c2, 0x23d6, 0x23eb, 0x2539, 0x2578, 0x25a9, 0x25f6,
  0x2b4a, 0x2dc0, 0x301b, 0x05b3, 0x30bf, 0x70fd, 0x3190, 0x31a1,
  0x3774, 0x70fd, 0x4792, 0x70fd, 0x60f5, 0x70fd, 0x0028, 0x01c6,
  0x06bd, 0x06be, 0x065d, 0x065e, 0x70fd, 0x70fd, 0x05c5, 0x05c6,
  0x05c7, 0x06bc, 0x70bb, 0x70bd, 0x04fd, 0x0601, 0x0602, 0x0603,
  0x0604, 0x0605, 0x0606, 0x0607, 0x0608, 0x0609, 0x060a, 0x060b,
  0x060c, 0x060d, 0x060e, 0x060f, 0x0610, 0x0611, 0x0612, 0x0613,
  0x0614, 0x0615, 0x0616, 0x0617, 0x0618,
  /* 0xc7 */
  0x0619, 0x061a, 0x061b, 0x061c, 0x061d, 0x061e, 0x061f, 0x0620,
  0x0621, 0x0622, 0x0623, 0x0624, 0x0625, 0x0626, 0x0627, 0x0628,
  0x0629, 0x062a, 0x062b, 0x062c, 0x062d, 0x062e, 0x062f, 0x0630,
  0x0631, 0x0632, 0x0633, 0x0634, 0x0635, 0x0636, 0x0637, 0x0638,
  0x0639, 0x063a, 0x063b, 0x063c, 0x063d, 0x063e, 0x063f, 0x0640,
  0x0641, 0x0642, 0x0643, 0x0644, 0x0645, 0x0646, 0x0647, 0x0648,
  0x0649, 0x064a, 0x064b, 0x064c, 0x064d, 0x064e, 0x064f, 0x0650,
  0x0651, 0x0652, 0x0653, 0x0661, 0x0662, 0x0663, 0x0664, 0x0665,
  0x0666, 0x0667, 0x0668, 0x0669, 0x066a, 0x066b, 0x066c, 0x066d,
  0x066e, 0x066f, 0x0670, 0x0671, 0x0672, 0x0673, 0x0674, 0x0675,
  0x0676, 0x0677, 0x0678, 0x0679, 0x067a, 0x067b, 0x067c, 0x067d,
  0x067e, 0x067f, 0x0680, 0x0681, 0x0682, 0x0683, 0x0684, 0x0685,
  0x0686, 0x0687, 0x0688, 0x0689, 0x068a, 0x068b, 0x068c, 0x068d,
  0x068e, 0x068f, 0x0690, 0x0691, 0x0692, 0x0693, 0x0694, 0x0695,
  0x0696, 0x0697, 0x0698, 0x0699, 0x069a, 0x069b, 0x069c, 0x069d,
  0x069e, 0x069f, 0x06a0, 0x06a1, 0x06a2, 0x06a3, 0x06a4, 0x06a5,
  0x06a6, 0x06a7, 0x06a8, 0x06a9, 0x06aa, 0x06ab, 0x06ac, 0x06ad,
  0x06ae, 0x06af, 0x06b0, 0x06b1, 0x06b2, 0x06b3, 0x06b4, 0x06b5,
  0x06b6, 0x0210, 0x0211, 0x0212, 0x0213, 0x0214, 0x0215, 0x0201,
  0x0216, 0x0217, 0x0218, 0x0219, 0x021a,
  /* 0xc8 */
  0x021b, 0x021c, 0x021d, 0x021e, 0x021f, 0x0220, 0x0221, 0x0222,
  0x0223, 0x0224, 0x0225, 0x0226, 0x0227, 0x0228, 0x0229, 0x022a,
  0x022b, 0x022c, 0x022d, 0x022e, 0x022f, 0x0230, 0x0231, 0x0232,
  0x0233, 0x0234, 0x0235, 0x0251, 0x0236, 0x0237, 0x0238, 0x0239,
  0x023a, 0x023b, 0x023c, 0x023d, 0x023e, 0x023f, 0x0240, 0x0241,
  0x0242, 0x0243, 0x0244, 0x0245, 0x0246, 0x0247, 0x0248, 0x0249,
  0x024a, 0x024b, 0x024c, 0x024d, 0x024e, 0x024f, 0x03e7, 0x03b8,
  0x03b9, 0x06cf, 0x71cc, 0x209a, 0x718a, 0x2442, 0x1791, 0x7030,
  0x23c8, 0x7031, 0xd187, 0x70fd, 0x70fd, 0x70fd, 0x70fd, 0x70fd,
  0x70fd, 0x70fd, 0x70fd, 0x70fd, 0x70fd, 0x70fd, 0x70fd, 0x70fd,
  0x70fd, 0x70fd, 0x70fd, 0x70fd, 0x70fd, 0x70fd, 0x70fd, 0x70fd,
  0x70fd, 0x70fd, 0x70fd, 0x70fd, 0x70fd, 0x70fd, 0x70fd, 0x70fd,
  0x70fd, 0x70fd, 0x70fd, 0x70fd, 0x70fd, 0x70fd, 0x70fd, 0x70fd,
  0x70fd, 0x70fd, 0x70fd, 0x70e2, 0x70e4, 0x7087, 0x7082, 0x0731,
  0x0316, 0x0321, 0x065b, 0x065c, 0x0500, 0x0504, 0x0506, 0x0507,
  0x0508, 0x050a, 0x050c, 0x050d, 0x0515, 0x051c, 0x051d, 0x0525,
  0x0527, 0x052a, 0x052c, 0x052e, 0x0536, 0x053c, 0x053e, 0x0546,
  0x054a, 0x054c, 0x054d, 0x054f, 0x0556, 0x0557, 0x055e, 0x0563,
  0x70fd, 0x70fd, 0x70fd, 0x0183, 0x0150, 0x015b, 0x0154, 0x0175,
  0x00d3, 0x0078, 0x00cb, 0x018a, 0x016a,
};
static const unsigned short hkscs1999_2uni_pagef9[942] = {
  /* 0xf9 */
  0x70fd, 0x70fd, 0x70fd, 0x70fd, 0x70fd, 0x70fd, 0x70fd, 0x70fd,
  0x70fd, 0x70fd, 0x70fd, 0x70fd, 0x70fd, 0x70fd, 0x70fd, 0x70fd,
  0x70fd, 0x70fd, 0x70fd, 0x70fd, 0x70fd, 0x70fd, 0x70fd, 0x70fd,
  0x70fd, 0x70fd, 0x70fd, 0x70fd, 0x70fd, 0x70fd, 0x70fd, 0x70fd,
  0x70fd, 0x70fd, 0x70fd, 0x70fd, 0x70fd, 0x70fd, 0x70fd, 0x70fd,
  0x70fd, 0x70fd, 0x70fd, 0x70fd, 0x70fd, 0x70fd, 0x70fd, 0x70fd,
  0x70fd, 0x70fd, 0x70fd, 0x70fd, 0x70fd, 0x70fd, 0x70fd, 0x70fd,
  0x70fd, 0x70fd, 0x70fd, 0x70fd, 0x70fd, 0x70fd, 0x70fd, 0x70fd,
  0x70fd, 0x70fd, 0x70fd, 0x70fd, 0x70fd, 0x70fd, 0x70fd, 0x70fd,
  0x70fd, 0x70fd, 0x70fd, 0x70fd, 0x70fd, 0x70fd, 0x70fd, 0x70fd,
  0x70fd, 0x70fd, 0x70fd, 0x70fd, 0x70fd, 0x70fd, 0x70fd, 0x70fd,
  0x70fd, 0x70fd, 0x70fd, 0x70fd, 0x70fd, 0x70fd, 0x70fd, 0x70fd,
  0x70fd, 0x70fd, 0x70fd, 0x70fd, 0x70fd, 0x70fd, 0x70fd, 0x70fd,
  0x70fd, 0x70fd, 0x70fd, 0x70fd, 0x70fd, 0x70fd, 0x70fd, 0x70fd,
  0x70fd, 0x70fd, 0x70fd, 0x70fd, 0x4a81, 0x63b9, 0x5a4f, 0x2afb,
  0x3292, 0x4ea7, 0x2d3a, 0x0494, 0x04a6, 0x0497, 0x04a0, 0x04ac,
  0x04a3, 0x049a, 0x04a9, 0x049d, 0x0492, 0x04a4, 0x0495, 0x049e,
  0x04aa, 0x04a1, 0x0498, 0x04a7, 0x049b, 0x0493, 0x04a5, 0x0496,
  0x049f, 0x04ab, 0x04a2, 0x0499, 0x04a8, 0x049c, 0x0491, 0x0490,
  0x04ad, 0x04ae, 0x04b0, 0x04af, 0x70ed,
  /* 0xfa */
  0x75c7, 0x63db, 0x765f, 0xa505, 0x574c, 0x15b5, 0x45ef, 0x23f5,
  0x0989, 0xadc2, 0xdfe4, 0x6444, 0x881b, 0x54ee, 0xa288, 0x4a3c,
  0x3984, 0x351f, 0xadb3, 0xdfaa, 0x7360, 0xc8f3, 0x81c5, 0x21eb,
  0x902d, 0x2248, 0xca69, 0xd584, 0x9f00, 0xaf31, 0x9053, 0x70fd,
  0x743e, 0x31e4, 0x7440, 0x7407, 0x40db, 0x74df, 0x70fd, 0x2341,
  0x07ba, 0x234e, 0x696c, 0x0a83, 0x5616, 0xae24, 0x7547, 0x23a0,
  0x9cf4, 0x23aa, 0x7abf, 0x8d3c, 0x73a5, 0x9070, 0x760e, 0x9bb3,
  0x87c3, 0x2dc2, 0x58fd, 0x7633, 0xa259, 0x23f2, 0x23f8, 0x6db4,
  0x2409, 0x240f, 0x2411, 0x0fdc, 0x2413, 0xaf26, 0x23f3, 0x2422,
  0x2582, 0x242d, 0x55cd, 0x3b7e, 0xa02d, 0x31bb, 0x244b, 0x2466,
  0x247c, 0x24f5, 0x2497, 0x24d4, 0x24f9, 0x2505, 0x4e15, 0x5742,
  0x2520, 0x57cd, 0xc853, 0x70fd, 0xe0de, 0x2789, 0x40d9, 0xa4c0,
  0x7894, 0xa52c, 0x2573, 0x70fd, 0x7aa2, 0x3f0b, 0x8566, 0x3a5b,
  0x45d5, 0x328a, 0x11aa, 0x0c0c, 0x8528, 0x43dd, 0x17a2, 0x25ad,
  0x25b4, 0xde2b, 0x25be, 0x70fd, 0x8416, 0x8453, 0x49e6, 0x25d3,
  0xe09b, 0x25e0, 0x25eb, 0x25ee, 0x45a7, 0xb8f2, 0x1259, 0x459c,
  0x2601, 0x2605, 0x3e89, 0x2089, 0x2a3e, 0x2619, 0x0deb, 0x7a4f,
  0x2620, 0xa52b, 0x97a3, 0x2636, 0x7b37, 0x2653, 0x4279, 0x276b,
  0x3897, 0x3f9b, 0x26ad, 0xc893, 0x7c34, 0x279d, 0x26cf, 0x26e4,
  0x1aa6, 0x854d, 0x7d9d, 0x10b4, 0x7c0d,
  /* 0xfb */
  0xdfbc, 0x9198, 0x2787, 0x1fad, 0x266f, 0x4617, 0x27c6, 0x27e9,
  0x70fd, 0x8717, 0xa57a, 0x1852, 0xa975, 0x38f3, 0x7f74, 0x2877,
  0x390d, 0x9c0a, 0x38e4, 0x38ed, 0x288d, 0x288f, 0x4af1, 0x2931,
  0x6887, 0x263e, 0x2940, 0x292f, 0x292d, 0xe166, 0x0963, 0x810f,
  0x2986, 0xa6e5, 0x3eae, 0x428b, 0x2982, 0x09f1, 0xc9be, 0x2a26,
  0x82d6, 0x2a43, 0x8314, 0xa8a3, 0x2a66, 0xaff5, 0x2a9c, 0x2aea,
  0x08a1, 0x2b20, 0x2b1c, 0x80fc, 0x2b3b, 0x2e3f, 0x2983, 0xee90,
  0xa7b8, 0x64d3, 0x08e1, 0x2b5f, 0x3ae6, 0x0a03, 0x4059, 0x847e,
  0x2c64, 0x2793, 0x84d2, 0x5705, 0x2c09, 0x7c0e, 0xc9c1, 0xca6a,
  0x861c, 0x2c19, 0x863b, 0x85f2, 0xcae6, 0x3fb1, 0x8668, 0x8515,
  0x2c39, 0xcb85, 0x2ceb, 0x2ca3, 0x0a26, 0xae29, 0x70fd, 0x0a48,
  0x2cd6, 0x4665, 0x2d13, 0xcce1, 0x9094, 0x1085, 0x8751, 0x0a72,
  0x84f8, 0x30c3, 0x2510, 0x2db6, 0x37c8, 0x2dbc, 0xd50e, 0x1304,
  0x1b5d, 0x72c4, 0x2e15, 0x33a0, 0x8874, 0xba8c, 0x7625, 0x2e33,
  0x2ddd, 0x1fd0, 0x2e45, 0x88c4, 0x2e53, 0x45ce, 0x2e54, 0x89e5,
  0xc868, 0x2e89, 0x1bdd, 0x2ec5, 0x2f29, 0x2f2f, 0x2fcb, 0x8ab9,
  0x8af7, 0x2f50, 0x2f58, 0x2f86, 0x8b24, 0x2efa, 0x3017, 0x54fc,
  0x0b6d, 0xad81, 0x8c89, 0x8db3, 0x5487, 0x0b76, 0x0f02, 0x306e,
  0x3cca, 0x70fd, 0x30ba, 0xa9fc, 0x7b93, 0x25e6, 0x20f7, 0x70fd,
  0x25e8, 0x85b1, 0x3049, 0x3134, 0xdcc2,
  /* 0xfc */
  0x3139, 0x313b, 0x0be0, 0x313c, 0x3a7e, 0x651b, 0x314d, 0x7281,
  0xf254, 0x0e1e, 0x1bae, 0x81fa, 0x317a, 0xc648, 0x8f50, 0x70fd,
  0x8fb1, 0x31a3, 0x68bd, 0xcbae, 0x31b2, 0x6440, 0xe036, 0x31e7,
  0x2ff6, 0x105f, 0xb510, 0x8bea, 0xce38, 0x9168, 0x62d6, 0x735e,
  0xe029, 0x3271, 0x38c5, 0x86b7, 0x0ca3, 0x10c7, 0x0979, 0x29d0,
  0x92b4, 0x4b71, 0x1140, 0x32de, 0x70fd, 0x32f3, 0xae02, 0xae0f,
  0xd553, 0x46a4, 0x2321, 0x2ce0, 0x33a4, 0x5624, 0x3382, 0xf266,
  0xcc12, 0x33c1, 0x2434, 0x76d6, 0x33c7, 0x2dea, 0xa4f7, 0x935f,
  0x3413, 0xe19d, 0xe8dd, 0x3410, 0x0c72, 0x9480, 0x93c1, 0x3263,
  0x339c, 0x375e, 0x35cb, 0x7218, 0x3505, 0x85b0, 0x3515, 0x980d,
  0x35ac, 0xae5f, 0x0d57, 0x3678, 0x3638, 0x824e, 0x863c, 0x70fd,
  0x418a, 0x9836, 0x6914, 0xa5cc, 0xb89d, 0x3721, 0x3725, 0x657b,
  0x0da6, 0x367a, 0x0d97, 0x378d, 0x4116, 0xaea8, 0xaea3, 0x37c5,
  0x37ad, 0x379f, 0x99fe, 0x37f5, 0xadc0, 0x1df7, 0x3811, 0x13d8,
  0x8669, 0x3820, 0x3823, 0x321f, 0x9d40, 0x3858, 0x9b77, 0x9b78,
  0x3884, 0x9b24, 0x9b25, 0x388b, 0x7d35, 0x38a7, 0xb4a6, 0x38b3,
  0x70fd, 0x8afd, 0x9bb1, 0xddb4, 0x9b48, 0xb5d3, 0x49c5, 0x93f7,
  0x6aa4, 0x3942, 0xa8dc, 0xaea1, 0x0e6b, 0x3c3a, 0xa0c2, 0x70fd,
  0x39a7, 0x39a2, 0xa70d, 0xe4ad, 0x3a17, 0x17e9, 0x3a62, 0x4050,
  0x633c, 0x3a41, 0x9d26, 0xcae0, 0x3a9d,
  /* 0xfd */
  0x9daf, 0x3c21, 0x3c4b, 0xe0df, 0x3bb3, 0x3b03, 0x9ecd, 0x3b41,
  0x3b40, 0x1032, 0x0d41, 0x9f3c, 0x0ec0, 0x39ec, 0x3ba1, 0xe04a,
  0x15fc, 0x3b76, 0x3bd8, 0x0ee1, 0x7489, 0x5563, 0x22d0, 0x3c39,
  0x9f59, 0x8d6a, 0x3c85, 0xa003, 0x3cdd, 0x0f33, 0x39f1, 0x3d08,
  0xe55c, 0x0f4d, 0x3d5d, 0x7863, 0x331e, 0x3d75, 0x3db4, 0x92cd,
  0x40b5, 0xa1db, 0x7475, 0x8798, 0x0a80, 0x2661, 0xa21a, 0x3e21,
  0xa43c, 0x3e1c, 0x3e77, 0x8fcb, 0xad71, 0xc891, 0x3e9a, 0x5426,
  0x3eb9, 0xa33c, 0x17c5, 0xa33d, 0xa6e4, 0xad8c, 0xad80, 0xa289,
  0x0a25, 0x0feb, 0x7bf2, 0x6c83, 0x9b79, 0x8fd1, 0x518f, 0x3a77,
  0xca65, 0xcae1, 0xcb2b, 0x3fd6, 0x3f9c, 0x407c, 0x4104, 0xadff,
  0xa5c5, 0xcbb2, 0x5733, 0xccb4, 0x2407, 0x70fd, 0x70fd, 0x562e,
  0xe121, 0x70fd, 0xa3af, 0x4653, 0xa4c2, 0x4bcc, 0x404f, 0x2cd1,
  0x99cb, 0x41f8, 0x0a4d, 0x419d, 0xa3b0, 0x40fa, 0x8357, 0xa57d,
  0x1855, 0x64f0, 0x4144, 0x415c, 0x104e, 0x4174, 0xe530, 0x103b,
  0x419f, 0xa684, 0x41d3, 0xa5d1, 0xa695, 0xa579, 0xa530, 0xa4f4,
  0xa67f, 0x241f, 0xa696, 0xa697, 0xa680, 0xc0dd, 0x424b, 0x427e,
  0x42a7, 0x4281, 0x42cc, 0x42d5, 0x42d6, 0x42df, 0x1404, 0x10e8,
  0x43b4, 0x4396, 0xa7b7, 0x432b, 0x4345, 0x2cc8, 0x434a, 0x70fd,
  0x2edc, 0xa8a5, 0x434f, 0x6462, 0xa801, 0x432c, 0xa99a, 0xaea7,
  0xaea2, 0x43ba, 0xe1e8, 0x42bd, 0x440e,
  /* 0xfe */
  0x6542, 0x4415, 0x2b51, 0x6543, 0x4424, 0x6441, 0xb785, 0x442e,
  0x4440, 0xadf4, 0x3afd, 0x4455, 0x4457, 0x1155, 0x99c4, 0x3a4d,
  0x413d, 0x4482, 0x70fd, 0x452b, 0xace3, 0xdeeb, 0x1bed, 0xdec4,
  0x4528, 0x452e, 0x45cf, 0x45aa, 0x7afa, 0xc7ee, 0x45c9, 0x4649,
  0xa722, 0x8527, 0xaea4, 0x3863, 0x0a05, 0xae37, 0xae0d, 0xae7b,
  0x45f7, 0x4615, 0x3b43, 0xaea6, 0x4639, 0x7643, 0x11d7, 0x70fd,
  0x93ad, 0x4660, 0xe3b2, 0x4647, 0x45e4, 0x4676, 0x55b9, 0x466c,
  0x0a70, 0x4674, 0x64f1, 0x3c6c, 0x4682, 0x1c53, 0xaf0c, 0xa69f,
  0xaef9, 0xe18f, 0x2d86, 0xe203, 0x86de, 0x46c8, 0x87c8, 0x470e,
  0x70fd, 0x471e, 0xe3d9, 0x888b, 0x2e17, 0xe3ac, 0x6485, 0x474d,
  0x474a, 0x4767, 0x476e, 0xb2c2, 0x1204, 0xb0d3, 0x478e, 0x465d,
  0x479e, 0x47b4, 0x4802, 0x482c, 0x4851, 0x484f, 0x486f, 0x4876,
  0xc275, 0x4890, 0x53ef, 0x0b38, 0xc6d1, 0xc6ce, 0x48a1, 0x48a5,
  0x48b7, 0x48cc, 0xccdf, 0x5662, 0xb3dd, 0xb47d, 0x8adc, 0x491e,
  0x4926, 0x4940, 0x36ef, 0xb4e0, 0x4958, 0x9c2c, 0x49af, 0xdf64,
  0xdf68, 0x8501, 0x49f4, 0x70fd, 0x8236, 0xae92, 0x3b0a, 0x4aaf,
  0x4ac7, 0x4ad3, 0x67a5, 0x4b2e, 0xb760, 0x4ad7, 0x4b34, 0x4ab1,
  0xd18c, 0x60f8, 0x5a04, 0xe12b, 0xbfc3, 0x911c, 0x4b86, 0x5a80,
  0x3b42, 0x4b80, 0xb957, 0x4b9d, 0xd639, 0x4b3c, 0x4ba9, 0x402a,
  0xce66, 0x11a8, 0x4bc6, 0xe4cd, 0x4bd4,
};

static const ucs4_t hkscs1999_2uni_upages[973] = {
  0x00080, 0x000c0, 0x00100, 0x00140, 0x001c0, 0x00240, 0x00280, 0x002c0,
  0x00400, 0x00440, 0x01e80, 0x01ec0, 0x02100, 0x02140, 0x02180, 0x021c0,
  0x023c0, 0x02440, 0x02540, 0x02700, 0x02e80, 0x02ec0, 0x02f00, 0x03000,
  0x03040, 0x03080, 0x030c0, 0x031c0, 0x03200, 0x03400, 0x03440, 0x03480,
  0x034c0, 0x03500, 0x03540, 0x03580, 0x035c0, 0x03600, 0x03640, 0x03680,
  0x036c0, 0x03700, 0x03740, 0x03780, 0x037c0, 0x03800, 0x03840, 0x03880,
  0x038c0, 0x03900, 0x03940, 0x03980, 0x039c0, 0x03a00, 0x03a40, 0x03a80,
  0x03ac0, 0x03b00, 0x03b40, 0x03b80, 0x03bc0, 0x03c00, 0x03c40, 0x03cc0,
  0x03d00, 0x03d40, 0x03d80, 0x03dc0, 0x03e00, 0x03e40, 0x03e80, 0x03ec0,
  0x03f00, 0x03f40, 0x03f80, 0x03fc0, 0x04000, 0x04040, 0x04080, 0x040c0,
  0x04100, 0x04140, 0x04180, 0x041c0, 0x04200, 0x04240, 0x04280, 0x042c0,
  0x04300, 0x04340, 0x04380, 0x043c0, 0x04400, 0x04440, 0x04480, 0x044c0,
  0x04500, 0x04540, 0x04580, 0x045c0, 0x04600, 0x04640, 0x04680, 0x046c0,
  0x04700, 0x04740, 0x04780, 0x047c0, 0x04800, 0x04840, 0x04880, 0x048c0,
  0x04900, 0x04940, 0x04980, 0x049c0, 0x04a00, 0x04a80, 0x04ac0, 0x04b00,
  0x04b40, 0x04b80, 0x04bc0, 0x04c00, 0x04c40, 0x04c80, 0x04cc0, 0x04d00,
  0x04d80, 0x04e00, 0x04e40, 0x04e80, 0x04ec0, 0x04f00, 0x04f40, 0x04f80,
  0x04fc0, 0x05000, 0x05040, 0x05080, 0x050c0, 0x05100, 0x05140, 0x05180,
  0x051c0, 0x05200, 0x05240, 0x05280, 0x052c0, 0x05300, 0x05340, 0x05380,
  0x053c0, 0x05400, 0x05440, 0x05480, 0x054c0, 0x05500, 0x05540, 0x05580,
  0x055c0, 0x05600, 0x05640, 0x05680, 0x056c0, 0x05700, 0x05740, 0x05780,
  0x057c0, 0x05800, 0x05840, 0x05880, 0x058c0, 0x05900, 0x05940, 0x05980,
  0x059c0, 0x05a00, 0x05a40, 0x05a80, 0x05ac0, 0x05b00, 0x05b40, 0x05b80,
  0x05bc0, 0x05c00, 0x05c40, 0x05c80, 0x05cc0, 0x05d00, 0x05d40, 0x05d80,
  0x05dc0, 0x05e00, 0x05e40, 0x05e80, 0x05ec0, 0x05f00, 0x05f40, 0x05f80,
  0x05fc0, 0x06000, 0x06040, 0x06080, 0x060c0, 0x06100, 0x06140, 0x06180,
  0x061c0, 0x06200, 0x06240, 0x06280, 0x062c0, 0x06300, 0x06340, 0x06380,
  0x063c0, 0x06400, 0x06440, 0x06480, 0x064c0, 0x06500, 0x06540, 0x06580,
  0x065c0, 0x06600, 0x06640, 0x06680, 0x066c0, 0x06700, 0x06740, 0x06780,
  0x067c0, 0x06800, 0x06840, 0x06880, 0x068c0, 0x06900, 0x06940, 0x06980,
  0x069c0, 0x06a00, 0x06a40, 0x06a80, 0x06ac0, 0x06b00, 0x06b40, 0x06b80,
  0x06bc0, 0x06c00, 0x06c40, 0x06c80, 0x06cc0, 0x06d00, 0x06d40, 0x06d80,
  0x06e00, 0x06e40, 0x06e80, 0x06ec0, 0x06f00, 0x06f40, 0x06f80, 0x06fc0,
  0x07000, 0x07040, 0x07080, 0x070c0, 0x07100, 0x07140, 0x07180, 0x071c0,
  0x07200, 0x07240, 0x07280, 0x072c0, 0x07300, 0x07340, 0x07380, 0x073c0,
  0x07400, 0x07440, 0x07480, 0x074c0, 0x07500, 0x07540, 0x07580, 0x075c0,
  0x07600, 0x07640, 0x07680, 0x076c0, 0x07700, 0x07740, 0x07780, 0x077c0,
  0x07800, 0x07840, 0x07880, 0x078c0, 0x07900, 0x07940, 0x07980, 0x079c0,
  0x07a00, 0x07a40, 0x07a80, 0x07ac0, 0x07b00, 0x07b40, 0x07b80, 0x07bc0,
  0x07c00, 0x07c40, 0x07c80, 0x07cc0, 0x07d00, 0x07d40, 0x07d80, 0x07dc0,
  0x07e00, 0x07e40, 0x07e80, 0x07ec0, 0x07f00, 0x07f40, 0x07f80, 0x07fc0,
  0x08000, 0x08040, 0x08080, 0x080c0, 0x08100, 0x08140, 0x08180, 0x081c0,
  0x08200, 0x08240, 0x08280, 0x082c0, 0x08300, 0x08340, 0x08380, 0x083c0,
  0x08400, 0x08440, 0x08480, 0x084c0, 0x08500, 0x08540, 0x085c0, 0x08600,
  0x08640, 0x08680, 0x086c0, 0x08740, 0x08780, 0x087c0, 0x08800, 0x08840,
  0x08880, 0x088c0, 0x08900, 0x08940, 0x08980, 0x089c0, 0x08a00, 0x08a40,
  0x08a80, 0x08ac0, 0x08b00, 0x08b40, 0x08b80, 0x08bc0, 0x08c40, 0x08c80,
  0x08cc0, 0x08d00, 0x08d40, 0x08d80, 0x08dc0, 0x08e00, 0x08e40, 0x08e80,
  0x08ec0, 0x08f00, 0x08f40, 0x08f80, 0x08fc0, 0x09000, 0x09040, 0x09080,
  0x090c0, 0x09140, 0x09180, 0x091c0, 0x09200, 0x09240, 0x09280, 0x092c0,
  0x09300, 0x09340, 0x09380, 0x093c0, 0x09400, 0x09440, 0x09480, 0x094c0,
  0x09500, 0x09540, 0x09580, 0x095c0, 0x09600, 0x09640, 0x09680, 0x096c0,
  0x09700, 0x09740, 0x09780, 0x097c0, 0x09800, 0x09840, 0x09880, 0x098c0,
  0x09900, 0x09940, 0x09980, 0x099c0, 0x09a00, 0x09a40, 0x09a80, 0x09ac0,
  0x09b00, 0x09b40, 0x09b80, 0x09bc0, 0x09c00, 0x09c40, 0x09d00, 0x09d40,
  0x09d80, 0x09dc0, 0x09e00, 0x09e40, 0x09e80, 0x09ec0, 0x09f00, 0x09f40,
  0x09f80, 0x0f900, 0x0ff00, 0x0ffc0, 0x20000, 0x20040, 0x20080, 0x200c0,
  0x20100, 0x20180, 0x201c0, 0x20200, 0x20240, 0x20280, 0x202c0, 0x20300,
  0x20340, 0x20380, 0x203c0, 0x20400, 0x20440, 0x20480, 0x204c0, 0x20540,
  0x20580, 0x205c0, 0x20600, 0x20640, 0x20700, 0x20740, 0x20800, 0x20840,
  0x208c0, 0x20900, 0x20940, 0x209c0, 0x20a00, 0x20a40, 0x20a80, 0x20ac0,
  0x20b00, 0x20b80, 0x20bc0, 0x20c00, 0x20c40, 0x20c80, 0x20cc0, 0x20d00,
  0x20d40, 0x20d80, 0x20dc0, 0x20e00, 0x20e40, 0x20e80, 0x20ec0, 0x20f00,
  0x20f40, 0x20f80, 0x20fc0, 0x21000, 0x21040, 0x21080, 0x210c0, 0x21100,
  0x21140, 0x21180, 0x211c0, 0x21200, 0x21240, 0x21280, 0x212c0, 0x21300,
  0x21340, 0x21380, 0x213c0, 0x21400, 0x21440, 0x21480, 0x214c0, 0x21540,
  0x21580, 0x21600, 0x21640, 0x21680, 0x216c0, 0x21700, 0x21740, 0x21780,
  0x217c0, 0x21800, 0x21840, 0x21880, 0x218c0, 0x21900, 0x21940, 0x21980,
  0x219c0, 0x21a00, 0x21a40, 0x21b40, 0x21bc0, 0x21c00, 0x21c40, 0x21c80,
  0x21d40, 0x21d80, 0x21dc0, 0x21e00, 0x21e80, 0x21ec0, 0x21f00, 0x21f40,
  0x21f80, 0x21fc0, 0x22040, 0x22080, 0x220c0, 0x22100, 0x22140, 0x22180,
  0x221c0, 0x22200, 0x22240, 0x22300, 0x22380, 0x223c0, 0x22440, 0x22480,
  0x224c0, 0x22500, 0x22540, 0x22580, 0x22600, 0x22640, 0x22680, 0x226c0,
  0x22700, 0x22740, 0x22780, 0x227c0, 0x22800, 0x22840, 0x22880, 0x228c0,
  0x22900, 0x22940, 0x22980, 0x22a40, 0x22ac0, 0x22b00, 0x22b40, 0x22bc0,
  0x22c00, 0x22c40, 0x22c80, 0x22cc0, 0x22d00, 0x22d40, 0x22d80, 0x22dc0,
  0x22e00, 0x22e40, 0x22e80, 0x22ec0, 0x22f40, 0x22fc0, 0x23000, 0x23040,
  0x23080, 0x230c0, 0x23100, 0x23140, 0x23180, 0x231c0, 0x23200, 0x23240,
  0x23280, 0x232c0, 0x23300, 0x23380, 0x233c0, 0x23400, 0x23440, 0x234c0,
  0x23500, 0x23540, 0x23580, 0x235c0, 0x23600, 0x23640, 0x23680, 0x236c0,
  0x23700, 0x23740, 0x23780, 0x237c0, 0x23800, 0x239c0, 0x23a80, 0x23ac0,
  0x23b40, 0x23c80, 0x23cc0, 0x23d40, 0x23d80, 0x23dc0, 0x23e00, 0x23e80,
  0x23ec0, 0x23f00, 0x23f40, 0x23f80, 0x23fc0, 0x24000, 0x24040, 0x24080,
  0x240c0, 0x24100, 0x24140, 0x24180, 0x241c0, 0x24200, 0x24240, 0x24280,
  0x242c0, 0x24300, 0x24340, 0x24380, 0x243c0, 0x24400, 0x24440, 0x24480,
  0x244c0, 0x24500, 0x24540, 0x245c0, 0x24600, 0x24640, 0x24680, 0x246c0,
  0x24700, 0x24780, 0x247c0, 0x24800, 0x24880, 0x248c0, 0x24900, 0x24940,
  0x24980, 0x249c0, 0x24a00, 0x24a40, 0x24a80, 0x24ac0, 0x24b40, 0x24bc0,
  0x24c00, 0x24c80, 0x24cc0, 0x24d00, 0x24d80, 0x24dc0, 0x24e00, 0x24e40,
  0x24e80, 0x24f00, 0x24f40, 0x24f80, 0x24fc0, 0x25000, 0x25040, 0x25080,
  0x25100, 0x25140, 0x251c0, 0x25200, 0x25240, 0x25280, 0x252c0, 0x25300,
  0x25400, 0x25440, 0x25500, 0x25540, 0x25580, 0x255c0, 0x25600, 0x25640,
  0x25680, 0x256c0, 0x25700, 0x25740, 0x257c0, 0x25840, 0x258c0, 0x25900,
  0x25940, 0x25980, 0x259c0, 0x25a80, 0x25ac0, 0x25b40, 0x25b80, 0x25bc0,
  0x25c00, 0x25c40, 0x25c80, 0x25cc0, 0x25d00, 0x25d40, 0x25e00, 0x25e40,
  0x25e80, 0x25ec0, 0x25f00, 0x25f40, 0x25fc0, 0x26000, 0x26040, 0x26080,
  0x26100, 0x26140, 0x26180, 0x261c0, 0x26240, 0x262c0, 0x26300, 0x26340,
  0x26380, 0x263c0, 0x26400, 0x26440, 0x26480, 0x26500, 0x26540, 0x26580,
  0x26600, 0x26680, 0x266c0, 0x26700, 0x26740, 0x26780, 0x267c0, 0x26800,
  0x26840, 0x26880, 0x268c0, 0x26900, 0x26940, 0x26980, 0x269c0, 0x26a00,
  0x26a40, 0x26b00, 0x26b40, 0x26b80, 0x26bc0, 0x26c00, 0x26c40, 0x26c80,
  0x26cc0, 0x26d00, 0x26d40, 0x26d80, 0x26dc0, 0x26e00, 0x26e40, 0x26e80,
  0x26ec0, 0x26f00, 0x26f40, 0x26f80, 0x26fc0, 0x27000, 0x27040, 0x27080,
  0x270c0, 0x27100, 0x27140, 0x271c0, 0x27200, 0x27280, 0x272c0, 0x27380,
  0x27400, 0x27440, 0x27480, 0x27540, 0x27580, 0x275c0, 0x27600, 0x27640,
  0x27680, 0x27700, 0x27740, 0x27780, 0x277c0, 0x27840, 0x27880, 0x278c0,
  0x27900, 0x27940, 0x27980, 0x279c0, 0x27a00, 0x27a40, 0x27a80, 0x27ac0,
  0x27b00, 0x27b40, 0x27bc0, 0x27c00, 0x27d00, 0x27d40, 0x27d80, 0x27dc0,
  0x27e40, 0x27f00, 0x27fc0, 0x28000, 0x28040, 0x28080, 0x280c0, 0x28100,
  0x28140, 0x28180, 0x28200, 0x28240, 0x28280, 0x282c0, 0x28300, 0x28340,
  0x28380, 0x28400, 0x28440, 0x28480, 0x28500, 0x28540, 0x285c0, 0x28600,
  0x28680, 0x286c0, 0x28700, 0x28800, 0x28900, 0x28940, 0x28980, 0x289c0,
  0x28a00, 0x28a40, 0x28a80, 0x28ac0, 0x28b00, 0x28b40, 0x28b80, 0x28bc0,
  0x28c00, 0x28cc0, 0x28d00, 0x28d80, 0x28e00, 0x28e40, 0x28e80, 0x28ec0,
  0x28fc0, 0x29080, 0x290c0, 0x29100, 0x29140, 0x29180, 0x291c0, 0x29400,
  0x29440, 0x294c0, 0x29580, 0x295c0, 0x29700, 0x297c0, 0x29800, 0x29840,
  0x29880, 0x298c0, 0x29900, 0x29940, 0x29980, 0x299c0, 0x29a00, 0x29a40,
  0x29b00, 0x29bc0, 0x29c80, 0x29d00, 0x29d40, 0x29d80, 0x29dc0, 0x29e00,
  0x29e40, 0x29e80, 0x29ec0, 0x29f00, 0x29f80, 0x29fc0, 0x2a000, 0x2a080,
  0x2a0c0, 0x2a100, 0x2a140, 0x2a180, 0x2a1c0, 0x2a200, 0x2a280, 0x2a2c0,
  0x2a380, 0x2a400, 0x2a440, 0x2a5c0, 0x2a600, 0x2a640, 0x2a680, 0x2f800,
  0x2f840, 0x2f880, 0x2f8c0, 0x2f980, 0x2f9c0,
};

static int
hkscs1999_mbtowc (conv_t conv, ucs4_t *pwc, const unsigned char *s, int n)
{
  unsigned char c1 = s[0];
  if ((c1 >= 0x88 && c1 <= 0x8b) || (c1 >= 0x8d && c1 <= 0xa0) || (c1 >= 0xc6 && c1 <= 0xc8) || (c1 >= 0xf9 && c1 <= 0xfe)) {
    if (n >= 2) {
      unsigned char c2 = s[1];
      if ((c2 >= 0x40 && c2 < 0x7f) || (c2 >= 0xa1 && c2 < 0xff)) {
        unsigned int i = 157 * (c1 - 0x80) + (c2 - (c2 >= 0xa1 ? 0x62 : 0x40));
        ucs4_t wc = 0xfffd;
        unsigned short swc;
        if (i < 2041) {
          if (i < 1883)
            swc = hkscs1999_2uni_page88[i-1256],
            wc = hkscs1999_2uni_upages[swc>>6] | (swc & 0x3f);
        } else if (i < 10990) {
          if (i < 5181)
            swc = hkscs1999_2uni_page8d[i-2041],
            wc = hkscs1999_2uni_upages[swc>>6] | (swc & 0x3f);
        } else if (i < 18997) {
          if (i < 11461)
            swc = hkscs1999_2uni_pagec6[i-10990],
            wc = hkscs1999_2uni_upages[swc>>6] | (swc & 0x3f);
        } else {
          if (i < 19939)
            swc = hkscs1999_2uni_pagef9[i-18997],
            wc = hkscs1999_2uni_upages[swc>>6] | (swc & 0x3f);
        }
        if (wc != 0xfffd) {
          *pwc = wc;
          return 2;
        }
      }
      return RET_ILSEQ;
    }
    return RET_TOOFEW(0);
  }
  return RET_ILSEQ;
}

static const unsigned short hkscs1999_2charset[4698] = {
  0xc6d8, 0x8859, 0x8857, 0x885d, 0x885b, 0x8866, 0x8861, 0x885f,
  0x886a, 0x8868, 0x886f, 0x886d, 0x88a7, 0x8873, 0x8871, 0x8877,
  0x8875, 0xc8fb, 0x887b, 0x8879, 0x88a2, 0x8856, 0x8867, 0x885a,
  0x886c, 0x885c, 0x886e, 0x8870, 0xc8fc, 0x885e, 0x8874, 0xc8fa,
  0x8878, 0x8858, 0x8869, 0x8872, 0x8860, 0x8876, 0x887a, 0x887c,
  0x887d, 0x887e, 0x88a1, 0xc8f6, 0x886b, 0xc8f8, 0xc8f7, 0x88a8,
  0xc8fe, 0xc8f9, 0xc8f5, 0xc8fd, 0xc6d9, 0xc7f9, 0xc7f3, 0xc7f4,
  0xc7f5, 0xc7f6, 0xc7f7, 0xc7f8, 0xc7fa, 0xc7fb, 0xc7fc, 0xc7fd,
  0xc7fe, 0xc840, 0xc841, 0xc842, 0xc843, 0xc844, 0xc845, 0xc846,
  0xc847, 0xc848, 0xc849, 0xc84a, 0xc84b, 0xc84c, 0xc84d, 0xc84e,
  0xc84f, 0xc850, 0xc851, 0xc852, 0xc853, 0xc854, 0xc855, 0xc856,
  0xc857, 0xc858, 0xc859, 0xc85a, 0xc85c, 0xc85d, 0xc85e, 0xc85f,
  0xc860, 0xc861, 0xc862, 0xc863, 0xc864, 0xc865, 0xc866, 0xc867,
  0xc868, 0xc869, 0xc86a, 0xc86b, 0xc86c, 0xc86d, 0xc86e, 0xc86f,
  0xc870, 0xc871, 0xc872, 0xc873, 0xc874, 0xc875, 0xc85b, 0x8863,
  0x88a4, 0x8865, 0x88a6, 0xc8d2, 0xc8d3, 0xc6b5, 0xc6b6, 0xc6b7,
  0xc6b8, 0xc6b9, 0xc6ba, 0xc6bb, 0xc6bc, 0xc6bd, 0xc6be, 0xc877,
  0xc878, 0xc876, 0x88a9, 0x88aa, 0xc6a1, 0xc6a2, 0xc6a3, 0xc6a4,
  0xc6a5, 0xc6a6, 0xc6a7, 0xc6a8, 0xc6a9, 0xc6aa, 0xc6ab, 0xc6ac,
  0xc6ad, 0xc6ae, 0xc6af, 0xc6b0, 0xc6b1, 0xc6b2, 0xc6b3, 0xc6b4,
  0xf9f9, 0xf9f8, 0xf9e6, 0xf9ef, 0xf9dd, 0xf9e8, 0xf9f1, 0xf9df,
  0xf9ec, 0xf9f5, 0xf9e3, 0xf9ee, 0xf9f7, 0xf9e5, 0xf9e9, 0xf9f2,
  0xf9e0, 0xf9eb, 0xf9f4, 0xf9e2, 0xf9e7, 0xf9f0, 0xf9de, 0xf9ed,
  0xf9f6, 0xf9e4, 0xf9ea, 0xf9f3, 0xf9e1, 0xf9fa, 0xf9fb, 0xf9fd,
  0xf9fc, 0xc6e6, 0xc8d6, 0xc8d7, 0xc8d8, 0xc8d9, 0xc8da, 0xc8db,
  0xc8dc, 0xc8dd, 0xc8de, 0xc8df, 0xc8e0, 0xc8e1, 0xc8e2, 0xc8e3,
  0xc8e4, 0xc8e5, 0xc8e6, 0xc8e7, 0xc8e8, 0xc8e9, 0xc8ea, 0xc8eb,
  0xc8ec, 0xc8ed, 0xc8ee, 0xc8ef, 0xc8f0, 0xc8f1, 0xc6cd, 0xc6e0,
  0xc6e1, 0xc6e2, 0xc6e7, 0xc6e8, 0xc6e9, 0xc6ea, 0xc6eb, 0xc6ec,
  0xc6ed, 0xc6ee, 0xc6ef, 0xc6f0, 0xc6f1, 0xc6f2, 0xc6f3, 0xc6f4,
  0xc6f5, 0xc6f6, 0xc6f7, 0xc6f8, 0xc6f9, 0xc6fa, 0xc6fb, 0xc6fc,
  0xc6fd, 0xc6fe, 0xc740, 0xc741, 0xc742, 0xc743, 0xc744, 0xc745,
  0xc746, 0xc747, 0xc748, 0xc749, 0xc74a, 0xc74b, 0xc74c, 0xc74d,
  0xc74e, 0xc74f, 0xc750, 0xc751, 0xc752, 0xc753, 0xc754, 0xc755,
  0xc756, 0xc757, 0xc758, 0xc759, 0xc75a, 0xc75b, 0xc75c, 0xc75d,
  0xc75e, 0xc75f, 0xc760, 0xc761, 0xc762, 0xc763, 0xc764, 0xc765,
  0xc766, 0xc767, 0xc768, 0xc769, 0xc76a, 0xc76b, 0xc76c, 0xc76d,
  0xc76e, 0xc76f, 0xc770, 0xc771, 0xc772, 0xc773, 0xc774, 0xc775,
  0xc776, 0xc777, 0xc778, 0xc779, 0xc77a, 0xc8d4, 0xc8d5, 0xc6dc,
  0xc6dd, 0xc77b, 0xc77c, 0xc77d, 0xc77e, 0xc7a1, 0xc7a2, 0xc7a3,
  0xc7a4, 0xc7a5, 0xc7a6, 0xc7a7, 0xc7a8, 0xc7a9, 0xc7aa, 0xc7ab,
  0xc7ac, 0xc7ad, 0xc7ae, 0xc7af, 0xc7b0, 0xc7b1, 0xc7b2, 0xc7b3,
  0xc7b4, 0xc7b5, 0xc7b6, 0xc7b7, 0xc7b8, 0xc7b9, 0xc7ba, 0xc7bb,
  0xc7bc, 0xc7bd, 0xc7be, 0xc7bf, 0xc7c0, 0xc7c1, 0xc7c2, 0xc7c3,
  0xc7c4, 0xc7c5, 0xc7c6, 0xc7c7, 0xc7c8, 0xc7c9, 0xc7ca, 0xc7cb,
  0xc7cc, 0xc7cd, 0xc7ce, 0xc7cf, 0xc7d0, 0xc7d1, 0xc7d2, 0xc7d3,
  0xc7d4, 0xc7d5, 0xc7d6, 0xc7d7, 0xc7d8, 0xc7d9, 0xc7da, 0xc7db,
  0xc7dc, 0xc7dd, 0xc7de, 0xc7df, 0xc7e0, 0xc7e1, 0xc7e2, 0xc7e3,
  0xc7e4, 0xc7e5, 0xc7e6, 0xc7e7, 0xc7e8, 0xc7e9, 0xc7ea, 0xc7eb,
  0xc7ec, 0xc7ed, 0xc7ee, 0xc7ef, 0xc7f0, 0xc7f1, 0xc7f2, 0xc6e3,
  0xc6da, 0xc6db, 0x8840, 0x8841, 0x8842, 0x8843, 0x8844, 0x8846,
  0x8849, 0x884a, 0x884d, 0x884f, 0x8850, 0x8851, 0x8852, 0x8854,
  0x8855, 0xc879, 0xc8d1, 0x9277, 0x96df, 0x89d5, 0x93cd, 0x9bdf,
  0xfa68, 0x89da, 0x8f59, 0x89db, 0x8f5d, 0x89dc, 0x96f7, 0x8ada,
  0x8bdc, 0x97db, 0x9e53, 0x9daa, 0x9bea, 0x8a6e, 0x8bc8, 0x89e8,
  0x89ea, 0xfb70, 0x89ed, 0x94dd, 0x89ee, 0x9eb4, 0x8ad3, 0x92db,
  0x94db, 0x89f9, 0xfb7a, 0x89fb, 0x9efc, 0x89fc, 0x89bf, 0x89fe,
  0x89e6, 0x9d46, 0x9dee, 0xa07e, 0xa068, 0x98e9, 0x8b68, 0x8dfd,
  0x8bbe, 0x9fd9, 0x8aeb, 0x9fd7, 0x8b6a, 0x9c5c, 0x8bb1, 0xfb5e,
  0x9df3, 0xa0d0, 0xfc66, 0x92e9, 0x9aec, 0x8fab, 0xfa48, 0x8e45,
  0x9c6f, 0x9ede, 0x89ef, 0x96e9, 0x9ebb, 0x94de, 0x9eb8, 0x97ba,
  0xfb65, 0x95d6, 0x9cbb, 0x97da, 0x8f45, 0xfb7d, 0x9158, 0xfe64,
  0x9856, 0x9b4d, 0x935b, 0x95c7, 0x97e7, 0x9359, 0x91f5, 0x97b8,
  0xfda2, 0xfbb6, 0x92fa, 0x9357, 0x8ba6, 0xfbb9, 0x97b0, 0xfdc4,
  0x9ca1, 0x91f2, 0x91f9, 0x8ff1, 0x9745, 0x9853, 0xfe78, 0xfbc1,
  0x9251, 0x9dad, 0xfd6c, 0xfa6b, 0x9bc2, 0x9a7b, 0x8b60, 0x934b,
  0x9abd, 0x91b7, 0x95b4, 0xfec5, 0x9ef0, 0x8d64, 0x9269, 0x8d67,
  0xfbea, 0xfbef, 0x8d68, 0x93eb, 0xfc42, 0x9166, 0xfacd, 0x93dd,
  0x8bcc, 0x8d6d, 0x8d6e, 0x96a8, 0xfca6, 0x8d6f, 0x8d70, 0xfc64,
  0x9060, 0x8d74, 0x97c3, 0x8ad0, 0x9274, 0x9bbe, 0x9cc8, 0x9cba,
  0x8d78, 0x9eb9, 0x955a, 0x91b4, 0x8a48, 0x8d7d, 0x8a7d, 0x8ac2,
  0xfd4a, 0x8da1, 0x8ad1, 0xfcb4, 0x8b47, 0x93a4, 0x9eda, 0x8a51,
  0x8da6, 0x9ec5, 0xfcc4, 0xa078, 0x94b5, 0xfcc2, 0x8a6b, 0x8dab,
  0xfae8, 0x8dad, 0xfc49, 0x93c1, 0x906f, 0x8db0, 0x947e, 0x90fa,
  0x9479, 0x8db2, 0xfcee, 0x997b, 0x8db4, 0x8db7, 0x91b3, 0x8dbb,
  0x8dba, 0x8dbc, 0x9044, 0xfd4c, 0x93e4, 0x93e0, 0xfd53, 0x8dc3,
  0x9bb8, 0xfbf0, 0x93e9, 0x93f6, 0x8dc5, 0x8dca, 0x8dcc, 0xfd5d,
  0x93b5, 0xfd61, 0x9cf8, 0x9252, 0xa0e8, 0x9ca5, 0x8dd6, 0x97c0,
  0xa0de, 0x97d2, 0xfaa5, 0xfda3, 0x8ddb, 0x8eaf, 0x91b5, 0xfd49,
  0xfdd1, 0x8deb, 0x97c6, 0xfdce, 0x90fc, 0xfc59, 0x96d6, 0x97c5,
  0x8def, 0x97d7, 0x8df0, 0x96a6, 0xfbbf, 0x8df3, 0x9449, 0x8df5,
  0x9872, 0x8e6b, 0xfafd, 0x8f50, 0x9dcc, 0xfc65, 0x996e, 0x94a1,
  0x8f63, 0xa0da, 0x9253, 0xfde9, 0x9db5, 0x9879, 0x9d5d, 0x8d63,
  0x9669, 0x9f70, 0xfc6a, 0x8ac7, 0x89d7, 0xfe4d, 0x9edd, 0xfefb,
  0x98bc, 0xfacc, 0x95b0, 0x9464, 0x936f, 0x94b9, 0x95ec, 0x91ee,
  0x98c3, 0x95f6, 0x8ffd, 0x98c5, 0x9766, 0xfe6e, 0x97dd, 0x92d2,
  0x9761, 0x98cb, 0x95f0, 0x975d, 0x91e3, 0x98cc, 0x9469, 0x98cd,
  0x98ce, 0x95fc, 0x94a3, 0x9662, 0xfeb6, 0x9463, 0x98d0, 0x98d1,
  0x9475, 0xfae0, 0x9472, 0x98d6, 0x8af0, 0x98d9, 0x98db, 0x98dd,
  0x98a8, 0x8a6d, 0x8afb, 0x8aae, 0xfbc9, 0x98e4, 0x98e6, 0x98e8,
  0x8a4d, 0x9257, 0x95df, 0xa0ac, 0x98eb, 0x98ec, 0x98f4, 0x8ab8,
  0x9ee7, 0x94bc, 0xfcd1, 0x9cc6, 0x9e7e, 0x98fe, 0xfde8, 0x9940,
  0x94c9, 0x94d3, 0x9946, 0x90c0, 0x94d1, 0x9573, 0x93c2, 0x9948,
  0x994b, 0x8e55, 0x994e, 0x8efe, 0x8e59, 0x94ec, 0x94ef, 0x8f74,
  0x9955, 0x9544, 0x9956, 0x9959, 0x995b, 0xfa45, 0x90b7, 0x9743,
  0x95cd, 0x97c9, 0xfd50, 0x8eb9, 0x95c6, 0x9967, 0x8ab9, 0x8dfc,
  0x8a76, 0x9d51, 0x9973, 0x9d4f, 0x997a, 0x9564, 0x99a1, 0x99a5,
  0x99a7, 0x8eed, 0x99ad, 0xc87e, 0x946e, 0x8f70, 0xfad0, 0x99b3,
  0xa053, 0x965c, 0xfd7a, 0x97fe, 0x92bd, 0x97fd, 0x8f64, 0xfcf7,
  0x9562, 0x97cd, 0x9e64, 0x924c, 0x8ec9, 0x99bc, 0x9da5, 0x8f54,
  0x8f7c, 0x8ea2, 0x8f7a, 0x97ae, 0x96c8, 0x99c3, 0x90d6, 0x9cbe,
  0x8f76, 0x9470, 0xfb4b, 0xfdca, 0x8ec7, 0xa0f9, 0x8fa9, 0x99c7,
  0x90d7, 0x9edf, 0x99ce, 0x8fba, 0x8feb, 0x99cf, 0x8fc2, 0x92c9,
  0x97dc, 0x95b3, 0x9c79, 0x95b2, 0x8fdb, 0x9be3, 0x9e7a, 0x9bee,
  0x99de, 0xfafa, 0x8a52, 0x99e1, 0x8a67, 0x8bb5, 0x8aac, 0x99e9,
  0xfbca, 0x97de, 0x95d1, 0x99f5, 0xfc4a, 0x9ba9, 0xfbdc, 0xfe56,
  0x9ea4, 0x9d49, 0x95db, 0x89c5, 0x99f8, 0x9664, 0x9055, 0x96d4,
  0x977c, 0x964d, 0x97e1, 0x9a48, 0x9a49, 0xfe7d, 0x90aa, 0x9a50,
  0x9347, 0x8ed8, 0x90c9, 0x9a55, 0x90bc, 0x9a58, 0x8bb8, 0x90d5,
  0x9641, 0x9a5a, 0x9a5c, 0x97c2, 0x8abb, 0x9baa, 0x90f5, 0x9a60,
  0x9145, 0x9a63, 0x8bb6, 0xfccf, 0x966b, 0x9a6e, 0x914f, 0x9746,
  0xa0e6, 0x92d7, 0x9675, 0x93d4, 0x91bb, 0x9679, 0x9a70, 0x9678,
  0x91cd, 0x9c4a, 0xa06f, 0xa06a, 0x915f, 0x9fa5, 0x89ba, 0x9ecd,
  0x9a79, 0x9dce, 0x9d73, 0x96b9, 0x96bc, 0x9cd1, 0x89b7, 0x9eee,
  0xfb43, 0x9ec9, 0xfbd3, 0x91ae, 0x9d78, 0x9d7b, 0x9eb3, 0x9eb2,
  0x9dd6, 0x994f, 0x89ce, 0x8bc0, 0x9fc4, 0x8bd4, 0xc6bf, 0x8bf9,
  0x8946, 0xc6c0, 0xfae5, 0xc87b, 0x8bc6, 0x9c57, 0x9afb, 0x89d0,
  0x89cf, 0xc6c1, 0x89d1, 0x89e2, 0x927e, 0x9dba, 0xc6c2, 0xfbf8,
  0x8bc7, 0x926b, 0x89d2, 0x9fcf, 0x9da9, 0x89d3, 0x99e2, 0x9267,
  0x92a4, 0x894e, 0x894f, 0x9278, 0x91b6, 0x89d4, 0x9fd2, 0x92a7,
  0x95a2, 0x926e, 0x96ea, 0x926f, 0x92a3, 0x8950, 0xfa57, 0x9866,
  0x89d6, 0x98b2, 0x92ab, 0x96de, 0x92ac, 0x9f6e, 0x8ef2, 0x9f6c,
  0x89d8, 0xfa59, 0x92a8, 0x9163, 0x9f73, 0x92ad, 0x9be9, 0x92a9,
  0x92aa, 0x89d9, 0xfd56, 0x9fa8, 0x92a1, 0x90e3, 0xa0a6, 0x94ab,
  0xfc72, 0x97c4, 0x92ae, 0xfa67, 0x92a2, 0xfa69, 0x9268, 0x8951,
  0xfa6f, 0xfa71, 0x8952, 0x945a, 0xc6c3, 0x89dd, 0xc8a2, 0xc6c4,
  0x9e52, 0x8953, 0x9e55, 0x92ba, 0xc6c5, 0xfa7d, 0xfaa8, 0x9a68,
  0xfa47, 0xfa7e, 0x92bb, 0xfdb6, 0xfaa2, 0xfaa3, 0xfaa4, 0x9bb4,
  0xfaa6, 0x89df, 0xfddb, 0xfaa9, 0x8954, 0xfaab, 0xfc7a, 0x89e0,
  0x9f4f, 0xc87d, 0x89e1, 0xfab0, 0x9fcd, 0xa0e7, 0xfab1, 0x89a6,
  0x9efa, 0xfab2, 0xfab4, 0x92c4, 0x9f6f, 0x8bb0, 0x9fac, 0x89e3,
  0x9bd3, 0x89e4, 0xfab5, 0x9fd5, 0x8955, 0x92c5, 0x8956, 0xfab3,
  0xfab6, 0xfab7, 0x9edc, 0xfbc4, 0x9f71, 0xfaba, 0x92c7, 0xc6c6,
  0x9a4c, 0x89e5, 0x9f7d, 0xa0a9, 0xfac4, 0xc6c7, 0x8957, 0xfaaa,
  0x8958, 0x8be3, 0x8b61, 0x9af1, 0x9eb7, 0xc6c8, 0xfad1, 0xfad2,
  0x9eba, 0xfad4, 0xfad9, 0xfadb, 0x9ce0, 0xfbf7, 0xfbfa, 0x89e7,
  0xa07a, 0xfadc, 0xfadd, 0x89e9, 0xc6c9, 0xfae2, 0x89eb, 0xfae3,
  0x90c8, 0x92da, 0x8959, 0x9cf5, 0x895a, 0xfae7, 0x9fa2, 0xfaea,
  0xfaed, 0x8fad, 0xfb59, 0xfaef, 0x96ef, 0x9dec, 0x9dca, 0xfd6d,
  0x89ec, 0xfb44, 0x9de2, 0x9ec0, 0x9e56, 0x9f79, 0x9ac7, 0xfaf4,
  0x98a1, 0xfaf8, 0x89f0, 0x9e47, 0x9df7, 0x9fd3, 0x9aca, 0x89f1,
  0xfaf9, 0x8e5a, 0x89f2, 0x89f3, 0x925d, 0x8b51, 0x92e0, 0x89f4,
  0x9fd4, 0x8a79, 0x89f5, 0x97a7, 0x93ba, 0x9e58, 0x89f6, 0x9e57,
  0x89f7, 0x8a41, 0x89f8, 0xfaf1, 0x89fa, 0xfb42, 0xfabf, 0xfba3,
  0xfaf7, 0x9e4e, 0x94dc, 0x95da, 0x9df8, 0x9f6a, 0x8ab7, 0xfb46,
  0x8a46, 0xfb47, 0x9148, 0x92de, 0x8b53, 0x9df6, 0x9bda, 0x9d7e,
  0x89fd, 0x99e4, 0x9e43, 0x9de9, 0x8f52, 0x9df5, 0x9df0, 0x99e7,
  0x8bbd, 0x9def, 0x9fb7, 0x9dd0, 0x9feb, 0x8da9, 0x9dcf, 0x98e1,
  0x9de5, 0x9dc8, 0xfb4f, 0x9deb, 0xfb54, 0xfb55, 0x9aa2, 0x8ad6,
  0x9a5f, 0x9ef5, 0x8fb7, 0x9ad2, 0x9e6a, 0x9ee8, 0x8bbf, 0x91c2,
  0x9d62, 0x9260, 0x925e, 0x91c1, 0x8ac5, 0x97a3, 0x8b6c, 0x8d7e,
  0x9c54, 0x9dbd, 0x9cc5, 0x895b, 0xfb5c, 0xfb5b, 0xfb57, 0x98c7,
  0xfb5a, 0x9cee, 0x92e2, 0x94a7, 0x9bd4, 0xfb64, 0xfb76, 0xfb60,
  0x99e5, 0x9ac2, 0x91fb, 0xa073, 0x9f72, 0x9fcc, 0x98a5, 0x92e8,
  0x9bbc, 0x96f3, 0x92e7, 0xfc67, 0x8b7d, 0x9bf4, 0x9ef7, 0x9ec1,
  0x996f, 0x96f1, 0x8e41, 0x954a, 0x97e6, 0x96f5, 0x92e6, 0x9f42,
  0xfb67, 0x99a9, 0xfae6, 0xfb69, 0x97e5, 0x967d, 0xfb6c, 0x99a2,
  0x9abb, 0x9a65, 0x944e, 0xfb6e, 0x99df, 0x98e3, 0x9254, 0x967b,
  0x8aaf, 0x8baf, 0x9ebd, 0x9ee6, 0xfb6f, 0x8ee1, 0x9b7d, 0x9c7e,
  0xf9d9, 0x92ea, 0xfb72, 0xfb71, 0x895c, 0x98f0, 0x96f2, 0xfb74,
  0x8bc1, 0x895d, 0x89de, 0x895e, 0xc6ca, 0xfe42, 0xfb7b, 0x895f,
  0x8960, 0x9bcd, 0x9dd3, 0x984c, 0x9752, 0x95c3, 0x9bb6, 0x9ab9,
  0x97b3, 0x9f74, 0x92f1, 0x97df, 0xfba6, 0xfbab, 0x9877, 0x9854,
  0x95c5, 0x9d55, 0xfbb2, 0x957e, 0x9742, 0x94e6, 0x92f5, 0x92fd,
  0xfba2, 0x9c51, 0x94e9, 0x985c, 0x92f0, 0x944c, 0x916b, 0x8b78,
  0x94e2, 0x984f, 0xfbb5, 0x9271, 0x9365, 0x985b, 0x9850, 0x97bc,
  0x92f3, 0x9340, 0x984d, 0x9572, 0xfdef, 0xfdc1, 0xfbba, 0x92eb,
  0xfc73, 0x97b7, 0xfbb4, 0x90a7, 0x9741, 0x92f4, 0xfbbc, 0x9577,
  0x9ee2, 0x8f78, 0xf9dc, 0x9672, 0x9eb5, 0x964b, 0xa0fa, 0x9575,
  0x90da, 0x9367, 0xfea4, 0x90df, 0x9354, 0x8961, 0x8bb4, 0x9dc0,
  0x8e48, 0xfbc5, 0xfbc7, 0xc6cb, 0xfa79, 0x9e67, 0xfbd2, 0x8962,
  0x8963, 0xfc7d, 0x9f6b, 0xfbcc, 0xfeae, 0xfbd1, 0xfb75, 0xfbd4,
  0xfbd6, 0xfbd8, 0x8b5d, 0x934c, 0x9ae2, 0x8bc9, 0xfbdb, 0x9fc9,
  0x9f44, 0x98ed, 0xfbdd, 0x8df2, 0x8964, 0xfdf2, 0x934d, 0xfbe7,
  0xa0f2, 0x9868, 0x9f58, 0x8d73, 0xfbde, 0xfbdf, 0xfbe3, 0x8da8,
  0xfbe4, 0x9c75, 0x9878, 0x8d60, 0xfbe5, 0x8d61, 0x8d62, 0xa0a1,
  0x9c40, 0x98ad, 0x9eea, 0x9ceb, 0xfbe0, 0x9f51, 0x8d65, 0x9cf1,
  0xfc58, 0x8d66, 0x9654, 0xfbe8, 0xc6cc, 0x9fce, 0xfbfc, 0x9ae4,
  0x9f75, 0xfbf1, 0x8d69, 0x934f, 0x934e, 0xfbf4, 0xc6ce, 0xfbc3,
  0x8965, 0x8d6a, 0x9353, 0x9dfb, 0xfbfd, 0x9059, 0xfc40, 0xfc41,
  0xfc43, 0x9361, 0xfc46, 0x9362, 0x8d6b, 0xfc4c, 0x95b8, 0xc6d0,
  0x8bca, 0x987a, 0xc6d1, 0xfc51, 0xfc54, 0xfaaf, 0x8d6c, 0xfa61,
  0xfc57, 0x9b70, 0xa051, 0x8bcb, 0x936e, 0xfcd5, 0xfca9, 0xfc61,
  0x8966, 0xfacb, 0xf9da, 0x937a, 0xa0e0, 0x936b, 0xfc6b, 0xa0dc,
  0x9468, 0xfc6d, 0x8d71, 0xfd64, 0x99ba, 0x9ad0, 0x9a61, 0xa0e5,
  0xa05b, 0x96ac, 0x9740, 0x9ef1, 0x9f7e, 0x8d72, 0xfc76, 0x96a9,
  0xa06e, 0xfcaa, 0xfbcd, 0xfc74, 0xa074, 0xa071, 0xfc79, 0xfc7c,
  0x9c50, 0x9379, 0x9378, 0xa0dd, 0x8d75, 0x8d76, 0x9374, 0x8d77,
  0xfca5, 0xfca2, 0x90c3, 0xa079, 0x8d79, 0x8bfc, 0xa076, 0x8bcd,
  0x9f5a, 0x9ff4, 0x9fba, 0x8d7a, 0x9e45, 0x93b0, 0xa075, 0x9b46,
  0xfcae, 0xfcb0, 0xfa51, 0x8d7b, 0x8d7c, 0x9ed6, 0x93ac, 0x9f5b,
  0x93a9, 0xa07c, 0xfcb2, 0x8ac1, 0x9fb4, 0xfcac, 0x9e4c, 0x8fc5,
  0x93ad, 0x9dc3, 0x8da2, 0x9d4a, 0xfcb6, 0x8da3, 0x9e4b, 0x9e4d,
  0x8da4, 0x8afd, 0xfcb5, 0xfcc3, 0x93b2, 0x8da5, 0x93a1, 0x8ac6,
  0x8a5b, 0x894d, 0xfed4, 0x8a78, 0x93ab, 0x8da7, 0x9f45, 0x8a56,
  0xfcbf, 0xfcc0, 0x8ee6, 0x8aa4, 0x8943, 0x93f3, 0xfcab, 0x9ea2,
  0x9dc7, 0xc6d2, 0x8bce, 0xfcc5, 0xfccb, 0x93b3, 0xfcca, 0xfcc9,
  0x8dac, 0xfbc6, 0x8967, 0xfccd, 0x9cf3, 0xfcd0, 0x95bb, 0xfcd3,
  0xfcd4, 0x8dae, 0xfcd7, 0x93db, 0xfe63, 0x93d5, 0xfcda, 0x9b71,
  0xfcdd, 0x8daf, 0xfaf2, 0x93d8, 0xfcdf, 0x93d3, 0xfce1, 0x8e76,
  0xfc62, 0x93d1, 0x8db1, 0x9859, 0xfb52, 0xfb53, 0xfb4d, 0x9cbf,
  0x9b72, 0xfb50, 0x93be, 0x9df1, 0xfceb, 0xa0bb, 0x9b7e, 0x8db3,
  0x9ae8, 0x8edc, 0x9cf9, 0xfa50, 0x98e7, 0xfcf3, 0xfcf2, 0x93e5,
  0x9a59, 0x8db5, 0xfd4d, 0xfd5e, 0x8f7d, 0x9547, 0xfcf6, 0x9250,
  0x8968, 0x8db6, 0xfcfb, 0xa07d, 0x98fc, 0x8969, 0xfe4f, 0x9256,
  0xfac9, 0x93e8, 0xfcf8, 0x9ce3, 0xfda9, 0xfc44, 0x9640, 0x8db8,
  0x9b4a, 0x8fb9, 0xfcfe, 0x896a, 0x8db9, 0x917e, 0x93f4, 0xfb7c,
  0x93e7, 0x97ef, 0xfe4a, 0xfd45, 0x96a5, 0xfee0, 0xfd48, 0xfd47,
  0xfef2, 0xfe6a, 0x8dbd, 0x9ba1, 0x9ab7, 0x8efc, 0x9fa1, 0xfd51,
  0xfaad, 0x8dbe, 0x89a4, 0x9ad9, 0xfd4e, 0x8dc0, 0x97f0, 0x93b4,
  0xfd44, 0x9fa7, 0x8dc2, 0x99b6, 0xfd52, 0x8dc1, 0x8e46, 0xa0d1,
  0x9fca, 0x92cf, 0x9cf4, 0x8dc4, 0xfd41, 0x9b4c, 0xfd57, 0xfcef,
  0x9cde, 0xfd42, 0x986c, 0x97f9, 0x9558, 0x985e, 0xfe7b, 0x94cd,
  0x93ee, 0xfd5a, 0x93f5, 0x93ef, 0x8eea, 0x8f5b, 0x8dc6, 0x8dc8,
  0x8dc7, 0x93f7, 0x8dc9, 0xfbf2, 0x9670, 0x8dcb, 0xfd5c, 0x8f65,
  0x8dcd, 0x9da8, 0x94f9, 0x8dce, 0x93ea, 0xfd5f, 0x93f0, 0x9fb6,
  0x8dcf, 0x9763, 0x8dd0, 0x93f1, 0xfd62, 0xfd65, 0x9fdb, 0x93f8,
  0x8bf7, 0xfd66, 0x8bcf, 0x8dd1, 0x8dd2, 0xfd71, 0xfd6f, 0x8dd3,
  0x9fe7, 0x90bd, 0x9fd0, 0x8bd0, 0xfd72, 0x9cae, 0x8bd1, 0x8adb,
  0xfae4, 0x95ce, 0xfd76, 0xfb62, 0x8dd4, 0xfd78, 0x8ee3, 0x9076,
  0x98c6, 0x8dd5, 0x97d1, 0x9eb6, 0xfac7, 0xa042, 0x9873, 0x9ffc,
  0x8dd7, 0x92fb, 0x8dd8, 0x944f, 0x8dd9, 0x896b, 0x97ce, 0xfaf3,
  0xfdae, 0xfbaf, 0x92b7, 0x8dda, 0x9c5a, 0xfdad, 0x8ddc, 0x9444,
  0x8ddd, 0xa0d6, 0x97d5, 0x944a, 0x944d, 0x97cb, 0x8dde, 0x8ddf,
  0x8de0, 0xfef9, 0xfdc0, 0xfcf9, 0xfb7e, 0x92b3, 0xfdaf, 0x8de1,
  0x95d3, 0x89c1, 0xfd68, 0x9cb7, 0x8de3, 0xfac0, 0x8de5, 0xfa64,
  0x8947, 0x8de4, 0x8de7, 0x8de8, 0xfdc7, 0xfdb0, 0x9445, 0x97d6,
  0xfcc6, 0x9844, 0x8de9, 0x8dea, 0xfe50, 0xfdcc, 0x9da7, 0xfdcd,
  0xfdcf, 0x95d2, 0x8ded, 0xfcba, 0xfdc5, 0xfdd2, 0x9cdc, 0x95cf,
  0x8dee, 0xfdd4, 0x96ec, 0x96eb, 0x90b6, 0xfdc3, 0x98ab, 0x96ee,
  0x8df4, 0xfde0, 0x8df6, 0x8df7, 0x8ffa, 0x97d0, 0x8bd2, 0x8df8,
  0x90d9, 0xfaf0, 0xfde1, 0x8df9, 0xfde3, 0x8dfa, 0xfb63, 0x90a6,
  0x9970, 0x91eb, 0x9770, 0x986f, 0xfde2, 0x98f2, 0x9afc, 0x896c,
  0xfdfd, 0x995e, 0x95bd, 0xfde4, 0x91e6, 0xfde5, 0xfde6, 0xfde7,
  0x9454, 0x99b8, 0x97e9, 0x9346, 0x9863, 0x95bc, 0xfded, 0xfdf7,
  0x9870, 0x96f6, 0x8ea9, 0x9451, 0x8e43, 0x8b5a, 0xfdee, 0xfdf0,
  0xfdf4, 0x9bf5, 0x977e, 0x9bd5, 0x9ac3, 0x97c8, 0xa0db, 0x91d0,
  0x9fe4, 0x8fdd, 0x91e9, 0x98e0, 0x92ca, 0x9857, 0xfdeb, 0x9b49,
  0x9d76, 0x9eaf, 0x9ccc, 0xfdea, 0x8df1, 0xfdfb, 0x8e53, 0xfacf,
  0x96f9, 0x98bf, 0x9e49, 0x9bca, 0xfdfe, 0x92dc, 0xfe41, 0x91cc,
  0x91e2, 0xfe44, 0x8bd3, 0xfe47, 0xfe48, 0x9455, 0xfe4b, 0xfe4c,
  0x8dbf, 0x9e78, 0xfe51, 0x9456, 0x9d61, 0x9457, 0x9966, 0x8bd5,
  0xa069, 0x98b4, 0xa049, 0xa04c, 0x9e65, 0x98b5, 0xfe58, 0x9975,
  0xfe53, 0xa065, 0xfe59, 0x98b7, 0x98b8, 0x98ba, 0x98bb, 0x9fbc,
  0xa04a, 0x9ec7, 0x98ae, 0x92d6, 0xfae1, 0x91d4, 0xfade, 0xfe5b,
  0xfe5e, 0xfbd7, 0xfe5a, 0x94c5, 0xfaca, 0x98c1, 0x975c, 0xfe74,
  0x9773, 0xfa46, 0x9764, 0xfe68, 0x964e, 0x9765, 0x89a1, 0x95fa,
  0x92d4, 0xfe69, 0xfb45, 0x98c8, 0x90ef, 0x98c9, 0x98ca, 0x946d,
  0x94b7, 0xfe6c, 0x946b, 0x92fc, 0x95eb, 0xfe73, 0x976e, 0xfe5f,
  0xfdbd, 0x92d5, 0xfeb9, 0xfe71, 0xfbbb, 0x947a, 0x95fb, 0xfe77,
  0xfe79, 0xfe75, 0x945d, 0xfe7c, 0x9344, 0x8ea6, 0x92d3, 0x94b8,
  0xfc71, 0x975e, 0xfea7, 0x946a, 0x93e3, 0x98cf, 0xa0d9, 0xa0bf,
  0xa04d, 0xa0b8, 0xa0ce, 0xa0b7, 0xfea9, 0x89c3, 0xfeab, 0x9df4,
  0x896d, 0x9c7b, 0x98d2, 0x9fa9, 0xfeb2, 0xfeb1, 0x97d9, 0xa0c4,
  0x9476, 0x9978, 0xfeb3, 0x98d3, 0xfeb4, 0x98d4, 0x9fb9, 0x9471,
  0x98d5, 0xfeb8, 0xc6d4, 0xfeba, 0x9e5c, 0xfebb, 0xa044, 0x98d7,
  0x98d8, 0xfebc, 0x9ffe, 0xfebd, 0x9ddd, 0x9ee1, 0x98da, 0x9ddf,
  0xfebf, 0xfebe, 0x9eeb, 0x9e59, 0xa05c, 0xfec0, 0x9477, 0x98dc,
  0xfec1, 0x98de, 0xfec3, 0xfec8, 0xfec9, 0xfeca, 0xfecb, 0x9fc2,
  0x98c4, 0x94b0, 0x94b1, 0xfed1, 0xa0c1, 0xfed2, 0xa0cd, 0xfed3,
  0x98e5, 0xfed6, 0x91e4, 0x8fc7, 0x94ae, 0x8a4f, 0x94b2, 0x8fd4,
  0x98ea, 0xfed8, 0x9de0, 0x98ee, 0x95c4, 0xfce8, 0x98ef, 0xfad8,
  0xfedc, 0xa0ae, 0x9d4c, 0x98f1, 0x98f3, 0x94c1, 0x98f5, 0xfa4f,
  0x96e2, 0x9450, 0x96a2, 0x98f6, 0x96e5, 0x98f7, 0xa046, 0x96e3,
  0x98f8, 0x9ee4, 0xf9d6, 0x94c3, 0x94c2, 0xfee1, 0xfee9, 0x96e4,
  0x89ac, 0x96db, 0xfee2, 0x94c4, 0xfee3, 0xfee7, 0x9ffb, 0x93c9,
  0x94e8, 0xfb56, 0x90c5, 0xa0a8, 0xfee5, 0x98fd, 0x98fb, 0xfee8,
  0x8ebf, 0x8bd8, 0xfef7, 0x8f68, 0x94c6, 0x9dea, 0xfc69, 0x9cda,
  0xfef3, 0x9c72, 0xfef0, 0x89c9, 0x9941, 0x9942, 0xfef5, 0x91d7,
  0x94cc, 0xfef8, 0x97a8, 0xfefc, 0xfdbf, 0xfefe, 0x96d1, 0x94d5,
  0x94d0, 0x9944, 0xa0b3, 0x94cf, 0x9ffa, 0x91e5, 0x9c6a, 0x8e49,
  0x8e4c, 0x8e4d, 0x9a73, 0x9947, 0x8e50, 0x8e4f, 0x9949, 0x8e51,
  0x8e52, 0x9ab2, 0x89a5, 0x994c, 0x9ff8, 0x8e56, 0x994d, 0x91ca,
  0x8e57, 0x94e1, 0x9047, 0x8fd8, 0x8e58, 0x94eb, 0x8e5c, 0x9553,
  0x9fe5, 0x9f56, 0x954f, 0x8e5e, 0x996a, 0x9c64, 0x9cd9, 0x8e5d,
  0x9950, 0x9951, 0x8e62, 0x9952, 0x8e68, 0x8e61, 0x9f59, 0x8bb3,
  0x9f5d, 0x8e66, 0x8e6e, 0x9f64, 0x9953, 0xfab8, 0x9954, 0x8e70,
  0x9f61, 0x8e72, 0xa06b, 0x9f40, 0x94ed, 0x94ee, 0x9fbd, 0x8e7b,
  0x9957, 0x94f7, 0x9f5f, 0x8e73, 0x9f62, 0x94f6, 0x9958, 0x8e75,
  0xf9db, 0x9072, 0x94f8, 0x995a, 0xa0b0, 0x8e79, 0x8e78, 0x94f3,
  0x98af, 0xa0b2, 0x8e7a, 0x995c, 0x8e7c, 0x8e7d, 0x8bd9, 0x89a2,
  0x9ed7, 0xa0b6, 0x9e42, 0x8ea4, 0x8ea7, 0x9542, 0x987d, 0x9755,
  0x8ea8, 0x8eaa, 0x89a3, 0x9960, 0x9962, 0x94fc, 0x9961, 0x94fa,
  0x8eae, 0x8eb2, 0x8eb0, 0x9963, 0x97aa, 0x94fb, 0x8ebb, 0x9876,
  0x8ea1, 0x8eb7, 0x9da6, 0x9eb0, 0x8eb8, 0x9d70, 0x896e, 0x896f,
  0x8970, 0x8971, 0x8972, 0x8973, 0x8974, 0x8975, 0x8ebc, 0x8ebd,
  0x8ebe, 0x9dd1, 0x94fd, 0x8bd7, 0x8bda, 0xa0e2, 0x9fe9, 0xfda8,
  0x8ae7, 0x8ec2, 0x8ec4, 0x9964, 0x9965, 0x954e, 0x98b3, 0x8ecb,
  0x8bdf, 0x8ece, 0x8ecf, 0x9968, 0x9969, 0x996b, 0x8ed1, 0x996c,
  0x8ed4, 0x8ed5, 0x996d, 0xa0be, 0x8ed6, 0xa0bc, 0xa0b5, 0xa0b4,
  0x8be0, 0x89b5, 0x8edd, 0x9e5d, 0x9971, 0x89ae, 0x9de8, 0x9565,
  0x9972, 0x8b5c, 0x89b1, 0xa0c0, 0x8edf, 0x9566, 0x9974, 0x9976,
  0x9977, 0x9979, 0x9dda, 0x8ee0, 0x935c, 0x9de6, 0x8b5f, 0x9563,
  0x9567, 0x9de3, 0x997c, 0x997d, 0x997e, 0x8b5b, 0x99a3, 0x99a4,
  0x99a6, 0x99a8, 0x8abe, 0x9e61, 0x99aa, 0xa0c8, 0x99ab, 0xfec4,
  0x98c2, 0x8ee8, 0xa0ba, 0xfd77, 0x8eee, 0x9ebf, 0x89c2, 0x99ac,
  0x956b, 0x956c, 0x99af, 0x994a, 0x8976, 0x8f48, 0xfbee, 0x99ae,
  0x8efb, 0x8b52, 0x99b0, 0x8977, 0x8f41, 0x99b1, 0x8f49, 0xfa4d,
  0x9de4, 0xfbe9, 0x9b54, 0x99b2, 0x9e68, 0x8f4a, 0x8f42, 0x8f51,
  0x9846, 0x99b4, 0x8ef5, 0xfd55, 0x9ccd, 0x8978, 0x8f53, 0x8f6f,
  0x8e63, 0x8f56, 0xfe76, 0x9fc6, 0xfaac, 0x8f58, 0x9848, 0x99b7,
  0x9665, 0xfa6c, 0x9de7, 0x9e62, 0x96cc, 0x8e67, 0xfc75, 0x987e,
  0xfdb9, 0x97fc, 0x98f9, 0x8f66, 0x956e, 0x9245, 0x8f60, 0x9ed1,
  0xfecd, 0x99b9, 0x8f62, 0x974c, 0x91c7, 0x955f, 0x99bb, 0x8e6d,
  0x8f71, 0x94cb, 0x95b1, 0x9af2, 0x96c3, 0x99bd, 0xa0cf, 0x8f6d,
  0x99be, 0x8ef4, 0x8f72, 0x95e4, 0x99bf, 0x9242, 0xfba5, 0x99c0,
  0xfdb4, 0x8f77, 0x99c1, 0xfab9, 0x8f40, 0xfa44, 0x99c2, 0x8f5c,
  0x99c4, 0x99c5, 0x8f7b, 0x8fa3, 0x99c6, 0x96cd, 0x96c7, 0x8fa5,
  0xfabb, 0x9570, 0x9368, 0x8f7e, 0x8faa, 0xa050, 0x90d3, 0x9556,
  0x8fb8, 0x99c8, 0x8faf, 0x99c9, 0x9579, 0x9f49, 0x99ca, 0x99cb,
  0x9dd5, 0x8fb0, 0xfa7a, 0x9e5f, 0x99cd, 0xa0c9, 0x9adb, 0xa0c6,
  0x8fb4, 0xa0d7, 0xa0c7, 0xa043, 0x8fb5, 0x8fb2, 0xa061, 0x9e5e,
  0x8fb6, 0x9fe8, 0x9cb2, 0x957c, 0x9fc7, 0x8fbb, 0x8fbc, 0x8fec,
  0x8fc0, 0x936a, 0x8be4, 0x9c7c, 0x95a1, 0xfeec, 0x95a3, 0x8fc1,
  0xa052, 0x99d0, 0x8fc3, 0x8fc4, 0x95a4, 0x8fc6, 0x9e60, 0xf9d8,
  0x95a5, 0x9cb3, 0x99d1, 0xfef1, 0x99d2, 0x9cc2, 0x99d3, 0x95a7,
  0x95a9, 0x95a6, 0x9c5d, 0x98e2, 0x8fc9, 0xa0c2, 0x8fca, 0x99d4,
  0xa0b9, 0x9b58, 0x8fcd, 0xa0d4, 0x8fce, 0x8be5, 0x8979, 0x8fd0,
  0x95b6, 0x99d6, 0x95e5, 0x99d7, 0x95b5, 0xa0ca, 0x9ffd, 0xa058,
  0x8fd6, 0x99d8, 0x8fd3, 0x8fe5, 0x8fe9, 0x99d9, 0x927c, 0x9c45,
  0x8fde, 0x8fdf, 0xa04b, 0x8fe2, 0xa0cc, 0x8fe3, 0x8fe4, 0x9bc4,
  0x9bfc, 0x964c, 0x9af6, 0x8fe7, 0x8fe8, 0x8be7, 0x897a, 0x897b,
  0x99da, 0x8fed, 0x95c0, 0xa0cb, 0x9e48, 0x99db, 0x8ff3, 0x8ff9,
  0x95c1, 0xa04e, 0x99dc, 0xa064, 0x8ff7, 0x89b0, 0xa048, 0x8ffb,
  0x8ff6, 0x9ddc, 0x99dd, 0x8be8, 0x92c1, 0x9fd6, 0xa0d2, 0x9040,
  0x8ac4, 0x99e0, 0x9ff0, 0x9ff3, 0x9dbf, 0x9ff6, 0x95c8, 0x9e5a,
  0x99e3, 0x8a4a, 0x9ff1, 0x8aa7, 0x99e6, 0x9ff7, 0x9fed, 0x8a5c,
  0x9dae, 0x95c9, 0x9048, 0x99e8, 0x9049, 0x90b1, 0x904a, 0x99ea,
  0x9bd1, 0x99eb, 0x99ec, 0x99ed, 0x99ee, 0x904c, 0x904d, 0x95cb,
  0x97e2, 0x95cc, 0x9f78, 0x897c, 0x897d, 0x897e, 0x995d, 0x9b5a,
  0x9050, 0x9054, 0xc6d6, 0x9aa8, 0x99ef, 0xfeeb, 0x9da3, 0x9da1,
  0x9943, 0x9945, 0x9d7d, 0x99f0, 0x99f1, 0x99f2, 0x9d60, 0xa0a3,
  0x905b, 0x9edb, 0x9d79, 0x99f3, 0x9062, 0x9f55, 0x9bf9, 0x9065,
  0x96e0, 0x98be, 0x9068, 0x906c, 0x95d8, 0x906a, 0x9fb2, 0x9fae,
  0x9fb0, 0x89ad, 0x906e, 0x9e71, 0x9e4a, 0x9fdc, 0x89ab, 0x9fb8,
  0x9070, 0x8b63, 0x95dc, 0x9071, 0xfc5e, 0x8949, 0x965b, 0x94a6,
  0x8fd5, 0x9e73, 0x9075, 0x99f7, 0x99f9, 0x9663, 0x95b9, 0x94d4,
  0xfcfa, 0x9077, 0x90ab, 0x9d4d, 0x99fa, 0x92e3, 0x97bb, 0x9078,
  0x99fb, 0x97e0, 0x96dc, 0x9ca8, 0x9772, 0x9440, 0x92f2, 0x99fd,
  0x99fc, 0xf9d7, 0x964a, 0x96d8, 0x99fe, 0x904b, 0xfa41, 0x9a40,
  0x975b, 0x9a41, 0x91dd, 0x93fc, 0x9a42, 0x9a43, 0x9659, 0x9a44,
  0x9051, 0x94bf, 0x90a2, 0x9cab, 0x9776, 0xfc55, 0xfe45, 0x94a8,
  0x9a45, 0xfa4b, 0x9de1, 0x96d9, 0x9774, 0xfdf5, 0x92e5, 0x9645,
  0x91da, 0x90a3, 0xa05f, 0x90af, 0x97bf, 0x914c, 0x967a, 0x91de,
  0x9a46, 0xfeb0, 0x9779, 0x946c, 0x9858, 0x9266, 0x93fb, 0x9a47,
  0x9749, 0x9748, 0x934a, 0x9ce2, 0x9264, 0x91df, 0xfb79, 0x96d7,
  0x9343, 0xfdcb, 0xfe7a, 0x91db, 0x97af, 0x95dd, 0x9348, 0x9a4b,
  0xfc45, 0x9a4d, 0x91bc, 0x90e2, 0x90b4, 0x95e1, 0x9a4e, 0x9a4f,
  0xfe40, 0xfe43, 0x96dd, 0x9a51, 0x96a7, 0x90b0, 0x9c4e, 0x9443,
  0x8eba, 0x9a52, 0xfcc1, 0x8be9, 0x9caf, 0x8bfd, 0x9abc, 0x9ab8,
  0x9aae, 0x9aa7, 0x9a53, 0x9d74, 0x8bea, 0x8beb, 0x90b2, 0x95e9,
  0x95e8, 0x95e6, 0x90b5, 0x9a54, 0x90b3, 0x95e7, 0x8b50, 0x8bec,
  0x9a56, 0x8bfb, 0x9a57, 0xa0aa, 0x9fa6, 0x99cc, 0x9c59, 0x99b5,
  0x90be, 0x9faf, 0x95f2, 0x90bf, 0x90c1, 0xfee4, 0x90c4, 0x90c7,
  0x92e4, 0x9f52, 0x90db, 0xa066, 0x90d2, 0x90d4, 0x9a5b, 0x95fd,
  0x8bc4, 0x90de, 0x9ce4, 0x90e1, 0x9e46, 0x9651, 0xfb58, 0x90e6,
  0x9650, 0x90e7, 0x90e8, 0x9a5d, 0x9f7a, 0x9b5c, 0x9f7c, 0xfc52,
  0x90e9, 0x90ea, 0x9a5e, 0x9f76, 0x90eb, 0x90ec, 0x8bee, 0x90ee,
  0x91c6, 0x90f2, 0xfcbc, 0x8a74, 0x9657, 0x9cef, 0x9fdf, 0x90f7,
  0x90f6, 0x9b5e, 0x90f8, 0x90f9, 0xfa6a, 0x8bef, 0x9fe0, 0x9142,
  0x9a62, 0x9569, 0x9144, 0x9143, 0x9141, 0x8bf0, 0x9660, 0x8bf1,
  0x99f6, 0x9149, 0x914a, 0x914b, 0x9a64, 0x8abf, 0x9a66, 0x9a67,
  0x9a69, 0x9a6a, 0x9652, 0x914d, 0x9666, 0x9f7b, 0x9a6b, 0xa06c,
  0x9667, 0x9a6c, 0x9a6d, 0x8bf2, 0x966a, 0xfcea, 0x966c, 0x91c4,
  0x9677, 0x99f4, 0x9a6f, 0x9fab, 0x8ec1, 0x9555, 0x9152, 0x9153,
  0x9155, 0x955d, 0x9671, 0x9c6d, 0x9673, 0x9154, 0x9a71, 0x9156,
  0x966d, 0x9557, 0x89c6, 0x89c7, 0x8a6a, 0x8b57, 0x9fe1, 0x9b5f,
  0xa05d, 0x915b, 0x915c, 0x915e, 0x9f5c, 0x9f57, 0x9f65, 0x9a72,
  0x9160, 0x9f5e, 0x9161, 0x9164, 0x9f41, 0x9169, 0x9168, 0x9a74,
  0x96b2, 0x9a75, 0xfda5, 0x9ee9, 0x8bba, 0x916d, 0xa060, 0x9fde,
  0x9fc3, 0x96b5, 0xa067, 0x96b3, 0x9a76, 0x95d5, 0x9eca, 0x9a77,
  0x9a78, 0x9170, 0x916f, 0x9fa3, 0x9171, 0x96b1, 0x9f63, 0x9f67,
  0x8bb9, 0x9a7a, 0x8b56, 0x9ada, 0x96b0, 0x9a7e, 0x9dde, 0x96ad,
  0x96ae, 0x9ea1, 0x9e50, 0x96af, 0x8bf4, 0x9fa4, 0x96bd, 0x96f4,
  0x96b8, 0xfaa1, 0x91a7, 0xa05e, 0x9a7d, 0x8948, 0x9eb1, 0x9ddb,
  0x95bf, 0x8a73, 0x9efe, 0x917a, 0x917b, 0x9aa3, 0x96c2, 0x9f77,
  0x9aa4, 0x9aa5, 0x91a1, 0x89b8, 0x9173, 0x9aa6, 0x89bd, 0x89b9,
  0x917d, 0x96bb, 0x9ff2, 0x8bf5, 0x9aa9, 0x9f54, 0x9fe3, 0x9eed,
  0x91aa, 0x91ab, 0xa070, 0x9f6d, 0x91ac, 0x91ad, 0xa0fd, 0x9fe2,
  0x91af, 0x9e41, 0x9aaa, 0x91b0, 0x9aab, 0x9aac, 0x9a4a, 0x91b2,
  0x8bf6, 0x9aad, 0x89b6, 0x9aaf, 0x9ab0, 0x9ab1, 0x9aa1, 0x91b9,
  0x91ba, 0x91be, 0xa041, 0x8bb7, 0x91c0, 0x9ab3, 0x91c3, 0xa0fc,
  0x9fee, 0x9f69, 0x91c8, 0x91c9, 0x8de6, 0x91cb, 0x89c8, 0x8daa,
  0x9fdd, 0xc8a1, 0xc8a3, 0x8bf8, 0xc8d0, 0xc8cf, 0xc6e4, 0xc6e5,
  0xc8cd, 0xc8ce, 0xf9fe, 0x9c71, 0x9375, 0x9376, 0x9548, 0x8ec6,
  0x8bc5, 0x8bfa, 0xc87c, 0x9ab4, 0x884e, 0x884b, 0xc87a, 0x8848,
  0x8847, 0xa0f6, 0x8845, 0x8853, 0xfcad, 0x8aad, 0x9272, 0xfc47,
  0x94df, 0x9fd1, 0xfbcb, 0x927d, 0x98a4, 0x94e7, 0x90cb, 0x927b,
  0x94d8, 0xfc5f, 0xfa54, 0x9ab5, 0x96da, 0x9279, 0xfa74, 0x9275,
  0x8dfb, 0x8a49, 0x92df, 0x9b7c, 0xfa63, 0xfa60, 0x926d, 0xfa62,
  0x9ab6, 0x976b, 0xfd6a, 0xfd54, 0x9273, 0x97d8, 0x9fbb, 0x9342,
  0x9276, 0xfa65, 0x926c, 0xfa6e, 0x9ee0, 0x92c0, 0x92bf, 0x92be,
  0x9aba, 0x8ab3, 0x9775, 0xfa40, 0xfa76, 0xfbd0, 0xfa7b, 0xfe6d,
  0x9bb3, 0x89cc, 0x9abe, 0xfa42, 0x92bc, 0x945c, 0x9bb5, 0x9abf,
  0x98a7, 0x97a4, 0x90fd, 0xfc7b, 0x9ac0, 0x92c3, 0x8aaa, 0x9bd0,
  0x9550, 0x92c6, 0x98a6, 0x9546, 0xfd63, 0xfac2, 0x9ec3, 0x89b2,
  0x9c66, 0x9053, 0x97c1, 0x9ac4, 0x9ac5, 0x8eef, 0xfae9, 0x9262,
  0x8af7, 0x9ac6, 0x92e1, 0x9ac9, 0xfac6, 0x97a5, 0x9acb, 0xfa72,
  0x8a5e, 0x94e0, 0x92cc, 0x8ae5, 0xfe5c, 0x9acc, 0x9df9, 0x8a43,
  0x8aa6, 0x9acd, 0x9ace, 0xfaee, 0x9bcc, 0x9acf, 0x9ad1, 0x9dfa,
  0x9d7c, 0x9ad3, 0x97a6, 0x995f, 0xfbf6, 0x9fc5, 0x8a59, 0x8b6b,
  0x9ad4, 0x9ad5, 0x97a2, 0x8a44, 0x9f4a, 0x90a1, 0xfda4, 0x8a64,
  0x8af2, 0x8af8, 0x9dd8, 0x94d6, 0xfafe, 0xfba7, 0x9ad6, 0x9f4d,
  0xfaf6, 0x8a57, 0x8b43, 0x8b44, 0x8ab6, 0x8ac0, 0x9e54, 0x9ad7,
  0x9ad8, 0x9adc, 0x8aca, 0x9ea8, 0x9263, 0x9add, 0x8b65, 0x8b6f,
  0x8b7e, 0x8f43, 0x92d0, 0x8af4, 0x9dbe, 0x9ae1, 0xfcde, 0x9dfd,
  0x8b66, 0x8b70, 0x8b75, 0x8ae4, 0x8ba4, 0x8aed, 0x8a5d, 0x8b48,
  0x9ded, 0x9e40, 0x8aef, 0x8af6, 0x9e76, 0x9ee3, 0x9ade, 0x8dfe,
  0xfafc, 0x9cb1, 0x9e77, 0x8b64, 0x8b67, 0x974b, 0x9653, 0x9ae0,
  0x8b4a, 0x8af1, 0x8ad7, 0xa0ab, 0x8ab5, 0x8a5f, 0x8aee, 0x9adf,
  0x8afe, 0x8a58, 0x8ba3, 0x8ba7, 0x9ae3, 0x9261, 0x9dd7, 0x9e7d,
  0x9ea7, 0x9eab, 0x9042, 0x8b79, 0x8b7a, 0x9ae6, 0x9ae5, 0x8a7e,
  0x9e44, 0x9ae7, 0x8a7c, 0x8b71, 0x9ae9, 0x9aea, 0x9aeb, 0x8abd,
  0xfb4e, 0x9aed, 0x8af9, 0x9e63, 0x8b49, 0x8ace, 0x8b6e, 0x8ae8,
  0x9aee, 0x92ce, 0x8a5a, 0x8b7b, 0x8b7c, 0x9aef, 0x9af0, 0x8afa,
  0x8941, 0x8b72, 0x8af3, 0x8ba8, 0x9eae, 0x9e72, 0xfb73, 0xfb5f,
  0x90ba, 0x91fe, 0x9ef6, 0x97ed, 0x9af3, 0xa0ee, 0x967c, 0x9345,
  0x986e, 0xfa56, 0x9af5, 0xfc4b, 0x9af4, 0xfede, 0xfcb7, 0x97f1,
  0x97c7, 0x9ccb, 0x9240, 0x9ce8, 0x91fd, 0x974e, 0xfb68, 0x976c,
  0x97e8, 0xfb6a, 0x8b74, 0x8ee7, 0xfdc8, 0x9241, 0x96a1, 0x8ef3,
  0x9af7, 0x8fa6, 0xfad6, 0x9cc7, 0xfad7, 0x9af8, 0xfba1, 0x8ec5,
  0xfba4, 0xfbc2, 0x9ac1, 0x91fa, 0xfedb, 0x97ab, 0x9147, 0xfbb1,
  0x8fea, 0x94d2, 0xfe61, 0xface, 0x92ed, 0x91f3, 0x93c6, 0x935a,
  0xfafb, 0x92ef, 0xfac8, 0x9847, 0x9366, 0x9855, 0x96e6, 0x9f43,
  0x9faa, 0x94da, 0x92ee, 0xfcaf, 0xfbfb, 0x8ef9, 0x91f6, 0x9364,
  0x94f5, 0x9cb6, 0xfbad, 0x984e, 0x8f44, 0x96fd, 0x9af9, 0x9afa,
  0x9769, 0x95d4, 0x984b, 0xfbaa, 0x987c, 0x91ea, 0x9daf, 0x9dc5,
  0x91f1, 0x8eb1, 0x97a9, 0xfbac, 0xfcb8, 0x9cb9, 0xfbb0, 0xfcd2,
  0x93cb, 0x9afd, 0x91f4, 0x8bac, 0xa055, 0x9574, 0x95be, 0x97ad,
  0x8ee9, 0x92f8, 0x97be, 0x916c, 0x94aa, 0xfc63, 0x9dc6, 0x97b5,
  0x92b8, 0x91ef, 0xfea6, 0x9760, 0x9358, 0x9576, 0x8fac, 0x91ec,
  0x97b4, 0x91f7, 0x974a, 0xfb49, 0x9578, 0x93bc, 0x91d6, 0x9355,
  0x9356, 0x9851, 0x8ff8, 0xfbc0, 0x93f2, 0x90d0, 0x9c44, 0x9255,
  0x9363, 0x91a5, 0xa0ed, 0xfd6b, 0x9afe, 0x9351, 0xfa78, 0xfea8,
  0x9350, 0xfa4c, 0x92f7, 0x9b40, 0xfbce, 0x9b41, 0xfead, 0xfbd5,
  0x8bc2, 0x9a7c, 0x9b42, 0x9b43, 0x9e79, 0xfbd9, 0x9b44, 0xa0a7,
  0x9bf3, 0x935e, 0x89cb, 0x9f53, 0x93d7, 0xfbe1, 0xfed0, 0xfbe2,
  0xfce3, 0x9074, 0xfbe6, 0x9bb7, 0x9b45, 0x9b47, 0x9f50, 0x9b48,
  0xfc5b, 0x98a9, 0x9cfd, 0x884c, 0x9b4b, 0xfbec, 0x9ba8, 0x8ad5,
  0xfa73, 0xfd59, 0x91a2, 0xfbed, 0x9ca9, 0x8aa8, 0x9bc3, 0x8ae1,
  0x9b4e, 0x95d0, 0x905f, 0x97ee, 0xfc4e, 0x9b4f, 0x9b50, 0x9ec6,
  0xfc50, 0xfd73, 0xfda7, 0x9da2, 0xfa58, 0xfa5e, 0xa059, 0xfa75,
  0xfbbe, 0x9ca2, 0x9370, 0x9371, 0x9377, 0xfeef, 0x936d, 0xfc5d,
  0x90b8, 0x8afc, 0xfb41, 0x9e6b, 0x94e3, 0x8ee2, 0x8ed7, 0x9c4d,
  0x96a3, 0x9b51, 0x8ac3, 0x96aa, 0xfc68, 0x8b6d, 0xfd67, 0x8ae9,
  0xfca1, 0x936c, 0x9b52, 0xfe70, 0xfca8, 0xfce9, 0x9cb4, 0x8aea,
  0x9b53, 0x9b55, 0x96ab, 0xfca7, 0x9b56, 0x8abc, 0x8acb, 0x9b57,
  0x89cd, 0x9b59, 0x9b5b, 0x93a5, 0x9b5d, 0x9e4f, 0x93a3, 0x8a7b,
  0x8b42, 0x9750, 0x8fb3, 0x8a50, 0x9b60, 0x8b45, 0x8b46, 0x9dfe,
  0x9b62, 0x937b, 0x93b1, 0x8a60, 0x8ad8, 0x9b63, 0x8a69, 0x8a47,
  0x8acc, 0x937c, 0x9b65, 0x9b66, 0x8a72, 0x8a7a, 0x93af, 0x8ab0,
  0x9b68, 0x9ea3, 0xfaec, 0x8b77, 0x9b67, 0x8b59, 0xfcb1, 0xfcbb,
  0x9b69, 0x93a8, 0x8ae0, 0x9e51, 0x8f5f, 0x9b6a, 0x9b6b, 0x97ec,
  0x9b6c, 0xfe4e, 0xfdc2, 0x9b6d, 0x9167, 0xfccc, 0x93b6, 0x90e4,
  0x90e5, 0x9ef2, 0x93ca, 0x8bbc, 0x8f46, 0x93cf, 0xfcdb, 0xfcdc,
  0x93c0, 0xfce6, 0x96e7, 0xfcd8, 0xfcd9, 0xfda6, 0x93ce, 0x95f1,
  0x9ce9, 0xfce4, 0x94af, 0xfa77, 0x93cc, 0x905a, 0x93bf, 0xfb51,
  0x93b9, 0xfed7, 0x93b7, 0x93d9, 0x93bb, 0x93da, 0x98a3, 0x90d1,
  0x9b6e, 0xfa70, 0x9beb, 0x9b6f, 0xfcfc, 0x8b40, 0xa07b, 0x97f7,
  0x93e2, 0xfcd6, 0x9559, 0x93a6, 0xfd40, 0x935f, 0x97f2, 0x9c76,
  0x8ef8, 0x8f47, 0x9b74, 0x92b4, 0x91ed, 0x96d2, 0xfd46, 0x8f4f,
  0x9549, 0x9b75, 0xfa5c, 0x9b79, 0xfd4b, 0x96d3, 0xfd58, 0x945f,
  0xa0f5, 0x9243, 0x97fa, 0x9dd9, 0x97f4, 0x924d, 0xfd5b, 0x9b7a,
  0x9ed5, 0xfaae, 0x9cc9, 0x9258, 0x8ec8, 0x94b4, 0x93e1, 0x93df,
  0xfcf0, 0x93ec, 0x97f6, 0x96cf, 0x93de, 0x8acf, 0x9ba2, 0xfd69,
  0x9352, 0x98a2, 0xfd6e, 0xfa7c, 0x93fa, 0x907c, 0x8f67, 0x9db7,
  0xa0e9, 0xfa4e, 0xfda1, 0x9e74, 0x9fbf, 0x9ecb, 0x9bb9, 0x9dd4,
  0x97b9, 0x8ef1, 0x957b, 0x9ed2, 0x9753, 0x96a4, 0x8fbe, 0x94d9,
  0x9058, 0xfd79, 0xfd7b, 0x8eda, 0x8efa, 0x9ba5, 0x9ed9, 0x97d4,
  0x90bb, 0xfdbc, 0xfdc6, 0x9248, 0x92b5, 0x9dc1, 0x92b9, 0x92a6,
  0x8f4b, 0x9ba6, 0x92b6, 0x8e40, 0x9ed8, 0x945e, 0x985f, 0x94ce,
  0x924a, 0xfd70, 0x9467, 0x8dec, 0x9bd8, 0x9448, 0xfac1, 0x9cf7,
  0xfdbe, 0x8fda, 0xfdd9, 0xfc7e, 0x93f9, 0xfa43, 0xfaeb, 0xfac3,
  0x97d3, 0x95f9, 0x9c48, 0xfdd8, 0xa0d8, 0xfdd7, 0xfb4a, 0x9baf,
  0x944b, 0xfdc9, 0x8eac, 0xfdb2, 0x925a, 0xfcbd, 0x92d9, 0xfdd5,
  0x92dd, 0x9259, 0x96ba, 0x925b, 0x9bab, 0xfdda, 0xfdde, 0xfdd3,
  0xfdd6, 0xfddc, 0xfddd, 0x90fe, 0xfea1, 0x8bad, 0x9cd8, 0x9e6d,
  0xfd7c, 0xfb61, 0x96f8, 0x96f0, 0xfcf4, 0xfe60, 0x9852, 0x964f,
  0x916e, 0x986d, 0x9864, 0x9453, 0xfdec, 0xfb78, 0x95ba, 0x985d,
  0x92f9, 0x985a, 0xfdf6, 0x93d0, 0x9862, 0x9bad, 0x974f, 0x9bae,
  0x9452, 0x9bb0, 0x91d2, 0x97ea, 0xfb6b, 0x91b1, 0xfdf3, 0x92cb,
  0x9bb1, 0xfcec, 0x986b, 0x9751, 0x9871, 0x95ef, 0x9ef3, 0x91e8,
  0x9bba, 0xfb4c, 0x926a, 0xfdf8, 0x9861, 0x91e7, 0x93ed, 0x9744,
  0x91e1, 0xfbf5, 0x9869, 0x8a62, 0x9bbb, 0x9c55, 0x8e77, 0x8ab2,
  0x9ebc, 0x93e6, 0x93a2, 0x9bbd, 0x94b3, 0x937d, 0x9e66, 0x9459,
  0x9bbf, 0x9458, 0x9ea5, 0x9bc7, 0xfe54, 0x8e74, 0x8bd6, 0x94b6,
  0xfd74, 0x98c0, 0x94a5, 0x9bc8, 0x95ed, 0xfd7e, 0xfbeb, 0xfd7d,
  0x976f, 0x9461, 0x9fc1, 0x95d7, 0xfa52, 0x9c58, 0x9f68, 0x9be7,
  0xfcce, 0x96e8, 0xfa49, 0x97a1, 0x954d, 0x9ef8, 0xfe49, 0x91ce,
  0x9771, 0xfdb1, 0xfc6e, 0x9cf2, 0x93b8, 0x9043, 0x9759, 0x94d7,
  0xfe66, 0x947d, 0xfc6f, 0x9246, 0xfa6d, 0x8ef7, 0xfbb7, 0x947c,
  0x92cd, 0x97b2, 0xfe65, 0x967e, 0x9758, 0x9b77, 0x91cf, 0x94a4,
  0x9cad, 0x8bab, 0x96d5, 0xfcb3, 0x93ae, 0x976d, 0x9446, 0x95f7,
  0x9c46, 0x955b, 0x91d1, 0x94f4, 0xfe67, 0x92a5, 0xfedf, 0x9bc9,
  0xfced, 0xfdfa, 0xfcc8, 0xfe62, 0x91fc, 0xfe6b, 0xfdf9, 0xfcc7,
  0x914e, 0x9cb8, 0x9767, 0x95ee, 0x9bb2, 0x9460, 0x94a2, 0x9875,
  0x97ac, 0x91d3, 0x987b, 0x8eeb, 0x976a, 0x965e, 0x97eb, 0x9ff9,
  0x95f8, 0xfea2, 0x8fe6, 0xfe7e, 0x9da4, 0x9768, 0x8eec, 0x94bd,
  0x945b, 0x9cf6, 0xfaa7, 0x9bd9, 0xfa5d, 0x9656, 0x9762, 0x94ba,
  0xa04f, 0x92d8, 0x9bcb, 0x94bb, 0x9d5f, 0x90cf, 0x9465, 0x9f4c,
  0x90d8, 0x9ebe, 0xfb6d, 0x95ca, 0x9dc2, 0x97f8, 0x8ffc, 0x9473,
  0x9474, 0xfeb7, 0x8a4b, 0x8a55, 0x8b69, 0x8adc, 0x8b76, 0x9bce,
  0x8a68, 0xa0f8, 0x98df, 0xfeb5, 0x9bcf, 0x96fb, 0x9bfb, 0x9ece,
  0x8ee5, 0x9e7b, 0x9bd2, 0x8aa5, 0xfece, 0x8a45, 0x9dfc, 0xfecf,
  0x8ba5, 0x8aec, 0xfce0, 0x94ad, 0xfed5, 0x94ac, 0xfc5a, 0x9bd6,
  0x8a6f, 0x8ba9, 0x8e5f, 0x9dcb, 0xfce7, 0x9bd7, 0x93c8, 0x91f0,
  0x8fe0, 0x9bdb, 0x90ed, 0x9bdc, 0xa0ec, 0x98fa, 0x9be0, 0x93c7,
  0x9249, 0x96e1, 0x9be2, 0x9be4, 0x8fe1, 0x9be5, 0x94c0, 0x93c3,
  0x93c5, 0x9079, 0x977b, 0x907e, 0xfee6, 0xfe46, 0x9db8, 0x9270,
  0x95a8, 0x94c8, 0x98b9, 0x9140, 0xfcbe, 0x9157, 0x8bb2, 0xfadf,
  0x9be6, 0x9643, 0x8e44, 0x9c4f, 0xfef4, 0x9be8, 0x93dc, 0x966f,
  0x8e4a, 0x9bed, 0x92f6, 0x9db9, 0x8e4e, 0xfbcf, 0x9ec2, 0x94e5,
  0x9bf0, 0x94e4, 0x9551, 0x8bbb, 0x9bf1, 0x94f0, 0x8e64, 0x94ea,
  0x8f61, 0x9b64, 0x8e5b, 0x9bf2, 0x9fbe, 0x9dc9, 0x8e6c, 0x8f73,
  0x8f75, 0x8e71, 0x8e60, 0x8e6a, 0x9552, 0x9554, 0x8ad4, 0x9dbb,
  0x9543, 0x92fe, 0x94f2, 0x94f1, 0xa0ea, 0x9dd2, 0xa0b1, 0x91f8,
  0x9462, 0x9ba4, 0x8ead, 0x9ead, 0x96d0, 0xfeee, 0x8ab4, 0x9757,
  0x8a77, 0x9bf7, 0x8eb5, 0xa06d, 0x8eb6, 0x9756, 0x9540, 0xa0f3,
  0x94be, 0x9bfa, 0xfddf, 0x9dbc, 0x94fe, 0x8bdb, 0xa0fe, 0x8ec0,
  0x9f47, 0x8bde, 0xa0fb, 0x8ec3, 0x9649, 0xfec2, 0x954c, 0x9bfd,
  0x90cc, 0x9c60, 0x954b, 0x9bfe, 0x9c70, 0x9c43, 0x9c47, 0x8ecc,
  0x8e54, 0x8ee4, 0x9c49, 0x8b5e, 0x955e, 0x955c, 0x9c4b, 0x8be1,
  0x8ed9, 0x9db4, 0x925f, 0x9c4c, 0x8aa1, 0x8edb, 0x9c56, 0x8aa2,
  0x9754, 0x9c5e, 0x9ed4, 0x9568, 0xa0c3, 0x8ae6, 0xa0f7, 0x9c61,
  0x9c5f, 0xfc4d, 0x9e5b, 0x9e69, 0x9c63, 0xfec7, 0xfec6, 0x9c67,
  0x9c69, 0x8be2, 0x9165, 0x9ce7, 0x8a54, 0x9c6c, 0x9c6e, 0xfe5d,
  0x9c73, 0x956a, 0x956d, 0x8ef0, 0x8f4d, 0x8ef6, 0xfabc, 0xfbda,
  0x8b4c, 0xfd75, 0x9bdd, 0xfaf5, 0x9c74, 0x9545, 0x96c6, 0x8f6a,
  0x8f4e, 0x9c78, 0xfa55, 0x97e4, 0x9c41, 0x925c, 0x96fa, 0xfb66,
  0x8e65, 0x9849, 0xfba8, 0x9842, 0x9c7a, 0x97fb, 0x90ca, 0x9c5b,
  0x974d, 0x8ed3, 0x9561, 0x9f4b, 0x9fb5, 0x93d2, 0xfdaa, 0x9840,
  0x9146, 0x9867, 0xfa5a, 0xfba9, 0x9841, 0xfcfd, 0xfdab, 0x91bd,
  0x8f4c, 0x96c9, 0x8f55, 0xfbae, 0x956f, 0x9c7d, 0xa0f0, 0x946f,
  0xfdac, 0x96cb, 0x96ce, 0xa056, 0x9ce1, 0x96c4, 0x8f5e, 0x8f6c,
  0x8ea3, 0xfbb3, 0xfc53, 0xfdb3, 0x8f6b, 0x96ca, 0x8f79, 0x9e6f,
  0xa0c5, 0xfc78, 0x8e42, 0x8f5a, 0x90c2, 0x8ea5, 0x9061, 0x924f,
  0x9373, 0xfdb5, 0xfecc, 0xfbbd, 0x9843, 0x96c5, 0x89bc, 0x9ca3,
  0x924b, 0x984a, 0x8fa4, 0xa0f1, 0x9efb, 0x9cd2, 0x8fa7, 0xfc5c,
  0x9845, 0x9046, 0xfefa, 0x9560, 0x9f48, 0x9247, 0x90fb, 0x9ca4,
  0x9571, 0x9ca6, 0x9ca7, 0x9caa, 0x9ed3, 0x9e70, 0x9cac, 0x8fae,
  0x957d, 0x9cb0, 0x97b6, 0xa0bd, 0x8adf, 0x9eaa, 0x8fbd, 0x8fbf,
  0x9369, 0x9ba7, 0xc8a4, 0xfeea, 0x9be1, 0x8b41, 0x9db6, 0xa0eb,
  0x9ba3, 0x8ba1, 0x8fc8, 0x894c, 0x9860, 0x94c7, 0x8b58, 0x95ab,
  0x95aa, 0x9cc3, 0x9cc4, 0x93d6, 0x9dac, 0x8be6, 0x8a71, 0x8fd1,
  0x99d5, 0x90f4, 0x8aa3, 0x9cce, 0x9cd4, 0x9cd5, 0xfbc8, 0x9db3,
  0xfc70, 0x8fd7, 0x9b73, 0xfa5b, 0x8fd2, 0x9064, 0x98b6, 0x9668,
  0x9cd6, 0x98bd, 0x8fdc, 0xfef6, 0x8fd9, 0x9541, 0x97f3, 0x9bf8,
  0x9e6c, 0x8ff2, 0x8fee, 0x9cd7, 0x9e6e, 0x8a40, 0x8fef, 0x8ff4,
  0x8ff5, 0x95c2, 0x986a, 0x97cf, 0x9ee5, 0x9e7c, 0x9041, 0x9cdb,
  0x9441, 0x9ce6, 0x9db0, 0x9cea, 0x9ced, 0x9cfa, 0x8b62, 0x8a4e,
  0x9cca, 0x8a66, 0x9cfb, 0x9cfc, 0x9cfe, 0x8a53, 0x9ce5, 0x9d40,
  0x9d41, 0x9045, 0x8b73, 0x97ca, 0x9d42, 0x8a61, 0x8bae, 0x8ad2,
  0x8ba2, 0x9df2, 0x9d43, 0x9cdf, 0x9d44, 0x8eca, 0x904e, 0x8eb3,
  0x9ff5, 0x9d45, 0x904f, 0x9d47, 0x89ca, 0x9cb5, 0xfbfe, 0x905e,
  0x9063, 0x9057, 0x9066, 0x9bc0, 0xfce5, 0x9162, 0x9067, 0x8fa1,
  0x8fa2, 0x9d48, 0xfad3, 0x905d, 0x90b9, 0x906b, 0x9069, 0xfe57,
  0xfe55, 0x9073, 0x9bef, 0x9cf0, 0x9d4b, 0xfed9, 0xfeda, 0x91e0,
  0x91d8, 0x9646, 0x9360, 0xfa53, 0x9cd3, 0x9d4e, 0xfb40, 0x8de2,
  0x9442, 0x9056, 0x9865, 0xfa4a, 0x9d50, 0x9d52, 0x95af, 0x975a,
  0x9349, 0x9747, 0xa0f4, 0x9778, 0x8fcf, 0xfc60, 0xfc56, 0x91dc,
  0x9661, 0x92ec, 0x935d, 0x8ede, 0x96fe, 0xfd4f, 0x95de, 0x98b0,
  0xa040, 0x97bd, 0x977d, 0x97f5, 0x9bac, 0xfada, 0x92c2, 0x97b1,
  0x907b, 0x93fe, 0x947b, 0x9777, 0xfabe, 0xfd43, 0x90c6, 0x90a4,
  0x90a8, 0x94a9, 0x90a9, 0x95e0, 0x907d, 0x9265, 0xfdba, 0x93c4,
  0xfeed, 0x9dab, 0xa0e3, 0x9648, 0x9d53, 0x8aa9, 0x9bc5, 0x965d,
  0x975f, 0x965f, 0x966e, 0xfb5d, 0x9db1, 0xfea3, 0x9db2, 0x95ae,
  0xfca3, 0xa0a2, 0x9655, 0x9d54, 0x9341, 0x95ad, 0x91d5, 0x977a,
  0xfdfc, 0x8e47, 0x93fd, 0x90a5, 0x90ac, 0x95ac, 0x90ae, 0xfea5,
  0x9d56, 0x97e3, 0x95e2, 0x9466, 0x9647, 0x91b8, 0x9cec, 0x90ad,
  0x95e3, 0x8b4f, 0x8ae3, 0x8b4d, 0x95ea, 0x8b4e, 0x8bed, 0x91d9,
  0xa0a4, 0x95f5, 0x95f4, 0x9fb3, 0xfeaf, 0xfe72, 0x927a, 0xfeac,
  0x95f3, 0x9d58, 0x9372, 0x91c5, 0x9642, 0x90cd, 0x95fe, 0x9159,
  0x9c65, 0x97cc, 0x90ce, 0x9d59, 0xfcf5, 0xfefd, 0x9d5b, 0x9d5c,
  0x937e, 0x98ac, 0x9d5e, 0xfdd0, 0xfd60, 0x9ccf, 0x90dd, 0x90e0,
  0x90f3, 0x98b1, 0x90f0, 0x93bd, 0x95b7, 0x9f46, 0x8e4b, 0x9658,
  0x8a4c, 0x9d63, 0x9ecf, 0x9d65, 0x9d66, 0x965a, 0x9d64, 0x8a6c,
  0x8ad9, 0x9d67, 0x8a70, 0x8bf3, 0x9150, 0x9cc1, 0x9d68, 0x93a7,
  0x9674, 0xa0ef, 0x9151, 0x96c1, 0x9676, 0x9d69, 0xfca4, 0x9d6a,
  0x924e, 0x9d6b, 0x9bc1, 0x9d6c, 0x8a65, 0x915d, 0x9d6d, 0x915a,
  0x9cc0, 0x916a, 0x9d6e, 0x9ea6, 0x9dcd, 0x9d6f, 0x89bb, 0x9ef9,
  0x96b4, 0x9172, 0x9ec8, 0x8b55, 0x9d71, 0x9d72, 0x9ecc, 0x9174,
  0x9ed0, 0x905c, 0x8ed2, 0x91a8, 0x9177, 0x96bf, 0x96c0, 0x8fb1,
  0x96b7, 0x9178, 0x89be, 0x917c, 0xfb77, 0x9175, 0x91a3, 0x9176,
  0x96be, 0x9179, 0x96b6, 0x91a4, 0x91a6, 0x9d75, 0x9052, 0xa045,
  0x91a9, 0x98aa, 0x8baa, 0x9cdd, 0x9d77, 0x8940, 0x9eec, 0x93aa,
  0x9478, 0x9d7a, 0x8ac9, 0x8b4b, 0x9fec, 0x8ae2, 0x9e75, 0x9874,
  0x9ac8, 0xa047, 0x8bc3, 0xfc48, 0xfc77, 0x9c52, 0x8efd, 0x8fa8,
  0x957a, 0x8ff0,
};

static const Summary16 hkscs1999_uni2indx_page00[45] = {
  /* 0x0000 */
  {    0, 0x0000 }, {    0, 0x0000 }, {    0, 0x0000 }, {    0, 0x0000 },
  {    0, 0x0000 }, {    0, 0x0000 }, {    0, 0x0000 }, {    0, 0x0000 },
  {    0, 0x0000 }, {    0, 0x0000 }, {    0, 0x0100 }, {    1, 0x0000 },
  {    1, 0x0703 }, {    6, 0x000c }, {    8, 0x3703 }, {   15, 0x170c },
  /* 0x0100 */
  {   21, 0x0003 }, {   23, 0x0c0c }, {   27, 0x0800 }, {   28, 0x0000 },
  {   28, 0x3800 }, {   31, 0x0008 }, {   32, 0x0800 }, {   33, 0x0000 },
  {   33, 0x0000 }, {   33, 0x0000 }, {   33, 0x0000 }, {   33, 0x0000 },
  {   33, 0x6000 }, {   35, 0x1557 }, {   43, 0x0000 }, {   43, 0x0000 },
  /* 0x0200 */
  {   43, 0x0000 }, {   43, 0x0000 }, {   43, 0x0000 }, {   43, 0x0000 },
  {   43, 0x0000 }, {   43, 0x0813 }, {   47, 0x0402 }, {   49, 0x0020 },
  {   50, 0x0408 }, {   52, 0x0000 }, {   52, 0x0000 }, {   52, 0x0000 },
  {   52, 0x0040 },
};
static const Summary16 hkscs1999_uni2indx_page04[6] = {
  /* 0x0400 */
  {   53, 0x0002 }, {   54, 0xffff }, {   70, 0xffff }, {   86, 0xffff },
  {  102, 0xffff }, {  118, 0x0002 },
};
static const Summary16 hkscs1999_uni2indx_page1e[13] = {
  /* 0x1e00 */
  {  119, 0x0000 }, {  119, 0x0000 }, {  119, 0x0000 }, {  119, 0x0000 },
  {  119, 0x0000 }, {  119, 0x0000 }, {  119, 0x0000 }, {  119, 0x0000 },
  {  119, 0x0000 }, {  119, 0x0000 }, {  119, 0x0000 }, {  119, 0xc000 },
  {  121, 0x0003 },
};
static const Summary16 hkscs1999_uni2indx_page21[15] = {
  /* 0x2100 */
  {  123, 0x0000 }, {  123, 0x0040 }, {  124, 0x0002 }, {  125, 0x0000 },
  {  125, 0x0000 }, {  125, 0x0000 }, {  125, 0x0000 }, {  125, 0x03ff },
  {  135, 0x0000 }, {  135, 0x0000 }, {  135, 0x0000 }, {  135, 0x0300 },
  {  137, 0x0000 }, {  137, 0x0000 }, {  137, 0x0080 },
};
static const Summary16 hkscs1999_uni2indx_page23[40] = {
  /* 0x2300 */
  {  138, 0x0000 }, {  138, 0x0000 }, {  138, 0x0000 }, {  138, 0x0000 },
  {  138, 0x0000 }, {  138, 0x0000 }, {  138, 0x0000 }, {  138, 0x0000 },
  {  138, 0x0000 }, {  138, 0x0000 }, {  138, 0x0000 }, {  138, 0x0000 },
  {  138, 0x0000 }, {  138, 0x0c00 }, {  140, 0x0000 }, {  140, 0x0000 },
  /* 0x2400 */
  {  140, 0x0000 }, {  140, 0x0000 }, {  140, 0x0000 }, {  140, 0x0000 },
  {  140, 0x0000 }, {  140, 0x0000 }, {  140, 0x03ff }, {  150, 0x3ff0 },
  {  160, 0x0000 }, {  160, 0x0000 }, {  160, 0x0000 }, {  160, 0x0000 },
  {  160, 0x0000 }, {  160, 0x0000 }, {  160, 0x0000 }, {  160, 0x0000 },
  /* 0x2500 */
  {  160, 0x0000 }, {  160, 0x0000 }, {  160, 0x0000 }, {  160, 0x0000 },
  {  160, 0x0000 }, {  160, 0xffff }, {  176, 0xffff }, {  192, 0x0001 },
};
static const Summary16 hkscs1999_uni2indx_page27[4] = {
  /* 0x2700 */
  {  193, 0x0000 }, {  193, 0x0000 }, {  193, 0x0000 }, {  193, 0x2000 },
};
static const Summary16 hkscs1999_uni2indx_page2e[68] = {
  /* 0x2e00 */
  {  194, 0x0000 }, {  194, 0x0000 }, {  194, 0x0000 }, {  194, 0x0000 },
  {  194, 0x0000 }, {  194, 0x0000 }, {  194, 0x0000 }, {  194, 0x0000 },
  {  194, 0x35d1 }, {  202, 0x3020 }, {  205, 0x54a0 }, {  210, 0x5040 },
  {  213, 0xb440 }, {  218, 0x40c0 }, {  221, 0x0008 }, {  222, 0x0000 },
  /* 0x2f00 */
  {  222, 0x0000 }, {  222, 0x0000 }, {  222, 0x0000 }, {  222, 0x0008 },
  {  223, 0x0000 }, {  223, 0x0000 }, {  223, 0x0000 }, {  223, 0x0000 },
  {  223, 0x0000 }, {  223, 0x0000 }, {  223, 0x0000 }, {  223, 0x0000 },
  {  223, 0x0000 }, {  223, 0x0000 }, {  223, 0x0000 }, {  223, 0x0000 },
  /* 0x3000 */
  {  223, 0x00e0 }, {  226, 0x0000 }, {  226, 0x0000 }, {  226, 0x0000 },
  {  226, 0xfffe }, {  241, 0xffff }, {  257, 0xffff }, {  273, 0xffff },
  {  289, 0xffff }, {  305, 0x780f }, {  313, 0xfffe }, {  328, 0xffff },
  {  344, 0xffff }, {  360, 0xffff }, {  376, 0xffff }, {  392, 0x707f },
  /* 0x3100 */
  {  402, 0x0000 }, {  402, 0x0000 }, {  402, 0x0000 }, {  402, 0x0000 },
  {  402, 0x0000 }, {  402, 0x0000 }, {  402, 0x0000 }, {  402, 0x0000 },
  {  402, 0x0000 }, {  402, 0x0000 }, {  402, 0x0000 }, {  402, 0x0000 },
  {  402, 0xffff }, {  418, 0x0000 }, {  418, 0x0000 }, {  418, 0x0000 },
  /* 0x3200 */
  {  418, 0x0000 }, {  418, 0x0000 }, {  418, 0x0000 }, {  418, 0x0002 },
};
static const Summary16 hkscs1999_uni2indx_page34[1724] = {
  /* 0x3400 */
  {  419, 0x0000 }, {  419, 0x0000 }, {  419, 0x0000 }, {  419, 0x0020 },
  {  420, 0x1001 }, {  422, 0x0000 }, {  422, 0x0010 }, {  423, 0x6408 },
  {  427, 0x0000 }, {  427, 0x0048 }, {  429, 0x8020 }, {  431, 0x1000 },
  {  432, 0x0102 }, {  434, 0x8000 }, {  435, 0x0010 }, {  436, 0x0800 },
  /* 0x3500 */
  {  437, 0x0040 }, {  438, 0x0000 }, {  438, 0x0000 }, {  438, 0x4000 },
  {  439, 0x0000 }, {  439, 0x000a }, {  441, 0x2002 }, {  443, 0x0185 },
  {  447, 0x0010 }, {  448, 0x0180 }, {  450, 0x2022 }, {  453, 0x8000 },
  {  454, 0x44a2 }, {  459, 0x2844 }, {  463, 0x0000 }, {  463, 0x480e },
  /* 0x3600 */
  {  468, 0x0200 }, {  469, 0x0500 }, {  471, 0x2008 }, {  473, 0x4220 },
  {  476, 0x4380 }, {  480, 0x8000 }, {  481, 0x0000 }, {  481, 0x0400 },
  {  482, 0x0002 }, {  483, 0x0400 }, {  484, 0x1420 }, {  487, 0x1223 },
  {  492, 0x01ba }, {  498, 0x2058 }, {  502, 0x0066 }, {  506, 0x0020 },
  /* 0x3700 */
  {  507, 0x250a }, {  512, 0x1000 }, {  513, 0x302c }, {  518, 0x040d },
  {  522, 0x0009 }, {  524, 0x0000 }, {  524, 0x8004 }, {  526, 0x0000 },
  {  526, 0x0000 }, {  526, 0x0080 }, {  527, 0x0001 }, {  528, 0x4200 },
  {  530, 0x0000 }, {  530, 0x0000 }, {  530, 0x0000 }, {  530, 0x0904 },
  /* 0x3800 */
  {  533, 0x8000 }, {  534, 0x0200 }, {  535, 0x2001 }, {  537, 0x0140 },
  {  539, 0x0000 }, {  539, 0x0000 }, {  539, 0x0008 }, {  540, 0x0000 },
  {  540, 0x0000 }, {  540, 0x0000 }, {  540, 0x0001 }, {  541, 0x0000 },
  {  541, 0x1008 }, {  543, 0x0002 }, {  544, 0x0000 }, {  544, 0x0400 },
  /* 0x3900 */
  {  545, 0x0100 }, {  546, 0x0010 }, {  547, 0x0080 }, {  548, 0x8004 },
  {  550, 0x2000 }, {  551, 0x0000 }, {  551, 0x0008 }, {  552, 0x0000 },
  {  552, 0x0601 }, {  555, 0x0a04 }, {  558, 0x0012 }, {  560, 0x0100 },
  {  561, 0x0000 }, {  561, 0x1000 }, {  562, 0x1024 }, {  565, 0x4900 },
  /* 0x3a00 */
  {  568, 0x004a }, {  571, 0x0180 }, {  573, 0x0600 }, {  575, 0x0010 },
  {  576, 0x0800 }, {  577, 0x5084 }, {  581, 0x00c0 }, {  583, 0x0000 },
  {  583, 0x0000 }, {  583, 0x0080 }, {  584, 0x0800 }, {  585, 0x2000 },
  {  586, 0x0000 }, {  586, 0x4000 }, {  587, 0x0001 }, {  588, 0x0805 },
  /* 0x3b00 */
  {  591, 0x4000 }, {  592, 0x0200 }, {  593, 0x0804 }, {  595, 0x0200 },
  {  596, 0x0004 }, {  597, 0x0100 }, {  598, 0x0001 }, {  599, 0x1806 },
  {  603, 0x0001 }, {  604, 0x0240 }, {  606, 0x0002 }, {  607, 0x5000 },
  {  609, 0x0014 }, {  611, 0x2080 }, {  613, 0x1000 }, {  614, 0x001c },
  /* 0x3c00 */
  {  617, 0x2000 }, {  618, 0x0122 }, {  621, 0x0000 }, {  621, 0x0000 },
  {  621, 0x0000 }, {  621, 0x0010 }, {  622, 0x0000 }, {  622, 0x0000 },
  {  622, 0x0000 }, {  622, 0x0000 }, {  622, 0x0000 }, {  622, 0x0000 },
  {  622, 0x2800 }, {  624, 0x1042 }, {  627, 0x8800 }, {  629, 0x0000 },
  /* 0x3d00 */
  {  629, 0x0000 }, {  629, 0x2008 }, {  631, 0x0000 }, {  631, 0x0804 },
  {  633, 0x5040 }, {  636, 0x8002 }, {  638, 0x8604 }, {  642, 0x2020 },
  {  644, 0x8420 }, {  647, 0x0002 }, {  648, 0x2020 }, {  650, 0x8010 },
  {  652, 0x30c0 }, {  656, 0x0808 }, {  658, 0x0980 }, {  661, 0x3088 },
  /* 0x3e00 */
  {  665, 0x0040 }, {  666, 0x0000 }, {  666, 0x0000 }, {  666, 0x0000 },
  {  666, 0x0109 }, {  669, 0x0020 }, {  670, 0x0000 }, {  670, 0x0010 },
  {  671, 0x0000 }, {  671, 0x0000 }, {  671, 0x2700 }, {  675, 0x8102 },
  {  678, 0x1484 }, {  682, 0x44c3 }, {  688, 0x0a86 }, {  693, 0x9419 },
  /* 0x3f00 */
  {  699, 0x4051 }, {  703, 0x0000 }, {  703, 0x0000 }, {  703, 0x0000 },
  {  703, 0x0000 }, {  703, 0x0308 }, {  706, 0x0008 }, {  707, 0x1000 },
  {  708, 0x0000 }, {  708, 0x0008 }, {  709, 0x0000 }, {  709, 0x0000 },
  {  709, 0x0001 }, {  710, 0x1080 }, {  712, 0x2020 }, {  714, 0x0600 },
  /* 0x4000 */
  {  716, 0x0010 }, {  717, 0x2000 }, {  718, 0x0000 }, {  718, 0x0200 },
  {  719, 0x0020 }, {  720, 0x0088 }, {  722, 0x8424 }, {  726, 0x0000 },
  {  726, 0x0000 }, {  726, 0x0000 }, {  726, 0x0100 }, {  727, 0x8800 },
  {  729, 0x0100 }, {  730, 0x8100 }, {  732, 0x0000 }, {  732, 0x0400 },
  /* 0x4100 */
  {  733, 0x4218 }, {  737, 0x0000 }, {  737, 0x0000 }, {  737, 0x0004 },
  {  738, 0x0000 }, {  738, 0x0000 }, {  738, 0x5080 }, {  741, 0x8000 },
  {  742, 0x0000 }, {  742, 0x0001 }, {  743, 0x0000 }, {  743, 0x0004 },
  {  744, 0x8410 }, {  747, 0x0800 }, {  748, 0x8000 }, {  749, 0x0200 },
  /* 0x4200 */
  {  750, 0x0000 }, {  750, 0x0002 }, {  751, 0x0000 }, {  751, 0x0000 },
  {  751, 0x0001 }, {  752, 0x0000 }, {  752, 0x0401 }, {  754, 0x0400 },
  {  755, 0x1000 }, {  756, 0x0010 }, {  757, 0x0000 }, {  757, 0x1220 },
  {  760, 0x0000 }, {  760, 0x0000 }, {  760, 0x0000 }, {  760, 0x1810 },
  /* 0x4300 */
  {  763, 0x0000 }, {  763, 0x0000 }, {  763, 0x0800 }, {  764, 0x0000 },
  {  764, 0x0000 }, {  764, 0x0000 }, {  764, 0x4000 }, {  765, 0x0000 },
  {  765, 0x0000 }, {  765, 0x0080 }, {  766, 0x0000 }, {  766, 0x0400 },
  {  767, 0x0002 }, {  768, 0x8200 }, {  770, 0x2000 }, {  771, 0x0004 },
  /* 0x4400 */
  {  772, 0x0006 }, {  774, 0x0008 }, {  775, 0x2020 }, {  777, 0x0000 },
  {  777, 0x0000 }, {  777, 0x0000 }, {  777, 0x0000 }, {  777, 0x0400 },
  {  778, 0x8000 }, {  779, 0x8002 }, {  781, 0x0005 }, {  783, 0x0081 },
  {  785, 0x4021 }, {  788, 0xa000 }, {  790, 0x1e10 }, {  795, 0x0010 },
  /* 0x4500 */
  {  796, 0x0a18 }, {  800, 0x2040 }, {  802, 0x4080 }, {  804, 0xa808 },
  {  808, 0x0008 }, {  809, 0x1026 }, {  813, 0x0404 }, {  815, 0x0080 },
  {  816, 0x0020 }, {  817, 0x0000 }, {  817, 0x0000 }, {  817, 0x0000 },
  {  817, 0x0000 }, {  817, 0x0000 }, {  817, 0x0200 }, {  818, 0x0000 },
  /* 0x4600 */
  {  818, 0x8040 }, {  820, 0x00a0 }, {  822, 0x0000 }, {  822, 0x0000 },
  {  822, 0x0000 }, {  822, 0x0800 }, {  823, 0x0000 }, {  823, 0x0400 },
  {  824, 0x0001 }, {  825, 0x0000 }, {  825, 0x0000 }, {  825, 0x0000 },
  {  825, 0x8000 }, {  826, 0x0001 }, {  827, 0x0000 }, {  827, 0x0020 },
  /* 0x4700 */
  {  828, 0x0000 }, {  828, 0x0108 }, {  830, 0x0000 }, {  830, 0x0000 },
  {  830, 0x4000 }, {  831, 0x0000 }, {  831, 0x0000 }, {  831, 0x1000 },
  {  832, 0x0000 }, {  832, 0x0100 }, {  833, 0x0040 }, {  834, 0x0000 },
  {  834, 0x0000 }, {  834, 0x0020 }, {  835, 0x2000 }, {  836, 0x0010 },
  /* 0x4800 */
  {  837, 0x0801 }, {  839, 0x0000 }, {  839, 0x0000 }, {  839, 0x0080 },
  {  840, 0x0000 }, {  840, 0x2000 }, {  841, 0x0000 }, {  841, 0x0002 },
  {  842, 0x0000 }, {  842, 0x0800 }, {  843, 0x6000 }, {  845, 0x0000 },
  {  845, 0x0000 }, {  845, 0x2001 }, {  847, 0x2000 }, {  848, 0x0408 },
  /* 0x4900 */
  {  850, 0x0040 }, {  851, 0x4002 }, {  853, 0x2420 }, {  856, 0x5020 },
  {  859, 0x0020 }, {  860, 0x000a }, {  862, 0x0420 }, {  864, 0x0004 },
  {  865, 0x0200 }, {  866, 0x0000 }, {  866, 0x0082 }, {  868, 0x0000 },
  {  868, 0x0000 }, {  868, 0x8000 }, {  869, 0x00a0 }, {  871, 0x0000 },
  /* 0x4a00 */
  {  871, 0x8000 }, {  872, 0x2000 }, {  873, 0x0010 }, {  874, 0x0020 },
  {  875, 0x0000 }, {  875, 0x0000 }, {  875, 0x0000 }, {  875, 0x0000 },
  {  875, 0x0000 }, {  875, 0x0040 }, {  876, 0x0000 }, {  876, 0x0110 },
  {  878, 0x0000 }, {  878, 0x0002 }, {  879, 0x0010 }, {  880, 0x8000 },
  /* 0x4b00 */
  {  881, 0x0000 }, {  881, 0x0200 }, {  882, 0x1000 }, {  883, 0x0080 },
  {  884, 0x0000 }, {  884, 0x0000 }, {  884, 0x8000 }, {  885, 0x4805 },
  {  889, 0x4000 }, {  890, 0x20c9 }, {  895, 0x0000 }, {  895, 0x6000 },
  {  897, 0x0001 }, {  898, 0x0000 }, {  898, 0x0000 }, {  898, 0x0000 },
  /* 0x4c00 */
  {  898, 0x4090 }, {  901, 0x0000 }, {  901, 0x0000 }, {  901, 0x4800 },
  {  903, 0x0000 }, {  903, 0x0800 }, {  904, 0x2000 }, {  905, 0x2000 },
  {  906, 0x0000 }, {  906, 0x0000 }, {  906, 0x4010 }, {  908, 0x0081 },
  {  910, 0x2000 }, {  911, 0x0000 }, {  911, 0x2002 }, {  913, 0x0000 },
  /* 0x4d00 */
  {  913, 0x0200 }, {  914, 0x0001 }, {  915, 0x0000 }, {  915, 0x0010 },
  {  916, 0x0000 }, {  916, 0x0000 }, {  916, 0x0000 }, {  916, 0x0000 },
  {  916, 0x0000 }, {  916, 0x1002 }, {  918, 0x0000 }, {  918, 0x0000 },
  {  918, 0x0000 }, {  918, 0x0000 }, {  918, 0x0000 }, {  918, 0x0000 },
  /* 0x4e00 */
  {  918, 0x0010 }, {  919, 0x1400 }, {  921, 0x1512 }, {  926, 0xa0c0 },
  {  930, 0x0200 }, {  931, 0x0c00 }, {  933, 0x0400 }, {  934, 0x0100 },
  {  935, 0x02a3 }, {  940, 0x0500 }, {  942, 0x0001 }, {  943, 0x9880 },
  {  947, 0x4000 }, {  948, 0x0000 }, {  948, 0x4c00 }, {  951, 0x0100 },
  /* 0x4f00 */
  {  952, 0x0008 }, {  953, 0x0400 }, {  954, 0x0300 }, {  956, 0x0284 },
  {  959, 0x0824 }, {  962, 0x0000 }, {  962, 0x0000 }, {  962, 0x0004 },
  {  963, 0x0400 }, {  964, 0x0000 }, {  964, 0x0904 }, {  967, 0x2001 },
  {  969, 0x1100 }, {  971, 0x0000 }, {  971, 0x0030 }, {  973, 0x2204 },
  /* 0x5000 */
  {  976, 0x0108 }, {  978, 0x0000 }, {  978, 0x4000 }, {  979, 0x0010 },
  {  980, 0x0000 }, {  980, 0x0140 }, {  982, 0x1040 }, {  984, 0x0000 },
  {  984, 0x0102 }, {  986, 0x0001 }, {  987, 0x0040 }, {  988, 0x0000 },
  {  988, 0x2000 }, {  989, 0x8201 }, {  992, 0x0002 }, {  993, 0x1010 },
  /* 0x5100 */
  {  995, 0x6002 }, {  998, 0x0000 }, {  998, 0x0800 }, {  999, 0x0000 },
  {  999, 0x0000 }, {  999, 0x0040 }, { 1000, 0x0401 }, { 1002, 0x0210 },
  { 1004, 0x0144 }, { 1007, 0x1440 }, { 1010, 0x0980 }, { 1013, 0x013c },
  { 1018, 0x8288 }, { 1022, 0x880e }, { 1027, 0x2014 }, { 1030, 0x5010 },
  /* 0x5200 */
  { 1033, 0x0824 }, { 1036, 0x8000 }, { 1037, 0x00c1 }, { 1040, 0x1010 },
  { 1042, 0x0000 }, { 1042, 0x0280 }, { 1044, 0x0101 }, { 1046, 0x0208 },
  { 1048, 0x8000 }, { 1049, 0x0411 }, { 1052, 0x0112 }, { 1055, 0x0220 },
  { 1057, 0x1020 }, { 1059, 0x0003 }, { 1061, 0x0003 }, { 1063, 0x0200 },
  /* 0x5300 */
  { 1064, 0x0002 }, { 1065, 0x0000 }, { 1065, 0x1080 }, { 1067, 0x090c },
  { 1071, 0x4004 }, { 1073, 0xa000 }, { 1075, 0x2290 }, { 1079, 0x6010 },
  { 1082, 0x0000 }, { 1082, 0x0008 }, { 1083, 0x4f45 }, { 1091, 0x0041 },
  { 1093, 0x1026 }, { 1097, 0x0707 }, { 1103, 0x0001 }, { 1104, 0x40c0 },
  /* 0x5400 */
  { 1107, 0x0000 }, { 1107, 0x0458 }, { 1111, 0x800a }, { 1114, 0x0004 },
  { 1115, 0x2800 }, { 1117, 0x0000 }, { 1117, 0x2600 }, { 1120, 0x0000 },
  { 1120, 0x8020 }, { 1122, 0x5098 }, { 1127, 0x0018 }, { 1129, 0x0214 },
  { 1132, 0x3800 }, { 1135, 0x0401 }, { 1137, 0x8008 }, { 1139, 0x0000 },
  /* 0x5500 */
  { 1139, 0x2004 }, { 1141, 0x4108 }, { 1144, 0x0928 }, { 1148, 0x8000 },
  { 1149, 0x0280 }, { 1151, 0x2008 }, { 1153, 0x0a00 }, { 1155, 0x020e },
  { 1159, 0x0040 }, { 1160, 0x0001 }, { 1161, 0x0200 }, { 1162, 0x1611 },
  { 1167, 0x0002 }, { 1168, 0x4180 }, { 1171, 0x1400 }, { 1173, 0x0823 },
  /* 0x5600 */
  { 1177, 0x0020 }, { 1178, 0x4002 }, { 1180, 0x202f }, { 1186, 0x0080 },
  { 1187, 0xa008 }, { 1190, 0x2015 }, { 1194, 0x0002 }, { 1195, 0x1c00 },
  { 1198, 0x0e00 }, { 1201, 0xc004 }, { 1204, 0x8012 }, { 1207, 0x8202 },
  { 1210, 0x0000 }, { 1210, 0x0040 }, { 1211, 0xa004 }, { 1214, 0x2002 },
  /* 0x5700 */
  { 1216, 0x0001 }, { 1217, 0x2020 }, { 1219, 0x0000 }, { 1219, 0x8004 },
  { 1221, 0x004c }, { 1224, 0x8890 }, { 1228, 0x0080 }, { 1229, 0xc400 },
  { 1232, 0x2500 }, { 1235, 0x1001 }, { 1237, 0x0482 }, { 1240, 0x4810 },
  { 1243, 0x0110 }, { 1245, 0x6080 }, { 1248, 0x8040 }, { 1250, 0x4000 },
  /* 0x5800 */
  { 1251, 0x0008 }, { 1252, 0x0004 }, { 1253, 0x0044 }, { 1255, 0x0400 },
  { 1256, 0x0091 }, { 1259, 0x9000 }, { 1261, 0x1200 }, { 1263, 0x000c },
  { 1265, 0x0000 }, { 1265, 0x0600 }, { 1267, 0x0480 }, { 1269, 0x0861 },
  { 1273, 0x0800 }, { 1274, 0x1000 }, { 1275, 0x0001 }, { 1276, 0x080d },
  /* 0x5900 */
  { 1280, 0x04b4 }, { 1285, 0x8002 }, { 1287, 0x0000 }, { 1287, 0x0014 },
  { 1289, 0x0000 }, { 1289, 0x0000 }, { 1289, 0x0020 }, { 1290, 0x0020 },
  { 1291, 0x0200 }, { 1292, 0x8410 }, { 1295, 0x1000 }, { 1296, 0x0181 },
  { 1299, 0x0210 }, { 1301, 0x0200 }, { 1302, 0x8800 }, { 1304, 0x0301 },
  /* 0x5a00 */
  { 1307, 0x2804 }, { 1310, 0x0004 }, { 1311, 0x1c92 }, { 1317, 0x2000 },
  { 1318, 0x0020 }, { 1319, 0x0210 }, { 1321, 0x490a }, { 1326, 0x4202 },
  { 1329, 0x0146 }, { 1333, 0x0242 }, { 1336, 0x0803 }, { 1339, 0x0000 },
  { 1339, 0xc008 }, { 1342, 0x0008 }, { 1343, 0x0010 }, { 1344, 0x4405 },
  /* 0x5b00 */
  { 1348, 0x2000 }, { 1349, 0x8002 }, { 1351, 0x0800 }, { 1352, 0x0000 },
  { 1352, 0x8452 }, { 1357, 0x0000 }, { 1357, 0x2140 }, { 1360, 0x1050 },
  { 1363, 0x0005 }, { 1365, 0xe001 }, { 1369, 0x0400 }, { 1370, 0x0000 },
  { 1370, 0x0008 }, { 1371, 0x00a0 }, { 1373, 0x0000 }, { 1373, 0x8008 },
  /* 0x5c00 */
  { 1375, 0x0020 }, { 1376, 0x5018 }, { 1380, 0x0009 }, { 1382, 0x0000 },
  { 1382, 0x0600 }, { 1384, 0x4008 }, { 1386, 0x0000 }, { 1386, 0x0000 },
  { 1386, 0x0020 }, { 1387, 0x5600 }, { 1391, 0x0000 }, { 1391, 0x0400 },
  { 1392, 0x0006 }, { 1394, 0x0002 }, { 1395, 0x8220 }, { 1398, 0x0000 },
  /* 0x5d00 */
  { 1398, 0x0000 }, { 1398, 0x0121 }, { 1401, 0x9000 }, { 1403, 0x4000 },
  { 1404, 0x0140 }, { 1406, 0x08c0 }, { 1409, 0x0000 }, { 1409, 0x0011 },
  { 1411, 0x4820 }, { 1414, 0x0000 }, { 1414, 0x0810 }, { 1416, 0x0240 },
  { 1418, 0x0002 }, { 1419, 0x0880 }, { 1421, 0x0000 }, { 1421, 0x0020 },
  /* 0x5e00 */
  { 1422, 0x0a00 }, { 1424, 0x0004 }, { 1425, 0x4000 }, { 1426, 0x0000 },
  { 1426, 0x0104 }, { 1428, 0x4000 }, { 1429, 0x0000 }, { 1429, 0x8400 },
  { 1431, 0x0048 }, { 1433, 0x0000 }, { 1433, 0x0000 }, { 1433, 0x2000 },
  { 1434, 0x2000 }, { 1435, 0x0001 }, { 1436, 0x0000 }, { 1436, 0x1b10 },
  /* 0x5f00 */
  { 1441, 0x7000 }, { 1444, 0x0000 }, { 1444, 0x0020 }, { 1445, 0x0400 },
  { 1446, 0x2000 }, { 1447, 0x1003 }, { 1450, 0x000a }, { 1452, 0x0804 },
  { 1454, 0x0008 }, { 1455, 0x0000 }, { 1455, 0x0090 }, { 1457, 0x0402 },
  { 1459, 0x0010 }, { 1460, 0x8800 }, { 1462, 0x0000 }, { 1462, 0x0000 },
  /* 0x6000 */
  { 1462, 0x0000 }, { 1462, 0x0000 }, { 1462, 0x0008 }, { 1463, 0x0802 },
  { 1465, 0x0400 }, { 1466, 0x0004 }, { 1467, 0x0000 }, { 1467, 0x40a0 },
  { 1470, 0x0000 }, { 1470, 0x4000 }, { 1471, 0x0090 }, { 1473, 0x0008 },
  { 1474, 0x0000 }, { 1474, 0x4080 }, { 1476, 0x0388 }, { 1480, 0x2000 },
  /* 0x6100 */
  { 1481, 0x1080 }, { 1483, 0x0200 }, { 1484, 0x0000 }, { 1484, 0x2001 },
  { 1486, 0x0004 }, { 1487, 0x1201 }, { 1490, 0x8011 }, { 1493, 0x2000 },
  { 1494, 0x0082 }, { 1496, 0x1320 }, { 1500, 0x0000 }, { 1500, 0x0280 },
  { 1502, 0x8001 }, { 1504, 0x0409 }, { 1507, 0x0004 }, { 1508, 0x0000 },
  /* 0x6200 */
  { 1508, 0x0000 }, { 1508, 0x0000 }, { 1508, 0x1000 }, { 1509, 0x0280 },
  { 1511, 0x1000 }, { 1512, 0x0000 }, { 1512, 0x0100 }, { 1513, 0x0000 },
  { 1513, 0x0024 }, { 1515, 0x2001 }, { 1517, 0x0050 }, { 1519, 0x0000 },
  { 1519, 0x0028 }, { 1521, 0x8020 }, { 1523, 0x0020 }, { 1524, 0x0000 },
  /* 0x6300 */
  { 1524, 0x0000 }, { 1524, 0x0100 }, { 1525, 0x4000 }, { 1526, 0x00a2 },
  { 1529, 0x0000 }, { 1529, 0x0000 }, { 1529, 0x1010 }, { 1531, 0x8200 },
  { 1533, 0x0800 }, { 1534, 0x0000 }, { 1534, 0x0000 }, { 1534, 0x0200 },
  { 1535, 0x0002 }, { 1536, 0x4002 }, { 1538, 0x0044 }, { 1540, 0x5900 },
  /* 0x6400 */
  { 1544, 0x0080 }, { 1545, 0x0000 }, { 1545, 0x0000 }, { 1545, 0x0d04 },
  { 1549, 0x0000 }, { 1549, 0x0400 }, { 1550, 0x0000 }, { 1550, 0x1002 },
  { 1552, 0x2000 }, { 1553, 0x0002 }, { 1554, 0x8000 }, { 1555, 0x0050 },
  { 1557, 0x0001 }, { 1558, 0x2008 }, { 1560, 0x04a2 }, { 1564, 0x0000 },
  /* 0x6500 */
  { 1564, 0x0400 }, { 1565, 0xc002 }, { 1568, 0x0000 }, { 1568, 0x0031 },
  { 1571, 0x2000 }, { 1572, 0x8000 }, { 1573, 0x2800 }, { 1575, 0x0000 },
  { 1575, 0x0360 }, { 1579, 0x0000 }, { 1579, 0x0000 }, { 1579, 0x4020 },
  { 1581, 0x0000 }, { 1581, 0x0012 }, { 1583, 0x0009 }, { 1585, 0x8000 },
  /* 0x6600 */
  { 1586, 0x0000 }, { 1586, 0x4100 }, { 1588, 0x0008 }, { 1589, 0x0001 },
  { 1590, 0x0910 }, { 1593, 0x0088 }, { 1595, 0x0888 }, { 1598, 0x2008 },
  { 1600, 0x4020 }, { 1602, 0x0404 }, { 1604, 0x2010 }, { 1606, 0x8048 },
  { 1609, 0x6000 }, { 1611, 0x0000 }, { 1611, 0x0000 }, { 1611, 0x0002 },
  /* 0x6700 */
  { 1612, 0x5004 }, { 1615, 0x4040 }, { 1617, 0x0020 }, { 1618, 0x0040 },
  { 1619, 0x0010 }, { 1620, 0x0000 }, { 1620, 0x0086 }, { 1623, 0x0000 },
  { 1623, 0x8000 }, { 1624, 0x0000 }, { 1624, 0x1011 }, { 1627, 0x8002 },
  { 1629, 0x0000 }, { 1629, 0x00c0 }, { 1631, 0x0000 }, { 1631, 0x4200 },
  /* 0x6800 */
  { 1633, 0x201f }, { 1639, 0x4801 }, { 1642, 0x0004 }, { 1643, 0x40c0 },
  { 1646, 0x0480 }, { 1648, 0x2060 }, { 1651, 0x0020 }, { 1652, 0x0000 },
  { 1652, 0x0110 }, { 1654, 0x0100 }, { 1655, 0x0040 }, { 1656, 0x2240 },
  { 1659, 0x0428 }, { 1662, 0x0000 }, { 1662, 0x0000 }, { 1662, 0x0000 },
  /* 0x6900 */
  { 1662, 0x020f }, { 1667, 0x0d00 }, { 1670, 0x1000 }, { 1671, 0x4040 },
  { 1673, 0x0048 }, { 1675, 0x0020 }, { 1676, 0x0092 }, { 1679, 0x000c },
  { 1681, 0x0421 }, { 1684, 0x8100 }, { 1686, 0x0004 }, { 1687, 0x0004 },
  { 1688, 0x0001 }, { 1689, 0x0062 }, { 1692, 0x0202 }, { 1694, 0x0600 },
  /* 0x6a00 */
  { 1696, 0x1808 }, { 1699, 0x1400 }, { 1701, 0x3800 }, { 1704, 0x0008 },
  { 1705, 0x1020 }, { 1707, 0x008c }, { 1710, 0x0020 }, { 1711, 0x0412 },
  { 1714, 0x8404 }, { 1717, 0x2200 }, { 1719, 0x0880 }, { 1721, 0x4026 },
  { 1725, 0x0700 }, { 1728, 0x0110 }, { 1730, 0x0000 }, { 1730, 0x0040 },
  /* 0x6b00 */
  { 1731, 0x0020 }, { 1732, 0x2000 }, { 1733, 0x0000 }, { 1733, 0x0020 },
  { 1734, 0x0000 }, { 1734, 0x0084 }, { 1736, 0x8000 }, { 1737, 0x0410 },
  { 1739, 0x0002 }, { 1740, 0x0000 }, { 1740, 0x0000 }, { 1740, 0x0000 },
  { 1740, 0x0002 }, { 1741, 0x1000 }, { 1742, 0x0402 }, { 1744, 0x0400 },
  /* 0x6c00 */
  { 1745, 0x0000 }, { 1745, 0x1000 }, { 1746, 0x0000 }, { 1746, 0x26a2 },
  { 1752, 0x0200 }, { 1753, 0x0500 }, { 1755, 0x4000 }, { 1756, 0x8220 },
  { 1759, 0x0000 }, { 1759, 0x8000 }, { 1760, 0x8404 }, { 1763, 0x0004 },
  { 1764, 0x4800 }, { 1766, 0x8000 }, { 1767, 0x0400 }, { 1768, 0x0000 },
  /* 0x6d00 */
  { 1768, 0x0064 }, { 1771, 0x0000 }, { 1771, 0x0050 }, { 1773, 0x0000 },
  { 1773, 0x4000 }, { 1774, 0x1880 }, { 1777, 0x0000 }, { 1777, 0x0006 },
  { 1779, 0x8002 }, { 1781, 0x0040 }, { 1782, 0x0030 }, { 1784, 0x0202 },
  { 1786, 0x0000 }, { 1786, 0x0000 }, { 1786, 0x0000 }, { 1786, 0x0000 },
  /* 0x6e00 */
  { 1786, 0x8414 }, { 1790, 0x0120 }, { 1792, 0x0600 }, { 1794, 0x0000 },
  { 1794, 0x8000 }, { 1795, 0x0201 }, { 1797, 0x0000 }, { 1797, 0x1040 },
  { 1799, 0x0840 }, { 1801, 0x0400 }, { 1802, 0x0000 }, { 1802, 0x0920 },
  { 1805, 0x0000 }, { 1805, 0x2e00 }, { 1809, 0x0304 }, { 1812, 0x0400 },
  /* 0x6f00 */
  { 1813, 0x1810 }, { 1816, 0x00c0 }, { 1818, 0x0010 }, { 1819, 0x2010 },
  { 1821, 0x0010 }, { 1822, 0x1040 }, { 1824, 0x0000 }, { 1824, 0x0210 },
  { 1826, 0x0402 }, { 1828, 0xa000 }, { 1830, 0x0000 }, { 1830, 0x4820 },
  { 1833, 0x0000 }, { 1833, 0x0608 }, { 1836, 0x0000 }, { 1836, 0x0140 },
  /* 0x7000 */
  { 1838, 0x0008 }, { 1839, 0x4000 }, { 1840, 0x1000 }, { 1841, 0x0000 },
  { 1841, 0x0800 }, { 1842, 0x1011 }, { 1845, 0x9080 }, { 1848, 0xc220 },
  { 1852, 0x8a02 }, { 1856, 0x0000 }, { 1856, 0x00e9 }, { 1861, 0x3a00 },
  { 1865, 0x1011 }, { 1868, 0x8061 }, { 1872, 0x0000 }, { 1872, 0x4022 },
  /* 0x7100 */
  { 1875, 0x0020 }, { 1876, 0x2000 }, { 1877, 0x1a00 }, { 1880, 0x4838 },
  { 1885, 0x8421 }, { 1889, 0x0002 }, { 1890, 0x0800 }, { 1891, 0x54ea },
  { 1899, 0x5100 }, { 1902, 0x0140 }, { 1904, 0x200c }, { 1907, 0x0490 },
  { 1910, 0x0000 }, { 1910, 0x2002 }, { 1912, 0x0800 }, { 1913, 0x0060 },
  /* 0x7200 */
  { 1915, 0xc200 }, { 1918, 0x00e0 }, { 1921, 0x4810 }, { 1924, 0x0000 },
  { 1924, 0x0001 }, { 1925, 0x10a1 }, { 1929, 0x0040 }, { 1930, 0x0000 },
  { 1930, 0x8084 }, { 1933, 0x8010 }, { 1935, 0x2000 }, { 1936, 0x0004 },
  { 1937, 0x2000 }, { 1938, 0x0000 }, { 1938, 0x0004 }, { 1939, 0x0000 },
  /* 0x7300 */
  { 1939, 0x0014 }, { 1941, 0x0001 }, { 1942, 0x5d00 }, { 1947, 0x0300 },
  { 1949, 0x8102 }, { 1952, 0x0000 }, { 1952, 0x0000 }, { 1952, 0x0012 },
  { 1954, 0x8000 }, { 1955, 0x5100 }, { 1958, 0x0480 }, { 1960, 0x0000 },
  { 1960, 0xc200 }, { 1963, 0x0021 }, { 1965, 0x8056 }, { 1970, 0x0a88 },
  /* 0x7400 */
  { 1974, 0x0000 }, { 1974, 0xd2b6 }, { 1983, 0x0000 }, { 1983, 0x1380 },
  { 1987, 0x03a8 }, { 1992, 0x2048 }, { 1995, 0x1921 }, { 2000, 0x0450 },
  { 2003, 0x3004 }, { 2006, 0x0a00 }, { 2008, 0x0010 }, { 2009, 0x0010 },
  { 2010, 0x1100 }, { 2012, 0x0009 }, { 2014, 0x0080 }, { 2015, 0x0107 },
  /* 0x7500 */
  { 2019, 0x4020 }, { 2021, 0x4200 }, { 2023, 0x0000 }, { 2023, 0x0830 },
  { 2026, 0x2444 }, { 2030, 0x002a }, { 2033, 0x6081 }, { 2037, 0x0404 },
  { 2039, 0x6008 }, { 2042, 0x4004 }, { 2044, 0x0000 }, { 2044, 0x0012 },
  { 2046, 0x0108 }, { 2048, 0x1000 }, { 2049, 0x0000 }, { 2049, 0x0000 },
  /* 0x7600 */
  { 2049, 0x0084 }, { 2051, 0x0000 }, { 2051, 0x1000 }, { 2052, 0x0800 },
  { 2053, 0xe001 }, { 2057, 0x0012 }, { 2059, 0x80c0 }, { 2062, 0x0458 },
  { 2066, 0x0000 }, { 2066, 0x0001 }, { 2067, 0x0022 }, { 2069, 0x0080 },
  { 2070, 0x1000 }, { 2071, 0x0040 }, { 2072, 0x0000 }, { 2072, 0x0000 },
  /* 0x7700 */
  { 2072, 0xd000 }, { 2075, 0x4000 }, { 2076, 0x0850 }, { 2079, 0x0000 },
  { 2079, 0x0009 }, { 2081, 0x0100 }, { 2082, 0x0000 }, { 2082, 0x0d84 },
  { 2087, 0x0000 }, { 2087, 0x0108 }, { 2089, 0x8000 }, { 2090, 0x4200 },
  { 2092, 0x0828 }, { 2095, 0x0000 }, { 2095, 0x0040 }, { 2096, 0x4010 },
  /* 0x7800 */
  { 2098, 0x0100 }, { 2099, 0x5100 }, { 2102, 0x0000 }, { 2102, 0x3200 },
  { 2105, 0x0894 }, { 2109, 0x001a }, { 2112, 0x0040 }, { 2113, 0x0400 },
  { 2114, 0x2102 }, { 2117, 0x0000 }, { 2117, 0x8000 }, { 2118, 0x0342 },
  { 2122, 0x0080 }, { 2123, 0x018c }, { 2127, 0x4000 }, { 2128, 0x0023 },
  /* 0x7900 */
  { 2131, 0x0040 }, { 2132, 0x0000 }, { 2132, 0x4000 }, { 2133, 0x185c },
  { 2139, 0x0000 }, { 2139, 0x0300 }, { 2141, 0x0004 }, { 2142, 0x4002 },
  { 2144, 0x00c9 }, { 2148, 0xa202 }, { 2152, 0x0220 }, { 2154, 0x0000 },
  { 2154, 0x1050 }, { 2157, 0x0010 }, { 2158, 0x0004 }, { 2159, 0x0012 },
  /* 0x7a00 */
  { 2161, 0x0040 }, { 2162, 0x0000 }, { 2162, 0x2000 }, { 2163, 0x4400 },
  { 2165, 0x0228 }, { 2168, 0x0000 }, { 2168, 0x0020 }, { 2169, 0x2000 },
  { 2170, 0x0008 }, { 2171, 0x0002 }, { 2172, 0x0000 }, { 2172, 0x1801 },
  { 2175, 0x830c }, { 2180, 0x3c08 }, { 2185, 0x0684 }, { 2189, 0x4000 },
  /* 0x7b00 */
  { 2190, 0x1800 }, { 2192, 0x8010 }, { 2194, 0x0280 }, { 2196, 0x0200 },
  { 2197, 0x000c }, { 2199, 0x0020 }, { 2200, 0x9004 }, { 2203, 0x0800 },
  { 2204, 0x0000 }, { 2204, 0x0004 }, { 2205, 0x000c }, { 2207, 0x0004 },
  { 2208, 0x8000 }, { 2209, 0x0001 }, { 2210, 0x0000 }, { 2210, 0x1400 },
  /* 0x7c00 */
  { 2212, 0x0000 }, { 2212, 0x0824 }, { 2215, 0x0000 }, { 2215, 0x0020 },
  { 2216, 0x0014 }, { 2218, 0x2042 }, { 2221, 0x2000 }, { 2222, 0x5811 },
  { 2227, 0x4048 }, { 2230, 0x1000 }, { 2231, 0x50c0 }, { 2235, 0x0100 },
  { 2236, 0x2284 }, { 2240, 0x0408 }, { 2242, 0x2040 }, { 2244, 0x1228 },
  /* 0x7d00 */
  { 2248, 0x0000 }, { 2248, 0x0000 }, { 2248, 0x0020 }, { 2249, 0x0000 },
  { 2249, 0x2000 }, { 2250, 0x2400 }, { 2252, 0x0000 }, { 2252, 0x0000 },
  { 2252, 0x0200 }, { 2253, 0x0080 }, { 2254, 0x0910 }, { 2257, 0x0008 },
  { 2258, 0xa000 }, { 2260, 0x1019 }, { 2264, 0x0030 }, { 2266, 0x6020 },
  /* 0x7e00 */
  { 2269, 0x0080 }, { 2270, 0x0000 }, { 2270, 0x0080 }, { 2271, 0x0000 },
  { 2271, 0x0000 }, { 2271, 0x0000 }, { 2271, 0x40a0 }, { 2274, 0x8000 },
  { 2275, 0x4000 }, { 2276, 0x8004 }, { 2278, 0x1010 }, { 2280, 0x0400 },
  { 2281, 0x8080 }, { 2283, 0x8000 }, { 2284, 0x0000 }, { 2284, 0x0000 },
  /* 0x7f00 */
  { 2284, 0x0040 }, { 2285, 0x0000 }, { 2285, 0x0000 }, { 2285, 0x0080 },
  { 2286, 0x4283 }, { 2291, 0x000c }, { 2293, 0x0000 }, { 2293, 0x0102 },
  { 2295, 0x8000 }, { 2296, 0x0088 }, { 2298, 0x4008 }, { 2300, 0x0010 },
  { 2301, 0x0000 }, { 2301, 0x2000 }, { 2302, 0x0080 }, { 2303, 0x0400 },
  /* 0x8000 */
  { 2304, 0x0104 }, { 2306, 0x2000 }, { 2307, 0xc021 }, { 2311, 0x1802 },
  { 2314, 0x0000 }, { 2314, 0x0810 }, { 2316, 0x004e }, { 2320, 0x0000 },
  { 2320, 0x0001 }, { 2321, 0x8000 }, { 2322, 0x0080 }, { 2323, 0x30c0 },
  { 2327, 0x0040 }, { 2328, 0x0000 }, { 2328, 0x1200 }, { 2330, 0x0040 },
  /* 0x8100 */
  { 2331, 0x5288 }, { 2336, 0x0494 }, { 2340, 0x0400 }, { 2341, 0x0094 },
  { 2344, 0x0104 }, { 2346, 0x0640 }, { 2349, 0x2000 }, { 2350, 0x1000 },
  { 2351, 0x0010 }, { 2352, 0x0008 }, { 2353, 0x0420 }, { 2355, 0x0040 },
  { 2356, 0x0102 }, { 2358, 0x0000 }, { 2358, 0x8010 }, { 2360, 0x0040 },
  /* 0x8200 */
  { 2361, 0x0000 }, { 2361, 0x0500 }, { 2363, 0x2240 }, { 2366, 0x4000 },
  { 2367, 0x0000 }, { 2367, 0x0010 }, { 2368, 0x0024 }, { 2370, 0x0e40 },
  { 2374, 0x0080 }, { 2375, 0x0000 }, { 2375, 0x0440 }, { 2377, 0x0000 },
  { 2377, 0x8410 }, { 2380, 0x0101 }, { 2382, 0x4004 }, { 2384, 0xb080 },
  /* 0x8300 */
  { 2388, 0x0800 }, { 2389, 0x2500 }, { 2392, 0x0000 }, { 2392, 0x2000 },
  { 2393, 0x0000 }, { 2393, 0x0080 }, { 2394, 0x804c }, { 2398, 0x0000 },
  { 2398, 0x0020 }, { 2399, 0x1002 }, { 2401, 0x1000 }, { 2402, 0x4200 },
  { 2404, 0x2000 }, { 2405, 0x0008 }, { 2406, 0x2000 }, { 2407, 0x0000 },
  /* 0x8400 */
  { 2407, 0x0020 }, { 2408, 0x1150 }, { 2412, 0x4053 }, { 2417, 0x4000 },
  { 2418, 0x0500 }, { 2420, 0x1128 }, { 2424, 0x0014 }, { 2426, 0x8006 },
  { 2429, 0x0101 }, { 2431, 0x004c }, { 2434, 0x2008 }, { 2436, 0x6000 },
  { 2438, 0x0000 }, { 2438, 0x4400 }, { 2440, 0x0036 }, { 2444, 0x0100 },
  /* 0x8500 */
  { 2445, 0x0028 }, { 2447, 0x0001 }, { 2448, 0x0000 }, { 2448, 0x0118 },
  { 2451, 0x1804 }, { 2454, 0x0404 }, { 2456, 0x8000 }, { 2457, 0x0009 },
  { 2459, 0x0000 }, { 2459, 0x0000 }, { 2459, 0x0000 }, { 2459, 0x0000 },
  { 2459, 0x0002 }, { 2460, 0x0000 }, { 2460, 0x4001 }, { 2462, 0x1000 },
  /* 0x8600 */
  { 2463, 0x2004 }, { 2465, 0x0051 }, { 2468, 0x8100 }, { 2470, 0x0000 },
  { 2470, 0x0024 }, { 2472, 0x0000 }, { 2472, 0x1000 }, { 2473, 0x4004 },
  { 2475, 0x0000 }, { 2475, 0x0004 }, { 2476, 0x2001 }, { 2478, 0x0004 },
  { 2479, 0x0000 }, { 2479, 0x0000 }, { 2479, 0x8000 }, { 2480, 0x0000 },
  /* 0x8700 */
  { 2480, 0x0000 }, { 2480, 0x0000 }, { 2480, 0x0000 }, { 2480, 0x0000 },
  { 2480, 0x0000 }, { 2480, 0x0000 }, { 2480, 0x0000 }, { 2480, 0x2003 },
  { 2483, 0x1840 }, { 2486, 0x0000 }, { 2486, 0x0220 }, { 2488, 0x0002 },
  { 2489, 0x4002 }, { 2491, 0x0440 }, { 2493, 0x4000 }, { 2494, 0x0020 },
  /* 0x8800 */
  { 2495, 0x8010 }, { 2497, 0x0100 }, { 2498, 0x2080 }, { 2500, 0x0000 },
  { 2500, 0x8064 }, { 2504, 0x4000 }, { 2505, 0x4031 }, { 2509, 0x0000 },
  { 2509, 0x0090 }, { 2511, 0x1000 }, { 2512, 0x4001 }, { 2514, 0x8030 },
  { 2517, 0x80a0 }, { 2520, 0x0000 }, { 2520, 0x0040 }, { 2521, 0x8020 },
  /* 0x8900 */
  { 2523, 0x0001 }, { 2524, 0x0000 }, { 2524, 0x0010 }, { 2525, 0x0000 },
  { 2525, 0x2088 }, { 2528, 0x0010 }, { 2529, 0x0020 }, { 2530, 0x0080 },
  { 2531, 0x0681 }, { 2535, 0x0012 }, { 2537, 0x02a0 }, { 2540, 0x1000 },
  { 2541, 0x0042 }, { 2543, 0x0000 }, { 2543, 0x0080 }, { 2544, 0x0000 },
  /* 0x8a00 */
  { 2544, 0x0000 }, { 2544, 0x1000 }, { 2545, 0x0a00 }, { 2547, 0x2100 },
  { 2549, 0x0200 }, { 2550, 0x0000 }, { 2550, 0x0080 }, { 2551, 0x4000 },
  { 2552, 0x0000 }, { 2552, 0x1011 }, { 2555, 0x8200 }, { 2557, 0x0010 },
  { 2558, 0x0000 }, { 2558, 0x0400 }, { 2559, 0x0400 }, { 2560, 0x0000 },
  /* 0x8b00 */
  { 2560, 0x1000 }, { 2561, 0x8000 }, { 2562, 0x2000 }, { 2563, 0x8000 },
  { 2564, 0x3008 }, { 2567, 0x4000 }, { 2568, 0x0204 }, { 2570, 0x0000 },
  { 2570, 0x0002 }, { 2571, 0x0801 }, { 2573, 0x0001 }, { 2574, 0x4000 },
  { 2575, 0x0000 }, { 2575, 0x0000 }, { 2575, 0x0004 }, { 2576, 0x0000 },
  /* 0x8c00 */
  { 2576, 0x0000 }, { 2576, 0x0000 }, { 2576, 0x0000 }, { 2576, 0x0000 },
  { 2576, 0x0000 }, { 2576, 0x0002 }, { 2577, 0x0000 }, { 2577, 0x0000 },
  { 2577, 0x0000 }, { 2577, 0x8800 }, { 2579, 0x2000 }, { 2580, 0x0000 },
  { 2580, 0x2000 }, { 2581, 0x0850 }, { 2584, 0x0a00 }, { 2586, 0x0084 },
  /* 0x8d00 */
  { 2588, 0x1808 }, { 2591, 0x3106 }, { 2596, 0x0000 }, { 2596, 0x0000 },
  { 2596, 0x0000 }, { 2596, 0x0000 }, { 2596, 0x0000 }, { 2596, 0x0400 },
  { 2597, 0x0004 }, { 2598, 0x0000 }, { 2598, 0x0240 }, { 2600, 0x0000 },
  { 2600, 0x0009 }, { 2602, 0x0010 }, { 2603, 0x0000 }, { 2603, 0x0000 },
  /* 0x8e00 */
  { 2603, 0x4002 }, { 2605, 0x0000 }, { 2605, 0x2500 }, { 2608, 0x0400 },
  { 2609, 0x8040 }, { 2611, 0x0000 }, { 2611, 0x0100 }, { 2612, 0x40a2 },
  { 2616, 0x0001 }, { 2617, 0x0000 }, { 2617, 0x2080 }, { 2619, 0x1041 },
  { 2622, 0x4008 }, { 2624, 0x0400 }, { 2625, 0x2014 }, { 2628, 0x0004 },
  /* 0x8f00 */
  { 2629, 0x0000 }, { 2629, 0x0200 }, { 2630, 0x2000 }, { 2631, 0x0001 },
  { 2632, 0x0402 }, { 2634, 0x1000 }, { 2635, 0x40c0 }, { 2638, 0x0000 },
  { 2638, 0x0000 }, { 2638, 0x0008 }, { 2639, 0x0021 }, { 2641, 0x5fe8 },
  { 2651, 0x1402 }, { 2654, 0x0401 }, { 2656, 0x0000 }, { 2656, 0x0200 },
  /* 0x9000 */
  { 2657, 0x0100 }, { 2658, 0x0004 }, { 2659, 0x0000 }, { 2659, 0x0088 },
  { 2661, 0x1000 }, { 2662, 0x0040 }, { 2663, 0x1012 }, { 2666, 0x0000 },
  { 2666, 0x0000 }, { 2666, 0x0000 }, { 2666, 0x4100 }, { 2668, 0x0800 },
  { 2669, 0x0010 }, { 2670, 0x0000 }, { 2670, 0x0000 }, { 2670, 0x0000 },
  /* 0x9100 */
  { 2670, 0x0000 }, { 2670, 0x0000 }, { 2670, 0x0000 }, { 2670, 0x0000 },
  { 2670, 0x0000 }, { 2670, 0x5202 }, { 2674, 0x0080 }, { 2675, 0x1041 },
  { 2678, 0x5000 }, { 2680, 0x0000 }, { 2680, 0x0200 }, { 2681, 0x0840 },
  { 2683, 0x0010 }, { 2684, 0x8040 }, { 2686, 0x0020 }, { 2687, 0x4400 },
  /* 0x9200 */
  { 2689, 0x4100 }, { 2691, 0x0008 }, { 2692, 0x0d00 }, { 2695, 0x1020 },
  { 2697, 0x0012 }, { 2699, 0xa120 }, { 2703, 0x4804 }, { 2706, 0x0080 },
  { 2707, 0x8212 }, { 2711, 0x0000 }, { 2711, 0x4000 }, { 2712, 0xc602 },
  { 2717, 0x0000 }, { 2717, 0x0810 }, { 2719, 0x1828 }, { 2723, 0x205c },
  /* 0x9300 */
  { 2728, 0x0088 }, { 2730, 0x0000 }, { 2730, 0x1000 }, { 2731, 0x0003 },
  { 2733, 0x013f }, { 2740, 0x8000 }, { 2741, 0x4b44 }, { 2747, 0x2118 },
  { 2751, 0x00f2 }, { 2756, 0x1001 }, { 2758, 0x2001 }, { 2760, 0xa900 },
  { 2764, 0x0840 }, { 2766, 0x0808 }, { 2768, 0x0001 }, { 2769, 0x000b },
  /* 0x9400 */
  { 2772, 0x0112 }, { 2775, 0x2880 }, { 2778, 0x20f0 }, { 2783, 0x4000 },
  { 2784, 0x200c }, { 2787, 0x0910 }, { 2790, 0x10a0 }, { 2793, 0x0a00 },
  { 2795, 0x0020 }, { 2796, 0x8000 }, { 2797, 0x0004 }, { 2798, 0x0000 },
  { 2798, 0x000a }, { 2800, 0x1000 }, { 2801, 0x0000 }, { 2801, 0x0040 },
  /* 0x9500 */
  { 2802, 0x0000 }, { 2802, 0x0000 }, { 2802, 0x2000 }, { 2803, 0x0000 },
  { 2803, 0x0080 }, { 2804, 0x0000 }, { 2804, 0x0000 }, { 2804, 0x8100 },
  { 2806, 0x0020 }, { 2807, 0x02c0 }, { 2810, 0x04c5 }, { 2815, 0x0000 },
  { 2815, 0x0000 }, { 2815, 0x0000 }, { 2815, 0x0100 }, { 2816, 0x0010 },
  /* 0x9600 */
  { 2817, 0x0000 }, { 2817, 0x2000 }, { 2818, 0x0000 }, { 2818, 0x0108 },
  { 2820, 0x0022 }, { 2822, 0x0040 }, { 2823, 0x0200 }, { 2824, 0x0800 },
  { 2825, 0x8002 }, { 2827, 0x0040 }, { 2828, 0x0028 }, { 2830, 0x2040 },
  { 2832, 0x0000 }, { 2832, 0x0000 }, { 2832, 0x0000 }, { 2832, 0x0010 },
  /* 0x9700 */
  { 2833, 0x0008 }, { 2834, 0x0800 }, { 2835, 0x0002 }, { 2836, 0x0042 },
  { 2838, 0x0003 }, { 2840, 0xa082 }, { 2844, 0x2000 }, { 2845, 0x0002 },
  { 2846, 0x0280 }, { 2848, 0x8800 }, { 2850, 0x0000 }, { 2850, 0x6516 },
  { 2857, 0x0105 }, { 2860, 0x0004 }, { 2861, 0x4041 }, { 2864, 0x0024 },
  /* 0x9800 */
  { 2866, 0x0000 }, { 2866, 0x8030 }, { 2869, 0x4008 }, { 2871, 0x0018 },
  { 2873, 0x0880 }, { 2875, 0x0000 }, { 2875, 0x1040 }, { 2877, 0x0020 },
  { 2878, 0x0000 }, { 2878, 0x0000 }, { 2878, 0x0000 }, { 2878, 0x0290 },
  { 2881, 0x4588 }, { 2886, 0x5000 }, { 2888, 0x1043 }, { 2892, 0x0022 },
  /* 0x9900 */
  { 2894, 0x4000 }, { 2895, 0x1200 }, { 2897, 0x0000 }, { 2897, 0x0b80 },
  { 2901, 0x2405 }, { 2905, 0x2000 }, { 2906, 0x000c }, { 2908, 0x0000 },
  { 2908, 0x0000 }, { 2908, 0x0800 }, { 2909, 0x0410 }, { 2911, 0x1100 },
  { 2913, 0x0030 }, { 2915, 0x0400 }, { 2916, 0x0042 }, { 2918, 0x0020 },
  /* 0x9a00 */
  { 2919, 0x1000 }, { 2920, 0x8001 }, { 2922, 0x8042 }, { 2925, 0x1800 },
  { 2927, 0x0000 }, { 2927, 0x1100 }, { 2929, 0x1008 }, { 2931, 0x0000 },
  { 2931, 0x8000 }, { 2932, 0x0000 }, { 2932, 0x0000 }, { 2932, 0x2444 },
  { 2936, 0x0000 }, { 2936, 0x0080 }, { 2937, 0x0005 }, { 2939, 0x8010 },
  /* 0x9b00 */
  { 2941, 0x8204 }, { 2944, 0x0010 }, { 2945, 0x2400 }, { 2947, 0x0210 },
  { 2949, 0x0001 }, { 2950, 0x0001 }, { 2951, 0x0200 }, { 2952, 0x8000 },
  { 2953, 0xe80a }, { 2959, 0xa080 }, { 2962, 0x0000 }, { 2962, 0x0001 },
  { 2963, 0x8000 }, { 2964, 0x2000 }, { 2965, 0x2200 }, { 2967, 0x8012 },
  /* 0x9c00 */
  { 2970, 0x1404 }, { 2973, 0x8821 }, { 2977, 0x8041 }, { 2980, 0x0420 },
  { 2982, 0x8020 }, { 2984, 0x2008 }, { 2986, 0x0000 }, { 2986, 0x1804 },
  { 2989, 0x0000 }, { 2989, 0x0000 }, { 2989, 0x0000 }, { 2989, 0x0000 },
  { 2989, 0x0000 }, { 2989, 0x0000 }, { 2989, 0x0000 }, { 2989, 0x0000 },
  /* 0x9d00 */
  { 2989, 0x1004 }, { 2991, 0x0040 }, { 2992, 0x0002 }, { 2993, 0x0210 },
  { 2995, 0x4210 }, { 2998, 0x4001 }, { 3000, 0x6000 }, { 3002, 0x5000 },
  { 3004, 0x0008 }, { 3005, 0x0008 }, { 3006, 0x0820 }, { 3008, 0x2000 },
  { 3009, 0x0211 }, { 3012, 0x0010 }, { 3013, 0x0000 }, { 3013, 0x1000 },
  /* 0x9e00 */
  { 3014, 0x5400 }, { 3017, 0x9100 }, { 3020, 0x0000 }, { 3020, 0x0000 },
  { 3020, 0x0000 }, { 3020, 0x0000 }, { 3020, 0x0000 }, { 3020, 0x0800 },
  { 3021, 0x0032 }, { 3024, 0x4161 }, { 3029, 0x9d44 }, { 3036, 0xa002 },
  { 3039, 0x00d2 }, { 3043, 0x0000 }, { 3043, 0x0004 }, { 3044, 0x4102 },
  /* 0x9f00 */
  { 3047, 0x0104 }, { 3049, 0x0080 }, { 3050, 0x00c0 }, { 3052, 0x0200 },
  { 3053, 0x0030 }, { 3055, 0x0409 }, { 3058, 0x0204 }, { 3060, 0x8000 },
  { 3061, 0x4000 }, { 3062, 0x8200 }, { 3064, 0x0020 }, { 3065, 0x0003 },
};
static const Summary16 hkscs1999_uni2indx_pagef9[1] = {
  /* 0xf900 */
  { 3067, 0x0080 },
};
static const Summary16 hkscs1999_uni2indx_pageff[15] = {
  /* 0xff00 */
  { 3068, 0x0084 }, { 3070, 0x0000 }, { 3070, 0x0000 }, { 3070, 0x2800 },
  { 3072, 0x0000 }, { 3072, 0x0000 }, { 3072, 0x0000 }, { 3072, 0x0000 },
  { 3072, 0x0000 }, { 3072, 0x0000 }, { 3072, 0x0000 }, { 3072, 0x0000 },
  { 3072, 0x0000 }, { 3072, 0x0000 }, { 3072, 0x2014 },
};
static const Summary16 hkscs1999_uni2indx_page200[2335] = {
  /* 0x20000 */
  { 3075, 0x0000 }, { 3075, 0x0000 }, { 3075, 0x0002 }, { 3076, 0x4000 },
  { 3077, 0x4040 }, { 3079, 0x0000 }, { 3079, 0x0100 }, { 3080, 0x0000 },
  { 3080, 0x04c0 }, { 3083, 0x0010 }, { 3084, 0x0000 }, { 3084, 0x0000 },
  { 3084, 0x3c00 }, { 3088, 0x0002 }, { 3089, 0x4000 }, { 3090, 0x0000 },
  /* 0x20100 */
  { 3090, 0x5000 }, { 3092, 0x0100 }, { 3093, 0x0000 }, { 3093, 0x0000 },
  { 3093, 0x0000 }, { 3093, 0x0000 }, { 3093, 0x0000 }, { 3093, 0x0000 },
  { 3093, 0x0000 }, { 3093, 0x0000 }, { 3093, 0x0a00 }, { 3095, 0x0000 },
  { 3095, 0x0002 }, { 3096, 0x0010 }, { 3097, 0x0000 }, { 3097, 0x0004 },
  /* 0x20200 */
  { 3098, 0x1010 }, { 3100, 0x0010 }, { 3101, 0x0000 }, { 3101, 0x0000 },
  { 3101, 0x0000 }, { 3101, 0x0800 }, { 3102, 0x0000 }, { 3102, 0x0030 },
  { 3104, 0x0000 }, { 3104, 0x4200 }, { 3106, 0x0001 }, { 3107, 0x8080 },
  { 3109, 0x0001 }, { 3110, 0x0000 }, { 3110, 0x0020 }, { 3111, 0x0000 },
  /* 0x20300 */
  { 3111, 0x0400 }, { 3112, 0x0000 }, { 3112, 0x0020 }, { 3113, 0x0000 },
  { 3113, 0x00e2 }, { 3117, 0x0000 }, { 3117, 0x0000 }, { 3117, 0xc000 },
  { 3119, 0x0001 }, { 3120, 0x0000 }, { 3120, 0x0081 }, { 3122, 0x0020 },
  { 3123, 0x0a00 }, { 3125, 0x0000 }, { 3125, 0x0000 }, { 3125, 0x1020 },
  /* 0x20400 */
  { 3127, 0x0000 }, { 3127, 0x8018 }, { 3130, 0x0000 }, { 3130, 0x0000 },
  { 3130, 0x0000 }, { 3130, 0x0000 }, { 3130, 0x0020 }, { 3131, 0x0000 },
  { 3131, 0x4080 }, { 3133, 0x0006 }, { 3135, 0x0008 }, { 3136, 0x0000 },
  { 3136, 0x0000 }, { 3136, 0x0080 }, { 3137, 0x0000 }, { 3137, 0x5000 },
  /* 0x20500 */
  { 3139, 0x0000 }, { 3139, 0x0000 }, { 3139, 0x0000 }, { 3139, 0x0000 },
  { 3139, 0x0080 }, { 3140, 0x0000 }, { 3140, 0x0000 }, { 3140, 0x0000 },
  { 3140, 0x4000 }, { 3141, 0x0000 }, { 3141, 0x0020 }, { 3142, 0x0008 },
  { 3143, 0x0408 }, { 3145, 0x8021 }, { 3148, 0x0801 }, { 3150, 0x0000 },
  /* 0x20600 */
  { 3150, 0x0000 }, { 3150, 0x0622 }, { 3154, 0x0000 }, { 3154, 0x0001 },
  { 3155, 0x0000 }, { 3155, 0x0040 }, { 3156, 0x0000 }, { 3156, 0x0040 },
  { 3157, 0x0000 }, { 3157, 0x0000 }, { 3157, 0x0000 }, { 3157, 0x0000 },
  { 3157, 0x0000 }, { 3157, 0x0000 }, { 3157, 0x0000 }, { 3157, 0x0000 },
  /* 0x20700 */
  { 3157, 0x4000 }, { 3158, 0x0000 }, { 3158, 0x0000 }, { 3158, 0x0002 },
  { 3159, 0x0000 }, { 3159, 0x0000 }, { 3159, 0x0000 }, { 3159, 0x0200 },
  { 3160, 0x0000 }, { 3160, 0x0000 }, { 3160, 0x0000 }, { 3160, 0x0000 },
  { 3160, 0x0000 }, { 3160, 0x0000 }, { 3160, 0x0000 }, { 3160, 0x0000 },
  /* 0x20800 */
  { 3160, 0x0000 }, { 3160, 0x0000 }, { 3160, 0x1000 }, { 3161, 0x0000 },
  { 3161, 0x0000 }, { 3161, 0x0000 }, { 3161, 0x0000 }, { 3161, 0x0008 },
  { 3162, 0x0000 }, { 3162, 0x0000 }, { 3162, 0x0000 }, { 3162, 0x0000 },
  { 3162, 0x0000 }, { 3162, 0x0020 }, { 3163, 0x0000 }, { 3163, 0x0000 },
  /* 0x20900 */
  { 3163, 0x0000 }, { 3163, 0x0040 }, { 3164, 0x0008 }, { 3165, 0x0000 },
  { 3165, 0x0000 }, { 3165, 0x0010 }, { 3166, 0x0000 }, { 3166, 0x0200 },
  { 3167, 0x0000 }, { 3167, 0x0000 }, { 3167, 0x0000 }, { 3167, 0x0000 },
  { 3167, 0x0000 }, { 3167, 0x0000 }, { 3167, 0x0080 }, { 3168, 0x0000 },
  /* 0x20a00 */
  { 3168, 0x0000 }, { 3168, 0x0002 }, { 3169, 0x0000 }, { 3169, 0x0000 },
  { 3169, 0x0000 }, { 3169, 0x0001 }, { 3170, 0x0000 }, { 3170, 0x0000 },
  { 3170, 0x0000 }, { 3170, 0x0000 }, { 3170, 0x0000 }, { 3170, 0x0010 },
  { 3171, 0x2004 }, { 3173, 0x0000 }, { 3173, 0x0000 }, { 3173, 0x0000 },
  /* 0x20b00 */
  { 3173, 0x2000 }, { 3174, 0x0000 }, { 3174, 0x0000 }, { 3174, 0x0000 },
  { 3174, 0x0000 }, { 3174, 0x0000 }, { 3174, 0x0000 }, { 3174, 0x0000 },
  { 3174, 0x8000 }, { 3175, 0x0000 }, { 3175, 0x0300 }, { 3177, 0x8000 },
  { 3178, 0x0840 }, { 3180, 0x0000 }, { 3180, 0x0804 }, { 3182, 0x8800 },
  /* 0x20c00 */
  { 3184, 0x2800 }, { 3186, 0x0000 }, { 3186, 0x0001 }, { 3187, 0x0c10 },
  { 3190, 0x000e }, { 3193, 0x0008 }, { 3194, 0x0020 }, { 3195, 0x1180 },
  { 3198, 0x2000 }, { 3199, 0x1040 }, { 3201, 0x0000 }, { 3201, 0x0120 },
  { 3203, 0x8000 }, { 3204, 0x2078 }, { 3209, 0x2000 }, { 3210, 0x8000 },
  /* 0x20d00 */
  { 3211, 0x0000 }, { 3211, 0x0020 }, { 3212, 0x0100 }, { 3213, 0x0006 },
  { 3215, 0x73c0 }, { 3222, 0x0000 }, { 3222, 0x8000 }, { 3223, 0xd012 },
  { 3228, 0x0000 }, { 3228, 0x1040 }, { 3230, 0x0080 }, { 3231, 0x0004 },
  { 3232, 0x0100 }, { 3233, 0x0000 }, { 3233, 0x0000 }, { 3233, 0x0000 },
  /* 0x20e00 */
  { 3233, 0xe610 }, { 3239, 0x2043 }, { 3243, 0x0000 }, { 3243, 0x0000 },
  { 3243, 0x1000 }, { 3244, 0x0000 }, { 3244, 0x2000 }, { 3245, 0x0fe8 },
  { 3253, 0x1000 }, { 3254, 0x2140 }, { 3257, 0x1c04 }, { 3261, 0x0040 },
  { 3262, 0x0000 }, { 3262, 0x2180 }, { 3265, 0x0000 }, { 3265, 0x0f00 },
  /* 0x20f00 */
  { 3269, 0x0000 }, { 3269, 0x2000 }, { 3270, 0x6040 }, { 3273, 0x0803 },
  { 3276, 0x1000 }, { 3277, 0x0000 }, { 3277, 0x0010 }, { 3278, 0x0000 },
  { 3278, 0x2000 }, { 3279, 0x0001 }, { 3280, 0x2000 }, { 3281, 0x1070 },
  { 3285, 0x0000 }, { 3285, 0x8000 }, { 3286, 0x3c00 }, { 3290, 0x0000 },
  /* 0x21000 */
  { 3290, 0x0000 }, { 3290, 0x6010 }, { 3293, 0x0000 }, { 3293, 0x0000 },
  { 3293, 0x8000 }, { 3294, 0x1000 }, { 3295, 0x8000 }, { 3296, 0x09e0 },
  { 3301, 0x0100 }, { 3302, 0x2040 }, { 3304, 0x0000 }, { 3304, 0x8010 },
  { 3306, 0x8383 }, { 3312, 0x0008 }, { 3313, 0x0010 }, { 3314, 0x0070 },
  /* 0x21100 */
  { 3317, 0x0000 }, { 3317, 0x0000 }, { 3317, 0x8000 }, { 3318, 0x2800 },
  { 3320, 0x8120 }, { 3323, 0x0000 }, { 3323, 0x0000 }, { 3323, 0x0000 },
  { 3323, 0x0081 }, { 3325, 0x0000 }, { 3325, 0x0000 }, { 3325, 0x0000 },
  { 3325, 0x0000 }, { 3325, 0x0200 }, { 3326, 0x0000 }, { 3326, 0x0000 },
  /* 0x21200 */
  { 3326, 0x0000 }, { 3326, 0x0000 }, { 3326, 0x0000 }, { 3326, 0x1000 },
  { 3327, 0x8000 }, { 3328, 0x0000 }, { 3328, 0x0000 }, { 3328, 0x1000 },
  { 3329, 0x0000 }, { 3329, 0x0000 }, { 3329, 0x0300 }, { 3331, 0x0001 },
  { 3332, 0x0000 }, { 3332, 0x0000 }, { 3332, 0x0008 }, { 3333, 0x4000 },
  /* 0x21300 */
  { 3334, 0x003c }, { 3338, 0x0000 }, { 3338, 0x0000 }, { 3338, 0x0440 },
  { 3340, 0x0000 }, { 3340, 0x0000 }, { 3340, 0x0000 }, { 3340, 0x0060 },
  { 3342, 0x4000 }, { 3343, 0x1100 }, { 3345, 0x0000 }, { 3345, 0x0000 },
  { 3345, 0x0060 }, { 3347, 0x0000 }, { 3347, 0x2000 }, { 3348, 0x4000 },
  /* 0x21400 */
  { 3349, 0x0000 }, { 3349, 0x0048 }, { 3351, 0x0010 }, { 3352, 0x0000 },
  { 3352, 0x0000 }, { 3352, 0x0034 }, { 3355, 0x0000 }, { 3355, 0x0000 },
  { 3355, 0x0400 }, { 3356, 0x0080 }, { 3357, 0x0000 }, { 3357, 0x0040 },
  { 3358, 0x0000 }, { 3358, 0x0000 }, { 3358, 0x0100 }, { 3359, 0x2000 },
  /* 0x21500 */
  { 3360, 0x0000 }, { 3360, 0x0000 }, { 3360, 0x0000 }, { 3360, 0x0000 },
  { 3360, 0x0000 }, { 3360, 0x0000 }, { 3360, 0x0000 }, { 3360, 0x0080 },
  { 3361, 0x0004 }, { 3362, 0x0040 }, { 3363, 0x0000 }, { 3363, 0x0000 },
  { 3363, 0x0000 }, { 3363, 0x0000 }, { 3363, 0x0000 }, { 3363, 0x0000 },
  /* 0x21600 */
  { 3363, 0x0400 }, { 3364, 0x0208 }, { 3366, 0x0000 }, { 3366, 0x4000 },
  { 3367, 0x0000 }, { 3367, 0x0000 }, { 3367, 0x0002 }, { 3368, 0x0000 },
  { 3368, 0x0000 }, { 3368, 0x0004 }, { 3369, 0x0000 }, { 3369, 0x0500 },
  { 3371, 0x0007 }, { 3374, 0x8028 }, { 3377, 0x01c0 }, { 3380, 0x5c00 },
  /* 0x21700 */
  { 3384, 0x2000 }, { 3385, 0x0001 }, { 3386, 0x0040 }, { 3387, 0x1c00 },
  { 3390, 0x0000 }, { 3390, 0x0080 }, { 3391, 0xf000 }, { 3395, 0x001b },
  { 3399, 0x0000 }, { 3399, 0x0000 }, { 3399, 0x0800 }, { 3400, 0x003f },
  { 3406, 0x0088 }, { 3408, 0x9e00 }, { 3413, 0x8000 }, { 3414, 0x1f60 },
  /* 0x21800 */
  { 3421, 0x0000 }, { 3421, 0x0000 }, { 3421, 0x2701 }, { 3426, 0x0e00 },
  { 3429, 0x0021 }, { 3431, 0x4004 }, { 3433, 0x001e }, { 3437, 0x0880 },
  { 3439, 0x0038 }, { 3442, 0xc000 }, { 3444, 0x0007 }, { 3447, 0xc000 },
  { 3449, 0x0000 }, { 3449, 0x03c2 }, { 3454, 0x0000 }, { 3454, 0x0400 },
  /* 0x21900 */
  { 3455, 0x0038 }, { 3458, 0x1027 }, { 3463, 0x0084 }, { 3465, 0x0800 },
  { 3466, 0x0010 }, { 3467, 0x0100 }, { 3468, 0x0400 }, { 3469, 0x1000 },
  { 3470, 0x0108 }, { 3472, 0x0040 }, { 3473, 0x0000 }, { 3473, 0x0000 },
  { 3473, 0x0000 }, { 3473, 0x0800 }, { 3474, 0x0000 }, { 3474, 0x0008 },
  /* 0x21a00 */
  { 3475, 0x0000 }, { 3475, 0x0000 }, { 3475, 0x2000 }, { 3476, 0x0010 },
  { 3477, 0x0820 }, { 3479, 0x0000 }, { 3479, 0x0000 }, { 3479, 0x0000 },
  { 3479, 0x0000 }, { 3479, 0x0000 }, { 3479, 0x0000 }, { 3479, 0x0000 },
  { 3479, 0x0000 }, { 3479, 0x0000 }, { 3479, 0x0000 }, { 3479, 0x0000 },
  /* 0x21b00 */
  { 3479, 0x0000 }, { 3479, 0x0000 }, { 3479, 0x0000 }, { 3479, 0x0000 },
  { 3479, 0x0010 }, { 3480, 0x0000 }, { 3480, 0x0000 }, { 3480, 0x0000 },
  { 3480, 0x0000 }, { 3480, 0x0000 }, { 3480, 0x0000 }, { 3480, 0x0000 },
  { 3480, 0x0006 }, { 3482, 0x0000 }, { 3482, 0x0000 }, { 3482, 0x0000 },
  /* 0x21c00 */
  { 3482, 0x0000 }, { 3482, 0x0000 }, { 3482, 0x0400 }, { 3483, 0x0000 },
  { 3483, 0x0000 }, { 3483, 0x0000 }, { 3483, 0x0000 }, { 3483, 0x0001 },
  { 3484, 0x0000 }, { 3484, 0x0000 }, { 3484, 0x1024 }, { 3487, 0x0000 },
  { 3487, 0x0000 }, { 3487, 0x0000 }, { 3487, 0x0000 }, { 3487, 0x0000 },
  /* 0x21d00 */
  { 3487, 0x0000 }, { 3487, 0x0000 }, { 3487, 0x0000 }, { 3487, 0x0000 },
  { 3487, 0x0040 }, { 3488, 0x0000 }, { 3488, 0x0000 }, { 3488, 0x0000 },
  { 3488, 0x0000 }, { 3488, 0x0001 }, { 3489, 0x0000 }, { 3489, 0x0400 },
  { 3490, 0x0400 }, { 3491, 0x0002 }, { 3492, 0x0800 }, { 3493, 0x0200 },
  /* 0x21e00 */
  { 3494, 0x0000 }, { 3494, 0x1000 }, { 3495, 0x0000 }, { 3495, 0x2080 },
  { 3497, 0x0000 }, { 3497, 0x0000 }, { 3497, 0x0000 }, { 3497, 0x0000 },
  { 3497, 0x0200 }, { 3498, 0x0000 }, { 3498, 0x0110 }, { 3500, 0x0000 },
  { 3500, 0x0100 }, { 3501, 0x0020 }, { 3502, 0x0000 }, { 3502, 0x0000 },
  /* 0x21f00 */
  { 3502, 0x8000 }, { 3503, 0x0020 }, { 3504, 0x0000 }, { 3504, 0x0000 },
  { 3504, 0x0000 }, { 3504, 0x0000 }, { 3504, 0x0400 }, { 3505, 0x0000 },
  { 3505, 0x0000 }, { 3505, 0x4000 }, { 3506, 0x0002 }, { 3507, 0x0000 },
  { 3507, 0x0000 }, { 3507, 0x0000 }, { 3507, 0x0100 }, { 3508, 0x0000 },
  /* 0x22000 */
  { 3508, 0x0000 }, { 3508, 0x0000 }, { 3508, 0x0000 }, { 3508, 0x0000 },
  { 3508, 0x0220 }, { 3510, 0x0000 }, { 3510, 0x0000 }, { 3510, 0x0000 },
  { 3510, 0x0000 }, { 3510, 0x0400 }, { 3511, 0x0000 }, { 3511, 0x0000 },
  { 3511, 0x0080 }, { 3512, 0x0000 }, { 3512, 0x0000 }, { 3512, 0x1000 },
  /* 0x22100 */
  { 3513, 0x0000 }, { 3513, 0x0000 }, { 3513, 0x0400 }, { 3514, 0x0000 },
  { 3514, 0x0000 }, { 3514, 0x0800 }, { 3515, 0x0000 }, { 3515, 0x0408 },
  { 3517, 0x0000 }, { 3517, 0x0000 }, { 3517, 0x0002 }, { 3518, 0x0000 },
  { 3518, 0x0008 }, { 3519, 0x0000 }, { 3519, 0x0000 }, { 3519, 0x0000 },
  /* 0x22200 */
  { 3519, 0x0100 }, { 3520, 0x0000 }, { 3520, 0x0000 }, { 3520, 0x0000 },
  { 3520, 0x0000 }, { 3520, 0x0000 }, { 3520, 0x0000 }, { 3520, 0x1000 },
  { 3521, 0x0000 }, { 3521, 0x0000 }, { 3521, 0x0000 }, { 3521, 0x0000 },
  { 3521, 0x0000 }, { 3521, 0x0000 }, { 3521, 0x0000 }, { 3521, 0x0000 },
  /* 0x22300 */
  { 3521, 0x0000 }, { 3521, 0x0000 }, { 3521, 0x0022 }, { 3523, 0x0000 },
  { 3523, 0x0000 }, { 3523, 0x0000 }, { 3523, 0x0000 }, { 3523, 0x0000 },
  { 3523, 0x0000 }, { 3523, 0x0000 }, { 3523, 0x0000 }, { 3523, 0x2000 },
  { 3524, 0x0000 }, { 3524, 0x0081 }, { 3526, 0x0000 }, { 3526, 0x0400 },
  /* 0x22400 */
  { 3527, 0x0000 }, { 3527, 0x0000 }, { 3527, 0x0000 }, { 3527, 0x0000 },
  { 3527, 0x0000 }, { 3527, 0x0000 }, { 3527, 0x0020 }, { 3528, 0x0002 },
  { 3529, 0x0800 }, { 3530, 0x0002 }, { 3531, 0x0000 }, { 3531, 0x0001 },
  { 3532, 0x0000 }, { 3532, 0x0000 }, { 3532, 0x2000 }, { 3533, 0x0000 },
  /* 0x22500 */
  { 3533, 0x0000 }, { 3533, 0x0808 }, { 3535, 0x0000 }, { 3535, 0x0001 },
  { 3536, 0x0000 }, { 3536, 0x0010 }, { 3537, 0x0000 }, { 3537, 0x0000 },
  { 3537, 0x2000 }, { 3538, 0x0000 }, { 3538, 0x8000 }, { 3539, 0x4000 },
  { 3540, 0x0000 }, { 3540, 0x0000 }, { 3540, 0x0000 }, { 3540, 0x0000 },
  /* 0x22600 */
  { 3540, 0x0000 }, { 3540, 0x1800 }, { 3542, 0x0800 }, { 3543, 0x0000 },
  { 3543, 0x0000 }, { 3543, 0x0000 }, { 3543, 0x0100 }, { 3544, 0x0400 },
  { 3545, 0x0000 }, { 3545, 0x0140 }, { 3547, 0x0000 }, { 3547, 0x0000 },
  { 3547, 0x0000 }, { 3547, 0x0000 }, { 3547, 0x0000 }, { 3547, 0x0070 },
  /* 0x22700 */
  { 3550, 0x0000 }, { 3550, 0x8810 }, { 3553, 0x0400 }, { 3554, 0x0000 },
  { 3554, 0x0000 }, { 3554, 0x0000 }, { 3554, 0x0000 }, { 3554, 0x0020 },
  { 3555, 0x0002 }, { 3556, 0x0000 }, { 3556, 0x0000 }, { 3556, 0x0030 },
  { 3558, 0x2000 }, { 3559, 0x0000 }, { 3559, 0x0000 }, { 3559, 0x0000 },
  /* 0x22800 */
  { 3559, 0x0008 }, { 3560, 0x0000 }, { 3560, 0x0000 }, { 3560, 0x0000 },
  { 3560, 0x0000 }, { 3560, 0x8000 }, { 3561, 0x0001 }, { 3562, 0x0002 },
  { 3563, 0x0000 }, { 3563, 0x0000 }, { 3563, 0x2000 }, { 3564, 0x0000 },
  { 3564, 0x0002 }, { 3565, 0x0000 }, { 3565, 0x0000 }, { 3565, 0x0080 },
  /* 0x22900 */
  { 3566, 0x0000 }, { 3566, 0x0000 }, { 3566, 0x0040 }, { 3567, 0x0200 },
  { 3568, 0x8000 }, { 3569, 0x0000 }, { 3569, 0x0880 }, { 3571, 0x0000 },
  { 3571, 0x0001 }, { 3572, 0x0008 }, { 3573, 0x0000 }, { 3573, 0x0000 },
  { 3573, 0x0000 }, { 3573, 0x0000 }, { 3573, 0x0000 }, { 3573, 0x0000 },
  /* 0x22a00 */
  { 3573, 0x0000 }, { 3573, 0x0000 }, { 3573, 0x0000 }, { 3573, 0x0000 },
  { 3573, 0x0000 }, { 3573, 0x0000 }, { 3573, 0x0040 }, { 3574, 0x0000 },
  { 3574, 0x0000 }, { 3574, 0x0000 }, { 3574, 0x0000 }, { 3574, 0x0000 },
  { 3574, 0x8000 }, { 3575, 0x0020 }, { 3576, 0x0140 }, { 3578, 0x0000 },
  /* 0x22b00 */
  { 3578, 0x4000 }, { 3579, 0x0000 }, { 3579, 0x0004 }, { 3580, 0x8000 },
  { 3581, 0x0008 }, { 3582, 0x0000 }, { 3582, 0x0400 }, { 3583, 0x0000 },
  { 3583, 0x0000 }, { 3583, 0x0000 }, { 3583, 0x0000 }, { 3583, 0x0000 },
  { 3583, 0x4400 }, { 3585, 0x0000 }, { 3585, 0x0000 }, { 3585, 0x0000 },
  /* 0x22c00 */
  { 3585, 0x0000 }, { 3585, 0x0000 }, { 3585, 0x00c0 }, { 3587, 0x0100 },
  { 3588, 0x1000 }, { 3589, 0x0022 }, { 3591, 0x0004 }, { 3592, 0x0000 },
  { 3592, 0x0100 }, { 3593, 0x0800 }, { 3594, 0x0202 }, { 3596, 0x0084 },
  { 3598, 0x0244 }, { 3601, 0x0000 }, { 3601, 0x0000 }, { 3601, 0x0000 },
  /* 0x22d00 */
  { 3601, 0x0180 }, { 3603, 0x0004 }, { 3604, 0x0000 }, { 3604, 0x0000 },
  { 3604, 0x1010 }, { 3606, 0x0000 }, { 3606, 0x0080 }, { 3607, 0x0000 },
  { 3607, 0x2000 }, { 3608, 0x0020 }, { 3609, 0x0019 }, { 3612, 0x0080 },
  { 3613, 0x0000 }, { 3613, 0x0000 }, { 3613, 0x4000 }, { 3614, 0x0000 },
  /* 0x22e00 */
  { 3614, 0x2000 }, { 3615, 0x0000 }, { 3615, 0x0000 }, { 3615, 0x0040 },
  { 3616, 0x0004 }, { 3617, 0x0000 }, { 3617, 0x0000 }, { 3617, 0x0100 },
  { 3618, 0x0800 }, { 3619, 0x0000 }, { 3619, 0x0000 }, { 3619, 0x0008 },
  { 3620, 0x0000 }, { 3620, 0x0000 }, { 3620, 0x8000 }, { 3621, 0x0000 },
  /* 0x22f00 */
  { 3621, 0x0000 }, { 3621, 0x0000 }, { 3621, 0x0000 }, { 3621, 0x0000 },
  { 3621, 0x0000 }, { 3621, 0x0000 }, { 3621, 0x0000 }, { 3621, 0x0010 },
  { 3622, 0x0000 }, { 3622, 0x0000 }, { 3622, 0x0000 }, { 3622, 0x0000 },
  { 3622, 0x1000 }, { 3623, 0x0000 }, { 3623, 0x0008 }, { 3624, 0x0000 },
  /* 0x23000 */
  { 3624, 0x0000 }, { 3624, 0x0000 }, { 3624, 0x0000 }, { 3624, 0x0008 },
  { 3625, 0x0810 }, { 3627, 0x0000 }, { 3627, 0x0040 }, { 3628, 0x6000 },
  { 3630, 0x4000 }, { 3631, 0x0000 }, { 3631, 0x0000 }, { 3631, 0x1080 },
  { 3633, 0x0000 }, { 3633, 0x0400 }, { 3634, 0x0000 }, { 3634, 0x0000 },
  /* 0x23100 */
  { 3634, 0x0008 }, { 3635, 0x0000 }, { 3635, 0x0000 }, { 3635, 0x2000 },
  { 3636, 0x0000 }, { 3636, 0x0000 }, { 3636, 0x0000 }, { 3636, 0x2000 },
  { 3637, 0x0004 }, { 3638, 0x0000 }, { 3638, 0x0030 }, { 3640, 0x0008 },
  { 3641, 0x0300 }, { 3643, 0x0000 }, { 3643, 0x0000 }, { 3643, 0x0380 },
  /* 0x23200 */
  { 3646, 0x8000 }, { 3647, 0x0000 }, { 3647, 0x8020 }, { 3649, 0x001e },
  { 3653, 0x0000 }, { 3653, 0x0000 }, { 3653, 0x0004 }, { 3654, 0x0000 },
  { 3654, 0x0600 }, { 3656, 0x0000 }, { 3656, 0x3800 }, { 3659, 0x0000 },
  { 3659, 0x0000 }, { 3659, 0x0004 }, { 3660, 0x0003 }, { 3662, 0x0000 },
  /* 0x23300 */
  { 3662, 0x0401 }, { 3664, 0x8000 }, { 3665, 0x0000 }, { 3665, 0x0000 },
  { 3665, 0x0000 }, { 3665, 0x0000 }, { 3665, 0x0000 }, { 3665, 0x0000 },
  { 3665, 0x0000 }, { 3665, 0x0000 }, { 3665, 0x0000 }, { 3665, 0x0010 },
  { 3666, 0x1000 }, { 3667, 0x4000 }, { 3668, 0x0040 }, { 3669, 0x4430 },
  /* 0x23400 */
  { 3673, 0x0001 }, { 3674, 0x0000 }, { 3674, 0x0000 }, { 3674, 0x8000 },
  { 3675, 0x0000 }, { 3675, 0x0001 }, { 3676, 0x8000 }, { 3677, 0x0004 },
  { 3678, 0x0000 }, { 3678, 0x0000 }, { 3678, 0x0000 }, { 3678, 0x0000 },
  { 3678, 0x0000 }, { 3678, 0x0000 }, { 3678, 0x0020 }, { 3679, 0x0000 },
  /* 0x23500 */
  { 3679, 0x0000 }, { 3679, 0x0200 }, { 3680, 0x0000 }, { 3680, 0x0001 },
  { 3681, 0x0000 }, { 3681, 0x0400 }, { 3682, 0x0080 }, { 3683, 0x0000 },
  { 3683, 0x0000 }, { 3683, 0x1220 }, { 3686, 0x0000 }, { 3686, 0x0000 },
  { 3686, 0xe000 }, { 3689, 0x0000 }, { 3689, 0x0000 }, { 3689, 0x0008 },
  /* 0x23600 */
  { 3690, 0x0001 }, { 3691, 0x0400 }, { 3692, 0x0000 }, { 3692, 0x1000 },
  { 3693, 0x0001 }, { 3694, 0x8200 }, { 3696, 0x0000 }, { 3696, 0x0080 },
  { 3697, 0x0000 }, { 3697, 0x0000 }, { 3697, 0x2040 }, { 3699, 0x0400 },
  { 3700, 0x0000 }, { 3700, 0x8000 }, { 3701, 0x4000 }, { 3702, 0x0000 },
  /* 0x23700 */
  { 3702, 0x0008 }, { 3703, 0x0040 }, { 3704, 0xa001 }, { 3707, 0x8000 },
  { 3708, 0x0000 }, { 3708, 0x0000 }, { 3708, 0x0040 }, { 3709, 0x0000 },
  { 3709, 0x0002 }, { 3710, 0x0000 }, { 3710, 0x0004 }, { 3711, 0x1000 },
  { 3712, 0x0004 }, { 3713, 0x00e0 }, { 3716, 0x0000 }, { 3716, 0x0000 },
  /* 0x23800 */
  { 3716, 0x0000 }, { 3716, 0x0000 }, { 3716, 0x0000 }, { 3716, 0x0400 },
  { 3717, 0x0000 }, { 3717, 0x0000 }, { 3717, 0x0000 }, { 3717, 0x0000 },
  { 3717, 0x0000 }, { 3717, 0x0000 }, { 3717, 0x0000 }, { 3717, 0x0000 },
  { 3717, 0x0000 }, { 3717, 0x0000 }, { 3717, 0x0000 }, { 3717, 0x0000 },
  /* 0x23900 */
  { 3717, 0x0000 }, { 3717, 0x0000 }, { 3717, 0x0000 }, { 3717, 0x0000 },
  { 3717, 0x0000 }, { 3717, 0x0000 }, { 3717, 0x0000 }, { 3717, 0x0000 },
  { 3717, 0x0000 }, { 3717, 0x0000 }, { 3717, 0x0000 }, { 3717, 0x0000 },
  { 3717, 0x0004 }, { 3718, 0x0000 }, { 3718, 0x0000 }, { 3718, 0x0000 },
  /* 0x23a00 */
  { 3718, 0x0000 }, { 3718, 0x0000 }, { 3718, 0x0000 }, { 3718, 0x0000 },
  { 3718, 0x0000 }, { 3718, 0x0000 }, { 3718, 0x0000 }, { 3718, 0x0000 },
  { 3718, 0x0000 }, { 3718, 0x0000 }, { 3718, 0x0080 }, { 3719, 0x0000 },
  { 3719, 0x0000 }, { 3719, 0x0800 }, { 3720, 0x4000 }, { 3721, 0x0400 },
  /* 0x23b00 */
  { 3722, 0x0000 }, { 3722, 0x0000 }, { 3722, 0x0000 }, { 3722, 0x0000 },
  { 3722, 0x0000 }, { 3722, 0x0400 }, { 3723, 0x0000 }, { 3723, 0x0000 },
  { 3723, 0x0000 }, { 3723, 0x0000 }, { 3723, 0x0000 }, { 3723, 0x0000 },
  { 3723, 0x0000 }, { 3723, 0x0000 }, { 3723, 0x0000 }, { 3723, 0x0000 },
  /* 0x23c00 */
  { 3723, 0x0000 }, { 3723, 0x0000 }, { 3723, 0x0000 }, { 3723, 0x0000 },
  { 3723, 0x0000 }, { 3723, 0x0000 }, { 3723, 0x0000 }, { 3723, 0x0000 },
  { 3723, 0x0000 }, { 3723, 0x0e00 }, { 3726, 0x0000 }, { 3726, 0x00a0 },
  { 3728, 0x0380 }, { 3731, 0x0000 }, { 3731, 0x0000 }, { 3731, 0xf000 },
  /* 0x23d00 */
  { 3735, 0x0000 }, { 3735, 0x0000 }, { 3735, 0x0000 }, { 3735, 0x0000 },
  { 3735, 0x0001 }, { 3736, 0x0800 }, { 3737, 0x0000 }, { 3737, 0x4000 },
  { 3738, 0x8000 }, { 3739, 0x0000 }, { 3739, 0x0000 }, { 3739, 0x3fc0 },
  { 3747, 0x0000 }, { 3747, 0x0000 }, { 3747, 0x0008 }, { 3748, 0x0100 },
  /* 0x23e00 */
  { 3749, 0x0000 }, { 3749, 0x0002 }, { 3750, 0xf000 }, { 3754, 0x0203 },
  { 3757, 0x0000 }, { 3757, 0x0000 }, { 3757, 0x0000 }, { 3757, 0x0000 },
  { 3757, 0x0f00 }, { 3761, 0x0000 }, { 3761, 0x0000 }, { 3761, 0x8200 },
  { 3763, 0x0000 }, { 3763, 0x0080 }, { 3764, 0x0000 }, { 3764, 0x1f80 },
  /* 0x23f00 */
  { 3770, 0x0000 }, { 3770, 0x0000 }, { 3770, 0x0000 }, { 3770, 0x0020 },
  { 3771, 0x0402 }, { 3773, 0x0000 }, { 3773, 0x0000 }, { 3773, 0x8000 },
  { 3774, 0x8007 }, { 3778, 0x0000 }, { 3778, 0x0000 }, { 3778, 0x0090 },
  { 3780, 0x0021 }, { 3782, 0x0000 }, { 3782, 0xf800 }, { 3787, 0x0001 },
  /* 0x24000 */
  { 3788, 0x0000 }, { 3788, 0x0002 }, { 3789, 0x0000 }, { 3789, 0x3e00 },
  { 3794, 0x0000 }, { 3794, 0x0080 }, { 3795, 0x0000 }, { 3795, 0x0000 },
  { 3795, 0x3820 }, { 3799, 0x0002 }, { 3800, 0x0000 }, { 3800, 0x0000 },
  { 3800, 0x0200 }, { 3801, 0x0000 }, { 3801, 0x0002 }, { 3802, 0x0000 },
  /* 0x24100 */
  { 3802, 0x8010 }, { 3804, 0x0200 }, { 3805, 0x0000 }, { 3805, 0x8000 },
  { 3806, 0x0011 }, { 3808, 0x90e0 }, { 3813, 0x0000 }, { 3813, 0x0480 },
  { 3815, 0x0000 }, { 3815, 0x0000 }, { 3815, 0x1038 }, { 3819, 0x0020 },
  { 3820, 0x2000 }, { 3821, 0x0000 }, { 3821, 0x0004 }, { 3822, 0x1000 },
  /* 0x24200 */
  { 3823, 0x0000 }, { 3823, 0x0800 }, { 3824, 0x0000 }, { 3824, 0x0000 },
  { 3824, 0x0800 }, { 3825, 0x0240 }, { 3827, 0x0000 }, { 3827, 0x01c0 },
  { 3830, 0x0010 }, { 3831, 0x0028 }, { 3833, 0x0020 }, { 3834, 0x0000 },
  { 3834, 0x0602 }, { 3837, 0x0000 }, { 3837, 0x4000 }, { 3838, 0x0400 },
  /* 0x24300 */
  { 3839, 0x2000 }, { 3840, 0x0400 }, { 3841, 0x0000 }, { 3841, 0x0010 },
  { 3842, 0x0100 }, { 3843, 0x0000 }, { 3843, 0x003c }, { 3847, 0x0000 },
  { 3847, 0x1000 }, { 3848, 0x1040 }, { 3850, 0x0000 }, { 3850, 0x2000 },
  { 3851, 0x0002 }, { 3852, 0x0000 }, { 3852, 0x0600 }, { 3854, 0x0104 },
  /* 0x24400 */
  { 3856, 0x0010 }, { 3857, 0x0000 }, { 3857, 0x0000 }, { 3857, 0x0060 },
  { 3859, 0x0000 }, { 3859, 0x0c00 }, { 3861, 0x0000 }, { 3861, 0x0008 },
  { 3862, 0x0180 }, { 3864, 0x0000 }, { 3864, 0x0000 }, { 3864, 0x1200 },
  { 3866, 0x4000 }, { 3867, 0x0048 }, { 3869, 0x0000 }, { 3869, 0x0000 },
  /* 0x24500 */
  { 3869, 0x0000 }, { 3869, 0x0000 }, { 3869, 0x0002 }, { 3870, 0x0000 },
  { 3870, 0x0000 }, { 3870, 0x0000 }, { 3870, 0x0000 }, { 3870, 0x0100 },
  { 3871, 0x0000 }, { 3871, 0x0000 }, { 3871, 0x0000 }, { 3871, 0x0000 },
  { 3871, 0x0100 }, { 3872, 0x0000 }, { 3872, 0x0000 }, { 3872, 0x0000 },
  /* 0x24600 */
  { 3872, 0x0000 }, { 3872, 0x0100 }, { 3873, 0x0400 }, { 3874, 0x0000 },
  { 3874, 0x0000 }, { 3874, 0x0000 }, { 3874, 0x0020 }, { 3875, 0x0010 },
  { 3876, 0x0000 }, { 3876, 0x0080 }, { 3877, 0x0000 }, { 3877, 0x0000 },
  { 3877, 0x0000 }, { 3877, 0x0010 }, { 3878, 0x0000 }, { 3878, 0x0000 },
  /* 0x24700 */
  { 3878, 0x0040 }, { 3879, 0x0000 }, { 3879, 0x8020 }, { 3881, 0x0000 },
  { 3881, 0x0000 }, { 3881, 0x0000 }, { 3881, 0x0000 }, { 3881, 0x0000 },
  { 3881, 0x8000 }, { 3882, 0x0000 }, { 3882, 0x0000 }, { 3882, 0x0000 },
  { 3882, 0x0000 }, { 3882, 0x0000 }, { 3882, 0x0001 }, { 3883, 0x0000 },
  /* 0x24800 */
  { 3883, 0x0000 }, { 3883, 0x0004 }, { 3884, 0x0008 }, { 3885, 0x0000 },
  { 3885, 0x0000 }, { 3885, 0x0000 }, { 3885, 0x0000 }, { 3885, 0x0000 },
  { 3885, 0x0004 }, { 3886, 0x0000 }, { 3886, 0x0000 }, { 3886, 0x0000 },
  { 3886, 0x0000 }, { 3886, 0x0000 }, { 3886, 0x0200 }, { 3887, 0x880f },
  /* 0x24900 */
  { 3893, 0x1003 }, { 3896, 0x02c0 }, { 3899, 0x8000 }, { 3900, 0xc018 },
  { 3904, 0x000f }, { 3908, 0x0000 }, { 3908, 0x000c }, { 3910, 0x8070 },
  { 3914, 0xff04 }, { 3923, 0x0010 }, { 3924, 0x3a90 }, { 3930, 0x0f80 },
  { 3935, 0x0020 }, { 3936, 0xc401 }, { 3940, 0x3028 }, { 3944, 0x0bc0 },
  /* 0x24a00 */
  { 3949, 0x4000 }, { 3950, 0x0024 }, { 3952, 0x07fe }, { 3962, 0x4000 },
  { 3963, 0xc424 }, { 3968, 0x2003 }, { 3971, 0x00e0 }, { 3974, 0x0782 },
  { 3979, 0x1000 }, { 3980, 0x0078 }, { 3984, 0x00f0 }, { 3988, 0x1c0e },
  { 3994, 0x0481 }, { 3997, 0x8002 }, { 3999, 0x0204 }, { 4001, 0x0000 },
  /* 0x24b00 */
  { 4001, 0x0000 }, { 4001, 0x0000 }, { 4001, 0x0000 }, { 4001, 0x0000 },
  { 4001, 0x0000 }, { 4001, 0x0000 }, { 4001, 0x4000 }, { 4002, 0x0000 },
  { 4002, 0x0000 }, { 4002, 0x0000 }, { 4002, 0x0000 }, { 4002, 0x0000 },
  { 4002, 0x0000 }, { 4002, 0x0000 }, { 4002, 0x0000 }, { 4002, 0x0020 },
  /* 0x24c00 */
  { 4003, 0x0200 }, { 4004, 0x0000 }, { 4004, 0x0000 }, { 4004, 0x0000 },
  { 4004, 0x0000 }, { 4004, 0x0000 }, { 4004, 0x0000 }, { 4004, 0x0000 },
  { 4004, 0x0000 }, { 4004, 0xc000 }, { 4006, 0x0000 }, { 4006, 0x0000 },
  { 4006, 0x0200 }, { 4007, 0x0200 }, { 4008, 0x0000 }, { 4008, 0x0000 },
  /* 0x24d00 */
  { 4008, 0x0040 }, { 4009, 0x0008 }, { 4010, 0x0000 }, { 4010, 0x0000 },
  { 4010, 0x0000 }, { 4010, 0x0000 }, { 4010, 0x0000 }, { 4010, 0x0000 },
  { 4010, 0x0000 }, { 4010, 0x0000 }, { 4010, 0x0000 }, { 4010, 0x0100 },
  { 4011, 0x0000 }, { 4011, 0x0000 }, { 4011, 0x0c00 }, { 4013, 0x0000 },
  /* 0x24e00 */
  { 4013, 0x0000 }, { 4013, 0x0000 }, { 4013, 0x0000 }, { 4013, 0x0800 },
  { 4014, 0x0000 }, { 4014, 0x0001 }, { 4015, 0x0000 }, { 4015, 0x0000 },
  { 4015, 0x0000 }, { 4015, 0x0000 }, { 4015, 0x00a0 }, { 4017, 0x0000 },
  { 4017, 0x0000 }, { 4017, 0x0000 }, { 4017, 0x0000 }, { 4017, 0x0000 },
  /* 0x24f00 */
  { 4017, 0x4000 }, { 4018, 0x0000 }, { 4018, 0x0000 }, { 4018, 0x0000 },
  { 4018, 0x0000 }, { 4018, 0x1000 }, { 4019, 0x0000 }, { 4019, 0x0000 },
  { 4019, 0x0044 }, { 4021, 0x0480 }, { 4023, 0x0200 }, { 4024, 0x0100 },
  { 4025, 0x0004 }, { 4026, 0x0000 }, { 4026, 0x0000 }, { 4026, 0x0000 },
  /* 0x25000 */
  { 4026, 0x0000 }, { 4026, 0x0000 }, { 4026, 0x1000 }, { 4027, 0x0000 },
  { 4027, 0x0000 }, { 4027, 0x0004 }, { 4028, 0x0000 }, { 4028, 0x0000 },
  { 4028, 0x0000 }, { 4028, 0x2000 }, { 4029, 0x0000 }, { 4029, 0x0000 },
  { 4029, 0x0000 }, { 4029, 0x0000 }, { 4029, 0x0000 }, { 4029, 0x0000 },
  /* 0x25100 */
  { 4029, 0x0000 }, { 4029, 0x0000 }, { 4029, 0x0800 }, { 4030, 0x0000 },
  { 4030, 0x0100 }, { 4031, 0x0000 }, { 4031, 0x0000 }, { 4031, 0x6000 },
  { 4033, 0x0000 }, { 4033, 0x0000 }, { 4033, 0x0000 }, { 4033, 0x0000 },
  { 4033, 0x0000 }, { 4033, 0x0000 }, { 4033, 0x00c8 }, { 4036, 0x0000 },
  /* 0x25200 */
  { 4036, 0x0000 }, { 4036, 0x0000 }, { 4036, 0x0003 }, { 4038, 0x0000 },
  { 4038, 0x0000 }, { 4038, 0x0001 }, { 4039, 0x0000 }, { 4039, 0x0000 },
  { 4039, 0x0000 }, { 4039, 0x0200 }, { 4040, 0x0000 }, { 4040, 0x0000 },
  { 4040, 0x0080 }, { 4041, 0x0100 }, { 4042, 0x0000 }, { 4042, 0x0000 },
  /* 0x25300 */
  { 4042, 0x4000 }, { 4043, 0x000a }, { 4045, 0x0000 }, { 4045, 0x0000 },
  { 4045, 0x0000 }, { 4045, 0x0000 }, { 4045, 0x0000 }, { 4045, 0x0000 },
  { 4045, 0x0000 }, { 4045, 0x0000 }, { 4045, 0x0000 }, { 4045, 0x0000 },
  { 4045, 0x0000 }, { 4045, 0x0000 }, { 4045, 0x0000 }, { 4045, 0x0000 },
  /* 0x25400 */
  { 4045, 0x0000 }, { 4045, 0x0200 }, { 4046, 0x8020 }, { 4048, 0x0001 },
  { 4049, 0x0040 }, { 4050, 0x0000 }, { 4050, 0x5000 }, { 4052, 0x0000 },
  { 4052, 0x0000 }, { 4052, 0x0000 }, { 4052, 0x0000 }, { 4052, 0x0000 },
  { 4052, 0x0000 }, { 4052, 0x0000 }, { 4052, 0x0000 }, { 4052, 0x0000 },
  /* 0x25500 */
  { 4052, 0x0000 }, { 4052, 0x0000 }, { 4052, 0x0000 }, { 4052, 0x8022 },
  { 4055, 0x0000 }, { 4055, 0x7800 }, { 4059, 0x0064 }, { 4062, 0x0000 },
  { 4062, 0x8012 }, { 4065, 0x0000 }, { 4065, 0x0000 }, { 4065, 0x0200 },
  { 4066, 0x0000 }, { 4066, 0x0820 }, { 4068, 0x0001 }, { 4069, 0x0000 },
  /* 0x25600 */
  { 4069, 0x0020 }, { 4070, 0x0000 }, { 4070, 0x0000 }, { 4070, 0x0020 },
  { 4071, 0x0000 }, { 4071, 0x0002 }, { 4072, 0x0000 }, { 4072, 0x0000 },
  { 4072, 0x0008 }, { 4073, 0x0000 }, { 4073, 0x0000 }, { 4073, 0x0000 },
  { 4073, 0x0000 }, { 4073, 0x0000 }, { 4073, 0x0008 }, { 4074, 0x0040 },
  /* 0x25700 */
  { 4075, 0x0040 }, { 4076, 0x2000 }, { 4077, 0x0020 }, { 4078, 0x2000 },
  { 4079, 0x0000 }, { 4079, 0x0000 }, { 4079, 0x0000 }, { 4079, 0x0004 },
  { 4080, 0x0000 }, { 4080, 0x0000 }, { 4080, 0x0000 }, { 4080, 0x0000 },
  { 4080, 0x0080 }, { 4081, 0x8000 }, { 4082, 0x0003 }, { 4084, 0x0000 },
  /* 0x25800 */
  { 4084, 0x0000 }, { 4084, 0x0000 }, { 4084, 0x0000 }, { 4084, 0x0000 },
  { 4084, 0x0000 }, { 4084, 0x2080 }, { 4086, 0x0000 }, { 4086, 0x0004 },
  { 4087, 0x0000 }, { 4087, 0x0000 }, { 4087, 0x0000 }, { 4087, 0x0000 },
  { 4087, 0x0100 }, { 4088, 0x0000 }, { 4088, 0x0002 }, { 4089, 0x0000 },
  /* 0x25900 */
  { 4089, 0x0008 }, { 4090, 0x0000 }, { 4090, 0x0000 }, { 4090, 0x0000 },
  { 4090, 0x0040 }, { 4091, 0x0040 }, { 4092, 0x0000 }, { 4092, 0x0000 },
  { 4092, 0x0000 }, { 4092, 0x0000 }, { 4092, 0x1000 }, { 4093, 0x0000 },
  { 4093, 0x1000 }, { 4094, 0x0000 }, { 4094, 0x0000 }, { 4094, 0x0000 },
  /* 0x25a00 */
  { 4094, 0x0000 }, { 4094, 0x0000 }, { 4094, 0x0000 }, { 4094, 0x0000 },
  { 4094, 0x0000 }, { 4094, 0x0000 }, { 4094, 0x0000 }, { 4094, 0x0000 },
  { 4094, 0x0000 }, { 4094, 0x1020 }, { 4096, 0xc000 }, { 4098, 0x0000 },
  { 4098, 0x0000 }, { 4098, 0x0000 }, { 4098, 0x0200 }, { 4099, 0x0000 },
  /* 0x25b00 */
  { 4099, 0x0000 }, { 4099, 0x0000 }, { 4099, 0x0000 }, { 4099, 0x0000 },
  { 4099, 0x0000 }, { 4099, 0x0000 }, { 4099, 0x0000 }, { 4099, 0x0010 },
  { 4100, 0x0200 }, { 4101, 0x0000 }, { 4101, 0x0000 }, { 4101, 0x0018 },
  { 4103, 0x0040 }, { 4104, 0x0000 }, { 4104, 0x0110 }, { 4106, 0x0000 },
  /* 0x25c00 */
  { 4106, 0x0042 }, { 4108, 0x0000 }, { 4108, 0x0002 }, { 4109, 0x0000 },
  { 4109, 0x0400 }, { 4110, 0x0000 }, { 4110, 0x0020 }, { 4111, 0x0000 },
  { 4111, 0x0000 }, { 4111, 0x0002 }, { 4112, 0x0000 }, { 4112, 0x0000 },
  { 4112, 0x0003 }, { 4114, 0x0000 }, { 4114, 0x0000 }, { 4114, 0x4000 },
  /* 0x25d00 */
  { 4115, 0x0000 }, { 4115, 0x0000 }, { 4115, 0x0001 }, { 4116, 0x0000 },
  { 4116, 0x0008 }, { 4117, 0x0000 }, { 4117, 0x0000 }, { 4117, 0x0000 },
  { 4117, 0x0000 }, { 4117, 0x0000 }, { 4117, 0x0000 }, { 4117, 0x0000 },
  { 4117, 0x0000 }, { 4117, 0x0000 }, { 4117, 0x0000 }, { 4117, 0x0000 },
  /* 0x25e00 */
  { 4117, 0x4000 }, { 4118, 0x0000 }, { 4118, 0x0000 }, { 4118, 0x0000 },
  { 4118, 0x0200 }, { 4119, 0x0000 }, { 4119, 0x0000 }, { 4119, 0x0000 },
  { 4119, 0x000e }, { 4122, 0x0000 }, { 4122, 0x0040 }, { 4123, 0x1000 },
  { 4124, 0x0000 }, { 4124, 0x0180 }, { 4126, 0x0000 }, { 4126, 0x0000 },
  /* 0x25f00 */
  { 4126, 0x0000 }, { 4126, 0x0400 }, { 4127, 0x0000 }, { 4127, 0x0000 },
  { 4127, 0x0800 }, { 4128, 0x0000 }, { 4128, 0x0000 }, { 4128, 0x0000 },
  { 4128, 0x0000 }, { 4128, 0x0000 }, { 4128, 0x0000 }, { 4128, 0x0000 },
  { 4128, 0x0000 }, { 4128, 0x0000 }, { 4128, 0x0006 }, { 4130, 0x0000 },
  /* 0x26000 */
  { 4130, 0x0000 }, { 4130, 0x0000 }, { 4130, 0x0200 }, { 4131, 0x0000 },
  { 4131, 0x0100 }, { 4132, 0x0000 }, { 4132, 0x0010 }, { 4133, 0x0000 },
  { 4133, 0x0008 }, { 4134, 0x0080 }, { 4135, 0x0030 }, { 4137, 0x0000 },
  { 4137, 0x0000 }, { 4137, 0x0000 }, { 4137, 0x0000 }, { 4137, 0x0000 },
  /* 0x26100 */
  { 4137, 0x0004 }, { 4138, 0x0000 }, { 4138, 0x0002 }, { 4139, 0x0000 },
  { 4139, 0x0000 }, { 4139, 0x1e00 }, { 4143, 0x0000 }, { 4143, 0x0000 },
  { 4143, 0x0000 }, { 4143, 0x0000 }, { 4143, 0x6000 }, { 4145, 0x0004 },
  { 4146, 0x0000 }, { 4146, 0x2000 }, { 4147, 0x0000 }, { 4147, 0x0000 },
  /* 0x26200 */
  { 4147, 0x0000 }, { 4147, 0x0000 }, { 4147, 0x0000 }, { 4147, 0x0000 },
  { 4147, 0x0000 }, { 4147, 0x0100 }, { 4148, 0x0c02 }, { 4151, 0x0000 },
  { 4151, 0x0000 }, { 4151, 0x0000 }, { 4151, 0x0000 }, { 4151, 0x0000 },
  { 4151, 0x0000 }, { 4151, 0x0001 }, { 4152, 0x0000 }, { 4152, 0x0000 },
  /* 0x26300 */
  { 4152, 0x0000 }, { 4152, 0x0000 }, { 4152, 0x0000 }, { 4152, 0x0020 },
  { 4153, 0x1800 }, { 4155, 0x0002 }, { 4156, 0x0000 }, { 4156, 0x0000 },
  { 4156, 0x0000 }, { 4156, 0x0000 }, { 4156, 0x0000 }, { 4156, 0x4000 },
  { 4157, 0x0000 }, { 4157, 0x0000 }, { 4157, 0x0000 }, { 4157, 0x0120 },
  /* 0x26400 */
  { 4159, 0x0004 }, { 4160, 0x0007 }, { 4163, 0x0000 }, { 4163, 0x0000 },
  { 4163, 0x0400 }, { 4164, 0x0000 }, { 4164, 0x0200 }, { 4165, 0x0000 },
  { 4165, 0x2310 }, { 4169, 0x0100 }, { 4170, 0x0000 }, { 4170, 0x0000 },
  { 4170, 0x0000 }, { 4170, 0x0000 }, { 4170, 0x0000 }, { 4170, 0x0000 },
  /* 0x26500 */
  { 4170, 0x0000 }, { 4170, 0x0004 }, { 4171, 0x0000 }, { 4171, 0x0000 },
  { 4171, 0x0000 }, { 4171, 0x0000 }, { 4171, 0x0000 }, { 4171, 0x0004 },
  { 4172, 0x0000 }, { 4172, 0x0000 }, { 4172, 0x2001 }, { 4174, 0x8000 },
  { 4175, 0x0000 }, { 4175, 0x0000 }, { 4175, 0x0000 }, { 4175, 0x0000 },
  /* 0x26600 */
  { 4175, 0x0000 }, { 4175, 0x0004 }, { 4176, 0x0040 }, { 4177, 0x0000 },
  { 4177, 0x0000 }, { 4177, 0x0000 }, { 4177, 0x0000 }, { 4177, 0x0000 },
  { 4177, 0x0000 }, { 4177, 0x0000 }, { 4177, 0x8000 }, { 4178, 0x0022 },
  { 4180, 0x0000 }, { 4180, 0x0400 }, { 4181, 0x0100 }, { 4182, 0x1000 },
  /* 0x26700 */
  { 4183, 0x0000 }, { 4183, 0x0040 }, { 4184, 0x0000 }, { 4184, 0x0000 },
  { 4184, 0x0002 }, { 4185, 0x0000 }, { 4185, 0x0000 }, { 4185, 0x0000 },
  { 4185, 0x0000 }, { 4185, 0x0200 }, { 4186, 0x0000 }, { 4186, 0x0018 },
  { 4188, 0x1000 }, { 4189, 0x0000 }, { 4189, 0x0000 }, { 4189, 0x0000 },
  /* 0x26800 */
  { 4189, 0x0000 }, { 4189, 0x1000 }, { 4190, 0x0000 }, { 4190, 0x0000 },
  { 4190, 0x0040 }, { 4191, 0x4000 }, { 4192, 0x4000 }, { 4193, 0x0000 },
  { 4193, 0x0500 }, { 4195, 0x0008 }, { 4196, 0x0000 }, { 4196, 0x0000 },
  { 4196, 0x0080 }, { 4197, 0x0000 }, { 4197, 0x0000 }, { 4197, 0x0000 },
  /* 0x26900 */
  { 4197, 0x4000 }, { 4198, 0x0002 }, { 4199, 0x0040 }, { 4200, 0x0200 },
  { 4201, 0x0000 }, { 4201, 0x0002 }, { 4202, 0x0000 }, { 4202, 0x0000 },
  { 4202, 0x0000 }, { 4202, 0x0000 }, { 4202, 0x0100 }, { 4203, 0x0020 },
  { 4204, 0x0000 }, { 4204, 0x0000 }, { 4204, 0x0000 }, { 4204, 0x0404 },
  /* 0x26a00 */
  { 4206, 0x0000 }, { 4206, 0x0000 }, { 4206, 0x6000 }, { 4208, 0x0010 },
  { 4209, 0x0004 }, { 4210, 0x0006 }, { 4212, 0x0000 }, { 4212, 0x0000 },
  { 4212, 0x0000 }, { 4212, 0x0000 }, { 4212, 0x0000 }, { 4212, 0x0000 },
  { 4212, 0x0000 }, { 4212, 0x0000 }, { 4212, 0x0000 }, { 4212, 0x0000 },
  /* 0x26b00 */
  { 4212, 0x0420 }, { 4214, 0x0008 }, { 4215, 0x0100 }, { 4216, 0x0000 },
  { 4216, 0x0000 }, { 4216, 0x080f }, { 4221, 0x0000 }, { 4221, 0x0020 },
  { 4222, 0x0004 }, { 4223, 0x20c0 }, { 4226, 0x0000 }, { 4226, 0x0008 },
  { 4227, 0x0001 }, { 4228, 0x0000 }, { 4228, 0x0000 }, { 4228, 0x0080 },
  /* 0x26c00 */
  { 4229, 0x0000 }, { 4229, 0x0000 }, { 4229, 0x0002 }, { 4230, 0x0000 },
  { 4230, 0x0001 }, { 4231, 0x0000 }, { 4231, 0x0000 }, { 4231, 0xc000 },
  { 4233, 0x0007 }, { 4236, 0x0000 }, { 4236, 0x0010 }, { 4237, 0x2180 },
  { 4240, 0x0009 }, { 4242, 0x0002 }, { 4243, 0x0000 }, { 4243, 0x0000 },
  /* 0x26d00 */
  { 4243, 0x0000 }, { 4243, 0x0000 }, { 4243, 0x07fc }, { 4252, 0x0000 },
  { 4252, 0x0000 }, { 4252, 0x0002 }, { 4253, 0x0000 }, { 4253, 0x0000 },
  { 4253, 0x0000 }, { 4253, 0x0000 }, { 4253, 0x40ff }, { 4262, 0x0000 },
  { 4262, 0x0000 }, { 4262, 0x1000 }, { 4263, 0x0c00 }, { 4265, 0x0001 },
  /* 0x26e00 */
  { 4266, 0x00a1 }, { 4269, 0x0004 }, { 4270, 0x0000 }, { 4270, 0x0000 },
  { 4270, 0x003c }, { 4274, 0x0000 }, { 4274, 0x4000 }, { 4275, 0x0084 },
  { 4277, 0x0010 }, { 4278, 0x0200 }, { 4279, 0x0000 }, { 4279, 0x0000 },
  { 4279, 0x0000 }, { 4279, 0x00ff }, { 4287, 0x0000 }, { 4287, 0x0000 },
  /* 0x26f00 */
  { 4287, 0x0000 }, { 4287, 0x0000 }, { 4287, 0x0040 }, { 4288, 0x0000 },
  { 4288, 0x0000 }, { 4288, 0x0000 }, { 4288, 0x0000 }, { 4288, 0x0018 },
  { 4290, 0x0000 }, { 4290, 0x8000 }, { 4291, 0x0002 }, { 4292, 0x0000 },
  { 4292, 0x0000 }, { 4292, 0xc000 }, { 4294, 0x0000 }, { 4294, 0x0000 },
  /* 0x27000 */
  { 4294, 0x4000 }, { 4295, 0x0000 }, { 4295, 0x0000 }, { 4295, 0x0000 },
  { 4295, 0x0800 }, { 4296, 0x000c }, { 4298, 0x0000 }, { 4298, 0x0000 },
  { 4298, 0x0100 }, { 4299, 0x0000 }, { 4299, 0xe000 }, { 4302, 0x0000 },
  { 4302, 0x2000 }, { 4303, 0x0000 }, { 4303, 0x0000 }, { 4303, 0x0100 },
  /* 0x27100 */
  { 4304, 0x1200 }, { 4306, 0x0000 }, { 4306, 0x00c0 }, { 4308, 0x0000 },
  { 4308, 0x0000 }, { 4308, 0x0000 }, { 4308, 0x0030 }, { 4310, 0x0020 },
  { 4311, 0x0000 }, { 4311, 0x0000 }, { 4311, 0x0000 }, { 4311, 0x0000 },
  { 4311, 0x2000 }, { 4312, 0x0000 }, { 4312, 0x0000 }, { 4312, 0x0000 },
  /* 0x27200 */
  { 4312, 0x0000 }, { 4312, 0x0800 }, { 4313, 0x0000 }, { 4313, 0x0000 },
  { 4313, 0x0000 }, { 4313, 0x0000 }, { 4313, 0x0000 }, { 4313, 0x0000 },
  { 4313, 0x0821 }, { 4316, 0x0000 }, { 4316, 0x0000 }, { 4316, 0x0044 },
  { 4318, 0x0000 }, { 4318, 0x0000 }, { 4318, 0x0040 }, { 4319, 0x0000 },
  /* 0x27300 */
  { 4319, 0x0000 }, { 4319, 0x0000 }, { 4319, 0x0000 }, { 4319, 0x0000 },
  { 4319, 0x0000 }, { 4319, 0x0000 }, { 4319, 0x0000 }, { 4319, 0x0000 },
  { 4319, 0x0000 }, { 4319, 0x0400 }, { 4320, 0x0000 }, { 4320, 0x0000 },
  { 4320, 0x0000 }, { 4320, 0x0000 }, { 4320, 0x0000 }, { 4320, 0x0000 },
  /* 0x27400 */
  { 4320, 0x0000 }, { 4320, 0x0000 }, { 4320, 0x0004 }, { 4321, 0x0000 },
  { 4321, 0x0000 }, { 4321, 0x0001 }, { 4322, 0x0000 }, { 4322, 0x0000 },
  { 4322, 0x0050 }, { 4324, 0x0000 }, { 4324, 0x0000 }, { 4324, 0x0000 },
  { 4324, 0x0000 }, { 4324, 0x0000 }, { 4324, 0x0000 }, { 4324, 0x0000 },
  /* 0x27500 */
  { 4324, 0x0000 }, { 4324, 0x0000 }, { 4324, 0x0000 }, { 4324, 0x0000 },
  { 4324, 0x0000 }, { 4324, 0x0000 }, { 4324, 0x0000 }, { 4324, 0x0010 },
  { 4325, 0x0000 }, { 4325, 0x0000 }, { 4325, 0x0008 }, { 4326, 0x0000 },
  { 4326, 0x0000 }, { 4326, 0x0000 }, { 4326, 0x0011 }, { 4328, 0x6000 },
  /* 0x27600 */
  { 4330, 0x1080 }, { 4332, 0x0000 }, { 4332, 0x0000 }, { 4332, 0x0204 },
  { 4334, 0x0000 }, { 4334, 0x00e0 }, { 4337, 0x0000 }, { 4337, 0x0000 },
  { 4337, 0x0000 }, { 4337, 0x0010 }, { 4338, 0x0000 }, { 4338, 0x0000 },
  { 4338, 0x0000 }, { 4338, 0x0000 }, { 4338, 0x0000 }, { 4338, 0x0000 },
  /* 0x27700 */
  { 4338, 0x8000 }, { 4339, 0x0000 }, { 4339, 0x0000 }, { 4339, 0x0060 },
  { 4341, 0x0002 }, { 4342, 0x4000 }, { 4343, 0x0000 }, { 4343, 0x0000 },
  { 4343, 0x0030 }, { 4345, 0x0000 }, { 4345, 0x0000 }, { 4345, 0x0000 },
  { 4345, 0x1000 }, { 4346, 0x0000 }, { 4346, 0x0000 }, { 4346, 0x0000 },
  /* 0x27800 */
  { 4346, 0x0000 }, { 4346, 0x0000 }, { 4346, 0x0000 }, { 4346, 0x0000 },
  { 4346, 0x0000 }, { 4346, 0x0100 }, { 4347, 0x0000 }, { 4347, 0x0001 },
  { 4348, 0x0000 }, { 4348, 0x2000 }, { 4349, 0x0000 }, { 4349, 0x0004 },
  { 4350, 0x0100 }, { 4351, 0x0000 }, { 4351, 0x0000 }, { 4351, 0x0000 },
  /* 0x27900 */
  { 4351, 0x0000 }, { 4351, 0x0000 }, { 4351, 0x0010 }, { 4352, 0x0000 },
  { 4352, 0x0000 }, { 4352, 0x0000 }, { 4352, 0x0080 }, { 4353, 0x0400 },
  { 4354, 0x0000 }, { 4354, 0x0000 }, { 4354, 0x0001 }, { 4355, 0x0000 },
  { 4355, 0x0000 }, { 4355, 0x2000 }, { 4356, 0x0000 }, { 4356, 0x2000 },
  /* 0x27a00 */
  { 4357, 0x4400 }, { 4359, 0x0000 }, { 4359, 0x0000 }, { 4359, 0x4000 },
  { 4360, 0x0000 }, { 4360, 0x0208 }, { 4362, 0x0000 }, { 4362, 0x0200 },
  { 4363, 0x0010 }, { 4364, 0x0000 }, { 4364, 0x0000 }, { 4364, 0x6000 },
  { 4366, 0x0000 }, { 4366, 0x0000 }, { 4366, 0x0000 }, { 4366, 0x0010 },
  /* 0x27b00 */
  { 4367, 0x0840 }, { 4369, 0x0100 }, { 4370, 0x0000 }, { 4370, 0x0700 },
  { 4373, 0x0100 }, { 4374, 0x0000 }, { 4374, 0x0000 }, { 4374, 0x0000 },
  { 4374, 0x0000 }, { 4374, 0x0000 }, { 4374, 0x0000 }, { 4374, 0x0000 },
  { 4374, 0x0000 }, { 4374, 0x0000 }, { 4374, 0x0000 }, { 4374, 0x0010 },
  /* 0x27c00 */
  { 4375, 0x0000 }, { 4375, 0x0004 }, { 4376, 0x0000 }, { 4376, 0x0000 },
  { 4376, 0x0000 }, { 4376, 0x0000 }, { 4376, 0x0000 }, { 4376, 0x0000 },
  { 4376, 0x0000 }, { 4376, 0x0000 }, { 4376, 0x0000 }, { 4376, 0x0000 },
  { 4376, 0x0000 }, { 4376, 0x0000 }, { 4376, 0x0000 }, { 4376, 0x0000 },
  /* 0x27d00 */
  { 4376, 0x0000 }, { 4376, 0x0000 }, { 4376, 0x8000 }, { 4377, 0x0000 },
  { 4377, 0x0000 }, { 4377, 0x0018 }, { 4379, 0x0040 }, { 4380, 0x0008 },
  { 4381, 0x8010 }, { 4383, 0x0100 }, { 4384, 0x0000 }, { 4384, 0x2000 },
  { 4385, 0x0000 }, { 4385, 0x1000 }, { 4386, 0x0000 }, { 4386, 0x0000 },
  /* 0x27e00 */
  { 4386, 0x0000 }, { 4386, 0x0000 }, { 4386, 0x0000 }, { 4386, 0x0000 },
  { 4386, 0xa000 }, { 4388, 0x0000 }, { 4388, 0x0000 }, { 4388, 0x0000 },
  { 4388, 0x0000 }, { 4388, 0x0000 }, { 4388, 0x0000 }, { 4388, 0x0000 },
  { 4388, 0x0000 }, { 4388, 0x0000 }, { 4388, 0x0000 }, { 4388, 0x0000 },
  /* 0x27f00 */
  { 4388, 0x0000 }, { 4388, 0x0000 }, { 4388, 0x4000 }, { 4389, 0x0000 },
  { 4389, 0x0000 }, { 4389, 0x0000 }, { 4389, 0x0000 }, { 4389, 0x0000 },
  { 4389, 0x0000 }, { 4389, 0x0000 }, { 4389, 0x0000 }, { 4389, 0x0000 },
  { 4389, 0x0000 }, { 4389, 0x0000 }, { 4389, 0x0000 }, { 4389, 0x0200 },
  /* 0x28000 */
  { 4390, 0x0204 }, { 4392, 0x4000 }, { 4393, 0x0018 }, { 4395, 0x0000 },
  { 4395, 0x0100 }, { 4396, 0x0000 }, { 4396, 0x0000 }, { 4396, 0x0000 },
  { 4396, 0x0008 }, { 4397, 0x0001 }, { 4398, 0x0000 }, { 4398, 0x6000 },
  { 4400, 0x0000 }, { 4400, 0x0000 }, { 4400, 0x0300 }, { 4402, 0x0010 },
  /* 0x28100 */
  { 4403, 0x0000 }, { 4403, 0x0000 }, { 4403, 0x4000 }, { 4404, 0x0000 },
  { 4404, 0x8000 }, { 4405, 0x2000 }, { 4406, 0x8000 }, { 4407, 0x0000 },
  { 4407, 0x0200 }, { 4408, 0x0000 }, { 4408, 0x8000 }, { 4409, 0x1000 },
  { 4410, 0x0000 }, { 4410, 0x0000 }, { 4410, 0x0000 }, { 4410, 0x0000 },
  /* 0x28200 */
  { 4410, 0x0080 }, { 4411, 0x0500 }, { 4413, 0x0000 }, { 4413, 0x0000 },
  { 4413, 0x0000 }, { 4413, 0x0040 }, { 4414, 0x0000 }, { 4414, 0x1000 },
  { 4415, 0x0000 }, { 4415, 0x0800 }, { 4416, 0x0000 }, { 4416, 0x0000 },
  { 4416, 0x2000 }, { 4417, 0x0000 }, { 4417, 0x0004 }, { 4418, 0x0000 },
  /* 0x28300 */
  { 4418, 0x0040 }, { 4419, 0x0100 }, { 4420, 0x8000 }, { 4421, 0x0400 },
  { 4422, 0x0000 }, { 4422, 0x0000 }, { 4422, 0x2020 }, { 4424, 0x2000 },
  { 4425, 0x0400 }, { 4426, 0x0000 }, { 4426, 0x0000 }, { 4426, 0x0000 },
  { 4426, 0x0000 }, { 4426, 0x0000 }, { 4426, 0x0000 }, { 4426, 0x0000 },
  /* 0x28400 */
  { 4426, 0x0000 }, { 4426, 0x0004 }, { 4427, 0x0000 }, { 4427, 0x0000 },
  { 4427, 0x0000 }, { 4427, 0x0000 }, { 4427, 0x1100 }, { 4429, 0x0008 },
  { 4430, 0x0004 }, { 4431, 0x0000 }, { 4431, 0x0000 }, { 4431, 0x0000 },
  { 4431, 0x0000 }, { 4431, 0x0000 }, { 4431, 0x0000 }, { 4431, 0x0000 },
  /* 0x28500 */
  { 4431, 0x0002 }, { 4432, 0x0000 }, { 4432, 0x0000 }, { 4432, 0x3000 },
  { 4434, 0x0000 }, { 4434, 0x0000 }, { 4434, 0x1000 }, { 4435, 0x0000 },
  { 4435, 0x0000 }, { 4435, 0x0000 }, { 4435, 0x0000 }, { 4435, 0x0000 },
  { 4435, 0x0000 }, { 4435, 0x0000 }, { 4435, 0x0100 }, { 4436, 0x0010 },
  /* 0x28600 */
  { 4437, 0x0801 }, { 4439, 0x0000 }, { 4439, 0x0020 }, { 4440, 0x0800 },
  { 4441, 0x0000 }, { 4441, 0x0000 }, { 4441, 0x0000 }, { 4441, 0x0000 },
  { 4441, 0x0000 }, { 4441, 0x0000 }, { 4441, 0x0c00 }, { 4443, 0x1000 },
  { 4444, 0x0000 }, { 4444, 0x0100 }, { 4445, 0x0040 }, { 4446, 0x0000 },
  /* 0x28700 */
  { 4446, 0x0000 }, { 4446, 0x0008 }, { 4447, 0x0000 }, { 4447, 0x0000 },
  { 4447, 0x0000 }, { 4447, 0x0000 }, { 4447, 0x0000 }, { 4447, 0x0000 },
  { 4447, 0x0000 }, { 4447, 0x0000 }, { 4447, 0x0000 }, { 4447, 0x0000 },
  { 4447, 0x0000 }, { 4447, 0x0000 }, { 4447, 0x0000 }, { 4447, 0x0000 },
  /* 0x28800 */
  { 4447, 0x0010 }, { 4448, 0x0000 }, { 4448, 0x0800 }, { 4449, 0x0000 },
  { 4449, 0x0000 }, { 4449, 0x0000 }, { 4449, 0x0000 }, { 4449, 0x0000 },
  { 4449, 0x0000 }, { 4449, 0x0000 }, { 4449, 0x0000 }, { 4449, 0x0000 },
  { 4449, 0x0000 }, { 4449, 0x0000 }, { 4449, 0x0000 }, { 4449, 0x0000 },
  /* 0x28900 */
  { 4449, 0x0000 }, { 4449, 0x0000 }, { 4449, 0x0000 }, { 4449, 0x0008 },
  { 4450, 0x0300 }, { 4452, 0x0040 }, { 4453, 0x1110 }, { 4456, 0x4000 },
  { 4457, 0x0200 }, { 4458, 0x0000 }, { 4458, 0x0d00 }, { 4461, 0x1100 },
  { 4463, 0x0001 }, { 4464, 0x5000 }, { 4466, 0x0192 }, { 4470, 0x1e00 },
  /* 0x28a00 */
  { 4474, 0x8000 }, { 4475, 0x0040 }, { 4476, 0x0220 }, { 4478, 0x0040 },
  { 4479, 0x0ff0 }, { 4487, 0x0600 }, { 4489, 0x0000 }, { 4489, 0x0000 },
  { 4489, 0x000e }, { 4492, 0x1c00 }, { 4495, 0x0000 }, { 4495, 0x0000 },
  { 4495, 0x5841 }, { 4500, 0xc000 }, { 4502, 0x002f }, { 4507, 0x1000 },
  /* 0x28b00 */
  { 4508, 0x1000 }, { 4509, 0x0008 }, { 4510, 0xb806 }, { 4516, 0x0000 },
  { 4516, 0x5040 }, { 4519, 0x0001 }, { 4520, 0x1078 }, { 4525, 0x0000 },
  { 4525, 0x8000 }, { 4526, 0x3200 }, { 4529, 0x0000 }, { 4529, 0x0000 },
  { 4529, 0x0024 }, { 4531, 0x0690 }, { 4535, 0x1f80 }, { 4541, 0x8020 },
  /* 0x28c00 */
  { 4543, 0x0208 }, { 4545, 0x3000 }, { 4547, 0x0848 }, { 4550, 0x0a01 },
  { 4553, 0x0000 }, { 4553, 0x0000 }, { 4553, 0x0000 }, { 4553, 0x0000 },
  { 4553, 0x0000 }, { 4553, 0x0000 }, { 4553, 0x0000 }, { 4553, 0x0000 },
  { 4553, 0x2400 }, { 4555, 0x0004 }, { 4556, 0x0000 }, { 4556, 0x0000 },
  /* 0x28d00 */
  { 4556, 0x0000 }, { 4556, 0x0000 }, { 4556, 0x0000 }, { 4556, 0x0010 },
  { 4557, 0x0000 }, { 4557, 0x0000 }, { 4557, 0x0000 }, { 4557, 0x0000 },
  { 4557, 0x0000 }, { 4557, 0x0200 }, { 4558, 0x0000 }, { 4558, 0x0000 },
  { 4558, 0x0000 }, { 4558, 0x0000 }, { 4558, 0x0000 }, { 4558, 0x0000 },
  /* 0x28e00 */
  { 4558, 0x8000 }, { 4559, 0x0000 }, { 4559, 0x0000 }, { 4559, 0x0240 },
  { 4561, 0x0000 }, { 4561, 0x0000 }, { 4561, 0x0060 }, { 4563, 0x0000 },
  { 4563, 0x0000 }, { 4563, 0x0080 }, { 4564, 0x1000 }, { 4565, 0x000c },
  { 4567, 0x0000 }, { 4567, 0x0200 }, { 4568, 0x0080 }, { 4569, 0x0000 },
  /* 0x28f00 */
  { 4569, 0x0000 }, { 4569, 0x0000 }, { 4569, 0x0000 }, { 4569, 0x0000 },
  { 4569, 0x0000 }, { 4569, 0x0000 }, { 4569, 0x0000 }, { 4569, 0x0000 },
  { 4569, 0x0000 }, { 4569, 0x0000 }, { 4569, 0x0000 }, { 4569, 0x0000 },
  { 4569, 0x0020 }, { 4570, 0x0000 }, { 4570, 0x0000 }, { 4570, 0x0000 },
  /* 0x29000 */
  { 4570, 0x0000 }, { 4570, 0x0000 }, { 4570, 0x0000 }, { 4570, 0x0000 },
  { 4570, 0x0000 }, { 4570, 0x0000 }, { 4570, 0x0000 }, { 4570, 0x0000 },
  { 4570, 0x0900 }, { 4572, 0x0008 }, { 4573, 0x8000 }, { 4574, 0x0003 },
  { 4576, 0x0001 }, { 4577, 0x0000 }, { 4577, 0x3030 }, { 4581, 0x0000 },
  /* 0x29100 */
  { 4581, 0x2000 }, { 4582, 0x0001 }, { 4583, 0x0000 }, { 4583, 0x1000 },
  { 4584, 0x2000 }, { 4585, 0x4800 }, { 4587, 0x0000 }, { 4587, 0x0001 },
  { 4588, 0x0000 }, { 4588, 0x1000 }, { 4589, 0x0100 }, { 4590, 0x0000 },
  { 4590, 0x0000 }, { 4590, 0x0020 }, { 4591, 0x0800 },
};
static const Summary16 hkscs1999_uni2indx_page294[32] = {
  /* 0x29400 */
  { 4592, 0x0000 }, { 4592, 0x2000 }, { 4593, 0x0001 }, { 4594, 0x8008 },
  { 4596, 0x0100 }, { 4597, 0x0000 }, { 4597, 0x0000 }, { 4597, 0x0000 },
  { 4597, 0x0000 }, { 4597, 0x0000 }, { 4597, 0x0000 }, { 4597, 0x0000 },
  { 4597, 0x0000 }, { 4597, 0x0601 }, { 4600, 0x00a0 }, { 4602, 0x0000 },
  /* 0x29500 */
  { 4602, 0x0000 }, { 4602, 0x0000 }, { 4602, 0x0000 }, { 4602, 0x0000 },
  { 4602, 0x0000 }, { 4602, 0x0000 }, { 4602, 0x0000 }, { 4602, 0x0000 },
  { 4602, 0x0000 }, { 4602, 0x4000 }, { 4603, 0x0000 }, { 4603, 0x0101 },
  { 4605, 0x0000 }, { 4605, 0x0080 }, { 4606, 0x0200 }, { 4607, 0x0010 },
};
static const Summary16 hkscs1999_uni2indx_page297[251] = {
  /* 0x29700 */
  { 4608, 0x0000 }, { 4608, 0x0000 }, { 4608, 0x0001 }, { 4609, 0x0004 },
  { 4610, 0x0000 }, { 4610, 0x0000 }, { 4610, 0x0000 }, { 4610, 0x0000 },
  { 4610, 0x0000 }, { 4610, 0x0000 }, { 4610, 0x0000 }, { 4610, 0x0000 },
  { 4610, 0x0000 }, { 4610, 0x0010 }, { 4611, 0x0000 }, { 4611, 0x0000 },
  /* 0x29800 */
  { 4611, 0x0000 }, { 4611, 0x0001 }, { 4612, 0x0000 }, { 4612, 0x0000 },
  { 4612, 0x0000 }, { 4612, 0x0080 }, { 4613, 0x0000 }, { 4613, 0x0000 },
  { 4613, 0x0000 }, { 4613, 0x0000 }, { 4613, 0x0010 }, { 4614, 0x0000 },
  { 4614, 0x0000 }, { 4614, 0x0002 }, { 4615, 0x0400 }, { 4616, 0x0002 },
  /* 0x29900 */
  { 4617, 0x0028 }, { 4619, 0x0000 }, { 4619, 0x8000 }, { 4620, 0x0000 },
  { 4620, 0x0300 }, { 4622, 0x2000 }, { 4623, 0x0400 }, { 4624, 0x0000 },
  { 4624, 0x0000 }, { 4624, 0x2000 }, { 4625, 0x0000 }, { 4625, 0x0000 },
  { 4625, 0x0208 }, { 4627, 0x0000 }, { 4627, 0x0000 }, { 4627, 0x0000 },
  /* 0x29a00 */
  { 4627, 0x0000 }, { 4627, 0x0000 }, { 4627, 0x0100 }, { 4628, 0x0000 },
  { 4628, 0x2000 }, { 4629, 0x0000 }, { 4629, 0x0000 }, { 4629, 0x0000 },
  { 4629, 0x0000 }, { 4629, 0x0000 }, { 4629, 0x0000 }, { 4629, 0x0000 },
  { 4629, 0x0000 }, { 4629, 0x0000 }, { 4629, 0x0000 }, { 4629, 0x0000 },
  /* 0x29b00 */
  { 4629, 0x4020 }, { 4631, 0x0000 }, { 4631, 0x0000 }, { 4631, 0x0000 },
  { 4631, 0x0000 }, { 4631, 0x0000 }, { 4631, 0x0000 }, { 4631, 0x0000 },
  { 4631, 0x0000 }, { 4631, 0x0000 }, { 4631, 0x0000 }, { 4631, 0x0000 },
  { 4631, 0x0000 }, { 4631, 0x0020 }, { 4632, 0x0000 }, { 4632, 0x0000 },
  /* 0x29c00 */
  { 4632, 0x0000 }, { 4632, 0x0000 }, { 4632, 0x0000 }, { 4632, 0x0000 },
  { 4632, 0x0000 }, { 4632, 0x0000 }, { 4632, 0x0000 }, { 4632, 0x0000 },
  { 4632, 0x0000 }, { 4632, 0x0000 }, { 4632, 0x2000 }, { 4633, 0x0000 },
  { 4633, 0x0000 }, { 4633, 0x0000 }, { 4633, 0x0000 }, { 4633, 0x0000 },
  /* 0x29d00 */
  { 4633, 0x0000 }, { 4633, 0x0000 }, { 4633, 0x0000 }, { 4633, 0x4000 },
  { 4634, 0x0000 }, { 4634, 0x0400 }, { 4635, 0x0000 }, { 4635, 0x1000 },
  { 4636, 0x0000 }, { 4636, 0x0900 }, { 4638, 0x0000 }, { 4638, 0x0000 },
  { 4638, 0x0000 }, { 4638, 0x0000 }, { 4638, 0x0000 }, { 4638, 0x0040 },
  /* 0x29e00 */
  { 4639, 0x0040 }, { 4640, 0x0000 }, { 4640, 0x2000 }, { 4641, 0x0000 },
  { 4641, 0x0000 }, { 4641, 0x0000 }, { 4641, 0x0100 }, { 4642, 0x0000 },
  { 4642, 0x0000 }, { 4642, 0x0000 }, { 4642, 0x1000 }, { 4643, 0x0000 },
  { 4643, 0x0008 }, { 4644, 0x0000 }, { 4644, 0x0000 }, { 4644, 0x0100 },
  /* 0x29f00 */
  { 4645, 0x0000 }, { 4645, 0x0000 }, { 4645, 0x0008 }, { 4646, 0x0001 },
  { 4647, 0x0000 }, { 4647, 0x0000 }, { 4647, 0x0000 }, { 4647, 0x0000 },
  { 4647, 0x0000 }, { 4647, 0x0000 }, { 4647, 0x0000 }, { 4647, 0x0080 },
  { 4648, 0x0000 }, { 4648, 0x4000 }, { 4649, 0x0000 }, { 4649, 0x0000 },
  /* 0x2a000 */
  { 4649, 0x0000 }, { 4649, 0x0010 }, { 4650, 0x0000 }, { 4650, 0x0000 },
  { 4650, 0x0000 }, { 4650, 0x0000 }, { 4650, 0x0000 }, { 4650, 0x0000 },
  { 4650, 0x0080 }, { 4651, 0x0000 }, { 4651, 0x0000 }, { 4651, 0x0200 },
  { 4652, 0x0000 }, { 4652, 0x0000 }, { 4652, 0x2002 }, { 4654, 0x4108 },
  /* 0x2a100 */
  { 4657, 0x0000 }, { 4657, 0x0000 }, { 4657, 0x0008 }, { 4658, 0x0018 },
  { 4660, 0x0000 }, { 4660, 0x0001 }, { 4661, 0x0000 }, { 4661, 0x0000 },
  { 4661, 0x0000 }, { 4661, 0x000c }, { 4663, 0x0800 }, { 4664, 0x0010 },
  { 4665, 0x0000 }, { 4665, 0x8000 }, { 4666, 0x0000 }, { 4666, 0x0020 },
  /* 0x2a200 */
  { 4667, 0x0000 }, { 4667, 0x0000 }, { 4667, 0x0001 }, { 4668, 0x0008 },
  { 4669, 0x0000 }, { 4669, 0x0000 }, { 4669, 0x0000 }, { 4669, 0x0000 },
  { 4669, 0x0000 }, { 4669, 0x8008 }, { 4671, 0x0000 }, { 4671, 0x2054 },
  { 4675, 0x0000 }, { 4675, 0x8000 }, { 4676, 0x0000 }, { 4676, 0x8000 },
  /* 0x2a300 */
  { 4677, 0x0000 }, { 4677, 0x0000 }, { 4677, 0x0000 }, { 4677, 0x0000 },
  { 4677, 0x0000 }, { 4677, 0x0000 }, { 4677, 0x0000 }, { 4677, 0x0000 },
  { 4677, 0x0000 }, { 4677, 0x0000 }, { 4677, 0x0200 }, { 4678, 0x0000 },
  { 4678, 0x0000 }, { 4678, 0x0000 }, { 4678, 0x0000 }, { 4678, 0x0000 },
  /* 0x2a400 */
  { 4678, 0x0000 }, { 4678, 0x0000 }, { 4678, 0x0000 }, { 4678, 0x0010 },
  { 4679, 0x0000 }, { 4679, 0x0800 }, { 4680, 0x0000 }, { 4680, 0x0000 },
  { 4680, 0x0000 }, { 4680, 0x0000 }, { 4680, 0x0000 }, { 4680, 0x0000 },
  { 4680, 0x0000 }, { 4680, 0x0000 }, { 4680, 0x0000 }, { 4680, 0x0000 },
  /* 0x2a500 */
  { 4680, 0x0000 }, { 4680, 0x0000 }, { 4680, 0x0000 }, { 4680, 0x0000 },
  { 4680, 0x0000 }, { 4680, 0x0000 }, { 4680, 0x0000 }, { 4680, 0x0000 },
  { 4680, 0x0000 }, { 4680, 0x0000 }, { 4680, 0x0000 }, { 4680, 0x0000 },
  { 4680, 0x0840 }, { 4682, 0x0000 }, { 4682, 0x0000 }, { 4682, 0x0000 },
  /* 0x2a600 */
  { 4682, 0x0002 }, { 4683, 0x0000 }, { 4683, 0x0000 }, { 4683, 0x0004 },
  { 4684, 0x0400 }, { 4685, 0x0800 }, { 4686, 0x0000 }, { 4686, 0x0000 },
  { 4686, 0x0000 }, { 4686, 0x0000 }, { 4686, 0x0200 },
};
static const Summary16 hkscs1999_uni2indx_page2f8[30] = {
  /* 0x2f800 */
  { 4687, 0x0000 }, { 4687, 0x0000 }, { 4687, 0x0020 }, { 4688, 0x0800 },
  { 4689, 0x0001 }, { 4690, 0x0000 }, { 4690, 0x0000 }, { 4690, 0x0100 },
  { 4691, 0x0000 }, { 4691, 0x0010 }, { 4692, 0x0040 }, { 4693, 0x0000 },
  { 4693, 0x2000 }, { 4694, 0x0000 }, { 4694, 0x0000 }, { 4694, 0x0000 },
  /* 0x2f900 */
  { 4694, 0x0000 }, { 4694, 0x0000 }, { 4694, 0x0000 }, { 4694, 0x0000 },
  { 4694, 0x0000 }, { 4694, 0x0000 }, { 4694, 0x0000 }, { 4694, 0x0000 },
  { 4694, 0x0000 }, { 4694, 0x0010 }, { 4695, 0x0000 }, { 4695, 0x1004 },
  { 4697, 0x0000 }, { 4697, 0x0010 },
};

static int
hkscs1999_wctomb (conv_t conv, unsigned char *r, ucs4_t wc, int n)
{
  if (n >= 2) {
    const Summary16 *summary = NULL;
    if (wc >= 0x0000 && wc < 0x02d0)
      summary = &hkscs1999_uni2indx_page00[(wc>>4)];
    else if (wc >= 0x0400 && wc < 0x0460)
      summary = &hkscs1999_uni2indx_page04[(wc>>4)-0x040];
    else if (wc >= 0x1e00 && wc < 0x1ed0)
      summary = &hkscs1999_uni2indx_page1e[(wc>>4)-0x1e0];
    else if (wc >= 0x2100 && wc < 0x21f0)
      summary = &hkscs1999_uni2indx_page21[(wc>>4)-0x210];
    else if (wc >= 0x2300 && wc < 0x2580)
      summary = &hkscs1999_uni2indx_page23[(wc>>4)-0x230];
    else if (wc >= 0x2700 && wc < 0x2740)
      summary = &hkscs1999_uni2indx_page27[(wc>>4)-0x270];
    else if (wc >= 0x2e00 && wc < 0x3240)
      summary = &hkscs1999_uni2indx_page2e[(wc>>4)-0x2e0];
    else if (wc >= 0x3400 && wc < 0x9fc0)
      summary = &hkscs1999_uni2indx_page34[(wc>>4)-0x340];
    else if (wc >= 0xf900 && wc < 0xf910)
      summary = &hkscs1999_uni2indx_pagef9[(wc>>4)-0xf90];
    else if (wc >= 0xff00 && wc < 0xfff0)
      summary = &hkscs1999_uni2indx_pageff[(wc>>4)-0xff0];
    else if (wc >= 0x20000 && wc < 0x291f0)
      summary = &hkscs1999_uni2indx_page200[(wc>>4)-0x2000];
    else if (wc >= 0x29400 && wc < 0x29600)
      summary = &hkscs1999_uni2indx_page294[(wc>>4)-0x2940];
    else if (wc >= 0x29700 && wc < 0x2a6b0)
      summary = &hkscs1999_uni2indx_page297[(wc>>4)-0x2970];
    else if (wc >= 0x2f800 && wc < 0x2f9e0)
      summary = &hkscs1999_uni2indx_page2f8[(wc>>4)-0x2f80];
    if (summary) {
      unsigned short used = summary->used;
      unsigned int i = wc & 0x0f;
      if (used & ((unsigned short) 1 << i)) {
        unsigned short c;
        /* Keep in `used' only the bits 0..i-1. */
        used &= ((unsigned short) 1 << i) - 1;
        /* Add `summary->indx' and the number of bits set in `used'. */
        used = (used & 0x5555) + ((used & 0xaaaa) >> 1);
        used = (used & 0x3333) + ((used & 0xcccc) >> 2);
        used = (used & 0x0f0f) + ((used & 0xf0f0) >> 4);
        used = (used & 0x00ff) + (used >> 8);
        c = hkscs1999_2charset[summary->indx + used];
        r[0] = (c >> 8); r[1] = (c & 0xff);
        return 2;
      }
    }
    return RET_ILUNI;
  }
  return RET_TOOSMALL;
}
