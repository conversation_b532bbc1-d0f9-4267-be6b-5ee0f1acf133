# em-integrated-management-service服务

| 缓存信息                   | redisKey           | redisVal                                                 | 存入处理逻辑                                                 | 存入位置                             | 获取使用                                    |
| -------------------------- | ------------------ | -------------------------------------------------------- | ------------------------------------------------------------ | ------------------------------------ | ------------------------------------------- |
| 所有未逻辑删除员工信息     | STAFF_INFO         | staffId=>staff                                           | 解密idcard,phone,fundId                                      | BaseRedisJob; StaffEsopSService      | AppOperationService                         |
| 所有未逻辑删除公司         | COMPANY            | companyId=>company                                       |                                                              | BaseRedisJob                         | AppOperationService; EmbaseOperationService |
| 所有未逻辑删除默认量价方案 | SHOW_PLAN          | userCode+companyId +eiPlanId+planType =>EiQtyPriceRecord | 查询所有未逻辑删除默认量价方案,根据qtyPriceId查询第一条量价调整记录 | BaseRedisJob;  QtyPriceManageService |                                             |
| 激励计划股价信息           | QRY_PRICE          | eiPlanId=>List<EiStockPrice>                             |                                                              | BaseRedisJob                         |                                             |
| 所有未逻辑删除量价调整记录 | QTYPRICE           | eiPlanId=>List<EiQtyPriceRecord>                         |                                                              | BaseRedisJob; QtyPriceManageService  |                                             |
| 所有未逻辑删除登录账户     | LOGIN_ACCOUNT_INFO | userCode=>LoginAccount                                   |                                                              | BaseRedisJob                         |                                             |
| 所有未逻辑删除激励计划     | EI_PLAN            | eiPlanId=>EiPlan                                         |                                                              | BaseRedisJob; StaffEsopSService      | AppOperationService                         |

# em-integrated-management-web-company服务

CommonSService定时刷新缓存,从企业通同步用户与公司时刷新对应缓存

| 缓存信息                   | Key                | Val                                                      | 存入处理逻辑                                                 | 存入位置                                                     | 获取使用                                                     |
| -------------------------- | ------------------ | -------------------------------------------------------- | ------------------------------------------------------------ | ------------------------------------------------------------ | ------------------------------------------------------------ |
| 所有未逻辑删除员工信息     | STAFF_INFO         | staffId=>staff                                           | 解密idcard,phone,fundId                                      | PublishOperateService; StaffEsopSService; StaffSService      | AppOperationService; QryOrderService; StaffEsopSService; StaffSService; TaxationComputeSService; UnlockImplSService |
| 所有未逻辑删除公司         | COMPANY            | companyId=>company                                       |                                                              | PushCompanyListener                                          | AppOperationService; EmbaseOperationService                  |
| 所有未逻辑删除默认量价方案 | SHOW_PLAN          | userCode+companyId +eiPlanId+planType =>EiQtyPriceRecord | 查询所有未逻辑删除默认量价方案,根据qtyPriceId查询第一条量价调整记录 | PublishOperateService; QtyPriceManageService; ShowPlanOpSService; StaffEsopSService; StaffSService; UnlockImplSService | ShowPlanOpSService                                           |
| 激励计划股价信息           | QRY_PRICE          | eiPlanId=>List<EiStockPrice>                             |                                                              | PlanBaseService; PublishOperateService; TaxationComputeSService | TaxationComputeSService                                      |
| 所有未逻辑删除量价调整记录 | QTYPRICE           | eiPlanId=>List<EiQtyPriceRecord>                         |                                                              | PlanBaseService; PublishOperateService; QtyPriceManageService | QtyPriceManageService                                        |
| 所有未逻辑删除登录账户     | LOGIN_ACCOUNT_INFO | userCode=>LoginAccount                                   |                                                              | PushAccountListener                                          | StaffEsopSService; StaffSService; TaxationComputeSService; UnlockImplSService |
| 所有未逻辑删除激励计划     | EI_PLAN            | eiPlanId=>EiPlan                                         |                                                              | PlanBaseService; StaffEsopSService;                          | AppOperationService; QtyPriceManageService; StaffSService; TaxationComputeSService |
