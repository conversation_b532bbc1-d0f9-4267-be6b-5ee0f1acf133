---
created: 2024-11-20 09:43
updated: 2024-11-20 16:00
---
# 1. 流程图



![[../../../attachments/个税代扣OMP审核状态同步/个税代扣OMP审核状态同步.png|../../../../../attachs/个税代扣OMP审核状态同步.png]]
# 2. 涉及的数据库、<PERSON><PERSON>、Kafka 等中间件的修改/新增图或者说明
新增个税代扣表
```sql
CREATE TABLE LCIMS.EXERCISE_TAX_WITHHOLD  
(  
    EID                   NUMBER(18, 0) NOT NULL,  
    EITIME                DATE    DEFAULT sysdate,  
    EUTIME                DATE    DEFAULT sysdate,  
    EI_PLAN_ID            NUMBER(18, 0),  
    PLAN_ID               NUMBER(18, 0),  
    BATCH_SNO             NUMBER(18, 0),  
    UNLOCK_ID             NUMBER(18),  
    STAFF_ID              NUMBER(18, 0),  
    IS_WITHHOLD_TAX       CHAR(1) DEFAULT '1',  
    TAX_THRESHOLD         NUMBER(18, 2),  
    STAFF_TYPE            CHAR(1),  
    WORK_MONTHS           NUMBER(10, 0),  
    AUDIT_STATUS          CHAR(1) DEFAULT '0',  
    AUDIT_REMARK          VARCHAR2(500),  
    LAST_MODIFY_USER_CODE VARCHAR2(50),  
    LAST_MODIFY_TIME      DATE    DEFAULT sysdate,  
    CONSTRAINT PK_EXERCISE_TAX_WITHHOLD PRIMARY KEY (EID)  
);  
  
-- 表注释  
COMMENT ON TABLE LCIMS.EXERCISE_TAX_WITHHOLD IS '个税代扣配置表';  
  
-- 字段注释  
COMMENT ON COLUMN LCIMS.EXERCISE_TAX_WITHHOLD.EID IS '系统物理主键';  
COMMENT ON COLUMN LCIMS.EXERCISE_TAX_WITHHOLD.EITIME IS '创建时间';  
COMMENT ON COLUMN LCIMS.EXERCISE_TAX_WITHHOLD.EUTIME IS '更新时间';  
COMMENT ON COLUMN LCIMS.EXERCISE_TAX_WITHHOLD.EI_PLAN_ID IS '计划id';  
COMMENT ON COLUMN LCIMS.EXERCISE_TAX_WITHHOLD.PLAN_ID IS '批次计划ID';  
COMMENT ON COLUMN LCIMS.EXERCISE_TAX_WITHHOLD.BATCH_SNO IS '批次号';  
comment on column LCIMS.EXERCISE_TAX_WITHHOLD.UNLOCK_ID is '解锁表逻辑主键';  
COMMENT ON COLUMN LCIMS.EXERCISE_TAX_WITHHOLD.STAFF_ID IS '员工id';  
COMMENT ON COLUMN LCIMS.EXERCISE_TAX_WITHHOLD.IS_WITHHOLD_TAX IS '是否代扣个税 0-否 1-是';  
COMMENT ON COLUMN LCIMS.EXERCISE_TAX_WITHHOLD.TAX_THRESHOLD IS '所得税起征金额';  
COMMENT ON COLUMN LCIMS.EXERCISE_TAX_WITHHOLD.STAFF_TYPE IS '员工类型 1-普通员工 2-高管人员 3-5%以上大股东及一致行动人';  
COMMENT ON COLUMN LCIMS.EXERCISE_TAX_WITHHOLD.WORK_MONTHS IS '工作月数（截至实际行权日）';  
COMMENT ON COLUMN LCIMS.EXERCISE_TAX_WITHHOLD.AUDIT_STATUS IS '审核状态 0-未发起 1-审核中 2-审核通过 3-审核失败';  
COMMENT ON COLUMN LCIMS.EXERCISE_TAX_WITHHOLD.AUDIT_REMARK IS '审核备注（失败原因）';  
COMMENT ON COLUMN LCIMS.EXERCISE_TAX_WITHHOLD.LAST_MODIFY_USER_CODE IS '最近修改人ID';  
COMMENT ON COLUMN LCIMS.EXERCISE_TAX_WITHHOLD.LAST_MODIFY_TIME IS '最近修改时间';
```

# 3. 前后端交互接口信息

不涉及
# 4. 配置&部署修改信息

不涉及

# 5. 新增技术组件说明

不涉及

# 6. 影响范围

个税代扣列表查询

# 7. 外部依赖项

OMP
