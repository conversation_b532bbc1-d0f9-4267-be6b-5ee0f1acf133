## 1. 参数校验
## 2. 查询计划时间点
### 3. 根据不同激励类型处理数据
### 3.1 第一类计划
#### 3.1.1 校验回归日期为交易日
#### 3.1.2 根据主键查询需要编辑的解锁记录
#### 3.1.3 获得非终止状态的数据(离职终止和退休终止、其他原因终止状态的数据)


```plantuml
title 解锁管理批量勾选编辑逻辑梳理

participant 股权激励管理端
participant 数据库

股权激励管理端->股权激励管理端: 参数校验
股权激励管理端->数据库: 查询计划时间点
股权激励管理端->股权激励管理端: 根据不同激励类型处理数据
alt 激励类型为第一类计划
股权激励管理端->数据库: 根据主键查询需要编辑的解锁记录
股权激励管理端->数据库: 获得非终止状态的数据(离职终止和退休终止、其他原因终止状态的数据)
alt 如果需要编辑为终止状态
股权激励管理端->股权激励管理端: 设置需要更新的解锁记录为非终止状态的数据
else 如果不是编辑为终止状态并且之前的解锁记录状态存在终止状态的数据
股权激励管理端->股权激励管理端: 设置需要更新的解锁记录为终止状态的数据
end
股权激励管理端->股权激励管理端: 根据上市流通日查询市价

loop 遍历需要更新的解锁记录
alt 原来为已解锁并且公司考核达标、个人考核比例不为空 现修改为已解锁 达标-公司考核比例不为空、个人考核比例为空
股权激励管理端->股权激励管理端: 设置计算实际解锁股份，实际解锁股份最新量价，公司解锁比例，个人解锁比例，是否重置回购,回购日期，回购价格，回购价格最新量价，上市流通日
else 原来为公司考核达标、公司考核比例不为空修改为已解锁、公司达标为空、个人考核比例不为空
股权激励管理端->股权激励管理端: 设置个人解锁比例
alt 考核比例分数展示
股权激励管理端->股权激励管理端: 设置计算实际解锁股份，实际解锁股份最新量价
股权激励管理端->股权激励管理端: 设置个人解锁比例,是否分数展示，分子，分母
else 考核比例百分数展示
股权激励管理端->股权激励管理端: 设置计算实际解锁股份，实际解锁股份最新量价
end
股权激励管理端->股权激励管理端:公司考核比例维持不变
股权激励管理端->股权激励管理端:是否重置回购,回购日期，回购价格，回购价格最新量价，上市流通日
else 现修改为已解锁 达标-公司考核比例为空、个人考核比例为空
股权激励管理端->股权激励管理端:是否重置回购,回购日期，回购价格，回购价格最新量价，上市流通日
else 其他
股权激励管理端->股权激励管理端: 设置公司解锁比例,是否分数展示，分子，分母
股权激励管理端->股权激励管理端: 设置公司考核kpi默认未考核或传入
alt 原来未解锁 或 已解锁未考核
股权激励管理端->股权激励管理端: 设置计算实际解锁股份为null，实际解锁股份最新量价为null，个人解锁比例为null，是否重置回购为null,回购日期为null，回购价格为null，，上市流通日为null,公司考核为未考核,已回购股份为null
end
alt 原来（已解锁，公司考核不达标 ）或者（终止未考核）
股权激励管理端->股权激励管理端: 设置计算实际解锁股份为0，实际解锁股份最新量价为0，上市流通日,个人解锁比例为0,,回购日期，回购价格，回购价格最新量价

alt 回购日期为空
股权激励管理端->股权激励管理端: 设置已回购股份为传入或null,待回购股份为传入或null
end
alt 回购日期小于等于当前日期
alt 待回购股份不为空
股权激励管理端->股权激励管理端: 设置待回购股份为0
股权激励管理端->股权激励管理端: 设置已回购股份为传入已回购股份+待回购股份
end
alt 待回购股份为空
股权激励管理端->股权激励管理端: 设置待回购股份为传入待回购股份或null
股权激励管理端->股权激励管理端: 设置已回购股份为传入已回购股份或null
end
else 回购日期大于当前日期
股权激励管理端->股权激励管理端: 设置待回购股份为传入待回购股份或null
股权激励管理端->股权激励管理端: 设置已回购股份为传入已回购股份或null
end
else 原来为已解锁 且 已达标 ，当前个人考核解锁比例不为空且不为"未考核"
股权激励管理端->股权激励管理端:设置实际解锁股份和实际解锁股份最新量价，市价，回购价格，回购价格最新量价，，是否重置回购
alt 回购日期为空

股权激励管理端->股权激励管理端:设置待回购股份和已回购股份为传入或Null
end
alt 回购日期不为空且回购日期大于当前日期
股权激励管理端->股权激励管理端:设置待回购股份和已回购股份为传入或Null

end
alt 回购日期不为空，并且回购日期小于或等于当前日期
alt 待回购股份不为空
股权激励管理端->股权激励管理端: 设置待回购股份为0
股权激励管理端->股权激励管理端: 设置已回购股份为传入已回购股份+待回购股份
end
alt 待回购股份为空
股权激励管理端->股权激励管理端: 设置待回购股份为传入待回购股份或null
股权激励管理端->股权激励管理端: 设置已回购股份为传入已回购股份或null
股权激励管理端->股权激励管理端: 设置员工解锁比例
end
end
else 原来已解锁，已达标修改为员工考核比例为空或未考核
股权激励管理端->股权激励管理端: 设置个人解锁比例为null，是否重置回购为null,回购日期，回购价格，回购价格最新量价，待回购股份为null,已回购股份为null
end
end
end
股权激励管理端->股权激励管理端: 返回结果
```
