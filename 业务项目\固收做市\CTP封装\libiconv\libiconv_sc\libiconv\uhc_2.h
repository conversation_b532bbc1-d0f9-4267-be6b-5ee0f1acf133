/*
 * Copyright (C) 1999-2001 Free Software Foundation, Inc.
 * This file is part of the GNU LIBICONV Library.
 *
 * The GNU LIBICONV Library is free software; you can redistribute it
 * and/or modify it under the terms of the GNU Library General Public
 * License as published by the Free Software Foundation; either version 2
 * of the License, or (at your option) any later version.
 *
 * The GNU LIBICONV Library is distributed in the hope that it will be
 * useful, but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
 * Library General Public License for more details.
 *
 * You should have received a copy of the GNU Library General Public
 * License along with the GNU LIBICONV Library; see the file COPYING.LIB.
 * If not, write to the Free Software Foundation, Inc., 51 Franklin Street,
 * Fifth Floor, Boston, MA 02110-1301, USA.
 */

/*
 * Unified Hangul Code part 2
 */

static const unsigned short uhc_2_2uni_main_pagea1[76] = {
  0xc8a5, 0xc8d8, 0xc910, 0xc93e, 0xc971, 0xc9a5, 0xc9de, 0xca1c,
  0xca47, 0xca7b, 0xcaa8, 0xcadd, 0xcb0b, 0xcb3a, 0xcb6d, 0xcb99,
  0xcbc5, 0xcbf3, 0xcc25, 0xcc67, 0xcc94, 0xcccf, 0xccfe, 0xcd34,
  0xcd61, 0xcd92, 0xcdc6, 0xcdf5, 0xce2c, 0xce5d, 0xce9a, 0xcecc,
  0xcf02, 0xcf3b, 0xcf6d, 0xcf9e, 0xcfcc, 0xcfff, 0xd02e, 0xd064,
  0xd095, 0xd0cc, 0xd105, 0xd132, 0xd16e, 0xd19b, 0xd1d0, 0xd1fd,
  0xd22a, 0xd25e, 0xd28d, 0xd2c5, 0xd2fb, 0xd33e, 0xd36a, 0xd3a1,
  0xd3d7, 0xd40d, 0xd438, 0xd467, 0xd49e, 0xd4c9, 0xd4fe, 0xd52e,
  0xd564, 0xd59d, 0xd5ca, 0xd606, 0xd63d, 0xd677, 0xd6ab, 0xd6e2,
  0xd715, 0xd74e, 0xd78d, 0xfffd,
};
static const unsigned char uhc_2_2uni_pagea1[3126] = {
  /* 0xa1 */
  0x00, 0x01, 0x02, 0x04, 0x05, 0x06, 0x07, 0x08,
  0x09, 0x0a, 0x0b, 0x0c, 0x0d, 0x0e, 0x0f, 0x10,
  0x11, 0x12, 0x13, 0x14, 0x15, 0x16, 0x19, 0x1a,
  0x1b, 0x1c, 0x1d, 0x1e, 0x20, 0x21, 0x22, 0x24,
  0x25, 0x26, 0x28, 0x29, 0x2a, 0x2b, 0x2c, 0x2d,
  0x2e, 0x31, 0x00, 0x02, 0x03, 0x04, 0x05, 0x06,
  0x07, 0x0a, 0x0b, 0x0d, 0x0e, 0x0f, 0x10, 0x11,
  0x12, 0x13, 0x14, 0x15, 0x16, 0x17, 0x18, 0x19,
  0x1a, 0x1b, 0x1c, 0x1e, 0x1f, 0x20, 0x21, 0x22,
  0x23, 0x26, 0x27, 0x29, 0x2a, 0x2b, 0x2f, 0x30,
  0x31, 0x32, 0x33, 0x36,
  /* 0xa2 */
  0x00, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07, 0x09,
  0x0a, 0x0b, 0x0c, 0x0d, 0x0e, 0x0f, 0x10, 0x11,
  0x12, 0x13, 0x14, 0x15, 0x16, 0x17, 0x18, 0x19,
  0x1a, 0x1b, 0x1d, 0x1e, 0x1f, 0x20, 0x21, 0x22,
  0x23, 0x25, 0x26, 0x27, 0x28, 0x29, 0x2a, 0x2b,
  0x2c, 0x2d, 0x00, 0x01, 0x02, 0x03, 0x04, 0x05,
  0x06, 0x07, 0x08, 0x09, 0x0a, 0x0b, 0x0c, 0x0d,
  0x0e, 0x0f, 0x10, 0x11, 0x14, 0x15, 0x17, 0x18,
  0x19, 0x1b, 0x1c, 0x1d, 0x1e, 0x1f, 0x20, 0x21,
  0x24, 0x26, 0x27, 0x28, 0x29, 0x2a, 0x2b, 0x2c,
  0x2d, 0x2f, 0x30, 0x31,
  /* 0xa3 */
  0x00, 0x01, 0x02, 0x04, 0x05, 0x06, 0x07, 0x08,
  0x09, 0x0a, 0x0c, 0x0d, 0x0e, 0x0f, 0x10, 0x11,
  0x12, 0x13, 0x14, 0x15, 0x16, 0x19, 0x1a, 0x1c,
  0x1d, 0x1e, 0x20, 0x21, 0x22, 0x23, 0x24, 0x25,
  0x26, 0x29, 0x2b, 0x2d, 0x2e, 0x2f, 0x30, 0x31,
  0x32, 0x33, 0x00, 0x01, 0x02, 0x03, 0x04, 0x05,
  0x06, 0x07, 0x08, 0x09, 0x0a, 0x0b, 0x0c, 0x0d,
  0x0e, 0x0f, 0x10, 0x11, 0x12, 0x13, 0x14, 0x15,
  0x16, 0x17, 0x18, 0x19, 0x1a, 0x1d, 0x1e, 0x20,
  0x21, 0x24, 0x26, 0x27, 0x28, 0x29, 0x2a, 0x2d,
  0x2f, 0x32, 0x33, 0x36,
  /* 0xa4 */
  0x00, 0x01, 0x03, 0x05, 0x07, 0x08, 0x0a, 0x0b,
  0x0c, 0x0d, 0x10, 0x14, 0x15, 0x16, 0x17, 0x18,
  0x19, 0x1c, 0x1d, 0x1f, 0x20, 0x21, 0x23, 0x24,
  0x25, 0x26, 0x27, 0x28, 0x29, 0x2c, 0x30, 0x31,
  0x32, 0x33, 0x34, 0x35, 0x37, 0x38, 0x39, 0x3b,
  0x3c, 0x3d, 0x00, 0x01, 0x02, 0x03, 0x04, 0x05,
  0x06, 0x07, 0x08, 0x09, 0x0a, 0x0b, 0x0c, 0x0e,
  0x0f, 0x10, 0x11, 0x12, 0x13, 0x14, 0x15, 0x16,
  0x17, 0x18, 0x19, 0x1a, 0x1b, 0x1c, 0x1d, 0x1e,
  0x1f, 0x20, 0x21, 0x22, 0x23, 0x24, 0x25, 0x26,
  0x27, 0x28, 0x29, 0x2a,
  /* 0xa5 */
  0x00, 0x01, 0x02, 0x03, 0x04, 0x07, 0x08, 0x0a,
  0x0b, 0x0c, 0x0e, 0x0f, 0x10, 0x11, 0x12, 0x13,
  0x14, 0x17, 0x1b, 0x1c, 0x1d, 0x1e, 0x1f, 0x20,
  0x22, 0x23, 0x24, 0x25, 0x26, 0x27, 0x28, 0x29,
  0x2a, 0x2b, 0x2c, 0x2d, 0x2e, 0x2f, 0x30, 0x31,
  0x32, 0x33, 0x00, 0x01, 0x03, 0x04, 0x05, 0x06,
  0x07, 0x08, 0x0a, 0x0b, 0x0c, 0x0d, 0x0e, 0x0f,
  0x10, 0x11, 0x12, 0x13, 0x14, 0x15, 0x16, 0x17,
  0x18, 0x19, 0x1a, 0x1b, 0x1c, 0x1e, 0x1f, 0x20,
  0x21, 0x22, 0x23, 0x24, 0x25, 0x26, 0x27, 0x28,
  0x29, 0x2a, 0x2b, 0x2c,
  /* 0xa6 */
  0x00, 0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07,
  0x08, 0x09, 0x0a, 0x0b, 0x0c, 0x0d, 0x0e, 0x0f,
  0x10, 0x11, 0x12, 0x13, 0x16, 0x17, 0x19, 0x1a,
  0x1b, 0x1d, 0x1e, 0x1f, 0x20, 0x21, 0x22, 0x23,
  0x26, 0x28, 0x2a, 0x2c, 0x2d, 0x2e, 0x2f, 0x32,
  0x33, 0x34, 0x00, 0x01, 0x02, 0x04, 0x05, 0x06,
  0x07, 0x08, 0x09, 0x0a, 0x0b, 0x0c, 0x0d, 0x0e,
  0x10, 0x11, 0x12, 0x13, 0x14, 0x15, 0x16, 0x18,
  0x19, 0x1a, 0x1b, 0x1c, 0x1d, 0x1e, 0x1f, 0x20,
  0x21, 0x22, 0x23, 0x24, 0x25, 0x26, 0x27, 0x28,
  0x29, 0x2a, 0x2c, 0x2d,
  /* 0xa7 */
  0x00, 0x01, 0x02, 0x03, 0x04, 0x06, 0x07, 0x08,
  0x0a, 0x0b, 0x0c, 0x0e, 0x0f, 0x10, 0x11, 0x12,
  0x13, 0x14, 0x17, 0x18, 0x19, 0x1a, 0x1b, 0x1c,
  0x1d, 0x1e, 0x1f, 0x20, 0x21, 0x22, 0x23, 0x24,
  0x25, 0x26, 0x27, 0x28, 0x29, 0x2a, 0x2b, 0x2c,
  0x2d, 0x2e, 0x00, 0x01, 0x02, 0x03, 0x04, 0x05,
  0x06, 0x08, 0x09, 0x0a, 0x0b, 0x0c, 0x0d, 0x10,
  0x11, 0x13, 0x14, 0x15, 0x17, 0x18, 0x19, 0x1a,
  0x1b, 0x1c, 0x1d, 0x20, 0x21, 0x22, 0x24, 0x25,
  0x26, 0x27, 0x28, 0x29, 0x2b, 0x2c, 0x2d, 0x2e,
  0x2f, 0x30, 0x31, 0x32,
  /* 0xa8 */
  0x00, 0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07,
  0x08, 0x09, 0x0a, 0x0d, 0x0e, 0x0f, 0x10, 0x11,
  0x12, 0x13, 0x14, 0x15, 0x16, 0x17, 0x18, 0x19,
  0x1a, 0x1b, 0x1c, 0x1d, 0x1e, 0x1f, 0x20, 0x21,
  0x22, 0x23, 0x24, 0x25, 0x26, 0x27, 0x28, 0x29,
  0x2a, 0x2b, 0x00, 0x01, 0x02, 0x04, 0x05, 0x06,
  0x07, 0x08, 0x09, 0x0a, 0x0b, 0x0c, 0x0d, 0x0e,
  0x0f, 0x10, 0x11, 0x12, 0x13, 0x14, 0x15, 0x16,
  0x17, 0x18, 0x19, 0x1a, 0x1b, 0x1c, 0x1d, 0x1e,
  0x20, 0x21, 0x22, 0x23, 0x24, 0x25, 0x26, 0x27,
  0x28, 0x29, 0x2a, 0x2b,
  /* 0xa9 */
  0x00, 0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07,
  0x08, 0x09, 0x0a, 0x0b, 0x0c, 0x0d, 0x0e, 0x10,
  0x11, 0x12, 0x13, 0x14, 0x15, 0x16, 0x17, 0x18,
  0x19, 0x1a, 0x1b, 0x1c, 0x1d, 0x1e, 0x20, 0x21,
  0x23, 0x25, 0x26, 0x27, 0x28, 0x29, 0x2a, 0x2b,
  0x2c, 0x2d, 0x00, 0x01, 0x02, 0x03, 0x04, 0x05,
  0x06, 0x07, 0x08, 0x09, 0x0a, 0x0b, 0x0c, 0x0d,
  0x0e, 0x0f, 0x10, 0x11, 0x12, 0x13, 0x14, 0x15,
  0x16, 0x17, 0x18, 0x1b, 0x1c, 0x1e, 0x1f, 0x20,
  0x22, 0x23, 0x24, 0x25, 0x26, 0x27, 0x28, 0x2b,
  0x2c, 0x2d, 0x30, 0x31,
  /* 0xaa */
  0x00, 0x01, 0x05, 0x06, 0x08, 0x0a, 0x0c, 0x0d,
  0x0e, 0x0f, 0x10, 0x11, 0x12, 0x15, 0x1a, 0x1b,
  0x1c, 0x1d, 0x1e, 0x21, 0x22, 0x24, 0x25, 0x26,
  0x28, 0x29, 0x2a, 0x2b, 0x2c, 0x2d, 0x2e, 0x31,
  0x35, 0x36, 0x37, 0x38, 0x39, 0x3a, 0x3c, 0x3d,
  0x3e, 0x40, 0x00, 0x02, 0x03, 0x04, 0x05, 0x06,
  0x07, 0x08, 0x0a, 0x0b, 0x0c, 0x0d, 0x0f, 0x10,
  0x11, 0x12, 0x13, 0x14, 0x15, 0x16, 0x17, 0x18,
  0x19, 0x1a, 0x1b, 0x1c, 0x1d, 0x1e, 0x1f, 0x20,
  0x21, 0x22, 0x23, 0x24, 0x25, 0x26, 0x27, 0x28,
  0x29, 0x2a, 0x2b, 0x2c,
  /* 0xab */
  0x00, 0x01, 0x02, 0x03, 0x06, 0x07, 0x09, 0x0a,
  0x0b, 0x0d, 0x0e, 0x0f, 0x10, 0x11, 0x12, 0x13,
  0x16, 0x1a, 0x1b, 0x1c, 0x1d, 0x1e, 0x1f, 0x22,
  0x23, 0x25, 0x26, 0x27, 0x29, 0x2a, 0x2b, 0x2c,
  0x2d, 0x2e, 0x2f, 0x32, 0x34, 0x36, 0x37, 0x38,
  0x39, 0x3a, 0x00, 0x02, 0x03, 0x04, 0x06, 0x07,
  0x08, 0x09, 0x0a, 0x0b, 0x0c, 0x0d, 0x0e, 0x0f,
  0x10, 0x11, 0x12, 0x13, 0x14, 0x16, 0x17, 0x18,
  0x19, 0x1a, 0x1b, 0x1c, 0x1e, 0x1f, 0x20, 0x22,
  0x23, 0x24, 0x25, 0x26, 0x27, 0x28, 0x29, 0x2a,
  0x2b, 0x2c, 0x2d, 0x2e,
  /* 0xac */
  0x00, 0x01, 0x02, 0x04, 0x05, 0x06, 0x07, 0x08,
  0x09, 0x0c, 0x0d, 0x0f, 0x10, 0x11, 0x13, 0x14,
  0x15, 0x16, 0x17, 0x18, 0x19, 0x1c, 0x1e, 0x20,
  0x21, 0x22, 0x23, 0x24, 0x25, 0x27, 0x28, 0x29,
  0x2b, 0x2c, 0x2d, 0x2f, 0x30, 0x31, 0x32, 0x33,
  0x34, 0x35, 0x00, 0x01, 0x02, 0x03, 0x04, 0x06,
  0x07, 0x08, 0x09, 0x0a, 0x0b, 0x0c, 0x0d, 0x0e,
  0x0f, 0x10, 0x11, 0x12, 0x13, 0x14, 0x15, 0x16,
  0x17, 0x18, 0x19, 0x1a, 0x1b, 0x1c, 0x1d, 0x1e,
  0x1f, 0x20, 0x21, 0x22, 0x23, 0x24, 0x25, 0x26,
  0x27, 0x29, 0x2a, 0x2b,
  /* 0xad */
  0x00, 0x01, 0x02, 0x04, 0x05, 0x06, 0x07, 0x08,
  0x09, 0x0a, 0x0d, 0x0f, 0x11, 0x12, 0x13, 0x14,
  0x15, 0x16, 0x18, 0x19, 0x1a, 0x1b, 0x1c, 0x1d,
  0x1e, 0x1f, 0x20, 0x21, 0x22, 0x23, 0x24, 0x25,
  0x26, 0x28, 0x29, 0x2a, 0x2b, 0x2c, 0x2d, 0x2e,
  0x2f, 0x30, 0x00, 0x01, 0x04, 0x05, 0x07, 0x08,
  0x09, 0x0b, 0x0c, 0x0d, 0x0e, 0x0f, 0x10, 0x11,
  0x14, 0x16, 0x18, 0x19, 0x1a, 0x1b, 0x1c, 0x1d,
  0x1f, 0x20, 0x21, 0x22, 0x23, 0x24, 0x25, 0x26,
  0x27, 0x28, 0x29, 0x2a, 0x2b, 0x2c, 0x2d, 0x2e,
  0x2f, 0x30, 0x31, 0x33,
  /* 0xae */
  0x00, 0x01, 0x02, 0x03, 0x04, 0x05, 0x07, 0x08,
  0x09, 0x0b, 0x0c, 0x0d, 0x0e, 0x0f, 0x10, 0x11,
  0x12, 0x13, 0x14, 0x15, 0x16, 0x17, 0x18, 0x19,
  0x1a, 0x1b, 0x1c, 0x1d, 0x1e, 0x1f, 0x20, 0x21,
  0x23, 0x24, 0x25, 0x27, 0x28, 0x29, 0x2b, 0x2c,
  0x2d, 0x2e, 0x00, 0x01, 0x02, 0x05, 0x07, 0x09,
  0x0a, 0x0b, 0x0c, 0x0d, 0x0e, 0x10, 0x11, 0x12,
  0x14, 0x15, 0x16, 0x18, 0x19, 0x1a, 0x1b, 0x1c,
  0x1d, 0x1e, 0x20, 0x21, 0x22, 0x23, 0x25, 0x26,
  0x27, 0x28, 0x29, 0x2a, 0x2d, 0x2e, 0x30, 0x31,
  0x32, 0x34, 0x35, 0x36,
  /* 0xaf */
  0x00, 0x01, 0x02, 0x03, 0x06, 0x08, 0x0a, 0x0b,
  0x0c, 0x0d, 0x0e, 0x0f, 0x10, 0x11, 0x12, 0x13,
  0x14, 0x15, 0x16, 0x17, 0x18, 0x19, 0x1a, 0x1b,
  0x1c, 0x1d, 0x1e, 0x1f, 0x20, 0x21, 0x22, 0x23,
  0x24, 0x25, 0x26, 0x27, 0x28, 0x29, 0x2a, 0x2b,
  0x2e, 0x2f, 0x00, 0x01, 0x05, 0x06, 0x07, 0x08,
  0x09, 0x0a, 0x0d, 0x0f, 0x11, 0x12, 0x13, 0x14,
  0x15, 0x16, 0x19, 0x1a, 0x1c, 0x1d, 0x1e, 0x20,
  0x21, 0x22, 0x23, 0x24, 0x25, 0x26, 0x29, 0x2b,
  0x2d, 0x2e, 0x2f, 0x30, 0x31, 0x32, 0x35, 0x36,
  0x38, 0x39, 0x3a, 0x3c,
  /* 0xb0 */
  0x00, 0x01, 0x02, 0x03, 0x04, 0x05, 0x08, 0x0c,
  0x0d, 0x0e, 0x0f, 0x10, 0x11, 0x14, 0x15, 0x16,
  0x17, 0x18, 0x19, 0x1a, 0x1b, 0x1c, 0x1d, 0x1e,
  0x1f, 0x20, 0x21, 0x22, 0x23, 0x24, 0x25, 0x26,
  0x28, 0x29, 0x2a, 0x2b, 0x2c, 0x2d, 0x2e, 0x2f,
  0x30, 0x31, 0x00, 0x01, 0x02, 0x03, 0x04, 0x05,
  0x06, 0x07, 0x08, 0x09, 0x0a, 0x0b, 0x0c, 0x0d,
  0x0e, 0x0f, 0x10, 0x11, 0x12, 0x13, 0x14, 0x15,
  0x16, 0x17, 0x1a, 0x1b, 0x1d, 0x1e, 0x21, 0x22,
  0x23, 0x24, 0x25, 0x26, 0x27, 0x2a, 0x2e, 0x2f,
  0x30, 0x31, 0x32, 0x33,
  /* 0xb1 */
  0x00, 0x01, 0x03, 0x04, 0x05, 0x07, 0x08, 0x09,
  0x0a, 0x0b, 0x0c, 0x0d, 0x10, 0x12, 0x14, 0x15,
  0x16, 0x17, 0x18, 0x19, 0x1b, 0x1c, 0x1d, 0x1f,
  0x20, 0x21, 0x23, 0x24, 0x25, 0x26, 0x27, 0x28,
  0x29, 0x2c, 0x30, 0x31, 0x32, 0x33, 0x34, 0x35,
  0x37, 0x38, 0x00, 0x01, 0x02, 0x03, 0x04, 0x05,
  0x06, 0x07, 0x08, 0x09, 0x0a, 0x0b, 0x0c, 0x0d,
  0x0e, 0x0f, 0x10, 0x11, 0x12, 0x13, 0x14, 0x15,
  0x16, 0x17, 0x18, 0x1b, 0x1c, 0x1e, 0x1f, 0x20,
  0x22, 0x23, 0x24, 0x25, 0x26, 0x27, 0x28, 0x2b,
  0x2d, 0x2f, 0x30, 0x31,
  /* 0xb2 */
  0x00, 0x01, 0x02, 0x05, 0x06, 0x08, 0x09, 0x0a,
  0x0c, 0x0d, 0x0e, 0x0f, 0x10, 0x11, 0x12, 0x14,
  0x15, 0x16, 0x17, 0x19, 0x1a, 0x1b, 0x1c, 0x1d,
  0x1e, 0x20, 0x21, 0x22, 0x23, 0x24, 0x25, 0x26,
  0x27, 0x28, 0x29, 0x2a, 0x2b, 0x2c, 0x2d, 0x2e,
  0x2f, 0x30, 0x00, 0x01, 0x02, 0x04, 0x05, 0x06,
  0x07, 0x08, 0x09, 0x0b, 0x0c, 0x0d, 0x0e, 0x0f,
  0x10, 0x11, 0x13, 0x14, 0x15, 0x16, 0x17, 0x18,
  0x19, 0x1a, 0x1b, 0x1c, 0x1d, 0x1e, 0x1f, 0x20,
  0x21, 0x22, 0x23, 0x24, 0x25, 0x27, 0x28, 0x29,
  0x2a, 0x2b, 0x2c, 0x2d,
  /* 0xb3 */
  0x00, 0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07,
  0x08, 0x09, 0x0a, 0x0b, 0x0c, 0x0d, 0x0e, 0x0f,
  0x10, 0x11, 0x12, 0x13, 0x16, 0x17, 0x19, 0x1a,
  0x1b, 0x1d, 0x1e, 0x1f, 0x20, 0x21, 0x22, 0x23,
  0x26, 0x28, 0x2a, 0x2b, 0x2c, 0x2d, 0x2e, 0x2f,
  0x31, 0x32, 0x00, 0x02, 0x03, 0x04, 0x06, 0x07,
  0x08, 0x09, 0x0a, 0x0b, 0x0c, 0x0d, 0x0e, 0x0f,
  0x10, 0x11, 0x13, 0x14, 0x15, 0x16, 0x17, 0x18,
  0x1a, 0x1b, 0x1c, 0x1d, 0x1e, 0x1f, 0x20, 0x21,
  0x22, 0x23, 0x24, 0x25, 0x26, 0x27, 0x28, 0x29,
  0x2a, 0x2b, 0x2c, 0x2d,
  /* 0xb4 */
  0x00, 0x01, 0x02, 0x03, 0x04, 0x05, 0x08, 0x09,
  0x0b, 0x0c, 0x0d, 0x0f, 0x10, 0x11, 0x12, 0x13,
  0x14, 0x15, 0x18, 0x1a, 0x1c, 0x1d, 0x1e, 0x1f,
  0x20, 0x21, 0x23, 0x24, 0x25, 0x27, 0x28, 0x29,
  0x2b, 0x2c, 0x2d, 0x2e, 0x2f, 0x30, 0x31, 0x33,
  0x34, 0x35, 0x00, 0x01, 0x02, 0x03, 0x04, 0x05,
  0x06, 0x07, 0x0a, 0x0b, 0x0d, 0x0e, 0x0f, 0x11,
  0x12, 0x13, 0x14, 0x15, 0x16, 0x17, 0x1a, 0x1b,
  0x1c, 0x1e, 0x1f, 0x20, 0x21, 0x22, 0x23, 0x24,
  0x25, 0x26, 0x27, 0x28, 0x29, 0x2a, 0x2b, 0x2c,
  0x2d, 0x2e, 0x2f, 0x30,
  /* 0xb5 */
  0x00, 0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07,
  0x08, 0x09, 0x0a, 0x0b, 0x0c, 0x0d, 0x0e, 0x11,
  0x12, 0x14, 0x15, 0x16, 0x18, 0x19, 0x1a, 0x1b,
  0x1c, 0x1d, 0x1e, 0x21, 0x23, 0x25, 0x26, 0x27,
  0x28, 0x29, 0x2a, 0x2d, 0x2e, 0x30, 0x31, 0x32,
  0x35, 0x36, 0x00, 0x01, 0x02, 0x03, 0x06, 0x0a,
  0x0b, 0x0c, 0x0d, 0x0e, 0x0f, 0x12, 0x13, 0x15,
  0x16, 0x17, 0x19, 0x1a, 0x1b, 0x1c, 0x1d, 0x1e,
  0x1f, 0x22, 0x26, 0x27, 0x28, 0x29, 0x2a, 0x2b,
  0x2d, 0x2e, 0x2f, 0x30, 0x31, 0x32, 0x33, 0x34,
  0x35, 0x36, 0x37, 0x38,
  /* 0xb6 */
  0x00, 0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07,
  0x09, 0x0a, 0x0b, 0x0c, 0x0d, 0x0e, 0x0f, 0x10,
  0x11, 0x12, 0x13, 0x14, 0x15, 0x16, 0x17, 0x18,
  0x19, 0x1a, 0x1b, 0x1c, 0x1d, 0x1e, 0x1f, 0x20,
  0x21, 0x22, 0x23, 0x24, 0x25, 0x26, 0x27, 0x28,
  0x29, 0x2a, 0x00, 0x01, 0x03, 0x04, 0x05, 0x07,
  0x09, 0x0a, 0x0b, 0x0c, 0x0d, 0x10, 0x14, 0x15,
  0x16, 0x17, 0x18, 0x19, 0x1c, 0x1d, 0x1f, 0x20,
  0x21, 0x23, 0x24, 0x25, 0x26, 0x27, 0x28, 0x29,
  0x2c, 0x2e, 0x30, 0x31, 0x32, 0x33, 0x34, 0x35,
  0x37, 0x38, 0x39, 0x3b,
  /* 0xb7 */
  0x00, 0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07,
  0x08, 0x09, 0x0a, 0x0b, 0x0c, 0x0d, 0x0f, 0x10,
  0x11, 0x12, 0x13, 0x14, 0x15, 0x17, 0x18, 0x19,
  0x1b, 0x1c, 0x1d, 0x1e, 0x1f, 0x20, 0x21, 0x22,
  0x23, 0x24, 0x25, 0x26, 0x27, 0x28, 0x29, 0x2a,
  0x2b, 0x2c, 0x00, 0x01, 0x02, 0x03, 0x04, 0x07,
  0x08, 0x0a, 0x0b, 0x0c, 0x0e, 0x0f, 0x10, 0x11,
  0x12, 0x13, 0x14, 0x17, 0x19, 0x1b, 0x1c, 0x1d,
  0x1e, 0x20, 0x22, 0x23, 0x24, 0x26, 0x27, 0x28,
  0x29, 0x2a, 0x2b, 0x2c, 0x2d, 0x2e, 0x2f, 0x30,
  0x31, 0x32, 0x33, 0x34,
  /* 0xb8 */
  0x00, 0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07,
  0x09, 0x0a, 0x0b, 0x0c, 0x0d, 0x0e, 0x0f, 0x10,
  0x11, 0x12, 0x13, 0x14, 0x15, 0x16, 0x17, 0x18,
  0x19, 0x1a, 0x1b, 0x1c, 0x1d, 0x1e, 0x1f, 0x20,
  0x21, 0x22, 0x23, 0x25, 0x26, 0x27, 0x29, 0x2a,
  0x2b, 0x2c, 0x00, 0x01, 0x02, 0x03, 0x04, 0x05,
  0x06, 0x07, 0x08, 0x09, 0x0b, 0x0d, 0x0e, 0x0f,
  0x10, 0x11, 0x12, 0x14, 0x15, 0x16, 0x17, 0x18,
  0x19, 0x1a, 0x1b, 0x1c, 0x1d, 0x1e, 0x1f, 0x20,
  0x21, 0x22, 0x23, 0x24, 0x25, 0x26, 0x27, 0x28,
  0x29, 0x2a, 0x2b, 0x2c,
  /* 0xb9 */
  0x00, 0x01, 0x04, 0x05, 0x07, 0x08, 0x09, 0x0b,
  0x0c, 0x0d, 0x0e, 0x0f, 0x10, 0x11, 0x14, 0x16,
  0x18, 0x19, 0x1a, 0x1b, 0x1c, 0x1d, 0x1f, 0x20,
  0x21, 0x22, 0x23, 0x24, 0x25, 0x26, 0x27, 0x28,
  0x29, 0x2a, 0x2b, 0x2c, 0x2d, 0x2e, 0x2f, 0x30,
  0x31, 0x33, 0x00, 0x01, 0x02, 0x03, 0x04, 0x05,
  0x07, 0x08, 0x09, 0x0a, 0x0b, 0x0c, 0x0d, 0x0e,
  0x0f, 0x10, 0x11, 0x12, 0x13, 0x14, 0x15, 0x16,
  0x17, 0x18, 0x19, 0x1a, 0x1b, 0x1c, 0x1d, 0x1e,
  0x1f, 0x20, 0x21, 0x24, 0x25, 0x27, 0x28, 0x29,
  0x2b, 0x2c, 0x2d, 0x2e,
  /* 0xba */
  0x00, 0x01, 0x02, 0x05, 0x06, 0x07, 0x09, 0x0a,
  0x0b, 0x0c, 0x0d, 0x0e, 0x10, 0x11, 0x12, 0x14,
  0x15, 0x16, 0x18, 0x19, 0x1a, 0x1b, 0x1c, 0x1d,
  0x1e, 0x20, 0x21, 0x22, 0x23, 0x25, 0x26, 0x27,
  0x28, 0x29, 0x2a, 0x2d, 0x2e, 0x30, 0x31, 0x34,
  0x36, 0x37, 0x00, 0x01, 0x02, 0x05, 0x07, 0x08,
  0x09, 0x0a, 0x0b, 0x0c, 0x0d, 0x0e, 0x10, 0x11,
  0x12, 0x14, 0x15, 0x16, 0x18, 0x19, 0x1a, 0x1b,
  0x1c, 0x1d, 0x1e, 0x21, 0x22, 0x23, 0x24, 0x25,
  0x26, 0x27, 0x28, 0x29, 0x2a, 0x2d, 0x2e, 0x30,
  0x31, 0x32, 0x34, 0x35,
  /* 0xbb */
  0x00, 0x01, 0x02, 0x03, 0x04, 0x07, 0x09, 0x0b,
  0x0c, 0x0d, 0x0e, 0x0f, 0x10, 0x14, 0x16, 0x17,
  0x18, 0x1a, 0x1c, 0x1d, 0x1e, 0x1f, 0x20, 0x23,
  0x27, 0x28, 0x29, 0x2b, 0x2c, 0x2f, 0x30, 0x32,
  0x33, 0x34, 0x36, 0x37, 0x38, 0x39, 0x3a, 0x3b,
  0x3c, 0x3f, 0x00, 0x01, 0x02, 0x03, 0x04, 0x05,
  0x08, 0x09, 0x0a, 0x0b, 0x0c, 0x0d, 0x0e, 0x0f,
  0x10, 0x11, 0x12, 0x13, 0x14, 0x15, 0x16, 0x17,
  0x18, 0x19, 0x1a, 0x1b, 0x1c, 0x1d, 0x1e, 0x1f,
  0x20, 0x21, 0x22, 0x23, 0x24, 0x25, 0x26, 0x27,
  0x28, 0x29, 0x2a, 0x2b,
  /* 0xbc */
  0x00, 0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07,
  0x08, 0x09, 0x0a, 0x0b, 0x0c, 0x0d, 0x0e, 0x0f,
  0x10, 0x11, 0x14, 0x15, 0x17, 0x18, 0x19, 0x1b,
  0x1c, 0x1d, 0x1e, 0x1f, 0x20, 0x21, 0x24, 0x28,
  0x29, 0x2a, 0x2b, 0x2c, 0x2d, 0x30, 0x31, 0x33,
  0x34, 0x35, 0x00, 0x01, 0x02, 0x03, 0x04, 0x05,
  0x06, 0x09, 0x0b, 0x0d, 0x0e, 0x0f, 0x10, 0x11,
  0x12, 0x14, 0x15, 0x16, 0x18, 0x19, 0x1a, 0x1c,
  0x1d, 0x1e, 0x1f, 0x20, 0x21, 0x22, 0x25, 0x26,
  0x29, 0x2a, 0x2b, 0x2c, 0x2d, 0x2e, 0x30, 0x31,
  0x32, 0x33, 0x34, 0x35,
  /* 0xbd */
  0x00, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07, 0x08,
  0x09, 0x0b, 0x0d, 0x0e, 0x0f, 0x10, 0x11, 0x12,
  0x13, 0x14, 0x17, 0x18, 0x1a, 0x1b, 0x1c, 0x1e,
  0x1f, 0x20, 0x21, 0x22, 0x23, 0x24, 0x27, 0x29,
  0x2b, 0x2c, 0x2d, 0x2e, 0x2f, 0x30, 0x32, 0x33,
  0x34, 0x35, 0x00, 0x01, 0x02, 0x03, 0x04, 0x05,
  0x06, 0x07, 0x08, 0x09, 0x0a, 0x0b, 0x0c, 0x0d,
  0x0e, 0x0f, 0x11, 0x12, 0x13, 0x14, 0x15, 0x16,
  0x17, 0x18, 0x19, 0x1a, 0x1b, 0x1c, 0x1d, 0x1e,
  0x1f, 0x20, 0x21, 0x22, 0x23, 0x24, 0x25, 0x26,
  0x27, 0x28, 0x29, 0x2a,
  /* 0xbe */
  0x00, 0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07,
  0x09, 0x0a, 0x0b, 0x0d, 0x0e, 0x0f, 0x10, 0x11,
  0x12, 0x13, 0x14, 0x15, 0x16, 0x17, 0x18, 0x19,
  0x1a, 0x1b, 0x1c, 0x1d, 0x1e, 0x1f, 0x20, 0x21,
  0x22, 0x23, 0x25, 0x26, 0x27, 0x29, 0x2a, 0x2b,
  0x2d, 0x2e, 0x00, 0x01, 0x02, 0x03, 0x04, 0x05,
  0x07, 0x09, 0x0a, 0x0b, 0x0c, 0x0d, 0x0e, 0x0f,
  0x10, 0x13, 0x14, 0x16, 0x17, 0x1a, 0x1c, 0x1d,
  0x1e, 0x1f, 0x20, 0x23, 0x25, 0x27, 0x28, 0x29,
  0x2a, 0x2b, 0x2c, 0x2e, 0x2f, 0x30, 0x31, 0x32,
  0x33, 0x34, 0x35, 0x36,
  /* 0xbf */
  0x00, 0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07,
  0x08, 0x09, 0x0a, 0x0c, 0x0d, 0x0e, 0x0f, 0x10,
  0x11, 0x12, 0x13, 0x14, 0x15, 0x16, 0x17, 0x18,
  0x19, 0x1a, 0x1b, 0x1c, 0x1d, 0x1e, 0x1f, 0x20,
  0x21, 0x22, 0x23, 0x24, 0x25, 0x26, 0x27, 0x28,
  0x29, 0x2a, 0x00, 0x01, 0x02, 0x04, 0x05, 0x06,
  0x08, 0x09, 0x0a, 0x0c, 0x0d, 0x0e, 0x0f, 0x10,
  0x11, 0x12, 0x14, 0x15, 0x17, 0x18, 0x19, 0x1a,
  0x1b, 0x1c, 0x1d, 0x1e, 0x20, 0x21, 0x22, 0x24,
  0x25, 0x26, 0x28, 0x29, 0x2a, 0x2b, 0x2c, 0x2d,
  0x2e, 0x30, 0x31, 0x33,
  /* 0xc0 */
  0x00, 0x01, 0x02, 0x03, 0x04, 0x05, 0x07, 0x08,
  0x09, 0x0b, 0x0c, 0x0d, 0x0f, 0x10, 0x11, 0x12,
  0x13, 0x14, 0x15, 0x18, 0x1a, 0x1b, 0x1c, 0x1d,
  0x1e, 0x1f, 0x20, 0x21, 0x22, 0x23, 0x24, 0x25,
  0x26, 0x27, 0x28, 0x29, 0x2a, 0x2b, 0x2c, 0x2d,
  0x2e, 0x2f, 0x00, 0x01, 0x02, 0x03, 0x04, 0x05,
  0x06, 0x07, 0x08, 0x09, 0x0a, 0x0b, 0x0c, 0x0d,
  0x10, 0x11, 0x13, 0x14, 0x15, 0x17, 0x18, 0x19,
  0x1a, 0x1b, 0x1c, 0x1d, 0x20, 0x22, 0x24, 0x25,
  0x26, 0x27, 0x28, 0x29, 0x2c, 0x2d, 0x2f, 0x30,
  0x31, 0x33, 0x34, 0x35,
  /* 0xc1 */
  0x00, 0x02, 0x03, 0x06, 0x08, 0x0a, 0x0b, 0x0c,
  0x0d, 0x0e, 0x0f, 0x12, 0x13, 0x15, 0x16, 0x17,
  0x19, 0x1a, 0x1b, 0x1c, 0x1d, 0x1e, 0x1f, 0x22,
  0x26, 0x27, 0x28, 0x29, 0x2a, 0x2b, 0x2d, 0x2e,
  0x2f, 0x30, 0x31, 0x32, 0x33, 0x34, 0x35, 0x36,
  0x37, 0x38, 0x00, 0x01, 0x02, 0x03, 0x04, 0x05,
  0x06, 0x07, 0x09, 0x0a, 0x0b, 0x0c, 0x0d, 0x0e,
  0x0f, 0x10, 0x11, 0x12, 0x13, 0x14, 0x15, 0x16,
  0x17, 0x18, 0x19, 0x1a, 0x1b, 0x1c, 0x1d, 0x1e,
  0x1f, 0x20, 0x21, 0x22, 0x23, 0x24, 0x25, 0x26,
  0x27, 0x28, 0x29, 0x2a,
  /* 0xc2 */
  0x00, 0x01, 0x03, 0x04, 0x05, 0x07, 0x09, 0x0a,
  0x0b, 0x0c, 0x0d, 0x10, 0x12, 0x14, 0x15, 0x16,
  0x17, 0x18, 0x19, 0x1c, 0x1d, 0x1f, 0x20, 0x21,
  0x23, 0x24, 0x25, 0x26, 0x27, 0x28, 0x29, 0x2c,
  0x2e, 0x30, 0x31, 0x32, 0x33, 0x34, 0x35, 0x38,
  0x39, 0x3b, 0x00, 0x01, 0x03, 0x04, 0x05, 0x06,
  0x07, 0x08, 0x09, 0x0c, 0x10, 0x11, 0x12, 0x13,
  0x14, 0x15, 0x17, 0x18, 0x19, 0x1b, 0x1c, 0x1d,
  0x1f, 0x20, 0x21, 0x22, 0x23, 0x24, 0x25, 0x26,
  0x28, 0x29, 0x2a, 0x2b, 0x2c, 0x2d, 0x2e, 0x2f,
  0x30, 0x31, 0x34, 0x35,
  /* 0xc3 */
  0x00, 0x01, 0x02, 0x04, 0x05, 0x06, 0x07, 0x09,
  0x0a, 0x0d, 0x0f, 0x11, 0x12, 0x13, 0x15, 0x16,
  0x19, 0x1a, 0x1c, 0x1d, 0x1e, 0x20, 0x21, 0x22,
  0x23, 0x24, 0x25, 0x26, 0x27, 0x28, 0x29, 0x2b,
  0x2d, 0x2e, 0x2f, 0x30, 0x31, 0x32, 0x35, 0x36,
  0x38, 0x39, 0x00, 0x01, 0x02, 0x03, 0x04, 0x05,
  0x06, 0x07, 0x08, 0x09, 0x0a, 0x0b, 0x0d, 0x0f,
  0x10, 0x11, 0x12, 0x13, 0x14, 0x17, 0x18, 0x1a,
  0x1b, 0x1c, 0x1e, 0x1f, 0x20, 0x21, 0x22, 0x23,
  0x24, 0x25, 0x27, 0x29, 0x2b, 0x2c, 0x2d, 0x2e,
  0x2f, 0x30, 0x32, 0x33,
  /* 0xc4 */
  0x00, 0x02, 0x03, 0x04, 0x06, 0x07, 0x08, 0x09,
  0x0a, 0x0b, 0x0c, 0x0d, 0x0f, 0x11, 0x12, 0x13,
  0x14, 0x15, 0x16, 0x17, 0x18, 0x1b, 0x1c, 0x1e,
  0x1f, 0x20, 0x22, 0x23, 0x24, 0x25, 0x27, 0x28,
  0x2a, 0x2b, 0x2d, 0x2f, 0x30, 0x31, 0x32, 0x33,
  0x34, 0x36, 0x00, 0x01, 0x03, 0x04, 0x05, 0x07,
  0x08, 0x09, 0x0a, 0x0b, 0x0c, 0x0d, 0x0f, 0x10,
  0x11, 0x12, 0x14, 0x15, 0x16, 0x17, 0x18, 0x19,
  0x1c, 0x1d, 0x1f, 0x20, 0x21, 0x23, 0x24, 0x25,
  0x26, 0x27, 0x28, 0x29, 0x2a, 0x2b, 0x2c, 0x2d,
  0x2e, 0x30, 0x31, 0x32,
  /* 0xc5 */
  0x00, 0x01, 0x02, 0x05, 0x06, 0x08, 0x09, 0x0a,
  0x0c, 0x0d, 0x0e, 0x0f, 0x10, 0x11, 0x12, 0x15,
  0x17, 0x19, 0x1a, 0x1b, 0x1c, 0x1d, 0x1e, 0x21,
  0x22, 0x24, 0x25, 0x26, 0x28, 0x29, 0x2a, 0x2b,
  0x2c, 0x2d, 0x2e, 0x30, 0x31, 0x33, 0x35, 0x36,
  0x37, 0x38, 0x00, 0x01, 0x04, 0x05, 0x07, 0x0c,
  0x0d, 0x0e, 0x0f, 0x10, 0x11, 0x14, 0x16, 0x18,
  0x19, 0x1a, 0x1c, 0x1d, 0x1f, 0x20, 0x21, 0x23,
  0x24, 0x25, 0x27, 0x28, 0x29, 0x2a, 0x2b, 0x2c,
  0x2d, 0x30, 0x31, 0x32, 0x34, 0x35, 0x36, 0x37,
  0x38, 0x39, 0x3c, 0x3d,
  /* 0xc6 */
  0x00, 0x01, 0x02, 0x04, 0x05, 0x06, 0x07, 0x08,
  0x09, 0x0a, 0x0d, 0x0f, 0x11, 0x12, 0x13, 0x14,
  0x15, 0x16,
};

static int
uhc_2_mbtowc (conv_t conv, ucs4_t *pwc, const unsigned char *s, int n)
{
  unsigned char c1 = s[0];
  if ((c1 >= 0xa1 && c1 <= 0xc6)) {
    if (n >= 2) {
      unsigned char c2 = s[1];
      if ((c2 >= 0x41 && c2 < 0x5b) || (c2 >= 0x61 && c2 < 0x7b) || (c2 >= 0x81 && c2 < 0xa1)) {
        unsigned int row = c1 - 0xa1;
        unsigned int col = c2 - (c2 >= 0x81 ? 0x4d : c2 >= 0x61 ? 0x47 : 0x41);
        unsigned int i = 84 * row + col;
        if (i < 3126) {
          *pwc = (ucs4_t) (uhc_2_2uni_main_pagea1[2*row+(col>=42?1:0)] + uhc_2_2uni_pagea1[i]);
          return 2;
        }
      }
      return RET_ILSEQ;
    }
    return RET_TOOFEW(0);
  }
  return RET_ILSEQ;
}

static const unsigned short uhc_2_2charset_main[49] = {
  0xa141, 0xa18d, 0xa273, 0xa359, 0xa445, 0xa491, 0xa577, 0xa663,
  0xa749, 0xa795, 0xa881, 0xa967, 0xaa4d, 0xaa99, 0xab85, 0xac6b,
  0xad51, 0xad9d, 0xae89, 0xaf6f, 0xb055, 0xb141, 0xb18d, 0xb273,
  0xb359, 0xb445, 0xb491, 0xb577, 0xb663, 0xb749, 0xb795, 0xb881,
  0xb967, 0xba4d, 0xba99, 0xbb85, 0xbc6b, 0xbd51, 0xbd9d, 0xbe89,
  0xbf6f, 0xc055, 0xc141, 0xc18d, 0xc273, 0xc359, 0xc445, 0xc491,
  0xc577,
};
static const unsigned char uhc_2_2charset[3126] = {
  0x00, 0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07,
  0x08, 0x09, 0x0a, 0x0b, 0x0c, 0x0d, 0x0e, 0x0f,
  0x10, 0x11, 0x12, 0x13, 0x14, 0x15, 0x16, 0x17,
  0x18, 0x19, 0x20, 0x21, 0x22, 0x23, 0x24, 0x25,
  0x26, 0x27, 0x28, 0x29, 0x2a, 0x2b, 0x2c, 0x2d,
  0x2e, 0x2f, 0x30, 0x31, 0x32, 0x33, 0x34, 0x35,
  0x36, 0x37, 0x38, 0x39, 0x40, 0x41, 0x42, 0x43,
  0x44, 0x45, 0x46, 0x47, 0x48, 0x49, 0x4a, 0x4b,
  0x00, 0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07,
  0x08, 0x09, 0x0a, 0x0b, 0x0c, 0x0d, 0x0e, 0x0f,
  0x10, 0x11, 0x12, 0x13, 0xb4, 0xb5, 0xb6, 0xb7,
  0xb8, 0xb9, 0xba, 0xbb, 0xbc, 0xbd, 0xbe, 0xbf,
  0xc0, 0xc1, 0xc2, 0xc3, 0xc4, 0xc5, 0xc6, 0xc7,
  0xc8, 0xc9, 0xca, 0xcb, 0xcc, 0xcd, 0xd4, 0xd5,
  0xd6, 0xd7, 0xd8, 0xd9, 0xda, 0xdb, 0xdc, 0xdd,
  0xde, 0xdf, 0xe0, 0xe1, 0xe2, 0xe3, 0xe4, 0xe5,
  0x00, 0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07,
  0x0e, 0x0f, 0x10, 0x11, 0x12, 0x13, 0x14, 0x15,
  0x16, 0x17, 0x18, 0x19, 0x1a, 0x1b, 0x1c, 0x1d,
  0x1e, 0x1f, 0x20, 0x21, 0x22, 0x23, 0x24, 0x25,
  0x26, 0x27, 0x28, 0x29, 0x2a, 0x2b, 0x2c, 0x2d,
  0xce, 0xcf, 0xd0, 0xd1, 0xd2, 0xd3, 0xd4, 0xd5,
  0xd6, 0xd7, 0xd8, 0xd9, 0xda, 0xdb, 0xdc, 0xdd,
  0xde, 0xdf, 0xe0, 0xe1, 0xe2, 0xe3, 0xe4, 0xe5,
  0x00, 0x01, 0x08, 0x09, 0x0a, 0x0b, 0x0c, 0x0d,
  0x0e, 0x0f, 0x10, 0x11, 0x12, 0x13, 0x14, 0x15,
  0x16, 0x17, 0x18, 0x19, 0x1a, 0x1b, 0x1c, 0x1d,
  0x1e, 0x1f, 0x20, 0x21, 0x28, 0x29, 0x2a, 0x2b,
  0x2c, 0x2d, 0x2e, 0x2f, 0x30, 0x31, 0x32, 0x33,
  0x34, 0x35, 0x36, 0x37, 0x38, 0x39, 0x3a, 0x3b,
  0x3c, 0x3d, 0x3e, 0x3f, 0x40, 0x41, 0x42, 0x43,
  0x44, 0x45, 0x46, 0x47, 0xe8, 0xe9, 0xea, 0xeb,
  0x00, 0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07,
  0x08, 0x09, 0x0a, 0x0b, 0x0c, 0x0d, 0x0e, 0x0f,
  0x10, 0x11, 0x12, 0x13, 0x14, 0x15, 0x1c, 0x1d,
  0x1e, 0x1f, 0x20, 0x21, 0x22, 0x23, 0x24, 0x25,
  0x26, 0x27, 0x28, 0x29, 0x2a, 0x2b, 0x2c, 0x2d,
  0x2e, 0x2f, 0x30, 0x31, 0x32, 0x33, 0x34, 0x35,
  0x3c, 0x3d, 0x3e, 0x3f, 0x40, 0x41, 0x42, 0x43,
  0x44, 0x45, 0x46, 0x47, 0x48, 0x49, 0x4a, 0x4b,
  0x00, 0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07,
  0x08, 0x09, 0x0a, 0x0b, 0x0c, 0x0d, 0x0e, 0x0f,
  0xb0, 0xb1, 0xb2, 0xb3, 0xb4, 0xb5, 0xb6, 0xb7,
  0xb8, 0xb9, 0xba, 0xbb, 0xbc, 0xbd, 0xbe, 0xbf,
  0xc0, 0xc1, 0xc2, 0xc3, 0xc4, 0xc5, 0xc6, 0xc7,
  0xc8, 0xc9, 0xd0, 0xd1, 0xd2, 0xd3, 0xd4, 0xd5,
  0xd6, 0xd7, 0xd8, 0xd9, 0xda, 0xdb, 0xdc, 0xdd,
  0xde, 0xdf, 0xe0, 0xe1, 0xe2, 0xe3, 0xe4, 0xe5,
  0x00, 0x01, 0x02, 0x03, 0x0a, 0x0b, 0x0c, 0x0d,
  0x0e, 0x0f, 0x10, 0x11, 0x12, 0x13, 0x14, 0x15,
  0x16, 0x17, 0x18, 0x19, 0x1a, 0x1b, 0x1c, 0x1d,
  0x1e, 0x1f, 0x20, 0x21, 0x22, 0x23, 0x24, 0x25,
  0x26, 0x27, 0x28, 0x29, 0xca, 0xcb, 0xcc, 0xcd,
  0xce, 0xcf, 0xd0, 0xd1, 0xd2, 0xd3, 0xd4, 0xd5,
  0xd6, 0xd7, 0xd8, 0xd9, 0xda, 0xdb, 0xdc, 0xdd,
  0xde, 0xdf, 0xe0, 0xe1, 0xe2, 0xe3, 0xea, 0xeb,
  0x00, 0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07,
  0x08, 0x09, 0x0a, 0x0b, 0x0c, 0x0d, 0x0e, 0x0f,
  0x10, 0x11, 0x12, 0x13, 0x14, 0x15, 0x16, 0x17,
  0x1e, 0x1f, 0x20, 0x21, 0x22, 0x23, 0x24, 0x25,
  0x26, 0x27, 0x28, 0x29, 0x2a, 0x2b, 0x2c, 0x2d,
  0x2e, 0x2f, 0x30, 0x31, 0x32, 0x33, 0x34, 0x35,
  0x36, 0x37, 0x38, 0x39, 0x3a, 0x3b, 0x3c, 0x3d,
  0xde, 0xdf, 0xe0, 0xe1, 0xe2, 0xe3, 0xe4, 0xe5,
  0x00, 0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07,
  0x08, 0x09, 0x0a, 0x0b, 0x0c, 0x0d, 0x0e, 0x0f,
  0x10, 0x11, 0x18, 0x19, 0x1a, 0x1b, 0x1c, 0x1d,
  0x1e, 0x1f, 0x20, 0x21, 0x22, 0x23, 0x24, 0x25,
  0x26, 0x27, 0x28, 0x29, 0x2a, 0x2b, 0x2c, 0x2d,
  0x2e, 0x2f, 0x30, 0x31, 0x38, 0x39, 0x3a, 0x3b,
  0x3c, 0x3d, 0x3e, 0x3f, 0x40, 0x41, 0x42, 0x43,
  0x44, 0x45, 0x46, 0x47, 0x48, 0x49, 0x4a, 0x4b,
  0x00, 0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07,
  0x08, 0x09, 0x0a, 0x0b, 0xac, 0xad, 0xae, 0xaf,
  0xb0, 0xb1, 0xb2, 0xb3, 0xb4, 0xb5, 0xb6, 0xb7,
  0xb8, 0xb9, 0xba, 0xbb, 0xbc, 0xbd, 0xbe, 0xbf,
  0xc0, 0xc1, 0xc2, 0xc3, 0xc4, 0xc5, 0xcc, 0xcd,
  0xce, 0xcf, 0xd0, 0xd1, 0xd2, 0xd3, 0xd4, 0xd5,
  0xd6, 0xd7, 0xd8, 0xd9, 0xda, 0xdb, 0xdc, 0xdd,
  0xde, 0xdf, 0xe0, 0xe1, 0xe2, 0xe3, 0xe4, 0xe5,
  0x00, 0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07,
  0x08, 0x09, 0x0a, 0x0b, 0x0c, 0x0d, 0x0e, 0x0f,
  0x10, 0x11, 0x12, 0x13, 0x14, 0x15, 0x16, 0x17,
  0x18, 0x19, 0x1a, 0x1b, 0x1c, 0x1d, 0x1e, 0x1f,
  0xc0, 0xc1, 0xc2, 0xc3, 0xc4, 0xc5, 0xc6, 0xc7,
  0xc8, 0xc9, 0xca, 0xcb, 0xcc, 0xcd, 0xce, 0xcf,
  0xd0, 0xd1, 0xd2, 0xd3, 0xd4, 0xd5, 0xd6, 0xd7,
  0xd8, 0xd9, 0xe0, 0xe1, 0xe2, 0xe3, 0xe4, 0xe5,
  0x00, 0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07,
  0x08, 0x09, 0x0a, 0x0b, 0x0c, 0x0d, 0x0e, 0x0f,
  0x10, 0x11, 0x12, 0x13, 0x1a, 0x1b, 0x1c, 0x1d,
  0x1e, 0x1f, 0x20, 0x21, 0x22, 0x23, 0x24, 0x25,
  0x26, 0x27, 0x28, 0x29, 0x2a, 0x2b, 0x2c, 0x2d,
  0x2e, 0x2f, 0x30, 0x31, 0x32, 0x33, 0x34, 0x35,
  0x36, 0x37, 0x38, 0x39, 0xda, 0xdb, 0xdc, 0xdd,
  0xde, 0xdf, 0xe0, 0xe1, 0xe2, 0xe3, 0xe4, 0xe5,
  0x00, 0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07,
  0x08, 0x09, 0x0a, 0x0b, 0x0c, 0x0d, 0x14, 0x15,
  0x16, 0x17, 0x18, 0x19, 0x1a, 0x1b, 0x1c, 0x1d,
  0x1e, 0x1f, 0x20, 0x21, 0x22, 0x23, 0x24, 0x25,
  0x26, 0x27, 0x28, 0x29, 0x2a, 0x2b, 0x2c, 0x2d,
  0x34, 0x35, 0x36, 0x37, 0x38, 0x39, 0x3a, 0x3b,
  0x3c, 0x3d, 0x3e, 0x3f, 0x40, 0x41, 0x42, 0x43,
  0x44, 0x45, 0x46, 0x47, 0x48, 0x49, 0x4a, 0x4b,
  0x00, 0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07,
  0xa8, 0xa9, 0xaa, 0xab, 0xac, 0xad, 0xae, 0xaf,
  0xb0, 0xb1, 0xb2, 0xb3, 0xb4, 0xb5, 0xb6, 0xb7,
  0xb8, 0xb9, 0xba, 0xbb, 0xbc, 0xbd, 0xbe, 0xbf,
  0xc0, 0xc1, 0xc8, 0xc9, 0xca, 0xcb, 0xcc, 0xcd,
  0xce, 0xcf, 0xd0, 0xd1, 0xd2, 0xd3, 0xd4, 0xd5,
  0xd6, 0xd7, 0xd8, 0xd9, 0xda, 0xdb, 0xdc, 0xdd,
  0xde, 0xdf, 0xe0, 0xe1, 0xe8, 0xe9, 0xea, 0xeb,
  0x00, 0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07,
  0x08, 0x09, 0x0a, 0x0b, 0x0c, 0x0d, 0x0e, 0x0f,
  0x10, 0x11, 0x12, 0x13, 0x14, 0x15, 0x16, 0x17,
  0x18, 0x19, 0x1a, 0x1b, 0xbc, 0xbd, 0xbe, 0xbf,
  0xc0, 0xc1, 0xc2, 0xc3, 0xc4, 0xc5, 0xc6, 0xc7,
  0xc8, 0xc9, 0xca, 0xcb, 0xcc, 0xcd, 0xce, 0xcf,
  0xd0, 0xd1, 0xd2, 0xd3, 0xd4, 0xd5, 0xdc, 0xdd,
  0xde, 0xdf, 0xe0, 0xe1, 0xe2, 0xe3, 0xe4, 0xe5,
  0x00, 0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07,
  0x08, 0x09, 0x0a, 0x0b, 0x0c, 0x0d, 0x0e, 0x0f,
  0x16, 0x17, 0x18, 0x19, 0x1a, 0x1b, 0x1c, 0x1d,
  0x1e, 0x1f, 0x20, 0x21, 0x22, 0x23, 0x24, 0x25,
  0x26, 0x27, 0x28, 0x29, 0x2a, 0x2b, 0x2c, 0x2d,
  0x2e, 0x2f, 0x30, 0x31, 0x32, 0x33, 0x34, 0x35,
  0xd6, 0xd7, 0xd8, 0xd9, 0xda, 0xdb, 0xdc, 0xdd,
  0xde, 0xdf, 0xe0, 0xe1, 0xe2, 0xe3, 0xe4, 0xe5,
  0x00, 0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07,
  0x08, 0x09, 0x10, 0x11, 0x12, 0x13, 0x14, 0x15,
  0x16, 0x17, 0x18, 0x19, 0x1a, 0x1b, 0x1c, 0x1d,
  0x1e, 0x1f, 0x20, 0x21, 0x22, 0x23, 0x24, 0x25,
  0x26, 0x27, 0x28, 0x29, 0x30, 0x31, 0x32, 0x33,
  0x34, 0x35, 0x36, 0x37, 0x38, 0x39, 0x3a, 0x3b,
  0x3c, 0x3d, 0x3e, 0x3f, 0x40, 0x41, 0x42, 0x43,
  0x44, 0x45, 0x46, 0x47, 0x48, 0x49, 0x4a, 0x4b,
  0x00, 0x01, 0x02, 0x03, 0xa4, 0xa5, 0xa6, 0xa7,
  0xa8, 0xa9, 0xaa, 0xab, 0xac, 0xad, 0xae, 0xaf,
  0xb0, 0xb1, 0xb2, 0xb3, 0xb4, 0xb5, 0xb6, 0xb7,
  0xb8, 0xb9, 0xba, 0xbb, 0xbc, 0xbd, 0xc4, 0xc5,
  0xc6, 0xc7, 0xc8, 0xc9, 0xca, 0xcb, 0xcc, 0xcd,
  0xce, 0xcf, 0xd0, 0xd1, 0xd2, 0xd3, 0xd4, 0xd5,
  0xd6, 0xd7, 0xd8, 0xd9, 0xda, 0xdb, 0xdc, 0xdd,
  0xe4, 0xe5, 0xe6, 0xe7, 0xe8, 0xe9, 0xea, 0xeb,
  0x00, 0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07,
  0x08, 0x09, 0x0a, 0x0b, 0x0c, 0x0d, 0x0e, 0x0f,
  0x10, 0x11, 0x12, 0x13, 0x14, 0x15, 0x16, 0x17,
  0xb8, 0xb9, 0xba, 0xbb, 0xbc, 0xbd, 0xbe, 0xbf,
  0xc0, 0xc1, 0xc2, 0xc3, 0xc4, 0xc5, 0xc6, 0xc7,
  0xc8, 0xc9, 0xca, 0xcb, 0xcc, 0xcd, 0xce, 0xcf,
  0xd0, 0xd1, 0xd8, 0xd9, 0xda, 0xdb, 0xdc, 0xdd,
  0xde, 0xdf, 0xe0, 0xe1, 0xe2, 0xe3, 0xe4, 0xe5,
  0x00, 0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07,
  0x08, 0x09, 0x0a, 0x0b, 0x12, 0x13, 0x14, 0x15,
  0x16, 0x17, 0x18, 0x19, 0x1a, 0x1b, 0x1c, 0x1d,
  0x1e, 0x1f, 0x20, 0x21, 0x22, 0x23, 0x24, 0x25,
  0x26, 0x27, 0x28, 0x29, 0x2a, 0x2b, 0x2c, 0x2d,
  0x2e, 0x2f, 0x30, 0x31, 0xd2, 0xd3, 0xd4, 0xd5,
  0xd6, 0xd7, 0xd8, 0xd9, 0xda, 0xdb, 0xdc, 0xdd,
  0xde, 0xdf, 0xe0, 0xe1, 0xe2, 0xe3, 0xe4, 0xe5,
  0x00, 0x01, 0x02, 0x03, 0x04, 0x05, 0x0c, 0x0d,
  0x0e, 0x0f, 0x10, 0x11, 0x12, 0x13, 0x14, 0x15,
  0x16, 0x17, 0x18, 0x19, 0x1a, 0x1b, 0x1c, 0x1d,
  0x1e, 0x1f, 0x20, 0x21, 0x22, 0x23, 0x24, 0x25,
  0x2c, 0x2d, 0x2e, 0x2f, 0x30, 0x31, 0x32, 0x33,
  0x34, 0x35, 0x36, 0x37, 0x38, 0x39, 0x3a, 0x3b,
  0x3c, 0x3d, 0x3e, 0x3f, 0x40, 0x41, 0x42, 0x43,
  0x44, 0x45, 0x46, 0x47, 0x48, 0x49, 0x4a, 0x4b,
  0x00, 0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07,
  0x08, 0x09, 0x0a, 0x0b, 0x0c, 0x0d, 0x0e, 0x0f,
  0x10, 0x11, 0x12, 0x13, 0x14, 0x15, 0x16, 0x17,
  0x18, 0x19, 0x20, 0x21, 0x22, 0x23, 0x24, 0x25,
  0x26, 0x27, 0x28, 0x29, 0x2a, 0x2b, 0x2c, 0x2d,
  0x2e, 0x2f, 0x30, 0x31, 0x32, 0x33, 0x34, 0x35,
  0x36, 0x37, 0x38, 0x39, 0x40, 0x41, 0x42, 0x43,
  0x44, 0x45, 0x46, 0x47, 0x48, 0x49, 0x4a, 0x4b,
  0x00, 0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07,
  0x08, 0x09, 0x0a, 0x0b, 0x0c, 0x0d, 0x0e, 0x0f,
  0x10, 0x11, 0x12, 0x13, 0xb4, 0xb5, 0xb6, 0xb7,
  0xb8, 0xb9, 0xba, 0xbb, 0xbc, 0xbd, 0xbe, 0xbf,
  0xc0, 0xc1, 0xc2, 0xc3, 0xc4, 0xc5, 0xc6, 0xc7,
  0xc8, 0xc9, 0xca, 0xcb, 0xcc, 0xcd, 0xd4, 0xd5,
  0xd6, 0xd7, 0xd8, 0xd9, 0xda, 0xdb, 0xdc, 0xdd,
  0xde, 0xdf, 0xe0, 0xe1, 0xe2, 0xe3, 0xe4, 0xe5,
  0x00, 0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07,
  0x0e, 0x0f, 0x10, 0x11, 0x12, 0x13, 0x14, 0x15,
  0x16, 0x17, 0x18, 0x19, 0x1a, 0x1b, 0x1c, 0x1d,
  0x1e, 0x1f, 0x20, 0x21, 0x22, 0x23, 0x24, 0x25,
  0x26, 0x27, 0x28, 0x29, 0x2a, 0x2b, 0x2c, 0x2d,
  0xce, 0xcf, 0xd0, 0xd1, 0xd2, 0xd3, 0xd4, 0xd5,
  0xd6, 0xd7, 0xd8, 0xd9, 0xda, 0xdb, 0xdc, 0xdd,
  0xde, 0xdf, 0xe0, 0xe1, 0xe2, 0xe3, 0xe4, 0xe5,
  0x00, 0x01, 0x08, 0x09, 0x0a, 0x0b, 0x0c, 0x0d,
  0x0e, 0x0f, 0x10, 0x11, 0x12, 0x13, 0x14, 0x15,
  0x16, 0x17, 0x18, 0x19, 0x1a, 0x1b, 0x1c, 0x1d,
  0x1e, 0x1f, 0x20, 0x21, 0x28, 0x29, 0x2a, 0x2b,
  0x2c, 0x2d, 0x2e, 0x2f, 0x30, 0x31, 0x32, 0x33,
  0x34, 0x35, 0x36, 0x37, 0x38, 0x39, 0x3a, 0x3b,
  0x3c, 0x3d, 0x3e, 0x3f, 0x40, 0x41, 0x42, 0x43,
  0x44, 0x45, 0x46, 0x47, 0xe8, 0xe9, 0xea, 0xeb,
  0x00, 0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07,
  0x08, 0x09, 0x0a, 0x0b, 0x0c, 0x0d, 0x0e, 0x0f,
  0x10, 0x11, 0x12, 0x13, 0x14, 0x15, 0x1c, 0x1d,
  0x1e, 0x1f, 0x20, 0x21, 0x22, 0x23, 0x24, 0x25,
  0x26, 0x27, 0x28, 0x29, 0x2a, 0x2b, 0x2c, 0x2d,
  0x2e, 0x2f, 0x30, 0x31, 0x32, 0x33, 0x34, 0x35,
  0x3c, 0x3d, 0x3e, 0x3f, 0x40, 0x41, 0x42, 0x43,
  0x44, 0x45, 0x46, 0x47, 0x48, 0x49, 0x4a, 0x4b,
  0x00, 0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07,
  0x08, 0x09, 0x0a, 0x0b, 0x0c, 0x0d, 0x0e, 0x0f,
  0xb0, 0xb1, 0xb2, 0xb3, 0xb4, 0xb5, 0xb6, 0xb7,
  0xb8, 0xb9, 0xba, 0xbb, 0xbc, 0xbd, 0xbe, 0xbf,
  0xc0, 0xc1, 0xc2, 0xc3, 0xc4, 0xc5, 0xc6, 0xc7,
  0xc8, 0xc9, 0xd0, 0xd1, 0xd2, 0xd3, 0xd4, 0xd5,
  0xd6, 0xd7, 0xd8, 0xd9, 0xda, 0xdb, 0xdc, 0xdd,
  0xde, 0xdf, 0xe0, 0xe1, 0xe2, 0xe3, 0xe4, 0xe5,
  0x00, 0x01, 0x02, 0x03, 0x0a, 0x0b, 0x0c, 0x0d,
  0x0e, 0x0f, 0x10, 0x11, 0x12, 0x13, 0x14, 0x15,
  0x16, 0x17, 0x18, 0x19, 0x1a, 0x1b, 0x1c, 0x1d,
  0x1e, 0x1f, 0x20, 0x21, 0x22, 0x23, 0x24, 0x25,
  0x26, 0x27, 0x28, 0x29, 0xca, 0xcb, 0xcc, 0xcd,
  0xce, 0xcf, 0xd0, 0xd1, 0xd2, 0xd3, 0xd4, 0xd5,
  0xd6, 0xd7, 0xd8, 0xd9, 0xda, 0xdb, 0xdc, 0xdd,
  0xde, 0xdf, 0xe0, 0xe1, 0xe2, 0xe3, 0xea, 0xeb,
  0x00, 0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07,
  0x08, 0x09, 0x0a, 0x0b, 0x0c, 0x0d, 0x0e, 0x0f,
  0x10, 0x11, 0x12, 0x13, 0x14, 0x15, 0x16, 0x17,
  0x1e, 0x1f, 0x20, 0x21, 0x22, 0x23, 0x24, 0x25,
  0x26, 0x27, 0x28, 0x29, 0x2a, 0x2b, 0x2c, 0x2d,
  0x2e, 0x2f, 0x30, 0x31, 0x32, 0x33, 0x34, 0x35,
  0x36, 0x37, 0x38, 0x39, 0x3a, 0x3b, 0x3c, 0x3d,
  0xde, 0xdf, 0xe0, 0xe1, 0xe2, 0xe3, 0xe4, 0xe5,
  0x00, 0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07,
  0x08, 0x09, 0x0a, 0x0b, 0x0c, 0x0d, 0x0e, 0x0f,
  0x10, 0x11, 0x18, 0x19, 0x1a, 0x1b, 0x1c, 0x1d,
  0x1e, 0x1f, 0x20, 0x21, 0x22, 0x23, 0x24, 0x25,
  0x26, 0x27, 0x28, 0x29, 0x2a, 0x2b, 0x2c, 0x2d,
  0x2e, 0x2f, 0x30, 0x31, 0x38, 0x39, 0x3a, 0x3b,
  0x3c, 0x3d, 0x3e, 0x3f, 0x40, 0x41, 0x42, 0x43,
  0x44, 0x45, 0x46, 0x47, 0x48, 0x49, 0x4a, 0x4b,
  0x00, 0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07,
  0x08, 0x09, 0x0a, 0x0b, 0xac, 0xad, 0xae, 0xaf,
  0xb0, 0xb1, 0xb2, 0xb3, 0xb4, 0xb5, 0xb6, 0xb7,
  0xb8, 0xb9, 0xba, 0xbb, 0xbc, 0xbd, 0xbe, 0xbf,
  0xc0, 0xc1, 0xc2, 0xc3, 0xc4, 0xc5, 0xcc, 0xcd,
  0xce, 0xcf, 0xd0, 0xd1, 0xd2, 0xd3, 0xd4, 0xd5,
  0xd6, 0xd7, 0xd8, 0xd9, 0xda, 0xdb, 0xdc, 0xdd,
  0xde, 0xdf, 0xe0, 0xe1, 0xe2, 0xe3, 0xe4, 0xe5,
  0x00, 0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07,
  0x08, 0x09, 0x0a, 0x0b, 0x0c, 0x0d, 0x0e, 0x0f,
  0x10, 0x11, 0x12, 0x13, 0x14, 0x15, 0x16, 0x17,
  0x18, 0x19, 0x1a, 0x1b, 0x1c, 0x1d, 0x1e, 0x1f,
  0xc0, 0xc1, 0xc2, 0xc3, 0xc4, 0xc5, 0xc6, 0xc7,
  0xc8, 0xc9, 0xca, 0xcb, 0xcc, 0xcd, 0xce, 0xcf,
  0xd0, 0xd1, 0xd2, 0xd3, 0xd4, 0xd5, 0xd6, 0xd7,
  0xd8, 0xd9, 0xe0, 0xe1, 0xe2, 0xe3, 0xe4, 0xe5,
  0x00, 0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07,
  0x08, 0x09, 0x0a, 0x0b, 0x0c, 0x0d, 0x0e, 0x0f,
  0x10, 0x11, 0x12, 0x13, 0x1a, 0x1b, 0x1c, 0x1d,
  0x1e, 0x1f, 0x20, 0x21, 0x22, 0x23, 0x24, 0x25,
  0x26, 0x27, 0x28, 0x29, 0x2a, 0x2b, 0x2c, 0x2d,
  0x2e, 0x2f, 0x30, 0x31, 0x32, 0x33, 0x34, 0x35,
  0x36, 0x37, 0x38, 0x39, 0xda, 0xdb, 0xdc, 0xdd,
  0xde, 0xdf, 0xe0, 0xe1, 0xe2, 0xe3, 0xe4, 0xe5,
  0x00, 0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07,
  0x08, 0x09, 0x0a, 0x0b, 0x0c, 0x0d, 0x14, 0x15,
  0x16, 0x17, 0x18, 0x19, 0x1a, 0x1b, 0x1c, 0x1d,
  0x1e, 0x1f, 0x20, 0x21, 0x22, 0x23, 0x24, 0x25,
  0x26, 0x27, 0x28, 0x29, 0x2a, 0x2b, 0x2c, 0x2d,
  0x34, 0x35, 0x36, 0x37, 0x38, 0x39, 0x3a, 0x3b,
  0x3c, 0x3d, 0x3e, 0x3f, 0x40, 0x41, 0x42, 0x43,
  0x44, 0x45, 0x46, 0x47, 0x48, 0x49, 0x4a, 0x4b,
  0x00, 0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07,
  0xa8, 0xa9, 0xaa, 0xab, 0xac, 0xad, 0xae, 0xaf,
  0xb0, 0xb1, 0xb2, 0xb3, 0xb4, 0xb5, 0xb6, 0xb7,
  0xb8, 0xb9, 0xba, 0xbb, 0xbc, 0xbd, 0xbe, 0xbf,
  0xc0, 0xc1, 0xc8, 0xc9, 0xca, 0xcb, 0xcc, 0xcd,
  0xce, 0xcf, 0xd0, 0xd1, 0xd2, 0xd3, 0xd4, 0xd5,
  0xd6, 0xd7, 0xd8, 0xd9, 0xda, 0xdb, 0xdc, 0xdd,
  0xde, 0xdf, 0xe0, 0xe1, 0xe8, 0xe9, 0xea, 0xeb,
  0x00, 0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07,
  0x08, 0x09, 0x0a, 0x0b, 0x0c, 0x0d, 0x0e, 0x0f,
  0x10, 0x11, 0x12, 0x13, 0x14, 0x15, 0x16, 0x17,
  0x18, 0x19, 0x1a, 0x1b, 0xbc, 0xbd, 0xbe, 0xbf,
  0xc0, 0xc1, 0xc2, 0xc3, 0xc4, 0xc5, 0xc6, 0xc7,
  0xc8, 0xc9, 0xca, 0xcb, 0xcc, 0xcd, 0xce, 0xcf,
  0xd0, 0xd1, 0xd2, 0xd3, 0xd4, 0xd5, 0xdc, 0xdd,
  0xde, 0xdf, 0xe0, 0xe1, 0xe2, 0xe3, 0xe4, 0xe5,
  0x00, 0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07,
  0x08, 0x09, 0x0a, 0x0b, 0x0c, 0x0d, 0x0e, 0x0f,
  0x16, 0x17, 0x18, 0x19, 0x1a, 0x1b, 0x1c, 0x1d,
  0x1e, 0x1f, 0x20, 0x21, 0x22, 0x23, 0x24, 0x25,
  0x26, 0x27, 0x28, 0x29, 0x2a, 0x2b, 0x2c, 0x2d,
  0x2e, 0x2f, 0x30, 0x31, 0x32, 0x33, 0x34, 0x35,
  0xd6, 0xd7, 0xd8, 0xd9, 0xda, 0xdb, 0xdc, 0xdd,
  0xde, 0xdf, 0xe0, 0xe1, 0xe2, 0xe3, 0xe4, 0xe5,
  0x00, 0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07,
  0x08, 0x09, 0x10, 0x11, 0x12, 0x13, 0x14, 0x15,
  0x16, 0x17, 0x18, 0x19, 0x1a, 0x1b, 0x1c, 0x1d,
  0x1e, 0x1f, 0x20, 0x21, 0x22, 0x23, 0x24, 0x25,
  0x26, 0x27, 0x28, 0x29, 0x30, 0x31, 0x32, 0x33,
  0x34, 0x35, 0x36, 0x37, 0x38, 0x39, 0x3a, 0x3b,
  0x3c, 0x3d, 0x3e, 0x3f, 0x40, 0x41, 0x42, 0x43,
  0x44, 0x45, 0x46, 0x47, 0x48, 0x49, 0x4a, 0x4b,
  0x00, 0x01, 0x02, 0x03, 0xa4, 0xa5, 0xa6, 0xa7,
  0xa8, 0xa9, 0xaa, 0xab, 0xac, 0xad, 0xae, 0xaf,
  0xb0, 0xb1, 0xb2, 0xb3, 0xb4, 0xb5, 0xb6, 0xb7,
  0xb8, 0xb9, 0xba, 0xbb, 0xbc, 0xbd, 0xc4, 0xc5,
  0xc6, 0xc7, 0xc8, 0xc9, 0xca, 0xcb, 0xcc, 0xcd,
  0xce, 0xcf, 0xd0, 0xd1, 0xd2, 0xd3, 0xd4, 0xd5,
  0xd6, 0xd7, 0xd8, 0xd9, 0xda, 0xdb, 0xdc, 0xdd,
  0xe4, 0xe5, 0xe6, 0xe7, 0xe8, 0xe9, 0xea, 0xeb,
  0x00, 0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07,
  0x08, 0x09, 0x0a, 0x0b, 0x0c, 0x0d, 0x0e, 0x0f,
  0x10, 0x11, 0x12, 0x13, 0x14, 0x15, 0x16, 0x17,
  0xb8, 0xb9, 0xba, 0xbb, 0xbc, 0xbd, 0xbe, 0xbf,
  0xc0, 0xc1, 0xc2, 0xc3, 0xc4, 0xc5, 0xc6, 0xc7,
  0xc8, 0xc9, 0xca, 0xcb, 0xcc, 0xcd, 0xce, 0xcf,
  0xd0, 0xd1, 0xd8, 0xd9, 0xda, 0xdb, 0xdc, 0xdd,
  0xde, 0xdf, 0xe0, 0xe1, 0xe2, 0xe3, 0xe4, 0xe5,
  0x00, 0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07,
  0x08, 0x09, 0x0a, 0x0b, 0x12, 0x13, 0x14, 0x15,
  0x16, 0x17, 0x18, 0x19, 0x1a, 0x1b, 0x1c, 0x1d,
  0x1e, 0x1f, 0x20, 0x21, 0x22, 0x23, 0x24, 0x25,
  0x26, 0x27, 0x28, 0x29, 0x2a, 0x2b, 0x2c, 0x2d,
  0x2e, 0x2f, 0x30, 0x31, 0xd2, 0xd3, 0xd4, 0xd5,
  0xd6, 0xd7, 0xd8, 0xd9, 0xda, 0xdb, 0xdc, 0xdd,
  0xde, 0xdf, 0xe0, 0xe1, 0xe2, 0xe3, 0xe4, 0xe5,
  0x00, 0x01, 0x02, 0x03, 0x04, 0x05, 0x0c, 0x0d,
  0x0e, 0x0f, 0x10, 0x11, 0x12, 0x13, 0x14, 0x15,
  0x16, 0x17, 0x18, 0x19, 0x1a, 0x1b, 0x1c, 0x1d,
  0x1e, 0x1f, 0x20, 0x21, 0x22, 0x23, 0x24, 0x25,
  0x2c, 0x2d, 0x2e, 0x2f, 0x30, 0x31, 0x32, 0x33,
  0x34, 0x35, 0x36, 0x37, 0x38, 0x39, 0x3a, 0x3b,
  0x3c, 0x3d, 0x3e, 0x3f, 0x40, 0x41, 0x42, 0x43,
  0x44, 0x45, 0x46, 0x47, 0x48, 0x49, 0x4a, 0x4b,
  0x00, 0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07,
  0x08, 0x09, 0x0a, 0x0b, 0x0c, 0x0d, 0x0e, 0x0f,
  0x10, 0x11, 0x12, 0x13, 0x14, 0x15, 0x16, 0x17,
  0x18, 0x19, 0x20, 0x21, 0x22, 0x23, 0x24, 0x25,
  0x26, 0x27, 0x28, 0x29, 0x2a, 0x2b, 0x2c, 0x2d,
  0x2e, 0x2f, 0x30, 0x31, 0x32, 0x33, 0x34, 0x35,
  0x36, 0x37, 0x38, 0x39, 0x40, 0x41, 0x42, 0x43,
  0x44, 0x45, 0x46, 0x47, 0x48, 0x49, 0x4a, 0x4b,
  0x00, 0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07,
  0x08, 0x09, 0x0a, 0x0b, 0x0c, 0x0d, 0x0e, 0x0f,
  0x10, 0x11, 0x12, 0x13, 0xb4, 0xb5, 0xb6, 0xb7,
  0xb8, 0xb9, 0xba, 0xbb, 0xbc, 0xbd, 0xbe, 0xbf,
  0xc0, 0xc1, 0xc2, 0xc3, 0xc4, 0xc5, 0xc6, 0xc7,
  0xc8, 0xc9, 0xca, 0xcb, 0xcc, 0xcd, 0xd4, 0xd5,
  0xd6, 0xd7, 0xd8, 0xd9, 0xda, 0xdb, 0xdc, 0xdd,
  0xde, 0xdf, 0xe0, 0xe1, 0xe2, 0xe3, 0xe4, 0xe5,
  0x00, 0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07,
  0x0e, 0x0f, 0x10, 0x11, 0x12, 0x13, 0x14, 0x15,
  0x16, 0x17, 0x18, 0x19, 0x1a, 0x1b, 0x1c, 0x1d,
  0x1e, 0x1f, 0x20, 0x21, 0x22, 0x23, 0x24, 0x25,
  0x26, 0x27, 0x28, 0x29, 0x2a, 0x2b, 0x2c, 0x2d,
  0xce, 0xcf, 0xd0, 0xd1, 0xd2, 0xd3, 0xd4, 0xd5,
  0xd6, 0xd7, 0xd8, 0xd9, 0xda, 0xdb, 0xdc, 0xdd,
  0xde, 0xdf, 0xe0, 0xe1, 0xe2, 0xe3, 0xe4, 0xe5,
  0x00, 0x01, 0x08, 0x09, 0x0a, 0x0b, 0x0c, 0x0d,
  0x0e, 0x0f, 0x10, 0x11, 0x12, 0x13, 0x14, 0x15,
  0x16, 0x17, 0x18, 0x19, 0x1a, 0x1b, 0x1c, 0x1d,
  0x1e, 0x1f, 0x20, 0x21, 0x28, 0x29, 0x2a, 0x2b,
  0x2c, 0x2d, 0x2e, 0x2f, 0x30, 0x31, 0x32, 0x33,
  0x34, 0x35, 0x36, 0x37, 0x38, 0x39, 0x3a, 0x3b,
  0x3c, 0x3d, 0x3e, 0x3f, 0x40, 0x41, 0x42, 0x43,
  0x44, 0x45, 0x46, 0x47, 0xe8, 0xe9, 0xea, 0xeb,
  0x00, 0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07,
  0x08, 0x09, 0x0a, 0x0b, 0x0c, 0x0d, 0x0e, 0x0f,
  0x10, 0x11, 0x12, 0x13, 0x14, 0x15, 0x1c, 0x1d,
  0x1e, 0x1f, 0x20, 0x21, 0x22, 0x23, 0x24, 0x25,
  0x26, 0x27, 0x28, 0x29, 0x2a, 0x2b, 0x2c, 0x2d,
  0x2e, 0x2f, 0x30, 0x31, 0x32, 0x33, 0x34, 0x35,
  0x3c, 0x3d, 0x3e, 0x3f, 0x40, 0x41, 0x42, 0x43,
  0x44, 0x45, 0x46, 0x47, 0x48, 0x49, 0x4a, 0x4b,
  0x00, 0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07,
  0x08, 0x09, 0x0a, 0x0b, 0x0c, 0x0d, 0x0e, 0x0f,
  0xb0, 0xb1, 0xb2, 0xb3, 0xb4, 0xb5, 0xb6, 0xb7,
  0xb8, 0xb9, 0xba, 0xbb, 0xbc, 0xbd, 0xbe, 0xbf,
  0xc0, 0xc1, 0xc2, 0xc3, 0xc4, 0xc5, 0xc6, 0xc7,
  0xc8, 0xc9, 0xd0, 0xd1, 0xd2, 0xd3, 0xd4, 0xd5,
  0xd6, 0xd7, 0xd8, 0xd9, 0xda, 0xdb, 0xdc, 0xdd,
  0xde, 0xdf, 0xe0, 0xe1, 0xe2, 0xe3, 0xe4, 0xe5,
  0x00, 0x01, 0x02, 0x03, 0x0a, 0x0b, 0x0c, 0x0d,
  0x0e, 0x0f, 0x10, 0x11, 0x12, 0x13, 0x14, 0x15,
  0x16, 0x17, 0x18, 0x19, 0x1a, 0x1b, 0x1c, 0x1d,
  0x1e, 0x1f, 0x20, 0x21, 0x22, 0x23, 0x24, 0x25,
  0x26, 0x27, 0x28, 0x29, 0xca, 0xcb, 0xcc, 0xcd,
  0xce, 0xcf, 0xd0, 0xd1, 0xd2, 0xd3, 0xd4, 0xd5,
  0xd6, 0xd7, 0xd8, 0xd9, 0xda, 0xdb,
};

static const Summary16 uhc_2_uni2indx_pagec8[251] = {
  /* 0xc800 */
  {    0, 0x0000 }, {    0, 0x0000 }, {    0, 0x0000 }, {    0, 0x0000 },
  {    0, 0x0000 }, {    0, 0x0000 }, {    0, 0x0000 }, {    0, 0x0000 },
  {    0, 0x0000 }, {    0, 0x0000 }, {    0, 0xfee0 }, {   10, 0xcfff },
  {   24, 0xeeef }, {   37, 0xfd4f }, {   49, 0xffec }, {   62, 0xcfdf },
  /* 0xc900 */
  {   75, 0x4f8e }, {   84, 0xfefd }, {   98, 0xefff }, {  113, 0xffef },
  {  128, 0xffff }, {  144, 0xfeec }, {  156, 0xeff4 }, {  168, 0xefee },
  {  181, 0xecff }, {  194, 0xd4fe }, {  205, 0xffff }, {  221, 0xffff },
  {  237, 0xfa6c }, {  247, 0xc994 }, {  254, 0x4f6a }, {  263, 0xecfc },
  /* 0xca00 */
  {  274, 0xc4fe }, {  284, 0xfeef }, {  298, 0xfdff }, {  313, 0xffff },
  {  329, 0xcfff }, {  343, 0x4fee }, {  354, 0xfefc }, {  367, 0xdfff },
  {  382, 0xffef }, {  397, 0xfeff }, {  412, 0xffff }, {  428, 0xcfff },
  {  442, 0x4fee }, {  453, 0xfcf5 }, {  465, 0xeffe }, {  479, 0xffef },
  /* 0xcb00 */
  {  494, 0xfeff }, {  509, 0xfeee }, {  522, 0xfffc }, {  536, 0xffff },
  {  552, 0xecfd }, {  564, 0xdcfe }, {  576, 0xffef }, {  591, 0xfcff },
  {  605, 0xffff }, {  621, 0xefff }, {  636, 0xffff }, {  652, 0xfeff },
  {  667, 0xffff }, {  683, 0xffef }, {  698, 0xfd6f }, {  711, 0xffff },
  /* 0xcc00 */
  {  727, 0xcfff }, {  741, 0xcfee }, {  753, 0xac79 }, {  762, 0x84fe },
  {  771, 0xeecf }, {  783, 0xfc4f }, {  794, 0xfeae }, {  806, 0xffde },
  {  820, 0xffff }, {  836, 0xecff }, {  849, 0xc4fe }, {  859, 0xeecf },
  {  871, 0xfd4f }, {  883, 0xffee }, {  897, 0xefef }, {  911, 0xfffe },
  /* 0xcd00 */
  {  926, 0xecfd }, {  938, 0xd4fe }, {  949, 0xeeef }, {  962, 0xfdff },
  {  977, 0xffff }, {  993, 0xefff }, { 1008, 0x4fee }, { 1019, 0xfefd },
  { 1033, 0xfeff }, { 1048, 0xeecf }, { 1060, 0xfd4f }, { 1072, 0xfffe },
  { 1087, 0xefef }, { 1101, 0xfffe }, { 1116, 0xeeff }, { 1130, 0xd4fe },
  /* 0xce00 */
  { 1141, 0xeeef }, { 1154, 0xfdef }, { 1168, 0xfeec }, { 1180, 0xffd4 },
  { 1192, 0xffff }, { 1208, 0x6cff }, { 1220, 0xd4fc }, { 1230, 0xeecf },
  { 1242, 0xfd4f }, { 1254, 0xfeec }, { 1266, 0xcfc4 }, { 1275, 0xffff },
  { 1291, 0xfffd }, { 1306, 0xffff }, { 1322, 0xe6cf }, { 1333, 0xfc4f },
  /* 0xcf00 */
  { 1344, 0xfeec }, { 1356, 0xefd4 }, { 1367, 0x4fee }, { 1378, 0xfefc },
  { 1391, 0xffff }, { 1407, 0xeecf }, { 1419, 0xfd4f }, { 1431, 0xfeec },
  { 1443, 0xefde }, { 1456, 0xffff }, { 1472, 0xfefd }, { 1486, 0xfffe },
  { 1501, 0xffef }, { 1516, 0xffff }, { 1532, 0xfeec }, { 1544, 0xefd4 },
  /* 0xd000 */
  { 1555, 0xffee }, { 1569, 0xfefd }, { 1583, 0xdfff }, { 1598, 0xeecf },
  { 1610, 0xfd4f }, { 1622, 0xfeee }, { 1635, 0xcffe }, { 1648, 0xcfee },
  { 1660, 0xfffd }, { 1675, 0xffff }, { 1691, 0xeecf }, { 1703, 0xfd4f },
  { 1715, 0xfcec }, { 1726, 0xcfc4 }, { 1735, 0x4fee }, { 1746, 0xfefc },
  /* 0xd100 */
  { 1759, 0xdfff }, { 1774, 0xffff }, { 1790, 0xffff }, { 1806, 0xfaec },
  { 1817, 0xcfc4 }, { 1826, 0x4fee }, { 1837, 0xeefd }, { 1850, 0xefff },
  { 1865, 0xfeef }, { 1879, 0xffff }, { 1895, 0xfeec }, { 1907, 0xebd4 },
  { 1917, 0xfffe }, { 1932, 0xfeff }, { 1947, 0xffff }, { 1963, 0xfeef },
  /* 0xd200 */
  { 1977, 0xfd7f }, { 1991, 0xfffe }, { 2006, 0xcfff }, { 2020, 0x4fee },
  { 2031, 0xfefd }, { 2045, 0xefff }, { 2060, 0xffef }, { 2075, 0xffff },
  { 2091, 0xfeec }, { 2103, 0xefdc }, { 2115, 0xefee }, { 2128, 0x6cfd },
  { 2139, 0xf4fa }, { 2150, 0xeeef }, { 2163, 0xffcf }, { 2177, 0xfeec },
  /* 0xd300 */
  { 2189, 0x8fd4 }, { 2198, 0x4fae }, { 2208, 0xecdc }, { 2218, 0xc4fe },
  { 2228, 0xffcf }, { 2242, 0xffff }, { 2258, 0xffff }, { 2274, 0xcfff },
  { 2288, 0x4fee }, { 2299, 0xecfc }, { 2310, 0xd4fe }, { 2321, 0xeeef },
  { 2334, 0xfccf }, { 2346, 0xfefe }, { 2360, 0xcff5 }, { 2372, 0x4fee },
  /* 0xd400 */
  { 2383, 0xfefd }, { 2397, 0xdfff }, { 2412, 0xffff }, { 2428, 0xffff },
  { 2444, 0xffee }, { 2458, 0xefff }, { 2473, 0x5fee }, { 2485, 0x6cff },
  { 2497, 0xd4fa }, { 2507, 0xffef }, { 2522, 0xfdff }, { 2537, 0xffff },
  { 2553, 0xefff }, { 2568, 0x6fee }, { 2580, 0xeeff }, { 2594, 0xd6fe },
  /* 0xd500 */
  { 2606, 0xeeef }, { 2619, 0xff4f }, { 2632, 0xffff }, { 2648, 0xcfff },
  { 2662, 0x4fee }, { 2673, 0xecfd }, { 2685, 0xd4de }, { 2695, 0xeecf },
  { 2707, 0xfc4f }, { 2718, 0xfffe }, { 2733, 0xffdf }, { 2748, 0xffff },
  { 2764, 0xecff }, { 2777, 0xd4fa }, { 2787, 0xeecf }, { 2799, 0xfd4f },
  /* 0xd600 */
  { 2811, 0xfeec }, { 2823, 0xefc4 }, { 2833, 0xdfee }, { 2846, 0xecff },
  { 2859, 0xd4de }, { 2869, 0xeecd }, { 2880, 0xfd7f }, { 2894, 0xffec },
  { 2907, 0xcfd7 }, { 2919, 0x5fee }, { 2931, 0xeefd }, { 2944, 0xf5fe },
  { 2957, 0xeecf }, { 2969, 0xfd6d }, { 2981, 0xfeee }, { 2994, 0xcfde },
  /* 0xd700 */
  { 3006, 0xffee }, { 3020, 0xecfd }, { 3032, 0xd4fe }, { 3043, 0xeecf },
  { 3055, 0xfd6f }, { 3068, 0xfc2c }, { 3077, 0xedd4 }, { 3087, 0xcfee },
  { 3099, 0xecfd }, { 3111, 0xd4fe }, { 3122, 0x000f },
};

static int
uhc_2_wctomb (conv_t conv, unsigned char *r, ucs4_t wc, int n)
{
  if (n >= 2) {
    if (wc >= 0xc800 && wc < 0xd7b0) {
      const Summary16 *summary = &uhc_2_uni2indx_pagec8[(wc>>4)-0xc80];
      unsigned short used = summary->used;
      unsigned int i = wc & 0x0f;
      if (used & ((unsigned short) 1 << i)) {
        unsigned short c;
        /* Keep in `used' only the bits 0..i-1. */
        used &= ((unsigned short) 1 << i) - 1;
        /* Add `summary->indx' and the number of bits set in `used'. */
        used = (used & 0x5555) + ((used & 0xaaaa) >> 1);
        used = (used & 0x3333) + ((used & 0xcccc) >> 2);
        used = (used & 0x0f0f) + ((used & 0xf0f0) >> 4);
        used = (used & 0x00ff) + (used >> 8);
        used += summary->indx;
        c = uhc_2_2charset_main[used>>6] + uhc_2_2charset[used];
        r[0] = (c >> 8); r[1] = (c & 0xff);
        return 2;
      }
    }
    return RET_ILUNI;
  }
  return RET_TOOSMALL;
}
