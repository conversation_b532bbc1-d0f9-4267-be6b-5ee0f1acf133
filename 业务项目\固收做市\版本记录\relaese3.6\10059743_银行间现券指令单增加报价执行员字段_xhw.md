# 1. 功能设计
## 1.1. 接口变更
### 1.1.1. 现券语料解析接口--api/cashBond/parseSyntax
增加报价执行人字段返回
### 1.1.2. 现券下单接口--api/cashBond/addTradingOrder
 增加报价执行人入参
### 1.1.3. 现券导出接口--api/cashBond/exportOrderBondExcel
 Excel导出文件新增报价执行人返回
## 1.2. 定时任务变更
### 1.2.1. 指令同步-TradingOrderSyncAllHandler

### 1.2.2. 历史指令同步-TradingOrderSyncAllHisHandler

### 1.2.3. 现券指令同步-TradingOrderBondSyncHandler

# 2. 涉及的数据库、Redis、Kafka 等中间件的修改/新增图或者说明
```sql
alter table ptms.ficc_trading_order_bond  
    add cfetsExecutetrader varchar(128) null comment '报价执行人';

alter table ptms.ficc_trading_order_all  
    add cfetsExecutetrader varchar(128) null comment '报价执行人';
    
alter table ptms.ficc_trading_order_all_his  
    add cfetsExecutetrader varchar(128) null comment '报价执行人';
```
# 3. 前后端交互接口信息
### 1.1.1. 现券语料解析接口--api/cashBond/parseSyntax
增加报价执行人字段返回

| 字段名                | 类型     | 描述    |
| ------------------ | ------ | ----- |
| cfetsExecutetrader | String | 报价执行人 |
### 1.1.2. 现券下单接口--api/cashBond/addTradingOrder
 增加报价执行人入参

| 字段名                | 类型     | 描述    |
| ------------------ | ------ | ----- |
| cfetsExecutetrader | String | 报价执行人 |

# 4. 配置&部署修改信息
不涉及
# 5. 新增技术组件说明
不涉及
# 6. 影响范围
现券语料解析
现券下单 
现券导出 
指令同步
历史指令同步 
现券指令同步
# 7. 外部依赖项
衡泰