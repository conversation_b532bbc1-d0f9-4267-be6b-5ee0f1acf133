/*
 * Copyright (C) 1999-2002 Free Software Foundation, Inc.
 * This file is part of the GNU LIBICONV Library.
 *
 * The GNU LIBICONV Library is free software; you can redistribute it
 * and/or modify it under the terms of the GNU Library General Public
 * License as published by the Free Software Foundation; either version 2
 * of the License, or (at your option) any later version.
 *
 * The GNU LIBICONV Library is distributed in the hope that it will be
 * useful, but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
 * Library General Public License for more details.
 *
 * You should have received a copy of the GNU Library General Public
 * License along with the GNU LIBICONV Library; see the file COPYING.LIB.
 * If not, write to the Free Software Foundation, Inc., 51 Franklin Street,
 * Fifth Floor, Boston, MA 02110-1301, USA.
 */

/*
 * CNS 11643-1992 plane 4 part b
 */

static const unsigned short cns11643_4b_2uni_page40[4384] = {
  /* 0x40 */
  0xb65b, 0xb66a, 0x1011, 0xb668, 0x101b, 0x1012, 0x100e, 0x1015,
  0x3f68, 0x1010, 0xb681, 0x1017, 0x4046, 0x4043, 0x1070, 0x10ed,
  0xbae7, 0x10ee, 0xbae6, 0xbad1, 0xbb11, 0x4181, 0xbad0, 0xbad9,
  0xbb0a, 0x10f4, 0xbada, 0xbadd, 0xbac8, 0xbae2, 0xbae9, 0xbacb,
  0x417c, 0xbacc, 0xbac1, 0x416a, 0xbade, 0x4188, 0x10f2, 0x10f8,
  0x10f3, 0xbb51, 0xbb12, 0x10fa, 0xbae0, 0xbb2b, 0xf6b4, 0xc0d8,
  0xc0d1, 0x4410, 0xc0d2, 0xc0cd, 0x441f, 0x11b6, 0x11b7, 0xc13d,
  0x440f, 0xc0ca, 0x11ba, 0xc0cb, 0x11bc, 0xc0d7, 0xc3f4, 0x123b,
  0x45d3, 0x122f, 0xc487, 0xc48b, 0xc483, 0x1237, 0x1299, 0xc7c5,
  0xc7d2, 0xc7c6, 0xc7d3, 0x12d9, 0x12d8, 0x12d7, 0xc7f0, 0xc7cd,
  0xc7cc, 0xc7dc, 0xc7d6, 0x1336, 0xc9e6, 0xca41, 0xca3f, 0x4987,
  0xcac5, 0xcac0, 0xcac6, 0xcabe, 0xcabf, 0x49c6,
  /* 0x41 */
  0x1370, 0xcb63, 0xccd3, 0xccda, 0xccd5, 0x13a5, 0x13a6, 0x13a9,
  0x13ee, 0x1454, 0xd0ee, 0x1457, 0xd0fc, 0xd214, 0x14a1, 0xd323,
  0xd322, 0xd330, 0xd4b5, 0x14d3, 0xd538, 0x14d4, 0xd53f, 0x14d7,
  0x4dcc, 0x4e6d, 0x4e70, 0x14f7, 0x14f6, 0x14f9, 0x14f8, 0x4f58,
  0x4f42, 0x4f68, 0x4f69, 0xd768, 0xd767, 0x4f43, 0xd765, 0x4f47,
  0x158a, 0xda75, 0xdaf7, 0xdaf3, 0xdaf6, 0x1594, 0xdb00, 0x1593,
  0xdaff, 0xdaf5, 0x529d, 0x15dd, 0x15dc, 0x15f7, 0x53cf, 0xde92,
  0xe006, 0x1642, 0xe007, 0xe06b, 0x1654, 0xe06c, 0x1655, 0xe193,
  0x168f, 0xe194, 0x54f4, 0x16b4, 0x16b3, 0xe262, 0xe263, 0x5509,
  0xe25f, 0xe264, 0xe25b, 0xe259, 0x16fb, 0x16fd, 0xe3a4, 0xe3ac,
  0x55ab, 0x16fc, 0xe4b5, 0x172c, 0x172f, 0xe4ae, 0x172b, 0xe4c5,
  0x1733, 0x1734, 0x55fb, 0xe63b, 0x57ac, 0x57ae,
  /* 0x42 */
  0x57aa, 0x17e8, 0xe8bf, 0xea2b, 0xea84, 0xea80, 0xeb4d, 0xeb4f,
  0x585c, 0xeb4c, 0xed92, 0x195d, 0x1ad2, 0x0085, 0x0088, 0x5fae,
  0x5f79, 0x008e, 0x0084, 0x5f71, 0x1adf, 0x5fb3, 0x0083, 0x5f2c,
  0x5f77, 0x612f, 0x63c3, 0x0102, 0x63cd, 0x0106, 0x0105, 0x63c9,
  0x00fe, 0x0101, 0x0100, 0x63ce, 0x64a5, 0x64a0, 0x64fe, 0x6559,
  0x669a, 0x01e7, 0x1e19, 0x68e4, 0x68d7, 0x68dc, 0x01e6, 0x68e7,
  0x01ed, 0x01e2, 0x01eb, 0x68e5, 0x01e8, 0x01ec, 0x1e0a, 0x0224,
  0x1f9a, 0x6e14, 0x0262, 0x6df1, 0x0261, 0x0260, 0x0264, 0x028b,
  0x6fb6, 0x706e, 0x706c, 0x7081, 0x7142, 0x0319, 0x0316, 0x0318,
  0x0322, 0x711a, 0x031d, 0x0317, 0x031e, 0x7127, 0x7125, 0x7117,
  0x711c, 0x713d, 0x7120, 0x0369, 0x036a, 0x7381, 0x036c, 0x037a,
  0x038a, 0x7509, 0x75c6, 0x75c4, 0x039c, 0x75c5,
  /* 0x43 */
  0x03fd, 0x03f9, 0x76ef, 0x03ff, 0x76df, 0x76de, 0x76ee, 0x76f5,
  0x76ec, 0x03fc, 0x76dd, 0x2385, 0x03fb, 0x0402, 0x045f, 0x2456,
  0x045e, 0x045d, 0x045c, 0x7822, 0x2451, 0x0492, 0x7997, 0x7995,
  0x0494, 0x0495, 0x04d1, 0x7ba9, 0x04f1, 0x7c53, 0x25b1, 0x7c56,
  0x7c55, 0x0577, 0x056f, 0x7de3, 0x7de2, 0x0587, 0x057d, 0x057c,
  0x057e, 0x0585, 0x058b, 0x0586, 0x0580, 0x5dfd, 0x7da9, 0x0578,
  0xf5ab, 0x7e33, 0x7e0a, 0x05ba, 0x8059, 0x0633, 0x81bc, 0x062d,
  0x81c4, 0x81c7, 0x81c6, 0x0637, 0x275a, 0x2763, 0x81b8, 0x81da,
  0x062e, 0x81b7, 0x81c0, 0x063d, 0x81cd, 0x06a0, 0x84bc, 0x84ab,
  0x06a3, 0x8586, 0x299b, 0x875a, 0x29a3, 0x070e, 0x299e, 0x8843,
  0x07b6, 0x89ac, 0x07ab, 0x07ad, 0x07a6, 0x89be, 0x2ab8, 0x07aa,
  0x2aba, 0x07b1, 0x89ab, 0x07a8, 0x07af, 0x07b0,
  /* 0x44 */
  0x07a7, 0x07b2, 0x079d, 0x07a5, 0x07b5, 0x743e, 0x2ac7, 0x2ad7,
  0x8cf9, 0x0841, 0x8cf3, 0x8cf7, 0x2c70, 0x8e45, 0x8e48, 0x0872,
  0x2c9d, 0x086f, 0x0871, 0x8e44, 0x0885, 0x8eaf, 0x8eb1, 0x08a4,
  0x08a5, 0x08a6, 0x8fcf, 0x08a8, 0x8fcb, 0x8fcd, 0x08a3, 0x9050,
  0x91ec, 0x921a, 0x2d16, 0x2d24, 0x91d8, 0x0943, 0x91d0, 0x91d1,
  0x093d, 0x0945, 0x917b, 0x91d2, 0x0944, 0x91d4, 0x91e7, 0x91df,
  0x91de, 0x2d45, 0x91d9, 0x91cf, 0x950d, 0x09e3, 0x2f79, 0x5dfd,
  0x09df, 0x09e4, 0x2f7a, 0x09e5, 0x9538, 0x3054, 0x0a22, 0x0a4a,
  0x98a9, 0x0a49, 0x0a44, 0x0a4b, 0x0a87, 0x0a89, 0x0a92, 0x0a91,
  0x0a90, 0x0a8e, 0x993e, 0x9946, 0x9aed, 0xf630, 0x0b12, 0x0b10,
  0x0b11, 0x9c6c, 0x0b32, 0x0b34, 0x0b37, 0x0b33, 0x0b36, 0x0b35,
  0x0b65, 0x9dac, 0x337c, 0x337b, 0x9ec9, 0x3412,
  /* 0x45 */
  0x0bb0, 0x9f12, 0x0baf, 0x0baa, 0x9efd, 0x9f01, 0x9f11, 0x0bab,
  0x9f89, 0x9f05, 0x9efe, 0x9f0b, 0x9f20, 0x9f04, 0xa088, 0x0bf3,
  0xa102, 0x0bf4, 0xa103, 0x34b6, 0x34e0, 0x0c08, 0x0c4e, 0x0c55,
  0x0c4b, 0xa229, 0xa23b, 0x0c4d, 0x3573, 0xa206, 0x0c52, 0x3572,
  0x0c4c, 0x3570, 0x0c50, 0x0c53, 0xa203, 0x0c51, 0xa378, 0xa379,
  0xa37d, 0x0c89, 0xa37f, 0x0c95, 0x0ccc, 0x0cc8, 0x0cce, 0xa432,
  0x0cca, 0xa400, 0x369d, 0xa422, 0x0ccd, 0xa5a9, 0x0d5c, 0x0d67,
  0x0d69, 0x0d65, 0x0d62, 0xa704, 0x3827, 0x3835, 0xf659, 0x0daa,
  0xa8b8, 0xa99b, 0x0dd2, 0x39a2, 0x0e03, 0x0e0c, 0xaa92, 0x0e09,
  0x0e06, 0x0e05, 0x3989, 0xaa8f, 0x0e0b, 0x0e08, 0xaa98, 0x39a5,
  0xaaae, 0x0e8e, 0xad9d, 0x3ab6, 0x0ee8, 0xaf49, 0xaf50, 0xaf46,
  0x0eea, 0xaf4e, 0x3ba5, 0x3bc3, 0xaf55, 0x0ee9,
  /* 0x46 */
  0x0eeb, 0xaf64, 0x0ef0, 0xb138, 0x0f4b, 0x3dab, 0x0f73, 0x0f75,
  0x0f92, 0x0f91, 0x0f93, 0x3e25, 0x0fa7, 0x0fa6, 0x0fa8, 0x0faa,
  0xb3fe, 0x0fa9, 0x3e59, 0x0fbb, 0x0fbc, 0x0fba, 0x0fbd, 0x1027,
  0x3f85, 0xb6a6, 0x1024, 0x101e, 0x101f, 0x101d, 0x1020, 0x1023,
  0x1029, 0x1022, 0xb69c, 0xb699, 0x101c, 0x3f8e, 0x1028, 0xb6b5,
  0xb6a3, 0xb6a0, 0xb6a7, 0xb69b, 0xb8df, 0xb8e1, 0x1071, 0x1073,
  0x1072, 0xb94d, 0x1102, 0xbbf3, 0xbb6f, 0xbb69, 0x10fe, 0x41be,
  0xbb6b, 0xbb78, 0xbb87, 0x1108, 0xbb85, 0xbb82, 0xbb90, 0x1107,
  0x1104, 0xbb80, 0xbb67, 0x1100, 0x10fc, 0xbb61, 0x1144, 0xbb93,
  0x10f1, 0xbbf2, 0xbb86, 0x41a6, 0x1106, 0xbfcd, 0xbfc4, 0x11c6,
  0x11c3, 0x11c1, 0x11c2, 0xc10f, 0x11c4, 0x11c7, 0xc10d, 0x11bf,
  0x11d2, 0xc173, 0x11ca, 0xf6ba, 0xc10a, 0x442f,
  /* 0x47 */
  0xc108, 0xc113, 0x1213, 0xc3f8, 0x1230, 0x123e, 0x1239, 0xc4ab,
  0xc4a8, 0x123c, 0x123f, 0xc4a5, 0x1234, 0x123d, 0xc4c3, 0xc4a4,
  0x1238, 0xc4d4, 0xc4ba, 0xc5f1, 0x46a0, 0x1282, 0xc63f, 0x1283,
  0xc6ea, 0x129b, 0xc7f7, 0x12e0, 0x12dd, 0xc7fa, 0xc7f5, 0x12de,
  0xc7fe, 0x12e3, 0x12e5, 0xc800, 0x4797, 0x12e2, 0xc802, 0xc7fb,
  0xc807, 0x12df, 0xc81a, 0x132e, 0xc9b8, 0x1337, 0x1338, 0xc9e9,
  0xc9eb, 0xca50, 0xca4f, 0x498b, 0xcb86, 0x0162, 0xcb8e, 0x1394,
  0x1393, 0x13ab, 0x13ad, 0xccf0, 0xccfb, 0x13f5, 0x13f7, 0xce42,
  0x13f6, 0x13f8, 0xce85, 0x13fb, 0x13f9, 0x1458, 0x145a, 0xd105,
  0x1459, 0x4c0f, 0x1485, 0x14a4, 0x14d8, 0x14d9, 0xd54b, 0x14dd,
  0x14c8, 0xd563, 0x14fa, 0x14fb, 0x4f75, 0x1527, 0x4f9f, 0x152a,
  0x1525, 0xd799, 0x1528, 0xda81, 0xdb17, 0xdb10,
  /* 0x48 */
  0xdb12, 0x52a6, 0x1595, 0x539a, 0xdcfa, 0xdcf3, 0xdcf2, 0xdcf5,
  0xdcf6, 0xddbb, 0xddc2, 0xdea7, 0x160f, 0x1611, 0xdea8, 0xdea3,
  0x1610, 0xdeaa, 0x1615, 0x1613, 0x5457, 0xdfdc, 0x1647, 0x1646,
  0xe00f, 0x1659, 0x165b, 0xe079, 0x165e, 0xe07f, 0xe085, 0x165a,
  0x1691, 0x1692, 0x1690, 0x1693, 0xe21b, 0x54f7, 0x16be, 0xe277,
  0xe276, 0xe298, 0x16bc, 0x16bb, 0x16b7, 0x16b9, 0xe27a, 0x1701,
  0x16fe, 0xe3bc, 0xe3ba, 0x1702, 0xe3b6, 0x16ff, 0x55b0, 0xe3b4,
  0x1700, 0xe4cf, 0x1737, 0x173a, 0x176f, 0x1777, 0x1779, 0x56c6,
  0xe67b, 0x17c8, 0xe81c, 0xe821, 0xe81d, 0xe8c0, 0x17f2, 0xe8ff,
  0x17f1, 0x17f0, 0x5862, 0xeb56, 0x1834, 0xeda1, 0xeda2, 0xeda6,
  0xf056, 0xf057, 0x192c, 0x192d, 0xf101, 0xf1ed, 0xf71c, 0xf3f2,
  0x1afa, 0x5fb8, 0x0091, 0x5fc0, 0x0094, 0x5fb7,
  /* 0x49 */
  0x5fe1, 0x00c4, 0x010a, 0x63e8, 0x1c85, 0x64b2, 0x0152, 0x66ae,
  0x0159, 0x026f, 0x697e, 0x01f2, 0x01f4, 0x1e43, 0x6976, 0x01f1,
  0x1e3c, 0x6996, 0x026a, 0x6e20, 0x6e21, 0x6e23, 0x6e29, 0x7077,
  0x7151, 0x0324, 0x7156, 0x0323, 0x7188, 0x7159, 0x7155, 0x0327,
  0x7297, 0x7298, 0x036d, 0x21ed, 0x036e, 0x036f, 0x73a1, 0x73a3,
  0x2235, 0x039f, 0x040a, 0x0406, 0x040e, 0x770a, 0x040d, 0x0405,
  0x773d, 0x770c, 0x040b, 0x0410, 0x042e, 0x783d, 0x7839, 0x79b0,
  0x79b2, 0x79ae, 0x0496, 0x0497, 0x04c4, 0x2547, 0x04c5, 0x7b20,
  0x04d2, 0x7c6e, 0x7c6d, 0x7c6a, 0x0581, 0x7e32, 0x058e, 0x0590,
  0x058f, 0x7e39, 0x0591, 0x0595, 0x0593, 0x7da3, 0x266d, 0x7e7f,
  0x7e35, 0x7e3d, 0x7ff4, 0x7ff5, 0x063b, 0x0648, 0x8248, 0x8228,
  0x0646, 0x0647, 0x8227, 0x8232, 0x822c, 0x064c,
  /* 0x4a */
  0x822e, 0x064a, 0x0650, 0x0643, 0x8223, 0x8231, 0xf5c5, 0x0649,
  0x06a6, 0x06a5, 0x06a4, 0x84c9, 0x8589, 0x06b9, 0x85bb, 0x06ce,
  0x06cf, 0x0713, 0x8897, 0x8893, 0x8a28, 0x07c6, 0x07c5, 0x07ca,
  0x07d9, 0x07c1, 0x8a18, 0x8a3b, 0x2af5, 0x8a27, 0x8a24, 0x8a1b,
  0x8a31, 0x07cb, 0x8a26, 0x8aa3, 0x8a3f, 0x8a22, 0x8a19, 0x2b03,
  0x8a41, 0x8a2b, 0x2b65, 0x0842, 0x8d0c, 0x2c75, 0x0874, 0x0873,
  0x8e4e, 0x8eb9, 0x8efa, 0x8fe9, 0x8fe8, 0x8fe4, 0x2d8a, 0x2d56,
  0x0952, 0x925f, 0x925d, 0x9252, 0x0950, 0x9274, 0x094b, 0x9246,
  0x094c, 0x096d, 0x92aa, 0x2d98, 0x924a, 0x9259, 0x924b, 0x094f,
  0x2d68, 0x09f0, 0x9550, 0x3034, 0x3045, 0x0a4d, 0x0a4c, 0x98d0,
  0x0a4f, 0x0a4e, 0x0a50, 0x98cc, 0x315c, 0x0a96, 0x3156, 0x9964,
  0x9965, 0x0a97, 0x0a95, 0x0a98, 0x995c, 0x9b15,
  /* 0x4b */
  0x0aec, 0x0aeb, 0x0b13, 0x0b14, 0x0b38, 0x0b3a, 0x0b39, 0x9c79,
  0x0b68, 0x0b67, 0x9dc5, 0x9db8, 0x9f2c, 0x0bbe, 0x0bbc, 0x9f37,
  0x9f35, 0x9f31, 0x0bbb, 0x9f2f, 0x0bba, 0x9f2b, 0x0bb9, 0x0bb7,
  0x9f2d, 0x9f2a, 0x0bc1, 0xa095, 0x0bf7, 0xa23e, 0x0c60, 0xa247,
  0xa245, 0x0c59, 0x0c5c, 0x0c5a, 0x0c58, 0xa252, 0x0c5b, 0xa270,
  0xa250, 0xa258, 0xa251, 0xa23d, 0x0c5d, 0xa241, 0xa20c, 0xa23c,
  0xa386, 0xa383, 0xa389, 0xa3f3, 0x0cd4, 0x0cd3, 0x36bf, 0x0cd2,
  0x36bd, 0xa42d, 0x0cd7, 0x0cd1, 0x36e4, 0x0cd5, 0xa5c5, 0xf655,
  0x0d6d, 0x0d6f, 0x3834, 0x0d75, 0x0d6c, 0x0d74, 0xa743, 0x0d73,
  0xa737, 0xa745, 0x3836, 0x0dac, 0xa8e5, 0xa9a6, 0xaadb, 0x0e10,
  0xaada, 0xaae6, 0x39ba, 0x39bc, 0x0e0f, 0x39c8, 0x0e23, 0x39c3,
  0x0e1d, 0x39b6, 0x0e0e, 0xaaf8, 0xaae9, 0x0e15,
  /* 0x4c */
  0x39c2, 0x0e13, 0xaae8, 0xaaf6, 0x0e1b, 0x39c5, 0x0e22, 0x0e26,
  0xaae7, 0x39bd, 0x39b0, 0x0e21, 0x0e1c, 0x0e17, 0xaad5, 0x0e1a,
  0x39bb, 0xadd3, 0xadc7, 0xadd1, 0x0e99, 0xadc3, 0x0e97, 0xaf80,
  0xaf98, 0x0efd, 0xaf84, 0x0ef6, 0x0efe, 0x0ef5, 0x0eff, 0x0ef7,
  0xaf97, 0xaf83, 0xaf81, 0x0f01, 0x3c04, 0xaf8c, 0xb142, 0xb2ab,
  0x0f77, 0xb2a3, 0xb2a6, 0xb35c, 0xb369, 0xb367, 0x0fab, 0xb48b,
  0xb4a8, 0x0fc0, 0xb6d8, 0x1031, 0x102e, 0xf689, 0xb6dc, 0x102c,
  0xb6e0, 0xb6e5, 0x1032, 0x102f, 0x102b, 0x102d, 0x1033, 0xb818,
  0xb819, 0x3ff1, 0x1057, 0x105c, 0x107b, 0xb95f, 0xb95e, 0x107a,
  0xbc02, 0x4222, 0x1113, 0x111e, 0x1117, 0x1120, 0x112a, 0x1111,
  0x1115, 0x110f, 0x1118, 0x4238, 0xbc12, 0xbc36, 0x112c, 0x4232,
  0xf6a8, 0x4210, 0xbc23, 0xbc03, 0x111c, 0xbc00,
  /* 0x4d */
  0x1129, 0xbc46, 0xbc61, 0x1112, 0x424f, 0x1197, 0xc184, 0x4472,
  0xc16b, 0xc162, 0xc156, 0xc16a, 0xc152, 0xc155, 0x11d4, 0x11d0,
  0x447c, 0xc161, 0xf6bb, 0xc158, 0xc177, 0x11d3, 0x1214, 0xc4d7,
  0x1246, 0x1245, 0xc4de, 0x1243, 0xc4df, 0x460d, 0x1244, 0x1248,
  0xc4d1, 0x1247, 0xc4e2, 0xc4e1, 0xc4dd, 0x4608, 0x1249, 0x1285,
  0xc64b, 0x1284, 0xc64e, 0x129d, 0xc6fc, 0x129e, 0x12a0, 0xc6fa,
  0x129c, 0xc6fb, 0x129f, 0xc6fe, 0x12f7, 0x12ea, 0xc831, 0x12ef,
  0x12e9, 0x12f3, 0x12f0, 0x12eb, 0xc838, 0x12ec, 0x12f2, 0x12f5,
  0x12ee, 0xc83a, 0xc9bb, 0x133a, 0x134b, 0xca59, 0x134a, 0x134c,
  0xcadb, 0xcadf, 0xcae2, 0x1379, 0x137b, 0x1378, 0xcb9e, 0xcba1,
  0x13b5, 0xcd10, 0x13b4, 0x13b7, 0x4a9e, 0x1409, 0x13fe, 0x1408,
  0x1407, 0xce76, 0xce7f, 0xce7d, 0x1406, 0x1404,
  /* 0x4e */
  0x1405, 0x13ff, 0x140b, 0xce82, 0xd057, 0x143b, 0x145d, 0x145c,
  0x145f, 0x145e, 0x4c28, 0xd12e, 0x4c21, 0x1483, 0xd38b, 0xd38d,
  0x14a7, 0x4d66, 0x4d6c, 0xd390, 0x14a8, 0xd4ec, 0xd56f, 0xd56b,
  0xd571, 0xd578, 0x4df6, 0x14e0, 0x14df, 0x14fe, 0x14fc, 0x14ff,
  0x14fd, 0xd7ad, 0x152c, 0x4fec, 0x4fba, 0x4fe3, 0x4fbd, 0x159d,
  0xdb2f, 0x52b4, 0xdd78, 0x1640, 0xdff8, 0xe019, 0x165f, 0xe09b,
  0xe094, 0xe097, 0xe099, 0xe1a6, 0xe1a4, 0xe1a7, 0x54d1, 0xe295,
  0x16c0, 0x5523, 0xe290, 0x16c1, 0x16c6, 0xe29b, 0xe3c4, 0x1704,
  0x1705, 0xe3c6, 0x560b, 0x173e, 0x173d, 0x1740, 0x173f, 0xe4e3,
  0x1742, 0xe4df, 0xe4dd, 0xe4e7, 0x1784, 0x1782, 0x177f, 0x1785,
  0xe82d, 0xe82c, 0x17cc, 0x57b2, 0x17cb, 0x17cd, 0xe834, 0xe838,
  0x57db, 0xe91d, 0x17f5, 0xe91a, 0xe91b, 0xe914,
  /* 0x4f */
  0x57f0, 0xe917, 0xea21, 0x1820, 0x1821, 0xeaaa, 0xeaa1, 0x1837,
  0x183e, 0x5873, 0x183d, 0x586e, 0xeb63, 0xeb79, 0xeb60, 0x5865,
  0xeb62, 0x183c, 0xeb61, 0x1838, 0x586a, 0xeb70, 0x586d, 0xeb6a,
  0x183b, 0xedc8, 0x18b0, 0xedc5, 0xedbe, 0xedc2, 0x18ad, 0x18b2,
  0x18b8, 0x5a0b, 0xedc7, 0x18af, 0xedb0, 0xedca, 0x191a, 0x5b76,
  0x1920, 0x1921, 0x1930, 0x5ba8, 0x192f, 0xf10d, 0xf107, 0xf196,
  0xf1ef, 0x195f, 0x1960, 0xf21e, 0xf21d, 0x5c11, 0xf390, 0x5feb,
  0x008a, 0x1b19, 0x009c, 0x5fee, 0x009a, 0x5fef, 0x5fec, 0x63fa,
  0x010c, 0x010b, 0x010d, 0x1c92, 0x6504, 0x69d3, 0x01fe, 0x69d1,
  0x69fa, 0x01ff, 0x01fb, 0x01fc, 0x0209, 0x69c8, 0x0200, 0x69d5,
  0x1e75, 0x69cd, 0x69d2, 0x69fb, 0x6c2a, 0x6e88, 0x6e61, 0x0271,
  0x6e63, 0x6e62, 0x206f, 0x5e57, 0x71c8, 0x7198,
  /* 0x50 */
  0x032b, 0x73c1, 0x773f, 0x7741, 0x0414, 0x0411, 0x0412, 0x7852,
  0x0463, 0x785e, 0x046e, 0x049a, 0x79c7, 0x049b, 0x7a70, 0x7b27,
  0x04c8, 0x7b71, 0x7bb0, 0x04f6, 0x7e42, 0x7e43, 0x26a5, 0x058c,
  0x0597, 0x05a2, 0x26a0, 0x7e57, 0x7e9d, 0x8289, 0x828d, 0x828b,
  0x8280, 0x8292, 0x828a, 0x82c8, 0x0654, 0x828f, 0x8293, 0x8291,
  0x06a8, 0x84f2, 0x84de, 0x06a9, 0x85c8, 0x28b4, 0x28d8, 0x29c2,
  0x0718, 0x0717, 0x071d, 0x87a7, 0x87a1, 0x0731, 0x8a8c, 0x8a7f,
  0x07d8, 0x07d5, 0x8a7b, 0x8a95, 0x8a99, 0x07e1, 0x8a8e, 0x07d4,
  0x8ada, 0x8a8a, 0x8a9c, 0x07e3, 0x8a7e, 0x0844, 0x0845, 0x8d1a,
  0x8e55, 0x0876, 0x0875, 0x8e60, 0x2ca8, 0x0888, 0x08aa, 0x8ffb,
  0x08ab, 0x08ac, 0x8ffd, 0x0957, 0x2d83, 0x0960, 0x095d, 0x096b,
  0x92d0, 0x0963, 0x0967, 0x92c5, 0x095e, 0x92d2,
  /* 0x51 */
  0x9311, 0x2dc5, 0x2fcd, 0x09f9, 0x09f3, 0x95ad, 0x95a4, 0x95a9,
  0x95b0, 0x959d, 0x09f6, 0x9798, 0x309c, 0x0a51, 0x0a53, 0x0a52,
  0x9992, 0x0a9b, 0x998a, 0x0a9c, 0x998d, 0x9996, 0x0af7, 0x3299,
  0x328f, 0x3291, 0x9b45, 0x9b4b, 0x9c0f, 0x9c16, 0x0b3b, 0x9c8a,
  0x0b3e, 0x0b3d, 0x9dc9, 0x0b69, 0x9dc8, 0x9dca, 0x9f64, 0x0bc3,
  0x0bc4, 0x0bc7, 0x9f5d, 0x9f63, 0x3439, 0x0bc6, 0x342e, 0x0bc8,
  0x9f88, 0xa03a, 0xa039, 0x349f, 0x34a0, 0x0be6, 0x0bf8, 0xa117,
  0xa193, 0x0c07, 0xa195, 0x0c64, 0x0c68, 0xa276, 0x3594, 0x0c65,
  0x35ae, 0xa280, 0xa27b, 0x0c69, 0xa248, 0xa2a8, 0xa288, 0xa38b,
  0xa38a, 0xa38c, 0xa3fc, 0x0cda, 0x0ce0, 0x36e6, 0xa458, 0x0cde,
  0xa451, 0xa455, 0xa453, 0x0d1c, 0x0d1d, 0x0d1a, 0xa5dc, 0x0d7b,
  0x0d7a, 0x0d7c, 0xa75c, 0x0d78, 0x0d77, 0xa765,
  /* 0x52 */
  0xf65a, 0x0db1, 0xa8ee, 0x0db2, 0x0db0, 0xa8f0, 0x38bc, 0xa8f2,
  0x0e36, 0xab35, 0x0e2e, 0xab22, 0x39d6, 0xab20, 0x0e34, 0xab2d,
  0xab28, 0xab26, 0xab3c, 0x0e2a, 0xab38, 0x0e33, 0x0e2d, 0x0e2f,
  0x0e31, 0x0e2b, 0x0e32, 0xab2a, 0x0e35, 0xab1a, 0xab30, 0xaae3,
  0xab19, 0xade9, 0xade7, 0x3acf, 0xafd6, 0xafc4, 0x0f08, 0xaf87,
  0x0f06, 0xafc7, 0xafd9, 0x3c18, 0xafdf, 0x0f4d, 0x0f61, 0xb20a,
  0xb201, 0xb2b1, 0x0f78, 0xb2c1, 0x0f94, 0x0f95, 0xb376, 0xb40d,
  0xb40e, 0x0fc1, 0x0fc3, 0x3e6d, 0xb707, 0x1039, 0xb709, 0xb716,
  0x103a, 0x103b, 0x1035, 0x1036, 0xb70e, 0x103c, 0x3f90, 0xb706,
  0xb81e, 0xb84a, 0x1058, 0x107c, 0x107d, 0x108d, 0x108c, 0xbca6,
  0xbc91, 0x113b, 0x113f, 0xbcb8, 0xbc96, 0x1132, 0x112d, 0xbcc5,
  0x112f, 0x1139, 0x112e, 0x113a, 0xbd0a, 0x1136,
  /* 0x53 */
  0x1131, 0x113e, 0x1138, 0x4252, 0x1134, 0xbcb2, 0x1141, 0xbcb7,
  0xbcb4, 0xbc89, 0xbc8d, 0x1130, 0xbc87, 0xbcc2, 0xbc9c, 0xbc92,
  0x1143, 0xbcca, 0x4250, 0xbc8a, 0xbfe0, 0x1198, 0xbfe6, 0xbfe5,
  0x44a0, 0xc1a5, 0xc1b3, 0x4486, 0x11da, 0x11d7, 0xc1b5, 0xc1af,
  0x4495, 0xc1b0, 0xc1cc, 0x448c, 0xc1a2, 0xc1be, 0xc1c6, 0xc1ac,
  0xc1ae, 0x1218, 0x4560, 0xf6c3, 0xc508, 0xc505, 0x1252, 0x4628,
  0xc4fe, 0x124e, 0x4620, 0xc500, 0x124f, 0x1250, 0xc664, 0xc668,
  0x46a8, 0x1286, 0x1287, 0x1289, 0xc66a, 0xc669, 0xc70d, 0xc712,
  0x12a2, 0x12a3, 0xc70f, 0x12a1, 0xc867, 0xc879, 0xc872, 0xc866,
  0xc87c, 0x12f9, 0x12fd, 0xc868, 0xc885, 0xc876, 0xc874, 0xc871,
  0xc864, 0x133e, 0x133c, 0xc9f8, 0x134d, 0xca6c, 0x134e, 0xcaeb,
  0x1381, 0x1383, 0x1382, 0xcbb8, 0x1380, 0x1388,
  /* 0x54 */
  0xcd2d, 0xcd2e, 0xcd28, 0xcd29, 0xcd31, 0x13b9, 0xcd2f, 0xcd2a,
  0x4b3a, 0xcea4, 0xceb7, 0xcebf, 0x1411, 0x140d, 0x1410, 0x1413,
  0xd063, 0x143c, 0x1462, 0x1463, 0xd167, 0xd3be, 0xf6e0, 0xd595,
  0xd59c, 0x1503, 0x1506, 0x1502, 0x1501, 0xd671, 0xd672, 0x1505,
  0xd66f, 0x4e94, 0x5011, 0xd7f1, 0x5037, 0x1536, 0xd7e8, 0x1535,
  0x5043, 0xf6ea, 0x15a1, 0x15a3, 0xdb49, 0xdb64, 0x15a0, 0xdb48,
  0x15ea, 0x53a6, 0x15e8, 0xdd2c, 0xdde1, 0xddef, 0xdec0, 0x1621,
  0x161b, 0xded7, 0xded4, 0x1649, 0x1648, 0xe0ad, 0x5495, 0x1662,
  0x1661, 0x1664, 0x1660, 0x1663, 0xe0ae, 0xe0ac, 0x5496, 0x1666,
  0x16ac, 0x16ab, 0xe227, 0xe2ae, 0x16c3, 0xe2b0, 0x16c4, 0x5525,
  0xe2c0, 0xe2c4, 0x1708, 0x1709, 0x170a, 0x1706, 0x1707, 0x1741,
  0xe507, 0x1745, 0xe4fd, 0x1743, 0x1744, 0x5626,
  /* 0x55 */
  0x5634, 0x1747, 0xf704, 0xe61b, 0xe61c, 0x1771, 0xe6b2, 0xe6b4,
  0xe6b7, 0xe6b1, 0xe6b3, 0xe6ae, 0x178b, 0x17d4, 0x17d3, 0x17d1,
  0x57ba, 0x17d2, 0xe83f, 0xe936, 0x17f7, 0x17f8, 0xe931, 0xe93b,
  0xe935, 0xe93a, 0xe937, 0xea22, 0xea36, 0xea32, 0x1822, 0xeac0,
  0x1845, 0x1841, 0x5881, 0x1840, 0x588a, 0xeb85, 0x587f, 0x1842,
  0xeb89, 0x18c1, 0x18c5, 0xede1, 0x18bb, 0x18b9, 0x18bd, 0x18c9,
  0xee0a, 0x5a11, 0xee09, 0x18bf, 0x18c7, 0xede8, 0x18c3, 0x1924,
  0x1931, 0x1933, 0xf118, 0x1932, 0x1934, 0x1952, 0x1961, 0x5bd9,
  0x197a, 0x1982, 0x5c3c, 0xf3c5, 0x1b23, 0x600d, 0x600c, 0x6021,
  0x1b2c, 0x6216, 0x640e, 0x010f, 0x1c95, 0x6417, 0x0123, 0x0125,
  0x6a48, 0x0206, 0x0208, 0x1e88, 0x6a55, 0x6a49, 0x6a4c, 0x1e8b,
  0x6a4f, 0x6a3d, 0x027a, 0x0277, 0x6e8a, 0x6ea8,
  /* 0x56 */
  0x708a, 0x032e, 0x71cb, 0x032f, 0x71d5, 0x78d4, 0x041b, 0x0413,
  0x775c, 0x7775, 0x0466, 0x7861, 0x0465, 0x7096, 0x04f7, 0x7c8a,
  0x7ed0, 0x26e1, 0x26d7, 0x7e49, 0x7ecf, 0x059c, 0x82d6, 0x827f,
  0x0653, 0x82d2, 0x82cf, 0x8506, 0x8509, 0x06ba, 0x28a3, 0x0722,
  0x29d3, 0x8b06, 0x8af1, 0x8b04, 0x2b8b, 0x8afa, 0x8af4, 0x07eb,
  0x07dd, 0x8af9, 0x07ef, 0x8a8b, 0x8b03, 0x0847, 0x0846, 0x8da2,
  0x0878, 0x2cac, 0x0889, 0x900c, 0x900b, 0x0968, 0x0976, 0x0974,
  0x0979, 0x9324, 0x097a, 0x0977, 0xf614, 0x0971, 0x9325, 0x0972,
  0x95f6, 0x09ff, 0x95fb, 0x0a05, 0x9732, 0x97fb, 0x0a54, 0x98f2,
  0x98f3, 0x0a9e, 0x0a9f, 0x3174, 0x999c, 0x9b72, 0x0afa, 0x9b74,
  0x0b44, 0x0b3f, 0x0b40, 0x9c86, 0x0b42, 0x9c8e, 0x9c90, 0x0b51,
  0x9ccd, 0x9cf7, 0x9dd6, 0x9f84, 0x9f95, 0x9f8a,
  /* 0x57 */
  0x3440, 0x0bca, 0x9f97, 0x3441, 0x0bce, 0x0bc9, 0xa0a0, 0xa0a1,
  0xa122, 0xa1a6, 0xa1a4, 0x0c09, 0x34e8, 0x0c6c, 0x0c6e, 0x0c70,
  0x0c6d, 0x0c6b, 0x0c71, 0x0c72, 0xa2af, 0xa2b0, 0xa2bd, 0x0c8c,
  0x0ce4, 0xa476, 0x0ce1, 0xa47b, 0xa479, 0x36f6, 0x0ce7, 0x3700,
  0x0ce2, 0x0d1f, 0xa5ee, 0xa5f1, 0x0d7e, 0xa794, 0x0d80, 0x3859,
  0x3855, 0xa791, 0x0db9, 0x0db7, 0x0db8, 0xa910, 0x0dba, 0x38f4,
  0xa9af, 0x0dd3, 0x0e3f, 0x3a04, 0x0e45, 0x0e41, 0x3a15, 0x0e42,
  0x0e43, 0x0e3b, 0x0e38, 0xab7b, 0xab77, 0x0e3a, 0x39f5, 0xab80,
  0xabc6, 0x0e3c, 0xab7c, 0xab90, 0x0e3e, 0xaba3, 0xab7d, 0xabbd,
  0x0e9e, 0x0e9f, 0x0ea1, 0xae13, 0x0e9b, 0x0f12, 0xb011, 0xb044,
  0xb00d, 0x0f18, 0x0f0c, 0xb214, 0x0f62, 0xb2b8, 0x0f7a, 0xb2b7,
  0xb383, 0x0fae, 0x0faf, 0xb414, 0x0fad, 0xb41c,
  /* 0x58 */
  0x0fc4, 0x0fc7, 0x0fc6, 0x0fc5, 0xb4d4, 0xb4d5, 0x3fc1, 0x1040,
  0xb743, 0xb742, 0x103f, 0x1041, 0xf68a, 0xb741, 0xb84e, 0x107f,
  0xb987, 0x1086, 0x1081, 0x1080, 0x108e, 0x114a, 0xbd39, 0x1147,
  0xbd8f, 0xbd2a, 0x114b, 0x1146, 0x114e, 0x427d, 0xbd2b, 0x42a5,
  0xbd50, 0x1148, 0xbd6e, 0x1145, 0xbd3b, 0xbd53, 0xbd5f, 0xbd2f,
  0xbd30, 0xbd38, 0xbd4c, 0xbff1, 0x11db, 0x11e7, 0x11e4, 0xc207,
  0xc216, 0x11e1, 0xc214, 0x11e9, 0xc1fb, 0x11e5, 0x11e0, 0x11e3,
  0xc1f8, 0xc210, 0xc21d, 0xc1ff, 0xc20b, 0xc204, 0x11ea, 0xc1fe,
  0xc3ff, 0x463a, 0x1254, 0x1258, 0x125c, 0xc523, 0x1255, 0x128b,
  0x128c, 0x12a6, 0x12a5, 0xc72a, 0xc8a0, 0xc898, 0xc89c, 0x12ff,
  0xc89e, 0xc8a6, 0xc8b5, 0xc8b0, 0x1330, 0x1340, 0x1341, 0xcaf9,
  0xcaf5, 0x1386, 0xcbd2, 0x13bf, 0x13bd, 0xcd50,
  /* 0x59 */
  0xcd4e, 0xcd4b, 0xcd52, 0xcd4d, 0x13be, 0x1419, 0xcee4, 0x141c,
  0xceda, 0x141b, 0x1417, 0x1418, 0x4b51, 0xcedf, 0xcee8, 0x143d,
  0x146a, 0x1466, 0xd170, 0xd172, 0x1467, 0xd177, 0x1468, 0x14ad,
  0x14ae, 0xd3e6, 0xd5aa, 0x14d6, 0x1509, 0xd68c, 0x4e98, 0xd689,
  0x150c, 0x150a, 0xd832, 0x153b, 0x153a, 0x5084, 0x5081, 0xd87a,
  0x506f, 0xda9e, 0xdaa0, 0xdb70, 0x15af, 0x15aa, 0x15ab, 0xdb6e,
  0xdb66, 0x15b1, 0xdb65, 0x15ac, 0x15ec, 0xdd7f, 0xdde0, 0x1601,
  0xddff, 0xdef6, 0xdef7, 0xdef5, 0x1623, 0xdefc, 0x1624, 0x161e,
  0xdef9, 0x164a, 0x1665, 0x166a, 0xe0ca, 0xe0c3, 0xe0c6, 0x1669,
  0xe1b8, 0xe1bd, 0x1695, 0xe1bc, 0xe205, 0xe2e0, 0xe2e9, 0x5542,
  0xe2df, 0xe2ec, 0x16cc, 0xe2e5, 0xe2de, 0xf700, 0x16cf, 0xe2f0,
  0xe2e3, 0x170f, 0xe3ec, 0x170e, 0x170b, 0x1710,
  /* 0x5a */
  0x170d, 0x170c, 0xe3f2, 0xe3ef, 0xe3e9, 0xe4fb, 0x1746, 0x1748,
  0x5637, 0x1749, 0xe537, 0xe6de, 0x1791, 0x178e, 0xe6da, 0x17d8,
  0x17d6, 0xe84b, 0x17da, 0xe849, 0x17d7, 0xe8d5, 0x57ff, 0x17f9,
  0xe952, 0xe947, 0x17fc, 0xe948, 0xeacc, 0xead0, 0x58a9, 0x184a,
  0x58a7, 0x184e, 0x58b3, 0x58ac, 0x58b0, 0xeb86, 0xeba7, 0xeba3,
  0x589c, 0xebb6, 0xebad, 0xee13, 0x5a3c, 0x5a1c, 0x5a3a, 0x18d3,
  0x18cd, 0x18d1, 0xee17, 0xee22, 0x5a32, 0x5a34, 0xee49, 0xee26,
  0xf70c, 0xee3c, 0xee28, 0xf0a8, 0x5bc7, 0xf1fb, 0x1962, 0xf232,
  0xf2d6, 0xf348, 0x1983, 0x5c3f, 0xf3c6, 0x1992, 0x009f, 0x00a0,
  0x6025, 0x6026, 0x6024, 0x6033, 0x6170, 0x0127, 0x6790, 0x020b,
  0x6a95, 0x6aa1, 0x6a92, 0x6a8f, 0x6a9f, 0x6a96, 0x6a98, 0x6a9d,
  0x6aa0, 0x028d, 0x7097, 0x71eb, 0x0370, 0x7787,
  /* 0x5b */
  0x24eb, 0x7b32, 0x059a, 0x059f, 0x059d, 0x7ed8, 0x7efb, 0x7f06,
  0x059b, 0x7ed1, 0x26d5, 0xf5b0, 0x0660, 0x0664, 0x0669, 0x0663,
  0x0667, 0x0662, 0x82f6, 0x8304, 0x82fe, 0x2802, 0x82ff, 0x82f7,
  0x8518, 0x06ac, 0x8514, 0x85cd, 0x8620, 0x87de, 0x0726, 0x0723,
  0x0725, 0x8b45, 0x8b53, 0x07f8, 0x8b4b, 0x8b55, 0x8b41, 0x07f7,
  0x07fb, 0x07fa, 0x8b5c, 0x8b54, 0x8e71, 0x8ed0, 0x08b0, 0x08af,
  0x9053, 0x9329, 0x937e, 0x097e, 0x9379, 0x097d, 0x0980, 0x9370,
  0x936a, 0x097f, 0x0986, 0x9385, 0x9364, 0x2e12, 0x9378, 0x0981,
  0x9632, 0x9627, 0x962f, 0x0a24, 0x0a58, 0x0a57, 0x0aa0, 0x99ba,
  0x0afe, 0x9b71, 0x9b8c, 0x0b15, 0x9c1a, 0x0b47, 0x0b46, 0x9c98,
  0x9de4, 0x0b6b, 0x0b6c, 0x3385, 0x3454, 0x9fc2, 0x0bcc, 0x9fba,
  0x3455, 0x9fc8, 0x0bcb, 0x34a7, 0x34a8, 0x0bf9,
  /* 0x5c */
  0xa1b9, 0xa1b8, 0xa1a5, 0xa2e2, 0x0c78, 0x0c7a, 0x0c75, 0xa2d9,
  0x0c76, 0x0c77, 0xa2ac, 0xa2dd, 0x0cea, 0x0cee, 0x0ced, 0xa49d,
  0x0cec, 0x370f, 0xa611, 0xa603, 0x0d84, 0x0d85, 0x0d83, 0xa7ee,
  0x0dbc, 0x0dbd, 0x0dd4, 0xaba4, 0xabd8, 0xabdd, 0xabde, 0x0e55,
  0xabe7, 0x0e50, 0x0e4c, 0x0e48, 0xabd4, 0x0e53, 0xabce, 0x0e57,
  0x0e54, 0x0e4e, 0x0e4a, 0x0e51, 0xabf1, 0xabd3, 0x0e49, 0x0e4b,
  0x0e63, 0xabca, 0xabe9, 0x0ea7, 0x0ea6, 0x0ea4, 0xae1a, 0xae41,
  0xf668, 0x3ae4, 0x3ae5, 0xb03d, 0xb040, 0x3c65, 0x3c4e, 0x0f17,
  0xb043, 0x0f16, 0xb03f, 0xb03c, 0x0f63, 0xb221, 0xb220, 0x3d82,
  0xb2c6, 0x0f7b, 0x0f7c, 0xb2d1, 0xb2ca, 0xb38e, 0xb391, 0x0fb0,
  0x3e2d, 0xb4e3, 0xb788, 0x1042, 0xb770, 0x1044, 0xb89d, 0xb99d,
  0xb991, 0xb998, 0xb999, 0x1088, 0x108f, 0x1153,
  /* 0x5d */
  0x115b, 0xbdbf, 0x1159, 0xbdae, 0xbdb1, 0xbdcc, 0xbe04, 0x42ca,
  0xbe16, 0xbdcd, 0x1154, 0x42bc, 0xbde0, 0xbdcb, 0xbdd4, 0xbdc9,
  0xbfff, 0x1199, 0xbffd, 0xc257, 0xc252, 0xc250, 0xc245, 0xc24d,
  0x11f1, 0xc253, 0x11ef, 0xc282, 0xc244, 0xc3ce, 0xc3cf, 0xc3d2,
  0xc402, 0xc54f, 0xc558, 0x1262, 0xc543, 0x1263, 0xc552, 0x1260,
  0x1261, 0x125f, 0xc549, 0xc553, 0xc54d, 0x128d, 0xc684, 0x128e,
  0xc683, 0xc732, 0xc8e2, 0x1309, 0xc8e4, 0xc8d3, 0x1305, 0xc8d5,
  0xc8dd, 0x1303, 0x1306, 0xc8ec, 0xc8e6, 0xc8d2, 0xc8fa, 0xc8da,
  0x1331, 0xca06, 0xca04, 0x134f, 0xca7b, 0xcb04, 0xcb02, 0x1366,
  0x49ff, 0x13c4, 0xcd60, 0x13c3, 0x13c1, 0x13c5, 0xcf07, 0xcf05,
  0xcf0c, 0x1421, 0xcf5a, 0x141f, 0x1422, 0xcf1a, 0x1427, 0x1420,
  0xd18a, 0x146d, 0x146c, 0x146b, 0x146f, 0x1470,
  /* 0x5e */
  0xd18c, 0xd409, 0xd6a6, 0x4ea6, 0xd6ac, 0xd6a9, 0x1542, 0xd88b,
  0x50b6, 0xd88c, 0x1544, 0x1540, 0xd888, 0xd889, 0x153f, 0xd893,
  0x50ab, 0x158b, 0xdb83, 0xdd4f, 0x1625, 0x1628, 0xdf20, 0x5421,
  0xe036, 0xe0e2, 0x1675, 0x1672, 0xe0ee, 0x166f, 0xe0e7, 0xe0e9,
  0x1676, 0x1671, 0x54a7, 0xe0df, 0x1697, 0xe1c7, 0x16d7, 0xe309,
  0x16d6, 0xe301, 0x16d8, 0x16dc, 0x16db, 0x16d4, 0x553e, 0x1713,
  0x1711, 0x1714, 0xe405, 0xe40c, 0xe578, 0xe55d, 0x1751, 0x1750,
  0x1753, 0x1754, 0x1752, 0xe55e, 0xe560, 0xe567, 0x176d, 0xf705,
  0xe6f4, 0x1795, 0x1799, 0xe6f1, 0x179a, 0xe6fa, 0x1793, 0x1797,
  0xe6f8, 0xe6f9, 0xe709, 0xe6fd, 0xe6f7, 0x17dc, 0xe859, 0x17fd,
  0xe960, 0xe968, 0x17fe, 0x1800, 0x1802, 0x1801, 0x1803, 0xe96a,
  0xea14, 0xea3e, 0xeae4, 0x1827, 0x1826, 0x1824,
  /* 0x5f */
  0x184c, 0x58bc, 0x1850, 0x1855, 0x1853, 0x58b7, 0x1852, 0xebd2,
  0x1857, 0x58be, 0x1858, 0x18d6, 0xee58, 0xee50, 0x18d4, 0xee5c,
  0x18da, 0x18d9, 0xcf19, 0x5a62, 0x18d5, 0x18e4, 0xf70e, 0x18dc,
  0x191b, 0x5b8f, 0x1937, 0x1936, 0x194b, 0x5bcb, 0x1966, 0x1976,
  0xf2df, 0x197e, 0x197d, 0x197f, 0x1984, 0x198b, 0xf3d9, 0x1994,
  0x00a1, 0x0111, 0x6566, 0x0210, 0x1ea9, 0x6ae1, 0x6aef, 0x6ae8,
  0x6c33, 0x2013, 0x71fe, 0x0332, 0x21f4, 0x73ef, 0x73ec, 0x75ec,
  0x779c, 0x0420, 0x7799, 0x7870, 0x786e, 0x049d, 0x7b41, 0x26ec,
  0x26ef, 0x7f02, 0x7f01, 0x05a5, 0x801b, 0x8323, 0x8325, 0x8324,
  0x8326, 0x8333, 0x832f, 0x858f, 0x8856, 0x0802, 0x07fe, 0x0801,
  0x2bd6, 0x0803, 0x07ff, 0x8b99, 0x0804, 0xf5ed, 0x084a, 0x8ed9,
  0x0987, 0x93b7, 0x0984, 0x93b6, 0x0985, 0x3009,
  /* 0x60 */
  0x9654, 0x9657, 0x967a, 0x0a59, 0x3179, 0x9b8e, 0x32c6, 0x9b90,
  0x9bb9, 0x0b04, 0x0b49, 0x0b48, 0x9def, 0x0b6d, 0x0bd2, 0x0bd3,
  0x9fe2, 0x0bd1, 0x9fd6, 0x9fd8, 0x9fda, 0x9fde, 0x0be7, 0x0c0a,
  0x35c3, 0xa308, 0xa304, 0xa30a, 0xa30b, 0xa302, 0x0cf0, 0xa4aa,
  0xa4c1, 0x371f, 0xa7d7, 0xa7d9, 0x3865, 0xa7de, 0xa7da, 0x0dbe,
  0x0dbf, 0xa92a, 0x38c6, 0x3a3a, 0xac31, 0x3a36, 0xac2b, 0xac2c,
  0xac29, 0xac2e, 0x0e5e, 0xac27, 0xac28, 0x0e5b, 0xac5f, 0xac30,
  0xac24, 0x3aeb, 0x0eab, 0xae3a, 0x0eac, 0xae39, 0xae40, 0xb080,
  0xb084, 0x0f1f, 0x0f1d, 0xb075, 0xb076, 0x0f1c, 0xb07c, 0x0f1e,
  0xb078, 0xb09b, 0xb07e, 0xb15a, 0x0f64, 0xb22c, 0x3d84, 0xb39c,
  0xb747, 0xb78a, 0x1048, 0x1047, 0xb827, 0xbe4a, 0x115e, 0x1161,
  0xbe27, 0x42e0, 0x42f3, 0xbe2e, 0xbe26, 0xc008,
  /* 0x61 */
  0x11f7, 0xc2bd, 0xc296, 0x11f4, 0x11f8, 0x451e, 0xc2be, 0xc28e,
  0xc574, 0x1264, 0xc580, 0x1292, 0x128f, 0x1290, 0x46b4, 0x1293,
  0x12a8, 0xc73c, 0xc73d, 0x12a9, 0xc73a, 0xc742, 0x46f9, 0xc924,
  0xc906, 0x4844, 0x130e, 0xc915, 0x130f, 0xc902, 0xc90c, 0x130b,
  0xc908, 0xc90a, 0xc905, 0xc91c, 0x1310, 0x1351, 0xca82, 0x1350,
  0xca86, 0x1363, 0xcc03, 0xcd7b, 0x13c7, 0xcd7a, 0x4b71, 0x1424,
  0x1426, 0x4b6e, 0xcf80, 0x4b79, 0xcf58, 0x4bc4, 0x1474, 0x1473,
  0x1472, 0xd1aa, 0xd1ab, 0xd236, 0xd24a, 0x14b1, 0x4d8c, 0xd5d6,
  0x150e, 0x1511, 0x1510, 0x150f, 0x1512, 0x1549, 0x50c9, 0x154f,
  0x154d, 0xd903, 0xd8cf, 0x1555, 0xdb9f, 0xdba2, 0xde2a, 0xde2f,
  0xdf44, 0xdf40, 0x162c, 0x162b, 0xe111, 0xe10f, 0x1679, 0xe10d,
  0xe107, 0xe103, 0x167a, 0x54b0, 0x1699, 0x169a,
  /* 0x62 */
  0xe235, 0x16ae, 0x16af, 0xe304, 0x16e4, 0x16e1, 0x16de, 0x16e6,
  0x16df, 0xe326, 0x16e7, 0x16e2, 0x16e0, 0xe31e, 0x16e5, 0x555a,
  0xe40e, 0x1718, 0xe41d, 0xe41e, 0xe41f, 0x1756, 0xe588, 0x5646,
  0xe58d, 0xe591, 0xe580, 0x176e, 0xe654, 0xe655, 0x179d, 0x17a0,
  0x179c, 0xe725, 0xe71a, 0x17a1, 0x17a2, 0x179f, 0x17df, 0x17de,
  0x57c3, 0x17ea, 0xe988, 0x1806, 0xe97a, 0x1804, 0x580f, 0xe980,
  0xeb1e, 0xebfc, 0xec25, 0x185f, 0x58f4, 0x58fa, 0x185c, 0xec0b,
  0x185e, 0xec06, 0xec04, 0x58dd, 0x1859, 0xebf9, 0xec00, 0x1864,
  0x185d, 0x1862, 0xec02, 0x1865, 0xec07, 0x58ed, 0x185b, 0x58ef,
  0xeeb5, 0x18dd, 0xee87, 0x18df, 0xee93, 0xf70f, 0x18e2, 0xeebe,
  0xf066, 0x1927, 0xf0c7, 0xf0cf, 0x5b96, 0x193a, 0x193c, 0xf13d,
  0x1939, 0xf13c, 0xf147, 0x193d, 0x193b, 0x5bb3,
  /* 0x63 */
  0x194c, 0xf1c3, 0x1968, 0x5be2, 0xf31b, 0x1980, 0x1985, 0xf3c9,
  0x1995, 0xf3dd, 0x1996, 0xf493, 0x5c8f, 0x603d, 0x00a4, 0x0112,
  0x1eb1, 0x0225, 0x6ee6, 0x2141, 0x0337, 0x73f7, 0x77b0, 0x77ae,
  0x5dfd, 0x0468, 0x0467, 0x049e, 0x7c9f, 0x7c9e, 0x7f30, 0x05aa,
  0x7f4f, 0x05a9, 0x05a4, 0x7f27, 0x7f51, 0x0671, 0x066f, 0x8351,
  0x8354, 0x8356, 0x8527, 0x06ad, 0x8524, 0x2bf6, 0x080c, 0x2bf2,
  0x080b, 0x8bec, 0x8bc4, 0x080f, 0x0879, 0x93f8, 0x93f6, 0x93f7,
  0x93ed, 0x098d, 0x098f, 0x93f4, 0x93ef, 0x098e, 0x0a0c, 0x967f,
  0x96a2, 0x967e, 0x0aa6, 0x99c5, 0x0aa3, 0x0aa4, 0x0aa5, 0x3388,
  0x0b6e, 0x9ff1, 0x9ff2, 0x0bfa, 0xa12f, 0x0c7c, 0x0c7e, 0x0c7b,
  0x0c7d, 0xa323, 0xa329, 0x0c8d, 0x0cf4, 0x0cf3, 0xa61b, 0xa7eb,
  0x0d89, 0xa7ea, 0xa933, 0x0dc0, 0xac63, 0x0e65,
  /* 0x64 */
  0xac92, 0xac65, 0x0ead, 0x0f25, 0xb0a0, 0xf670, 0xb15e, 0x0fc9,
  0xb7aa, 0x104a, 0xb7a9, 0x4067, 0x1089, 0xbe9c, 0x1166, 0x1170,
  0xbe92, 0x116d, 0x1169, 0x1167, 0xbe86, 0x1172, 0x430e, 0x116e,
  0xbe83, 0x119c, 0x11fc, 0x11fd, 0x1204, 0x11ff, 0xf6c2, 0x11fe,
  0x1200, 0xc2ce, 0x1266, 0x1269, 0xc593, 0x12aa, 0x12ab, 0x1317,
  0xc92e, 0xc927, 0xc928, 0x1315, 0x485e, 0x1312, 0x4a0e, 0xcc18,
  0xcc16, 0xcd8d, 0x13ca, 0xcd8e, 0x13c9, 0x13cb, 0xcd90, 0xcd8f,
  0xcf81, 0x1429, 0x1428, 0xcf8a, 0xcf8c, 0xd08d, 0x1440, 0x1475,
  0x1476, 0xd1b2, 0x1488, 0xd5d9, 0x4eb6, 0x1557, 0x5101, 0xd90d,
  0x155f, 0xd913, 0x511d, 0x1558, 0x155b, 0xd91b, 0x512f, 0xdbac,
  0x15b3, 0xdbb3, 0x15ef, 0xdf5e, 0x1630, 0xdf60, 0xdf68, 0xdf63,
  0xdf69, 0xdf67, 0x1641, 0x164b, 0xe128, 0x167d,
  /* 0x65 */
  0xe12e, 0xe130, 0x167c, 0xe126, 0xe131, 0xe141, 0x54e0, 0xe1da,
  0x54db, 0xf6fa, 0xe20b, 0x5561, 0xe334, 0xe333, 0x16e8, 0x16ea,
  0x16e9, 0xe339, 0xe33b, 0xe340, 0xe430, 0x171b, 0xe432, 0xe437,
  0x1755, 0x564a, 0x1759, 0x1758, 0xe581, 0xe59f, 0xe5a7, 0x17a4,
  0x17a3, 0xe744, 0xe747, 0xe748, 0xe73d, 0x5733, 0x17a7, 0xe749,
  0x17e0, 0xe880, 0xe9a0, 0xe99d, 0x1808, 0x180a, 0x1809, 0xe99c,
  0xea47, 0xeb07, 0x1871, 0x590f, 0x186c, 0xec49, 0x5911, 0xec44,
  0x5903, 0x5901, 0x186e, 0xecdf, 0x5916, 0xec4c, 0x5dfd, 0xec4f,
  0x18e0, 0x18ee, 0xeec1, 0x18eb, 0xeeb9, 0xeecb, 0xeecf, 0xeec4,
  0x5a93, 0x18ea, 0x18ef, 0x18e7, 0xeeca, 0xeec3, 0xf0d0, 0xf151,
  0x1948, 0x1949, 0xf1a8, 0xf1c7, 0xf1c6, 0x194d, 0xf1ca, 0xf202,
  0x1955, 0xf25d, 0xf25a, 0x196a, 0x196c, 0xf259,
  /* 0x66 */
  0x196b, 0xf2cc, 0xf31c, 0xf3cc, 0x1998, 0x1999, 0x1997, 0xf3e8,
  0xf3ec, 0xf3ea, 0xf4ad, 0xf4b0, 0x605f, 0x6058, 0x6057, 0x1d5b,
  0x6793, 0x0216, 0x6b3b, 0x1ebf, 0x6b34, 0x6ef2, 0x0339, 0x73fd,
  0x751d, 0x0425, 0x23ce, 0x7a04, 0x7b48, 0x7f58, 0x834f, 0x0674,
  0x836e, 0x8372, 0x06ae, 0x852e, 0x8bfc, 0x8bf4, 0x9036, 0x940e,
  0x0992, 0x0994, 0x9414, 0x0995, 0x9419, 0x0a0d, 0x96a6, 0x0a25,
  0x9bc9, 0x9bc0, 0x9bcc, 0x9c1b, 0x9caa, 0x9ca8, 0xa003, 0x0bd5,
  0x0bd6, 0x34ac, 0x0be8, 0xa135, 0x0c7f, 0x35d2, 0x0cf5, 0x0cf6,
  0x0cf7, 0xa4d9, 0x0d24, 0x0d8d, 0x0d8a, 0xa93f, 0xa93d, 0x0e6c,
  0x0e66, 0x0e6a, 0xac8b, 0x0e67, 0x0e6d, 0x0e68, 0x3a52, 0xac68,
  0xac8a, 0xae58, 0xae57, 0xb0ce, 0xb0bc, 0xb0c0, 0xb0c1, 0xb0bf,
  0xb0ab, 0xb15f, 0x0f65, 0xb3a6, 0x0f9a, 0xb429,
  /* 0x67 */
  0x0fb1, 0x104b, 0x104d, 0x104c, 0x104e, 0xb7b8, 0x1173, 0x1175,
  0xbedd, 0xbed6, 0xf6b1, 0xbed5, 0xbee7, 0xbed8, 0xc2ec, 0x1203,
  0xc300, 0xc307, 0xc2fd, 0xc2f1, 0xc2ff, 0xc5aa, 0xc5b0, 0xc948,
  0x131e, 0xc953, 0x4873, 0xc94d, 0x1319, 0x131c, 0x131a, 0x131d,
  0x4876, 0xc943, 0xc950, 0x1343, 0x1352, 0xca8c, 0xcc27, 0x1395,
  0xcd99, 0x13cc, 0xcfb1, 0x142b, 0xcfb0, 0xcfaa, 0xcfac, 0x142a,
  0x4bc7, 0x1477, 0xd1c8, 0xd1ca, 0xd442, 0xd5e0, 0xd6e7, 0xd6e8,
  0xd6e6, 0x1513, 0x1514, 0x5134, 0xd95b, 0xd956, 0x155d, 0xd95a,
  0x1560, 0x513e, 0x1562, 0xdab8, 0xdbc5, 0x15b2, 0x15f0, 0xde48,
  0xdf7d, 0xdf7c, 0xdf81, 0xdf82, 0xdf62, 0x164c, 0xe145, 0x1682,
  0x54bc, 0x1681, 0x169b, 0xe1e3, 0x16a4, 0x16ee, 0x16ec, 0xe350,
  0x16ed, 0xe34f, 0x16f0, 0x16ef, 0xe439, 0x171d,
  /* 0x68 */
  0xe43a, 0x1760, 0x175e, 0x175d, 0xe5c1, 0xe74e, 0xe76e, 0x17b1,
  0x17ab, 0x17ac, 0x17ad, 0xe771, 0x17ae, 0xe88c, 0xe889, 0x17e2,
  0xe8e5, 0xe9b3, 0xe9b6, 0xe9b4, 0xea4d, 0x5839, 0xeb13, 0xec78,
  0x592a, 0x187b, 0x5926, 0x1878, 0x1875, 0x5927, 0xec72, 0x18f2,
  0x18f4, 0x18f3, 0x5ac0, 0x5ac9, 0xf075, 0xf0da, 0xf0d7, 0x193f,
  0x193e, 0x1940, 0x194e, 0x1957, 0x1959, 0x1958, 0xf716, 0xf269,
  0xf267, 0x196e, 0xf266, 0xf26f, 0xf271, 0x5bec, 0xf2cf, 0xf323,
  0x1981, 0x1986, 0xf3a5, 0x198f, 0xf3fe, 0xf3fb, 0xf3fd, 0x5c68,
  0x199b, 0x19b1, 0x19b3, 0x6b6d, 0x033a, 0x7405, 0x7520, 0x0427,
  0x77c8, 0x77c9, 0x046a, 0x05ac, 0x8627, 0x0818, 0x8c1e, 0x8d36,
  0x084c, 0x943d, 0x0996, 0x99d7, 0x0b4a, 0x9cad, 0x9e00, 0x0c81,
  0xa33a, 0x0c83, 0x0cf9, 0x0cf8, 0xa626, 0x0d8e,
  /* 0x69 */
  0x0d8f, 0x0dc1, 0xacb7, 0xacb6, 0xacc0, 0x0e70, 0xac9b, 0x0e71,
  0xb0d8, 0x0f2a, 0x0f2d, 0x0f7d, 0x3e32, 0x3e31, 0xb7c0, 0x104f,
  0xb7bf, 0xb9bf, 0x1090, 0xbf20, 0xbed0, 0xbf0e, 0x1179, 0xbf1d,
  0xbf1e, 0xbf15, 0xbf14, 0xc31e, 0xc32c, 0x1205, 0xc5b9, 0xc5b8,
  0xc5b6, 0xc69e, 0xc69c, 0xc74d, 0x46fd, 0xc96e, 0xc960, 0x1321,
  0xc964, 0xc962, 0xb0e5, 0x1332, 0xcda3, 0x13cd, 0x13cf, 0xd1d2,
  0xd1d5, 0x4d8e, 0x1516, 0x1515, 0x15b5, 0x1608, 0xde55, 0x1632,
  0xdf93, 0x1633, 0x1634, 0x163c, 0xe156, 0x54c2, 0xe1e9, 0x169c,
  0xe245, 0x16f4, 0x16f2, 0xe47c, 0x1762, 0xe5d3, 0x1761, 0x1764,
  0x17b5, 0x574b, 0x17b4, 0xe78e, 0xe897, 0x17e3, 0xe89b, 0xe899,
  0x581c, 0x180e, 0xe9cf, 0x581b, 0xea59, 0x182c, 0x182b, 0xeb20,
  0xeb23, 0xeb2a, 0x1885, 0x1881, 0x187e, 0x1883,
  /* 0x6a */
  0x1880, 0xecb0, 0x5942, 0xef2f, 0x5ad4, 0x18fb, 0x18f7, 0xef32,
  0xef43, 0xef3f, 0xef39, 0x18f8, 0xef30, 0xf0dd, 0xf1da, 0xf1db,
  0x195a, 0xf284, 0xf27f, 0xf272, 0xf280, 0xf2ee, 0x1978, 0xf32a,
  0xf322, 0xf371, 0xf3cd, 0x199d, 0x199c, 0xf40f, 0xf418, 0x606a,
  0x00a9, 0x00bf, 0x1ed0, 0x1ecf, 0x740c, 0x23da, 0x7ca6, 0x0677,
  0x0676, 0x8537, 0x06bb, 0x29ea, 0x8ee2, 0x099b, 0x96bc, 0x0a0f,
  0x0a5b, 0x9bd5, 0x0b4c, 0x0b6f, 0x0bd9, 0xa012, 0x0c82, 0xa34b,
  0xa341, 0xa3a1, 0xa4ec, 0x0e74, 0x0e72, 0xacd4, 0xacd8, 0xacd9,
  0x0e73, 0xacda, 0xae6c, 0xae6d, 0x0eb1, 0x0f2e, 0xb0e7, 0xb0eb,
  0xb0ec, 0xb162, 0x0f4e, 0xb42b, 0xb50d, 0xb7cd, 0xb9c3, 0xbf3f,
  0xbf3c, 0xbf3e, 0xbf3d, 0xbf3a, 0xbf38, 0xc344, 0xc345, 0x120b,
  0xc348, 0xc350, 0x126c, 0x4889, 0xc978, 0xc979,
  /* 0x6b */
  0x138b, 0xcc3e, 0x13d0, 0x142d, 0xd454, 0x14e4, 0x1571, 0xd9b9,
  0x5158, 0x156f, 0xde5f, 0x1687, 0x16a5, 0xe372, 0xe375, 0x171e,
  0x1765, 0x17b9, 0x17b7, 0x17b8, 0x17e4, 0xe8a3, 0xe8a5, 0xe9dc,
  0xe9dd, 0xea5a, 0x188c, 0x1889, 0x188a, 0xecdb, 0xecdc, 0x188b,
  0xefab, 0xef84, 0xef76, 0x1901, 0x18fe, 0x5ae7, 0x1903, 0x1906,
  0xef83, 0x5aea, 0x5af1, 0xf07f, 0x191d, 0x1943, 0xf173, 0xf1ad,
  0xf1b0, 0x194f, 0xf20f, 0xf20c, 0x195b, 0x1970, 0xf379, 0x1988,
  0xf377, 0xf37a, 0x1989, 0x5c44, 0xf432, 0xf427, 0xf42a, 0xf42c,
  0x5c6d, 0xf428, 0xf429, 0xf438, 0x642f, 0x6b7b, 0x740d, 0x23d9,
  0x77d6, 0x77d5, 0x83a1, 0x8c4c, 0x099e, 0x099f, 0x0aa7, 0x0b4b,
  0x0bdb, 0x0bda, 0xa0c0, 0x35d6, 0x0c8e, 0x0e76, 0xacf4, 0x0f30,
  0x0f2f, 0xb0f0, 0x0f66, 0xb23f, 0x117e, 0xc35d,
  /* 0x6c */
  0xc372, 0xc362, 0x453a, 0xc366, 0x4675, 0x126f, 0xc988, 0x13d1,
  0x142f, 0xcfe8, 0xcfe4, 0x14b2, 0x1518, 0x1517, 0xd6ff, 0x1576,
  0xdfaf, 0xdfae, 0x164f, 0x1689, 0xe1f2, 0xe248, 0xe381, 0xe37e,
  0x16f5, 0x171f, 0xe452, 0xe5ef, 0x575d, 0x17e5, 0xe8ad, 0xe9e6,
  0x1810, 0xe9ed, 0x180f, 0xe9e9, 0xea61, 0xea60, 0xeb33, 0x182f,
  0x1830, 0x5964, 0xed0b, 0xed08, 0x1893, 0x1894, 0xed07, 0x1907,
  0x1909, 0x1908, 0xefca, 0x190b, 0xefc6, 0x5b0a, 0xf084, 0xf0eb,
  0xf17d, 0x1950, 0x1971, 0xf29b, 0xf2a2, 0xf2a1, 0xf2a0, 0xf29c,
  0x197b, 0x197c, 0xf380, 0x5c73, 0xf440, 0x19a1, 0xf439, 0xf43c,
  0x19a0, 0x19a2, 0x64ca, 0x021b, 0x6b89, 0x0282, 0x853c, 0x8d40,
  0x9463, 0x9469, 0x0be9, 0xa353, 0x0c84, 0x35e1, 0xa817, 0xa81a,
  0xad00, 0x0eb3, 0x0f34, 0x0f33, 0x1180, 0xc36f,
  /* 0x6d */
  0xc6ab, 0x12ad, 0xc991, 0x1344, 0x1355, 0xcdb1, 0x13d2, 0xcfef,
  0xdbf1, 0xdbf2, 0xdfb7, 0xdfb5, 0x168a, 0xe386, 0xe45a, 0x1767,
  0xe7c6, 0xe7cb, 0x17e6, 0xe8b2, 0x1813, 0xe9f3, 0x582d, 0xed27,
  0x1897, 0x5b0c, 0xefd5, 0xefd8, 0x190c, 0xefec, 0xf087, 0xf0f2,
  0x1946, 0x195c, 0x1974, 0x1972, 0xf2ad, 0xf2b0, 0xf2fd, 0x5c1f,
  0xf387, 0xf44a, 0x19a4, 0x19a3, 0xf44e, 0xf449, 0xf451, 0xf44d,
  0x19b4, 0x6072, 0x0136, 0x7416, 0x8c6d, 0x8d41, 0x08b2, 0x9471,
  0x9474, 0x0b16, 0x3a70, 0x0e77, 0xae7c, 0x117f, 0xbf8a, 0xc756,
  0x147d, 0x5179, 0xd9fa, 0x544a, 0x168c, 0xe45b, 0x1768, 0x17be,
  0x1815, 0xeff5, 0xeff0, 0xf0f3, 0xf17f, 0xf213, 0x1975, 0x19a5,
  0x7419, 0x7f85, 0x83b0, 0x9477, 0xa4ff, 0x0e78, 0x0f35, 0x3c9d,
  0x1182, 0xbf87, 0x1183, 0xbf8b, 0x1271, 0xc99e,
  /* 0x6e */
  0x147e, 0x168e, 0xe38f, 0x5660, 0x1769, 0xe639, 0xe7d4, 0xe8f1,
  0xea02, 0xea6b, 0xeb40, 0x189a, 0x189b, 0xf010, 0xf2be, 0xf2b9,
  0x1990, 0xf464, 0x5c9e, 0x9be9, 0xbf90, 0x1186, 0x1185, 0xc01c,
  0x120e, 0xc392, 0xc6ae, 0xc9a3, 0x1519, 0xdfc9, 0x17c0, 0xe7d8,
  0xeb44, 0xf024, 0x5bf8, 0x5c3a, 0x5c7d, 0xf470, 0xf4d3, 0x0c0d,
  0x1816, 0xf2c3, 0x19a9, 0x19aa, 0x0c85, 0xad21, 0xb9ca, 0xc39c,
  0xea73, 0xf186, 0xf3c1, 0xea09, 0x5c96, 0xf4d5, 0x17c2, 0x1831,
  0x1911, 0x19ab, 0x189c, 0xdfd4,
};

static const ucs4_t cns11643_4b_2uni_upages[248] = {
  0x03400, 0x03500, 0x03600, 0x03700, 0x03800, 0x03900, 0x03a00, 0x03b00,
  0x03c00, 0x03d00, 0x03e00, 0x03f00, 0x04000, 0x04100, 0x04200, 0x04300,
  0x04400, 0x04500, 0x04600, 0x04700, 0x04800, 0x04900, 0x04a00, 0x04b00,
  0x04c00, 0x04d00, 0x05000, 0x05100, 0x05200, 0x05300, 0x05600, 0x05800,
  0x05900, 0x05b00, 0x05c00, 0x05d00, 0x05e00, 0x05f00, 0x06100, 0x06400,
  0x06500, 0x06600, 0x06900, 0x06a00, 0x06b00, 0x06f00, 0x07000, 0x07100,
  0x07200, 0x07300, 0x07400, 0x07500, 0x07600, 0x07700, 0x07800, 0x07900,
  0x07a00, 0x07b00, 0x07c00, 0x07d00, 0x07e00, 0x07f00, 0x08000, 0x08100,
  0x08200, 0x08400, 0x08500, 0x08600, 0x08700, 0x08800, 0x08900, 0x08a00,
  0x08b00, 0x08c00, 0x08d00, 0x08e00, 0x08f00, 0x09000, 0x09100, 0x09200,
  0x09300, 0x09400, 0x09500, 0x09600, 0x09700, 0x09800, 0x09900, 0x09a00,
  0x09b00, 0x09c00, 0x09d00, 0x09e00, 0x09f00, 0x0ff00, 0x20000, 0x20300,
  0x20400, 0x20500, 0x20600, 0x20700, 0x20800, 0x20900, 0x20a00, 0x20b00,
  0x20e00, 0x20f00, 0x21000, 0x21100, 0x21200, 0x21300, 0x21400, 0x21500,
  0x21600, 0x21800, 0x21900, 0x21a00, 0x21b00, 0x21c00, 0x21e00, 0x21f00,
  0x22100, 0x22200, 0x22300, 0x22400, 0x22500, 0x22700, 0x22800, 0x22900,
  0x22a00, 0x22c00, 0x22d00, 0x22e00, 0x22f00, 0x23000, 0x23100, 0x23200,
  0x23300, 0x23500, 0x23600, 0x23700, 0x23800, 0x23900, 0x23a00, 0x23b00,
  0x23c00, 0x23e00, 0x23f00, 0x24000, 0x24100, 0x24300, 0x24400, 0x24500,
  0x24600, 0x24800, 0x24900, 0x24a00, 0x24b00, 0x24c00, 0x24d00, 0x24e00,
  0x24f00, 0x25000, 0x25200, 0x25300, 0x25500, 0x25600, 0x25700, 0x25800,
  0x25900, 0x25a00, 0x25b00, 0x25c00, 0x25d00, 0x25e00, 0x25f00, 0x26000,
  0x26100, 0x26200, 0x26300, 0x26400, 0x26500, 0x26600, 0x26700, 0x26800,
  0x26900, 0x26a00, 0x26c00, 0x26d00, 0x26e00, 0x26f00, 0x27000, 0x27100,
  0x27200, 0x27300, 0x27400, 0x27500, 0x27600, 0x27700, 0x27800, 0x27900,
  0x27a00, 0x27b00, 0x27c00, 0x27d00, 0x27e00, 0x27f00, 0x28000, 0x28100,
  0x28200, 0x28300, 0x28400, 0x28500, 0x28600, 0x28700, 0x28800, 0x28900,
  0x28a00, 0x28b00, 0x28c00, 0x28d00, 0x28e00, 0x28f00, 0x29000, 0x29100,
  0x29200, 0x29300, 0x29400, 0x29500, 0x29600, 0x29700, 0x29800, 0x29900,
  0x29a00, 0x29b00, 0x29c00, 0x29d00, 0x29e00, 0x29f00, 0x2a000, 0x2a100,
  0x2a200, 0x2a300, 0x2a400, 0x2a500, 0x2a600, 0x2f800, 0x2f900, 0x2fa00,
};

