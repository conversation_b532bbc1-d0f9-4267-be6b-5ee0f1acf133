{"recentFiles": [{"basename": "工作总结", "path": "个人文档/工作总结.md"}, {"basename": "日志", "path": "个人文档/日志.mdenc"}, {"basename": "Accounts", "path": "个人文档/Accounts.mdenc"}, {"basename": "项目账号信息", "path": "个人文档/项目账号信息.mdenc"}, {"basename": "日志模板", "path": "templates/日志模板.md"}, {"basename": "技术方案模板", "path": "templates/技术方案模板.md"}, {"basename": "个券可投额度查询技术方案", "path": "业务项目/固收做市/版本记录/release3.10/个券可投额度查询技术方案.md"}, {"basename": "手动入库债券管理技术方案", "path": "业务项目/固收做市/版本记录/release3.10/手动入库债券管理技术方案.md"}, {"basename": "未命名", "path": "个人文档/未命名.md"}, {"basename": "客户报价管理报价分析兼容QT下行数据", "path": "业务项目/固收做市/版本记录/release3.5/客户报价管理报价分析兼容QT下行数据.md"}, {"basename": "测试线资金账号获取-数仓TIDB", "path": "业务项目/股权激励/测试线资金账号获取-数仓TIDB.md"}, {"basename": "开户链接测试", "path": "业务项目/股权激励/测试与验收文件/开户链接测试.md"}, {"basename": "未命名", "path": "SQL/未命名.md"}, {"basename": "行权业务流程", "path": "业务项目/股权激励/行权业务流程.md"}, {"basename": "运营库同步", "path": "业务项目/股权激励/运营库同步.md"}, {"basename": "开户链接生成逻辑", "path": "业务项目/企业财富管家/开户链接生成逻辑.md"}, {"basename": "开户链接", "path": "股权激励/开户链接.md"}, {"basename": "融资行权测试", "path": "股权激励/融资行权测试.md"}, {"basename": "测试线资金账号获取", "path": "work/projects/股权激励/测试线资金账号获取.md"}], "omittedPaths": [], "omittedTags": [], "omitBookmarks": false}