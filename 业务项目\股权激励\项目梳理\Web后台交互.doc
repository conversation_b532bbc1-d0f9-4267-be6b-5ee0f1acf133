Message-ID: <352774539.9820.1660197940882.JavaMail.EM-$@EM->
Subject: Exported From Confluence
MIME-Version: 1.0
Content-Type: multipart/related; 
	boundary="----=_Part_9819_2015950987.1660197940882"

------=_Part_9819_2015950987.1660197940882
Content-Type: text/html; charset=UTF-8
Content-Transfer-Encoding: quoted-printable
Content-Location: file:///C:/exported.html

<html xmlns:o=3D'urn:schemas-microsoft-com:office:office'
      xmlns:w=3D'urn:schemas-microsoft-com:office:word'
      xmlns:v=3D'urn:schemas-microsoft-com:vml'
      xmlns=3D'urn:w3-org-ns:HTML'>
<head>
    <meta http-equiv=3D"Content-Type" content=3D"text/html; charset=3Dutf-8=
">
    <title>Web=E5=90=8E=E5=8F=B0=E4=BA=A4=E4=BA=92</title>
    <!--[if gte mso 9]>
    <xml>
        <o:OfficeDocumentSettings>
            <o:TargetScreenSize>1024x640</o:TargetScreenSize>
            <o:PixelsPerInch>72</o:PixelsPerInch>
            <o:AllowPNG/>
        </o:OfficeDocumentSettings>
        <w:WordDocument>
            <w:View>Print</w:View>
            <w:Zoom>90</w:Zoom>
            <w:DoNotOptimizeForBrowser/>
        </w:WordDocument>
    </xml>
    <![endif]-->
    <style>
                <!--
        @page Section1 {
            size: 8.5in 11.0in;
            margin: 1.0in;
            mso-header-margin: .5in;
            mso-footer-margin: .5in;
            mso-paper-source: 0;
        }

        table {
            border: solid 1px;
            border-collapse: collapse;
        }

        table td, table th {
            border: solid 1px;
            padding: 5px;
        }

        td {
            page-break-inside: avoid;
        }

        tr {
            page-break-after: avoid;
        }

        div.Section1 {
            page: Section1;
        }

        /* Confluence print stylesheet. Common to all themes for print medi=
a */
/* Full of !important until we improve batching for print CSS */

@media print {
    #main {
        padding-bottom: 1em !important; /* The default padding of 6em is to=
o much for printouts */
    }

    body {
        font-family: Arial, Helvetica, FreeSans, sans-serif;
        font-size: 10pt;
        line-height: 1.2;
    }

    body, #full-height-container, #main, #page, #content, .has-personal-sid=
ebar #content {
        background: #fff !important;
        color: #000 !important;
        border: 0 !important;
        width: 100% !important;
        height: auto !important;
        min-height: auto !important;
        margin: 0 !important;
        padding: 0 !important;
        display: block !important;
    }

    a, a:link, a:visited, a:focus, a:hover, a:active {
        color: #000;
    }

    #content h1,
    #content h2,
    #content h3,
    #content h4,
    #content h5,
    #content h6 {
        font-family: Arial, Helvetica, FreeSans, sans-serif;
        page-break-after: avoid;
    }

    pre {
        font-family: Monaco, "Courier New", monospace;
    }

    #header,
    .aui-header-inner,
    #navigation,
    #sidebar,
    .sidebar,
    #personal-info-sidebar,
    .ia-fixed-sidebar,
    .page-actions,
    .navmenu,
    .ajs-menu-bar,
    .noprint,
    .inline-control-link,
    .inline-control-link a,
    a.show-labels-editor,
    .global-comment-actions,
    .comment-actions,
    .quick-comment-container,
    #addcomment {
        display: none !important;
    }

    /* CONF-28544 cannot print multiple pages in IE */
    #splitter-content {
        position: relative !important;
    }

    .comment .date::before {
        content: none !important; /* remove middot for print view */
    }

    h1.pagetitle img {
        height: auto;
        width: auto;
    }

    .print-only {
        display: block;
    }

    #footer {
        position: relative !important; /* CONF-17506 Place the footer at en=
d of the content */
        margin: 0;
        padding: 0;
        background: none;
        clear: both;
    }

    #poweredby {
        border-top: none;
        background: none;
    }

    #poweredby li.print-only {
        display: list-item;
        font-style: italic;
    }

    #poweredby li.noprint {
        display: none;
    }

    /* no width controls in print */
    .wiki-content .table-wrap,
    .wiki-content p,
    .panel .codeContent,
    .panel .codeContent pre,
    .image-wrap {
        overflow: visible !important;
    }

    /* TODO - should this work? */
    #children-section,
    #comments-section .comment,
    #comments-section .comment .comment-body,
    #comments-section .comment .comment-content,
    #comments-section .comment p {
        page-break-inside: avoid;
    }

    #page-children a {
        text-decoration: none;
    }

    /**
     hide twixies

     the specificity here is a hack because print styles
     are getting loaded before the base styles. */
    #comments-section.pageSection .section-header,
    #comments-section.pageSection .section-title,
    #children-section.pageSection .section-header,
    #children-section.pageSection .section-title,
    .children-show-hide {
        padding-left: 0;
        margin-left: 0;
    }

    .children-show-hide.icon {
        display: none;
    }

    /* personal sidebar */
    .has-personal-sidebar #content {
        margin-right: 0px;
    }

    .has-personal-sidebar #content .pageSection {
        margin-right: 0px;
    }

    .no-print, .no-print * {
        display: none !important;
    }
}
-->
    </style>
</head>
<body>
    <h1>Web=E5=90=8E=E5=8F=B0=E4=BA=A4=E4=BA=92</h1>
    <div class=3D"Section1">
        <h3 id=3D"Web=E5=90=8E=E5=8F=B0=E4=BA=A4=E4=BA=92-=E4=B8=BB=E8=A6=
=81=E6=95=B0=E6=8D=AE=E6=9F=A5=E8=AF=A2">=E4=B8=BB=E8=A6=81=E6=95=B0=E6=8D=
=AE=E6=9F=A5=E8=AF=A2</h3>
<div class=3D"table-wrap">
<table class=3D"relative-table confluenceTable" style=3D"width: 83.5443%;">
<colgroup>
<col style=3D"width: 17.1276%;">
<col style=3D"width: 18.2872%;">
<col style=3D"width: 22.926%;">
<col style=3D"width: 10.9723%;">
<col style=3D"width: 30.6869%;">
</colgroup>
<tbody>
<tr>
<th class=3D"confluenceTh">=E6=8E=A7=E5=88=B6=E5=99=A8=E7=B1=BB</th>
<th class=3D"confluenceTh">=E6=96=B9=E6=B3=95=E5=90=8D</th>
<th class=3D"confluenceTh">PATH</th>
<th class=3D"confluenceTh">=E7=9B=B8=E5=85=B3=E6=95=B0=E6=8D=AE=E5=BA=93</t=
h>
<th colspan=3D"1" class=3D"confluenceTh">=E8=AF=B4=E6=98=8E</th>
</tr>
<tr>
<td class=3D"confluenceTd"><p>EmbaseController</p></td>
<td class=3D"confluenceTd">query</td>
<td class=3D"confluenceTd">/ei/Embase/query</td>
<td class=3D"confluenceTd">choice</td>
<td colspan=3D"1" class=3D"confluenceTd">=E5=85=AC=E5=8F=B8=E6=8E=88=E4=BA=
=88=E7=99=BB=E8=AE=B0=E6=97=A5=E6=9F=A5=E8=AF=A2=E5=B8=82=E4=BB=B7</td>
</tr>
<tr>
<td class=3D"confluenceTd">DataController</td>
<td class=3D"confluenceTd">GetDataStatus</td>
<td class=3D"confluenceTd">/ei/dataStatus/GetDataStatus</td>
<td class=3D"confluenceTd">lcims</td>
<td colspan=3D"1" class=3D"confluenceTd">=E6=9F=A5=E8=AF=A2=E6=BF=80=E5=8A=
=B1=E8=AE=A1=E5=88=92=E7=8A=B6=E6=80=81</td>
</tr>
<tr>
<td class=3D"confluenceTd">GrantController</td>
<td class=3D"confluenceTd">qryEiStaff</td>
<td class=3D"confluenceTd">/ei/grant/qryEiStaff</td>
<td class=3D"confluenceTd"><span>lcims</span></td>
<td colspan=3D"1" class=3D"confluenceTd">=E6=9F=A5=E8=AF=A2=E6=BF=80=E5=8A=
=B1=E5=AF=B9=E8=B1=A1</td>
</tr>
<tr>
<td class=3D"confluenceTd"><br></td>
<td class=3D"confluenceTd">importShareStaff</td>
<td class=3D"confluenceTd">/ei/grant/importShareStaff</td>
<td class=3D"confluenceTd">lcims</td>
<td colspan=3D"1" class=3D"confluenceTd">=E5=AF=BC=E5=85=A5=E6=8E=88=E4=BA=
=88=E5=AF=B9=E8=B1=A1=E6=95=B0=E6=8D=AE</td>
</tr>
<tr>
<td class=3D"confluenceTd"><br></td>
<td class=3D"confluenceTd">importShareStaffHttp</td>
<td class=3D"confluenceTd">/ei/grant/importShareStaffHttp</td>
<td class=3D"confluenceTd"><span>lcims</span></td>
<td colspan=3D"1" class=3D"confluenceTd">=E5=AF=BC=E5=85=A5=E6=8E=88=E4=BA=
=88=E5=AF=B9=E8=B1=A1=E6=95=B0=E6=8D=AE</td>
</tr>
<tr>
<td class=3D"confluenceTd"><br></td>
<td class=3D"confluenceTd">clearImportHttp</td>
<td class=3D"confluenceTd">/ei/grant/clearImportHttp</td>
<td class=3D"confluenceTd"><span>lcims</span></td>
<td colspan=3D"1" class=3D"confluenceTd">=E6=B8=85=E9=99=A4=E5=AF=BC=E5=85=
=A5=E6=8E=88=E4=BA=88=E5=AF=B9=E8=B1=A1=E6=95=B0=E6=8D=AE</td>
</tr>
<tr>
<td class=3D"confluenceTd"><br></td>
<td class=3D"confluenceTd">mdShareStaffBatch</td>
<td class=3D"confluenceTd">/ei/grant/<span>mdShareStaffBatch</span></td>
<td class=3D"confluenceTd"><span>lcims</span></td>
<td colspan=3D"1" class=3D"confluenceTd">=E6=89=B9=E9=87=8F=E6=9B=B4=E6=96=
=B0=E6=8E=88=E4=BA=88=E4=BF=A1=E6=81=AF</td>
</tr>
<tr>
<td class=3D"confluenceTd"><br></td>
<td class=3D"confluenceTd">delShareStaffBatch</td>
<td class=3D"confluenceTd">/ei/grant/delShareStaffBatch</td>
<td class=3D"confluenceTd"><span>lcims</span></td>
<td colspan=3D"1" class=3D"confluenceTd">=E5=88=A0=E9=99=A4=E6=8E=88=E4=BA=
=88=E4=BF=A1=E6=81=AF</td>
</tr>
<tr>
<td class=3D"confluenceTd"><br></td>
<td class=3D"confluenceTd">addFileOptRecord</td>
<td class=3D"confluenceTd">/ei/grant/addFileOptRecord</td>
<td class=3D"confluenceTd"><span>lcims</span></td>
<td colspan=3D"1" class=3D"confluenceTd">=E4=BF=9D=E5=AD=98=E6=96=87=E4=BB=
=B6=E6=93=8D=E4=BD=9C=E8=AE=B0=E5=BD=95</td>
</tr>
<tr>
<td colspan=3D"1" class=3D"confluenceTd"><br></td>
<td colspan=3D"1" class=3D"confluenceTd">qryFileOptRecord</td>
<td colspan=3D"1" class=3D"confluenceTd">/ei/grant/qryFileOptRecord</td>
<td colspan=3D"1" class=3D"confluenceTd"><span>lcims</span></td>
<td colspan=3D"1" class=3D"confluenceTd">=E6=9F=A5=E8=AF=A2=E6=96=87=E4=BB=
=B6=E6=93=8D=E4=BD=9C=E8=AE=B0=E5=BD=95</td>
</tr>
<tr>
<td colspan=3D"1" class=3D"confluenceTd">PlanController</td>
<td colspan=3D"1" class=3D"confluenceTd">addEiPlan</td>
<td colspan=3D"1" class=3D"confluenceTd">/ei/plan/addEiPlan</td>
<td colspan=3D"1" class=3D"confluenceTd"><span>lcims</span></td>
<td colspan=3D"1" class=3D"confluenceTd">=E6=96=B0=E5=A2=9E=E6=BF=80=E5=8A=
=B1=E8=AE=A1=E5=88=92</td>
</tr>
<tr>
<td colspan=3D"1" class=3D"confluenceTd"><br></td>
<td colspan=3D"1" class=3D"confluenceTd">qryEiPlan</td>
<td colspan=3D"1" class=3D"confluenceTd">/ei/plan/qryEiPlan</td>
<td colspan=3D"1" class=3D"confluenceTd"><span>lcims</span></td>
<td colspan=3D"1" class=3D"confluenceTd">=E6=9F=A5=E8=AF=A2=E6=BF=80=E5=8A=
=B1=E8=AE=A1=E5=88=92</td>
</tr>
<tr>
<td colspan=3D"1" class=3D"confluenceTd"><br></td>
<td colspan=3D"1" class=3D"confluenceTd">mdEiPlanInfo</td>
<td colspan=3D"1" class=3D"confluenceTd">/ei/plan/mdEiPlanInfo</td>
<td colspan=3D"1" class=3D"confluenceTd"><span>lcims</span></td>
<td colspan=3D"1" class=3D"confluenceTd">=E4=BF=AE=E6=94=B9=E6=BF=80=E5=8A=
=B1=E8=AE=A1=E5=88=92</td>
</tr>
<tr>
<td colspan=3D"1" class=3D"confluenceTd"><br></td>
<td colspan=3D"1" class=3D"confluenceTd">mdEiPlanStatus</td>
<td colspan=3D"1" class=3D"confluenceTd">/ei/plan/mdEiPlanStatus</td>
<td colspan=3D"1" class=3D"confluenceTd"><span>lcims</span></td>
<td colspan=3D"1" class=3D"confluenceTd">=E4=BF=AE=E6=94=B9=E6=BF=80=E5=8A=
=B1=E8=AE=A1=E5=88=92=E7=8A=B6=E6=80=81</td>
</tr>
<tr>
<td colspan=3D"1" class=3D"confluenceTd"><br></td>
<td colspan=3D"1" class=3D"confluenceTd">qryEiPlanInfo</td>
<td colspan=3D"1" class=3D"confluenceTd">/ei/plan/qryEiPlanInfo</td>
<td colspan=3D"1" class=3D"confluenceTd"><span>lcims</span></td>
<td colspan=3D"1" class=3D"confluenceTd">=E6=9F=A5=E8=AF=A2<span>=E6=BF=80=
=E5=8A=B1=E8=AE=A1=E5=88=92</span>=E4=BF=A1=E6=81=AF</td>
</tr>
<tr>
<td colspan=3D"1" class=3D"confluenceTd"><br></td>
<td colspan=3D"1" class=3D"confluenceTd">qryBatchSno</td>
<td colspan=3D"1" class=3D"confluenceTd">/ei/plan/qryBatchSno</td>
<td colspan=3D"1" class=3D"confluenceTd"><span>lcims</span></td>
<td colspan=3D"1" class=3D"confluenceTd">=E6=9F=A5=E8=AF=A2=E6=89=B9=E6=AC=
=A1=E5=8F=B7</td>
</tr>
<tr>
<td colspan=3D"1" class=3D"confluenceTd">QtyPriceController</td>
<td colspan=3D"1" class=3D"confluenceTd">addQtyPrice</td>
<td colspan=3D"1" class=3D"confluenceTd">/ei/qtyPrice/addQtyPrice</td>
<td colspan=3D"1" class=3D"confluenceTd"><span>lcims</span></td>
<td colspan=3D"1" class=3D"confluenceTd">=E6=96=B0=E5=A2=9E=E9=87=8F=E4=BB=
=B7=E8=B0=83=E6=95=B4</td>
</tr>
<tr>
<td colspan=3D"1" class=3D"confluenceTd"><br></td>
<td colspan=3D"1" class=3D"confluenceTd">qryQtyPrice</td>
<td colspan=3D"1" class=3D"confluenceTd">/ei/qtyPrice/qryQtyPrice</td>
<td colspan=3D"1" class=3D"confluenceTd"><span>lcims</span></td>
<td colspan=3D"1" class=3D"confluenceTd">=E6=9F=A5=E8=AF=A2=E9=87=8F=E4=BB=
=B7=E4=BF=A1=E6=81=AF</td>
</tr>
<tr>
<td colspan=3D"1" class=3D"confluenceTd"><br></td>
<td colspan=3D"1" class=3D"confluenceTd">qryQtyPriceInfo</td>
<td colspan=3D"1" class=3D"confluenceTd">/ei/qtyPrice/qryQtyPriceInfo</td>
<td colspan=3D"1" class=3D"confluenceTd"><span>lcims</span></td>
<td colspan=3D"1" class=3D"confluenceTd">=E6=9F=A5=E8=AF=A2=E5=8D=95=E4=B8=
=AA=E9=87=8F=E4=BB=B7=E4=BF=A1=E6=81=AF</td>
</tr>
<tr>
<td colspan=3D"1" class=3D"confluenceTd"><br></td>
<td colspan=3D"1" class=3D"confluenceTd">mdQtyPriceInfo</td>
<td colspan=3D"1" class=3D"confluenceTd">/ei/qtyPrice/mdQtyPriceInfo</td>
<td colspan=3D"1" class=3D"confluenceTd"><span>lcims</span></td>
<td colspan=3D"1" class=3D"confluenceTd">=E4=BF=AE=E6=94=B9=E9=87=8F=E4=BB=
=B7=E4=BF=A1=E6=81=AF</td>
</tr>
<tr>
<td colspan=3D"1" class=3D"confluenceTd"><br></td>
<td colspan=3D"1" class=3D"confluenceTd">deleteQtyPriceInfo</td>
<td colspan=3D"1" class=3D"confluenceTd">/ei/qtyPrice/deleteQtyPriceInfo</t=
d>
<td colspan=3D"1" class=3D"confluenceTd"><span>lcims</span></td>
<td colspan=3D"1" class=3D"confluenceTd">=E5=88=A0=E9=99=A4=E9=87=8F=E4=BB=
=B7=E4=BF=A1=E6=81=AF</td>
</tr>
<tr>
<td colspan=3D"1" class=3D"confluenceTd">ShowPlanController</td>
<td colspan=3D"1" class=3D"confluenceTd">SetShow</td>
<td colspan=3D"1" class=3D"confluenceTd">/ei/Show/SetShow</td>
<td colspan=3D"1" class=3D"confluenceTd"><span>lcims</span></td>
<td colspan=3D"1" class=3D"confluenceTd">=E9=BB=98=E8=AE=A4=E6=98=BE=E7=A4=
=BA=E6=96=B9=E6=A1=88</td>
</tr>
<tr>
<td colspan=3D"1" class=3D"confluenceTd"><br></td>
<td colspan=3D"1" class=3D"confluenceTd">GetShow</td>
<td colspan=3D"1" class=3D"confluenceTd">/ei/Show/GetShow</td>
<td colspan=3D"1" class=3D"confluenceTd"><span>lcims</span></td>
<td colspan=3D"1" class=3D"confluenceTd">=E6=9F=A5=E8=AF=A2=E6=98=BE=E7=A4=
=BA=E6=96=B9=E6=A1=88</td>
</tr>
<tr>
<td colspan=3D"1" class=3D"confluenceTd">StaffManageMentController</td>
<td colspan=3D"1" class=3D"confluenceTd">qryStaff</td>
<td colspan=3D"1" class=3D"confluenceTd">/ei/staff/qryStaff</td>
<td colspan=3D"1" class=3D"confluenceTd"><span>lcims</span></td>
<td colspan=3D"1" class=3D"confluenceTd">=E6=9F=A5=E8=AF=A2=E5=91=98=E5=B7=
=A5=EF=BC=8C=E6=BF=80=E5=8A=B1=E5=AF=B9=E8=B1=A1=E9=9B=86=E5=90=88</td>
</tr>
<tr>
<td colspan=3D"1" class=3D"confluenceTd"><br></td>
<td colspan=3D"1" class=3D"confluenceTd">qryStaffInfo</td>
<td colspan=3D"1" class=3D"confluenceTd">/ei/staff/qryStaffInfo</td>
<td colspan=3D"1" class=3D"confluenceTd"><span>lcims</span></td>
<td colspan=3D"1" class=3D"confluenceTd">=E6=9F=A5=E8=AF=A2=E5=91=98=E5=B7=
=A5=EF=BC=8C=E8=82=A1=E6=9D=83=E6=BF=80=E5=8A=B1=E8=AE=A1=E5=88=92=E5=90=88=
=E9=9B=86</td>
</tr>
<tr>
<td colspan=3D"1" class=3D"confluenceTd"><br></td>
<td colspan=3D"1" class=3D"confluenceTd">UpdateStaffRange</td>
<td colspan=3D"1" class=3D"confluenceTd">/ei/staff/UpdateStaffRange</td>
<td colspan=3D"1" class=3D"confluenceTd"><span>lcims</span></td>
<td colspan=3D"1" class=3D"confluenceTd">=E4=BF=AE=E6=94=B9=E5=91=98=E5=B7=
=A5=E8=8C=83=E5=9B=B4</td>
</tr>
<tr>
<td colspan=3D"1" class=3D"confluenceTd"><br></td>
<td colspan=3D"1" class=3D"confluenceTd">DelStaff</td>
<td colspan=3D"1" class=3D"confluenceTd">/ei/staff/DelStaff</td>
<td colspan=3D"1" class=3D"confluenceTd"><span>lcims</span></td>
<td colspan=3D"1" class=3D"confluenceTd">=E5=88=A0=E9=99=A4=E5=91=98=E5=B7=
=A5</td>
</tr>
<tr>
<td colspan=3D"1" class=3D"confluenceTd"><br></td>
<td colspan=3D"1" class=3D"confluenceTd">UpdateStaffSpec</td>
<td colspan=3D"1" class=3D"confluenceTd">/ei/staff/UpdateStaffSpec</td>
<td colspan=3D"1" class=3D"confluenceTd"><span>lcims</span></td>
<td colspan=3D"1" class=3D"confluenceTd">=E4=BF=AE=E6=94=B9=E5=91=98=E5=B7=
=A5</td>
</tr>
<tr>
<td colspan=3D"1" class=3D"confluenceTd"><br></td>
<td colspan=3D"1" class=3D"confluenceTd">qryAllStaff</td>
<td colspan=3D"1" class=3D"confluenceTd">/ei/staff/DelStaff/qryAllStaff</td=
>
<td colspan=3D"1" class=3D"confluenceTd"><span>lcims</span></td>
<td colspan=3D"1" class=3D"confluenceTd">=E6=9F=A5=E8=AF=A2=E5=85=A8=E9=83=
=A8=E5=91=98=E5=B7=A5</td>
</tr>
<tr>
<td colspan=3D"1" class=3D"confluenceTd">TaxationController</td>
<td colspan=3D"1" class=3D"confluenceTd">getTaxList</td>
<td colspan=3D"1" class=3D"confluenceTd">/ei/Taxation/getTaxList</td>
<td colspan=3D"1" class=3D"confluenceTd"><span>lcims</span></td>
<td colspan=3D"1" class=3D"confluenceTd"><span>=E6=9F=A5=E8=AF=A2</span>=E8=
=A7=A3=E9=94=81=E5=88=97=E8=A1=A8</td>
</tr>
<tr>
<td colspan=3D"1" class=3D"confluenceTd"><br></td>
<td colspan=3D"1" class=3D"confluenceTd">GetOtherTax</td>
<td colspan=3D"1" class=3D"confluenceTd">/ei/Taxation/GetOtherTax</td>
<td colspan=3D"1" class=3D"confluenceTd"><span>lcims</span></td>
<td colspan=3D"1" class=3D"confluenceTd">=E5=85=B6=E4=BB=96=E8=A7=A3=E9=94=
=81=E4=BF=A1=E6=81=AF=EF=BC=8C=E8=A1=8C=E6=9D=83</td>
</tr>
<tr>
<td colspan=3D"1" class=3D"confluenceTd"><br></td>
<td colspan=3D"1" class=3D"confluenceTd">QryTerm</td>
<td colspan=3D"1" class=3D"confluenceTd">/ei/Taxation/QryTerm</td>
<td colspan=3D"1" class=3D"confluenceTd"><span>lcims</span></td>
<td colspan=3D"1" class=3D"confluenceTd">=E6=9F=A5=E8=AF=A2=E8=AE=A1=E5=88=
=92=E8=A7=A3=E9=94=81=E6=9C=9F</td>
</tr>
<tr>
<td colspan=3D"1" class=3D"confluenceTd"><br></td>
<td colspan=3D"1" class=3D"confluenceTd">EditStockPrice</td>
<td colspan=3D"1" class=3D"confluenceTd">/ei/Taxation/EditStockPrice</td>
<td colspan=3D"1" class=3D"confluenceTd"><span>lcims</span></td>
<td colspan=3D"1" class=3D"confluenceTd">=E7=BC=96=E8=BE=91=E8=82=A1=E7=A5=
=A8=E5=B8=82=E4=BB=B7=E5=85=A5=E5=8F=82</td>
</tr>
<tr>
<td colspan=3D"1" class=3D"confluenceTd">UnlockController</td>
<td colspan=3D"1" class=3D"confluenceTd">getList</td>
<td colspan=3D"1" class=3D"confluenceTd">/ei/unlock/getList</td>
<td colspan=3D"1" class=3D"confluenceTd"><span>lcims</span></td>
<td colspan=3D"1" class=3D"confluenceTd">=E6=9F=A5=E8=AF=A2=E8=A7=A3=E9=94=
=81=E5=88=97=E8=A1=A8</td>
</tr>
<tr>
<td colspan=3D"1" class=3D"confluenceTd"><br></td>
<td colspan=3D"1" class=3D"confluenceTd">updateSpec</td>
<td colspan=3D"1" class=3D"confluenceTd">/ei/unlock/updateSpec</td>
<td colspan=3D"1" class=3D"confluenceTd"><span>lcims</span></td>
<td colspan=3D"1" class=3D"confluenceTd">=E6=9B=B4=E6=96=B0=E8=A7=A3=E9=94=
=81=E4=BF=A1=E6=81=AF</td>
</tr>
<tr>
<td colspan=3D"1" class=3D"confluenceTd"><br></td>
<td colspan=3D"1" class=3D"confluenceTd">updateRange</td>
<td colspan=3D"1" class=3D"confluenceTd">/ei/unlock/updateRange</td>
<td colspan=3D"1" class=3D"confluenceTd"><span>lcims</span></td>
<td colspan=3D"1" class=3D"confluenceTd">=E6=89=B9=E9=87=8F=E6=9B=B4=E6=96=
=B0=E8=A7=A3=E9=94=81=E4=BF=A1=E6=81=AF</td>
</tr>
</tbody>
</table>
</div>
    </div>
</body>
</html>
------=_Part_9819_2015950987.1660197940882--
