# 1. 流程图

```plantuml
title 报价分析列表查询接口

participant 客户端
participant em_pmms_admin
participant MySQL

activate 客户端
客户端->em_pmms_admin:POST /adminApi/quoteManage/queryAnalysisList
activate em_pmms_admin
em_pmms_admin->MySQL:查询账户结构树表（account_treee）“做市团队”节点下所有成员
activate MySQL
em_pmms_admin<--MySQL:return
deactivate MySQL
em_pmms_admin->MySQL:根据日期和报价人和勾选来源查询报价管理表（quotation_management）数据
activate MySQL
em_pmms_admin<--MySQL:return
deactivate MySQL
em_pmms_admin->em_pmms_admin:根据报价成员聚合计算：\n1、报价机构数量\n2、规范报价机构数量\n3、报价债券数量\n4、规范报价债券数量 \n5、报价数量\n6、规范报价数量
客户端<--em_pmms_admin:return
deactivate em_pmms_admin
deactivate 客户端
```

# 2. 涉及的数据库、Redis、Kafka 等中间件的修改/新增图或者说明
不涉及
# 3. 前后端交互接口信息
## 4.1 报价分析列表查询接口

### 4.1.1 请求路径

POST /adminApi/quoteManage/queryAnalysisList

### 4.1.2 入参

| 字段名         | 字段类型            | 描述                       | 是否必传 |
| ----------- | --------------- | ------------------------ | ---- |
| startDate   | Integer         | 报价日期开始                   | 否    |
| endDate     | Integer         | 报价日期结束                   | 否    |
| creatorList | List\<String\>  | 报价成员账号列表                 | 否    |
| quoteSource | List\<Integer\> | 报价来源,字典-pmms.quoteSource | 否    |
### 4.1.3 出参

| 字段名                  | 字段类型         | 描述       | 是否不为空 |
| -------------------- | ------------ | -------- | ----- |
| data                 | List<object> |          |       |
| creator              | String       | 创建人      | 是     |
| creatorName          | String       | 创建人姓名    | 是     |
| instQuantity         | Integer      | 报价机构数量   | 是     |
| standardInstQuantity | Integer      | 规范报价机构数量 | 是     |
| bondQuantity         | Integer      | 报价债券数量   | 是     |
| standardBondQuantity | Integer      | 规范报价债券数量 | 是     |
| quoteQuantity        | Integer      | 报价数量       | 是     |
| standardQuoteQuantity| Integer      | 规范报价数量    | 是     |
# 4. 配置&部署修改信息
不涉及
# 5. 新增技术组件说明
不涉及
# 6. 影响范围
报表管理-报价管理-报价分析
# 7. 外部依赖项
不涉及
