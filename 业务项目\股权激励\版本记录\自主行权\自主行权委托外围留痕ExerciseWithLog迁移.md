# 1. 流程图


```plantuml
title 自主行权委托外围留痕迁移 
participant APP
participant 股权激励中台服务
participant 股权激励前置机通讯服务
participant 柜台
APP->股权激励中台服务: 自主行权委托
    activate APP
    activate 股权激励中台服务

    股权激励中台服务->股权激励前置机通讯服务: 自主行权委托
    activate 股权激励前置机通讯服务

    股权激励前置机通讯服务->柜台: 调用282[410985]-自主行权业务
    activate 柜台

    股权激励前置机通讯服务<-柜台: return
    deactivate 柜台

    股权激励中台服务<-股权激励前置机通讯服务: return
    deactivate 股权激励前置机通讯服务

    股权激励中台服务->股权激励数据库: 插入行权记录
    activate 股权激励数据库

    股权激励中台服务<-股权激励数据库: return
    deactivate 股权激励数据库

    APP<-股权激励中台服务: return
    deactivate 股权激励中台服务
    deactivate APP
```

# 2. 涉及的数据库、Redis、Kafka 等中间件的修改/新增图或者说明

不涉及

# 3. 前后端交互接口信息

## 3.1. 自主行权委托

### 3.1.1. 接口路径

POST:exerciseWithLog

### 3.1.2. 接口入参

| 参数名称      | 参数含义 | 参数类型   | 是否必填 | 备注          |
| --------- | ---- | ------ | ---- | ----------- |
| planid    | 计划ID | String | 否    |             |
| versionid | 批次ID | String | 否    |             |
| Jysc      | 交易市场 | String | 是    | 0: 深市，1: 沪市 |
| Gddm      | 股东代码 | String | 是    |             |
| Zqdm      | 行权代码 | String | 是    |             |
| Sl        | 数量   | String | 是    |             |
| companyId | 公司ID | String | 否    |             |

### 3.1.3. 接口出参

| 参数名称 | 参数含义 | 参数类型   | 是否必填 | 备注  |
| ---- | ---- | ------ | ---- | --- |
| Wtxh | 委托序号 | String | 是    |     |
| Htxh | 合同序号 | String | 是    |     |
| Wtph | 委托批号 | String | 是    |     |

# 4. 配置&部署修改信息

不涉及

# 5. 新增技术组件说明

不涉及

# 6. 影响范围

不涉及

# 7. 外部依赖项

柜台
