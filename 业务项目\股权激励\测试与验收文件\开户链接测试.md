000027.SZ 企业客户部
# 分支机构userid
USEREID IN (213000168216308763, 213000169307900771, 213000168430455498, 213000041498076155, 213000134086650172, 1, 213000172222745031, 213000012195145994, 213000172798608945, 213000172798609360, 213000169382217060, 213000168430819070, 213000134523241130, 213000172222736280, 213000004914113517, 213000168430455872, 213000150780735094, 213000169377748390, 213000169213597148, 213000168801485235, 213000168430583695, 213000172222716995, 213000155546902767, 213000169709178727, 213000168206707465, 213000028573619309, 213000168603990810, 213000131632465583, 213000168966558416, 213000041449707485, 213***************, **********)
# 企客部userid
USEREID IN (213000157759892625, 213000169452542348, 213000157750987419, 213000169452543163, 213000169452543082, 213000169452542515, 213000172419740403, **********, 213000170622415623)


## DEPT_BROKER("102","部门经纪人信息")
INSERT INTO MY_TABLE(OACODE, REALNAME, USERID, SECUCODE) VALUES ('12352', '刘倩', 213000004914113517, '603127.SH');
 http://ceshi.securities.eastmoney.com:7396/Html/aghd/native/5.5/********/html/share1.html?orgid=&source=&specifiedBank=&ad_id=&channelname=81&mid=*********&secCode=GQJLKH|603127.SH|昭衍新药

## ENTERPRISE("101","企业客户部链接")
有二维码的userid
INSERT INTO DCOMP.OMP_ENTERPRISE_CUST_MANAGE_USERINFO (USERID, REALNAME) VALUES (213000157750987419, '王宝祥1');
INSERT INTO DCOMP.OMP_ENTERPRISE_CUST_MANAGE_USERINFO (USERID, REALNAME) VALUES (213000157759892625, '王宝祥2');
INSERT INTO DCOMP.OMP_ENTERPRISE_CUST_MANAGE_USERINFO (USERID, REALNAME) VALUES (213000169452542348, '赵竹君');
INSERT INTO DCOMP.OMP_ENTERPRISE_CUST_MANAGE_USERINFO (USERID, REALNAME) VALUES (213000169452543163, '范林玥');
ApiResponseEntity{Message='', Status=0, Data=[AccountOpenLinkResVO(openAccountUrl=https://ceshi.securities.eastmoney.com:7151?orgid=6&channelname=79&channelvalue=3335456&channelvalue2=GQJLKH|000026.SZ|飞亚达, message=101)], Count=1, Errcode=0}
## UNLISTED_COMPANY("100", "非上市公司"),
http://ceshi.securities.eastmoney.com:7396/Html/aghd/native/5.5/********/html/share1.html?orgid=&source=&specifiedBank=&ad_id=&channelname=81&mid=*********&secCode=GQJLKH|非上市公司测试
***********


##  ERROR_NO_CUST("4", "无服务经理")
000566	海南海药
【开户链接生成】-测试 未查询到服务经理信息，公司名称：海南海药,stkCode:000566.SZ
ApiResponseEntity{Message='', Status=0, Data=[AccountOpenLinkResVO(openAccountUrl=https://ceshi.securities.eastmoney.com:7396/Html/aghd/native/5.5/********/html/share1.html?orgid=&source=&specifiedBank=&ad_id=&channelname=81&mid=*********&secCode=GQJLKH|000566.SZ|海南海药, message=4)], Count=1, Errcode=0}
***********
***********


## ERROR_NO_DEPT("5", "无部门信息")
USERID IN (213000041924952721, 213000167373636398)
【开户链接生成】-测试 未查询到部门信息，公司名称：PT水仙,stkCode:600625.SH，userid:213000167373636398
ApiResponseEntity{Message='', Status=0, Data=[AccountOpenLinkResVO(openAccountUrl=https://ceshi.securities.eastmoney.com:7396/Html/aghd/native/5.5/********/html/share1.html?orgid=&source=&specifiedBank=&ad_id=&channelname=81&mid=*********&secCode=GQJLKH|600625.SH|PT水仙, message=5)], Count=1, Errcode=0}
***********



## ERROR_NO_BROKER("7","无经纪人信息")

INSERT INTO MY_TABLE(OACODE, REALNAME, USERID, SECUCODE) VALUES ('wangbaoxiang', '王宝祥', 213000168966558416, '605056.SH');


http://ceshi.securities.eastmoney.com:7396/Html/aghd/native/5.5/********/html/share1.html?orgid=&source=&specifiedBank=&ad_id=&channelname=81&mid=*********&secCode=GQJLKH|605056.SH|咸亨国际
***********
## ERROR_NO_QRCODE("6", "无二维码信息")
【开户链接生成】-测试 未查询到二维码信息,公司名称：000066.SZ|华人健康，userid:213000169452543082
ApiResponseEntity{Message='', Status=0, Data=[AccountOpenLinkResVO(openAccountUrl=https://ceshi.securities.eastmoney.com:7396/Html/aghd/native/5.5/********/html/share1.html?orgid=&source=&specifiedBank=&ad_id=&channelname=81&mid=*********&secCode=GQJLKH|000066.SZ|华人健康, message=6)], Count=1, Errcode=0}


## BRANCH("103","分支机构链接-个人经纪人")
INSERT INTO DCOMP.OMP_ENTERPRISE_CUST_MANAGE_USERINFO (EID, EITIME, EUTIME, USERID, REALNAME, DEPARTMENTNO, MOBILE, EMAIL, OACODE, DEPARTMENTNAME, GROUPRANK, GROUPEID, PERMISSIONSTATUS, ISDELETE, ISLEAVE) VALUES (251000000786046510, TIMESTAMP '2023-12-01 09:31:40', TIMESTAMP '2024-04-10 16:10:47', 213000169452542348, '赵竹君', '1101', '**********', '<EMAIL>', '19307', '北京陶然亭路证券营业部', null, null, '1', 0, 0);


【开户链接生成】-测试 完整链接：https://ceshi.securities.eastmoney.com:7396/Html/aghd/native/5.5/********/html/share1.html?orgid=&source=&specifiedBank=&ad_id=&channelname=81&mid=*********&secCode=GQJLKH|000636.SZ|风华高科，使用个人经纪人信息，使用公司名称：000636.SZ|风华高科，userid:213000169452542348，brokerId:*********
ApiResponseEntity{Message='', Status=0, Data=[AccountOpenLinkResVO(openAccountUrl=https://ceshi.securities.eastmoney.com:7396/Html/aghd/native/5.5/********/html/share1.html?orgid=&source=&specifiedBank=&ad_id=&channelname=81&mid=*********&secCode=GQJLKH|000636.SZ|风华高科, message=103)], Count=1, Errcode=0}






