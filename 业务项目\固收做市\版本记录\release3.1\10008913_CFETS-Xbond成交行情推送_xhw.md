---
created: 2024-10-15 16:32
updated: 2024-10-16 10:13
---
## 1. 流程图


![[../../../attachments/10008913_CFETS-Xbond成交行情推送_xhw/CFETS-Xbond成交行情推送.png|../../../../../attachs/CFETS-Xbond成交行情推送.png]]
```
title CFETS-Xbond成交行情推送


participant RISK服务/BUSINESS服务
participant CFETS服务
participant 外汇交易中心



group 行情订阅
RISK服务/BUSINESS服务->CFETS服务:Http查询Xbond成交行情历史数据
RISK服务/BUSINESS服务<-CFETS服务:Xbond成交行情历史数据
RISK服务/BUSINESS服务->RISK服务/BUSINESS服务:保存行情数据至内存
RISK服务/BUSINESS服务->CFETS服务:Netty订阅Xbond成交行情历史数据
CFETS服务<-外汇交易中心:最新Xbond成交行情推送
RISK服务/BUSINESS服务<-CFETS服务:推送最新Xbond成交行情
RISK服务/BUSINESS服务->RISK服务/BUSINESS服务:更新内存中行情数据
end
group 每日定时任务(0 0 0 * * ? )
RISK服务/BUSINESS服务->RISK服务/BUSINESS服务:清空内存行情数据

end
```
## 2. 涉及的数据库、Redis、Kafka 等中间件的修改/新增图或者说明
不涉及
## 3. 前后端交互接口信息

| 字段名              | 类型         | 描述                                              |
| ---------------- | ---------- | ----------------------------------------------- |
| securityMarketId | String     | 债券代码带后缀                                         |
| bidYield         | String     | 买入收益率，买入报价的收益率(%)                               |
| bidYte           | String     | bid行权收益率(%)                                     |
| bidDirtyPrice    | BigDecimal | bid全价                                           |
| bidNetPrice      | BigDecimal | bid净价                                           |
| bidCalcType      | Integer    | bid计算类型，原值类型，0表示净价,1表示全价,2表示到期收益率,3表示行权收益率      |
| bidQuoteType     | String     | bid报价类型，123 (做市商)                               |
| bidInitiator     | String     | bid报买机构                                         |
| bidVolumeStr     | String     | bid买入数量，买入报价的交易面额(万元)，字体红色，用右上角标形式代表清算速度T+1     |
| bidBroker        | String     | 经纪商 CNEX-国际、PATR-平安、BGC-中诚、TP-国利、MQM-信唐、UEDA-上田 |
| offerYield       | String     | offer收益率                                        |
| offerYte         | String     | offer行权收益率                                      |
| offerDirtyPrice  | BigDecimal | offer全价                                         |
| offerNetPrice    | BigDecimal | offer净价                                         |
| offerCalcType    | Integer    | offer计算类型，原值类型，0表示净价,1表示全价,2表示到期收益率,3表示行权收益率    |
| offerQuoteType   | String     | offer报价类别，123 (做市商)                             |
| offerInitiator   | String     | 报卖机构                                            |
| offerVolumeStr   | String     | offer报价的交易面额(万元)，字体绿色，用右上角标形式代表清算速度T+1          |
| offerBroker      | String     | 经纪商 CNEX-国际、PATR-平安、BGC-中诚、TP-国利、MQM-信唐、UEDA-上田 |
| optionEmbedded   | boolean    | 是否含权债                                           |
| mmXbondFlag      | boolean    | 是否为做市商和Xbond                                    |
| type             | String     | 行情类型，0-做市商，中介，Xbond最优行情，1-Xbond成交行情             |

## 4. 配置&部署修改信息
### 4.1. **em-pmms-business**
新增配置
```
netty.cfets.xbondDealPath=/cfetsApi/trade/getTrades
```

### 4.2. **em-pmms-risk**
新增配置
```
netty.cfets.xbondDealPath=/cfetsApi/trade/getTrades
```
## 5. 新增技术组件说明
不涉及
## 6. 影响范围
### 6.1. **em-pmms-risk**
- 做市商最优，中介最优，Xbond 最优，全市场最优行情接收
- 事前-价格倒挂
### 6.2. **em-pmms-business**
- 做市商最优，中介最优，Xbond 最优，全市场最优行情接收
- 策略重发
- 策略启用
- 双边报价列表行情查看
## 7. 外部依赖项
外汇 Xbond 成交行情
