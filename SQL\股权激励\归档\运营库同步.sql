

CREATE TABLE  LCIMS.OMP_USER_QRCODE (
	EID NUMBER(18,0),
	EITIME DATE DEFAULT sysdate ,
	EUTIME DATE DEFAULT sysdate ,
	OACODE VARCHAR2(128),
	USERNAME VARCHAR2(256),
	REALNAME VARCHAR2(512),
	CERTIFICATENO VARCHAR2(100),
	CERTIFICATEDATE DATE,
	ISDELETE VARCHAR2(2),
	CHANNEL VARCHAR2(32),
	REMARK VARCHAR2(4000),
	QRCODE CLOB,
	QRFLAG VARCHAR2(2) DEFAULT '0',
	BRANCHNO VARCHAR2(32),
	USEREID NUMBER(18,0),
	OPERATEEID NUMBER(18,0),
	OPERATEUSERNAME VARCHAR2(256),
	OPERATEREALNAME VARCHAR2(512),
CONSTRAINT PK_OMP_USER_QRCODE PRIMARY KEY (EID)
);
CREATE INDEX OMP_USER_QRCODE_OACODE_IDX ON  LCIMS.OMP_USER_QRCODE (OACODE);
CREATE INDEX OMP_USER_QRCODE_USEREID_IDX ON  LCIMS.OMP_USER_QRCODE (USEREID);
CREATE UNIQUE INDEX OMP_USER_QRCODE_USERNAME_IDX ON  LCIMS.OMP_USER_QRCODE (USERNAME);



COMMENT ON TABLE  LCIMS.OMP_USER_QRCODE IS '企业客户二维码维护';

COMMENT ON COLUMN  LCIMS.OMP_USER_QRCODE.EID IS '系统物理主键';

COMMENT ON COLUMN  LCIMS.OMP_USER_QRCODE.EITIME IS '数据入库时间';

COMMENT ON COLUMN  LCIMS.OMP_USER_QRCODE.EUTIME IS '最近一次修改时间';

COMMENT ON COLUMN  LCIMS.OMP_USER_QRCODE.OACODE IS '员工工号';

COMMENT ON COLUMN  LCIMS.OMP_USER_QRCODE.USERNAME IS '员工账号';

COMMENT ON COLUMN  LCIMS.OMP_USER_QRCODE.REALNAME IS '员工姓名';

COMMENT ON COLUMN  LCIMS.OMP_USER_QRCODE.CERTIFICATENO IS '证券从业资格编号';

COMMENT ON COLUMN  LCIMS.OMP_USER_QRCODE.CERTIFICATEDATE IS '证券从业资格登记时间';

COMMENT ON COLUMN  LCIMS.OMP_USER_QRCODE.ISDELETE IS '员工状态(0在职 1离职)';

COMMENT ON COLUMN  LCIMS.OMP_USER_QRCODE.CHANNEL IS '渠道(默认值79-企业客户部)';

COMMENT ON COLUMN  LCIMS.OMP_USER_QRCODE.REMARK IS '备注';

COMMENT ON COLUMN  LCIMS.OMP_USER_QRCODE.QRCODE IS '二维码';

COMMENT ON COLUMN  LCIMS.OMP_USER_QRCODE.QRFLAG IS '0不生成二维码 1生成二维码';

COMMENT ON COLUMN  LCIMS.OMP_USER_QRCODE.BRANCHNO IS '开户营业部';

COMMENT ON COLUMN  LCIMS.OMP_USER_QRCODE.USEREID IS '用户表主键';

COMMENT ON COLUMN  LCIMS.OMP_USER_QRCODE.OPERATEEID IS '操作人主键';

COMMENT ON COLUMN  LCIMS.OMP_USER_QRCODE.OPERATEUSERNAME IS '操作人账号';

COMMENT ON COLUMN  LCIMS.OMP_USER_QRCODE.OPERATEREALNAME IS '操作人姓名';



CREATE TABLE  LCIMS.OMP_CUST_BELONG (
	EID NUMBER(18,0),
	EITIME DATE DEFAULT sysdate ,
	EUTIME DATE DEFAULT sysdate ,
	CUSTNAME VARCHAR2(50),
	CUSTTYPE NUMBER(1,0),
	STATE NUMBER(4,0),
	CUSTID NUMBER(18,0),
	SOURCEUSERID NUMBER(18,0),
	SOURCEUSERNAME VARCHAR2(100),
	SOURCEOACODE VARCHAR2(128),
	OPENTIME DATE,
	ISOPEN NUMBER(1,0),
CONSTRAINT PK_OMP_CUST_BELONG PRIMARY KEY (EID)
);
CREATE INDEX OMP_CUST_BELONG_CUSTID_IDX ON  LCIMS.OMP_CUST_BELONG (CUSTID);

COMMENT ON TABLE  LCIMS.OMP_CUST_BELONG IS '企业客户管理归属表';

COMMENT ON COLUMN  LCIMS.OMP_CUST_BELONG.EID IS '系统物理主键';

COMMENT ON COLUMN  LCIMS.OMP_CUST_BELONG.EITIME IS '数据入库时间';

COMMENT ON COLUMN  LCIMS.OMP_CUST_BELONG.EUTIME IS '最近一次修改时间';

COMMENT ON COLUMN  LCIMS.OMP_CUST_BELONG.CUSTNAME IS '客户名称';

COMMENT ON COLUMN  LCIMS.OMP_CUST_BELONG.CUSTTYPE IS '客户类型 1-企业客户 2-关联个人客户';

COMMENT ON COLUMN  LCIMS.OMP_CUST_BELONG.STATE IS '2-已归属';

COMMENT ON COLUMN  LCIMS.OMP_CUST_BELONG.CUSTID IS '企业ID';

COMMENT ON COLUMN  LCIMS.OMP_CUST_BELONG.SOURCEUSERID IS '开发来源userid';

COMMENT ON COLUMN  LCIMS.OMP_CUST_BELONG.SOURCEUSERNAME IS '开发来源真实姓名';

COMMENT ON COLUMN  LCIMS.OMP_CUST_BELONG.SOURCEOACODE IS '开发来源工号';

COMMENT ON COLUMN  LCIMS.OMP_CUST_BELONG.OPENTIME IS '财富号开通时间';

COMMENT ON COLUMN  LCIMS.OMP_CUST_BELONG.ISOPEN IS '是否开通财富号 0-未开通 1-已开通';



CREATE TABLE  LCIMS.OMP_CUST_BELONG_VALUE (
	EID NUMBER(18,0),
	EITIME DATE DEFAULT sysdate ,
	EUTIME DATE DEFAULT sysdate ,
	PEID NUMBER(18,0),
	USERID NUMBER(18,0),
	BELONGPERCENT VARCHAR2(5),
	DEPARTMENTNO VARCHAR2(10),
	DEPARTMENTNAME VARCHAR2(50),
CONSTRAINT PK_OMP_CUST_BELONG_VALUE PRIMARY KEY (EID)
);
CREATE INDEX OMP_CUST_BELONG_VALUE_PEID_IDX ON  LCIMS.OMP_CUST_BELONG_VALUE (PEID);
CREATE INDEX OMP_CUST_BEL_VAL_USERID_IDX ON  LCIMS.OMP_CUST_BELONG_VALUE (USERID);


COMMENT ON TABLE  LCIMS.OMP_CUST_BELONG_VALUE IS '企业客户管理归属比例表';

COMMENT ON COLUMN  LCIMS.OMP_CUST_BELONG_VALUE.EID IS '系统物理主键';

COMMENT ON COLUMN  LCIMS.OMP_CUST_BELONG_VALUE.EITIME IS '数据入库时间';

COMMENT ON COLUMN  LCIMS.OMP_CUST_BELONG_VALUE.EUTIME IS '最近一次修改时间';

COMMENT ON COLUMN  LCIMS.OMP_CUST_BELONG_VALUE.PEID IS '归属表eid';

COMMENT ON COLUMN  LCIMS.OMP_CUST_BELONG_VALUE.USERID IS '用户ID';

COMMENT ON COLUMN  LCIMS.OMP_CUST_BELONG_VALUE.BELONGPERCENT IS '归属比例';

COMMENT ON COLUMN  LCIMS.OMP_CUST_BELONG_VALUE.DEPARTMENTNO IS '归属部门';

COMMENT ON COLUMN  LCIMS.OMP_CUST_BELONG_VALUE.DEPARTMENTNAME IS '归属部门名';

CREATE TABLE  LCIMS.OMP_CUST_MANAGE_CUST (
	EID NUMBER(18,0),
	EITIME DATE,
	EUTIME DATE,
	CUSTNAME VARCHAR2(500),
	REGNUM VARCHAR2(64),
	ORGPROFILE CLOB,
	INDUSTRYNAME VARCHAR2(64),
	MAINBUSINESS CLOB,
	ADDRESS VARCHAR2(500),
	ORGTEL VARCHAR2(128),
	ORGEMAIL VARCHAR2(500),
	ORGCODE VARCHAR2(64),
	SECUCODE VARCHAR2(100),
	LISTFLAG NUMBER(1,0),
	TRADEMARKETCODE VARCHAR2(100),
	MARKETCODE VARCHAR2(100),
	SOURCEUSERID NUMBER(18,0),
	SOURCEUSERNAME VARCHAR2(100),
	SOURCEOACODE VARCHAR2(128),
	OPENTIME DATE,
	ISOPEN NUMBER(1,0) DEFAULT 0,
	BASICINFO VARCHAR2(1000),
	CONTRASTINFO VARCHAR2(1000),
	CUSTSHORTNAME VARCHAR2(100),
	ISPAYCUST NUMBER(4,0) DEFAULT 0,
	AUDITTIMETYPE NUMBER(4,0),
	AUDITTIME DATE,
	REGADDRESS VARCHAR2(3000),
	PROVINCE VARCHAR2(500),
	CITY VARCHAR2(500),
	OPERATEINCOME VARCHAR2(100),
	NETPROFIT VARCHAR2(100),
	REPORTNAME VARCHAR2(200),
	REPROVINCE VARCHAR2(200),
	INDUSTRY NUMBER(18,0),
	REGPROVINCE VARCHAR2(500),
	REGCITY VARCHAR2(500),
	OPTPROVINCE VARCHAR2(500),
	OPTCITY VARCHAR2(500),
	LISTSTATE NUMBER(4,0),
	ORGTYPE VARCHAR2(64),
	ORGTYPE_BD VARCHAR2(64),
	ORGTYPECODE VARCHAR2(64),
CONSTRAINT PK_OMP_CUST_MANAGE_CUST PRIMARY KEY (EID)
);
CREATE INDEX IDX_CUST_SC ON  LCIMS.OMP_CUST_MANAGE_CUST (SECUCODE);
CREATE INDEX OC_MANAGE_CUST_CUSTNAME_IDX ON  LCIMS.OMP_CUST_MANAGE_CUST (CUSTNAME);
CREATE INDEX OC_MANAGE_CUST_ORGCODE_IDX ON  LCIMS.OMP_CUST_MANAGE_CUST (ORGCODE);


COMMENT ON TABLE  LCIMS.OMP_CUST_MANAGE_CUST IS '企业客户管理-用户表 定时从choice查询写入';

COMMENT ON COLUMN  LCIMS.OMP_CUST_MANAGE_CUST.EID IS '系统物理主键';

COMMENT ON COLUMN  LCIMS.OMP_CUST_MANAGE_CUST.EITIME IS '数据入库时间';

COMMENT ON COLUMN  LCIMS.OMP_CUST_MANAGE_CUST.EUTIME IS '数据修改时间';

COMMENT ON COLUMN  LCIMS.OMP_CUST_MANAGE_CUST.CUSTNAME IS '客户名称';

COMMENT ON COLUMN  LCIMS.OMP_CUST_MANAGE_CUST.REGNUM IS '统一社会信用代码';

COMMENT ON COLUMN  LCIMS.OMP_CUST_MANAGE_CUST.ORGPROFILE IS '公司简介';

COMMENT ON COLUMN  LCIMS.OMP_CUST_MANAGE_CUST.INDUSTRYNAME IS '所属行业';

COMMENT ON COLUMN  LCIMS.OMP_CUST_MANAGE_CUST.MAINBUSINESS IS '主营业务';

COMMENT ON COLUMN  LCIMS.OMP_CUST_MANAGE_CUST.ADDRESS IS '办公地址';

COMMENT ON COLUMN  LCIMS.OMP_CUST_MANAGE_CUST.ORGTEL IS '公司电话';

COMMENT ON COLUMN  LCIMS.OMP_CUST_MANAGE_CUST.ORGEMAIL IS '公司邮箱';

COMMENT ON COLUMN  LCIMS.OMP_CUST_MANAGE_CUST.ORGCODE IS '公司代码';

COMMENT ON COLUMN  LCIMS.OMP_CUST_MANAGE_CUST.SECUCODE IS '证券代码';

COMMENT ON COLUMN  LCIMS.OMP_CUST_MANAGE_CUST.LISTFLAG IS '上市状态 1-上市 2-退市';

COMMENT ON COLUMN  LCIMS.OMP_CUST_MANAGE_CUST.TRADEMARKETCODE IS '证券市场编码 069001002005 深交所风险警示板 069002004001 香港交易所主板 069001004001 新三板 069001001003 上交所风险警示板 069001004002 老三板 070002 纽约证券交易所 070001 美国证券交易所 070012 美国场外柜台交易系统 070015 美国BATS全球市场 069001001001 上交所主板 069001001006 上交所科创板 069001002001 深交所主板 069001017 北京证券交易所 069001002002 深交所创业板 070006001 美国纳斯达克市场 069002004002 香港交易所创业板 070014 纽约证券交易所高增长板市场';

COMMENT ON COLUMN  LCIMS.OMP_CUST_MANAGE_CUST.MARKETCODE IS '市场编码 0：深交所、北交所、三板；1：上交所；116：港交所';

COMMENT ON COLUMN  LCIMS.OMP_CUST_MANAGE_CUST.SOURCEUSERID IS '开发来源userid';

COMMENT ON COLUMN  LCIMS.OMP_CUST_MANAGE_CUST.SOURCEUSERNAME IS '开发来源真实姓名';

COMMENT ON COLUMN  LCIMS.OMP_CUST_MANAGE_CUST.SOURCEOACODE IS '开发来源工号';

COMMENT ON COLUMN  LCIMS.OMP_CUST_MANAGE_CUST.OPENTIME IS '财富号开通时间';

COMMENT ON COLUMN  LCIMS.OMP_CUST_MANAGE_CUST.ISOPEN IS '是否开通财富号 0-未开通 1-已开通';

COMMENT ON COLUMN  LCIMS.OMP_CUST_MANAGE_CUST.BASICINFO IS '企业基本信息';

COMMENT ON COLUMN  LCIMS.OMP_CUST_MANAGE_CUST.CONTRASTINFO IS '可比公司对比分析';

COMMENT ON COLUMN  LCIMS.OMP_CUST_MANAGE_CUST.CUSTSHORTNAME IS '客户简称';

COMMENT ON COLUMN  LCIMS.OMP_CUST_MANAGE_CUST.ISPAYCUST IS '是否付费客户 1-是 0-否';

COMMENT ON COLUMN  LCIMS.OMP_CUST_MANAGE_CUST.AUDITTIMETYPE IS '预算审批时间 1-未沟通 2-按实际需求确定 3-选择时间';

COMMENT ON COLUMN  LCIMS.OMP_CUST_MANAGE_CUST.AUDITTIME IS '预算审批时间';

COMMENT ON COLUMN  LCIMS.OMP_CUST_MANAGE_CUST.REGADDRESS IS '注册地址';

COMMENT ON COLUMN  LCIMS.OMP_CUST_MANAGE_CUST.PROVINCE IS '省份';

COMMENT ON COLUMN  LCIMS.OMP_CUST_MANAGE_CUST.CITY IS '城市';

COMMENT ON COLUMN  LCIMS.OMP_CUST_MANAGE_CUST.OPERATEINCOME IS '营业收入';

COMMENT ON COLUMN  LCIMS.OMP_CUST_MANAGE_CUST.NETPROFIT IS '净利润';

COMMENT ON COLUMN  LCIMS.OMP_CUST_MANAGE_CUST.REPORTNAME IS '最新报告期名称';

COMMENT ON COLUMN  LCIMS.OMP_CUST_MANAGE_CUST.REPROVINCE IS '报告期省份';

COMMENT ON COLUMN  LCIMS.OMP_CUST_MANAGE_CUST.INDUSTRY IS '细分行业';

COMMENT ON COLUMN  LCIMS.OMP_CUST_MANAGE_CUST.REGPROVINCE IS '注册地省份';

COMMENT ON COLUMN  LCIMS.OMP_CUST_MANAGE_CUST.REGCITY IS '注册地城市';

COMMENT ON COLUMN  LCIMS.OMP_CUST_MANAGE_CUST.OPTPROVINCE IS '运营地省份';

COMMENT ON COLUMN  LCIMS.OMP_CUST_MANAGE_CUST.OPTCITY IS '运营地城市';

COMMENT ON COLUMN  LCIMS.OMP_CUST_MANAGE_CUST.LISTSTATE IS '上市状态';

COMMENT ON COLUMN  LCIMS.OMP_CUST_MANAGE_CUST.ORGTYPE IS '机构类型';

COMMENT ON COLUMN  LCIMS.OMP_CUST_MANAGE_CUST.ORGTYPE_BD IS '客户类型映射';

COMMENT ON COLUMN  LCIMS.OMP_CUST_MANAGE_CUST.ORGTYPECODE IS '机构类型编码';

CREATE TABLE  LCIMS.OMP_CUST_MANAGE_USERINFO (
	EID NUMBER(18,0),
	EITIME DATE,
	EUTIME DATE,
	USERID NUMBER(18,0),
	REALNAME VARCHAR2(50),
	DEPARTMENTNO VARCHAR2(10),
	MOBILE VARCHAR2(50),
	EMAIL VARCHAR2(64),
	OACODE VARCHAR2(128),
	DEPARTMENTNAME VARCHAR2(100),
	GROUPRANK NUMBER(2,0),
	GROUPEID NUMBER(18,0),
	PERMISSIONSTATUS VARCHAR2(2),
	ISDELETE NUMBER(2,0) DEFAULT 0,
	ISLEAVE NUMBER(2,0) DEFAULT 0,
CONSTRAINT PK_OMP_CUST_MANAGE_USERINFO PRIMARY KEY (EID)
);
CREATE INDEX OC_MANAGE_USERINFO_USERID_IDX ON  LCIMS.OMP_CUST_MANAGE_USERINFO (USERID);


COMMENT ON TABLE  LCIMS.OMP_CUST_MANAGE_USERINFO IS '展业人员信息维护表';

COMMENT ON COLUMN  LCIMS.OMP_CUST_MANAGE_USERINFO.EID IS '系统物理主键';

COMMENT ON COLUMN  LCIMS.OMP_CUST_MANAGE_USERINFO.EITIME IS '数据入库时间';

COMMENT ON COLUMN  LCIMS.OMP_CUST_MANAGE_USERINFO.EUTIME IS '最近一次修改时间';

COMMENT ON COLUMN  LCIMS.OMP_CUST_MANAGE_USERINFO.USERID IS '用户eid';

COMMENT ON COLUMN  LCIMS.OMP_CUST_MANAGE_USERINFO.REALNAME IS '用户名称';

COMMENT ON COLUMN  LCIMS.OMP_CUST_MANAGE_USERINFO.DEPARTMENTNO IS '所属部门';

COMMENT ON COLUMN  LCIMS.OMP_CUST_MANAGE_USERINFO.MOBILE IS '手机号';

COMMENT ON COLUMN  LCIMS.OMP_CUST_MANAGE_USERINFO.EMAIL IS '邮箱';

COMMENT ON COLUMN  LCIMS.OMP_CUST_MANAGE_USERINFO.OACODE IS '员工工号';

COMMENT ON COLUMN  LCIMS.OMP_CUST_MANAGE_USERINFO.DEPARTMENTNAME IS '部门名称';

COMMENT ON COLUMN  LCIMS.OMP_CUST_MANAGE_USERINFO.GROUPRANK IS '团队职务 1-团队长  2-成员';

COMMENT ON COLUMN  LCIMS.OMP_CUST_MANAGE_USERINFO.GROUPEID IS '团队eid';

COMMENT ON COLUMN  LCIMS.OMP_CUST_MANAGE_USERINFO.PERMISSIONSTATUS IS '财富管家企业客户权限 1是 0否';

COMMENT ON COLUMN  LCIMS.OMP_CUST_MANAGE_USERINFO.ISDELETE IS '是否删除 0-未删除 1-已删除';

COMMENT ON COLUMN  LCIMS.OMP_CUST_MANAGE_USERINFO.ISLEAVE IS '是否离职 0-未离职 1-已离职';