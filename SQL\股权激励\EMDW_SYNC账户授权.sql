CREATE USER EMDW_SYNC IDENTIFIED BY gnD45x1ZtFU9y;


GRANT SELECT, INSERT, UPDATE, DELETE ON LCIMS.LOGASSET TO emdw_sync;
GRANT SELECT, INSERT, UPDATE, DELETE ON LCIMS.OMP_USER_QRCODE TO emdw_sync;
GRANT SELECT, INSERT, UPDATE, DELETE ON LCIMS.OMP_ENTER_SIGN_MANAGER TO emdw_sync;
GRANT SELECT, INSERT, UPDATE, DELETE ON LCIMS.OMP_ENTER_SIGN_RECORD TO emdw_sync;

GRANT SELECT, INSERT, UPDATE, DELETE ON LCIMS.OMP_CUST_BELONG TO emdw_sync;
GRANT SELECT, INSERT, UPDATE, DELETE ON LCIMS.OMP_CUST_BELONG_VALUE TO emdw_sync;
GRANT SELECT, INSERT, UPDATE, DELETE ON LCIMS.OMP_CUST_MANA<PERSON>_CUST TO emdw_sync;
GRANT SELECT, INSERT, UPDATE, DELETE ON LCIMS.OMP_CUST_MANAGE_USERINFO TO emdw_sync;

-- 查看系统对象权限
GRANT SELECT ON SYS.DBA_TABLES TO emdw_sync;
GRANT SELECT ON SYS.DBA_USERS TO emdw_sync;

-- 可选显式授权（
GRANT SELECT ON SYS.USER_TAB_COMMENTS TO emdw_sync;
GRANT SELECT ON SYS.USER_CONS_COLUMNS TO emdw_sync;
GRANT SELECT ON SYS.USER_CONSTRAINTS TO emdw_sync;
GRANT SELECT ON SYS.USER_TAB_COLUMNS TO emdw_sync;
GRANT SELECT ON SYS.USER_COL_COMMENTS TO emdw_sync;
--- 下掉同步任务
-- LCIMS.OMP_CUST_BELONG TO emdw_sync;
-- LCIMS.OMP_CUST_BELONG_VALUE TO emdw_sync;
-- LCIMS.OMP_CUST_MANAGE_CUST TO emdw_sync;
-- LCIMS.OMP_CUST_MANAGE_USERINFO TO emdw_sync;