# 1. 流程图

```plantuml
title 期权持仓查询迁移

participant APP
participant 股权激励中台服务
participant 股权激励数据库
participant 股权激励前置机通讯服务
participant 柜台
APP->股权激励中台服务:查询期权持仓信息

股权激励中台服务->股权激励数据库:查询用户股票期权激励计划
股权激励中台服务<-股权激励数据库:return
股权激励中台服务->股权激励前置机通讯服务:查询持仓明细
股权激励前置机通讯服务->柜台:查询303[410503]-持股明细
股权激励前置机通讯服务<-柜台:return
股权激励中台服务<-股权激励前置机通讯服务:return
股权激励中台服务->股权激励中台服务:筛选证券类型为Q权证的持仓
loop 持仓明细信息
股权激励中台服务->股权激励前置机通讯服务:查询权证信息
股权激励前置机通讯服务->柜台:查询275[410231]-取权证信息
股权激励前置机通讯服务<-柜台:return
股权激励中台服务<-股权激励前置机通讯服务:return
股权激励中台服务->股权激励中台服务:将权证信息设置到响应结果
股权激励中台服务->股权激励中台服务:根据行权代码匹配对应的计划批次信息，并将计划信息设置到响应结果
end
APP<-股权激励中台服务:return

```

# 2. 涉及的数据库、Redis、Kafka 等中间件的修改/新增图或者说明

不涉及

# 3. 前后端交互接口信息

## 3.1. 期权持仓查询

### 3.1.1. 接口路径

POST:financingOption/queryOptionPositions

### 3.1.2. 接口入参

无

### 3.1.3. 3. 接口出参

| 参数名称 | 参数含义     | 参数类型 | 是否必填 | 备注 |
| -------- | ------------ | -------- | -------- | ---- |
| Bdzqdm   | 标的证券代码 | String   | 是       |      |
| Bdzqmc   | 标的证券名称 | String   | 是       |      |
| Gddm     | 股东代码     | String   | 是       |      |
| Market   | 交易市场     | String   | 是       |      |
| Gfye     | 股份余额     | String   | 是       |      |
| Kysl     | 可用数量     | String   | 是       |      |
| Xqdm     | 行权代码     | String   | 是       |      |
| Xqdmmc   | 行权代码名称 | String   | 是       |      |
| Xqjg     | 行权价格     | String   | 是       |      |
| planid   | 计划ID       | String   | 是       |      |
| version  | 批次ID       | String   | 是       |      |

# 4. 配置&部署修改信息

不涉及

# 5. 新增技术组件说明

不涉及

# 6. 影响范围

不涉及

# 7. 外部依赖项

柜台
