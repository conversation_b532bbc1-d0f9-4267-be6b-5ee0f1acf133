--- 持仓查询,stklastbal股份余额，stkavl可用数量
SELECT *,d.stktype FROM run.dbo.stkasset s inner JOIN run.dbo.stktrd d ON s.stkcode = d.stkcode WHERE fundid = '541220113676';
--- 行权代码信息查询
SELECT * FROM run.dbo.stktrd;
--- 权证信息表，warrantcode行权代码,exerprice行权价格，exerratio行权比例，行权开始日期exerbegindate，行权结束日期exerenddate
SELECT * FROM run.dbo.warranttrd WHERE stkcode= '038036';
--- 融资行权担保品比例表
SELECT * FROM run.dbo.rzxq_pledgestkrate WHERE market =1;
--- 合约查询，dueintr利息=(conamt行权金额-payconamt已归还本金+taxamt扣税金额-paytaxamt已还扣税金额)*(intcaldate上次计息日期-sysdate当前时间)*duerate利息年利率/(360或者365)
--- rzdebt客户需偿还负债=conamt行权金额-payconamt已归还本金-payconamt_real实时已归还本金+taxamt扣税金额-paytaxamt已还扣税金额-paytaxamt_real实时已还扣税金额+dueintr应付利息-paydueintr已还利息–paydueintr_real实时已还利息+puniintr应付违约金-paypuniintr已还违约金-paypuniintr_real实时已还违约金
SELECT * FROM run.dbo.rzxq_debt;
----融资行权客户权证额度信息查询
SELECT * FROM run.dbo.rzxq_stkquota;

SELECT * FROM run.dbo.rzxq_comparam;
SELECT * FROM run.dbo.rzxq_stkscale;
--- 融资行权委托
SELECT * FROM run.dbo.fjy_orderrec;
SELECT * FROM run.dbo.fjy_orderrec;
--- 融资行权配置
select * from run.dbo.mixedconfig m where paraid LIKE '%rzxq%'
select * from run.dbo.mixedconfig m where paraid LIKE '%rzxq%'
--- 股权激励人员信息
SELECT * FROM  run.dbo.optionpersonmsg WHERE custid = '541220113676'
--- 融资行权税率速算扣除数
select beginamt,endamt,taxrate,freeamt from run..taxratepolicy