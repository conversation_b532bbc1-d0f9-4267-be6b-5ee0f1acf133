/* 全局 Dataview 表格 */
.dataview.table-view-table {
    width: 100%;
    border-collapse: collapse;
    background-color: #ffffff !important ; /* 纯白背景 */
    color: #333333 !important; /* 深灰文字 */
    font-family: "Helvetica", sans-serif;
    border: 1px solid #e0e0e0;
}
/* 覆盖问题选择器 */
table.dataview.table-view-table thead.table-view-thead tr > th.table-view-th:nth-child(2n+2) {
    background-color: #5a6ded !important;
    color: #ffffff !important;
}
/* 表头 */
.table-view-thead .table-view-th {
    background-color: #5a6ded; /* 主色蓝色 */
    color: #ffffff;
    padding: 10px;
    text-align: left;
    border-bottom: 2px solid #485cd2;
    font-weight: 500;
}

/* 小数字（文件计数） */
.table-view-th .dataview.small-text {
    color: #d6dcff;
    font-size: 0.85em;
    margin-left: 5px;
}

/* 表格主体单元格 - 强制统一 */
.table-view-tbody td {
    padding: 8px;
    border-bottom: 1px solid #e6e6e6;
}

/* 链接 */
.table-view-tbody .internal-link {
    color: #5a6ded;
    text-decoration: none;
    transition: color 0.2s ease;
}

.table-view-tbody .internal-link:hover {
    color: #7a8cff !important;
    text-decoration: underline;
}

/* 行效果 */
.table-view-tbody tr:nth-child(even) {
    background-color: #f9f9f9 !important;
}

.table-view-tbody tr:hover {
    background-color: #f0f2ff !important;
}

