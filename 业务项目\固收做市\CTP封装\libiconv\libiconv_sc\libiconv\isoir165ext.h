/*
 * Copyright (C) 1999-2001 Free Software Foundation, Inc.
 * This file is part of the GNU LIBICONV Library.
 *
 * The GNU LIBICONV Library is free software; you can redistribute it
 * and/or modify it under the terms of the GNU Library General Public
 * License as published by the Free Software Foundation; either version 2
 * of the License, or (at your option) any later version.
 *
 * The GNU LIBICONV Library is distributed in the hope that it will be
 * useful, but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
 * Library General Public License for more details.
 *
 * You should have received a copy of the GNU Library General Public
 * License along with the GNU LIBICONV Library; see the file COPYING.LIB.
 * If not, write to the Free Software Foundation, Inc., 51 Franklin Street,
 * Fifth Floor, Boston, MA 02110-1301, USA.
 */

/*
 * ISO-IR-165 extensions
 */

static const unsigned short isoir165ext_2uni_page2b[470] = {
  /* 0x2b */
  0x1fb1, 0x03ac, 0x1fb0, 0x1f70, 0x0113, 0x00e9, 0x011b, 0x00e8,
  0x012b, 0x00ed, 0x01d0, 0x00ec, 0x014d, 0x00f3, 0x01d2, 0x00f2,
  0x016b, 0x00fa, 0x01d4, 0x00f9, 0x01d6, 0x01d8, 0x01da, 0x01dc,
  0x00fc, 0x00ea, 0x03b1, 0x1e3f, 0x0144, 0x0148, 0x01f9, 0xff47,
  0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd,
  0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd,
  0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd,
  0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd,
  0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd,
  0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd,
  0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd,
  0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd,
  /* 0x2c */
  0x53be, 0x4eb8, 0x4f3e, 0x501e, 0x50c7, 0x9118, 0x6c98, 0x6cdc,
  0x6cc3, 0x6e5d, 0x6ea6, 0x6eeb, 0x6fa5, 0x6165, 0x5ea4, 0x9618,
  0x5848, 0x8453, 0x7cf5, 0x5f07, 0x6294, 0x647d, 0x725a, 0x5574,
  0x55a4, 0x5640, 0x5684, 0x5d1f, 0x72c9, 0x998c, 0x59de, 0x59fd,
  0x5a5e, 0x7ebb, 0x7ee4, 0x7ef9, 0x9a99, 0x71cf, 0x6245, 0x624a,
  0x797c, 0x739a, 0x742b, 0x7488, 0x74aa, 0x74d8, 0x6767, 0x6ab5,
  0x71ca, 0x6ba3, 0x8f80, 0x8f92, 0x8d5f, 0x9b36, 0x72a8, 0x87a3,
  0x8152, 0x6b38, 0x98d0, 0x8897, 0x88af, 0x8955, 0x770a, 0x94da,
  0x955a, 0x9560, 0x9e24, 0x9e40, 0x9e50, 0x9e5d, 0x9e60, 0x870e,
  0x7b5c, 0x7fd9, 0x7fef, 0x7e44, 0x8e45, 0x8e36, 0x8e62, 0x8e5c,
  0x9778, 0x9b46, 0x9f2b, 0x9f41, 0x7526, 0x4e26, 0x8bac, 0x8129,
  0x5091, 0x50cd, 0x52b9, 0x89d4, 0x5557, 0x94c7,
  /* 0x2d */
  0x9496, 0x9498, 0x94cf, 0x94d3, 0x94d4, 0x94e6, 0x9533, 0x951c,
  0x9520, 0x9527, 0x953d, 0x9543, 0x956e, 0x9574, 0x9c80, 0x9c84,
  0x9c8a, 0x9c93, 0x9c96, 0x9c97, 0x9c98, 0x9c99, 0x9cbf, 0x9cc0,
  0x9cc1, 0x9cd2, 0x9cdb, 0x9ce0, 0x9ce3, 0x9770, 0x977a, 0x97a1,
  0x97ae, 0x97a8, 0x9964, 0x9966, 0x9978, 0x9979, 0x997b, 0x997e,
  0x9982, 0x9983, 0x998e, 0x9b10, 0x9b18, 0x65a2, 0x9e80, 0x911c,
  0x9e91, 0x9f12, 0x52f3, 0x6c96, 0x6d44, 0x6e1b, 0x6e67, 0x6f82,
  0x6fec, 0x60ae, 0x5ec8, 0x8ffa, 0x577f, 0x5586, 0x849e, 0x8460,
  0x5c05, 0x5e0b, 0x5d11, 0x5d19, 0x5dd6, 0x59b3, 0x5aae, 0x9a94,
  0x658f, 0x709e, 0x7551, 0x71ff, 0x691d, 0x6a11, 0x68bf, 0x6607,
  0x668e, 0x6673, 0x6c25, 0x7652, 0x778b, 0x76ea, 0x9895, 0x8780,
  0x882d, 0x7b87, 0x7c50, 0x8ead, 0x9575, 0x65c2,
  /* 0x2e */
  0x5390, 0x79b8, 0x4f15, 0x4f21, 0x4f3b, 0x4fa2, 0x50a4, 0x5092,
  0x530a, 0x51c3, 0x51a8, 0x8d20, 0x5787, 0x579a, 0x5795, 0x57eb,
  0x585d, 0x585a, 0x5871, 0x5895, 0x5c30, 0x5f0c, 0x5f0d, 0x5f0e,
  0x5c72, 0x5cc7, 0x5fac, 0x5f68, 0x5f5f, 0x5a12, 0x5a65, 0x5a84,
  0x5ac4, 0x7394, 0x73ea, 0x73ee, 0x7437, 0x7415, 0x7454, 0x6799,
  0x686c, 0x68f8, 0x69fe, 0x72e2, 0x6667, 0x8d52, 0x89c3, 0x89cd,
  0x6427, 0x6477, 0x6c1d, 0x813f, 0x6b54, 0x98d6, 0x707a, 0x70f1,
  0x7120, 0x6153, 0x6c87, 0x6dad, 0x6e81, 0x6eb5, 0x6f94, 0x6f9b,
  0x793d, 0x794e, 0x7806, 0x7859, 0x7894, 0x78dc, 0x7903, 0x7a16,
  0x7a5e, 0x75e0, 0x7adc, 0x7676, 0x9892, 0x7bf2, 0x7c30, 0x7c5d,
  0x9c9d, 0x7cac, 0x8278, 0x83d1, 0x84ea, 0x7fc0, 0x7f1e, 0x8e21,
  0x8e53, 0x9754, 0x9f0c, 0x94fb, 0xfffd, 0xfffd,
  /* 0x2f */
  0x32c0, 0x32c1, 0x32c2, 0x32c3, 0x32c4, 0x32c5, 0x32c6, 0x32c7,
  0x32c8, 0x32c9, 0x32ca, 0x32cb, 0x33e0, 0x33e1, 0x33e2, 0x33e3,
  0x33e4, 0x33e5, 0x33e6, 0x33e7, 0x33e8, 0x33e9, 0x33ea, 0x33eb,
  0x33ec, 0x33ed, 0x33ee, 0x33ef, 0x33f0, 0x33f1, 0x33f2, 0x33f3,
  0x33f4, 0x33f5, 0x33f6, 0x33f7, 0x33f8, 0x33f9, 0x33fa, 0x33fb,
  0x33fc, 0x33fd, 0x33fe, 0x3358, 0x3359, 0x335a, 0x335b, 0x335c,
  0x335d, 0x335e, 0x335f, 0x3360, 0x3361, 0x3362, 0x3363, 0x3364,
  0x3365, 0x3366, 0x3367, 0x3368, 0x3369, 0x336a, 0x336b, 0x336c,
  0x336d, 0x336e, 0x336f, 0x3370, 0x3037, 0x90a8, 0x965e, 0x5842,
  0x5803, 0x6c3e, 0x6d29, 0x6ee7, 0x8534, 0x84c6, 0x633c, 0x5d05,
  0x7f10, 0x7eec, 0x7287, 0x712e, 0x8218, 0x8216, 0x756c, 0x75f3,
  0x9b25, 0x8980, 0x7ca6, 0x4e85, 0x5570, 0x91c6,
};
static const unsigned short isoir165ext_2uni_page7a[470] = {
  /* 0x7a */
  0x4e0f, 0x673f, 0x4e42, 0x752a, 0x592c, 0x9ee1, 0x8652, 0x531c,
  0x5187, 0x518f, 0x50f0, 0x4f0b, 0x4f23, 0x4f03, 0x4f61, 0x4f7a,
  0x4f6b, 0x4feb, 0x4ff5, 0x5034, 0x5022, 0x4ff6, 0x5072, 0x4eb6,
  0x51ae, 0x5910, 0x6bda, 0x522c, 0x5232, 0x4fb4, 0x5298, 0x52bb,
  0x52bc, 0x52cd, 0x52da, 0x52f7, 0x53c6, 0x53c7, 0x5770, 0x576c,
  0x57b1, 0x579f, 0x579e, 0x57be, 0x57cc, 0x580e, 0x580c, 0x57f5,
  0x5809, 0x583c, 0x5843, 0x5845, 0x5846, 0x583d, 0x5853, 0x5888,
  0x5884, 0x58f8, 0x56ad, 0x5940, 0x5953, 0x596d, 0x5c2a, 0x54a5,
  0x551d, 0x5536, 0x556f, 0x554d, 0x569a, 0x569c, 0x56f7, 0x5710,
  0x5719, 0x5e17, 0x5e21, 0x5e28, 0x5e6a, 0x5c74, 0x5c7c, 0x5ca8,
  0x5c9e, 0x5cc3, 0x5cd3, 0x5ce3, 0x5ce7, 0x5cff, 0x5d04, 0x5d00,
  0x5d1a, 0x5d0c, 0x5d4e, 0x5d5a, 0x5d85, 0x5d93,
  /* 0x7b */
  0x5d92, 0x5dc2, 0x5dc9, 0x8852, 0x5faf, 0x5906, 0x65a8, 0x7241,
  0x7242, 0x5ebc, 0x5ecb, 0x95ec, 0x95ff, 0x8a1a, 0x9607, 0x9613,
  0x961b, 0x5bac, 0x5ba7, 0x5c5d, 0x5f22, 0x59ee, 0x5a7c, 0x5a96,
  0x5a73, 0x5a9e, 0x5aad, 0x5ada, 0x5aea, 0x5b1b, 0x5b56, 0x9a72,
  0x9a83, 0x9a89, 0x9a8d, 0x9a8e, 0x9a95, 0x9aa6, 0x7395, 0x7399,
  0x73a0, 0x73b1, 0x73a5, 0x73a6, 0x73d6, 0x73f0, 0x73fd, 0x73e3,
  0x7424, 0x740e, 0x7407, 0x73f6, 0x73fa, 0x7432, 0x742f, 0x7444,
  0x7442, 0x7471, 0x7478, 0x7462, 0x7486, 0x749f, 0x74a0, 0x7498,
  0x74b2, 0x97e8, 0x6745, 0x679f, 0x677b, 0x67c8, 0x67ee, 0x684b,
  0x68a0, 0x6812, 0x681f, 0x686a, 0x68bc, 0x68fb, 0x686f, 0x68b1,
  0x68c1, 0x68eb, 0x6913, 0x68d1, 0x6911, 0x68d3, 0x68ec, 0x692b,
  0x68e8, 0x69be, 0x6969, 0x6940, 0x696f, 0x695f,
  /* 0x7c */
  0x6962, 0x6935, 0x6959, 0x69bc, 0x69c5, 0x69da, 0x69dc, 0x6a0b,
  0x69e5, 0x6a66, 0x6a96, 0x6ab4, 0x72dd, 0x5cf1, 0x7314, 0x733a,
  0x6b95, 0x5f67, 0x80fe, 0x74fb, 0x7503, 0x655c, 0x6569, 0x6f26,
  0x65f8, 0x65fb, 0x6609, 0x663d, 0x6662, 0x665e, 0x666c, 0x668d,
  0x668b, 0x8d51, 0x8d57, 0x7263, 0x7277, 0x63b1, 0x6261, 0x6260,
  0x6283, 0x62e4, 0x62c3, 0x631c, 0x6326, 0x63af, 0x63fe, 0x6422,
  0x6412, 0x64ed, 0x6713, 0x6718, 0x8158, 0x81d1, 0x98cf, 0x98d4,
  0x98d7, 0x6996, 0x7098, 0x70dc, 0x70fa, 0x710c, 0x711c, 0x71cb,
  0x721f, 0x70dd, 0x659d, 0x6246, 0x6017, 0x60c7, 0x60d3, 0x60b0,
  0x60d9, 0x6114, 0x6c3f, 0x6c67, 0x6c84, 0x6c9a, 0x6c6d, 0x6ca8,
  0x6cc6, 0x6cb5, 0x6d49, 0x6d38, 0x6d11, 0x6d3a, 0x6d28, 0x6d50,
  0x6d34, 0x6d55, 0x6d61, 0x6da2, 0x6d65, 0x6d5b,
  /* 0x7d */
  0x6d64, 0x6db4, 0x6e9a, 0x6e5c, 0x6e72, 0x6ea0, 0x6e87, 0x6e8e,
  0x6ec9, 0x6ec3, 0x6f37, 0x6ed8, 0x6eea, 0x6f56, 0x6f75, 0x6f5f,
  0x6fb4, 0x6fbc, 0x7014, 0x700d, 0x700c, 0x703c, 0x7943, 0x7947,
  0x794a, 0x7950, 0x7972, 0x7998, 0x79a0, 0x79a4, 0x77fc, 0x77fb,
  0x7822, 0x7820, 0x7841, 0x785a, 0x7875, 0x78b6, 0x78e1, 0x7933,
  0x8a5f, 0x76fb, 0x771b, 0x772c, 0x7786, 0x77ab, 0x77ad, 0x7564,
  0x756f, 0x6983, 0x7f7d, 0x76dd, 0x76e6, 0x76ec, 0x7521, 0x79fe,
  0x7a44, 0x767f, 0x769e, 0x9e27, 0x9e2e, 0x9e30, 0x9e34, 0x9e4d,
  0x9e52, 0x9e53, 0x9e54, 0x9e56, 0x9e59, 0x9e61, 0x9e62, 0x9e65,
  0x9e6f, 0x9e74, 0x75a2, 0x7604, 0x7608, 0x761d, 0x7ad1, 0x7a85,
  0x7a8e, 0x7aa3, 0x7ab8, 0x7abe, 0x77de, 0x8030, 0x988b, 0x988e,
  0x9899, 0x98a3, 0x8683, 0x8705, 0x8758, 0x87cf,
  /* 0x7e */
  0x87e2, 0x880b, 0x80d4, 0x7f4d, 0x7b4a, 0x7b4e, 0x7b7f, 0x7b93,
  0x7bef, 0x7c09, 0x7bf0, 0x7c15, 0x7c03, 0x7c20, 0x823a, 0x8886,
  0x88aa, 0x88c0, 0x88c8, 0x8926, 0x8976, 0x7f91, 0x8283, 0x82bc,
  0x82a7, 0x8313, 0x82fe, 0x8300, 0x835d, 0x8345, 0x8344, 0x831d,
  0x83a6, 0x8399, 0x83fe, 0x841a, 0x83fc, 0x8429, 0x8439, 0x84a8,
  0x84cf, 0x849f, 0x84c2, 0x84f7, 0x8570, 0x85b3, 0x85a2, 0x96d8,
  0x85b8, 0x85e0, 0x7fda, 0x7eae, 0x7eb4, 0x7ebc, 0x7ed6, 0x7f0a,
  0x5b43, 0x8d6a, 0x5245, 0x8c68, 0x8c6e, 0x8c6d, 0x8e16, 0x8e26,
  0x8e27, 0x8e50, 0x9098, 0x90a0, 0x90bd, 0x90c8, 0x90c3, 0x90da,
  0x90ff, 0x911a, 0x910c, 0x9120, 0x9142, 0x8fb5, 0x90e4, 0x8c86,
  0x89f1, 0x8bb1, 0x8bbb, 0x8bc7, 0x8bea, 0x8c09, 0x8c1e, 0x9702,
  0x68d0, 0x7306, 0x9f81, 0x9f82, 0x92c6, 0x9491,
};

static int
isoir165ext_mbtowc (conv_t conv, ucs4_t *pwc, const unsigned char *s, int n)
{
  unsigned char c1 = s[0];
  if ((c1 >= 0x2b && c1 <= 0x2f) || (c1 >= 0x7a && c1 <= 0x7e)) {
    if (n >= 2) {
      unsigned char c2 = s[1];
      if (c2 >= 0x21 && c2 < 0x7f) {
        unsigned int i = 94 * (c1 - 0x21) + (c2 - 0x21);
        unsigned short wc = 0xfffd;
        if (i < 8366) {
          if (i < 1410)
            wc = isoir165ext_2uni_page2b[i-940];
        } else {
          if (i < 8836)
            wc = isoir165ext_2uni_page7a[i-8366];
        }
        if (wc != 0xfffd) {
          *pwc = (ucs4_t) wc;
          return 2;
        }
      }
      return RET_ILSEQ;
    }
    return RET_TOOFEW(0);
  }
  return RET_ILSEQ;
}

static const unsigned short isoir165ext_2charset[876] = {
  0x2b28, 0x2b26, 0x2b3a, 0x2b2c, 0x2b2a, 0x2b30, 0x2b2e, 0x2b34,
  0x2b32, 0x2b39, 0x2b25, 0x2b27, 0x2b29, 0x2b3d, 0x2b3e, 0x2b2d,
  0x2b31, 0x2b2b, 0x2b2f, 0x2b33, 0x2b35, 0x2b36, 0x2b37, 0x2b38,
  0x2b3f, 0x2b22, 0x2b3b, 0x2b3c, 0x2b24, 0x2b23, 0x2b21, 0x2f65,
  0x2f21, 0x2f22, 0x2f23, 0x2f24, 0x2f25, 0x2f26, 0x2f27, 0x2f28,
  0x2f29, 0x2f2a, 0x2f2b, 0x2f2c, 0x2f4c, 0x2f4d, 0x2f4e, 0x2f4f,
  0x2f50, 0x2f51, 0x2f52, 0x2f53, 0x2f54, 0x2f55, 0x2f56, 0x2f57,
  0x2f58, 0x2f59, 0x2f5a, 0x2f5b, 0x2f5c, 0x2f5d, 0x2f5e, 0x2f5f,
  0x2f60, 0x2f61, 0x2f62, 0x2f63, 0x2f64, 0x2f2d, 0x2f2e, 0x2f2f,
  0x2f30, 0x2f31, 0x2f32, 0x2f33, 0x2f34, 0x2f35, 0x2f36, 0x2f37,
  0x2f38, 0x2f39, 0x2f3a, 0x2f3b, 0x2f3c, 0x2f3d, 0x2f3e, 0x2f3f,
  0x2f40, 0x2f41, 0x2f42, 0x2f43, 0x2f44, 0x2f45, 0x2f46, 0x2f47,
  0x2f48, 0x2f49, 0x2f4a, 0x2f4b, 0x7a21, 0x2c76, 0x7a23, 0x2f7c,
  0x7a38, 0x2c22, 0x7a2e, 0x7a2c, 0x2e23, 0x2e24, 0x7a2d, 0x2e25,
  0x2c23, 0x7a2f, 0x7a31, 0x7a30, 0x2e26, 0x7a3e, 0x7a32, 0x7a33,
  0x7a36, 0x2c24, 0x7a35, 0x7a34, 0x7a37, 0x2c79, 0x2e28, 0x2e27,
  0x2c25, 0x2c7a, 0x7a2b, 0x7a29, 0x7a2a, 0x2e2b, 0x7a39, 0x2e2a,
  0x7a3c, 0x7a3d, 0x7e5b, 0x7a3f, 0x2c7b, 0x7a40, 0x7a41, 0x7a42,
  0x7a43, 0x2d53, 0x7a44, 0x2e29, 0x7a28, 0x2e21, 0x2c21, 0x7a45,
  0x7a46, 0x7a60, 0x7a61, 0x7a62, 0x7a64, 0x2c7d, 0x7a63, 0x2f7d,
  0x2c38, 0x2d5e, 0x2c39, 0x2c3a, 0x2c3b, 0x7a65, 0x7a66, 0x7a5b,
  0x7a67, 0x7a68, 0x7a69, 0x7a48, 0x7a47, 0x2d5d, 0x2e2d, 0x2e2f,
  0x2e2e, 0x7a4b, 0x7a4a, 0x7a49, 0x7a4c, 0x7a4d, 0x2e30, 0x7a50,
  0x2f69, 0x7a51, 0x7a4f, 0x7a4e, 0x7a52, 0x7a56, 0x2f68, 0x7a53,
  0x7a54, 0x7a55, 0x2c31, 0x7a57, 0x2e32, 0x2e31, 0x2e33, 0x7a59,
  0x7a58, 0x2e34, 0x7a5a, 0x7b26, 0x7a3a, 0x7a25, 0x7a5c, 0x7a5d,
  0x7a5e, 0x2d66, 0x2c3f, 0x7b36, 0x2c40, 0x2e3e, 0x2c41, 0x2e3f,
  0x7b39, 0x7b37, 0x2e40, 0x7b38, 0x7b3a, 0x7b3b, 0x2d67, 0x2e41,
  0x7b3c, 0x7b3d, 0x7b3e, 0x7e59, 0x7b3f, 0x7b33, 0x7b32, 0x2d61,
  0x7a5f, 0x2e35, 0x7b34, 0x2e39, 0x7a6e, 0x7a6f, 0x7a71, 0x7a70,
  0x7a72, 0x2e3a, 0x7a73, 0x7a74, 0x7a75, 0x7c2e, 0x7a76, 0x7a78,
  0x7a77, 0x2f70, 0x7a7a, 0x2d63, 0x2d64, 0x7a79, 0x2c3c, 0x7a7b,
  0x7a7c, 0x7a7d, 0x7b21, 0x7a7e, 0x7b22, 0x7b23, 0x2d65, 0x2d62,
  0x7a6a, 0x7a6b, 0x7a6c, 0x7a6d, 0x2c2f, 0x7b2a, 0x2d5b, 0x7b2b,
  0x2c34, 0x2e36, 0x2e37, 0x2e38, 0x7b35, 0x2e3d, 0x7c32, 0x2e3c,
  0x2e3b, 0x7b25, 0x7c65, 0x2d5a, 0x7c68, 0x7c66, 0x7c67, 0x7c69,
  0x7c6a, 0x2e5a, 0x2c2e, 0x2c47, 0x7c64, 0x2c48, 0x7c48, 0x7c47,
  0x7c49, 0x2c35, 0x7c4b, 0x7c4a, 0x7c4c, 0x7c4d, 0x2f6f, 0x7c4e,
  0x7c46, 0x7c4f, 0x7c51, 0x7c50, 0x2e51, 0x2e52, 0x2c36, 0x7c52,
  0x7c36, 0x7c37, 0x2d69, 0x7c63, 0x2d4e, 0x7b27, 0x2d7e, 0x7c39,
  0x7c3a, 0x2d70, 0x7c3b, 0x7c3c, 0x7c3e, 0x7c3d, 0x2e4d, 0x7c3f,
  0x2d72, 0x7c41, 0x7c40, 0x2d71, 0x7c53, 0x7c54, 0x7a22, 0x7b63,
  0x2c4f, 0x7b65, 0x2e48, 0x7b64, 0x7b66, 0x7b67, 0x7b6a, 0x7b6b,
  0x7b68, 0x7b6c, 0x2e49, 0x7b6f, 0x7b69, 0x7b70, 0x7b6d, 0x2d6f,
  0x7b71, 0x7e79, 0x7b74, 0x7b76, 0x7b79, 0x7b72, 0x7b77, 0x2e4a,
  0x7b6e, 0x7b75, 0x7b73, 0x2d6d, 0x7b78, 0x7c22, 0x7b7c, 0x7c23,
  0x7b7e, 0x7c21, 0x7b7b, 0x7b7d, 0x7d52, 0x7c5a, 0x7c24, 0x7b7a,
  0x7c25, 0x7c26, 0x7c27, 0x7c29, 0x2e4b, 0x7c28, 0x2d6e, 0x7c2a,
  0x7c2b, 0x7c2c, 0x2c50, 0x2c5a, 0x2e55, 0x7c31, 0x2c52, 0x7a3b,
  0x2e53, 0x2d73, 0x2f6a, 0x7c6b, 0x7c6c, 0x7c6f, 0x7c6d, 0x2e5b,
  0x2d54, 0x2c27, 0x7c6e, 0x7c70, 0x7c72, 0x2c29, 0x7c71, 0x2c28,
  0x7c75, 0x7c77, 0x2f6b, 0x7c79, 0x7c74, 0x7c76, 0x2d55, 0x7c73,
  0x7c78, 0x7c7a, 0x7c7e, 0x7c7b, 0x7d21, 0x7c7d, 0x7c7c, 0x2e5c,
  0x7d22, 0x2d56, 0x7d24, 0x2c2a, 0x2d57, 0x7d25, 0x2e5d, 0x7d27,
  0x7d28, 0x7d23, 0x7d26, 0x2c2b, 0x2e5e, 0x7d2a, 0x7d29, 0x7d2c,
  0x2f6c, 0x7d2d, 0x2c2c, 0x7c38, 0x7d2b, 0x7d2e, 0x7d30, 0x7d2f,
  0x2d58, 0x2e5f, 0x2e60, 0x2c2d, 0x7d31, 0x7d32, 0x2d59, 0x7d35,
  0x7d34, 0x7d33, 0x7d36, 0x2e57, 0x7c5b, 0x2d6a, 0x7c5c, 0x7c62,
  0x2e58, 0x7c5d, 0x7c5e, 0x7c5f, 0x2e59, 0x2f74, 0x2c51, 0x7c60,
  0x2c46, 0x2d6c, 0x7c61, 0x7b28, 0x7b29, 0x2c37, 0x7c44, 0x7c45,
  0x2f73, 0x2c57, 0x2c3d, 0x7c2d, 0x2e4c, 0x7e7a, 0x7c2f, 0x7c30,
  0x2e42, 0x7b47, 0x7b48, 0x2c4a, 0x7b49, 0x7b4b, 0x7b4c, 0x7b4a,
  0x7b4d, 0x7b50, 0x2e43, 0x2e44, 0x7b4e, 0x7b54, 0x7b55, 0x7b4f,
  0x7b53, 0x7b52, 0x2e46, 0x7b51, 0x2c4b, 0x7b57, 0x7b56, 0x2e45,
  0x7b59, 0x7b58, 0x2e47, 0x7b5c, 0x7b5a, 0x7b5b, 0x7b5d, 0x2c4c,
  0x7b60, 0x7b5e, 0x7b5f, 0x2c4d, 0x7b61, 0x2c4e, 0x7c34, 0x7c35,
  0x7d57, 0x2c75, 0x7a24, 0x2d6b, 0x7d50, 0x2f77, 0x7d51, 0x7d6b,
  0x2e6a, 0x2f78, 0x7d6c, 0x7d6d, 0x7d6e, 0x2d74, 0x2e6c, 0x7d5a,
  0x7d5b, 0x7d54, 0x7d55, 0x2d76, 0x7d56, 0x7d4a, 0x2c5f, 0x7d4b,
  0x7d4c, 0x7d4d, 0x2d75, 0x7d4e, 0x7d4f, 0x7d75, 0x7d40, 0x7d3f,
  0x2e63, 0x7d42, 0x7d41, 0x7d43, 0x2e64, 0x7d44, 0x7d45, 0x2e65,
  0x7d46, 0x2e66, 0x7d47, 0x2e67, 0x7d48, 0x2e61, 0x7d37, 0x7d38,
  0x7d39, 0x2e62, 0x7d3a, 0x7d3b, 0x2c49, 0x7d3c, 0x7d3d, 0x7d3e,
  0x2e22, 0x7d58, 0x2e68, 0x7d59, 0x2e69, 0x7d70, 0x7d71, 0x7d72,
  0x7d73, 0x7d74, 0x7d6f, 0x2e6b, 0x7e25, 0x7e26, 0x2c69, 0x7e27,
  0x2d7a, 0x7e28, 0x7e29, 0x7e2b, 0x2e6e, 0x7e2d, 0x7e2a, 0x7e2c,
  0x7e2e, 0x2e6f, 0x2d7b, 0x2e70, 0x2f7b, 0x2e72, 0x2c33, 0x2c6c,
  0x7e54, 0x7e55, 0x2c42, 0x7e56, 0x7e57, 0x2c43, 0x2f72, 0x2c44,
  0x7e58, 0x2f71, 0x2e77, 0x7e24, 0x7d53, 0x7e36, 0x2e76, 0x2c6a,
  0x7e53, 0x2c6b, 0x7d76, 0x7e23, 0x7c33, 0x2c78, 0x2e54, 0x2c59,
  0x7c55, 0x7c56, 0x2f76, 0x2f75, 0x7e2f, 0x2e73, 0x7e37, 0x7e39,
  0x7e38, 0x7e3b, 0x7e3c, 0x7e3a, 0x7e40, 0x7e3f, 0x7e3e, 0x7e3d,
  0x7e42, 0x7e41, 0x2e74, 0x7e45, 0x7e43, 0x7e44, 0x7e46, 0x7e47,
  0x2c32, 0x2d60, 0x2d5f, 0x7e4a, 0x7e48, 0x7e4b, 0x2f6e, 0x7e49,
  0x2e75, 0x7e4c, 0x2f6d, 0x7e4d, 0x7e4f, 0x7e4e, 0x7e51, 0x7e52,
  0x7a27, 0x7d7b, 0x7d7c, 0x2c68, 0x7d7d, 0x2d78, 0x2c58, 0x7d7e,
  0x7e21, 0x7e22, 0x2d79, 0x7b24, 0x7e30, 0x2c5c, 0x7e31, 0x2c5d,
  0x7e32, 0x7e33, 0x7e34, 0x2c5e, 0x7e35, 0x2f7a, 0x2e4f, 0x2e50,
  0x2c7c, 0x7e71, 0x7b2e, 0x7d49, 0x2c77, 0x7e72, 0x7e73, 0x7e74,
  0x7e75, 0x7e76, 0x7e77, 0x7e5c, 0x7e5e, 0x7e5d, 0x7e70, 0x2e2c,
  0x7c42, 0x2e4e, 0x7c43, 0x2c55, 0x7e5a, 0x7e5f, 0x2e78, 0x7e60,
  0x7e61, 0x2c6e, 0x2c6d, 0x7e62, 0x2e79, 0x2c70, 0x2c6f, 0x2d7c,
  0x2c53, 0x2c54, 0x7e6e, 0x2d5c, 0x7e63, 0x7e64, 0x2f66, 0x7e65,
  0x7e67, 0x7e66, 0x7e68, 0x7e6f, 0x7e69, 0x7e6b, 0x2c26, 0x7e6a,
  0x2d50, 0x7e6c, 0x7e6d, 0x2f7e, 0x7e7d, 0x7e7e, 0x2d21, 0x2d22,
  0x2c7e, 0x2d23, 0x2d24, 0x2d25, 0x2c60, 0x2d26, 0x2e7c, 0x2d28,
  0x2d29, 0x2d2a, 0x2d27, 0x2d2b, 0x2d2c, 0x2c61, 0x2c62, 0x2d2d,
  0x2d2e, 0x2d7d, 0x7b2c, 0x7b2d, 0x7b2f, 0x7b30, 0x2c30, 0x7b31,
  0x2f67, 0x7e50, 0x7e78, 0x2e7a, 0x2d3e, 0x2c71, 0x2d3f, 0x2d40,
  0x2d42, 0x2d41, 0x7b62, 0x7d77, 0x7d78, 0x2e6d, 0x2d77, 0x7d79,
  0x7d7a, 0x7c57, 0x2c5b, 0x7c58, 0x2e56, 0x7c59, 0x2d43, 0x2d44,
  0x2d45, 0x2d46, 0x2d47, 0x2d48, 0x2d49, 0x2d4a, 0x2c3e, 0x2d4b,
  0x7b40, 0x7b41, 0x7b42, 0x7b43, 0x7b44, 0x2d68, 0x7b45, 0x2c45,
  0x7b46, 0x2d4c, 0x2d4d, 0x2f79, 0x2c56, 0x2c72, 0x2d2f, 0x2d30,
  0x2d31, 0x2d32, 0x2d33, 0x2d34, 0x2d35, 0x2d36, 0x2e71, 0x2d37,
  0x2d38, 0x2d39, 0x2d3a, 0x2d3b, 0x2d3c, 0x2d3d, 0x2c63, 0x7d5c,
  0x7d5d, 0x7d5e, 0x7d5f, 0x2c64, 0x7d60, 0x2c65, 0x7d61, 0x7d62,
  0x7d63, 0x7d64, 0x7d65, 0x2c66, 0x2c67, 0x7d66, 0x7d67, 0x7d68,
  0x7d69, 0x7d6a, 0x2d4f, 0x2d51, 0x7a26, 0x2e7b, 0x2d52, 0x2c73,
  0x2c74, 0x7e7b, 0x7e7c, 0x2b40,
};

static const Summary16 isoir165ext_uni2indx_page00[32] = {
  /* 0x0000 */
  {    0, 0x0000 }, {    0, 0x0000 }, {    0, 0x0000 }, {    0, 0x0000 },
  {    0, 0x0000 }, {    0, 0x0000 }, {    0, 0x0000 }, {    0, 0x0000 },
  {    0, 0x0000 }, {    0, 0x0000 }, {    0, 0x0000 }, {    0, 0x0000 },
  {    0, 0x0000 }, {    0, 0x0000 }, {    0, 0x3700 }, {    5, 0x160c },
  /* 0x0100 */
  {   10, 0x0000 }, {   10, 0x0808 }, {   12, 0x0800 }, {   13, 0x0000 },
  {   13, 0x2110 }, {   16, 0x0000 }, {   16, 0x0800 }, {   17, 0x0000 },
  {   17, 0x0000 }, {   17, 0x0000 }, {   17, 0x0000 }, {   17, 0x0000 },
  {   17, 0x0000 }, {   17, 0x1555 }, {   24, 0x0000 }, {   24, 0x0200 },
};
static const Summary16 isoir165ext_uni2indx_page03[12] = {
  /* 0x0300 */
  {   25, 0x0000 }, {   25, 0x0000 }, {   25, 0x0000 }, {   25, 0x0000 },
  {   25, 0x0000 }, {   25, 0x0000 }, {   25, 0x0000 }, {   25, 0x0000 },
  {   25, 0x0000 }, {   25, 0x0000 }, {   25, 0x1000 }, {   26, 0x0002 },
};
static const Summary16 isoir165ext_uni2indx_page1e[28] = {
  /* 0x1e00 */
  {   27, 0x0000 }, {   27, 0x0000 }, {   27, 0x0000 }, {   27, 0x8000 },
  {   28, 0x0000 }, {   28, 0x0000 }, {   28, 0x0000 }, {   28, 0x0000 },
  {   28, 0x0000 }, {   28, 0x0000 }, {   28, 0x0000 }, {   28, 0x0000 },
  {   28, 0x0000 }, {   28, 0x0000 }, {   28, 0x0000 }, {   28, 0x0000 },
  /* 0x1f00 */
  {   28, 0x0000 }, {   28, 0x0000 }, {   28, 0x0000 }, {   28, 0x0000 },
  {   28, 0x0000 }, {   28, 0x0000 }, {   28, 0x0000 }, {   28, 0x0001 },
  {   29, 0x0000 }, {   29, 0x0000 }, {   29, 0x0000 }, {   29, 0x0003 },
};
static const Summary16 isoir165ext_uni2indx_page30[4] = {
  /* 0x3000 */
  {   31, 0x0000 }, {   31, 0x0000 }, {   31, 0x0000 }, {   31, 0x0080 },
};
static const Summary16 isoir165ext_uni2indx_page32[32] = {
  /* 0x3200 */
  {   32, 0x0000 }, {   32, 0x0000 }, {   32, 0x0000 }, {   32, 0x0000 },
  {   32, 0x0000 }, {   32, 0x0000 }, {   32, 0x0000 }, {   32, 0x0000 },
  {   32, 0x0000 }, {   32, 0x0000 }, {   32, 0x0000 }, {   32, 0x0000 },
  {   32, 0x0fff }, {   44, 0x0000 }, {   44, 0x0000 }, {   44, 0x0000 },
  /* 0x3300 */
  {   44, 0x0000 }, {   44, 0x0000 }, {   44, 0x0000 }, {   44, 0x0000 },
  {   44, 0x0000 }, {   44, 0xff00 }, {   52, 0xffff }, {   68, 0x0001 },
  {   69, 0x0000 }, {   69, 0x0000 }, {   69, 0x0000 }, {   69, 0x0000 },
  {   69, 0x0000 }, {   69, 0x0000 }, {   69, 0xffff }, {   85, 0x7fff },
};
static const Summary16 isoir165ext_uni2indx_page4e[752] = {
  /* 0x4e00 */
  {  100, 0x8000 }, {  101, 0x0000 }, {  101, 0x0040 }, {  102, 0x0000 },
  {  102, 0x0004 }, {  103, 0x0000 }, {  103, 0x0000 }, {  103, 0x0000 },
  {  103, 0x0020 }, {  104, 0x0000 }, {  104, 0x0000 }, {  104, 0x0140 },
  {  106, 0x0000 }, {  106, 0x0000 }, {  106, 0x0000 }, {  106, 0x0000 },
  /* 0x4f00 */
  {  106, 0x0808 }, {  108, 0x0020 }, {  109, 0x000a }, {  111, 0x4800 },
  {  113, 0x0000 }, {  113, 0x0000 }, {  113, 0x0802 }, {  115, 0x0400 },
  {  116, 0x0000 }, {  116, 0x0000 }, {  116, 0x0004 }, {  117, 0x0010 },
  {  118, 0x0000 }, {  118, 0x0000 }, {  118, 0x0800 }, {  119, 0x0060 },
  /* 0x5000 */
  {  121, 0x0000 }, {  121, 0x4000 }, {  122, 0x0004 }, {  123, 0x0010 },
  {  124, 0x0000 }, {  124, 0x0000 }, {  124, 0x0000 }, {  124, 0x0004 },
  {  125, 0x0000 }, {  125, 0x0006 }, {  127, 0x0010 }, {  128, 0x0000 },
  {  128, 0x2080 }, {  130, 0x0000 }, {  130, 0x0000 }, {  130, 0x0001 },
  /* 0x5100 */
  {  131, 0x0000 }, {  131, 0x0000 }, {  131, 0x0000 }, {  131, 0x0000 },
  {  131, 0x0000 }, {  131, 0x0000 }, {  131, 0x0000 }, {  131, 0x0000 },
  {  131, 0x8080 }, {  133, 0x0000 }, {  133, 0x4100 }, {  135, 0x0000 },
  {  135, 0x0008 }, {  136, 0x0000 }, {  136, 0x0000 }, {  136, 0x0000 },
  /* 0x5200 */
  {  136, 0x0000 }, {  136, 0x0000 }, {  136, 0x1000 }, {  137, 0x0004 },
  {  138, 0x0020 }, {  139, 0x0000 }, {  139, 0x0000 }, {  139, 0x0000 },
  {  139, 0x0000 }, {  139, 0x0100 }, {  140, 0x0000 }, {  140, 0x1a00 },
  {  143, 0x2000 }, {  144, 0x0400 }, {  145, 0x0000 }, {  145, 0x0088 },
  /* 0x5300 */
  {  147, 0x0400 }, {  148, 0x1000 }, {  149, 0x0000 }, {  149, 0x0000 },
  {  149, 0x0000 }, {  149, 0x0000 }, {  149, 0x0000 }, {  149, 0x0000 },
  {  149, 0x0000 }, {  149, 0x0001 }, {  150, 0x0000 }, {  150, 0x4000 },
  {  151, 0x00c0 }, {  153, 0x0000 }, {  153, 0x0000 }, {  153, 0x0000 },
  /* 0x5400 */
  {  153, 0x0000 }, {  153, 0x0000 }, {  153, 0x0000 }, {  153, 0x0000 },
  {  153, 0x0000 }, {  153, 0x0000 }, {  153, 0x0000 }, {  153, 0x0000 },
  {  153, 0x0000 }, {  153, 0x0000 }, {  153, 0x0020 }, {  154, 0x0000 },
  {  154, 0x0000 }, {  154, 0x0000 }, {  154, 0x0000 }, {  154, 0x0000 },
  /* 0x5500 */
  {  154, 0x0000 }, {  154, 0x2000 }, {  155, 0x0000 }, {  155, 0x0040 },
  {  156, 0x2000 }, {  157, 0x0080 }, {  158, 0x8000 }, {  159, 0x0011 },
  {  161, 0x0040 }, {  162, 0x0000 }, {  162, 0x0010 }, {  163, 0x0000 },
  {  163, 0x0000 }, {  163, 0x0000 }, {  163, 0x0000 }, {  163, 0x0000 },
  /* 0x5600 */
  {  163, 0x0000 }, {  163, 0x0000 }, {  163, 0x0000 }, {  163, 0x0000 },
  {  163, 0x0001 }, {  164, 0x0000 }, {  164, 0x0000 }, {  164, 0x0000 },
  {  164, 0x0010 }, {  165, 0x1400 }, {  167, 0x2000 }, {  168, 0x0000 },
  {  168, 0x0000 }, {  168, 0x0000 }, {  168, 0x0000 }, {  168, 0x0080 },
  /* 0x5700 */
  {  169, 0x0000 }, {  169, 0x0201 }, {  171, 0x0000 }, {  171, 0x0000 },
  {  171, 0x0000 }, {  171, 0x0000 }, {  171, 0x1000 }, {  172, 0x8001 },
  {  174, 0x0080 }, {  175, 0xc420 }, {  179, 0x0000 }, {  179, 0x4002 },
  {  181, 0x1000 }, {  182, 0x0000 }, {  182, 0x0800 }, {  183, 0x0020 },
  /* 0x5800 */
  {  184, 0x5208 }, {  188, 0x0000 }, {  188, 0x0000 }, {  188, 0x3000 },
  {  190, 0x016c }, {  195, 0x2408 }, {  198, 0x0000 }, {  198, 0x0002 },
  {  199, 0x0110 }, {  201, 0x0020 }, {  202, 0x0000 }, {  202, 0x0000 },
  {  202, 0x0000 }, {  202, 0x0000 }, {  202, 0x0000 }, {  202, 0x0100 },
  /* 0x5900 */
  {  203, 0x0040 }, {  204, 0x0001 }, {  205, 0x1000 }, {  206, 0x0000 },
  {  206, 0x0001 }, {  207, 0x0008 }, {  208, 0x2000 }, {  209, 0x0000 },
  {  209, 0x0000 }, {  209, 0x0000 }, {  209, 0x0000 }, {  209, 0x0008 },
  {  210, 0x0000 }, {  210, 0x4000 }, {  211, 0x4000 }, {  212, 0x2000 },
  /* 0x5a00 */
  {  213, 0x0000 }, {  213, 0x0004 }, {  214, 0x0000 }, {  214, 0x0000 },
  {  214, 0x0000 }, {  214, 0x4000 }, {  215, 0x0020 }, {  216, 0x1008 },
  {  218, 0x0010 }, {  219, 0x4040 }, {  221, 0x6000 }, {  223, 0x0000 },
  {  223, 0x0010 }, {  224, 0x0400 }, {  225, 0x0400 }, {  226, 0x0000 },
  /* 0x5b00 */
  {  226, 0x0000 }, {  226, 0x0800 }, {  227, 0x0000 }, {  227, 0x0000 },
  {  227, 0x0008 }, {  228, 0x0040 }, {  229, 0x0000 }, {  229, 0x0000 },
  {  229, 0x0000 }, {  229, 0x0000 }, {  229, 0x1080 }, {  231, 0x0000 },
  {  231, 0x0000 }, {  231, 0x0000 }, {  231, 0x0000 }, {  231, 0x0000 },
  /* 0x5c00 */
  {  231, 0x0020 }, {  232, 0x0000 }, {  232, 0x0400 }, {  233, 0x0001 },
  {  234, 0x0000 }, {  234, 0x2000 }, {  235, 0x0000 }, {  235, 0x1014 },
  {  238, 0x0000 }, {  238, 0x4000 }, {  239, 0x0100 }, {  240, 0x0000 },
  {  240, 0x0088 }, {  242, 0x0008 }, {  243, 0x0088 }, {  245, 0x8002 },
  /* 0x5d00 */
  {  247, 0x1031 }, {  251, 0x8602 }, {  255, 0x0000 }, {  255, 0x0000 },
  {  255, 0x4000 }, {  256, 0x0400 }, {  257, 0x0000 }, {  257, 0x0000 },
  {  257, 0x0020 }, {  258, 0x000c }, {  260, 0x0000 }, {  260, 0x0000 },
  {  260, 0x0204 }, {  262, 0x0040 }, {  263, 0x0000 }, {  263, 0x0000 },
  /* 0x5e00 */
  {  263, 0x0800 }, {  264, 0x0080 }, {  265, 0x0102 }, {  267, 0x0000 },
  {  267, 0x0000 }, {  267, 0x0000 }, {  267, 0x0400 }, {  268, 0x0000 },
  {  268, 0x0000 }, {  268, 0x0000 }, {  268, 0x0010 }, {  269, 0x1000 },
  {  270, 0x0900 }, {  272, 0x0000 }, {  272, 0x0000 }, {  272, 0x0000 },
  /* 0x5f00 */
  {  272, 0x7080 }, {  276, 0x0000 }, {  276, 0x0004 }, {  277, 0x0000 },
  {  277, 0x0000 }, {  277, 0x8000 }, {  278, 0x0180 }, {  280, 0x0000 },
  {  280, 0x0000 }, {  280, 0x0000 }, {  280, 0x9000 }, {  282, 0x0000 },
  {  282, 0x0000 }, {  282, 0x0000 }, {  282, 0x0000 }, {  282, 0x0000 },
  /* 0x6000 */
  {  282, 0x0000 }, {  282, 0x0080 }, {  283, 0x0000 }, {  283, 0x0000 },
  {  283, 0x0000 }, {  283, 0x0000 }, {  283, 0x0000 }, {  283, 0x0000 },
  {  283, 0x0000 }, {  283, 0x0000 }, {  283, 0x4000 }, {  284, 0x0001 },
  {  285, 0x0080 }, {  286, 0x0208 }, {  288, 0x0000 }, {  288, 0x0000 },
  /* 0x6100 */
  {  288, 0x0000 }, {  288, 0x0010 }, {  289, 0x0000 }, {  289, 0x0000 },
  {  289, 0x0000 }, {  289, 0x0008 }, {  290, 0x0020 }, {  291, 0x0000 },
  {  291, 0x0000 }, {  291, 0x0000 }, {  291, 0x0000 }, {  291, 0x0000 },
  {  291, 0x0000 }, {  291, 0x0000 }, {  291, 0x0000 }, {  291, 0x0000 },
  /* 0x6200 */
  {  291, 0x0000 }, {  291, 0x0000 }, {  291, 0x0000 }, {  291, 0x0000 },
  {  291, 0x0460 }, {  294, 0x0000 }, {  294, 0x0003 }, {  296, 0x0000 },
  {  296, 0x0008 }, {  297, 0x0010 }, {  298, 0x0000 }, {  298, 0x0000 },
  {  298, 0x0008 }, {  299, 0x0000 }, {  299, 0x0010 }, {  300, 0x0000 },
  /* 0x6300 */
  {  300, 0x0000 }, {  300, 0x1000 }, {  301, 0x0040 }, {  302, 0x1000 },
  {  303, 0x0000 }, {  303, 0x0000 }, {  303, 0x0000 }, {  303, 0x0000 },
  {  303, 0x0000 }, {  303, 0x0000 }, {  303, 0x8000 }, {  304, 0x0002 },
  {  305, 0x0000 }, {  305, 0x0000 }, {  305, 0x0000 }, {  305, 0x4000 },
  /* 0x6400 */
  {  306, 0x0000 }, {  306, 0x0004 }, {  307, 0x0084 }, {  309, 0x0000 },
  {  309, 0x0000 }, {  309, 0x0000 }, {  309, 0x0000 }, {  309, 0x2080 },
  {  311, 0x0000 }, {  311, 0x0000 }, {  311, 0x0000 }, {  311, 0x0000 },
  {  311, 0x0000 }, {  311, 0x0000 }, {  311, 0x2000 }, {  312, 0x0000 },
  /* 0x6500 */
  {  312, 0x0000 }, {  312, 0x0000 }, {  312, 0x0000 }, {  312, 0x0000 },
  {  312, 0x0000 }, {  312, 0x1000 }, {  313, 0x0200 }, {  314, 0x0000 },
  {  314, 0x8000 }, {  315, 0x2000 }, {  316, 0x0104 }, {  318, 0x0000 },
  {  318, 0x0004 }, {  319, 0x0000 }, {  319, 0x0000 }, {  319, 0x0900 },
  /* 0x6600 */
  {  321, 0x0280 }, {  323, 0x0000 }, {  323, 0x0000 }, {  323, 0x2000 },
  {  324, 0x0000 }, {  324, 0x4000 }, {  325, 0x1084 }, {  328, 0x0008 },
  {  329, 0x6800 }, {  332, 0x0000 }, {  332, 0x0000 }, {  332, 0x0000 },
  {  332, 0x0000 }, {  332, 0x0000 }, {  332, 0x0000 }, {  332, 0x0000 },
  /* 0x6700 */
  {  332, 0x0000 }, {  332, 0x0108 }, {  334, 0x0000 }, {  334, 0x8000 },
  {  335, 0x0020 }, {  336, 0x0000 }, {  336, 0x0080 }, {  337, 0x0800 },
  {  338, 0x0000 }, {  338, 0x8200 }, {  340, 0x0000 }, {  340, 0x0000 },
  {  340, 0x0100 }, {  341, 0x0000 }, {  341, 0x4000 }, {  342, 0x0000 },
  /* 0x6800 */
  {  342, 0x0000 }, {  342, 0x8004 }, {  344, 0x0000 }, {  344, 0x0000 },
  {  344, 0x0800 }, {  345, 0x0000 }, {  345, 0x9400 }, {  348, 0x0000 },
  {  348, 0x0000 }, {  348, 0x0000 }, {  348, 0x0001 }, {  349, 0x9002 },
  {  352, 0x0002 }, {  353, 0x000b }, {  356, 0x1900 }, {  359, 0x0900 },
  /* 0x6900 */
  {  361, 0x0000 }, {  361, 0x200a }, {  364, 0x0800 }, {  365, 0x0020 },
  {  366, 0x0001 }, {  367, 0x8200 }, {  369, 0x8204 }, {  372, 0x0000 },
  {  372, 0x0008 }, {  373, 0x0040 }, {  374, 0x0000 }, {  374, 0x5000 },
  {  376, 0x0020 }, {  377, 0x1400 }, {  379, 0x0020 }, {  380, 0x4000 },
  /* 0x6a00 */
  {  381, 0x0800 }, {  382, 0x0002 }, {  383, 0x0000 }, {  383, 0x0000 },
  {  383, 0x0000 }, {  383, 0x0000 }, {  383, 0x0040 }, {  384, 0x0000 },
  {  384, 0x0000 }, {  384, 0x0040 }, {  385, 0x0000 }, {  385, 0x0030 },
  {  387, 0x0000 }, {  387, 0x0000 }, {  387, 0x0000 }, {  387, 0x0000 },
  /* 0x6b00 */
  {  387, 0x0000 }, {  387, 0x0000 }, {  387, 0x0000 }, {  387, 0x0100 },
  {  388, 0x0000 }, {  388, 0x0010 }, {  389, 0x0000 }, {  389, 0x0000 },
  {  389, 0x0000 }, {  389, 0x0020 }, {  390, 0x0008 }, {  391, 0x0000 },
  {  391, 0x0000 }, {  391, 0x0400 }, {  392, 0x0000 }, {  392, 0x0000 },
  /* 0x6c00 */
  {  392, 0x0000 }, {  392, 0x2000 }, {  393, 0x0020 }, {  394, 0xc000 },
  {  396, 0x0000 }, {  396, 0x0000 }, {  396, 0x2080 }, {  398, 0x0000 },
  {  398, 0x0090 }, {  400, 0x0540 }, {  403, 0x0100 }, {  404, 0x0020 },
  {  405, 0x0048 }, {  407, 0x1000 }, {  408, 0x0000 }, {  408, 0x0000 },
  /* 0x6d00 */
  {  408, 0x0000 }, {  408, 0x0002 }, {  409, 0x0300 }, {  411, 0x0510 },
  {  414, 0x0210 }, {  416, 0x0821 }, {  419, 0x0032 }, {  422, 0x0000 },
  {  422, 0x0000 }, {  422, 0x0000 }, {  422, 0x2004 }, {  424, 0x0010 },
  {  425, 0x0000 }, {  425, 0x0000 }, {  425, 0x0000 }, {  425, 0x0000 },
  /* 0x6e00 */
  {  425, 0x0000 }, {  425, 0x0800 }, {  426, 0x0000 }, {  426, 0x0000 },
  {  426, 0x0000 }, {  426, 0x3000 }, {  428, 0x0080 }, {  429, 0x0004 },
  {  430, 0x4082 }, {  433, 0x0400 }, {  434, 0x0041 }, {  436, 0x0020 },
  {  437, 0x0208 }, {  439, 0x0100 }, {  440, 0x0c80 }, {  443, 0x0000 },
  /* 0x6f00 */
  {  443, 0x0000 }, {  443, 0x0000 }, {  443, 0x0040 }, {  444, 0x0080 },
  {  445, 0x0000 }, {  445, 0x8040 }, {  447, 0x0000 }, {  447, 0x0020 },
  {  448, 0x0004 }, {  449, 0x0810 }, {  451, 0x0020 }, {  452, 0x1010 },
  {  454, 0x0000 }, {  454, 0x0000 }, {  454, 0x1000 }, {  455, 0x0000 },
  /* 0x7000 */
  {  455, 0x3000 }, {  457, 0x0010 }, {  458, 0x0000 }, {  458, 0x1000 },
  {  459, 0x0000 }, {  459, 0x0000 }, {  459, 0x0000 }, {  459, 0x0400 },
  {  460, 0x0000 }, {  460, 0x4100 }, {  462, 0x0000 }, {  462, 0x0000 },
  {  462, 0x0000 }, {  462, 0x3000 }, {  464, 0x0000 }, {  464, 0x0402 },
  /* 0x7100 */
  {  466, 0x1000 }, {  467, 0x1000 }, {  468, 0x4001 }, {  470, 0x0000 },
  {  470, 0x0000 }, {  470, 0x0000 }, {  470, 0x0000 }, {  470, 0x0000 },
  {  470, 0x0000 }, {  470, 0x0000 }, {  470, 0x0000 }, {  470, 0x0000 },
  {  470, 0x8c00 }, {  473, 0x0000 }, {  473, 0x0000 }, {  473, 0x8000 },
  /* 0x7200 */
  {  474, 0x0000 }, {  474, 0x8000 }, {  475, 0x0000 }, {  475, 0x0000 },
  {  475, 0x0006 }, {  477, 0x0400 }, {  478, 0x0008 }, {  479, 0x0080 },
  {  480, 0x0080 }, {  481, 0x0000 }, {  481, 0x0100 }, {  482, 0x0000 },
  {  482, 0x0200 }, {  483, 0x2000 }, {  484, 0x0004 }, {  485, 0x0000 },
  /* 0x7300 */
  {  485, 0x0040 }, {  486, 0x0010 }, {  487, 0x0000 }, {  487, 0x0400 },
  {  488, 0x0000 }, {  488, 0x0000 }, {  488, 0x0000 }, {  488, 0x0000 },
  {  488, 0x0000 }, {  488, 0x0630 }, {  492, 0x0061 }, {  495, 0x0002 },
  {  496, 0x0000 }, {  496, 0x0040 }, {  497, 0x4408 }, {  500, 0x2441 },
  /* 0x7400 */
  {  504, 0x4080 }, {  506, 0x0020 }, {  507, 0x8810 }, {  510, 0x0084 },
  {  512, 0x0014 }, {  514, 0x0010 }, {  515, 0x0004 }, {  516, 0x0102 },
  {  518, 0x0140 }, {  520, 0x8100 }, {  522, 0x0401 }, {  524, 0x0004 },
  {  525, 0x0000 }, {  525, 0x0100 }, {  526, 0x0000 }, {  526, 0x0800 },
  /* 0x7500 */
  {  527, 0x0008 }, {  528, 0x0000 }, {  528, 0x0442 }, {  531, 0x0000 },
  {  531, 0x0000 }, {  531, 0x0002 }, {  532, 0x9010 }, {  535, 0x0000 },
  {  535, 0x0000 }, {  535, 0x0000 }, {  535, 0x0004 }, {  536, 0x0000 },
  {  536, 0x0000 }, {  536, 0x0000 }, {  536, 0x0001 }, {  537, 0x0008 },
  /* 0x7600 */
  {  538, 0x0110 }, {  540, 0x2000 }, {  541, 0x0000 }, {  541, 0x0000 },
  {  541, 0x0000 }, {  541, 0x0004 }, {  542, 0x0000 }, {  542, 0x8040 },
  {  544, 0x0000 }, {  544, 0x4000 }, {  545, 0x0000 }, {  545, 0x0000 },
  {  545, 0x0000 }, {  545, 0x2000 }, {  546, 0x1440 }, {  549, 0x0800 },
  /* 0x7700 */
  {  550, 0x0400 }, {  551, 0x0800 }, {  552, 0x1000 }, {  553, 0x0000 },
  {  553, 0x0000 }, {  553, 0x0000 }, {  553, 0x0000 }, {  553, 0x0000 },
  {  553, 0x0840 }, {  555, 0x0000 }, {  555, 0x2800 }, {  557, 0x0000 },
  {  557, 0x0000 }, {  557, 0x4000 }, {  558, 0x0000 }, {  558, 0x1800 },
  /* 0x7800 */
  {  560, 0x0040 }, {  561, 0x0000 }, {  561, 0x0005 }, {  563, 0x0000 },
  {  563, 0x0002 }, {  564, 0x0600 }, {  566, 0x0000 }, {  566, 0x0020 },
  {  567, 0x0000 }, {  567, 0x0010 }, {  568, 0x0000 }, {  568, 0x0040 },
  {  569, 0x0000 }, {  569, 0x1000 }, {  570, 0x0002 }, {  571, 0x0000 },
  /* 0x7900 */
  {  571, 0x0008 }, {  572, 0x0000 }, {  572, 0x0000 }, {  572, 0x2008 },
  {  574, 0x4488 }, {  578, 0x0001 }, {  579, 0x0000 }, {  579, 0x1004 },
  {  581, 0x0000 }, {  581, 0x0100 }, {  582, 0x0011 }, {  584, 0x0100 },
  {  585, 0x0000 }, {  585, 0x0000 }, {  585, 0x0000 }, {  585, 0x4000 },
  /* 0x7a00 */
  {  586, 0x0000 }, {  586, 0x0040 }, {  587, 0x0000 }, {  587, 0x0000 },
  {  587, 0x0010 }, {  588, 0x4000 }, {  589, 0x0000 }, {  589, 0x0000 },
  {  589, 0x4020 }, {  591, 0x0000 }, {  591, 0x0008 }, {  592, 0x4100 },
  {  594, 0x0000 }, {  594, 0x1002 }, {  596, 0x0000 }, {  596, 0x0000 },
  /* 0x7b00 */
  {  596, 0x0000 }, {  596, 0x0000 }, {  596, 0x0000 }, {  596, 0x0000 },
  {  596, 0x4400 }, {  598, 0x1000 }, {  599, 0x0000 }, {  599, 0x8000 },
  {  600, 0x0080 }, {  601, 0x0008 }, {  602, 0x0000 }, {  602, 0x0000 },
  {  602, 0x0000 }, {  602, 0x0000 }, {  602, 0x8000 }, {  603, 0x0005 },
  /* 0x7c00 */
  {  605, 0x0208 }, {  607, 0x0020 }, {  608, 0x0001 }, {  609, 0x0001 },
  {  610, 0x0000 }, {  610, 0x2001 }, {  612, 0x0000 }, {  612, 0x0000 },
  {  612, 0x0000 }, {  612, 0x0000 }, {  612, 0x1040 }, {  614, 0x0000 },
  {  614, 0x0000 }, {  614, 0x0000 }, {  614, 0x0000 }, {  614, 0x0020 },
};
static const Summary16 isoir165ext_uni2indx_page7e[333] = {
  /* 0x7e00 */
  {  615, 0x0000 }, {  615, 0x0000 }, {  615, 0x0000 }, {  615, 0x0000 },
  {  615, 0x0010 }, {  616, 0x0000 }, {  616, 0x0000 }, {  616, 0x0000 },
  {  616, 0x0000 }, {  616, 0x0000 }, {  616, 0x4000 }, {  617, 0x1810 },
  {  620, 0x0000 }, {  620, 0x0040 }, {  621, 0x1010 }, {  623, 0x0200 },
  /* 0x7f00 */
  {  624, 0x0400 }, {  625, 0x4001 }, {  627, 0x0000 }, {  627, 0x0000 },
  {  627, 0x2000 }, {  628, 0x0000 }, {  628, 0x0000 }, {  628, 0x2000 },
  {  629, 0x0000 }, {  629, 0x0002 }, {  630, 0x0000 }, {  630, 0x0000 },
  {  630, 0x0001 }, {  631, 0x0600 }, {  633, 0x8000 }, {  634, 0x0000 },
  /* 0x8000 */
  {  634, 0x0000 }, {  634, 0x0000 }, {  634, 0x0000 }, {  634, 0x0001 },
  {  635, 0x0000 }, {  635, 0x0000 }, {  635, 0x0000 }, {  635, 0x0000 },
  {  635, 0x0000 }, {  635, 0x0000 }, {  635, 0x0000 }, {  635, 0x0000 },
  {  635, 0x0000 }, {  635, 0x0010 }, {  636, 0x0000 }, {  636, 0x4000 },
  /* 0x8100 */
  {  637, 0x0000 }, {  637, 0x0000 }, {  637, 0x0200 }, {  638, 0x8000 },
  {  639, 0x0000 }, {  639, 0x0104 }, {  641, 0x0000 }, {  641, 0x0000 },
  {  641, 0x0000 }, {  641, 0x0000 }, {  641, 0x0000 }, {  641, 0x0000 },
  {  641, 0x0000 }, {  641, 0x0002 }, {  642, 0x0000 }, {  642, 0x0000 },
  /* 0x8200 */
  {  642, 0x0000 }, {  642, 0x0140 }, {  644, 0x0000 }, {  644, 0x0400 },
  {  645, 0x0000 }, {  645, 0x0000 }, {  645, 0x0000 }, {  645, 0x0100 },
  {  646, 0x0008 }, {  647, 0x0000 }, {  647, 0x0080 }, {  648, 0x1000 },
  {  649, 0x0000 }, {  649, 0x0000 }, {  649, 0x0000 }, {  649, 0x4000 },
  /* 0x8300 */
  {  650, 0x0001 }, {  651, 0x2008 }, {  653, 0x0000 }, {  653, 0x0000 },
  {  653, 0x0030 }, {  655, 0x2000 }, {  656, 0x0000 }, {  656, 0x0000 },
  {  656, 0x0000 }, {  656, 0x0200 }, {  657, 0x0040 }, {  658, 0x0000 },
  {  658, 0x0000 }, {  658, 0x0002 }, {  659, 0x0000 }, {  659, 0x5000 },
  /* 0x8400 */
  {  661, 0x0000 }, {  661, 0x0400 }, {  662, 0x0200 }, {  663, 0x0200 },
  {  664, 0x0000 }, {  664, 0x0008 }, {  665, 0x0001 }, {  666, 0x0000 },
  {  666, 0x0000 }, {  666, 0xc000 }, {  668, 0x0100 }, {  669, 0x0000 },
  {  669, 0x8044 }, {  672, 0x0000 }, {  672, 0x0400 }, {  673, 0x0080 },
  /* 0x8500 */
  {  674, 0x0000 }, {  674, 0x0000 }, {  674, 0x0000 }, {  674, 0x0010 },
  {  675, 0x0000 }, {  675, 0x0000 }, {  675, 0x0000 }, {  675, 0x0001 },
  {  676, 0x0000 }, {  676, 0x0000 }, {  676, 0x0004 }, {  677, 0x0108 },
  {  679, 0x0000 }, {  679, 0x0000 }, {  679, 0x0001 }, {  680, 0x0000 },
  /* 0x8600 */
  {  680, 0x0000 }, {  680, 0x0000 }, {  680, 0x0000 }, {  680, 0x0000 },
  {  680, 0x0000 }, {  680, 0x0004 }, {  681, 0x0000 }, {  681, 0x0000 },
  {  681, 0x0008 }, {  682, 0x0000 }, {  682, 0x0000 }, {  682, 0x0000 },
  {  682, 0x0000 }, {  682, 0x0000 }, {  682, 0x0000 }, {  682, 0x0000 },
  /* 0x8700 */
  {  682, 0x4020 }, {  684, 0x0000 }, {  684, 0x0000 }, {  684, 0x0000 },
  {  684, 0x0000 }, {  684, 0x0100 }, {  685, 0x0000 }, {  685, 0x0000 },
  {  685, 0x0001 }, {  686, 0x0000 }, {  686, 0x0008 }, {  687, 0x0000 },
  {  687, 0x8000 }, {  688, 0x0000 }, {  688, 0x0004 }, {  689, 0x0000 },
  /* 0x8800 */
  {  689, 0x0800 }, {  690, 0x0000 }, {  690, 0x2000 }, {  691, 0x0000 },
  {  691, 0x0000 }, {  691, 0x0004 }, {  692, 0x0000 }, {  692, 0x0000 },
  {  692, 0x0040 }, {  693, 0x0080 }, {  694, 0x8400 }, {  696, 0x0000 },
  {  696, 0x0101 }, {  698, 0x0000 }, {  698, 0x0000 }, {  698, 0x0000 },
  /* 0x8900 */
  {  698, 0x0000 }, {  698, 0x0000 }, {  698, 0x0040 }, {  699, 0x0000 },
  {  699, 0x0000 }, {  699, 0x0020 }, {  700, 0x0000 }, {  700, 0x0040 },
  {  701, 0x0001 }, {  702, 0x0000 }, {  702, 0x0000 }, {  702, 0x0000 },
  {  702, 0x2008 }, {  704, 0x0010 }, {  705, 0x0000 }, {  705, 0x0002 },
  /* 0x8a00 */
  {  706, 0x0000 }, {  706, 0x0400 }, {  707, 0x0000 }, {  707, 0x0000 },
  {  707, 0x0000 }, {  707, 0x8000 }, {  708, 0x0000 }, {  708, 0x0000 },
  {  708, 0x0000 }, {  708, 0x0000 }, {  708, 0x0000 }, {  708, 0x0000 },
  {  708, 0x0000 }, {  708, 0x0000 }, {  708, 0x0000 }, {  708, 0x0000 },
  /* 0x8b00 */
  {  708, 0x0000 }, {  708, 0x0000 }, {  708, 0x0000 }, {  708, 0x0000 },
  {  708, 0x0000 }, {  708, 0x0000 }, {  708, 0x0000 }, {  708, 0x0000 },
  {  708, 0x0000 }, {  708, 0x0000 }, {  708, 0x1000 }, {  709, 0x0802 },
  {  711, 0x0080 }, {  712, 0x0000 }, {  712, 0x0400 }, {  713, 0x0000 },
  /* 0x8c00 */
  {  713, 0x0200 }, {  714, 0x4000 }, {  715, 0x0000 }, {  715, 0x0000 },
  {  715, 0x0000 }, {  715, 0x0000 }, {  715, 0x6100 }, {  718, 0x0000 },
  {  718, 0x0040 }, {  719, 0x0000 }, {  719, 0x0000 }, {  719, 0x0000 },
  {  719, 0x0000 }, {  719, 0x0000 }, {  719, 0x0000 }, {  719, 0x0000 },
  /* 0x8d00 */
  {  719, 0x0000 }, {  719, 0x0000 }, {  719, 0x0001 }, {  720, 0x0000 },
  {  720, 0x0000 }, {  720, 0x8086 }, {  724, 0x0400 }, {  725, 0x0000 },
  {  725, 0x0000 }, {  725, 0x0000 }, {  725, 0x0000 }, {  725, 0x0000 },
  {  725, 0x0000 }, {  725, 0x0000 }, {  725, 0x0000 }, {  725, 0x0000 },
  /* 0x8e00 */
  {  725, 0x0000 }, {  725, 0x0040 }, {  726, 0x00c2 }, {  729, 0x0040 },
  {  730, 0x0020 }, {  731, 0x1009 }, {  734, 0x0004 }, {  735, 0x0000 },
  {  735, 0x0000 }, {  735, 0x0000 }, {  735, 0x2000 }, {  736, 0x0000 },
  {  736, 0x0000 }, {  736, 0x0000 }, {  736, 0x0000 }, {  736, 0x0000 },
  /* 0x8f00 */
  {  736, 0x0000 }, {  736, 0x0000 }, {  736, 0x0000 }, {  736, 0x0000 },
  {  736, 0x0000 }, {  736, 0x0000 }, {  736, 0x0000 }, {  736, 0x0000 },
  {  736, 0x0001 }, {  737, 0x0004 }, {  738, 0x0000 }, {  738, 0x0020 },
  {  739, 0x0000 }, {  739, 0x0000 }, {  739, 0x0000 }, {  739, 0x0400 },
  /* 0x9000 */
  {  740, 0x0000 }, {  740, 0x0000 }, {  740, 0x0000 }, {  740, 0x0000 },
  {  740, 0x0000 }, {  740, 0x0000 }, {  740, 0x0000 }, {  740, 0x0000 },
  {  740, 0x0000 }, {  740, 0x0100 }, {  741, 0x0101 }, {  743, 0x2000 },
  {  744, 0x0108 }, {  746, 0x0400 }, {  747, 0x0010 }, {  748, 0x8000 },
  /* 0x9100 */
  {  749, 0x1000 }, {  750, 0x1500 }, {  753, 0x0001 }, {  754, 0x0000 },
  {  754, 0x0004 }, {  755, 0x0000 }, {  755, 0x0000 }, {  755, 0x0000 },
  {  755, 0x0000 }, {  755, 0x0000 }, {  755, 0x0000 }, {  755, 0x0000 },
  {  755, 0x0040 }, {  756, 0x0000 }, {  756, 0x0000 }, {  756, 0x0000 },
  /* 0x9200 */
  {  756, 0x0000 }, {  756, 0x0000 }, {  756, 0x0000 }, {  756, 0x0000 },
  {  756, 0x0000 }, {  756, 0x0000 }, {  756, 0x0000 }, {  756, 0x0000 },
  {  756, 0x0000 }, {  756, 0x0000 }, {  756, 0x0000 }, {  756, 0x0000 },
  {  756, 0x0040 },
};
static const Summary16 isoir165ext_uni2indx_page94[143] = {
  /* 0x9400 */
  {  757, 0x0000 }, {  757, 0x0000 }, {  757, 0x0000 }, {  757, 0x0000 },
  {  757, 0x0000 }, {  757, 0x0000 }, {  757, 0x0000 }, {  757, 0x0000 },
  {  757, 0x0000 }, {  757, 0x0142 }, {  760, 0x0000 }, {  760, 0x0000 },
  {  760, 0x8080 }, {  762, 0x0418 }, {  765, 0x0040 }, {  766, 0x0800 },
  /* 0x9500 */
  {  767, 0x0000 }, {  767, 0x1000 }, {  768, 0x0081 }, {  770, 0x2008 },
  {  772, 0x0008 }, {  773, 0x0400 }, {  774, 0x4001 }, {  776, 0x0030 },
  {  778, 0x0000 }, {  778, 0x0000 }, {  778, 0x0000 }, {  778, 0x0000 },
  {  778, 0x0000 }, {  778, 0x0000 }, {  778, 0x1000 }, {  779, 0x8000 },
  /* 0x9600 */
  {  780, 0x0080 }, {  781, 0x0908 }, {  784, 0x0000 }, {  784, 0x0000 },
  {  784, 0x0000 }, {  784, 0x4000 }, {  785, 0x0000 }, {  785, 0x0000 },
  {  785, 0x0000 }, {  785, 0x0000 }, {  785, 0x0000 }, {  785, 0x0000 },
  {  785, 0x0000 }, {  785, 0x0100 }, {  786, 0x0000 }, {  786, 0x0000 },
  /* 0x9700 */
  {  786, 0x0004 }, {  787, 0x0000 }, {  787, 0x0000 }, {  787, 0x0000 },
  {  787, 0x0000 }, {  787, 0x0010 }, {  788, 0x0000 }, {  788, 0x0501 },
  {  791, 0x0000 }, {  791, 0x0000 }, {  791, 0x4102 }, {  794, 0x0000 },
  {  794, 0x0000 }, {  794, 0x0000 }, {  794, 0x0100 }, {  795, 0x0000 },
  /* 0x9800 */
  {  795, 0x0000 }, {  795, 0x0000 }, {  795, 0x0000 }, {  795, 0x0000 },
  {  795, 0x0000 }, {  795, 0x0000 }, {  795, 0x0000 }, {  795, 0x0000 },
  {  795, 0x4800 }, {  797, 0x0224 }, {  800, 0x0008 }, {  801, 0x0000 },
  {  801, 0x8000 }, {  802, 0x00d1 }, {  806, 0x0000 }, {  806, 0x0000 },
  /* 0x9900 */
  {  806, 0x0000 }, {  806, 0x0000 }, {  806, 0x0000 }, {  806, 0x0000 },
  {  806, 0x0000 }, {  806, 0x0000 }, {  806, 0x0050 }, {  808, 0x4b00 },
  {  812, 0x500c }, {  816, 0x0000 }, {  816, 0x0000 }, {  816, 0x0000 },
  {  816, 0x0000 }, {  816, 0x0000 }, {  816, 0x0000 }, {  816, 0x0000 },
  /* 0x9a00 */
  {  816, 0x0000 }, {  816, 0x0000 }, {  816, 0x0000 }, {  816, 0x0000 },
  {  816, 0x0000 }, {  816, 0x0000 }, {  816, 0x0000 }, {  816, 0x0004 },
  {  817, 0x6208 }, {  821, 0x0230 }, {  824, 0x0040 }, {  825, 0x0000 },
  {  825, 0x0000 }, {  825, 0x0000 }, {  825, 0x0000 }, {  825, 0x0000 },
  /* 0x9b00 */
  {  825, 0x0000 }, {  825, 0x0101 }, {  827, 0x0020 }, {  828, 0x0040 },
  {  829, 0x0040 }, {  830, 0x0000 }, {  830, 0x0000 }, {  830, 0x0000 },
  {  830, 0x0000 }, {  830, 0x0000 }, {  830, 0x0000 }, {  830, 0x0000 },
  {  830, 0x0000 }, {  830, 0x0000 }, {  830, 0x0000 }, {  830, 0x0000 },
  /* 0x9c00 */
  {  830, 0x0000 }, {  830, 0x0000 }, {  830, 0x0000 }, {  830, 0x0000 },
  {  830, 0x0000 }, {  830, 0x0000 }, {  830, 0x0000 }, {  830, 0x0000 },
  {  830, 0x0411 }, {  833, 0x23c8 }, {  839, 0x0000 }, {  839, 0x8000 },
  {  840, 0x0003 }, {  842, 0x0804 }, {  844, 0x0009 },
};
static const Summary16 isoir165ext_uni2indx_page9e[25] = {
  /* 0x9e00 */
  {  846, 0x0000 }, {  846, 0x0000 }, {  846, 0x4090 }, {  849, 0x0011 },
  {  851, 0x2001 }, {  853, 0x225d }, {  860, 0x8027 }, {  865, 0x0010 },
  {  866, 0x0001 }, {  867, 0x0002 }, {  868, 0x0000 }, {  868, 0x0000 },
  {  868, 0x0000 }, {  868, 0x0000 }, {  868, 0x0002 }, {  869, 0x0000 },
  /* 0x9f00 */
  {  869, 0x1000 }, {  870, 0x0004 }, {  871, 0x0800 }, {  872, 0x0000 },
  {  872, 0x0002 }, {  873, 0x0000 }, {  873, 0x0000 }, {  873, 0x0000 },
  {  873, 0x0006 },
};
static const Summary16 isoir165ext_uni2indx_pageff[5] = {
  /* 0xff00 */
  {  875, 0x0000 }, {  875, 0x0000 }, {  875, 0x0000 }, {  875, 0x0000 },
  {  875, 0x0080 },
};

static int
isoir165ext_wctomb (conv_t conv, unsigned char *r, ucs4_t wc, int n)
{
  if (n >= 2) {
    const Summary16 *summary = NULL;
    if (wc >= 0x0000 && wc < 0x0200)
      summary = &isoir165ext_uni2indx_page00[(wc>>4)];
    else if (wc >= 0x0300 && wc < 0x03c0)
      summary = &isoir165ext_uni2indx_page03[(wc>>4)-0x030];
    else if (wc >= 0x1e00 && wc < 0x1fc0)
      summary = &isoir165ext_uni2indx_page1e[(wc>>4)-0x1e0];
    else if (wc >= 0x3000 && wc < 0x3040)
      summary = &isoir165ext_uni2indx_page30[(wc>>4)-0x300];
    else if (wc >= 0x3200 && wc < 0x3400)
      summary = &isoir165ext_uni2indx_page32[(wc>>4)-0x320];
    else if (wc >= 0x4e00 && wc < 0x7d00)
      summary = &isoir165ext_uni2indx_page4e[(wc>>4)-0x4e0];
    else if (wc >= 0x7e00 && wc < 0x92d0)
      summary = &isoir165ext_uni2indx_page7e[(wc>>4)-0x7e0];
    else if (wc >= 0x9400 && wc < 0x9cf0)
      summary = &isoir165ext_uni2indx_page94[(wc>>4)-0x940];
    else if (wc >= 0x9e00 && wc < 0x9f90)
      summary = &isoir165ext_uni2indx_page9e[(wc>>4)-0x9e0];
    else if (wc >= 0xff00 && wc < 0xff50)
      summary = &isoir165ext_uni2indx_pageff[(wc>>4)-0xff0];
    if (summary) {
      unsigned short used = summary->used;
      unsigned int i = wc & 0x0f;
      if (used & ((unsigned short) 1 << i)) {
        unsigned short c;
        /* Keep in `used' only the bits 0..i-1. */
        used &= ((unsigned short) 1 << i) - 1;
        /* Add `summary->indx' and the number of bits set in `used'. */
        used = (used & 0x5555) + ((used & 0xaaaa) >> 1);
        used = (used & 0x3333) + ((used & 0xcccc) >> 2);
        used = (used & 0x0f0f) + ((used & 0xf0f0) >> 4);
        used = (used & 0x00ff) + (used >> 8);
        c = isoir165ext_2charset[summary->indx + used];
        r[0] = (c >> 8); r[1] = (c & 0xff);
        return 2;
      }
    }
    return RET_ILUNI;
  }
  return RET_TOOSMALL;
}
