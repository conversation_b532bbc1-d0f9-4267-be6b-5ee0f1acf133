### **1、需求背景**

媒体统计管理平台对媒体进行编号统计，并且对于存在别名的媒体进行关联统计，维护媒体联系列表数据，方便后续有权限的用户对于媒体列表数据的维护

### **2、数据库表设计**

**ENTERPRISE-企业管理表**

| 英文字段名 | 中文字段名           | 字段类型            | 是否主键 | 是否可为空 | 默认值            |      |
| :--------- | :------------------- | :------------------ | :------- | :--------- | :---------------- | :--- |
| 英文字段名 | 中文字段名           | 字段类型            | 是否主键 | 是否可为空 | 默认值            |      |
| id         | 自增id，无实际意义   | bigint(20) unsigned | Y        | N          |                   |      |
| updateTime | 更新时间             | datetime            |          | N          | CURRENT_TIMESTAMP |      |
| createTime | 创建时间             | datetime            |          | N          | CURRENT_TIMESTAMP |      |
| isDelete   | 是否删除，0-非，1-是 | int(11)             |          | N          | 0                 |      |
| number     | 编号                 | varchar(100)        |          | N          |                   |      |
| mediaName  | 媒体名称             | varchar(255)        |          | N          |                   |      |
| alias      | 别名                 | varchar(255)        |          |            |                   |      |
| telephone  | 手机号               | varchar(100)        |          |            |                   |      |
| email      | 邮箱                 | varchar(100)        |          |            |                   |      |



### **3、业务实现**