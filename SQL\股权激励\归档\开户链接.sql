---- 1.根据证券代码查询归属服务经理信息：
--- 运营库

SELECT
	USERID,
	OACODE AS "工号",
	REALNAME AS "姓名"
FROM
	DCOMP.OMP_ENTER_SIGN_MANAGER
WHERE
	PEID IN (
	SELECT
		EID
	FROM
		DCOMP.OMP_ENTER_SIGN_RECORD
	WHERE
		BIZTYPE = 11
		AND SECUCODE = '603127.SH')
	AND USERID IS NOT NULL;


--2.根据服务经理工号查询channelvalue
-- 运营库
SELECT EID FROM DCOMP.OMP_ENTERPRISE_USER_QRCODE WHERE  ISDELETE='0' and QRFLAG='1' and  USEREID = '213000157759892625' and rownum=1
--注意：表内唯一键为OMP账号，非工号，如果一个工号有多个账号被维护，如何取？--和产品确认下来通过USERID(1 中返回)取

--3.根据USERID查询OMP组织架构 营业部代码、部门层级(1-总部、2-分公司、3-分支机构)
-- 开户库
SELECT B.EID,
	B.DEPARTMENTKEY,
	B.DEPARTMENTNAME,
	B.CATEGORY
FROM EMCRM.OMP_SYS_USER_DEPARTMENT A
JOIN EMCRM.OMP_SYS_DEPARTMENT B ON A.DEPARTMENTEID = B.EID
WHERE A.USEREID = '1';
--注意：B.CATEGORY =3时为营业部 ，B.DEPARTMENTKEY=6是为企业客户部。orgid=B.DEPARTMENTKEY


---crm 查询mid，取BROKERID ,员工编号STAFFNUMBER，
--- 开户库
SELECT BROKERID,STAFFNUMBER,BROKERTYPE,NAME FROM XTZX.EM_BROK_BROKERINFO a WHERE a.COLLECTIONSTATUS = '1' AND a.delflag = 0 AND STAFFNUMBER = ''


