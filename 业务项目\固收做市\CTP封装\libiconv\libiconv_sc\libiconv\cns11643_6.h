/*
 * Copyright (C) 1999-2002 Free Software Foundation, Inc.
 * This file is part of the GNU LIBICONV Library.
 *
 * The GNU LIBICONV Library is free software; you can redistribute it
 * and/or modify it under the terms of the GNU Library General Public
 * License as published by the Free Software Foundation; either version 2
 * of the License, or (at your option) any later version.
 *
 * The GNU LIBICONV Library is distributed in the hope that it will be
 * useful, but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
 * Library General Public License for more details.
 *
 * You should have received a copy of the GNU Library General Public
 * License along with the GNU LIBICONV Library; see the file COPYING.LIB.
 * If not, write to the Free Software Foundation, Inc., 51 Franklin Street,
 * Fifth Floor, Boston, MA 02110-1301, USA.
 */

/*
 * CNS 11643-1992 plane 6
 */

static const unsigned short cns11643_6_2uni_page21[6388] = {
  /* 0x21 */
  0xc902, 0x3362, 0x0005, 0x3388, 0x33d0, 0x33cf, 0x341e, 0x341f,
  0x3420, 0x3c0e, 0x3c2c, 0x3361, 0x3304, 0x3305, 0x3303, 0x0004,
  0xc901, 0x337d, 0x338d, 0x34a3, 0x34a4, 0x37db, 0x3800, 0xc91e,
  0x39a3, 0x3b32, 0x013f, 0x3dd4, 0x3dd5, 0x4e23, 0x552c, 0x557a,
  0x3803, 0x3307, 0x3308, 0x338f, 0x339c, 0x33d5, 0x3412, 0x3413,
  0x3414, 0x3421, 0x34a8, 0x34a6, 0x3776, 0x3801, 0x383d, 0xc914,
  0x383e, 0x3929, 0x3977, 0x3978, 0x39a6, 0x39a9, 0x39ac, 0x3b34,
  0x3804, 0x3c7b, 0x3d10, 0x392f, 0x3dd7, 0x3e1c, 0x3e1f, 0x3e1a,
  0xc936, 0x3e1e, 0x3e9e, 0x3e9a, 0x43a0, 0x43a1, 0x47d2, 0x47d3,
  0x4bb9, 0x4d1d, 0x4d55, 0x4eff, 0x51e7, 0x042a, 0x5211, 0x5213,
  0x5212, 0x53af, 0x53b1, 0xc990, 0x552d, 0x557b, 0x559d, 0x559a,
  0x04a7, 0x559f, 0x55a0, 0x559c, 0x56bd, 0x04fa,
  /* 0x22 */
  0x57aa, 0x626c, 0x6291, 0x6d74, 0x756a, 0xca29, 0x8605, 0x8f6b,
  0x8f6a, 0x33e0, 0x3312, 0x0000, 0x337f, 0x33a0, 0x3399, 0x3395,
  0x339e, 0x33df, 0x33de, 0x34af, 0x34ad, 0x34b0, 0x37df, 0x3843,
  0x3841, 0x3848, 0x3875, 0xc91a, 0x3932, 0x397b, 0x3979, 0x39b3,
  0x00db, 0x3ea7, 0x39b2, 0x39b0, 0x39b4, 0xc929, 0x3bcd, 0x3bce,
  0xc92b, 0x3c10, 0x3c80, 0x3cd3, 0x3d30, 0x3398, 0x3d2f, 0x3d31,
  0x3dda, 0x3dd9, 0x3e27, 0x3e28, 0x3e21, 0x3e25, 0x3ea6, 0x3ea2,
  0x3ea1, 0x3ea4, 0xc939, 0x3eaa, 0x3ea5, 0x3ea3, 0x3364, 0x443f,
  0x4444, 0x471b, 0x4753, 0x4784, 0x4786, 0x47d5, 0xc960, 0x4b3e,
  0x4b40, 0x4dca, 0x4dc9, 0x4e26, 0x4f00, 0x4f31, 0x4f3c, 0x4f30,
  0x4f33, 0x4f32, 0x4f38, 0x5234, 0x5253, 0x5254, 0x53b4, 0x53b2,
  0x552f, 0x55a5, 0x55a7, 0x55a4, 0x55a8, 0x55a9,
  /* 0x23 */
  0x55a2, 0x55a3, 0x55a6, 0x5681, 0x5680, 0x57ad, 0x5c24, 0x5c65,
  0x5c68, 0x5c69, 0x633c, 0x633b, 0x655d, 0x6a44, 0x7182, 0x74f1,
  0x756b, 0x771b, 0x78eb, 0x7bba, 0x7bd3, 0x7d13, 0x8922, 0x97f4,
  0xab0f, 0xab10, 0x3807, 0xc10b, 0xc10a, 0x3319, 0x331a, 0x3318,
  0x3366, 0x3382, 0x33a8, 0x33e5, 0x33e7, 0x33e4, 0x3415, 0x3423,
  0x3424, 0x3443, 0x34c3, 0x34bc, 0x34bb, 0x0038, 0x34b7, 0x34b9,
  0x34cf, 0xc90d, 0x377a, 0x377b, 0x37e1, 0x37e0, 0x3809, 0x384d,
  0xc916, 0x384b, 0x3879, 0x38b1, 0x3936, 0x3935, 0x3937, 0x3938,
  0x397f, 0x3980, 0x3981, 0x9653, 0x397e, 0x39cd, 0x39bf, 0x39be,
  0x39c2, 0x39b8, 0x39c1, 0x2030, 0x3c31, 0x3c2f, 0x3c6f, 0x3c82,
  0x013a, 0x3c87, 0x3c86, 0x3c8d, 0x3c94, 0x3cd7, 0x3cd4, 0x3cd8,
  0x3d16, 0x3d14, 0x3d2e, 0x3d36, 0x3d37, 0x51ee,
  /* 0x24 */
  0x3de0, 0x3de5, 0x3ddf, 0x3e2b, 0x3e29, 0x3e2d, 0x3e2f, 0x3eb7,
  0x3ec9, 0x3ec1, 0x3eca, 0x3eb2, 0x3eac, 0x3eae, 0x43b2, 0x43a8,
  0x43b0, 0x43a7, 0xc951, 0x4452, 0x444a, 0x4756, 0x4755, 0x475f,
  0x475e, 0x4788, 0xc95d, 0x47dd, 0x47e2, 0x47e1, 0x47df, 0x47e0,
  0x48af, 0x48b1, 0x48ba, 0x4b45, 0x4b43, 0x4bbd, 0x4bc8, 0xc96c,
  0x4bbe, 0x4bbf, 0xc974, 0x4d59, 0x4e29, 0x4e2c, 0x4e2f, 0x4e2d,
  0x4e2e, 0x4f04, 0x4f02, 0x4f01, 0x4f40, 0x4f4a, 0x4f3f, 0x4f4f,
  0x4f41, 0x4f4e, 0x51eb, 0x51ec, 0x33ac, 0x525b, 0x5263, 0x525a,
  0x5259, 0x53dc, 0x53db, 0x53d9, 0x53e0, 0x53dd, 0x3320, 0x55b0,
  0x55b1, 0x55b2, 0x55b6, 0x5651, 0x5652, 0x5650, 0x5684, 0x04c9,
  0x5683, 0x57c3, 0x57b8, 0x57b9, 0x57b2, 0x5b90, 0x5c26, 0x5c29,
  0x5c6e, 0x6340, 0x633e, 0x6341, 0x655e, 0x65bb,
  /* 0x25 */
  0x65bc, 0x65b9, 0xc9ef, 0x6980, 0x084f, 0x6a45, 0x6aba, 0x6abd,
  0x6d56, 0x6d55, 0x6d75, 0x6d77, 0x6d81, 0xc9fa, 0x6d83, 0x6d8b,
  0x6d84, 0x7186, 0x7183, 0x718b, 0x756d, 0x7616, 0x7728, 0x78ea,
  0x7bd4, 0x7c03, 0x7d15, 0x7f3d, 0x7f3e, 0x8416, 0xca4c, 0x8606,
  0x874d, 0x8f22, 0x8f73, 0x8f6d, 0x8f6e, 0x91b1, 0x384e, 0x932d,
  0x0fcd, 0x95f9, 0x9652, 0x9800, 0x97fc, 0x97fa, 0x1092, 0x97f7,
  0x97f8, 0xca90, 0xcac4, 0xa627, 0xab11, 0x33ae, 0xad50, 0xad4f,
  0xad4d, 0xaf71, 0xcae2, 0xaf72, 0xb518, 0x3d44, 0x3321, 0x3367,
  0x33af, 0x33b0, 0x3329, 0x3417, 0x3422, 0xc903, 0x3325, 0x3428,
  0x34ff, 0x34d9, 0x34db, 0x34de, 0x34f2, 0x34fe, 0x34f4, 0x34dd,
  0x0045, 0xc904, 0x3501, 0x34dc, 0x377d, 0x3781, 0x377e, 0x377f,
  0x37e5, 0x380f, 0x00b5, 0x380a, 0x00b6, 0x380b,
  /* 0x26 */
  0x3851, 0x3852, 0x3850, 0x38b8, 0x393d, 0x393e, 0x393c, 0x3987,
  0x39d7, 0x39db, 0x39cf, 0x39e0, 0x3b4c, 0x3b42, 0x3bd4, 0x3c11,
  0x3c14, 0x3c15, 0x3c12, 0x3c33, 0x3c35, 0x0137, 0x3c71, 0x0130,
  0x3c91, 0x3c93, 0x3c95, 0x3cdc, 0x3d42, 0x3d40, 0x83a6, 0x3de6,
  0x448f, 0x3e32, 0xc937, 0x3e31, 0x3ef5, 0x3ecd, 0x3ede, 0x3ed5,
  0x3eda, 0x3eee, 0x3eec, 0x3ecf, 0x3ece, 0xc93d, 0x3ecc, 0x3ef3,
  0x43c1, 0x43c4, 0x43c6, 0x43b4, 0x43c3, 0x43b8, 0x43bb, 0x43b7,
  0x43bc, 0x43bf, 0x43c7, 0x43be, 0x4460, 0x445d, 0x4476, 0x446c,
  0x4475, 0x445b, 0x4471, 0x4473, 0x4461, 0x445a, 0x4462, 0x4472,
  0x445f, 0x4458, 0x471e, 0x4758, 0x478b, 0x478a, 0x47f4, 0x47f0,
  0x48d2, 0x48cd, 0x48ce, 0x48bc, 0x48d0, 0x4b46, 0x4b47, 0x033e,
  0x034d, 0x0351, 0x4bce, 0x4bcf, 0x4bcc, 0x4bcb,
  /* 0x27 */
  0xc972, 0x4d60, 0x4d5f, 0x4d5d, 0x4dd4, 0x4e3a, 0x4e38, 0x4e3c,
  0x4e36, 0x4f08, 0x4f7e, 0x4f5e, 0x4f6e, 0x4f53, 0x4f70, 0x4f57,
  0x4f5d, 0x4f63, 0x4f7a, 0x4f79, 0x4f93, 0xc981, 0x521d, 0x5268,
  0x5273, 0x5274, 0x5266, 0x526f, 0x5275, 0x527a, 0x5270, 0x526d,
  0x5265, 0x538a, 0x53ed, 0x53e9, 0x53e3, 0x53ef, 0x0473, 0x53ea,
  0x5531, 0xc991, 0x5538, 0x9820, 0x5535, 0x553a, 0x5581, 0x5580,
  0x55b8, 0x55c1, 0x55ba, 0x5688, 0x56cf, 0x56ca, 0x56c7, 0x56c5,
  0x56c8, 0x56d0, 0x56c9, 0x57bb, 0x57b6, 0x57bd, 0xc99d, 0x57b4,
  0x57c1, 0x57d1, 0x57d2, 0x57cc, 0x57d4, 0x57cb, 0x57ce, 0x57d5,
  0x57d8, 0x5b95, 0x5b93, 0x5b99, 0x5b96, 0x05be, 0x5c89, 0x5c94,
  0x5c91, 0x5c8f, 0x5c84, 0x5c97, 0x5c86, 0x5c85, 0x5c8c, 0x60f2,
  0x60ed, 0x60ef, 0x62d9, 0x6346, 0x6347, 0x634b,
  /* 0x28 */
  0x6350, 0x634a, 0x634e, 0x634c, 0x6348, 0x6563, 0x6561, 0x6562,
  0x6560, 0x65c3, 0x65c5, 0x65c1, 0x65d0, 0xc9dc, 0x6982, 0x081e,
  0x6986, 0x6984, 0x6a4c, 0x6a48, 0x6ab8, 0x6abf, 0x6ac1, 0x6ac6,
  0x6ac5, 0x6ac7, 0x6ac0, 0x6ac3, 0x6bed, 0x6c03, 0x6c22, 0x6c21,
  0x6d9f, 0x6d90, 0x6d9e, 0x08c8, 0x6da0, 0x6d94, 0x6d95, 0x6da1,
  0x08c3, 0x08c7, 0x719a, 0x7190, 0x74f2, 0x74f3, 0x756e, 0x75ab,
  0x75a9, 0x761a, 0x7618, 0x761b, 0x7760, 0x774c, 0x7742, 0x7733,
  0x773f, 0x773e, 0x7738, 0x7743, 0x7746, 0x7736, 0x78ee, 0x78f5,
  0x78f4, 0x0b56, 0x7c1b, 0x0140, 0x7d2b, 0x7d29, 0x7d2a, 0x7d2e,
  0x803f, 0x80e6, 0x83a7, 0x83a8, 0x8418, 0x8608, 0x8750, 0x8753,
  0x8a56, 0x8a55, 0x8f6f, 0x8f7f, 0x91b3, 0x933a, 0x9338, 0x9337,
  0x9336, 0x3854, 0xca80, 0x95dd, 0x95fc, 0x95fa,
  /* 0x29 */
  0x9633, 0x9804, 0x9807, 0x9808, 0x9806, 0x9809, 0xca91, 0xca93,
  0x9d1e, 0xcad2, 0xad0b, 0xad43, 0xad52, 0xad58, 0xad5e, 0xaf68,
  0xaf8d, 0xaf82, 0xaf7b, 0xb358, 0x15bd, 0xc900, 0x332c, 0x332e,
  0x480b, 0x332d, 0x3369, 0x33b2, 0x33b6, 0x33a9, 0x33ed, 0x3419,
  0x344f, 0x3516, 0x3503, 0x350e, 0x3504, 0x3507, 0x3510, 0x3527,
  0x3528, 0x350d, 0x3529, 0x350f, 0x3522, 0x3511, 0x3785, 0x3784,
  0x3783, 0x378a, 0x3786, 0x3810, 0x3858, 0x385a, 0x3a0c, 0x38c4,
  0x38c5, 0x3947, 0x3949, 0x394a, 0x394b, 0x53bb, 0x398d, 0x398b,
  0x3a07, 0x3a06, 0xc920, 0x39ef, 0x39f0, 0x39ed, 0x39f9, 0x3a02,
  0x39e7, 0x39f4, 0x39f7, 0x39f1, 0x3b53, 0x3b55, 0x011a, 0x3bda,
  0x3bd9, 0x012c, 0x3c38, 0x3c99, 0x3ca2, 0x3c9c, 0x3c9a, 0x3c9b,
  0x3ce5, 0x3ce3, 0x3ce1, 0x3ce0, 0x3ce2, 0x3ce4,
  /* 0x2a */
  0x32fd, 0x3ce8, 0xc92f, 0x3d13, 0x3d56, 0x3d4b, 0x3d4a, 0x3d4d,
  0x3d4c, 0x0149, 0x3dea, 0x3de9, 0x3deb, 0x3dec, 0x3e3a, 0x3e3c,
  0x3e39, 0x3e3b, 0x3f14, 0x3f0e, 0x3f35, 0x3f0a, 0x3f3f, 0x3f38,
  0x017c, 0x3f4e, 0x3f17, 0xc93f, 0x3f1e, 0x43b6, 0x43ce, 0x43ca,
  0x43cb, 0x43cc, 0x43c9, 0x44b4, 0x44ac, 0x4488, 0x4486, 0x448c,
  0x4493, 0x448a, 0x44a4, 0x4487, 0x44a5, 0x44a6, 0x4485, 0x44a3,
  0x448e, 0x471f, 0x4763, 0x478d, 0x47fa, 0x47fb, 0x4809, 0x47fe,
  0x47ff, 0x4802, 0x4804, 0x47fd, 0x4805, 0x48f9, 0x48f7, 0x48db,
  0x48da, 0x4b51, 0x4b50, 0x4b57, 0x4be2, 0x4bd8, 0x4bda, 0x4bdc,
  0x4d20, 0x4d1f, 0x4d69, 0x4ddd, 0x4e40, 0x4e41, 0x4e43, 0x4f0d,
  0x4f0c, 0x4f87, 0x4fa9, 0x4f92, 0x4f85, 0x03bf, 0x4f8c, 0x4fa2,
  0x4f8f, 0x4f8a, 0x03ba, 0x4f8d, 0x51f1, 0x5237,
  /* 0x2b */
  0x528b, 0x5287, 0x5282, 0x538e, 0x53bc, 0x53f8, 0x53fa, 0x53eb,
  0x53f9, 0x53fc, 0x5405, 0x551b, 0x551d, 0x551e, 0xc992, 0x553c,
  0x553e, 0x5584, 0x55c8, 0x55c9, 0x55d3, 0x55c7, 0x55d2, 0x5655,
  0xc996, 0x568c, 0x568a, 0x56d5, 0x56d3, 0x04d8, 0x56d8, 0x57d0,
  0x57ee, 0x57f1, 0x57fb, 0x57d3, 0x57ec, 0x57cd, 0x5815, 0x5826,
  0x580e, 0x5827, 0x582a, 0x5800, 0x5804, 0x5828, 0x5808, 0x5814,
  0x5b9b, 0x05b2, 0x5ba1, 0x5b9d, 0x5ba0, 0x5b9e, 0x5ba2, 0x5b9c,
  0x5c32, 0x5c34, 0x5c93, 0x5c96, 0x5c90, 0x5cb4, 0x5cb5, 0xc9b6,
  0x5cb6, 0x5cc2, 0xc9b5, 0x5cba, 0x5c92, 0x60fb, 0x6105, 0x60f3,
  0x60fe, 0x60fd, 0x755d, 0x60fa, 0x6243, 0x6295, 0x6294, 0x06c4,
  0x636f, 0x6373, 0x635c, 0x635b, 0x6366, 0x6374, 0x6363, 0x6367,
  0x6360, 0x6362, 0x6371, 0x6372, 0x635e, 0x6523,
  /* 0x2c */
  0x6526, 0x6564, 0xc9d6, 0x65f2, 0x6601, 0x65dd, 0x65dc, 0x65db,
  0x65f3, 0x65fd, 0x65d8, 0x65f8, 0x65fb, 0x6983, 0x698c, 0x698e,
  0x6a49, 0x6a54, 0x6a52, 0x6a4e, 0x6a58, 0x6a51, 0x6a55, 0x6a53,
  0x6a57, 0x6a50, 0x6a4f, 0x6a4d, 0x6ad2, 0x6ad3, 0x6ac9, 0x6ad4,
  0x6bef, 0xc9f8, 0x33b1, 0x6c05, 0x6c04, 0x6c2e, 0x6c2d, 0x6d45,
  0x6d5b, 0x6d5a, 0x6d59, 0x6d9d, 0x6d8e, 0x6dbc, 0x6ddd, 0x6dba,
  0xc9fd, 0x6dd8, 0x6dcb, 0x6dd9, 0x6dda, 0x6dc4, 0x6db8, 0x6dbf,
  0x6ddb, 0x6dc1, 0xc9fc, 0x6dc5, 0xca19, 0x71b1, 0x71af, 0xca18,
  0x74f7, 0x74f6, 0x74f8, 0x7550, 0x7551, 0x7571, 0x7570, 0x75b0,
  0x75af, 0x75ae, 0x75ad, 0x7625, 0x762c, 0x7622, 0x7633, 0x7634,
  0x0a2b, 0x773a, 0x7740, 0x7768, 0x0a61, 0x7764, 0x775c, 0x7757,
  0x7753, 0x774f, 0x7751, 0x7754, 0x7766, 0x23dd,
  /* 0x2d */
  0x0ab2, 0x790d, 0x7908, 0xca2b, 0x78fa, 0x7aea, 0x7b2a, 0x7bbb,
  0x7bd5, 0x7bd7, 0x4fa4, 0x33f0, 0x33b5, 0x7c06, 0x7c28, 0x7c22,
  0x7c21, 0x5656, 0x7c23, 0x43cd, 0x7d30, 0x7d34, 0x7fc9, 0x7fca,
  0x7fc8, 0xca3d, 0x8044, 0x80f3, 0xca41, 0x80f8, 0x80fc, 0x80f6,
  0x80fb, 0x80f7, 0x8100, 0x8102, 0xca40, 0xca4d, 0x8424, 0x860d,
  0x0d29, 0x8762, 0x8930, 0x892b, 0x892a, 0x0d92, 0x892d, 0x8ada,
  0x8c28, 0x8d86, 0x8f77, 0x8f7c, 0x9050, 0x904e, 0x90f2, 0x91b2,
  0x91ce, 0x91cf, 0x91e5, 0x6607, 0x9235, 0x9231, 0x9313, 0x932b,
  0x932c, 0x9345, 0x9360, 0x9341, 0x9358, 0x9347, 0x935b, 0x9350,
  0xca82, 0x935f, 0x934a, 0x9356, 0x9343, 0x9344, 0x9351, 0x95fd,
  0x9634, 0x9635, 0x9654, 0x9655, 0x970c, 0x970b, 0x970a, 0x97f9,
  0x9835, 0x9824, 0x9813, 0x981c, 0x9869, 0x9825,
  /* 0x2e */
  0x9821, 0xca95, 0xca96, 0xca98, 0x9d20, 0x9fa9, 0x9fa8, 0x9fde,
  0xa009, 0xa00a, 0xa00b, 0xa1e3, 0xa207, 0xa2b2, 0xa2b3, 0xa35d,
  0xa858, 0xa886, 0xa887, 0xa9b9, 0xa9b8, 0xad0c, 0xad44, 0xad66,
  0xad80, 0xad75, 0xad6d, 0xad7e, 0xad67, 0xad81, 0xad77, 0xafa8,
  0xafa2, 0xafa5, 0xaf9b, 0xb357, 0xb50f, 0xb532, 0xb52c, 0xb533,
  0xb8e7, 0x336e, 0x33b7, 0x33f6, 0x33f2, 0x33f3, 0x3817, 0x3430,
  0x3454, 0x3453, 0x3552, 0x353d, 0x353c, 0x3534, 0x3533, 0x3554,
  0x352f, 0x0058, 0x3555, 0x353f, 0x3537, 0x3556, 0x3561, 0x3558,
  0xc906, 0x353b, 0x3532, 0x352e, 0x353e, 0x333b, 0x378c, 0x378d,
  0x3813, 0x3816, 0x3812, 0x385b, 0x388a, 0x38d7, 0x38ce, 0x3950,
  0x3951, 0x394f, 0x398e, 0x398f, 0x39f2, 0xc921, 0x3a28, 0x3a1a,
  0x3a25, 0x3a1d, 0x3a14, 0x3a20, 0x3a1f, 0x3a1b,
  /* 0x2f */
  0x3a17, 0x3a15, 0x3a1c, 0x3a13, 0xc925, 0x3b60, 0x011c, 0x3b66,
  0x3be3, 0x3bde, 0x3be0, 0x3be1, 0x3c1a, 0x3c1b, 0x3c18, 0x3c1c,
  0x3c19, 0x3c42, 0x3c40, 0x3c3e, 0x3c44, 0x3c74, 0x3c43, 0xc92d,
  0x3cf2, 0x3d1b, 0x3d19, 0x3d1e, 0xc930, 0x3d5e, 0x3d66, 0x3d5d,
  0x3d5a, 0x3d5f, 0x3d60, 0x3d5b, 0x3d5c, 0x3d59, 0x3df0, 0x3df1,
  0x3e43, 0x3e40, 0x3e42, 0x3e41, 0x3e3f, 0x3f57, 0x3f69, 0x3f6f,
  0x3fab, 0x3f71, 0x3f93, 0x3f56, 0x3f90, 0x3f6d, 0x3f6c, 0x3f70,
  0x3f66, 0x3f67, 0x43d7, 0x43b5, 0x43db, 0x43d8, 0x43d5, 0x43d4,
  0x44ba, 0x44b7, 0x44be, 0x44b9, 0x0237, 0x44e0, 0x44dd, 0x44de,
  0x7918, 0x44d8, 0x44bd, 0x44db, 0x471d, 0x4725, 0x4921, 0x490c,
  0x4929, 0x492d, 0x491b, 0x490e, 0x491f, 0x4904, 0x491c, 0x4905,
  0x4906, 0x4920, 0x490d, 0x492a, 0x4923, 0x4911,
  /* 0x30 */
  0x4b5c, 0x4b66, 0x4b5b, 0x4b4f, 0x4b5e, 0x4b5d, 0x4bf1, 0x4bea,
  0x4bf4, 0x4beb, 0x4bf0, 0x4bfa, 0x4bfb, 0x4d28, 0x4d2c, 0x4d6b,
  0x4d2a, 0x4d6a, 0x4d6f, 0x4ddb, 0x4de0, 0x7fd1, 0x4de3, 0x4de5,
  0x4de7, 0x4e4d, 0x4e55, 0x4e54, 0x4e53, 0x4e52, 0x4e4e, 0x4e60,
  0x53c0, 0x4f0f, 0x4f11, 0x4f13, 0x4fc6, 0x4fb3, 0x4fc7, 0x4fd2,
  0x4fb8, 0x4fac, 0x4fae, 0x4fcf, 0x4fc5, 0xc97b, 0x4fcc, 0x4fab,
  0x4fc9, 0x4fb9, 0x51fb, 0x51f8, 0x51f7, 0x51f9, 0x5220, 0x523b,
  0x5239, 0x529e, 0x529a, 0x52aa, 0x52ab, 0x52af, 0x5296, 0x52a9,
  0x52a6, 0x5291, 0x0443, 0x52ae, 0x529f, 0x52ac, 0x52a0, 0x5392,
  0x5391, 0x53bf, 0x5417, 0x540a, 0x540c, 0x554a, 0x5546, 0x5534,
  0x5545, 0x5543, 0x5544, 0xc993, 0x5587, 0x5586, 0x558a, 0x55da,
  0x55d8, 0x04b4, 0x3818, 0x3434, 0x55d6, 0x55d4,
  /* 0x31 */
  0x5654, 0x5659, 0x565a, 0x5657, 0x04e0, 0x56dd, 0x56e9, 0x56e0,
  0x5805, 0x5812, 0x5813, 0x5807, 0x5816, 0x5823, 0x5802, 0xc9a1,
  0x584a, 0x5836, 0x5840, 0x5856, 0x5843, 0xc9a0, 0x584b, 0x5846,
  0x583e, 0x5849, 0x5ba7, 0x5bb6, 0x5ba6, 0x5ba8, 0x5bac, 0x5ba9,
  0x5bab, 0x5c38, 0x5c37, 0x5c39, 0x5c41, 0x5c3e, 0x5cc0, 0x5cbb,
  0x5cbf, 0x5cbd, 0x5cfe, 0x5d1e, 0x5cee, 0x5cfc, 0xc9b7, 0x5cf9,
  0x5d06, 0x5ce4, 0x5ce9, 0x5ce5, 0x5d03, 0x5cfd, 0x5d49, 0x60be,
  0x610a, 0x8626, 0x6118, 0x610d, 0x610f, 0x610e, 0x6120, 0x6271,
  0x6299, 0x62e2, 0x62df, 0x62de, 0x6378, 0x6379, 0x06e4, 0x637c,
  0x637d, 0x6384, 0x638b, 0x638a, 0xc9d2, 0x6389, 0x652c, 0x6529,
  0x656c, 0x2104, 0x6609, 0x6608, 0x660c, 0x660d, 0x6610, 0x0826,
  0x699f, 0x6998, 0x69a2, 0x699a, 0x6ad5, 0x6ae2,
  /* 0x32 */
  0x6af0, 0x6aea, 0x6aeb, 0x6aed, 0x6ae8, 0x6ae0, 0x6b85, 0x6b86,
  0x6bf0, 0x5046, 0x6c45, 0x6c38, 0x6c3e, 0x6c42, 0x6c40, 0x6d47,
  0x6d5c, 0x6d5e, 0x6db4, 0x6dc2, 0x6e14, 0x6de5, 0x6e15, 0x6e11,
  0xc9ff, 0x6dee, 0x6de7, 0x6df5, 0x6df4, 0x6de8, 0x6e01, 0x6def,
  0x6df1, 0xca00, 0x71db, 0x71bf, 0x71da, 0x71c7, 0x71dd, 0xca1a,
  0x71eb, 0x71e1, 0x71c1, 0x71bd, 0x7507, 0x74fd, 0x7501, 0x750a,
  0x7503, 0x7572, 0x7574, 0x7575, 0x75b2, 0x75b1, 0x75b4, 0x764c,
  0x7642, 0x7640, 0x7649, 0x763c, 0x764d, 0x764a, 0x763b, 0x7761,
  0x7774, 0xca26, 0x777f, 0x777a, 0x7788, 0x777c, 0x0a6f, 0x7770,
  0x790f, 0x7928, 0x7913, 0x792a, 0x7aed, 0x7aef, 0x7b2e, 0x7bc1,
  0x7bdd, 0x3e47, 0x7c2d, 0x7c2b, 0x7c35, 0x7c2f, 0x7c31, 0x7c34,
  0xca35, 0x7c30, 0x7d3a, 0x7d39, 0x7d37, 0x7d4b,
  /* 0x33 */
  0x7d54, 0x7d4d, 0x7d51, 0x7d47, 0x7f27, 0x7f50, 0x7f4d, 0x7f4e,
  0x7f54, 0x7fd2, 0x7fce, 0x804b, 0x8049, 0x8105, 0x810f, 0x8119,
  0xca43, 0x8106, 0x810c, 0x8129, 0x8104, 0x8108, 0x8125, 0x0c1c,
  0x8103, 0x8127, 0x8110, 0x810a, 0xca42, 0x985d, 0x83aa, 0x83ab,
  0x83a9, 0x8441, 0x843a, 0x843c, 0x842b, 0x8449, 0x8615, 0x0d00,
  0x8616, 0xca53, 0x8631, 0x6d4a, 0x873c, 0x877c, 0x876d, 0x876a,
  0x8763, 0x876b, 0x877b, 0x8764, 0x877a, 0x8769, 0x876f, 0x8937,
  0x8935, 0x893c, 0x8936, 0x893d, 0x893e, 0x8ae1, 0x8ae0, 0x8c2d,
  0x8c2b, 0x8d8d, 0x8d92, 0x0eb6, 0x8d8e, 0xca6b, 0x8d91, 0x8d96,
  0x8f7b, 0x8f78, 0x8f81, 0x8f96, 0x8fa3, 0x8f95, 0x8f97, 0x9054,
  0x9052, 0x90f5, 0x9100, 0x90fb, 0x90f4, 0x90f6, 0x91e8, 0x91ea,
  0x933e, 0x933d, 0x933b, 0x9380, 0x0fe4, 0x9388,
  /* 0x34 */
  0x9381, 0x9382, 0x93ce, 0x9383, 0x9377, 0x9379, 0x9373, 0x936d,
  0x9370, 0x938d, 0x9375, 0x938c, 0x936a, 0x9391, 0x9389, 0x938e,
  0x44dc, 0x95ff, 0x9659, 0x96c7, 0x9712, 0x9714, 0x9713, 0x97cb,
  0x9842, 0x10ad, 0x983d, 0x2aff, 0x9840, 0x9844, 0x9862, 0x9843,
  0x983f, 0x9845, 0x983c, 0xca97, 0x9846, 0x9847, 0xcab3, 0x9c9e,
  0x9c9d, 0x9d2c, 0x9d29, 0x9d2f, 0x9d2e, 0x9d30, 0x9fe1, 0x9fe2,
  0xa00e, 0xa019, 0xa012, 0xa2b4, 0xa5da, 0xa726, 0xa859, 0xa85a,
  0xa888, 0xa9c0, 0xa9ba, 0xaba0, 0xcadd, 0xaba2, 0xad86, 0x2fec,
  0xad9d, 0xad88, 0xad8f, 0xad8e, 0xad9b, 0xafc1, 0xafc3, 0xafc4,
  0xaf96, 0xafc7, 0xafc6, 0xafbf, 0x14c3, 0xb20f, 0xb555, 0xb542,
  0xb546, 0xb54b, 0xb543, 0xb553, 0xb548, 0xb549, 0xb54a, 0xb54e,
  0x7bde, 0x3991, 0xbb4b, 0xbd80, 0xbd81, 0xbd83,
  /* 0x35 */
  0x358a, 0xbd82, 0x5542, 0x3c22, 0x3370, 0x3371, 0x33bc, 0x4f18,
  0x33be, 0x33ba, 0x33f8, 0x3437, 0x3435, 0x3dfc, 0x3456, 0x3459,
  0x345e, 0x356d, 0x3591, 0x3592, 0x3568, 0x3566, 0x3573, 0x0067,
  0x3596, 0x358b, 0x358c, 0x3796, 0x37ee, 0x381c, 0x381a, 0x3819,
  0x381b, 0x385d, 0x385e, 0xc918, 0x38dc, 0x38e2, 0x3952, 0x3992,
  0x3a30, 0x3a52, 0x3a42, 0x3a41, 0x3a45, 0x3a37, 0x3a40, 0x3a3f,
  0x3a3d, 0x3a38, 0x3a3a, 0x3a49, 0x3b6b, 0x3b78, 0x3b79, 0xc926,
  0x3b6c, 0x3be9, 0x3be6, 0x3be5, 0x3bea, 0x3be7, 0x3be8, 0x3c1f,
  0x3c4b, 0x3c4a, 0x3c53, 0x3c76, 0x3ca3, 0x3ca4, 0x3cf6, 0x3cf3,
  0x3cf9, 0x3cf7, 0x3cfc, 0x3d1d, 0x3d6d, 0x3d71, 0x3d6c, 0x3d6e,
  0x3d70, 0x3d6f, 0x3d67, 0x3d68, 0x3dfa, 0x3df9, 0x3e4e, 0x19de,
  0x3e4d, 0x3e4f, 0x3e4a, 0x3e4c, 0x0196, 0xc942,
  /* 0x36 */
  0x3fee, 0x3fb2, 0x3fc0, 0x3fc1, 0x3ff4, 0x3fc8, 0x3fc5, 0x3fc6,
  0x3fad, 0x43e2, 0x43ea, 0x43e3, 0x43e1, 0x44f7, 0x4501, 0x4512,
  0x44f6, 0x44f1, 0x451f, 0x44ee, 0xc952, 0x44f3, 0x4515, 0x4516,
  0x4517, 0x44f8, 0x4519, 0x44f2, 0x44f4, 0x44f5, 0x4513, 0x4506,
  0x4726, 0x4724, 0x475a, 0x60c8, 0x4797, 0x4795, 0x479a, 0x481f,
  0x3dfb, 0x4829, 0x4820, 0xc962, 0xc963, 0x494c, 0x4930, 0x4938,
  0x493d, 0x4951, 0x494f, 0x494a, 0x4934, 0x4936, 0x1b30, 0x4b6a,
  0x4b68, 0x4c1c, 0x4c0e, 0x4c1e, 0x0359, 0x4c09, 0x4c08, 0x4c13,
  0x4c01, 0x4c0f, 0x4c14, 0x4c06, 0x4c07, 0x1cb2, 0xc973, 0x0376,
  0x4d79, 0x4dea, 0x4ded, 0x4de9, 0x4dee, 0x4e68, 0x4e64, 0x4e67,
  0x4e72, 0x4e62, 0x4e74, 0x4e79, 0x4f19, 0x4f17, 0x4f15, 0x4f16,
  0x4fe6, 0x8fa4, 0x4fee, 0x03d2, 0x4fdf, 0x4fe4,
  /* 0x37 */
  0x4fda, 0x4fea, 0x4fed, 0x4fe3, 0x4fe9, 0x51fd, 0x3957, 0x5221,
  0x52c6, 0x52b8, 0x52cb, 0xc985, 0x52bd, 0x52b5, 0x52bb, 0x52bf,
  0x52be, 0x52b2, 0x52c1, 0x52c2, 0x5399, 0x53c6, 0x542c, 0x542d,
  0x5425, 0x541e, 0x541f, 0x5423, 0x5550, 0x554e, 0x554d, 0x5552,
  0x55e9, 0x55ec, 0x55e8, 0x5658, 0x565c, 0x565b, 0x568f, 0x6a72,
  0x56f6, 0x5700, 0x56fc, 0x56f8, 0x56ea, 0x56fe, 0x56f7, 0x56fd,
  0x5870, 0x5862, 0x5844, 0x0520, 0x584d, 0x584c, 0x583f, 0x5866,
  0x5835, 0x0529, 0x5834, 0x588d, 0x5884, 0x0538, 0x5886, 0x5889,
  0x5887, 0x5883, 0x5875, 0x5879, 0x58af, 0x58b0, 0x5bb7, 0x5bbb,
  0x5bb9, 0x5c46, 0x5c47, 0x5c45, 0x5cea, 0x5cf6, 0x5d68, 0x5d39,
  0xc9b9, 0x5d3d, 0x5d3b, 0x5d4d, 0x5d30, 0x5d4a, 0x5d3e, 0x5d40,
  0x5d4c, 0x5d47, 0x5d38, 0x5d52, 0x5d3a, 0x5d53,
  /* 0x38 */
  0x60c4, 0x60c1, 0x611c, 0x611d, 0x612a, 0x611e, 0x612f, 0x6122,
  0x612e, 0x6125, 0x0689, 0x06b0, 0x624a, 0x624b, 0x6276, 0x06bf,
  0x62e8, 0x62ef, 0x62e9, 0x06c5, 0x62ea, 0xc9cc, 0x06ea, 0x639b,
  0x639e, 0x6393, 0x63a7, 0x639c, 0x63a0, 0x639a, 0x63ab, 0x63be,
  0x63a9, 0x652d, 0x656e, 0x6644, 0x663d, 0x663a, 0x6668, 0x663c,
  0x666a, 0xc9e0, 0x6638, 0x6665, 0x6639, 0x666d, 0x6636, 0xc9e3,
  0x663e, 0x667e, 0x6637, 0x6999, 0x69a9, 0x69ad, 0x69a7, 0x69a8,
  0x6a66, 0x6a69, 0x6a6d, 0x6a67, 0x6a6b, 0x6a6a, 0x6aee, 0x6b01,
  0x6b03, 0x6af4, 0x6afb, 0x0837, 0x6af6, 0x6afc, 0x6bf4, 0x6c08,
  0x6c0a, 0x6c09, 0x6c6d, 0x6c62, 0x6c41, 0x6c5e, 0x6c5c, 0x6df3,
  0x6e26, 0x08e4, 0x6e39, 0xca04, 0x6e6c, 0x6e2b, 0x6e2e, 0x6e3b,
  0x6e5e, 0x6efb, 0x6e27, 0x6e24, 0x6e69, 0x6e30,
  /* 0x39 */
  0xca05, 0x6e62, 0x6e38, 0x6e35, 0x6e2a, 0x6e2c, 0x6e68, 0x6e31,
  0x6e2f, 0x6e2d, 0x6e3a, 0x6e36, 0xca03, 0x6e21, 0x6e3c, 0x6e20,
  0x6e64, 0x6e3e, 0x08e8, 0x71f7, 0x7212, 0x71f1, 0x71f5, 0x7222,
  0x71f2, 0x71df, 0x7215, 0x7216, 0x757a, 0x7576, 0x75be, 0x0a20,
  0x75bd, 0x7609, 0x7608, 0x7657, 0x77a3, 0x77bf, 0x77b8, 0x77af,
  0x779c, 0x77a5, 0x7772, 0x7775, 0x779d, 0x7799, 0x77b9, 0x794e,
  0x7939, 0x793b, 0x7935, 0x793c, 0x7955, 0x7af0, 0x7af3, 0x7af4,
  0x7b3b, 0x7b3c, 0x7b3a, 0x7b36, 0x7c07, 0x3feb, 0x7c55, 0x7c50,
  0x7c4f, 0x7c52, 0x7c56, 0x33bd, 0x7c32, 0x7d63, 0x7d6b, 0x7d66,
  0x7d57, 0x7d5d, 0x0b86, 0x7d6d, 0x7d61, 0x7d69, 0x7d5a, 0x7d5c,
  0x7d62, 0x7f2a, 0x7f29, 0x7f58, 0x7f5a, 0x7fd7, 0x7fdb, 0x7fdc,
  0x7fdd, 0x7fd8, 0x8054, 0x805b, 0x805c, 0x8053,
  /* 0x3a */
  0x804f, 0x8056, 0x8050, 0x805a, 0x806b, 0x8136, 0x8153, 0x813a,
  0x813c, 0x813e, 0x8149, 0x8140, 0xca46, 0xca47, 0x8364, 0x8365,
  0x83b5, 0x83b6, 0x83b2, 0x8448, 0x844a, 0x8472, 0x8469, 0x845a,
  0x844c, 0x862c, 0x8630, 0x864b, 0x8649, 0x8642, 0x8644, 0x864a,
  0x864f, 0x8792, 0xca57, 0x8797, 0x8780, 0x8782, 0x8786, 0x8953,
  0x895e, 0x8952, 0x895b, 0x894e, 0x8a6d, 0x8a6e, 0x8afa, 0x8af6,
  0x8afb, 0x8c33, 0x8c3d, 0x8c37, 0x8c3e, 0x8c35, 0x8d9a, 0x8dab,
  0x8da6, 0x8db0, 0x8d99, 0x8da0, 0x8d9e, 0x8da8, 0x8da1, 0x8daa,
  0x8dad, 0x8dbb, 0x8d9c, 0x8da5, 0x33b3, 0x8f27, 0x8f8d, 0x8f8e,
  0x8f8f, 0x8f92, 0x0f56, 0x8f91, 0x8fad, 0x9057, 0x9058, 0x905e,
  0x905d, 0x905c, 0x905b, 0x0f67, 0x910a, 0x9103, 0x910e, 0x91b8,
  0x924d, 0x923f, 0x9247, 0x924b, 0x924a, 0x923d,
  /* 0x3b */
  0x2838, 0x9241, 0x924c, 0x2881, 0x9362, 0x9369, 0x9361, 0x0fd1,
  0x93aa, 0x93a6, 0x93ac, 0x93bd, 0x93bb, 0x93a4, 0x93ba, 0x939a,
  0x0feb, 0x93a1, 0x93c1, 0x95e0, 0x960a, 0x9603, 0x9606, 0x9639,
  0x963a, 0x9636, 0x965b, 0x965f, 0x965e, 0x9667, 0x9661, 0x9662,
  0x965d, 0x96ca, 0x96cc, 0x96ce, 0x9718, 0x971d, 0x971f, 0x9720,
  0x9717, 0x9715, 0x981f, 0x9827, 0x9826, 0x5010, 0x988b, 0x98ae,
  0x988a, 0xca99, 0x9892, 0x9889, 0x9887, 0x10b6, 0x988f, 0x9884,
  0x9883, 0x988c, 0x9893, 0x988d, 0x9898, 0x987d, 0x987e, 0x98d2,
  0x9880, 0x9899, 0x9cac, 0x9d50, 0x9d55, 0x9d42, 0x9d3f, 0x9d3c,
  0x11a3, 0x9d4c, 0x9d49, 0x9d57, 0x9d58, 0x9d4f, 0x9d5c, 0x9d47,
  0xcab7, 0x9fab, 0x1210, 0x9faf, 0x9fad, 0x9fe8, 0x9fe7, 0xa030,
  0xa026, 0xa02f, 0xa028, 0xa02b, 0xa01d, 0xa02d,
  /* 0x3c */
  0xa020, 0xa02a, 0xa02c, 0xa035, 0xa021, 0xa023, 0xa024, 0xa036,
  0xa037, 0xa1e9, 0xa2ba, 0xa2b8, 0xcacc, 0xa36d, 0xa36a, 0xa368,
  0xa369, 0xa36b, 0xa361, 0xa5dc, 0xa5db, 0xa62d, 0xa62c, 0xa6a2,
  0xa72b, 0xa732, 0xcad7, 0xa894, 0xa892, 0xa890, 0xa9c9, 0xa9c4,
  0xa9c1, 0xa9c3, 0xa9cd, 0xab14, 0xaba7, 0xabaf, 0xabaa, 0xad0d,
  0xad54, 0xad5b, 0xad61, 0xadae, 0xadb3, 0xadc0, 0xadc4, 0xadbf,
  0xadcb, 0xadad, 0xada7, 0xada4, 0xadbd, 0xadaf, 0xadb2, 0xada5,
  0xafe7, 0xafe0, 0xafce, 0xafde, 0xafd5, 0xafdf, 0xafd9, 0xb0f2,
  0xb223, 0xb240, 0x151b, 0xb23e, 0x1587, 0xb3cf, 0x3e54, 0xb55b,
  0xb558, 0xb562, 0xb55f, 0xb567, 0xb563, 0xb55e, 0xb560, 0xb685,
  0xb686, 0xb687, 0xb8e8, 0xb8e6, 0xbd71, 0xbd85, 0xcb02, 0xbd86,
  0xbe10, 0x3cf8, 0x33bf, 0x3e61, 0x33fe, 0x33fc,
  /* 0x3d */
  0x3439, 0x3461, 0x3460, 0x35e2, 0x35ea, 0x35e3, 0x35b4, 0x35ae,
  0x35be, 0x35b8, 0x35a8, 0x35aa, 0x35a9, 0x35b3, 0x35d5, 0x35ad,
  0x35b9, 0x35bb, 0x35b1, 0x35c2, 0xc908, 0x35eb, 0x35ba, 0x35d2,
  0x35d4, 0x37f1, 0x381d, 0xc912, 0x3862, 0x388c, 0x38e6, 0x38e7,
  0x395a, 0x3958, 0x3959, 0x3996, 0x3997, 0x3a61, 0x3a67, 0x3a71,
  0x3a65, 0x3a7d, 0x3a7e, 0x3b7d, 0x3b84, 0x3b7c, 0x3b7e, 0x3b7f,
  0x3b80, 0x3bef, 0x3bf4, 0x3c1e, 0x3c4e, 0x3cfb, 0x3cfa, 0x3cfd,
  0xc931, 0xc932, 0x3d79, 0x3d7c, 0x3d7d, 0x3d84, 0x3d7b, 0x3d78,
  0x0157, 0x3e5e, 0x3e5a, 0x3e5c, 0x3e59, 0x3e55, 0x3e63, 0x3e56,
  0x3e5f, 0x3e60, 0x3e5b, 0x404a, 0x4065, 0x40b3, 0x402c, 0x4077,
  0x403d, 0x4052, 0x4061, 0x402a, 0x403e, 0x4034, 0x4029, 0x40b2,
  0x40ad, 0x4040, 0x4053, 0xc944, 0x403f, 0x4041,
  /* 0x3e */
  0x4072, 0x43f6, 0x43f5, 0x43f4, 0x43f2, 0x43f9, 0x4527, 0x4554,
  0x4555, 0x452e, 0xc954, 0xc953, 0x452c, 0x4538, 0x4539, 0x4531,
  0x454f, 0x4573, 0x4530, 0x452b, 0x4551, 0x472c, 0x475b, 0x475c,
  0x4768, 0x476c, 0x476b, 0x4769, 0x479f, 0x4838, 0x483c, 0x483a,
  0x4835, 0x029d, 0x4839, 0x4836, 0x483b, 0x4960, 0x4961, 0x4963,
  0x4964, 0x4994, 0x4993, 0x495e, 0x4968, 0x496a, 0x4965, 0xc966,
  0x4990, 0x495f, 0x4972, 0xc965, 0x4c3c, 0x4c27, 0x4c24, 0x4c26,
  0x4c25, 0x035f, 0x4c28, 0x4c36, 0x4d31, 0x4d30, 0x4d34, 0x4d81,
  0x4d7d, 0x4d82, 0x4d80, 0x0379, 0x35d3, 0x4df2, 0x4e66, 0x4e8c,
  0x4e7b, 0x4e83, 0x0398, 0x4e8e, 0x4e7a, 0x4e92, 0x4e91, 0x4e82,
  0x4f1b, 0x4f1c, 0x5027, 0x5021, 0x03dc, 0x1d2b, 0x5043, 0x03df,
  0x5018, 0x507b, 0x501a, 0x504b, 0x504a, 0x504d,
  /* 0x3f */
  0x504f, 0x5019, 0x5035, 0x5013, 0x5052, 0x5014, 0x501e, 0x502c,
  0x5020, 0x5022, 0x5012, 0x501f, 0x5200, 0x5223, 0x5240, 0x5243,
  0x52e4, 0x52db, 0x52ea, 0x52dd, 0x52cc, 0x52d9, 0x52e8, 0x52f6,
  0x52e3, 0x52d3, 0x52da, 0x52d6, 0x52e7, 0x543a, 0x543f, 0x5440,
  0x5448, 0x5459, 0x5437, 0x5444, 0xc98c, 0xc98b, 0x5455, 0x5439,
  0x5554, 0x5555, 0x5556, 0x5557, 0x5558, 0x5559, 0x558d, 0x55f2,
  0x55f8, 0x55f5, 0x55f6, 0x55fc, 0x55fe, 0x55f1, 0x55fd, 0x565e,
  0x5696, 0x5697, 0x569c, 0x569b, 0x5695, 0xc99a, 0x571a, 0x5709,
  0x5704, 0x570e, 0x571c, 0x5718, 0x570d, 0x5710, 0x570c, 0x5703,
  0x587b, 0x58a6, 0x5877, 0x5888, 0x5874, 0x58da, 0x5876, 0x5878,
  0x588a, 0x588f, 0x587d, 0x5890, 0x58ed, 0x58d9, 0x58d0, 0x591a,
  0x58d7, 0x58e2, 0x58e1, 0x58c5, 0x58e0, 0x58ca,
  /* 0x40 */
  0x5925, 0x58cc, 0xc9b3, 0x5bc6, 0x5bc1, 0x5c4d, 0x5d4b, 0x5d64,
  0x5d95, 0x5d99, 0xc9bc, 0x5d94, 0x5da2, 0x5dae, 0x5d9e, 0x5da7,
  0x5d86, 0x05fd, 0x5da4, 0x5d91, 0x5d93, 0xc9bb, 0x5d88, 0x60cd,
  0x60ca, 0x613f, 0x6140, 0x6146, 0x6141, 0x6145, 0x6158, 0x613b,
  0x6148, 0x624e, 0x6252, 0x624f, 0x627b, 0x627a, 0x62a0, 0x629f,
  0x62fb, 0x62f7, 0x63b8, 0x63b9, 0x63bb, 0x63b7, 0x06f4, 0x63ba,
  0x06ef, 0x63da, 0x63b5, 0x63bf, 0x63bc, 0x63c0, 0xc9d3, 0xc9d9,
  0x6575, 0x6579, 0x6576, 0x6635, 0x6640, 0x66c0, 0x6681, 0x66ad,
  0x66af, 0x66ac, 0x668f, 0x66a8, 0x66aa, 0x66a9, 0x6688, 0x667f,
  0x6680, 0x66bc, 0x69ae, 0x69bb, 0x69bd, 0x0831, 0x6a78, 0x6a74,
  0x6b0c, 0x6b11, 0x6b08, 0x6b06, 0x6b10, 0x6b8f, 0x6b90, 0x6b8d,
  0x6b8e, 0x6b96, 0x6b95, 0x6c0b, 0x6c0c, 0x6c7c,
  /* 0x41 */
  0x6c73, 0x6c75, 0x6c76, 0x6c7d, 0x6c78, 0x6c71, 0x6d4b, 0x6d4e,
  0x6e33, 0x6e32, 0x0903, 0x6e91, 0x6ee7, 0x6ee9, 0x6ea2, 0x6e94,
  0x6e87, 0x6ea3, 0x6edd, 0x6e7b, 0x6e83, 0x6e81, 0x6edf, 0x6e7c,
  0x6ee4, 0x6ee2, 0x6e93, 0x6e7d, 0x6ebf, 0x6e9b, 0x6e8e, 0x6e9f,
  0x0909, 0x6e8c, 0x6e7f, 0x6e9c, 0x6e84, 0x6e42, 0x6ee6, 0x7251,
  0x724a, 0x7264, 0x7225, 0x722f, 0x722e, 0x722b, 0x7228, 0x7232,
  0x722d, 0x7231, 0x7239, 0x722c, 0x7261, 0x7511, 0x7510, 0x7512,
  0x7553, 0x7555, 0x757b, 0x7581, 0x757d, 0x757c, 0x75c2, 0x75c5,
  0xca22, 0x75c4, 0xca23, 0x766b, 0x7668, 0x0a3e, 0x765c, 0x765d,
  0x766a, 0xca24, 0x7c76, 0x7776, 0x0a77, 0x77c4, 0x77cb, 0x77c8,
  0x77d4, 0x77d5, 0x77c9, 0x77d7, 0x0a7b, 0x7978, 0x795a, 0x795b,
  0x795c, 0x7956, 0x7958, 0x7971, 0x96d4, 0x7b40,
  /* 0x42 */
  0xca33, 0x7b3f, 0x7b43, 0x7b41, 0x7be2, 0x7be0, 0x7be3, 0x7c66,
  0x7c73, 0x7c6c, 0x7c71, 0x7c6a, 0x7c6d, 0x7c6e, 0x7c6b, 0x7d8c,
  0x7d77, 0xca3a, 0x7d7f, 0x7d89, 0x7d7a, 0x7d85, 0x7d78, 0x7d8a,
  0x7d86, 0x7f2c, 0x7f67, 0x7f5b, 0x7fe5, 0x7fe1, 0x8061, 0x8069,
  0x806a, 0x8165, 0x816d, 0x8163, 0x8186, 0x815c, 0x8162, 0xca48,
  0x8179, 0x8169, 0x8170, 0x8176, 0x815d, 0x8187, 0x816e, 0x8171,
  0x817c, 0x8173, 0x815b, 0x816b, 0x83bf, 0x83c1, 0x83bd, 0x83c9,
  0x83bc, 0x83c2, 0x83c0, 0x8492, 0x84a9, 0x848f, 0x8476, 0x847b,
  0x8475, 0x84a4, 0x8664, 0x873d, 0x87af, 0x0d3a, 0xca58, 0x879d,
  0x8799, 0x87b1, 0x8963, 0x8962, 0x8964, 0x8969, 0x0da0, 0x8a75,
  0x8a73, 0x8a71, 0x8a74, 0x8b0c, 0x8b16, 0x0de5, 0x8b11, 0x8b1f,
  0x8b1a, 0x8b0d, 0x8b1b, 0x8b13, 0x8c4e, 0x8c55,
  /* 0x43 */
  0x8c50, 0x0e7d, 0x8dd2, 0x8dd3, 0x8dd1, 0x8df1, 0x8ddc, 0x8dc8,
  0x8dcc, 0x8dd0, 0x8dcf, 0x8ddf, 0x8f2b, 0x8f2e, 0x8f2d, 0x8f9d,
  0x8f9e, 0x8f9f, 0x8fa9, 0x8fa0, 0x8f98, 0x8fa1, 0x8fab, 0x8faf,
  0x906e, 0x905f, 0x905a, 0x0f6d, 0x9065, 0x9068, 0x9072, 0x9117,
  0x9116, 0x9118, 0x9119, 0x911a, 0x9122, 0x911b, 0x911c, 0x91be,
  0x91ee, 0x925a, 0x9250, 0x9258, 0x9254, 0x9257, 0x9256, 0x9315,
  0x939b, 0x9393, 0x9392, 0x9372, 0x9398, 0x9399, 0x93df, 0x0ff8,
  0x93d5, 0x2958, 0x93d4, 0x93f3, 0x93f4, 0x93e7, 0x93e1, 0x93e6,
  0x93eb, 0x93ec, 0x35db, 0x1050, 0x960d, 0x960c, 0x4d2f, 0x9668,
  0x9665, 0x966b, 0x9669, 0x96d1, 0x96d3, 0x9727, 0x9728, 0x1068,
  0x1067, 0x9724, 0x97d7, 0x98ca, 0x98c7, 0xca9e, 0x98d8, 0x98e1,
  0x98c6, 0x98f8, 0x98c3, 0x98f4, 0x9917, 0x98ea,
  /* 0x44 */
  0x98cb, 0x9886, 0x98c4, 0x98d9, 0x9919, 0x98c2, 0x98e2, 0x10cf,
  0x98de, 0x98ed, 0xca9c, 0x10cd, 0x991d, 0x98dd, 0x98db, 0x98e8,
  0x98e9, 0x98bf, 0x98e0, 0x98d1, 0x98dc, 0x98ce, 0x991e, 0x98cc,
  0x98f2, 0x98f3, 0xca9f, 0xcaa2, 0xcaa3, 0xcaa1, 0x9cab, 0x9caa,
  0x9ca7, 0x9ca9, 0x9d88, 0x9d75, 0x9d60, 0x9d6c, 0x9d73, 0x11ab,
  0x9d6e, 0x9d74, 0x9d76, 0x9d68, 0x9d77, 0x9d86, 0x9fea, 0xa051,
  0xa050, 0xa058, 0xa04d, 0xa04f, 0xa04e, 0xa05c, 0xa052, 0xa044,
  0xa04a, 0xa04b, 0xa1ea, 0xa210, 0xa211, 0xa2cd, 0xa2bf, 0xa2c4,
  0xa2d0, 0xa2ce, 0xa2c0, 0xa2c2, 0xa2cf, 0xa2c9, 0xa2bb, 0xa397,
  0xa392, 0xa36f, 0xa37e, 0xa39a, 0x12c1, 0xa386, 0xa373, 0x2d24,
  0xa377, 0xa38f, 0xa370, 0xa381, 0xa382, 0xa399, 0xa37d, 0xa37f,
  0xa37b, 0xa387, 0xa5b2, 0xa62f, 0xa634, 0xa62e,
  /* 0x45 */
  0xa632, 0x1347, 0xa6aa, 0x1357, 0xa6a9, 0xa738, 0xa736, 0xa737,
  0xa747, 0xa733, 0xa739, 0xa735, 0xa744, 0xa8a5, 0xa8a6, 0xa89e,
  0xa9e3, 0xa9df, 0xa9d3, 0xa9f1, 0xa9e4, 0xa9e0, 0xa9d6, 0x2ec3,
  0xa9e6, 0xa9d8, 0xa9de, 0xa9db, 0xa9dc, 0xab1b, 0xab1f, 0xab1d,
  0xab1c, 0xab1e, 0xab20, 0xab21, 0x144e, 0xabb6, 0xabbc, 0xabc6,
  0xabc7, 0xabba, 0xabbe, 0xabbd, 0xabb5, 0xabb4, 0xad0f, 0xad62,
  0xadeb, 0xadd9, 0xade4, 0xadd7, 0xadd8, 0xadd6, 0xadce, 0xaddd,
  0xade7, 0xadd2, 0xadc5, 0xadc9, 0xaddb, 0xaf92, 0xaf8a, 0xaf8b,
  0xaf89, 0xb008, 0xb003, 0xb006, 0xb005, 0xaff5, 0xb00b, 0xaffb,
  0xb0fc, 0xb101, 0xb102, 0xb0fa, 0xb108, 0xb0f7, 0xb100, 0xb0ff,
  0xb106, 0xb0f6, 0xb0fb, 0xb10a, 0xb225, 0xb243, 0xb244, 0xb364,
  0xb362, 0xb35e, 0xb35b, 0xb3d6, 0x3372, 0x33c2,
  /* 0x46 */
  0xb577, 0xb582, 0xb57c, 0xb57d, 0xb586, 0xb581, 0xb584, 0xb576,
  0xb583, 0xb57f, 0xb57e, 0xb688, 0xb68d, 0xb68b, 0xb691, 0xb68f,
  0xb77c, 0xb779, 0xb77a, 0xb8ea, 0xbb4c, 0xbc99, 0x35d1, 0xbd8b,
  0xbd8d, 0xbd8a, 0xbd8e, 0xbe11, 0x5456, 0xcb15, 0x3374, 0x33c3,
  0x33c4, 0x341b, 0x345f, 0x346a, 0x3469, 0x346b, 0x360c, 0x35f6,
  0x35ed, 0x3629, 0x35fe, 0x35f1, 0x3617, 0x35ff, 0x35ee, 0x35fd,
  0x361c, 0x35fc, 0x3600, 0x3620, 0x0077, 0x35f9, 0x3667, 0x3608,
  0x379e, 0x37f3, 0x3825, 0x3827, 0x381f, 0x3865, 0x3863, 0x3894,
  0x3897, 0x38f1, 0x395f, 0x3962, 0x18f2, 0x3960, 0xc922, 0x3a8c,
  0x3a82, 0x3a90, 0x3a8b, 0x3a8d, 0x3a81, 0x3a9d, 0x3b8e, 0x3b8f,
  0x3b92, 0x3c23, 0x3c52, 0xc92e, 0x3d00, 0x3d01, 0x3d02, 0x3d1f,
  0x3d8c, 0x3d89, 0x3d8b, 0x3d88, 0x3d8d, 0x3d8f,
  /* 0x47 */
  0x9085, 0x3e00, 0x3e05, 0x3e01, 0x3e68, 0x3e6e, 0x3e67, 0x3e75,
  0x1ab6, 0xc945, 0x40d7, 0xc946, 0x3348, 0x40d4, 0x40d8, 0xc947,
  0xc948, 0x40ba, 0xc949, 0x40db, 0x40bf, 0x4135, 0x40bc, 0x40d9,
  0x01c4, 0x40dd, 0x4100, 0x40d5, 0x4130, 0x40bd, 0x40dc, 0x43fd,
  0x43fe, 0x4407, 0x7517, 0x456f, 0x4569, 0x4570, 0x4567, 0x45a9,
  0x4595, 0x4590, 0x456c, 0x4597, 0x4571, 0x0252, 0x4574, 0x456d,
  0x458e, 0x472f, 0xc61b, 0x47a9, 0x484e, 0xc95f, 0x485a, 0x4848,
  0x4855, 0x484c, 0x4849, 0x484f, 0x484a, 0x49d6, 0x49a0, 0x49a9,
  0xc967, 0x499d, 0x49d4, 0x49a4, 0x49a8, 0x49a6, 0x49e6, 0x4b7d,
  0x4b77, 0x4b7a, 0x4c41, 0x4c49, 0x4c59, 0x4c45, 0x4c48, 0x4c40,
  0x4d8e, 0x4d95, 0x4d90, 0x4df7, 0x4df8, 0x4df6, 0x4dfb, 0x4e9e,
  0x4e9d, 0x4e99, 0xc977, 0x4ea3, 0x4ea9, 0x4e98,
  /* 0x48 */
  0x4ea0, 0x4e96, 0x4e94, 0x4e95, 0x4e9f, 0x4ea1, 0x4f21, 0x4f1d,
  0x4f1f, 0x506d, 0xc97c, 0x509a, 0x5092, 0x507a, 0x507d, 0x50a1,
  0x509d, 0x5099, 0x506b, 0x506e, 0xc97d, 0x5245, 0xc984, 0x52fb,
  0x52eb, 0x52f2, 0x52f9, 0xc986, 0x52f8, 0x52ed, 0x5301, 0x52f7,
  0x5306, 0x539b, 0x53ca, 0x046b, 0x1ec3, 0x546b, 0x546c, 0x5474,
  0x5467, 0x545b, 0x5460, 0x5476, 0x5463, 0x5461, 0x5528, 0x555b,
  0x555e, 0x5560, 0x555d, 0x555c, 0x55f4, 0x5600, 0x5608, 0x5607,
  0x5601, 0x5605, 0x5664, 0x5663, 0x569e, 0x56a0, 0x56a1, 0x569f,
  0x5726, 0x572d, 0x5728, 0x571d, 0x58ec, 0x58e3, 0x58eb, 0x5916,
  0x58c8, 0x931b, 0x58e9, 0x58e4, 0x5924, 0x58d1, 0x0541, 0x58dd,
  0x58c2, 0x58cb, 0x58c7, 0x58e7, 0x0550, 0x58ea, 0x594b, 0xc9a5,
  0x5960, 0x597d, 0x593e, 0xc9a4, 0x5952, 0x594e,
  /* 0x49 */
  0x593c, 0x5932, 0x5930, 0x5923, 0x5bca, 0x5bcb, 0x5bc9, 0x5bc8,
  0x5bcd, 0x5d98, 0x5da0, 0x5d9f, 0x5d9c, 0x5da3, 0x5d97, 0xc9be,
  0x5df1, 0x5e09, 0x5e03, 0x5dea, 0x5e45, 0x5ded, 0x5e05, 0x5e1a,
  0x5e15, 0x5e01, 0x5dec, 0x5e0e, 0x5e17, 0x5e42, 0x5e12, 0x5e10,
  0x5def, 0x5dff, 0x5e00, 0x5e0c, 0x5e0f, 0x5e04, 0x5e08, 0x5e14,
  0x5e43, 0xc9bd, 0x5e1b, 0x5e11, 0x5e13, 0x60cf, 0x60ce, 0x616f,
  0x616e, 0x617a, 0x6170, 0x6164, 0x615e, 0x616c, 0xc9c9, 0x615b,
  0x6161, 0x6165, 0x627f, 0x6280, 0x627c, 0x62a7, 0x62a6, 0x62a1,
  0x06c0, 0x62a8, 0x62a3, 0x62a2, 0x62ad, 0x62a5, 0x6301, 0x62ff,
  0x62fc, 0x6300, 0x6335, 0x63ee, 0x63ef, 0x63f6, 0x63e8, 0x63ea,
  0x63e3, 0x641f, 0x06fc, 0x63e4, 0x63fa, 0x63f1, 0x63fb, 0xc9d4,
  0x653d, 0x653c, 0x0079, 0x6578, 0x6577, 0x100b,
  /* 0x4a */
  0x66d1, 0x66c7, 0x66df, 0x66d0, 0x66e0, 0x66d6, 0x66d8, 0x6716,
  0x670e, 0x66d9, 0x670f, 0x6711, 0x66cd, 0x6689, 0x66ce, 0x6714,
  0x66da, 0x6712, 0x66d3, 0x66c2, 0x66e1, 0x66e9, 0x66ea, 0x66de,
  0x6715, 0x69d9, 0x69d6, 0x69cd, 0x69d0, 0x69d3, 0xc9f0, 0x6a82,
  0x6a85, 0x6a7f, 0x6a7d, 0x6a81, 0x6a83, 0x6a84, 0xada0, 0x6b28,
  0x6b0f, 0x6b17, 0x6b1a, 0x6b25, 0xc9f7, 0x6b9b, 0x6b99, 0x6c92,
  0x6c8c, 0x6c95, 0x6c8d, 0x6ca3, 0x6c93, 0x6c91, 0x6edb, 0x6e99,
  0x6e9a, 0x6f08, 0x6f4c, 0x6f0d, 0x6f01, 0x6f4e, 0x6f02, 0x6f4d,
  0x6f21, 0x6efc, 0xca09, 0x6e8a, 0xca08, 0x6e95, 0x6f11, 0x6f12,
  0x6f46, 0x6f1c, 0x6f49, 0x6f0c, 0x091e, 0x6f13, 0x6f16, 0x6efd,
  0x6f0f, 0x6f1f, 0x7230, 0x726e, 0x726b, 0x729b, 0x727b, 0x7263,
  0x7297, 0x726d, 0x729c, 0x7298, 0x726f, 0x7267,
  /* 0x4b */
  0x7269, 0x7515, 0x7563, 0x7586, 0x758a, 0x7587, 0x7588, 0x7585,
  0x7589, 0x75ca, 0x75c7, 0x75cb, 0x75cc, 0x75c9, 0x760d, 0x7683,
  0x7684, 0x7678, 0x7682, 0x7673, 0x7679, 0x768c, 0x77d0, 0x77cf,
  0x77d2, 0x77d9, 0x77cc, 0x77eb, 0x77fd, 0x77ec, 0x77e8, 0x77f8,
  0x77fa, 0xca27, 0x0a85, 0x77e1, 0x77fb, 0x78e7, 0xca2e, 0x79a5,
  0x7991, 0x79a6, 0x797c, 0x7992, 0x79a2, 0x79a0, 0x7afc, 0x7afe,
  0x7b57, 0x7b53, 0x7b58, 0x7be7, 0x7c8e, 0xca38, 0x7c83, 0x7c8b,
  0x7c84, 0x0b97, 0x7da2, 0x7db7, 0x7da9, 0x7da5, 0x7d9f, 0x7daa,
  0x7d97, 0x7da1, 0x7d9e, 0x7dab, 0x7d99, 0x7da3, 0x7f30, 0x7f32,
  0x7f2f, 0x7f70, 0x7f6c, 0x7f6f, 0x7fe8, 0x7fee, 0x7fea, 0x806d,
  0x8076, 0x8070, 0x8071, 0x806c, 0x81eb, 0x81b5, 0x8196, 0x8190,
  0x818d, 0xca49, 0x81a2, 0x81b0, 0x8192, 0x81a0,
  /* 0x4c */
  0x8193, 0x81c3, 0x818e, 0x81b6, 0x819d, 0x8195, 0x81b3, 0x81a4,
  0x8370, 0x83d4, 0x0c93, 0x83cf, 0x3470, 0x8494, 0x2561, 0x84c0,
  0x84b4, 0x84c1, 0x0cb2, 0x84bd, 0x84af, 0x8677, 0x8678, 0x866b,
  0x866d, 0x866e, 0x8672, 0x866f, 0x8671, 0x62ab, 0x868c, 0x873f,
  0x87b6, 0x87b7, 0x2606, 0x87bb, 0x87b8, 0x3a9c, 0x87b9, 0x2601,
  0x898b, 0x897a, 0x8984, 0x8988, 0x8991, 0x8979, 0x898e, 0x8980,
  0x8982, 0x897c, 0x0da4, 0x8a84, 0x8a7f, 0x0df2, 0x8b3b, 0x8b71,
  0x8b3d, 0x8b30, 0x0df1, 0x8b3e, 0x8b38, 0x8c5e, 0x8c64, 0x0e81,
  0x8c5d, 0x8c6d, 0x8c4f, 0x8c62, 0x8c5f, 0x8dec, 0x8df2, 0x8df4,
  0x8df7, 0x8df6, 0x8e07, 0x8ded, 0x8dea, 0x8df0, 0x8df8, 0x8df3,
  0x0edc, 0xca72, 0x8f31, 0x8f2f, 0x8fb6, 0x8fae, 0x8faa, 0x8fbf,
  0x8fcc, 0x8fc7, 0x9983, 0x9080, 0x907b, 0x907f,
  /* 0x4d */
  0x907d, 0x9083, 0xca78, 0x9146, 0x912d, 0x9125, 0x9126, 0x912c,
  0x9137, 0x9131, 0x9133, 0x9132, 0x9127, 0x912a, 0x912e, 0x912f,
  0x91c0, 0x9271, 0x9261, 0x9262, 0x9266, 0x9318, 0x93c5, 0x93c3,
  0x93c4, 0x93c2, 0x93ae, 0x9410, 0x9408, 0x941f, 0x943a, 0x943b,
  0x9436, 0x940c, 0x9406, 0x942a, 0x9457, 0x9450, 0x9420, 0xca85,
  0x942c, 0x9421, 0x940b, 0x9419, 0x9435, 0x9418, 0x940f, 0x9413,
  0x9455, 0x9439, 0x941a, 0x100c, 0x9417, 0x95e4, 0x95e9, 0x758b,
  0x9610, 0x9612, 0x963f, 0x966e, 0x7518, 0xca8c, 0x96d6, 0x96d5,
  0x96d7, 0x3e02, 0x106d, 0x9731, 0x9730, 0x973a, 0x9885, 0x986b,
  0x9948, 0x994b, 0x9937, 0x997b, 0x996c, 0x9985, 0x9965, 0x9936,
  0x9986, 0x9934, 0x9968, 0x995a, 0x9958, 0x9972, 0x996a, 0x98d3,
  0x993c, 0x9933, 0x993b, 0x994d, 0x994f, 0x997c,
  /* 0x4e */
  0x99b0, 0x995b, 0x9955, 0x9964, 0x996b, 0x9953, 0x10e6, 0x9957,
  0x995e, 0x996d, 0x9935, 0x9969, 0x9959, 0x9966, 0x9950, 0x9951,
  0x995c, 0x9a5d, 0x9987, 0x9978, 0x9949, 0x994e, 0x98f1, 0x9973,
  0x9988, 0x98cf, 0xcaa4, 0x9cb1, 0x9cb3, 0x9d9c, 0x9da4, 0x9d90,
  0x9db8, 0x9da0, 0x9d9d, 0x9da8, 0x9da9, 0xcab9, 0x9db1, 0x9d93,
  0x9d9b, 0x9da2, 0x9da1, 0x9db0, 0x9da7, 0x9fb3, 0x2b49, 0x9fb4,
  0x9ff1, 0x9fed, 0x9fec, 0xa068, 0xa075, 0xa06a, 0xa062, 0xa067,
  0xa060, 0xa077, 0xa05f, 0xa079, 0xa223, 0xa221, 0xa21c, 0x2c99,
  0xa21f, 0xa21e, 0xa2d6, 0xa2d3, 0xa2d9, 0xa2d7, 0xa2d4, 0xa2dc,
  0xa2d8, 0xa3a3, 0x12d1, 0xa3be, 0xa3a9, 0xa3a1, 0xa3a0, 0xa3ab,
  0xa3a2, 0xa3ba, 0xa3c2, 0xa39c, 0xa3bb, 0xa3aa, 0xa3ac, 0xa3a5,
  0xa3c1, 0x132c, 0xa5e2, 0xa636, 0xa63d, 0xa63a,
  /* 0x4f */
  0x5661, 0xa648, 0xa63c, 0xa6b7, 0xa6ac, 0xa6b3, 0xa6b6, 0xa6b2,
  0x136e, 0xa75d, 0xa749, 0xa74e, 0xa74f, 0xa74d, 0xa75c, 0xa85d,
  0xa8bb, 0xa8ce, 0xa8bf, 0xa8ba, 0xa8c3, 0x13e5, 0xaa06, 0xa9f8,
  0xa9fe, 0xaa13, 0xa9f6, 0x13e6, 0xab34, 0xab2d, 0xab2a, 0xab35,
  0xab2c, 0xabd4, 0xabda, 0xabd3, 0xabd2, 0xabce, 0xabcf, 0x149f,
  0xadfd, 0xae0b, 0xadfe, 0xadf8, 0xadf7, 0xae17, 0xadfa, 0xadf9,
  0xae00, 0xadf5, 0xadf1, 0xae03, 0xae05, 0xae1c, 0xafac, 0xaf9a,
  0xafad, 0xafa0, 0xb01b, 0xb025, 0xb024, 0xb026, 0xb027, 0xb028,
  0xb02a, 0xb01a, 0xb02e, 0xb015, 0xb115, 0xb114, 0xb117, 0xb118,
  0xb10f, 0xb113, 0xb10b, 0xb122, 0x457a, 0xb226, 0x301f, 0xb251,
  0xb24d, 0xb24e, 0xb24a, 0xb24b, 0xb24c, 0xb250, 0xb262, 0xb24f,
  0xb252, 0xb368, 0xb369, 0xb3e5, 0xb3f0, 0xb3e1,
  /* 0x50 */
  0xb3e2, 0xcaf2, 0xb5a5, 0xb5a9, 0xb5a4, 0xb5af, 0xb5ac, 0xb5ae,
  0xb5aa, 0xb695, 0xb699, 0xb693, 0xb69d, 0xb698, 0xb69c, 0xb697,
  0x160d, 0xb789, 0xb787, 0xb8d7, 0xb8ed, 0xb8f1, 0xb8f0, 0xb905,
  0xb903, 0xb904, 0xb95f, 0xbb57, 0xbc9c, 0xbca1, 0xbc9a, 0xbd8f,
  0xbd93, 0xbd9e, 0xbda3, 0xbd98, 0xbd99, 0xbd95, 0xbe6e, 0xbe6a,
  0xbff4, 0xbff7, 0xc179, 0xc38f, 0xc391, 0xc40b, 0xc802, 0x3384,
  0x3404, 0x3480, 0x362f, 0x363f, 0x363b, 0x3662, 0x3644, 0x3633,
  0x365f, 0x362b, 0xc909, 0x3639, 0x3636, 0x3648, 0x3635, 0x366c,
  0x3658, 0x363a, 0x37a8, 0x37f4, 0x3829, 0x389a, 0x3900, 0x3abb,
  0x3abd, 0x3ab7, 0x3ab2, 0x00f8, 0x3aa8, 0x3aab, 0x3aa6, 0x3abc,
  0x3b97, 0x3b96, 0x3bf8, 0x1913, 0x3c51, 0x0133, 0x3cb5, 0x3cb4,
  0x3cb6, 0x013c, 0x3cb7, 0x3d05, 0x3d23, 0x3d95,
  /* 0x51 */
  0x3d98, 0x014f, 0x3d94, 0x3d93, 0x3e07, 0x3e73, 0x3e71, 0x3e72,
  0x3e78, 0x415f, 0x416a, 0x4167, 0x416b, 0x4169, 0x418e, 0x4149,
  0x4180, 0x01dc, 0x4144, 0x418f, 0x4145, 0xc94c, 0x414f, 0x4163,
  0x4136, 0x4148, 0x415c, 0x4193, 0x4161, 0x4160, 0x414e, 0x415e,
  0x413f, 0x41a4, 0x1ae0, 0x4168, 0x440b, 0x4411, 0x440d, 0x440e,
  0x45c2, 0xc957, 0x45b0, 0x45c3, 0x45c8, 0x4565, 0x45d7, 0x45bd,
  0x45b8, 0x45b6, 0x45c7, 0x45bc, 0x45b1, 0x45af, 0xc95b, 0x4735,
  0x4730, 0x475d, 0x3e76, 0x476f, 0x47b0, 0xc95e, 0x47b4, 0x485c,
  0x485d, 0x4a08, 0x49ee, 0x4a0b, 0x49f2, 0x49fd, 0x49f1, 0x4a10,
  0xc968, 0x4a14, 0x4b8b, 0x4b8c, 0x4b85, 0x4c7a, 0x4c6c, 0x4c60,
  0x4c67, 0x4c66, 0x4c6a, 0x4c5f, 0x4c6d, 0x4c68, 0x4c64, 0x4d3a,
  0x4d3b, 0x4d39, 0x4e02, 0x4e04, 0x4e03, 0x4eb4,
  /* 0x52 */
  0x4eb3, 0x4ebb, 0x4eac, 0x4eb6, 0x4eb1, 0x4eaf, 0x4eb5, 0x4ebe,
  0x4eb2, 0x4f24, 0x50b6, 0x50b9, 0x50ac, 0x50b0, 0x50d7, 0x50bb,
  0x50fe, 0x50cb, 0xc97f, 0x03f5, 0x50b3, 0x50be, 0x50cd, 0x50bc,
  0x50ba, 0x50c7, 0x5316, 0x531b, 0x5317, 0x5315, 0x539f, 0xc98e,
  0x5482, 0x5483, 0x548e, 0x546a, 0x5489, 0x5494, 0x5486, 0x5490,
  0x5562, 0x5590, 0x560c, 0x560f, 0x04c3, 0x56a5, 0x56a7, 0x56a6,
  0x56a4, 0x5735, 0x5738, 0x5736, 0x5743, 0x5747, 0x5737, 0x5943,
  0x59a2, 0x5951, 0x5972, 0x596d, 0x592f, 0x5954, 0x596e, 0x5955,
  0x5937, 0x594f, 0x5950, 0x1f39, 0x599e, 0xc9a8, 0x59b3, 0x59a7,
  0x0571, 0x59f9, 0x5991, 0xc9a7, 0x59b6, 0x59dd, 0x5999, 0x5bdf,
  0x5bd5, 0x77f5, 0x5e28, 0x5e40, 0x5e71, 0x5e98, 0x5e95, 0x5e65,
  0x5e78, 0x5e7f, 0x5e60, 0x5e7c, 0x5e96, 0x5e6a,
  /* 0x53 */
  0x5e79, 0x5e73, 0x5e72, 0x5e7b, 0x5e70, 0x60d5, 0x60d7, 0x618f,
  0x6189, 0x619e, 0x6187, 0x61a0, 0x618d, 0x6188, 0x617f, 0x618c,
  0x6193, 0x6259, 0xbcb0, 0x62b1, 0x81f4, 0x62af, 0x62b2, 0x6308,
  0x630a, 0x6336, 0x6337, 0xc9cf, 0x0701, 0x6436, 0x6429, 0x644a,
  0x6426, 0x6428, 0x6424, 0x642a, 0x6448, 0x6544, 0x6584, 0x658c,
  0x66c4, 0x66dc, 0x6787, 0x6753, 0x677f, 0x6731, 0x6751, 0x674b,
  0x6752, 0x6780, 0x67a5, 0x6781, 0x6743, 0x6734, 0x6736, 0x6732,
  0x6748, 0x6749, 0x673c, 0x674d, 0x674a, 0xc9e6, 0x678a, 0x6746,
  0x673e, 0x6783, 0x6750, 0x67b4, 0x69f0, 0x69e4, 0x69e3, 0x69e5,
  0x69e6, 0x69e7, 0x69e1, 0x69ef, 0x69e8, 0x69dd, 0x6a03, 0x6a88,
  0x6b26, 0x6b16, 0x6b3b, 0x6b2f, 0x6b39, 0x6b34, 0xc9f4, 0x6b35,
  0x6b31, 0x6b38, 0x3e81, 0x6baa, 0x6ba3, 0x6ba4,
  /* 0x54 */
  0x6ba0, 0x6ba1, 0x6ba9, 0x6ba5, 0x6caf, 0x6cb1, 0x6cab, 0x6cae,
  0x6cb0, 0x6cb3, 0x6cac, 0x6ca9, 0x6cb2, 0x6ca8, 0x6cb4, 0x6cc2,
  0x6d4f, 0x6d66, 0x6f1e, 0x6f15, 0x6f10, 0x6f7f, 0x6f7e, 0x6f60,
  0x6fcc, 0x6fb2, 0x6f62, 0x6f8d, 0x6f8e, 0x6f77, 0x6f7c, 0x6f8f,
  0x6f5d, 0x6f6d, 0x6f63, 0x6faf, 0x6f90, 0x6f7d, 0x6f7a, 0x6f06,
  0xca0b, 0x6f68, 0x6fb4, 0x6f78, 0x6fb1, 0x22d7, 0xca0c, 0x0941,
  0x72e8, 0x72e9, 0x72c0, 0x09d7, 0x72ea, 0x72b7, 0x72ba, 0x72b5,
  0xca1d, 0x72b4, 0x72bc, 0x72c6, 0x72b8, 0x72bd, 0x72c2, 0x734d,
  0x72f0, 0x72c7, 0x72c1, 0x72c3, 0x72f1, 0x72ec, 0x09e2, 0x751c,
  0x7520, 0x7558, 0x7565, 0x7564, 0x758c, 0x758d, 0x75d9, 0x75e0,
  0x7610, 0x7694, 0x7692, 0x7696, 0x7695, 0x76bf, 0x76a0, 0x0a45,
  0x77f4, 0x77f6, 0x77dc, 0x243d, 0x7816, 0x7815,
  /* 0x55 */
  0x781c, 0x780f, 0x782c, 0x7814, 0x7825, 0x7817, 0x7812, 0x781e,
  0x7980, 0x79a8, 0x79af, 0x79d6, 0x79e2, 0x79b4, 0x79b3, 0x79b0,
  0x79b2, 0x79a1, 0x7b60, 0x7b66, 0x7b61, 0x7b4e, 0x7b5d, 0x7b63,
  0x7be6, 0x7bef, 0x7bec, 0x7c98, 0x7ca7, 0x7c94, 0x7c95, 0x7c91,
  0x7c9d, 0x7c99, 0x7c9b, 0x7c9c, 0x7d1d, 0x7d1c, 0x7dd0, 0x7de0,
  0x7dcb, 0x7ddb, 0x7dda, 0x7dc2, 0x7dd3, 0x7de5, 0x7f7d, 0x7f7b,
  0x7fff, 0x0bf2, 0x7ff9, 0x8077, 0x807c, 0x8078, 0x807b, 0x807a,
  0x81d2, 0x81cb, 0x81c9, 0x81ce, 0x81e4, 0x81ca, 0x81d0, 0x61a5,
  0x0c49, 0x81d9, 0x81ee, 0x81dd, 0x8200, 0x81e1, 0x83de, 0x83e2,
  0x83da, 0x84ce, 0xca4f, 0x84cf, 0x84da, 0x84d1, 0x84d4, 0x84ed,
  0x84cb, 0x84d5, 0x84f1, 0x869c, 0x8688, 0x8741, 0x87d0, 0x87f7,
  0x87cf, 0x87d1, 0x87db, 0x87de, 0x87f8, 0x87dc,
  /* 0x56 */
  0x87d9, 0x89a0, 0x89b2, 0x89a1, 0x89aa, 0x89a9, 0x0da9, 0x89a6,
  0x899c, 0x89b5, 0x89a7, 0x8a8e, 0x8a90, 0x8a91, 0x8b85, 0x8b5b,
  0x8b70, 0x8b64, 0x8b67, 0x8b63, 0x8b77, 0x8b68, 0x8b65, 0x8b6a,
  0x8b78, 0x8b66, 0x8c88, 0x8c9e, 0x8c74, 0x8c7a, 0x8c79, 0x8c8b,
  0x8c7f, 0x8e13, 0x8e1e, 0x8e17, 0x8e1a, 0x8e22, 0x8e43, 0x8e19,
  0x8e1f, 0x8e27, 0x8e12, 0x8e24, 0x8e25, 0x365e, 0x8f35, 0x8f34,
  0x8fd1, 0x8fc4, 0x8fca, 0x8fc6, 0x8fcb, 0x8fcd, 0x8fe2, 0x9089,
  0x908b, 0x9086, 0x9088, 0x908d, 0x913b, 0x913c, 0x913d, 0x91f5,
  0x9279, 0x9275, 0x9282, 0x927f, 0x9285, 0x9276, 0x927c, 0x927e,
  0x927b, 0x9280, 0x927a, 0x5748, 0x0fcb, 0x931d, 0x93f6, 0x93f7,
  0x93f9, 0x9463, 0x946c, 0x946e, 0x9414, 0x100d, 0x9467, 0x946f,
  0x9469, 0x9476, 0x9495, 0x9471, 0x9461, 0x9478,
  /* 0x57 */
  0x946b, 0x9485, 0x9484, 0x9614, 0x9676, 0x89b6, 0x9675, 0x9674,
  0x96e2, 0x973f, 0x9744, 0x973d, 0x9747, 0x9748, 0x97db, 0x97dc,
  0x97dd, 0x98e5, 0x98e6, 0x99c2, 0x9a2e, 0x9a1d, 0x99f8, 0x99f0,
  0x99f6, 0x99c5, 0x99c6, 0x99fc, 0x9a52, 0x9a2f, 0x9a10, 0x99f3,
  0x99d2, 0x99ea, 0x99dc, 0x9a1b, 0x99fb, 0x99c3, 0x9a16, 0x9a07,
  0x99c9, 0x99d8, 0x9a30, 0x9a13, 0x9a31, 0x99fa, 0x99f2, 0x9ae3,
  0x99d5, 0x9a01, 0x99f1, 0x9a1c, 0x99d6, 0x9a08, 0x9a0b, 0x9a17,
  0x9a20, 0x99ca, 0x9a32, 0x9a05, 0x99e4, 0x99ce, 0x9a33, 0x9a02,
  0x9a19, 0x9a1e, 0x99d3, 0x99f7, 0x99e8, 0x9a1f, 0x99f4, 0x9ad8,
  0x9cbf, 0x9cbe, 0x9cbd, 0x9ddc, 0x9ddd, 0x9dab, 0x9dc9, 0x9dc8,
  0x9ddf, 0x9dd9, 0x9ddb, 0x9dcc, 0x9de0, 0x9def, 0x9df3, 0x9dae,
  0x9e01, 0x9fb7, 0x9fb9, 0x9fb6, 0x9fb8, 0x9ff6,
  /* 0x58 */
  0x9ff3, 0x9ff5, 0x9ff2, 0xa091, 0xa09d, 0xa09b, 0xa092, 0xa08d,
  0xa09e, 0xa08c, 0x1231, 0xcac7, 0xa095, 0xcac6, 0xa08a, 0xa08e,
  0xa09c, 0xa1ef, 0xa22d, 0xa252, 0xa235, 0xa228, 0xa22e, 0xa2e5,
  0xa3ea, 0xa3f1, 0xa3eb, 0xa3d8, 0xa3d0, 0xa3f3, 0xa3db, 0xa3ce,
  0x12d5, 0xa3da, 0xa3d7, 0xa3e1, 0xa3f2, 0xa3c8, 0xa3d9, 0xa3de,
  0xa3d1, 0xa3e7, 0xa3cf, 0xa5b7, 0xa647, 0xa642, 0xa643, 0xcad3,
  0xa6c3, 0xa6c1, 0xa6c7, 0xa764, 0xa76a, 0xa766, 0xa750, 0xa76e,
  0xa765, 0x69ec, 0xa77f, 0xcad5, 0xa79a, 0xa769, 0xa772, 0xa76f,
  0xa77d, 0xa770, 0xa860, 0xa8c2, 0xa8e7, 0xa8d1, 0xa8eb, 0xa8d4,
  0xa8dc, 0xa8db, 0xaa37, 0xaa25, 0xaa1f, 0xaa1e, 0xaa21, 0xaa1b,
  0xaa17, 0xaa22, 0xaa2a, 0xaa1a, 0xaa2d, 0xaa23, 0xaa26, 0xaa36,
  0xa9ff, 0xab3a, 0xab40, 0xab42, 0xab38, 0xab3b,
  /* 0x59 */
  0xab3c, 0xab43, 0xabe8, 0x1456, 0xabf9, 0xabeb, 0xabf1, 0xabe9,
  0xabec, 0xad15, 0xad47, 0xad46, 0xad45, 0xae24, 0xae56, 0xae21,
  0xae27, 0xae4d, 0xae31, 0xae1e, 0xae2c, 0xae4f, 0xae2b, 0xae53,
  0xae51, 0xae54, 0xae29, 0xae50, 0xae1f, 0xae32, 0xae2a, 0xae1d,
  0xae28, 0xae2e, 0xae2d, 0xafbc, 0xafbb, 0xafbd, 0xcae4, 0xb047,
  0xb041, 0xcae5, 0xb049, 0x14f4, 0xb12e, 0xb127, 0xb26a, 0xb27b,
  0xb273, 0xb275, 0xb269, 0xb279, 0xb272, 0xb376, 0xb377, 0xb374,
  0xb373, 0xb402, 0xb3fe, 0xb401, 0xb3f9, 0xb3f4, 0xb5d3, 0xb5d5,
  0xb5d8, 0xb5c3, 0xb5ca, 0xb5d0, 0xb5cb, 0xb5ce, 0xb5c5, 0xb5e6,
  0xb5c4, 0xb5c0, 0xb5d4, 0xb5e8, 0xb676, 0xb6a2, 0xb6ae, 0xb6a8,
  0xb6a3, 0xb6a7, 0xb696, 0xb6a9, 0xb6a5, 0xb6af, 0xb6a4, 0xb6ab,
  0xb6aa, 0xb6a6, 0xb6a0, 0xb798, 0xb8db, 0xb8f6,
  /* 0x5a */
  0xb8f5, 0xb90c, 0xb90a, 0x3175, 0xb968, 0xb963, 0xb966, 0x1658,
  0xb964, 0xb96a, 0xb969, 0xba95, 0xbb02, 0xbb6a, 0xbb5e, 0xbb68,
  0xbb69, 0xbb65, 0xcafe, 0xbca7, 0xbcae, 0xbca8, 0xbcb3, 0xbd9c,
  0xbda9, 0xbdb6, 0xbdb3, 0xbdb2, 0xcb03, 0xbdb8, 0xbdc0, 0xbdbf,
  0xbdba, 0xbda8, 0xbe3c, 0xbe72, 0xbe71, 0xbe75, 0xbe73, 0xbf17,
  0xbf15, 0xbf16, 0xbf1b, 0xbffa, 0xbff9, 0xc12c, 0xc185, 0xc182,
  0xc17f, 0xc17d, 0xc188, 0x72f3, 0xc24e, 0xc250, 0xc393, 0xc397,
  0xc398, 0xc39b, 0xc39c, 0xc396, 0xc58b, 0xc5fd, 0xc5fc, 0xcb17,
  0x6a8b, 0x3408, 0x3407, 0x3673, 0x36a2, 0x36af, 0x3682, 0x367b,
  0x3674, 0x36b0, 0x3676, 0x36b9, 0x369e, 0x36b1, 0x36a1, 0x36b2,
  0x366e, 0xc90b, 0x0087, 0x3678, 0x367a, 0x3683, 0x369a, 0x37f8,
  0x3831, 0x3869, 0x3868, 0x389c, 0x3904, 0x3999,
  /* 0x5b */
  0x3ac7, 0x3ac6, 0x3adc, 0x3ac4, 0x3ad8, 0x3ad4, 0x3adf, 0x3ad1,
  0x3ad0, 0x3ad6, 0x3acf, 0x3bad, 0x3baf, 0x3ba7, 0x3bfd, 0x3c5b,
  0x3c5a, 0x3d07, 0x3d9d, 0x3d9b, 0x3dab, 0x3da4, 0x3d9c, 0x3d9e,
  0x3da5, 0x3daa, 0x3da6, 0x3e0e, 0x3e7e, 0x3e7c, 0x41ee, 0x41d5,
  0x41e9, 0x4142, 0x41e2, 0x4223, 0x41d9, 0x41d4, 0x41e3, 0x4215,
  0x41ef, 0x41f0, 0x41d6, 0x41dd, 0x41f6, 0x421c, 0x41d8, 0x41db,
  0x41da, 0x41ed, 0x4611, 0x4415, 0x4418, 0x441a, 0x441f, 0x4416,
  0xc94d, 0x4419, 0xc94b, 0x45f0, 0x4609, 0x461b, 0xa5e7, 0x45f6,
  0x45f4, 0x45b5, 0x4610, 0x45f2, 0x4615, 0x45f3, 0x45f8, 0x4739,
  0x473b, 0x4736, 0x460e, 0x4772, 0x4774, 0x47b9, 0x47b7, 0x47b8,
  0x4872, 0x486b, 0x4a1d, 0x4a37, 0x4a22, 0x4a43, 0x4a4d, 0x4a38,
  0x4a5b, 0x4a79, 0x4a1b, 0x49f3, 0x4b91, 0x4c7b,
  /* 0x5c */
  0x4c94, 0xc96f, 0x4c96, 0x4c7f, 0x4c8f, 0x4c84, 0x4c7c, 0x4c8e,
  0x4c90, 0x4c98, 0x4c83, 0x4c80, 0x4c93, 0x4c82, 0x32fd, 0x4d3d,
  0x4d41, 0x4da1, 0x4d9f, 0x4e0a, 0x4e0d, 0x4ec8, 0x4ec9, 0x4ec7,
  0x4ecd, 0x4f25, 0x50b1, 0x50dc, 0xc980, 0x50e5, 0x50f4, 0x50bf,
  0x50db, 0x50ea, 0x50f2, 0x03fa, 0x50f1, 0x50ed, 0x50e6, 0x5202,
  0xc982, 0x5325, 0x5318, 0x531f, 0x5320, 0x53cf, 0x549d, 0x5499,
  0x54a8, 0x5568, 0x5566, 0x5567, 0x5591, 0x5613, 0x5615, 0x561d,
  0x5616, 0x5619, 0x566b, 0x5668, 0x566a, 0x566d, 0x5669, 0x56aa,
  0x5757, 0x5752, 0x5750, 0x575f, 0x5767, 0x574f, 0x04f2, 0x575b,
  0x575c, 0x575d, 0x5a1f, 0x599d, 0x59b1, 0x59b0, 0x5994, 0x59c3,
  0x59af, 0x59a8, 0x59dc, 0x5998, 0x59c4, 0x59a4, 0x59ab, 0x59aa,
  0x59a5, 0x5a21, 0x59eb, 0x59e6, 0x59f7, 0x59f8,
  /* 0x5d */
  0x59fc, 0x59fa, 0x59e0, 0xc9aa, 0x59f6, 0xc9a9, 0x59e1, 0x5bec,
  0x5be2, 0x5be4, 0x5bf9, 0x5e6f, 0x6b4c, 0x5ebb, 0x5ee1, 0x5f00,
  0x5ed8, 0x062f, 0x5ed6, 0x5ee2, 0x5ec3, 0x5eb3, 0x5ed2, 0xc9c1,
  0x5ece, 0x5ed0, 0x5ed5, 0x5eb9, 0x5eba, 0x5ecf, 0x5ebd, 0x60db,
  0x61aa, 0x61ad, 0x61b8, 0x61b6, 0x61b5, 0x61af, 0x61b4, 0x61b7,
  0x61a8, 0x61b9, 0x61be, 0x6282, 0x62bc, 0x62b8, 0x62b6, 0x62b9,
  0x06c1, 0x6310, 0x6427, 0x6469, 0x6470, 0x6456, 0x646b, 0x647a,
  0x646c, 0x646d, 0xc9d5, 0x94c1, 0x658d, 0x6590, 0x67b6, 0x6810,
  0x6812, 0x67ba, 0x67bd, 0x6805, 0x67c2, 0x6807, 0x67f5, 0xc9e9,
  0x67af, 0x67f4, 0x67f7, 0x67f8, 0x6811, 0x69f6, 0x69f5, 0x69fb,
  0x6a01, 0x6a00, 0x6a02, 0x69fe, 0x69fa, 0x69fd, 0x0840, 0x6b37,
  0x6b49, 0x6b4b, 0x6b46, 0x6b47, 0x6bb3, 0x6bb2,
  /* 0x5e */
  0x6bb0, 0x6bb7, 0x6c11, 0x6ccc, 0x6cdf, 0x6cd3, 0x6cd5, 0x6cdb,
  0x6cc5, 0x6cc8, 0x6cc9, 0x6ce2, 0x6cca, 0x6cd1, 0x6cd2, 0x6cdd,
  0x6f6c, 0x6f73, 0x7021, 0x6ff0, 0x701f, 0x703b, 0x7022, 0x7023,
  0x6fe8, 0x6fdd, 0x093f, 0x701b, 0x6fed, 0xca0e, 0x6ff2, 0x0946,
  0x6fdc, 0x6fe9, 0x701d, 0x6fda, 0x6fe6, 0x7313, 0x7315, 0x7316,
  0x733c, 0x730b, 0x731c, 0x733a, 0x733d, 0x739a, 0x731d, 0x7309,
  0x7308, 0x733b, 0x7522, 0x7526, 0x7525, 0x7524, 0x369b, 0x758f,
  0x7590, 0x75e6, 0x75e3, 0x75e5, 0x7611, 0xca25, 0x76ae, 0x76be,
  0x76b4, 0x76b3, 0x76af, 0x7691, 0x76c2, 0x76b6, 0x76b2, 0x7857,
  0x783b, 0x7858, 0x7851, 0x7841, 0x7839, 0x0a8f, 0x7859, 0x7845,
  0x7861, 0x78e8, 0x79fa, 0x79ea, 0x79ef, 0x79f2, 0x79f0, 0x7b08,
  0x7b70, 0x7b6a, 0x7b73, 0x7b68, 0x7bc8, 0x7bf2,
  /* 0x5f */
  0x3e7b, 0x7cae, 0x7cab, 0x7cb5, 0x7caf, 0x7cb2, 0x7cb6, 0x7cb0,
  0x7d1e, 0x7e03, 0x7e06, 0x7e1f, 0x0bac, 0x7e0f, 0x7e02, 0x7e19,
  0x7e18, 0x7e22, 0x7e15, 0x7e07, 0x7e0d, 0x7e24, 0x7e0c, 0x7e1e,
  0x7f89, 0x7f8a, 0x800a, 0x800b, 0x8007, 0x8004, 0x8009, 0x8084,
  0x8083, 0x8218, 0x8214, 0x8205, 0x8216, 0x820e, 0x8211, 0x8208,
  0x820b, 0x8215, 0x8085, 0x8237, 0x822a, 0x820d, 0x820f, 0x837e,
  0x8376, 0x8377, 0x83ec, 0x84fc, 0x8508, 0x84ff, 0x8503, 0x8510,
  0x8505, 0x8506, 0x84fa, 0x86c7, 0x86c0, 0x86c3, 0x86a7, 0x86a8,
  0x86ab, 0x86c1, 0x86aa, 0x86c8, 0x8743, 0x8802, 0x880e, 0x8801,
  0x87fe, 0x8803, 0x0d68, 0x8822, 0x8821, 0x8807, 0x8808, 0x880c,
  0x89ca, 0x89bc, 0x89be, 0x89bd, 0xca5c, 0x89bb, 0x89b9, 0x0dab,
  0x89c5, 0x8a99, 0x8b6b, 0x8b93, 0x8b94, 0x8ba9,
  /* 0x60 */
  0x8ba0, 0x8ba6, 0xca61, 0x8bab, 0x8b9e, 0x8b9b, 0x8b91, 0x8b99,
  0x8cb6, 0x8cb8, 0x8c9a, 0x0e91, 0x8c98, 0x8c9b, 0x8cb3, 0x8ca2,
  0x54aa, 0x8ca0, 0x8c9f, 0x8e5b, 0x8e70, 0x8e54, 0x8e71, 0x8e65,
  0x8e51, 0x8e9d, 0x8e61, 0x8e5a, 0x8e74, 0x8e4c, 0x8e4b, 0x8e5e,
  0x8e58, 0x8e53, 0x8e52, 0x8f3b, 0x8f39, 0x8fd6, 0x8fe7, 0x8fd7,
  0x8fd8, 0x8fd9, 0x8fda, 0x8fdb, 0x8fdc, 0x8fe0, 0x8fe4, 0x8fdd,
  0x8ff5, 0x8ff1, 0x9098, 0x909d, 0x9099, 0x9150, 0x9149, 0x27e4,
  0x9162, 0x91d7, 0x9201, 0x91f7, 0xca7d, 0x928c, 0x929c, 0x2888,
  0x931f, 0x931e, 0x943d, 0x943f, 0x9411, 0x9459, 0x943e, 0x9458,
  0x9500, 0x949e, 0x94b6, 0x94aa, 0x94af, 0x94ac, 0x1030, 0x94c0,
  0x94a9, 0x3e10, 0x95ee, 0x9677, 0x9679, 0x967a, 0x967d, 0x967f,
  0x9683, 0x9678, 0x967e, 0x96e4, 0x96e6, 0x96e5,
  /* 0x61 */
  0x105d, 0x974e, 0x9759, 0x1075, 0xca8e, 0x974f, 0x974a, 0x97e3,
  0x97de, 0x97e2, 0x9974, 0x99ac, 0x9961, 0x9962, 0x9976, 0x997a,
  0x9979, 0x9960, 0x9a64, 0x9b81, 0x9adf, 0x9a84, 0x9a8a, 0x9a92,
  0x9a79, 0x9ade, 0x9a98, 0x9a6c, 0x9ae1, 0xcaa5, 0x9a7c, 0x9a72,
  0x9a81, 0x9ae0, 0x9a65, 0x10ff, 0x9a6a, 0x9a97, 0x9aaa, 0x9ad3,
  0x9aab, 0x9a6e, 0x9aac, 0x9a76, 0x9a7b, 0x9aad, 0xcaa6, 0x9a94,
  0x9ad7, 0x9a70, 0x9ad5, 0x9af1, 0x9a7a, 0x9a68, 0x9a96, 0x110b,
  0x9a73, 0x9aae, 0x9add, 0x9ada, 0x9aaf, 0x9ab0, 0x9adb, 0x9a62,
  0x9af8, 0x9cc2, 0x9cc7, 0x9cc8, 0x9cc5, 0x9cc3, 0x9cc6, 0x9dde,
  0x11c8, 0x9e11, 0x9e15, 0x9e28, 0x9e21, 0x9e2d, 0x9e51, 0x9e2b,
  0x9e16, 0x9e24, 0x9e35, 0x9e1f, 0x9e12, 0x9e10, 0x9e80, 0x9e3b,
  0x9e29, 0x9e2a, 0x9e1b, 0x9e18, 0x9e20, 0x9e3f,
  /* 0x62 */
  0x9e1c, 0x9e26, 0x9e0b, 0x9fbe, 0x9fc4, 0x9fbd, 0x9ffa, 0x9ffb,
  0xa0b1, 0xa0b2, 0xa0b0, 0xa0b9, 0xa0a6, 0xa0bd, 0xa0b6, 0xa0b8,
  0xa0b4, 0xa0b3, 0xa0a7, 0xa0ae, 0xa0bc, 0xa1f2, 0xa1f3, 0xa1f4,
  0xa23b, 0xa240, 0xa246, 0xa2f0, 0xa2ee, 0xa2e8, 0xa2f1, 0xa2eb,
  0xa2ef, 0xa3fc, 0xa420, 0xa409, 0xa406, 0xa403, 0xcacf, 0xa419,
  0xa424, 0xa41b, 0xa41d, 0xa3fd, 0xa41e, 0xa3f4, 0xa401, 0xa408,
  0xa405, 0xa423, 0xa3ff, 0xa5ea, 0xa64d, 0xa64e, 0xa656, 0xa657,
  0xa651, 0xa655, 0xa654, 0xa6cb, 0xa6d4, 0xa6d1, 0xa6cf, 0xa6d2,
  0xa6ca, 0xa6d6, 0xa78b, 0xa788, 0xa785, 0xa789, 0x4c9b, 0xa7bb,
  0xa78c, 0x1374, 0xa799, 0xa78a, 0xa8ec, 0xa8ef, 0xa8f9, 0xa909,
  0xa8f8, 0xa8f3, 0xa900, 0xa91d, 0x13ac, 0xa8fd, 0xaa48, 0xaa5c,
  0xaa55, 0xaa5e, 0xaa49, 0xaa63, 0xaa60, 0xaa53,
  /* 0x63 */
  0xaa62, 0xaa40, 0xab49, 0xab4a, 0xab4c, 0xab4d, 0xac0a, 0xac06,
  0xac2f, 0xac21, 0xac07, 0xac09, 0xac02, 0xac16, 0xac03, 0xac0b,
  0xac0f, 0xae60, 0xae68, 0xae5e, 0xae5d, 0xae63, 0xae5f, 0xae64,
  0xae78, 0xae61, 0xae69, 0xae65, 0xafda, 0xafe6, 0xafdb, 0xafdc,
  0xb039, 0xb057, 0xb055, 0xb065, 0xb061, 0xb054, 0xb145, 0xb141,
  0xb13e, 0xb137, 0xb212, 0xb213, 0xb22c, 0xb296, 0xcae9, 0xb29c,
  0xb29d, 0xb285, 0xcae8, 0xb29f, 0xb2a3, 0xb382, 0xb383, 0xcaee,
  0xb41d, 0xb414, 0xb41f, 0xb420, 0xb547, 0xb580, 0xb5c9, 0xb5f9,
  0xb606, 0xb5f0, 0xb5f8, 0xb5ef, 0xb5fd, 0xb5f1, 0xb5fe, 0xb6b8,
  0xb6c0, 0xb6c3, 0xb6b5, 0xb6b6, 0xb6c9, 0xcaf3, 0xb6bd, 0xb6ba,
  0xb6bf, 0xb6b3, 0xb6c6, 0xb6b2, 0xb6bc, 0xb6b7, 0xb6b9, 0xb6c8,
  0xb7b5, 0xb7b3, 0x1616, 0xb7ac, 0xb7a9, 0xb7ad,
  /* 0x64 */
  0xb911, 0xb90d, 0xb916, 0xb989, 0xb97c, 0xb98b, 0xb97b, 0xb988,
  0xb984, 0xba9d, 0xba98, 0xbb88, 0xbb86, 0xbb82, 0xbb8b, 0xcaff,
  0xbb71, 0xbb72, 0xbb81, 0xbb8c, 0xbb80, 0xbb89, 0xbcbb, 0xbcc1,
  0xbcbe, 0xcb01, 0xbcbd, 0xbdc1, 0xbdb4, 0xbdb7, 0xbdc8, 0x173b,
  0xbdd3, 0xbdd0, 0xbdb0, 0xbdca, 0xbdcd, 0xbe15, 0xbe19, 0xbe17,
  0xbe3f, 0xbe40, 0xbe44, 0xbe7c, 0xbe78, 0xbe79, 0xbe88, 0xcb06,
  0xbe89, 0xbe7d, 0xbf23, 0xbf24, 0xbf26, 0xbf22, 0xbf27, 0xbf1f,
  0xbfc9, 0xbfc3, 0xc00a, 0xc00b, 0xc004, 0x17ef, 0xc003, 0xc001,
  0xc009, 0xc10f, 0xc12e, 0xc12d, 0xc191, 0xc199, 0xc19e, 0xc190,
  0xc194, 0xc19d, 0xc198, 0xc19b, 0xc19c, 0xc19a, 0xc254, 0xc39d,
  0xc39f, 0xc3a3, 0xc3a4, 0xc3a5, 0xc602, 0xc717, 0xc71b, 0xc719,
  0xc7d1, 0x4737,
};

static const ucs4_t cns11643_6_2uni_upages[204] = {
  0x03400, 0x03500, 0x03600, 0x03700, 0x03800, 0x03900, 0x03a00, 0x03b00,
  0x03c00, 0x03d00, 0x03e00, 0x03f00, 0x04000, 0x04100, 0x04200, 0x04300,
  0x04400, 0x04500, 0x04600, 0x04700, 0x04800, 0x04900, 0x04a00, 0x04b00,
  0x05100, 0x05300, 0x05500, 0x05a00, 0x05b00, 0x05d00, 0x05e00, 0x06100,
  0x06500, 0x06800, 0x06e00, 0x07200, 0x07300, 0x07800, 0x07a00, 0x07f00,
  0x08000, 0x08100, 0x08200, 0x08800, 0x08900, 0x08a00, 0x08d00, 0x08f00,
  0x09200, 0x09700, 0x0ff00, 0x20000, 0x20100, 0x20200, 0x20300, 0x20400,
  0x20500, 0x20600, 0x20700, 0x20800, 0x20900, 0x20a00, 0x20b00, 0x20c00,
  0x20d00, 0x20e00, 0x20f00, 0x21100, 0x21200, 0x21300, 0x21400, 0x21500,
  0x21600, 0x21700, 0x21800, 0x21900, 0x21a00, 0x21b00, 0x21c00, 0x21d00,
  0x21e00, 0x21f00, 0x22000, 0x22100, 0x22200, 0x22300, 0x22400, 0x22500,
  0x22600, 0x22700, 0x22800, 0x22900, 0x22a00, 0x22b00, 0x22c00, 0x22d00,
  0x22e00, 0x22f00, 0x23000, 0x23100, 0x23200, 0x23300, 0x23400, 0x23500,
  0x23600, 0x23800, 0x23900, 0x23a00, 0x23b00, 0x23c00, 0x23d00, 0x23e00,
  0x23f00, 0x24100, 0x24200, 0x24300, 0x24400, 0x24500, 0x24600, 0x24700,
  0x24800, 0x24900, 0x24a00, 0x24b00, 0x24c00, 0x24d00, 0x24e00, 0x24f00,
  0x25000, 0x25100, 0x25200, 0x25300, 0x25400, 0x25500, 0x25600, 0x25700,
  0x25800, 0x25900, 0x25a00, 0x25b00, 0x25e00, 0x25f00, 0x26000, 0x26200,
  0x26300, 0x26400, 0x26500, 0x26600, 0x26700, 0x26800, 0x26900, 0x26a00,
  0x26b00, 0x26c00, 0x26d00, 0x26e00, 0x27100, 0x27200, 0x27300, 0x27500,
  0x27600, 0x27700, 0x27800, 0x27900, 0x27a00, 0x27b00, 0x27c00, 0x27d00,
  0x27e00, 0x27f00, 0x28000, 0x28200, 0x28300, 0x28400, 0x28500, 0x28600,
  0x28700, 0x28800, 0x28900, 0x28c00, 0x28d00, 0x28e00, 0x28f00, 0x29000,
  0x29100, 0x29200, 0x29300, 0x29400, 0x29500, 0x29600, 0x29800, 0x29a00,
  0x29b00, 0x29c00, 0x29d00, 0x29f00, 0x2a000, 0x2a200, 0x2a300, 0x2a400,
  0x2a500, 0x2f800, 0x2f900, 0x2fa00,
};

static int
cns11643_6_mbtowc (conv_t conv, ucs4_t *pwc, const unsigned char *s, int n)
{
  unsigned char c1 = s[0];
  if ((c1 >= 0x21 && c1 <= 0x64)) {
    if (n >= 2) {
      unsigned char c2 = s[1];
      if (c2 >= 0x21 && c2 < 0x7f) {
        unsigned int i = 94 * (c1 - 0x21) + (c2 - 0x21);
        ucs4_t wc = 0xfffd;
        unsigned short swc;
        {
          if (i < 6388)
            swc = cns11643_6_2uni_page21[i],
            wc = cns11643_6_2uni_upages[swc>>8] | (swc & 0xff);
        }
        if (wc != 0xfffd) {
          *pwc = wc;
          return 2;
        }
      }
      return RET_ILSEQ;
    }
    return RET_TOOFEW(0);
  }
  return RET_ILSEQ;
}

