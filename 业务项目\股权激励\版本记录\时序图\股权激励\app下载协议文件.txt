title App获取员工协议文件时序图

participant App
participant 交易中台系统
participant 共享数据库
participant 股权激励系统

group 发起协议签署
共享数据库<-股权激励系统:发起协议签署,将协议信息(协议id,员工id,员工姓名等信息)写入共享数据库
App->交易中台系统:查询员工协议信息
交易中台系统->共享数据库:从共享数据库查询协议信息
交易中台系统<-共享数据库:返回协议信息(协议id,员工id,员工姓名等信息)
App<-交易中台系统:返回协议信息

end
group 获取文件下载授权码
App->交易中台系统:根据员工id,协议id,请求头appId获取文件下载授权码


交易中台系统->交易中台系统:使用国密算法加密生成授权码
App<-交易中台系统:返回授权码
end

group 根据文件授权码,获取协议文件
group 外网接口
App->股权激励系统:在请求头设置文件下载授权码,请求下载文件协议接口
activate App

activate 股权激励系统
end
股权激励系统->股权激励系统:使用国密算法解密授权码,获取员工id,协议id,时间信息
股权激励系统->股权激励系统:判断授权码是否在有效期
alt 授权码解密失败
App<-股权激励系统:返回授权码无效
else 授权码过期
App<-股权激励系统:返回授权码过期
else 授权码有效
股权激励系统->股权激励系统:获取文件信息并从文件服务器获取文件流
alt 股权激励从文件服务器获取文件失败
App<-股权激励系统:返回服务器获取文件异常
else 股权激励从文件服务器获取文件成功
App<-股权激励系统:返回协议文件流
deactivate 股权激励系统

deactivate App
end
end
end