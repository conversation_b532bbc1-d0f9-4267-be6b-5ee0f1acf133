-- 歌尔股份发布APP
UPDATE INCENT_OPTION_PLANS SET SYNCTRADEDB=1 WHERE COMPANYID='6af00c95d4b34f5183be5365b23daea4';
--- 新劲刚
UPDATE INCENT_OPTION_PLANS SET SYNCTRADEDB=1 WHERE COMPANYID='060dfaaaa17449b3950d415802d75329';
---- 中兴
UPDATE INCENT_OPTION_PLANS SET SYNCTRADEDB=1 WHERE COMPANYID='c6d4476c52d642e4ad7e0b5e82aa5b46';
---- 德业股份
UPDATE INCENT_OPTION_PLANS SET SYNCTRADEDB=1 WHERE COMPANYID='06cd9cbf33cd459fa499d3448c633837';
UPDATE INCENT_SHAREHOLDING_PLANS SET SYNCTRADEDB=1 WHERE COMPANYID='06cd9cbf33cd459fa499d3448c633837';
UPDATE INCENT_STOCK_PLANS SET SYNCTRADEDB=1 WHERE COMPANYID='06cd9cbf33cd459fa499d3448c633837';
---- wjk非上市
UPDATE INCENT_OPTION_PLANS SET SYNCTRADEDB=1 WHERE COMPANYID='c1972eb5af2443caa8597a8877eba461';
UPDATE INCENT_SHAREHOLDING_PLANS SET SYNCTRADEDB=1 WHERE COMPANYID='c1972eb5af2443caa8597a8877eba461';
UPDATE INCENT_STOCK_PLANS SET SYNCTRADEDB=1 WHERE COMPANYID='c1972eb5af2443caa8597a8877eba461';

-----查询真实行权扣税
SELECT L.ORDERSNO ,L.ORDERDATE,L.FUNDID,L.ORDERID FROM LOGASSET L WHERE L.ORDERID = #{orderId}
        AND L.ORDERDATE = #{orderDate}
        AND L.FUNDID = #{fundId};

-- GRANT_IMPORT_RECORD
-- INCENT_OPTION_PLANS
-- WX_LOGIN_INFO

SELECT count(1) FROM LCIMS.EI_UNLOCK_ONE_STK;
SELECT count(1) FROM LCIMS.GRANT_IMPORT_RECORD;
SELECT count(1) FROM LCIMS.INCENT_OPTION_PLANS;
SELECT count(1) FROM LCIMS.WX_LOGIN_INFO;

delete from GRANT_IMPORT_RECORD where 1=1;





