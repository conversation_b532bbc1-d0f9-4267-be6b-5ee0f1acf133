/*
THIS IS A GENERATED/BUNDLED FILE BY ESBUILD
if you want to view the source, please visit the github repository of this plugin
*/

function __extractDefault(module2) {
    return module2 && module2.__esModule && module2.default ? module2.default : module2;
  }

(function patchRequireEsmDefault() {
    const __require = require;
    require = Object.assign((id) => {
      const module2 = __require(id) ?? {};
      return __extractDefault(module2);
    }, __require);
  })()

"use strict";var Rh=Object.create;var xr=Object.defineProperty;var Dh=Object.getOwnPropertyDescriptor;var Mh=Object.getOwnPropertyNames;var jh=Object.getPrototypeOf,Bh=Object.prototype.hasOwnProperty;var b=(e,t)=>()=>(e&&(t=e(e=0)),t);var N=(e,t)=>()=>(t||e((t={exports:{}}).exports,t),t.exports),_r=(e,t)=>{for(var r in t)xr(e,r,{get:t[r],enumerable:!0})},ja=(e,t,r,n)=>{if(t&&typeof t=="object"||typeof t=="function")for(let i of Mh(t))!Bh.call(e,i)&&i!==r&&xr(e,i,{get:()=>t[i],enumerable:!(n=Dh(t,i))||n.enumerable});return e};var H=(e,t,r)=>(r=e!=null?Rh(jh(e)):{},ja(t||!e||!e.__esModule?xr(r,"default",{value:e,enumerable:!0}):r,e)),Vn=e=>ja(xr({},"__esModule",{value:!0}),e);var ot=N((sC,za)=>{function zh(e){return e&&e.__esModule&&e.default?e.default:e}(function(){let t=require;require=Object.assign(r=>{let n=t(r)??{};return zh(n)},t)})();var Wn=Object.defineProperty,Vh=Object.getOwnPropertyDescriptor,Wh=Object.getOwnPropertyNames,Uh=Object.prototype.hasOwnProperty,Hh=(e,t)=>{for(var r in t)Wn(e,r,{get:t[r],enumerable:!0})},$h=(e,t,r,n)=>{if(t&&typeof t=="object"||typeof t=="function")for(let i of Wh(t))!Uh.call(e,i)&&i!==r&&Wn(e,i,{get:()=>t[i],enumerable:!(n=Vh(t,i))||n.enumerable});return e},Gh=e=>$h(Wn({},"__esModule",{value:!0}),e),Ba={};Hh(Ba,{noop:()=>Jh,noopAsync:()=>Qh,omitAsyncReturnType:()=>Yh,omitReturnType:()=>Kh});za.exports=Gh(Ba);function Jh(){}async function Qh(){}function Yh(e){return async(...t)=>{await e(...t)}}function Kh(e){return(...t)=>{e(...t)}}});var Ct=N((lC,Ha)=>{"use strict";var Un=Object.defineProperty,Xh=Object.getOwnPropertyDescriptor,Zh=Object.getOwnPropertyNames,ed=Object.prototype.hasOwnProperty,td=(e,t)=>{for(var r in t)Un(e,r,{get:t[r],enumerable:!0})},rd=(e,t,r,n)=>{if(t&&typeof t=="object"||typeof t=="function")for(let i of Zh(t))!ed.call(e,i)&&i!==r&&Un(e,i,{get:()=>t[i],enumerable:!(n=Xh(t,i))||n.enumerable});return e},nd=e=>rd(Un({},"__esModule",{value:!0}),e),Va={};td(Va,{CustomArrayDictImpl:()=>dd,FileExtension:()=>cd,InternalPluginName:()=>He,ViewType:()=>J,createTFileInstance:()=>md,createTFolderInstance:()=>Gn,getAllPropertiesViewConstructor:()=>gd,getAppConstructor:()=>od,getAudioViewConstructor:()=>Ed,getBacklinkViewConstructor:()=>Dd,getBookmarksViewConstructor:()=>Pd,getBrowserHistoryViewConstructor:()=>qd,getBrowserViewConstructor:()=>vd,getCanvasViewConstructor:()=>wd,getEmptyViewConstructor:()=>Ad,getFileExplorerViewConstructor:()=>kd,getFilePropertiesViewConstructor:()=>Cd,getGraphViewConstructor:()=>Ld,getImageViewConstructor:()=>Id,getInternalPluginConstructor:()=>ud,getInternalPluginsConstructor:()=>sd,getLocalGraphViewConstructor:()=>Td,getMarkdownViewConstructor:()=>bd,getOutgoingLinkViewConstructor:()=>yd,getOutlineViewConstructor:()=>Sd,getPdfViewConstructor:()=>xd,getReleaseNotesViewConstructor:()=>Rd,getSearchViewConstructor:()=>_d,getSyncViewConstructor:()=>Nd,getTFileConstructor:()=>Ua,getTFolderConstructor:()=>Wa,getTagViewConstructor:()=>Fd,getVideoViewConstructor:()=>Od,getViewConstructorByViewType:()=>Q,isEmbedCache:()=>pd,isFrontmatterLinkCache:()=>hd,isLinkCache:()=>fd,isReferenceCache:()=>Hn,parentFolderPath:()=>$n});Ha.exports=nd(Va);var id=require("obsidian");function od(){return id.App}var ad=require("obsidian");function Wa(){return ad.TFolder}function sd(e){return e.internalPlugins.constructor}var ld=require("obsidian");function Ua(){return ld.TFile}function ud(e){let t=Object.values(e.internalPlugins.plugins)[0];if(!t)throw new Error("No internal plugin found");return t.constructor}var He={AudioRecorder:"audio-recorder",Backlink:"backlink",Bookmarks:"bookmarks",Browser:"browser",Canvas:"canvas",CommandPalette:"command-palette",DailyNotes:"daily-notes",EditorStatus:"editor-status",FileExplorer:"file-explorer",FileRecovery:"file-recovery",GlobalSearch:"global-search",Graph:"graph",MarkdownImporter:"markdown-importer",NoteComposer:"note-composer",OutgoingLink:"outgoing-link",Outline:"outline",PagePreview:"page-preview",Properties:"properties",Publish:"publish",RandomNote:"random-note",SlashCommand:"slash-command",Slides:"slides",Switcher:"switcher",Sync:"sync",TagPane:"tag-pane",Templates:"templates",WordCount:"word-count",Workspaces:"workspaces",ZkPrefixer:"zk-prefixer"},cd={_3gp:"3gp",avif:"avif",bmp:"bmp",canvas:"canvas",flac:"flac",gif:"gif",jpeg:"jpeg",jpg:"jpg",m4a:"m4a",md:"md",mkv:"mkv",mov:"mov",mp3:"mp3",mp4:"mp4",oga:"oga",ogg:"ogg",ogv:"ogv",opus:"opus",pdf:"pdf",png:"png",svg:"svg",wav:"wav",webm:"webm",webp:"webp"},J={AllProperties:"all-properties",Audio:"audio",Backlink:He.Backlink,Bookmarks:He.Bookmarks,Browser:"browser",BrowserHistory:"browser-history",Canvas:He.Canvas,Empty:"empty",FileExplorer:He.FileExplorer,FileProperties:"file-properties",Graph:He.Graph,Image:"image",LocalGraph:"localgraph",Markdown:"markdown",OutgoingLink:He.OutgoingLink,Outline:He.Outline,Pdf:"pdf",ReleaseNotes:"release-notes",Search:"search",Sync:"sync",Tag:"tag",Video:"video"};function Hn(e){return!!e.position}function fd(e){return Hn(e)&&e.original[0]!=="!"}function pd(e){return Hn(e)&&e.original[0]==="!"}function hd(e){return!!e.key}var dd=class{data=new Map;add(e,t){let r=this.get(e);r||(r=[],this.data.set(e,r)),r.includes(t)||r.push(t)}remove(e,t){let r=this.get(e);r&&(r.remove(t),r.length===0&&this.clear(e))}get(e){return this.data.get(e)||null}keys(){return Array.from(this.data.keys())}clear(e){this.data.delete(e)}clearAll(){this.data.clear()}contains(e,t){return!!this.get(e)?.contains(t)}count(){let e=0;for(let t in this.keys())e+=this.get(t)?.length??0;return e}};function $n(e){return e.replace(/\/?[^\/]*$/,"")||"/"}function Gn(e,t){let r=e.vault.getFolderByPath(t);return r||(r=new(Wa())(e.vault,t),r.parent=Gn(e,$n(t)),r.deleted=!0,r)}function md(e,t){let r=e.vault.getFileByPath(t);return r||(r=new(Ua())(e.vault,t),r.parent=Gn(e,$n(t)),r.deleted=!0,r)}function Q(e,t){let r=e.workspace.createLeafInTabGroup();try{let n=e.viewRegistry.getViewCreatorByType(t);if(!n)throw new Error("View creator not found");return n(r).constructor}finally{r.detach()}}function gd(e){return Q(e,J.AllProperties)}function bd(e){return Q(e,J.Markdown)}function wd(e){return Q(e,J.Canvas)}function kd(e){return Q(e,J.FileExplorer)}function yd(e){return Q(e,J.OutgoingLink)}function vd(e){return Q(e,J.Browser)}function xd(e){return Q(e,J.Pdf)}function _d(e){return Q(e,J.Search)}function Cd(e){return Q(e,J.FileProperties)}function Pd(e){return Q(e,J.Bookmarks)}function Ad(e){return Q(e,J.Empty)}function Ed(e){return Q(e,J.Audio)}function Sd(e){return Q(e,J.Outline)}function Fd(e){return Q(e,J.Tag)}function Od(e){return Q(e,J.Video)}function Ld(e){return Q(e,J.Graph)}function Td(e){return Q(e,J.LocalGraph)}function Id(e){return Q(e,J.Image)}function qd(e){return Q(e,J.BrowserHistory)}function Nd(e){return Q(e,J.Sync)}function Rd(e){return Q(e,J.ReleaseNotes)}function Dd(e){return Q(e,J.Backlink)}});var Ja=N((cC,Ga)=>{var Md=globalThis.process??{browser:!0,cwd:()=>"/",env:{},platform:"android"};function Ie(e){if(typeof e!="string")throw new TypeError("Path must be a string. Received "+JSON.stringify(e))}function $a(e,t){for(var r="",n=0,i=-1,o=0,a,s=0;s<=e.length;++s){if(s<e.length)a=e.charCodeAt(s);else{if(a===47)break;a=47}if(a===47){if(!(i===s-1||o===1))if(i!==s-1&&o===2){if(r.length<2||n!==2||r.charCodeAt(r.length-1)!==46||r.charCodeAt(r.length-2)!==46){if(r.length>2){var l=r.lastIndexOf("/");if(l!==r.length-1){l===-1?(r="",n=0):(r=r.slice(0,l),n=r.length-1-r.lastIndexOf("/")),i=s,o=0;continue}}else if(r.length===2||r.length===1){r="",n=0,i=s,o=0;continue}}t&&(r.length>0?r+="/..":r="..",n=2)}else r.length>0?r+="/"+e.slice(i+1,s):r=e.slice(i+1,s),n=s-i-1;i=s,o=0}else a===46&&o!==-1?++o:o=-1}return r}function jd(e,t){var r=t.dir||t.root,n=t.base||(t.name||"")+(t.ext||"");return r?r===t.root?r+n:r+e+n:n}var Pt={resolve:function(){for(var t="",r=!1,n,i=arguments.length-1;i>=-1&&!r;i--){var o;i>=0?o=arguments[i]:(n===void 0&&(n=Md.cwd()),o=n),Ie(o),o.length!==0&&(t=o+"/"+t,r=o.charCodeAt(0)===47)}return t=$a(t,!r),r?t.length>0?"/"+t:"/":t.length>0?t:"."},normalize:function(t){if(Ie(t),t.length===0)return".";var r=t.charCodeAt(0)===47,n=t.charCodeAt(t.length-1)===47;return t=$a(t,!r),t.length===0&&!r&&(t="."),t.length>0&&n&&(t+="/"),r?"/"+t:t},isAbsolute:function(t){return Ie(t),t.length>0&&t.charCodeAt(0)===47},join:function(){if(arguments.length===0)return".";for(var t,r=0;r<arguments.length;++r){var n=arguments[r];Ie(n),n.length>0&&(t===void 0?t=n:t+="/"+n)}return t===void 0?".":Pt.normalize(t)},relative:function(t,r){if(Ie(t),Ie(r),t===r||(t=Pt.resolve(t),r=Pt.resolve(r),t===r))return"";for(var n=1;n<t.length&&t.charCodeAt(n)===47;++n);for(var i=t.length,o=i-n,a=1;a<r.length&&r.charCodeAt(a)===47;++a);for(var s=r.length,l=s-a,c=o<l?o:l,u=-1,f=0;f<=c;++f){if(f===c){if(l>c){if(r.charCodeAt(a+f)===47)return r.slice(a+f+1);if(f===0)return r.slice(a+f)}else o>c&&(t.charCodeAt(n+f)===47?u=f:f===0&&(u=0));break}var p=t.charCodeAt(n+f),h=r.charCodeAt(a+f);if(p!==h)break;p===47&&(u=f)}var w="";for(f=n+u+1;f<=i;++f)(f===i||t.charCodeAt(f)===47)&&(w.length===0?w+="..":w+="/..");return w.length>0?w+r.slice(a+u):(a+=u,r.charCodeAt(a)===47&&++a,r.slice(a))},_makeLong:function(t){return t},dirname:function(t){if(Ie(t),t.length===0)return".";for(var r=t.charCodeAt(0),n=r===47,i=-1,o=!0,a=t.length-1;a>=1;--a)if(r=t.charCodeAt(a),r===47){if(!o){i=a;break}}else o=!1;return i===-1?n?"/":".":n&&i===1?"//":t.slice(0,i)},basename:function(t,r){if(r!==void 0&&typeof r!="string")throw new TypeError('"ext" argument must be a string');Ie(t);var n=0,i=-1,o=!0,a;if(r!==void 0&&r.length>0&&r.length<=t.length){if(r.length===t.length&&r===t)return"";var s=r.length-1,l=-1;for(a=t.length-1;a>=0;--a){var c=t.charCodeAt(a);if(c===47){if(!o){n=a+1;break}}else l===-1&&(o=!1,l=a+1),s>=0&&(c===r.charCodeAt(s)?--s===-1&&(i=a):(s=-1,i=l))}return n===i?i=l:i===-1&&(i=t.length),t.slice(n,i)}else{for(a=t.length-1;a>=0;--a)if(t.charCodeAt(a)===47){if(!o){n=a+1;break}}else i===-1&&(o=!1,i=a+1);return i===-1?"":t.slice(n,i)}},extname:function(t){Ie(t);for(var r=-1,n=0,i=-1,o=!0,a=0,s=t.length-1;s>=0;--s){var l=t.charCodeAt(s);if(l===47){if(!o){n=s+1;break}continue}i===-1&&(o=!1,i=s+1),l===46?r===-1?r=s:a!==1&&(a=1):r!==-1&&(a=-1)}return r===-1||i===-1||a===0||a===1&&r===i-1&&r===n+1?"":t.slice(r,i)},format:function(t){if(t===null||typeof t!="object")throw new TypeError('The "pathObject" argument must be of type Object. Received type '+typeof t);return jd("/",t)},parse:function(t){Ie(t);var r={root:"",dir:"",base:"",ext:"",name:""};if(t.length===0)return r;var n=t.charCodeAt(0),i=n===47,o;i?(r.root="/",o=1):o=0;for(var a=-1,s=0,l=-1,c=!0,u=t.length-1,f=0;u>=o;--u){if(n=t.charCodeAt(u),n===47){if(!c){s=u+1;break}continue}l===-1&&(c=!1,l=u+1),n===46?a===-1?a=u:f!==1&&(f=1):a!==-1&&(f=-1)}return a===-1||l===-1||f===0||f===1&&a===l-1&&a===s+1?l!==-1&&(s===0&&i?r.base=r.name=t.slice(1,l):r.base=r.name=t.slice(s,l)):(s===0&&i?(r.name=t.slice(1,a),r.base=t.slice(1,l)):(r.name=t.slice(s,a),r.base=t.slice(s,l)),r.ext=t.slice(a,l)),s>0?r.dir=t.slice(0,s-1):i&&(r.dir="/"),r},sep:"/",delimiter:":",win32:null,posix:null};Pt.posix=Pt;Ga.exports=Pt});var Ya=N((fC,Jn)=>{"use strict";var Bd=Object.prototype.hasOwnProperty,fe="~";function Ut(){}Object.create&&(Ut.prototype=Object.create(null),new Ut().__proto__||(fe=!1));function zd(e,t,r){this.fn=e,this.context=t,this.once=r||!1}function Qa(e,t,r,n,i){if(typeof r!="function")throw new TypeError("The listener must be a function");var o=new zd(r,n||e,i),a=fe?fe+t:t;return e._events[a]?e._events[a].fn?e._events[a]=[e._events[a],o]:e._events[a].push(o):(e._events[a]=o,e._eventsCount++),e}function Cr(e,t){--e._eventsCount===0?e._events=new Ut:delete e._events[t]}function ae(){this._events=new Ut,this._eventsCount=0}ae.prototype.eventNames=function(){var t=[],r,n;if(this._eventsCount===0)return t;for(n in r=this._events)Bd.call(r,n)&&t.push(fe?n.slice(1):n);return Object.getOwnPropertySymbols?t.concat(Object.getOwnPropertySymbols(r)):t};ae.prototype.listeners=function(t){var r=fe?fe+t:t,n=this._events[r];if(!n)return[];if(n.fn)return[n.fn];for(var i=0,o=n.length,a=new Array(o);i<o;i++)a[i]=n[i].fn;return a};ae.prototype.listenerCount=function(t){var r=fe?fe+t:t,n=this._events[r];return n?n.fn?1:n.length:0};ae.prototype.emit=function(t,r,n,i,o,a){var s=fe?fe+t:t;if(!this._events[s])return!1;var l=this._events[s],c=arguments.length,u,f;if(l.fn){switch(l.once&&this.removeListener(t,l.fn,void 0,!0),c){case 1:return l.fn.call(l.context),!0;case 2:return l.fn.call(l.context,r),!0;case 3:return l.fn.call(l.context,r,n),!0;case 4:return l.fn.call(l.context,r,n,i),!0;case 5:return l.fn.call(l.context,r,n,i,o),!0;case 6:return l.fn.call(l.context,r,n,i,o,a),!0}for(f=1,u=new Array(c-1);f<c;f++)u[f-1]=arguments[f];l.fn.apply(l.context,u)}else{var p=l.length,h;for(f=0;f<p;f++)switch(l[f].once&&this.removeListener(t,l[f].fn,void 0,!0),c){case 1:l[f].fn.call(l[f].context);break;case 2:l[f].fn.call(l[f].context,r);break;case 3:l[f].fn.call(l[f].context,r,n);break;case 4:l[f].fn.call(l[f].context,r,n,i);break;default:if(!u)for(h=1,u=new Array(c-1);h<c;h++)u[h-1]=arguments[h];l[f].fn.apply(l[f].context,u)}}return!0};ae.prototype.on=function(t,r,n){return Qa(this,t,r,n,!1)};ae.prototype.once=function(t,r,n){return Qa(this,t,r,n,!0)};ae.prototype.removeListener=function(t,r,n,i){var o=fe?fe+t:t;if(!this._events[o])return this;if(!r)return Cr(this,o),this;var a=this._events[o];if(a.fn)a.fn===r&&(!i||a.once)&&(!n||a.context===n)&&Cr(this,o);else{for(var s=0,l=[],c=a.length;s<c;s++)(a[s].fn!==r||i&&!a[s].once||n&&a[s].context!==n)&&l.push(a[s]);l.length?this._events[o]=l.length===1?l[0]:l:Cr(this,o)}return this};ae.prototype.removeAllListeners=function(t){var r;return t?(r=fe?fe+t:t,this._events[r]&&Cr(this,r)):(this._events=new Ut,this._eventsCount=0),this};ae.prototype.off=ae.prototype.removeListener;ae.prototype.addListener=ae.prototype.on;ae.prefixed=fe;ae.EventEmitter=ae;typeof Jn<"u"&&(Jn.exports=ae)});var ze=N((pC,Za)=>{function Vd(e){return e&&e.__esModule&&e.default?e.default:e}(function(){let t=require;require=Object.assign(r=>{let n=t(r)??{};return Vd(n)},t)})();var Qn=Object.defineProperty,Wd=Object.getOwnPropertyDescriptor,Ud=Object.getOwnPropertyNames,Hd=Object.prototype.hasOwnProperty,$d=(e,t)=>{for(var r in t)Qn(e,r,{get:t[r],enumerable:!0})},Gd=(e,t,r,n)=>{if(t&&typeof t=="object"||typeof t=="function")for(let i of Ud(t))!Hd.call(e,i)&&i!==r&&Qn(e,i,{get:()=>t[i],enumerable:!(n=Wd(t,i))||n.enumerable});return e},Jd=e=>Gd(Qn({},"__esModule",{value:!0}),e),Ka={};$d(Ka,{emitAsyncErrorEvent:()=>Yd,errorToString:()=>Kd,getStackTrace:()=>Xd,printError:()=>Xa,registerAsyncErrorEventHandler:()=>Zd,throwExpression:()=>em});Za.exports=Jd(Ka);var Qd=Ya(),Pr="asyncError",Ar=new Qd.EventEmitter;Ar.on(Pr,tm);function Yd(e){Ar.emit(Pr,e)}function Kd(e){return Yn(e).map(t=>"  ".repeat(t.level)+t.message).join(`
`)}function Xd(e=0){return(new Error().stack??"").split(`
`).slice(e+2).join(`
`)}function Xa(e){let t=Yn(e);for(let r of t)r.shouldClearAnsiSequence?console.error(`\x1B[0m${r.message}\x1B[0m`):console.error(r.message)}function Zd(e){return Ar.on(Pr,e),()=>Ar.off(Pr,e)}function em(e){throw e}function tm(e){Xa(new Error("An unhandled error occurred executing async operation",{cause:e}))}function Yn(e,t=0,r=[]){if(e===void 0)return r;if(!(e instanceof Error)){let i="";return e===null?i="(null)":typeof e=="string"?i=e:i=JSON.stringify(e),r.push({level:t,message:i}),r}let n=`${e.name}: ${e.message}`;if(r.push({level:t,message:n,shouldClearAnsiSequence:!0}),e.stack){let i=e.stack.startsWith(n)?e.stack.slice(n.length+1):e.stack;r.push({level:t,message:`Error stack:
${i}`})}return e.cause!==void 0&&(r.push({level:t,message:"Caused by:"}),Yn(e.cause,t+1,r)),r}});var Er=N((hC,ts)=>{function rm(e){return e&&e.__esModule&&e.default?e.default:e}(function(){let t=require;require=Object.assign(r=>{let n=t(r)??{};return rm(n)},t)})();var Kn=Object.defineProperty,nm=Object.getOwnPropertyDescriptor,im=Object.getOwnPropertyNames,om=Object.prototype.hasOwnProperty,am=(e,t)=>{for(var r in t)Kn(e,r,{get:t[r],enumerable:!0})},sm=(e,t,r,n)=>{if(t&&typeof t=="object"||typeof t=="function")for(let i of im(t))!om.call(e,i)&&i!==r&&Kn(e,i,{get:()=>t[i],enumerable:!(n=nm(t,i))||n.enumerable});return e},lm=e=>sm(Kn({},"__esModule",{value:!0}),e),es={};am(es,{escapeRegExp:()=>um,isValidRegExp:()=>cm});ts.exports=lm(es);function um(e){return e.replaceAll(/[.*+?^${}()|[\]\\]/g,"\\$&")}function cm(e){try{return new RegExp(e),!0}catch{return!1}}});var Sr=N((dC,ns)=>{function fm(e){return e&&e.__esModule&&e.default?e.default:e}(function(){let t=require;require=Object.assign(r=>{let n=t(r)??{};return fm(n)},t)})();var Xn=Object.defineProperty,pm=Object.getOwnPropertyDescriptor,hm=Object.getOwnPropertyNames,dm=Object.prototype.hasOwnProperty,mm=(e,t)=>{for(var r in t)Xn(e,r,{get:t[r],enumerable:!0})},gm=(e,t,r,n)=>{if(t&&typeof t=="object"||typeof t=="function")for(let i of hm(t))!dm.call(e,i)&&i!==r&&Xn(e,i,{get:()=>t[i],enumerable:!(n=pm(t,i))||n.enumerable});return e},bm=e=>gm(Xn({},"__esModule",{value:!0}),e),rs={};mm(rs,{resolveValue:()=>wm});ns.exports=bm(rs);async function wm(e,...t){return km(e)?await e(...t):e}function km(e){return typeof e=="function"}});var At=N((mC,ls)=>{function ym(e){return e&&e.__esModule&&e.default?e.default:e}(function(){let t=require;require=Object.assign(r=>{let n=t(r)??{};return ym(n)},t)})();var Zn=Object.defineProperty,vm=Object.getOwnPropertyDescriptor,xm=Object.getOwnPropertyNames,_m=Object.prototype.hasOwnProperty,Cm=(e,t)=>{for(var r in t)Zn(e,r,{get:t[r],enumerable:!0})},Pm=(e,t,r,n)=>{if(t&&typeof t=="object"||typeof t=="function")for(let i of xm(t))!_m.call(e,i)&&i!==r&&Zn(e,i,{get:()=>t[i],enumerable:!(n=vm(t,i))||n.enumerable});return e},Am=e=>Pm(Zn({},"__esModule",{value:!0}),e),is={};Cm(is,{ensureEndsWith:()=>Fm,ensureStartsWith:()=>Om,escape:()=>Lm,insertAt:()=>Tm,makeValidVariableName:()=>Im,normalize:()=>qm,replace:()=>ei,replaceAllAsync:()=>Nm,trimEnd:()=>Rm,trimStart:()=>Dm,unescape:()=>Mm});ls.exports=Am(is);var os=ze(),Em=Er(),Sm=Sr(),as={"\n":"\\n","\r":"\\r","	":"\\t","\b":"\\b","\f":"\\f","'":"\\'",'"':'\\"',"\\":"\\\\"},ss={};for(let[e,t]of Object.entries(as))ss[t]=e;function Fm(e,t){return e.endsWith(t)?e:e+t}function Om(e,t){return e.startsWith(t)?e:t+e}function Lm(e){return ei(e,as)}function Tm(e,t,r,n){return n??=r,e.slice(0,r)+t+e.slice(n)}function Im(e){return e.replace(/[^a-zA-Z0-9_]/g,"_")}function qm(e){return e.replace(/\u00A0|\u202F/g," ").normalize("NFC")}function ei(e,t){let r=new RegExp(Object.keys(t).map(n=>(0,Em.escapeRegExp)(n)).join("|"),"g");return e.replaceAll(r,n=>t[n]??(0,os.throwExpression)(new Error(`Unexpected replacement source: ${n}`)))}async function Nm(e,t,r){let n=[];e.replaceAll(t,(o,...a)=>(n.push((0,Sm.resolveValue)(r,o,...a)),o));let i=await Promise.all(n);return e.replaceAll(t,()=>i.shift()??(0,os.throwExpression)(new Error("Unexpected empty replacement")))}function Rm(e,t,r){if(e.endsWith(t))return e.slice(0,-t.length);if(r)throw new Error(`String ${e} does not end with suffix ${t}`);return e}function Dm(e,t,r){if(e.startsWith(t))return e.slice(t.length);if(r)throw new Error(`String ${e} does not start with prefix ${t}`);return e}function Mm(e){return ei(e,ss)}});var Ve=N((gC,bs)=>{function us(e){return e&&e.__esModule&&e.default?e.default:e}(function(){let t=require;require=Object.assign(r=>{let n=t(r)??{};return us(n)},t)})();var jm=Object.create,Fr=Object.defineProperty,Bm=Object.getOwnPropertyDescriptor,zm=Object.getOwnPropertyNames,Vm=Object.getPrototypeOf,Wm=Object.prototype.hasOwnProperty,Um=(e,t)=>{for(var r in t)Fr(e,r,{get:t[r],enumerable:!0})},cs=(e,t,r,n)=>{if(t&&typeof t=="object"||typeof t=="function")for(let i of zm(t))!Wm.call(e,i)&&i!==r&&Fr(e,i,{get:()=>t[i],enumerable:!(n=Bm(t,i))||n.enumerable});return e},Hm=(e,t,r)=>(r=e!=null?jm(Vm(e)):{},cs(t||!e||!e.__esModule?Fr(r,"default",{value:e,enumerable:!0}):r,e)),$m=e=>cs(Fr({},"__esModule",{value:!0}),e),fs={};Um(fs,{basename:()=>Ym,delimiter:()=>Jm,dirname:()=>ds,extname:()=>Km,format:()=>Xm,getDirname:()=>ig,getFilename:()=>ms,isAbsolute:()=>Zm,join:()=>eg,makeFileName:()=>og,normalize:()=>tg,normalizeIfRelative:()=>ag,parse:()=>rg,posix:()=>Ae,relative:()=>ng,resolve:()=>gs,sep:()=>Qm,toPosixBuffer:()=>sg,toPosixPath:()=>ti});bs.exports=$m(fs);var ps=Hm(us(Ja()),1),Gm=At(),hs=/[a-zA-Z]:\/[^:]*$/,Ae=ps.default.posix,Jm=Ae.delimiter,Qm=ps.default.posix.sep,Ym=Ae.basename,ds=Ae.dirname,Km=Ae.extname,Xm=Ae.format;function Zm(e){return Ae.isAbsolute(e)||hs.exec(e)?.[0]===e}var eg=Ae.join,tg=Ae.normalize,rg=Ae.parse,ng=Ae.relative;function ig(e){return ds(ms(e))}function ms(e){return gs(new URL(e).pathname)}function og(e,t){return t?`${e}.${t}`:e}function ag(e){return e.startsWith("/")||e.includes(":")?e:(0,Gm.ensureStartsWith)(e,"./")}function gs(...e){let t=Ae.resolve(...e);return t=ti(t),hs.exec(t)?.[0]??t}function sg(e){return Buffer.from(ti(e.toString()))}function ti(e){return e.replace(/\\/g,"/")}});var Oe=N((bC,Es)=>{function lg(e){return e&&e.__esModule&&e.default?e.default:e}(function(){let t=require;require=Object.assign(r=>{let n=t(r)??{};return lg(n)},t)})();var ri=Object.defineProperty,ug=Object.getOwnPropertyDescriptor,cg=Object.getOwnPropertyNames,fg=Object.prototype.hasOwnProperty,pg=(e,t)=>{for(var r in t)ri(e,r,{get:t[r],enumerable:!0})},hg=(e,t,r,n)=>{if(t&&typeof t=="object"||typeof t=="function")for(let i of cg(t))!fg.call(e,i)&&i!==r&&ri(e,i,{get:()=>t[i],enumerable:!(n=ug(t,i))||n.enumerable});return e},dg=e=>hg(ri({},"__esModule",{value:!0}),e),ks={};pg(ks,{CANVAS_FILE_EXTENSION:()=>vs,MARKDOWN_FILE_EXTENSION:()=>ii,checkExtension:()=>oi,getAbstractFile:()=>gg,getAbstractFileOrNull:()=>Gt,getFile:()=>bg,getFileOrNull:()=>Or,getFolder:()=>xs,getFolderOrNull:()=>ai,getMarkdownFiles:()=>wg,getOrCreateFile:()=>kg,getOrCreateFolder:()=>_s,getPath:()=>yg,isAbstractFile:()=>si,isCanvasFile:()=>Cs,isFile:()=>li,isFolder:()=>Ps,isMarkdownFile:()=>Ht,isNote:()=>vg,trimMarkdownExtension:()=>xg});Es.exports=dg(ks);var $t=require("obsidian"),ni=Ct(),ys=Ve(),mg=At(),ii="md",vs="canvas";function oi(e,t,r){if(li(t))return t.extension===r;if(typeof t=="string"){let n=Or(e,t);return n?n.extension===r:(0,ys.extname)(t).slice(1)===r}return!1}function gg(e,t,r){let n=Gt(e,t,r);if(!n)throw new Error(`Abstract file not found: ${t}`);return n}function Gt(e,t,r){if(t===null)return null;if(si(t))return t;let n=ws(e,t,r);if(n)return n;let i=As(t);return i===t?null:ws(e,i,r)}function bg(e,t,r,n){let i=Or(e,t,n);if(!i)if(r)i=(0,ni.createTFileInstance)(e,t);else throw new Error(`File not found: ${t}`);return i}function Or(e,t,r){let n=Gt(e,t,r);return li(n)?n:null}function xs(e,t,r,n){let i=ai(e,t,n);if(!i)if(r)i=(0,ni.createTFolderInstance)(e,t);else throw new Error(`Folder not found: ${t}`);return i}function ai(e,t,r){let n=Gt(e,t,r);return Ps(n)?n:null}function wg(e,t,r){let n=xs(e,t),i=[];return r?$t.Vault.recurseChildren(n,o=>{Ht(e,o)&&i.push(o)}):i=n.children.filter(o=>Ht(e,o)),i=i.sort((o,a)=>o.path.localeCompare(a.path)),i}async function kg(e,t){let r=Or(e,t);if(r)return r;let n=(0,ni.parentFolderPath)(t);return await _s(e,n),await e.vault.create(t,"")}async function _s(e,t){let r=ai(e,t);return r||await e.vault.createFolder(t)}function yg(e,t){if(si(t))return t.path;let r=Gt(e,t);return r?r.path:As(t)}function si(e){return e instanceof $t.TAbstractFile}function Cs(e,t){return oi(e,t,vs)}function li(e){return e instanceof $t.TFile}function Ps(e){return e instanceof $t.TFolder}function Ht(e,t){return oi(e,t,ii)}function vg(e,t){return Ht(e,t)||Cs(e,t)}function xg(e,t){return Ht(e,t)?(0,mg.trimEnd)(t.path,"."+ii):t.path}function ws(e,t,r){return r?e.vault.getAbstractFileByPathInsensitive(t):e.vault.getAbstractFileByPath(t)}function As(e){return(0,$t.normalizePath)((0,ys.resolve)("/",e))}});var Fs=N((wC,Ss)=>{var Et=1e3,St=Et*60,Ft=St*60,at=Ft*24,_g=at*7,Cg=at*365.25;Ss.exports=function(e,t){t=t||{};var r=typeof e;if(r==="string"&&e.length>0)return Pg(e);if(r==="number"&&isFinite(e))return t.long?Eg(e):Ag(e);throw new Error("val is not a non-empty string or a valid number. val="+JSON.stringify(e))};function Pg(e){if(e=String(e),!(e.length>100)){var t=/^(-?(?:\d+)?\.?\d+) *(milliseconds?|msecs?|ms|seconds?|secs?|s|minutes?|mins?|m|hours?|hrs?|h|days?|d|weeks?|w|years?|yrs?|y)?$/i.exec(e);if(t){var r=parseFloat(t[1]),n=(t[2]||"ms").toLowerCase();switch(n){case"years":case"year":case"yrs":case"yr":case"y":return r*Cg;case"weeks":case"week":case"w":return r*_g;case"days":case"day":case"d":return r*at;case"hours":case"hour":case"hrs":case"hr":case"h":return r*Ft;case"minutes":case"minute":case"mins":case"min":case"m":return r*St;case"seconds":case"second":case"secs":case"sec":case"s":return r*Et;case"milliseconds":case"millisecond":case"msecs":case"msec":case"ms":return r;default:return}}}}function Ag(e){var t=Math.abs(e);return t>=at?Math.round(e/at)+"d":t>=Ft?Math.round(e/Ft)+"h":t>=St?Math.round(e/St)+"m":t>=Et?Math.round(e/Et)+"s":e+"ms"}function Eg(e){var t=Math.abs(e);return t>=at?Lr(e,t,at,"day"):t>=Ft?Lr(e,t,Ft,"hour"):t>=St?Lr(e,t,St,"minute"):t>=Et?Lr(e,t,Et,"second"):e+" ms"}function Lr(e,t,r,n){var i=t>=r*1.5;return Math.round(e/r)+" "+n+(i?"s":"")}});var ui=N((kC,Os)=>{function Sg(e){r.debug=r,r.default=r,r.coerce=l,r.disable=a,r.enable=i,r.enabled=s,r.humanize=Fs(),r.destroy=c,Object.keys(e).forEach(u=>{r[u]=e[u]}),r.names=[],r.skips=[],r.formatters={};function t(u){let f=0;for(let p=0;p<u.length;p++)f=(f<<5)-f+u.charCodeAt(p),f|=0;return r.colors[Math.abs(f)%r.colors.length]}r.selectColor=t;function r(u){let f,p=null,h,w;function g(...x){if(!g.enabled)return;let k=g,F=Number(new Date),_=F-(f||F);k.diff=_,k.prev=f,k.curr=F,f=F,x[0]=r.coerce(x[0]),typeof x[0]!="string"&&x.unshift("%O");let V=0;x[0]=x[0].replace(/%([a-zA-Z%])/g,(v,q)=>{if(v==="%%")return"%";V++;let D=r.formatters[q];if(typeof D=="function"){let O=x[V];v=D.call(k,O),x.splice(V,1),V--}return v}),r.formatArgs.call(k,x),(k.log||r.log).apply(k,x)}return g.namespace=u,g.useColors=r.useColors(),g.color=r.selectColor(u),g.extend=n,g.destroy=r.destroy,Object.defineProperty(g,"enabled",{enumerable:!0,configurable:!1,get:()=>p!==null?p:(h!==r.namespaces&&(h=r.namespaces,w=r.enabled(u)),w),set:x=>{p=x}}),typeof r.init=="function"&&r.init(g),g}function n(u,f){let p=r(this.namespace+(typeof f>"u"?":":f)+u);return p.log=this.log,p}function i(u){r.save(u),r.namespaces=u,r.names=[],r.skips=[];let f=(typeof u=="string"?u:"").trim().replace(" ",",").split(",").filter(Boolean);for(let p of f)p[0]==="-"?r.skips.push(p.slice(1)):r.names.push(p)}function o(u,f){let p=0,h=0,w=-1,g=0;for(;p<u.length;)if(h<f.length&&(f[h]===u[p]||f[h]==="*"))f[h]==="*"?(w=h,g=p,h++):(p++,h++);else if(w!==-1)h=w+1,g++,p=g;else return!1;for(;h<f.length&&f[h]==="*";)h++;return h===f.length}function a(){let u=[...r.names,...r.skips.map(f=>"-"+f)].join(",");return r.enable(""),u}function s(u){for(let f of r.skips)if(o(u,f))return!1;for(let f of r.names)if(o(u,f))return!0;return!1}function l(u){return u instanceof Error?u.stack||u.message:u}function c(){console.warn("Instance method `debug.destroy()` is deprecated and no longer does anything. It will be removed in the next major version of `debug`.")}return r.enable(r.load()),r}Os.exports=Sg});var Ls=N((ye,Tr)=>{var ci=globalThis.process??{browser:!0,cwd:()=>"/",env:{},platform:"android"};ye.formatArgs=Og;ye.save=Lg;ye.load=Tg;ye.useColors=Fg;ye.storage=Ig();ye.destroy=(()=>{let e=!1;return()=>{e||(e=!0,console.warn("Instance method `debug.destroy()` is deprecated and no longer does anything. It will be removed in the next major version of `debug`."))}})();ye.colors=["#0000CC","#0000FF","#0033CC","#0033FF","#0066CC","#0066FF","#0099CC","#0099FF","#00CC00","#00CC33","#00CC66","#00CC99","#00CCCC","#00CCFF","#3300CC","#3300FF","#3333CC","#3333FF","#3366CC","#3366FF","#3399CC","#3399FF","#33CC00","#33CC33","#33CC66","#33CC99","#33CCCC","#33CCFF","#6600CC","#6600FF","#6633CC","#6633FF","#66CC00","#66CC33","#9900CC","#9900FF","#9933CC","#9933FF","#99CC00","#99CC33","#CC0000","#CC0033","#CC0066","#CC0099","#CC00CC","#CC00FF","#CC3300","#CC3333","#CC3366","#CC3399","#CC33CC","#CC33FF","#CC6600","#CC6633","#CC9900","#CC9933","#CCCC00","#CCCC33","#FF0000","#FF0033","#FF0066","#FF0099","#FF00CC","#FF00FF","#FF3300","#FF3333","#FF3366","#FF3399","#FF33CC","#FF33FF","#FF6600","#FF6633","#FF9900","#FF9933","#FFCC00","#FFCC33"];function Fg(){if(typeof window<"u"&&window.process&&(window.process.type==="renderer"||window.process.__nwjs))return!0;if(typeof navigator<"u"&&navigator.userAgent&&navigator.userAgent.toLowerCase().match(/(edge|trident)\/(\d+)/))return!1;let e;return typeof document<"u"&&document.documentElement&&document.documentElement.style&&document.documentElement.style.WebkitAppearance||typeof window<"u"&&window.console&&(window.console.firebug||window.console.exception&&window.console.table)||typeof navigator<"u"&&navigator.userAgent&&(e=navigator.userAgent.toLowerCase().match(/firefox\/(\d+)/))&&parseInt(e[1],10)>=31||typeof navigator<"u"&&navigator.userAgent&&navigator.userAgent.toLowerCase().match(/applewebkit\/(\d+)/)}function Og(e){if(e[0]=(this.useColors?"%c":"")+this.namespace+(this.useColors?" %c":" ")+e[0]+(this.useColors?"%c ":" ")+"+"+Tr.exports.humanize(this.diff),!this.useColors)return;let t="color: "+this.color;e.splice(1,0,t,"color: inherit");let r=0,n=0;e[0].replace(/%[a-zA-Z%]/g,i=>{i!=="%%"&&(r++,i==="%c"&&(n=r))}),e.splice(n,0,t)}ye.log=console.debug||console.log||(()=>{});function Lg(e){try{e?ye.storage.setItem("debug",e):ye.storage.removeItem("debug")}catch{}}function Tg(){let e;try{e=ye.storage.getItem("debug")}catch{}return!e&&typeof ci<"u"&&"env"in ci&&(e=ci.env.DEBUG),e}function Ig(){try{return localStorage}catch{}}Tr.exports=ui()(ye);var{formatters:qg}=Tr.exports;qg.j=function(e){try{return JSON.stringify(e)}catch(t){return"[UnexpectedJSONParseError]: "+t.message}}});var Is=N((yC,Ts)=>{var Ng=globalThis.process??{browser:!0,cwd:()=>"/",env:{},platform:"android"};Ts.exports=(e,t=Ng.argv)=>{let r=e.startsWith("-")?"":e.length===1?"-":"--",n=t.indexOf(r+e),i=t.indexOf("--");return n!==-1&&(i===-1||n<i)}});var Ds=N((vC,Rs)=>{var Ns=globalThis.process??{browser:!0,cwd:()=>"/",env:{},platform:"android"},Rg=require("os"),qs=require("tty"),Ee=Is(),{env:ee}=Ns,$e;Ee("no-color")||Ee("no-colors")||Ee("color=false")||Ee("color=never")?$e=0:(Ee("color")||Ee("colors")||Ee("color=true")||Ee("color=always"))&&($e=1);"FORCE_COLOR"in ee&&(ee.FORCE_COLOR==="true"?$e=1:ee.FORCE_COLOR==="false"?$e=0:$e=ee.FORCE_COLOR.length===0?1:Math.min(parseInt(ee.FORCE_COLOR,10),3));function fi(e){return e===0?!1:{level:e,hasBasic:!0,has256:e>=2,has16m:e>=3}}function pi(e,t){if($e===0)return 0;if(Ee("color=16m")||Ee("color=full")||Ee("color=truecolor"))return 3;if(Ee("color=256"))return 2;if(e&&!t&&$e===void 0)return 0;let r=$e||0;if(ee.TERM==="dumb")return r;if(Ns.platform==="win32"){let n=Rg.release().split(".");return Number(n[0])>=10&&Number(n[2])>=10586?Number(n[2])>=14931?3:2:1}if("CI"in ee)return["TRAVIS","CIRCLECI","APPVEYOR","GITLAB_CI","GITHUB_ACTIONS","BUILDKITE"].some(n=>n in ee)||ee.CI_NAME==="codeship"?1:r;if("TEAMCITY_VERSION"in ee)return/^(9\.(0*[1-9]\d*)\.|\d{2,}\.)/.test(ee.TEAMCITY_VERSION)?1:0;if(ee.COLORTERM==="truecolor")return 3;if("TERM_PROGRAM"in ee){let n=parseInt((ee.TERM_PROGRAM_VERSION||"").split(".")[0],10);switch(ee.TERM_PROGRAM){case"iTerm.app":return n>=3?3:2;case"Apple_Terminal":return 2}}return/-256(color)?$/i.test(ee.TERM)?2:/^screen|^xterm|^vt100|^vt220|^rxvt|color|ansi|cygwin|linux/i.test(ee.TERM)||"COLORTERM"in ee?1:r}function Dg(e){let t=pi(e,e&&e.isTTY);return fi(t)}Rs.exports={supportsColor:Dg,stdout:fi(pi(!0,qs.isatty(1))),stderr:fi(pi(!0,qs.isatty(2)))}});var js=N((te,qr)=>{var st=globalThis.process??{browser:!0,cwd:()=>"/",env:{},platform:"android"},Mg=require("tty"),Ir=require("util");te.init=Hg;te.log=Vg;te.formatArgs=Bg;te.save=Wg;te.load=Ug;te.useColors=jg;te.destroy=Ir.deprecate(()=>{},"Instance method `debug.destroy()` is deprecated and no longer does anything. It will be removed in the next major version of `debug`.");te.colors=[6,2,3,4,5,1];try{let e=Ds();e&&(e.stderr||e).level>=2&&(te.colors=[20,21,26,27,32,33,38,39,40,41,42,43,44,45,56,57,62,63,68,69,74,75,76,77,78,79,80,81,92,93,98,99,112,113,128,129,134,135,148,149,160,161,162,163,164,165,166,167,168,169,170,171,172,173,178,179,184,185,196,197,198,199,200,201,202,203,204,205,206,207,208,209,214,215,220,221])}catch{}te.inspectOpts=Object.keys(st.env).filter(e=>/^debug_/i.test(e)).reduce((e,t)=>{let r=t.substring(6).toLowerCase().replace(/_([a-z])/g,(i,o)=>o.toUpperCase()),n=st.env[t];return/^(yes|on|true|enabled)$/i.test(n)?n=!0:/^(no|off|false|disabled)$/i.test(n)?n=!1:n==="null"?n=null:n=Number(n),e[r]=n,e},{});function jg(){return"colors"in te.inspectOpts?!!te.inspectOpts.colors:Mg.isatty(st.stderr.fd)}function Bg(e){let{namespace:t,useColors:r}=this;if(r){let n=this.color,i="\x1B[3"+(n<8?n:"8;5;"+n),o=`  ${i};1m${t} \x1B[0m`;e[0]=o+e[0].split(`
`).join(`
`+o),e.push(i+"m+"+qr.exports.humanize(this.diff)+"\x1B[0m")}else e[0]=zg()+t+" "+e[0]}function zg(){return te.inspectOpts.hideDate?"":new Date().toISOString()+" "}function Vg(...e){return st.stderr.write(Ir.formatWithOptions(te.inspectOpts,...e)+`
`)}function Wg(e){e?st.env.DEBUG=e:delete st.env.DEBUG}function Ug(){return st.env.DEBUG}function Hg(e){e.inspectOpts={};let t=Object.keys(te.inspectOpts);for(let r=0;r<t.length;r++)e.inspectOpts[t[r]]=te.inspectOpts[t[r]]}qr.exports=ui()(te);var{formatters:Ms}=qr.exports;Ms.o=function(e){return this.inspectOpts.colors=this.useColors,Ir.inspect(e,this.inspectOpts).split(`
`).map(t=>t.trim()).join(" ")};Ms.O=function(e){return this.inspectOpts.colors=this.useColors,Ir.inspect(e,this.inspectOpts)}});var di=N((xC,hi)=>{var Nr=globalThis.process??{browser:!0,cwd:()=>"/",env:{},platform:"android"};typeof Nr>"u"||Nr.type==="renderer"||Nr.browser===!0||Nr.__nwjs?hi.exports=Ls():hi.exports=js()});var lt=N((_C,Hs)=>{function Bs(e){return e&&e.__esModule&&e.default?e.default:e}(function(){let t=require;require=Object.assign(r=>{let n=t(r)??{};return Bs(n)},t)})();var $g=Object.create,Dr=Object.defineProperty,Gg=Object.getOwnPropertyDescriptor,Jg=Object.getOwnPropertyNames,Qg=Object.getPrototypeOf,Yg=Object.prototype.hasOwnProperty,Kg=(e,t)=>{for(var r in t)Dr(e,r,{get:t[r],enumerable:!0})},zs=(e,t,r,n)=>{if(t&&typeof t=="object"||typeof t=="function")for(let i of Jg(t))!Yg.call(e,i)&&i!==r&&Dr(e,i,{get:()=>t[i],enumerable:!(n=Gg(t,i))||n.enumerable});return e},Xg=(e,t,r)=>(r=e!=null?$g(Qg(e)):{},zs(t||!e||!e.__esModule?Dr(r,"default",{value:e,enumerable:!0}):r,e)),Zg=e=>zs(Dr({},"__esModule",{value:!0}),e),Vs={};Kg(Vs,{getDebugger:()=>eb,initDebugHelpers:()=>tb});Hs.exports=Zg(Vs);var Jt=Xg(Bs(di()),1),Ws=",",Rr="-";function eb(e){let t=Jt.default.default(e);return t.log=(r,...n)=>{ib(e,r,...n)},t.printStackTrace=(r,n)=>{Us(e,r,n)},t}function tb(){window.DEBUG={disable:rb,enable:nb,get:mi,set:gi}}function rb(e){let t=new Set(mi());for(let r of Qt(e)){if(r.startsWith(Rr))continue;let n=Rr+r;t.has(r)&&t.delete(r),t.add(n)}gi(Array.from(t))}function nb(e){let t=new Set(mi());for(let r of Qt(e)){if(!r.startsWith(Rr)){let n=Rr+r;t.has(n)&&t.delete(n)}t.add(r)}gi(Array.from(t))}function mi(){return Qt(Jt.default.load()??"")}function ib(e,t,...r){if(!Jt.default.enabled(e))return;let o=(new Error().stack?.split(`
`)??[])[4]??"";console.debug(t,...r),Us(e,o,"Original debug message caller")}function Us(e,t,r){Jt.default.enabled(e)&&(t||(t="(unavailable)"),r||(r="Caller stack trace"),console.debug(`NotError:${e}:${r}
${t}`))}function gi(e){Jt.default.enable(Qt(e).join(Ws))}function Qt(e){return typeof e=="string"?e.split(Ws).filter(Boolean):e.flatMap(Qt)}});var Js=N((PC,Gs)=>{function ob(e){return e&&e.__esModule&&e.default?e.default:e}(function(){let t=require;require=Object.assign(r=>{let n=t(r)??{};return ob(n)},t)})();var bi=Object.defineProperty,ab=Object.getOwnPropertyDescriptor,sb=Object.getOwnPropertyNames,lb=Object.prototype.hasOwnProperty,ub=(e,t)=>{for(var r in t)bi(e,r,{get:t[r],enumerable:!0})},cb=(e,t,r,n)=>{if(t&&typeof t=="object"||typeof t=="function")for(let i of sb(t))!lb.call(e,i)&&i!==r&&bi(e,i,{get:()=>t[i],enumerable:!(n=ab(t,i))||n.enumerable});return e},fb=e=>cb(bi({},"__esModule",{value:!0}),e),$s={};ub($s,{loop:()=>db});Gs.exports=fb($s);var pb=lt(),hb=ze(),CC=globalThis.process??{browser:!0,cwd:()=>"/",env:{},platform:"android"};async function db(e){let t=e.items,r=0,n=new Notice("",0);for(let i of t){if(e.abortSignal?.aborted){n.hide();return}r++;let o=`# ${r.toString()} / ${t.length.toString()}`,a=e.buildNoticeMessage(i,o);n.setMessage(a),(0,pb.getDebugger)("obsidian-dev-utils:loop")(a);try{await e.processItem(i)}catch(s){if(e.shouldContinueOnError)(0,hb.emitAsyncErrorEvent)(s);else throw n.hide(),s}}n.hide()}});var yi=N((AC,Ks)=>{function mb(e){return e&&e.__esModule&&e.default?e.default:e}(function(){let t=require;require=Object.assign(r=>{let n=t(r)??{};return mb(n)},t)})();var ki=Object.defineProperty,gb=Object.getOwnPropertyDescriptor,bb=Object.getOwnPropertyNames,wb=Object.prototype.hasOwnProperty,kb=(e,t)=>{for(var r in t)ki(e,r,{get:t[r],enumerable:!0})},yb=(e,t,r,n)=>{if(t&&typeof t=="object"||typeof t=="function")for(let i of bb(t))!wb.call(e,i)&&i!==r&&ki(e,i,{get:()=>t[i],enumerable:!(n=gb(t,i))||n.enumerable});return e},vb=e=>yb(ki({},"__esModule",{value:!0}),e),Ys={};kb(Ys,{alert:()=>xb});Ks.exports=vb(Ys);var Qs=require("obsidian"),wi=class extends Qs.Modal{constructor(t,r){super(t.app),this.resolve=r;let n={app:t.app,message:"",okButtonStyles:{},okButtonText:"OK",title:""};this.options={...n,...t}}options;onClose(){this.resolve()}onOpen(){this.titleEl.setText(this.options.title),this.contentEl.createEl("p").setText(this.options.message);let r=new Qs.ButtonComponent(this.contentEl);r.setButtonText(this.options.okButtonText),r.setCta(),r.onClick(this.close.bind(this)),Object.assign(r.buttonEl.style,this.options.okButtonStyles)}};async function xb(e){return new Promise(t=>{new wi(e,t).open()})}});var xi=N((SC,nl)=>{function el(e){return e&&e.__esModule&&e.default?e.default:e}(function(){let t=require;require=Object.assign(r=>{let n=t(r)??{};return el(n)},t)})();var _b=Object.create,Mr=Object.defineProperty,Cb=Object.getOwnPropertyDescriptor,Pb=Object.getOwnPropertyNames,Ab=Object.getPrototypeOf,Eb=Object.prototype.hasOwnProperty,Sb=(e,t)=>{for(var r in t)Mr(e,r,{get:t[r],enumerable:!0})},tl=(e,t,r,n)=>{if(t&&typeof t=="object"||typeof t=="function")for(let i of Pb(t))!Eb.call(e,i)&&i!==r&&Mr(e,i,{get:()=>t[i],enumerable:!(n=Cb(t,i))||n.enumerable});return e},Fb=(e,t,r)=>(r=e!=null?_b(Ab(e)):{},tl(t||!e||!e.__esModule?Mr(r,"default",{value:e,enumerable:!0}):r,e)),Ob=e=>tl(Mr({},"__esModule",{value:!0}),e),rl={};Sb(rl,{PluginBase:()=>vi});nl.exports=Ob(rl);var Lb=Fb(el(di()),1),Xs=require("obsidian"),Tb=lt(),Ib=ze(),Zs=ot(),EC=globalThis.process??{browser:!0,cwd:()=>"/",env:{},platform:"android"},vi=class extends Xs.Plugin{consoleDebug;get abortSignal(){return this._abortSignal}get settingsCopy(){return this.createPluginSettings(this.settings.toJSON())}get settings(){return this._settings}_abortSignal;_settings;notice;constructor(t,r){super(t,r),(0,Tb.initDebugHelpers)(),this.consoleDebug=Lb.default.default(r.id),console.debug(`Debug messages for plugin '${r.name}' are not shown by default. Set window.DEBUG.enable('${r.id}') to see them. See https://github.com/debug-js/debug?tab=readme-ov-file for more information`)}async onload(){this.register((0,Ib.registerAsyncErrorEventHandler)(()=>{this.showNotice("An unhandled error occurred. Please check the console for more information.")})),await this.loadSettings();let t=this.createPluginSettingsTab();t&&this.addSettingTab(t);let r=new AbortController;this._abortSignal=r.signal,this.register(()=>{r.abort()}),await this.onloadComplete(),this.app.workspace.onLayoutReady(this.onLayoutReady.bind(this))}async saveSettings(t){let r=t.toJSON();this._settings=this.createPluginSettings(r),await this.saveData(r)}onLayoutReady(){(0,Zs.noop)()}onloadComplete(){(0,Zs.noop)()}showNotice(t){this.notice&&this.notice.hide(),this.notice=new Xs.Notice(`${this.manifest.name}
${t}`)}async loadSettings(){let t=await this.loadData();this._settings=this.createPluginSettings(t),this._settings.shouldSaveAfterLoad()&&await this.saveSettings(this._settings)}}});var zr=N((FC,ll)=>{function qb(e){return e&&e.__esModule&&e.default?e.default:e}(function(){let t=require;require=Object.assign(r=>{let n=t(r)??{};return qb(n)},t)})();var Pi=Object.defineProperty,Nb=Object.getOwnPropertyDescriptor,Rb=Object.getOwnPropertyNames,Db=Object.prototype.hasOwnProperty,Mb=(e,t)=>{for(var r in t)Pi(e,r,{get:t[r],enumerable:!0})},jb=(e,t,r,n)=>{if(t&&typeof t=="object"||typeof t=="function")for(let i of Rb(t))!Db.call(e,i)&&i!==r&&Pi(e,i,{get:()=>t[i],enumerable:!(n=Nb(t,i))||n.enumerable});return e},Bb=e=>jb(Pi({},"__esModule",{value:!0}),e),il={};Mb(il,{addErrorHandler:()=>ol,asyncFilter:()=>zb,asyncFlatMap:()=>Vb,asyncMap:()=>Ai,convertAsyncToSync:()=>Wb,convertSyncToAsync:()=>Ub,invokeAsyncSafely:()=>al,marksAsTerminateRetry:()=>Hb,retryWithTimeout:()=>$b,runWithTimeout:()=>sl,sleep:()=>Br,timeout:()=>Gb,toArray:()=>Jb});ll.exports=Bb(il);var _i=lt(),Ci=ze(),jr=(0,_i.getDebugger)("obsidian-dev-utils:Async:retryWithTimeout");async function ol(e){try{await e()}catch(t){(0,Ci.emitAsyncErrorEvent)(t)}}async function zb(e,t){let r=await Ai(e,t);return e.filter((n,i)=>r[i])}async function Vb(e,t){return(await Ai(e,t)).flat()}async function Ai(e,t){return await Promise.all(e.map(t))}function Wb(e){return(...t)=>{al(()=>e(...t))}}function Ub(e){return(...t)=>Promise.resolve().then(()=>e(...t))}function al(e){ol(e)}function Hb(e){return Object.assign(e,{__terminateRetry:!0})}async function $b(e,t={},r){r??=(0,Ci.getStackTrace)(1);let i={...{retryDelayInMilliseconds:100,shouldRetryOnError:!1,timeoutInMilliseconds:5e3},...t};await sl(i.timeoutInMilliseconds,async()=>{let o=0;for(;;){i.abortSignal?.throwIfAborted(),o++;let a;try{a=await e()}catch(s){if(!i.shouldRetryOnError||s.__terminateRetry)throw s;(0,Ci.printError)(s),a=!1}if(a){o>1&&(jr(`Retry completed successfully after ${o.toString()} attempts`),jr.printStackTrace(r));return}jr(`Retry attempt ${o.toString()} completed unsuccessfully. Trying again in ${i.retryDelayInMilliseconds.toString()} milliseconds`,{fn:e}),jr.printStackTrace(r),await Br(i.retryDelayInMilliseconds)}})}async function sl(e,t){let r=!0,n=null,i=performance.now();if(await Promise.race([o(),a()]),r)throw new Error("Timed out");return n;async function o(){n=await t(),r=!1;let s=performance.now()-i;(0,_i.getDebugger)("obsidian-dev-utils:Async:runWithTimeout")(`Execution time: ${s.toString()} milliseconds`,{fn:t})}async function a(){if(!r||(await Br(e),!r))return;let s=performance.now()-i;console.warn(`Timed out in ${s.toString()} milliseconds`,{fn:t}),(0,_i.getDebugger)("obsidian-dev-utils:Async:timeout").enabled&&(console.warn("The execution is not terminated because debugger obsidian-dev-utils:Async:timeout is enabled. See window.DEBUG.enable('obsidian-dev-utils:Async:timeout') and https://github.com/debug-js/debug?tab=readme-ov-file for more information"),await a())}}async function Br(e){await new Promise(t=>setTimeout(t,e))}async function Gb(e){throw await Br(e),new Error(`Timed out in ${e.toString()} milliseconds`)}async function Jb(e){let t=[];for await(let r of e)t.push(r);return t}});var Si=N((OC,cl)=>{function Qb(e){return e&&e.__esModule&&e.default?e.default:e}(function(){let t=require;require=Object.assign(r=>{let n=t(r)??{};return Qb(n)},t)})();var Ei=Object.defineProperty,Yb=Object.getOwnPropertyDescriptor,Kb=Object.getOwnPropertyNames,Xb=Object.prototype.hasOwnProperty,Zb=(e,t)=>{for(var r in t)Ei(e,r,{get:t[r],enumerable:!0})},ew=(e,t,r,n)=>{if(t&&typeof t=="object"||typeof t=="function")for(let i of Kb(t))!Xb.call(e,i)&&i!==r&&Ei(e,i,{get:()=>t[i],enumerable:!(n=Yb(t,i))||n.enumerable});return e},tw=e=>ew(Ei({},"__esModule",{value:!0}),e),ul={};Zb(ul,{ValueWrapper:()=>Vr,getApp:()=>rw,getObsidianDevUtilsState:()=>nw});cl.exports=tw(ul);var Vr=class{constructor(t){this.value=t}};function rw(){let e;try{globalThis.require.resolve("obsidian/app"),e=!0}catch{e=!1}if(e)return globalThis.require("obsidian/app");let t=globalThis.app;if(t)return t;throw new Error("Obsidian app not found")}function nw(e,t,r){let n=e,i=n.obsidianDevUtilsState??={};return i[t]??=new Vr(r)}});var hl=N((LC,pl)=>{function iw(e){return e&&e.__esModule&&e.default?e.default:e}(function(){let t=require;require=Object.assign(r=>{let n=t(r)??{};return iw(n)},t)})();var Fi=Object.defineProperty,ow=Object.getOwnPropertyDescriptor,aw=Object.getOwnPropertyNames,sw=Object.prototype.hasOwnProperty,lw=(e,t)=>{for(var r in t)Fi(e,r,{get:t[r],enumerable:!0})},uw=(e,t,r,n)=>{if(t&&typeof t=="object"||typeof t=="function")for(let i of aw(t))!sw.call(e,i)&&i!==r&&Fi(e,i,{get:()=>t[i],enumerable:!(n=ow(t,i))||n.enumerable});return e},cw=e=>uw(Fi({},"__esModule",{value:!0}),e),fl={};lw(fl,{invokeAsyncAndLog:()=>hw});pl.exports=cw(fl);var fw=lt(),pw=ze(),Ot=(0,fw.getDebugger)("obsidian-dev-utils:Logger:invokeAsyncAndLog");async function hw(e,t,r){let n=performance.now();r??=(0,pw.getStackTrace)(1),Ot(`${e}:start`,{fn:t,timestampStart:n}),Ot.printStackTrace(r);try{await t();let i=performance.now();Ot(`${e}:end`,{duration:i-n,fn:t,timestampEnd:i,timestampStart:n}),Ot.printStackTrace(r)}catch(i){let o=performance.now();throw Ot(`${e}:error`,{duration:o-n,error:i,fn:t,timestampEnd:o,timestampStart:n}),Ot.printStackTrace(r),i}}});var Ti=N((IC,kl)=>{function dw(e){return e&&e.__esModule&&e.default?e.default:e}(function(){let t=require;require=Object.assign(r=>{let n=t(r)??{};return dw(n)},t)})();var Oi=Object.defineProperty,mw=Object.getOwnPropertyDescriptor,gw=Object.getOwnPropertyNames,bw=Object.prototype.hasOwnProperty,ww=(e,t)=>{for(var r in t)Oi(e,r,{get:t[r],enumerable:!0})},kw=(e,t,r,n)=>{if(t&&typeof t=="object"||typeof t=="function")for(let i of gw(t))!bw.call(e,i)&&i!==r&&Oi(e,i,{get:()=>t[i],enumerable:!(n=mw(t,i))||n.enumerable});return e},yw=e=>kw(Oi({},"__esModule",{value:!0}),e),ml={};ww(ml,{addToQueue:()=>Cw,addToQueueAndWait:()=>Li,flushQueue:()=>Pw});kl.exports=yw(ml);var dl=zr(),gl=ze(),vw=ot(),xw=Si(),_w=hl(),TC=globalThis.process??{browser:!0,cwd:()=>"/",env:{},platform:"android"};function Cw(e,t,r,n){n??=(0,gl.getStackTrace)(1),Li(e,t,r,n)}async function Li(e,t,r,n){r??=6e4,n??=(0,gl.getStackTrace)(1);let o=bl(e).value;o.items.push({fn:t,stackTrace:n,timeoutInMilliseconds:r}),o.promise=o.promise.then(()=>wl(e)),await o.promise}async function Pw(e){await Li(e,vw.noop)}function bl(e){return(0,xw.getObsidianDevUtilsState)(e,"queue",{items:[],promise:Promise.resolve()})}async function wl(e){let t=bl(e).value,r=t.items[0];r&&(await(0,dl.addErrorHandler)(()=>(0,dl.runWithTimeout)(r.timeoutInMilliseconds,()=>(0,_w.invokeAsyncAndLog)(wl.name,r.fn,r.stackTrace))),t.items.shift())}});var yl=N(Yt=>{"use strict";function Aw(e,t){let r=Object.keys(t).map(n=>Ew(e,n,t[n]));return r.length===1?r[0]:function(){r.forEach(n=>n())}}function Ew(e,t,r){let n=e[t],i=e.hasOwnProperty(t),o=i?n:function(){return Object.getPrototypeOf(e)[t].apply(this,arguments)},a=r(o);return n&&Object.setPrototypeOf(a,n),Object.setPrototypeOf(s,a),e[t]=s,l;function s(...c){return a===o&&e[t]===s&&l(),a.apply(this,c)}function l(){e[t]===s&&(i?e[t]=o:delete e[t]),a!==o&&(a=o,Object.setPrototypeOf(s,n||Function))}}function Sw(e,t,r){return n[e]=e,n;function n(...i){return(t[e]===e?t:r).apply(this,i)}}function Ii(e,t){return e.then(t,t)}function Fw(e){let t=Promise.resolve();function r(...n){return t=new Promise((i,o)=>{Ii(t,()=>{e.apply(this,n).then(i,o)})})}return r.after=function(){return t=new Promise((n,i)=>{Ii(t,n)})},r}Yt.after=Ii;Yt.around=Aw;Yt.dedupe=Sw;Yt.serialize=Fw});var ut=N((RC,Pl)=>{function Ow(e){return e&&e.__esModule&&e.default?e.default:e}(function(){let t=require;require=Object.assign(r=>{let n=t(r)??{};return Ow(n)},t)})();var qi=Object.defineProperty,Lw=Object.getOwnPropertyDescriptor,Tw=Object.getOwnPropertyNames,Iw=Object.prototype.hasOwnProperty,qw=(e,t)=>{for(var r in t)qi(e,r,{get:t[r],enumerable:!0})},Nw=(e,t,r,n)=>{if(t&&typeof t=="object"||typeof t=="function")for(let i of Tw(t))!Iw.call(e,i)&&i!==r&&qi(e,i,{get:()=>t[i],enumerable:!(n=Lw(t,i))||n.enumerable});return e},Rw=e=>Nw(qi({},"__esModule",{value:!0}),e),vl={};qw(vl,{assignWithNonEnumerableProperties:()=>Mw,cloneWithNonEnumerableProperties:()=>jw,deepEqual:()=>xl,deleteProperties:()=>Bw,deleteProperty:()=>_l,getNestedPropertyValue:()=>zw,getPrototypeOf:()=>Wr,nameof:()=>Vw,normalizeOptionalProperties:()=>Ww,setNestedPropertyValue:()=>Uw,toJson:()=>Hw});Pl.exports=Rw(vl);var Dw=ze(),NC=globalThis.process??{browser:!0,cwd:()=>"/",env:{},platform:"android"};function Mw(e,...t){return Cl(e,...t)}function jw(e){return Object.create(Wr(e),Object.getOwnPropertyDescriptors(e))}function xl(e,t){if(e===t)return!0;if(typeof e!="object"||typeof t!="object"||e===null||t===null)return!1;let r=Object.keys(e),n=Object.keys(t);if(r.length!==n.length)return!1;let i=e,o=t;for(let a of r)if(!n.includes(a)||!xl(i[a],o[a]))return!1;return!0}function Bw(e,t){let r=!1;for(let n of t)r=_l(e,n)||r;return r}function _l(e,t){return Object.prototype.hasOwnProperty.call(e,t)?(delete e[t],!0):!1}function zw(e,t){let r=e,n=t.split(".");for(let i of n){if(r===void 0)return;r=r[i]}return r}function Wr(e){return e==null?e:Object.getPrototypeOf(e)}function Vw(e){return e}function Ww(e){return e}function Uw(e,t,r){let n=new Error(`Property path ${t} not found`),i=e,o=t.split(".");for(let s of o.slice(0,-1)){if(i===void 0)throw n;i=i[s]}let a=o.at(-1);if(i===void 0||a===void 0)throw n;i[a]=r}function Hw(e,t={}){let{shouldHandleFunctions:r=!1,space:n=2}=t;if(!r)return JSON.stringify(e,null,n);let i=[],a=JSON.stringify(e,(s,l)=>{if(typeof l=="function"){let c=i.length;return i.push(l.toString()),`__FUNCTION_${c.toString()}`}return l},n);return a=a.replaceAll(/"__FUNCTION_(\d+)"/g,(s,l)=>i[parseInt(l)]??(0,Dw.throwExpression)(new Error(`Function with index ${l} not found`))),a}function Cl(e,...t){for(let n of t)Object.defineProperties(e,Object.getOwnPropertyDescriptors(n));let r=t.map(n=>Wr(n)).filter(n=>!!n);if(r.length>0){let n=Cl({},Wr(e),...r);Object.setPrototypeOf(e,n)}return e}});var Mi=N((DC,Ol)=>{function $w(e){return e&&e.__esModule&&e.default?e.default:e}(function(){let t=require;require=Object.assign(r=>{let n=t(r)??{};return $w(n)},t)})();var Di=Object.defineProperty,Gw=Object.getOwnPropertyDescriptor,Jw=Object.getOwnPropertyNames,Qw=Object.prototype.hasOwnProperty,Yw=(e,t)=>{for(var r in t)Di(e,r,{get:t[r],enumerable:!0})},Kw=(e,t,r,n)=>{if(t&&typeof t=="object"||typeof t=="function")for(let i of Jw(t))!Qw.call(e,i)&&i!==r&&Di(e,i,{get:()=>t[i],enumerable:!(n=Gw(t,i))||n.enumerable});return e},Xw=e=>Kw(Di({},"__esModule",{value:!0}),e),El={};Yw(El,{getAttachmentFilePath:()=>Sl,getAttachmentFolderPath:()=>Ri,getAvailablePathForAttachments:()=>Fl,hasOwnAttachmentFolder:()=>ek});Ol.exports=Xw(El);var Zw=Ct(),Ur=Ve(),Ni=At(),Kt=Oe();async function Sl(e,t,r){let n=(0,Kt.getPath)(e,t),i=(0,Kt.getPath)(e,r),o=(0,Kt.getFile)(e,i,!0),a=(0,Ur.extname)(n),s=(0,Ur.basename)(n,a),l=e.vault.getAvailablePathForAttachments;return l.isExtended?l(s,a.slice(1),o,!0):await Fl(e,s,a.slice(1),o,!0)}async function Ri(e,t){return(0,Zw.parentFolderPath)(await Sl(e,"DUMMY_FILE.pdf",t))}async function Fl(e,t,r,n,i){let o=e.vault.getConfig("attachmentFolderPath"),a=o==="."||o==="./",s=null;o.startsWith("./")&&(s=(0,Ni.trimStart)(o,"./")),a?o=n?n.parent?.path??"":"":s&&(o=(n?n.parent?.getParentPrefix()??"":"")+s),o=(0,Ni.normalize)(Al(o)),t=(0,Ni.normalize)(Al(t));let l=(0,Kt.getFolderOrNull)(e,o,!0);!l&&s&&(i?l=(0,Kt.getFolder)(e,o,!0):l=await e.vault.createFolder(o));let c=l?.getParentPrefix()??"";return e.vault.getAvailablePath(c+t,r)}async function ek(e,t){let r=await Ri(e,t),n=await Ri(e,(0,Ur.join)((0,Ur.dirname)(t),"DUMMY_FILE.md"));return r!==n}function Al(e){return e=e.replace(/([\\/])+/g,"/"),e=e.replace(/(^\/+|\/+$)/g,""),e||"/"}});function ct(e,t){let r=t||tk,n=typeof r.includeImageAlt=="boolean"?r.includeImageAlt:!0,i=typeof r.includeHtml=="boolean"?r.includeHtml:!0;return Tl(e,n,i)}function Tl(e,t,r){if(rk(e)){if("value"in e)return e.type==="html"&&!r?"":e.value;if(t&&"alt"in e&&e.alt)return e.alt;if("children"in e)return Ll(e.children,t,r)}return Array.isArray(e)?Ll(e,t,r):""}function Ll(e,t,r){let n=[],i=-1;for(;++i<e.length;)n[i]=Tl(e[i],t,r);return n.join("")}function rk(e){return!!(e&&typeof e=="object")}var tk,Il=b(()=>{tk={}});var Hr=b(()=>{Il()});var ji,ql=b(()=>{ji={AElig:"\xC6",AMP:"&",Aacute:"\xC1",Abreve:"\u0102",Acirc:"\xC2",Acy:"\u0410",Afr:"\u{1D504}",Agrave:"\xC0",Alpha:"\u0391",Amacr:"\u0100",And:"\u2A53",Aogon:"\u0104",Aopf:"\u{1D538}",ApplyFunction:"\u2061",Aring:"\xC5",Ascr:"\u{1D49C}",Assign:"\u2254",Atilde:"\xC3",Auml:"\xC4",Backslash:"\u2216",Barv:"\u2AE7",Barwed:"\u2306",Bcy:"\u0411",Because:"\u2235",Bernoullis:"\u212C",Beta:"\u0392",Bfr:"\u{1D505}",Bopf:"\u{1D539}",Breve:"\u02D8",Bscr:"\u212C",Bumpeq:"\u224E",CHcy:"\u0427",COPY:"\xA9",Cacute:"\u0106",Cap:"\u22D2",CapitalDifferentialD:"\u2145",Cayleys:"\u212D",Ccaron:"\u010C",Ccedil:"\xC7",Ccirc:"\u0108",Cconint:"\u2230",Cdot:"\u010A",Cedilla:"\xB8",CenterDot:"\xB7",Cfr:"\u212D",Chi:"\u03A7",CircleDot:"\u2299",CircleMinus:"\u2296",CirclePlus:"\u2295",CircleTimes:"\u2297",ClockwiseContourIntegral:"\u2232",CloseCurlyDoubleQuote:"\u201D",CloseCurlyQuote:"\u2019",Colon:"\u2237",Colone:"\u2A74",Congruent:"\u2261",Conint:"\u222F",ContourIntegral:"\u222E",Copf:"\u2102",Coproduct:"\u2210",CounterClockwiseContourIntegral:"\u2233",Cross:"\u2A2F",Cscr:"\u{1D49E}",Cup:"\u22D3",CupCap:"\u224D",DD:"\u2145",DDotrahd:"\u2911",DJcy:"\u0402",DScy:"\u0405",DZcy:"\u040F",Dagger:"\u2021",Darr:"\u21A1",Dashv:"\u2AE4",Dcaron:"\u010E",Dcy:"\u0414",Del:"\u2207",Delta:"\u0394",Dfr:"\u{1D507}",DiacriticalAcute:"\xB4",DiacriticalDot:"\u02D9",DiacriticalDoubleAcute:"\u02DD",DiacriticalGrave:"`",DiacriticalTilde:"\u02DC",Diamond:"\u22C4",DifferentialD:"\u2146",Dopf:"\u{1D53B}",Dot:"\xA8",DotDot:"\u20DC",DotEqual:"\u2250",DoubleContourIntegral:"\u222F",DoubleDot:"\xA8",DoubleDownArrow:"\u21D3",DoubleLeftArrow:"\u21D0",DoubleLeftRightArrow:"\u21D4",DoubleLeftTee:"\u2AE4",DoubleLongLeftArrow:"\u27F8",DoubleLongLeftRightArrow:"\u27FA",DoubleLongRightArrow:"\u27F9",DoubleRightArrow:"\u21D2",DoubleRightTee:"\u22A8",DoubleUpArrow:"\u21D1",DoubleUpDownArrow:"\u21D5",DoubleVerticalBar:"\u2225",DownArrow:"\u2193",DownArrowBar:"\u2913",DownArrowUpArrow:"\u21F5",DownBreve:"\u0311",DownLeftRightVector:"\u2950",DownLeftTeeVector:"\u295E",DownLeftVector:"\u21BD",DownLeftVectorBar:"\u2956",DownRightTeeVector:"\u295F",DownRightVector:"\u21C1",DownRightVectorBar:"\u2957",DownTee:"\u22A4",DownTeeArrow:"\u21A7",Downarrow:"\u21D3",Dscr:"\u{1D49F}",Dstrok:"\u0110",ENG:"\u014A",ETH:"\xD0",Eacute:"\xC9",Ecaron:"\u011A",Ecirc:"\xCA",Ecy:"\u042D",Edot:"\u0116",Efr:"\u{1D508}",Egrave:"\xC8",Element:"\u2208",Emacr:"\u0112",EmptySmallSquare:"\u25FB",EmptyVerySmallSquare:"\u25AB",Eogon:"\u0118",Eopf:"\u{1D53C}",Epsilon:"\u0395",Equal:"\u2A75",EqualTilde:"\u2242",Equilibrium:"\u21CC",Escr:"\u2130",Esim:"\u2A73",Eta:"\u0397",Euml:"\xCB",Exists:"\u2203",ExponentialE:"\u2147",Fcy:"\u0424",Ffr:"\u{1D509}",FilledSmallSquare:"\u25FC",FilledVerySmallSquare:"\u25AA",Fopf:"\u{1D53D}",ForAll:"\u2200",Fouriertrf:"\u2131",Fscr:"\u2131",GJcy:"\u0403",GT:">",Gamma:"\u0393",Gammad:"\u03DC",Gbreve:"\u011E",Gcedil:"\u0122",Gcirc:"\u011C",Gcy:"\u0413",Gdot:"\u0120",Gfr:"\u{1D50A}",Gg:"\u22D9",Gopf:"\u{1D53E}",GreaterEqual:"\u2265",GreaterEqualLess:"\u22DB",GreaterFullEqual:"\u2267",GreaterGreater:"\u2AA2",GreaterLess:"\u2277",GreaterSlantEqual:"\u2A7E",GreaterTilde:"\u2273",Gscr:"\u{1D4A2}",Gt:"\u226B",HARDcy:"\u042A",Hacek:"\u02C7",Hat:"^",Hcirc:"\u0124",Hfr:"\u210C",HilbertSpace:"\u210B",Hopf:"\u210D",HorizontalLine:"\u2500",Hscr:"\u210B",Hstrok:"\u0126",HumpDownHump:"\u224E",HumpEqual:"\u224F",IEcy:"\u0415",IJlig:"\u0132",IOcy:"\u0401",Iacute:"\xCD",Icirc:"\xCE",Icy:"\u0418",Idot:"\u0130",Ifr:"\u2111",Igrave:"\xCC",Im:"\u2111",Imacr:"\u012A",ImaginaryI:"\u2148",Implies:"\u21D2",Int:"\u222C",Integral:"\u222B",Intersection:"\u22C2",InvisibleComma:"\u2063",InvisibleTimes:"\u2062",Iogon:"\u012E",Iopf:"\u{1D540}",Iota:"\u0399",Iscr:"\u2110",Itilde:"\u0128",Iukcy:"\u0406",Iuml:"\xCF",Jcirc:"\u0134",Jcy:"\u0419",Jfr:"\u{1D50D}",Jopf:"\u{1D541}",Jscr:"\u{1D4A5}",Jsercy:"\u0408",Jukcy:"\u0404",KHcy:"\u0425",KJcy:"\u040C",Kappa:"\u039A",Kcedil:"\u0136",Kcy:"\u041A",Kfr:"\u{1D50E}",Kopf:"\u{1D542}",Kscr:"\u{1D4A6}",LJcy:"\u0409",LT:"<",Lacute:"\u0139",Lambda:"\u039B",Lang:"\u27EA",Laplacetrf:"\u2112",Larr:"\u219E",Lcaron:"\u013D",Lcedil:"\u013B",Lcy:"\u041B",LeftAngleBracket:"\u27E8",LeftArrow:"\u2190",LeftArrowBar:"\u21E4",LeftArrowRightArrow:"\u21C6",LeftCeiling:"\u2308",LeftDoubleBracket:"\u27E6",LeftDownTeeVector:"\u2961",LeftDownVector:"\u21C3",LeftDownVectorBar:"\u2959",LeftFloor:"\u230A",LeftRightArrow:"\u2194",LeftRightVector:"\u294E",LeftTee:"\u22A3",LeftTeeArrow:"\u21A4",LeftTeeVector:"\u295A",LeftTriangle:"\u22B2",LeftTriangleBar:"\u29CF",LeftTriangleEqual:"\u22B4",LeftUpDownVector:"\u2951",LeftUpTeeVector:"\u2960",LeftUpVector:"\u21BF",LeftUpVectorBar:"\u2958",LeftVector:"\u21BC",LeftVectorBar:"\u2952",Leftarrow:"\u21D0",Leftrightarrow:"\u21D4",LessEqualGreater:"\u22DA",LessFullEqual:"\u2266",LessGreater:"\u2276",LessLess:"\u2AA1",LessSlantEqual:"\u2A7D",LessTilde:"\u2272",Lfr:"\u{1D50F}",Ll:"\u22D8",Lleftarrow:"\u21DA",Lmidot:"\u013F",LongLeftArrow:"\u27F5",LongLeftRightArrow:"\u27F7",LongRightArrow:"\u27F6",Longleftarrow:"\u27F8",Longleftrightarrow:"\u27FA",Longrightarrow:"\u27F9",Lopf:"\u{1D543}",LowerLeftArrow:"\u2199",LowerRightArrow:"\u2198",Lscr:"\u2112",Lsh:"\u21B0",Lstrok:"\u0141",Lt:"\u226A",Map:"\u2905",Mcy:"\u041C",MediumSpace:"\u205F",Mellintrf:"\u2133",Mfr:"\u{1D510}",MinusPlus:"\u2213",Mopf:"\u{1D544}",Mscr:"\u2133",Mu:"\u039C",NJcy:"\u040A",Nacute:"\u0143",Ncaron:"\u0147",Ncedil:"\u0145",Ncy:"\u041D",NegativeMediumSpace:"\u200B",NegativeThickSpace:"\u200B",NegativeThinSpace:"\u200B",NegativeVeryThinSpace:"\u200B",NestedGreaterGreater:"\u226B",NestedLessLess:"\u226A",NewLine:`
`,Nfr:"\u{1D511}",NoBreak:"\u2060",NonBreakingSpace:"\xA0",Nopf:"\u2115",Not:"\u2AEC",NotCongruent:"\u2262",NotCupCap:"\u226D",NotDoubleVerticalBar:"\u2226",NotElement:"\u2209",NotEqual:"\u2260",NotEqualTilde:"\u2242\u0338",NotExists:"\u2204",NotGreater:"\u226F",NotGreaterEqual:"\u2271",NotGreaterFullEqual:"\u2267\u0338",NotGreaterGreater:"\u226B\u0338",NotGreaterLess:"\u2279",NotGreaterSlantEqual:"\u2A7E\u0338",NotGreaterTilde:"\u2275",NotHumpDownHump:"\u224E\u0338",NotHumpEqual:"\u224F\u0338",NotLeftTriangle:"\u22EA",NotLeftTriangleBar:"\u29CF\u0338",NotLeftTriangleEqual:"\u22EC",NotLess:"\u226E",NotLessEqual:"\u2270",NotLessGreater:"\u2278",NotLessLess:"\u226A\u0338",NotLessSlantEqual:"\u2A7D\u0338",NotLessTilde:"\u2274",NotNestedGreaterGreater:"\u2AA2\u0338",NotNestedLessLess:"\u2AA1\u0338",NotPrecedes:"\u2280",NotPrecedesEqual:"\u2AAF\u0338",NotPrecedesSlantEqual:"\u22E0",NotReverseElement:"\u220C",NotRightTriangle:"\u22EB",NotRightTriangleBar:"\u29D0\u0338",NotRightTriangleEqual:"\u22ED",NotSquareSubset:"\u228F\u0338",NotSquareSubsetEqual:"\u22E2",NotSquareSuperset:"\u2290\u0338",NotSquareSupersetEqual:"\u22E3",NotSubset:"\u2282\u20D2",NotSubsetEqual:"\u2288",NotSucceeds:"\u2281",NotSucceedsEqual:"\u2AB0\u0338",NotSucceedsSlantEqual:"\u22E1",NotSucceedsTilde:"\u227F\u0338",NotSuperset:"\u2283\u20D2",NotSupersetEqual:"\u2289",NotTilde:"\u2241",NotTildeEqual:"\u2244",NotTildeFullEqual:"\u2247",NotTildeTilde:"\u2249",NotVerticalBar:"\u2224",Nscr:"\u{1D4A9}",Ntilde:"\xD1",Nu:"\u039D",OElig:"\u0152",Oacute:"\xD3",Ocirc:"\xD4",Ocy:"\u041E",Odblac:"\u0150",Ofr:"\u{1D512}",Ograve:"\xD2",Omacr:"\u014C",Omega:"\u03A9",Omicron:"\u039F",Oopf:"\u{1D546}",OpenCurlyDoubleQuote:"\u201C",OpenCurlyQuote:"\u2018",Or:"\u2A54",Oscr:"\u{1D4AA}",Oslash:"\xD8",Otilde:"\xD5",Otimes:"\u2A37",Ouml:"\xD6",OverBar:"\u203E",OverBrace:"\u23DE",OverBracket:"\u23B4",OverParenthesis:"\u23DC",PartialD:"\u2202",Pcy:"\u041F",Pfr:"\u{1D513}",Phi:"\u03A6",Pi:"\u03A0",PlusMinus:"\xB1",Poincareplane:"\u210C",Popf:"\u2119",Pr:"\u2ABB",Precedes:"\u227A",PrecedesEqual:"\u2AAF",PrecedesSlantEqual:"\u227C",PrecedesTilde:"\u227E",Prime:"\u2033",Product:"\u220F",Proportion:"\u2237",Proportional:"\u221D",Pscr:"\u{1D4AB}",Psi:"\u03A8",QUOT:'"',Qfr:"\u{1D514}",Qopf:"\u211A",Qscr:"\u{1D4AC}",RBarr:"\u2910",REG:"\xAE",Racute:"\u0154",Rang:"\u27EB",Rarr:"\u21A0",Rarrtl:"\u2916",Rcaron:"\u0158",Rcedil:"\u0156",Rcy:"\u0420",Re:"\u211C",ReverseElement:"\u220B",ReverseEquilibrium:"\u21CB",ReverseUpEquilibrium:"\u296F",Rfr:"\u211C",Rho:"\u03A1",RightAngleBracket:"\u27E9",RightArrow:"\u2192",RightArrowBar:"\u21E5",RightArrowLeftArrow:"\u21C4",RightCeiling:"\u2309",RightDoubleBracket:"\u27E7",RightDownTeeVector:"\u295D",RightDownVector:"\u21C2",RightDownVectorBar:"\u2955",RightFloor:"\u230B",RightTee:"\u22A2",RightTeeArrow:"\u21A6",RightTeeVector:"\u295B",RightTriangle:"\u22B3",RightTriangleBar:"\u29D0",RightTriangleEqual:"\u22B5",RightUpDownVector:"\u294F",RightUpTeeVector:"\u295C",RightUpVector:"\u21BE",RightUpVectorBar:"\u2954",RightVector:"\u21C0",RightVectorBar:"\u2953",Rightarrow:"\u21D2",Ropf:"\u211D",RoundImplies:"\u2970",Rrightarrow:"\u21DB",Rscr:"\u211B",Rsh:"\u21B1",RuleDelayed:"\u29F4",SHCHcy:"\u0429",SHcy:"\u0428",SOFTcy:"\u042C",Sacute:"\u015A",Sc:"\u2ABC",Scaron:"\u0160",Scedil:"\u015E",Scirc:"\u015C",Scy:"\u0421",Sfr:"\u{1D516}",ShortDownArrow:"\u2193",ShortLeftArrow:"\u2190",ShortRightArrow:"\u2192",ShortUpArrow:"\u2191",Sigma:"\u03A3",SmallCircle:"\u2218",Sopf:"\u{1D54A}",Sqrt:"\u221A",Square:"\u25A1",SquareIntersection:"\u2293",SquareSubset:"\u228F",SquareSubsetEqual:"\u2291",SquareSuperset:"\u2290",SquareSupersetEqual:"\u2292",SquareUnion:"\u2294",Sscr:"\u{1D4AE}",Star:"\u22C6",Sub:"\u22D0",Subset:"\u22D0",SubsetEqual:"\u2286",Succeeds:"\u227B",SucceedsEqual:"\u2AB0",SucceedsSlantEqual:"\u227D",SucceedsTilde:"\u227F",SuchThat:"\u220B",Sum:"\u2211",Sup:"\u22D1",Superset:"\u2283",SupersetEqual:"\u2287",Supset:"\u22D1",THORN:"\xDE",TRADE:"\u2122",TSHcy:"\u040B",TScy:"\u0426",Tab:"	",Tau:"\u03A4",Tcaron:"\u0164",Tcedil:"\u0162",Tcy:"\u0422",Tfr:"\u{1D517}",Therefore:"\u2234",Theta:"\u0398",ThickSpace:"\u205F\u200A",ThinSpace:"\u2009",Tilde:"\u223C",TildeEqual:"\u2243",TildeFullEqual:"\u2245",TildeTilde:"\u2248",Topf:"\u{1D54B}",TripleDot:"\u20DB",Tscr:"\u{1D4AF}",Tstrok:"\u0166",Uacute:"\xDA",Uarr:"\u219F",Uarrocir:"\u2949",Ubrcy:"\u040E",Ubreve:"\u016C",Ucirc:"\xDB",Ucy:"\u0423",Udblac:"\u0170",Ufr:"\u{1D518}",Ugrave:"\xD9",Umacr:"\u016A",UnderBar:"_",UnderBrace:"\u23DF",UnderBracket:"\u23B5",UnderParenthesis:"\u23DD",Union:"\u22C3",UnionPlus:"\u228E",Uogon:"\u0172",Uopf:"\u{1D54C}",UpArrow:"\u2191",UpArrowBar:"\u2912",UpArrowDownArrow:"\u21C5",UpDownArrow:"\u2195",UpEquilibrium:"\u296E",UpTee:"\u22A5",UpTeeArrow:"\u21A5",Uparrow:"\u21D1",Updownarrow:"\u21D5",UpperLeftArrow:"\u2196",UpperRightArrow:"\u2197",Upsi:"\u03D2",Upsilon:"\u03A5",Uring:"\u016E",Uscr:"\u{1D4B0}",Utilde:"\u0168",Uuml:"\xDC",VDash:"\u22AB",Vbar:"\u2AEB",Vcy:"\u0412",Vdash:"\u22A9",Vdashl:"\u2AE6",Vee:"\u22C1",Verbar:"\u2016",Vert:"\u2016",VerticalBar:"\u2223",VerticalLine:"|",VerticalSeparator:"\u2758",VerticalTilde:"\u2240",VeryThinSpace:"\u200A",Vfr:"\u{1D519}",Vopf:"\u{1D54D}",Vscr:"\u{1D4B1}",Vvdash:"\u22AA",Wcirc:"\u0174",Wedge:"\u22C0",Wfr:"\u{1D51A}",Wopf:"\u{1D54E}",Wscr:"\u{1D4B2}",Xfr:"\u{1D51B}",Xi:"\u039E",Xopf:"\u{1D54F}",Xscr:"\u{1D4B3}",YAcy:"\u042F",YIcy:"\u0407",YUcy:"\u042E",Yacute:"\xDD",Ycirc:"\u0176",Ycy:"\u042B",Yfr:"\u{1D51C}",Yopf:"\u{1D550}",Yscr:"\u{1D4B4}",Yuml:"\u0178",ZHcy:"\u0416",Zacute:"\u0179",Zcaron:"\u017D",Zcy:"\u0417",Zdot:"\u017B",ZeroWidthSpace:"\u200B",Zeta:"\u0396",Zfr:"\u2128",Zopf:"\u2124",Zscr:"\u{1D4B5}",aacute:"\xE1",abreve:"\u0103",ac:"\u223E",acE:"\u223E\u0333",acd:"\u223F",acirc:"\xE2",acute:"\xB4",acy:"\u0430",aelig:"\xE6",af:"\u2061",afr:"\u{1D51E}",agrave:"\xE0",alefsym:"\u2135",aleph:"\u2135",alpha:"\u03B1",amacr:"\u0101",amalg:"\u2A3F",amp:"&",and:"\u2227",andand:"\u2A55",andd:"\u2A5C",andslope:"\u2A58",andv:"\u2A5A",ang:"\u2220",ange:"\u29A4",angle:"\u2220",angmsd:"\u2221",angmsdaa:"\u29A8",angmsdab:"\u29A9",angmsdac:"\u29AA",angmsdad:"\u29AB",angmsdae:"\u29AC",angmsdaf:"\u29AD",angmsdag:"\u29AE",angmsdah:"\u29AF",angrt:"\u221F",angrtvb:"\u22BE",angrtvbd:"\u299D",angsph:"\u2222",angst:"\xC5",angzarr:"\u237C",aogon:"\u0105",aopf:"\u{1D552}",ap:"\u2248",apE:"\u2A70",apacir:"\u2A6F",ape:"\u224A",apid:"\u224B",apos:"'",approx:"\u2248",approxeq:"\u224A",aring:"\xE5",ascr:"\u{1D4B6}",ast:"*",asymp:"\u2248",asympeq:"\u224D",atilde:"\xE3",auml:"\xE4",awconint:"\u2233",awint:"\u2A11",bNot:"\u2AED",backcong:"\u224C",backepsilon:"\u03F6",backprime:"\u2035",backsim:"\u223D",backsimeq:"\u22CD",barvee:"\u22BD",barwed:"\u2305",barwedge:"\u2305",bbrk:"\u23B5",bbrktbrk:"\u23B6",bcong:"\u224C",bcy:"\u0431",bdquo:"\u201E",becaus:"\u2235",because:"\u2235",bemptyv:"\u29B0",bepsi:"\u03F6",bernou:"\u212C",beta:"\u03B2",beth:"\u2136",between:"\u226C",bfr:"\u{1D51F}",bigcap:"\u22C2",bigcirc:"\u25EF",bigcup:"\u22C3",bigodot:"\u2A00",bigoplus:"\u2A01",bigotimes:"\u2A02",bigsqcup:"\u2A06",bigstar:"\u2605",bigtriangledown:"\u25BD",bigtriangleup:"\u25B3",biguplus:"\u2A04",bigvee:"\u22C1",bigwedge:"\u22C0",bkarow:"\u290D",blacklozenge:"\u29EB",blacksquare:"\u25AA",blacktriangle:"\u25B4",blacktriangledown:"\u25BE",blacktriangleleft:"\u25C2",blacktriangleright:"\u25B8",blank:"\u2423",blk12:"\u2592",blk14:"\u2591",blk34:"\u2593",block:"\u2588",bne:"=\u20E5",bnequiv:"\u2261\u20E5",bnot:"\u2310",bopf:"\u{1D553}",bot:"\u22A5",bottom:"\u22A5",bowtie:"\u22C8",boxDL:"\u2557",boxDR:"\u2554",boxDl:"\u2556",boxDr:"\u2553",boxH:"\u2550",boxHD:"\u2566",boxHU:"\u2569",boxHd:"\u2564",boxHu:"\u2567",boxUL:"\u255D",boxUR:"\u255A",boxUl:"\u255C",boxUr:"\u2559",boxV:"\u2551",boxVH:"\u256C",boxVL:"\u2563",boxVR:"\u2560",boxVh:"\u256B",boxVl:"\u2562",boxVr:"\u255F",boxbox:"\u29C9",boxdL:"\u2555",boxdR:"\u2552",boxdl:"\u2510",boxdr:"\u250C",boxh:"\u2500",boxhD:"\u2565",boxhU:"\u2568",boxhd:"\u252C",boxhu:"\u2534",boxminus:"\u229F",boxplus:"\u229E",boxtimes:"\u22A0",boxuL:"\u255B",boxuR:"\u2558",boxul:"\u2518",boxur:"\u2514",boxv:"\u2502",boxvH:"\u256A",boxvL:"\u2561",boxvR:"\u255E",boxvh:"\u253C",boxvl:"\u2524",boxvr:"\u251C",bprime:"\u2035",breve:"\u02D8",brvbar:"\xA6",bscr:"\u{1D4B7}",bsemi:"\u204F",bsim:"\u223D",bsime:"\u22CD",bsol:"\\",bsolb:"\u29C5",bsolhsub:"\u27C8",bull:"\u2022",bullet:"\u2022",bump:"\u224E",bumpE:"\u2AAE",bumpe:"\u224F",bumpeq:"\u224F",cacute:"\u0107",cap:"\u2229",capand:"\u2A44",capbrcup:"\u2A49",capcap:"\u2A4B",capcup:"\u2A47",capdot:"\u2A40",caps:"\u2229\uFE00",caret:"\u2041",caron:"\u02C7",ccaps:"\u2A4D",ccaron:"\u010D",ccedil:"\xE7",ccirc:"\u0109",ccups:"\u2A4C",ccupssm:"\u2A50",cdot:"\u010B",cedil:"\xB8",cemptyv:"\u29B2",cent:"\xA2",centerdot:"\xB7",cfr:"\u{1D520}",chcy:"\u0447",check:"\u2713",checkmark:"\u2713",chi:"\u03C7",cir:"\u25CB",cirE:"\u29C3",circ:"\u02C6",circeq:"\u2257",circlearrowleft:"\u21BA",circlearrowright:"\u21BB",circledR:"\xAE",circledS:"\u24C8",circledast:"\u229B",circledcirc:"\u229A",circleddash:"\u229D",cire:"\u2257",cirfnint:"\u2A10",cirmid:"\u2AEF",cirscir:"\u29C2",clubs:"\u2663",clubsuit:"\u2663",colon:":",colone:"\u2254",coloneq:"\u2254",comma:",",commat:"@",comp:"\u2201",compfn:"\u2218",complement:"\u2201",complexes:"\u2102",cong:"\u2245",congdot:"\u2A6D",conint:"\u222E",copf:"\u{1D554}",coprod:"\u2210",copy:"\xA9",copysr:"\u2117",crarr:"\u21B5",cross:"\u2717",cscr:"\u{1D4B8}",csub:"\u2ACF",csube:"\u2AD1",csup:"\u2AD0",csupe:"\u2AD2",ctdot:"\u22EF",cudarrl:"\u2938",cudarrr:"\u2935",cuepr:"\u22DE",cuesc:"\u22DF",cularr:"\u21B6",cularrp:"\u293D",cup:"\u222A",cupbrcap:"\u2A48",cupcap:"\u2A46",cupcup:"\u2A4A",cupdot:"\u228D",cupor:"\u2A45",cups:"\u222A\uFE00",curarr:"\u21B7",curarrm:"\u293C",curlyeqprec:"\u22DE",curlyeqsucc:"\u22DF",curlyvee:"\u22CE",curlywedge:"\u22CF",curren:"\xA4",curvearrowleft:"\u21B6",curvearrowright:"\u21B7",cuvee:"\u22CE",cuwed:"\u22CF",cwconint:"\u2232",cwint:"\u2231",cylcty:"\u232D",dArr:"\u21D3",dHar:"\u2965",dagger:"\u2020",daleth:"\u2138",darr:"\u2193",dash:"\u2010",dashv:"\u22A3",dbkarow:"\u290F",dblac:"\u02DD",dcaron:"\u010F",dcy:"\u0434",dd:"\u2146",ddagger:"\u2021",ddarr:"\u21CA",ddotseq:"\u2A77",deg:"\xB0",delta:"\u03B4",demptyv:"\u29B1",dfisht:"\u297F",dfr:"\u{1D521}",dharl:"\u21C3",dharr:"\u21C2",diam:"\u22C4",diamond:"\u22C4",diamondsuit:"\u2666",diams:"\u2666",die:"\xA8",digamma:"\u03DD",disin:"\u22F2",div:"\xF7",divide:"\xF7",divideontimes:"\u22C7",divonx:"\u22C7",djcy:"\u0452",dlcorn:"\u231E",dlcrop:"\u230D",dollar:"$",dopf:"\u{1D555}",dot:"\u02D9",doteq:"\u2250",doteqdot:"\u2251",dotminus:"\u2238",dotplus:"\u2214",dotsquare:"\u22A1",doublebarwedge:"\u2306",downarrow:"\u2193",downdownarrows:"\u21CA",downharpoonleft:"\u21C3",downharpoonright:"\u21C2",drbkarow:"\u2910",drcorn:"\u231F",drcrop:"\u230C",dscr:"\u{1D4B9}",dscy:"\u0455",dsol:"\u29F6",dstrok:"\u0111",dtdot:"\u22F1",dtri:"\u25BF",dtrif:"\u25BE",duarr:"\u21F5",duhar:"\u296F",dwangle:"\u29A6",dzcy:"\u045F",dzigrarr:"\u27FF",eDDot:"\u2A77",eDot:"\u2251",eacute:"\xE9",easter:"\u2A6E",ecaron:"\u011B",ecir:"\u2256",ecirc:"\xEA",ecolon:"\u2255",ecy:"\u044D",edot:"\u0117",ee:"\u2147",efDot:"\u2252",efr:"\u{1D522}",eg:"\u2A9A",egrave:"\xE8",egs:"\u2A96",egsdot:"\u2A98",el:"\u2A99",elinters:"\u23E7",ell:"\u2113",els:"\u2A95",elsdot:"\u2A97",emacr:"\u0113",empty:"\u2205",emptyset:"\u2205",emptyv:"\u2205",emsp13:"\u2004",emsp14:"\u2005",emsp:"\u2003",eng:"\u014B",ensp:"\u2002",eogon:"\u0119",eopf:"\u{1D556}",epar:"\u22D5",eparsl:"\u29E3",eplus:"\u2A71",epsi:"\u03B5",epsilon:"\u03B5",epsiv:"\u03F5",eqcirc:"\u2256",eqcolon:"\u2255",eqsim:"\u2242",eqslantgtr:"\u2A96",eqslantless:"\u2A95",equals:"=",equest:"\u225F",equiv:"\u2261",equivDD:"\u2A78",eqvparsl:"\u29E5",erDot:"\u2253",erarr:"\u2971",escr:"\u212F",esdot:"\u2250",esim:"\u2242",eta:"\u03B7",eth:"\xF0",euml:"\xEB",euro:"\u20AC",excl:"!",exist:"\u2203",expectation:"\u2130",exponentiale:"\u2147",fallingdotseq:"\u2252",fcy:"\u0444",female:"\u2640",ffilig:"\uFB03",fflig:"\uFB00",ffllig:"\uFB04",ffr:"\u{1D523}",filig:"\uFB01",fjlig:"fj",flat:"\u266D",fllig:"\uFB02",fltns:"\u25B1",fnof:"\u0192",fopf:"\u{1D557}",forall:"\u2200",fork:"\u22D4",forkv:"\u2AD9",fpartint:"\u2A0D",frac12:"\xBD",frac13:"\u2153",frac14:"\xBC",frac15:"\u2155",frac16:"\u2159",frac18:"\u215B",frac23:"\u2154",frac25:"\u2156",frac34:"\xBE",frac35:"\u2157",frac38:"\u215C",frac45:"\u2158",frac56:"\u215A",frac58:"\u215D",frac78:"\u215E",frasl:"\u2044",frown:"\u2322",fscr:"\u{1D4BB}",gE:"\u2267",gEl:"\u2A8C",gacute:"\u01F5",gamma:"\u03B3",gammad:"\u03DD",gap:"\u2A86",gbreve:"\u011F",gcirc:"\u011D",gcy:"\u0433",gdot:"\u0121",ge:"\u2265",gel:"\u22DB",geq:"\u2265",geqq:"\u2267",geqslant:"\u2A7E",ges:"\u2A7E",gescc:"\u2AA9",gesdot:"\u2A80",gesdoto:"\u2A82",gesdotol:"\u2A84",gesl:"\u22DB\uFE00",gesles:"\u2A94",gfr:"\u{1D524}",gg:"\u226B",ggg:"\u22D9",gimel:"\u2137",gjcy:"\u0453",gl:"\u2277",glE:"\u2A92",gla:"\u2AA5",glj:"\u2AA4",gnE:"\u2269",gnap:"\u2A8A",gnapprox:"\u2A8A",gne:"\u2A88",gneq:"\u2A88",gneqq:"\u2269",gnsim:"\u22E7",gopf:"\u{1D558}",grave:"`",gscr:"\u210A",gsim:"\u2273",gsime:"\u2A8E",gsiml:"\u2A90",gt:">",gtcc:"\u2AA7",gtcir:"\u2A7A",gtdot:"\u22D7",gtlPar:"\u2995",gtquest:"\u2A7C",gtrapprox:"\u2A86",gtrarr:"\u2978",gtrdot:"\u22D7",gtreqless:"\u22DB",gtreqqless:"\u2A8C",gtrless:"\u2277",gtrsim:"\u2273",gvertneqq:"\u2269\uFE00",gvnE:"\u2269\uFE00",hArr:"\u21D4",hairsp:"\u200A",half:"\xBD",hamilt:"\u210B",hardcy:"\u044A",harr:"\u2194",harrcir:"\u2948",harrw:"\u21AD",hbar:"\u210F",hcirc:"\u0125",hearts:"\u2665",heartsuit:"\u2665",hellip:"\u2026",hercon:"\u22B9",hfr:"\u{1D525}",hksearow:"\u2925",hkswarow:"\u2926",hoarr:"\u21FF",homtht:"\u223B",hookleftarrow:"\u21A9",hookrightarrow:"\u21AA",hopf:"\u{1D559}",horbar:"\u2015",hscr:"\u{1D4BD}",hslash:"\u210F",hstrok:"\u0127",hybull:"\u2043",hyphen:"\u2010",iacute:"\xED",ic:"\u2063",icirc:"\xEE",icy:"\u0438",iecy:"\u0435",iexcl:"\xA1",iff:"\u21D4",ifr:"\u{1D526}",igrave:"\xEC",ii:"\u2148",iiiint:"\u2A0C",iiint:"\u222D",iinfin:"\u29DC",iiota:"\u2129",ijlig:"\u0133",imacr:"\u012B",image:"\u2111",imagline:"\u2110",imagpart:"\u2111",imath:"\u0131",imof:"\u22B7",imped:"\u01B5",in:"\u2208",incare:"\u2105",infin:"\u221E",infintie:"\u29DD",inodot:"\u0131",int:"\u222B",intcal:"\u22BA",integers:"\u2124",intercal:"\u22BA",intlarhk:"\u2A17",intprod:"\u2A3C",iocy:"\u0451",iogon:"\u012F",iopf:"\u{1D55A}",iota:"\u03B9",iprod:"\u2A3C",iquest:"\xBF",iscr:"\u{1D4BE}",isin:"\u2208",isinE:"\u22F9",isindot:"\u22F5",isins:"\u22F4",isinsv:"\u22F3",isinv:"\u2208",it:"\u2062",itilde:"\u0129",iukcy:"\u0456",iuml:"\xEF",jcirc:"\u0135",jcy:"\u0439",jfr:"\u{1D527}",jmath:"\u0237",jopf:"\u{1D55B}",jscr:"\u{1D4BF}",jsercy:"\u0458",jukcy:"\u0454",kappa:"\u03BA",kappav:"\u03F0",kcedil:"\u0137",kcy:"\u043A",kfr:"\u{1D528}",kgreen:"\u0138",khcy:"\u0445",kjcy:"\u045C",kopf:"\u{1D55C}",kscr:"\u{1D4C0}",lAarr:"\u21DA",lArr:"\u21D0",lAtail:"\u291B",lBarr:"\u290E",lE:"\u2266",lEg:"\u2A8B",lHar:"\u2962",lacute:"\u013A",laemptyv:"\u29B4",lagran:"\u2112",lambda:"\u03BB",lang:"\u27E8",langd:"\u2991",langle:"\u27E8",lap:"\u2A85",laquo:"\xAB",larr:"\u2190",larrb:"\u21E4",larrbfs:"\u291F",larrfs:"\u291D",larrhk:"\u21A9",larrlp:"\u21AB",larrpl:"\u2939",larrsim:"\u2973",larrtl:"\u21A2",lat:"\u2AAB",latail:"\u2919",late:"\u2AAD",lates:"\u2AAD\uFE00",lbarr:"\u290C",lbbrk:"\u2772",lbrace:"{",lbrack:"[",lbrke:"\u298B",lbrksld:"\u298F",lbrkslu:"\u298D",lcaron:"\u013E",lcedil:"\u013C",lceil:"\u2308",lcub:"{",lcy:"\u043B",ldca:"\u2936",ldquo:"\u201C",ldquor:"\u201E",ldrdhar:"\u2967",ldrushar:"\u294B",ldsh:"\u21B2",le:"\u2264",leftarrow:"\u2190",leftarrowtail:"\u21A2",leftharpoondown:"\u21BD",leftharpoonup:"\u21BC",leftleftarrows:"\u21C7",leftrightarrow:"\u2194",leftrightarrows:"\u21C6",leftrightharpoons:"\u21CB",leftrightsquigarrow:"\u21AD",leftthreetimes:"\u22CB",leg:"\u22DA",leq:"\u2264",leqq:"\u2266",leqslant:"\u2A7D",les:"\u2A7D",lescc:"\u2AA8",lesdot:"\u2A7F",lesdoto:"\u2A81",lesdotor:"\u2A83",lesg:"\u22DA\uFE00",lesges:"\u2A93",lessapprox:"\u2A85",lessdot:"\u22D6",lesseqgtr:"\u22DA",lesseqqgtr:"\u2A8B",lessgtr:"\u2276",lesssim:"\u2272",lfisht:"\u297C",lfloor:"\u230A",lfr:"\u{1D529}",lg:"\u2276",lgE:"\u2A91",lhard:"\u21BD",lharu:"\u21BC",lharul:"\u296A",lhblk:"\u2584",ljcy:"\u0459",ll:"\u226A",llarr:"\u21C7",llcorner:"\u231E",llhard:"\u296B",lltri:"\u25FA",lmidot:"\u0140",lmoust:"\u23B0",lmoustache:"\u23B0",lnE:"\u2268",lnap:"\u2A89",lnapprox:"\u2A89",lne:"\u2A87",lneq:"\u2A87",lneqq:"\u2268",lnsim:"\u22E6",loang:"\u27EC",loarr:"\u21FD",lobrk:"\u27E6",longleftarrow:"\u27F5",longleftrightarrow:"\u27F7",longmapsto:"\u27FC",longrightarrow:"\u27F6",looparrowleft:"\u21AB",looparrowright:"\u21AC",lopar:"\u2985",lopf:"\u{1D55D}",loplus:"\u2A2D",lotimes:"\u2A34",lowast:"\u2217",lowbar:"_",loz:"\u25CA",lozenge:"\u25CA",lozf:"\u29EB",lpar:"(",lparlt:"\u2993",lrarr:"\u21C6",lrcorner:"\u231F",lrhar:"\u21CB",lrhard:"\u296D",lrm:"\u200E",lrtri:"\u22BF",lsaquo:"\u2039",lscr:"\u{1D4C1}",lsh:"\u21B0",lsim:"\u2272",lsime:"\u2A8D",lsimg:"\u2A8F",lsqb:"[",lsquo:"\u2018",lsquor:"\u201A",lstrok:"\u0142",lt:"<",ltcc:"\u2AA6",ltcir:"\u2A79",ltdot:"\u22D6",lthree:"\u22CB",ltimes:"\u22C9",ltlarr:"\u2976",ltquest:"\u2A7B",ltrPar:"\u2996",ltri:"\u25C3",ltrie:"\u22B4",ltrif:"\u25C2",lurdshar:"\u294A",luruhar:"\u2966",lvertneqq:"\u2268\uFE00",lvnE:"\u2268\uFE00",mDDot:"\u223A",macr:"\xAF",male:"\u2642",malt:"\u2720",maltese:"\u2720",map:"\u21A6",mapsto:"\u21A6",mapstodown:"\u21A7",mapstoleft:"\u21A4",mapstoup:"\u21A5",marker:"\u25AE",mcomma:"\u2A29",mcy:"\u043C",mdash:"\u2014",measuredangle:"\u2221",mfr:"\u{1D52A}",mho:"\u2127",micro:"\xB5",mid:"\u2223",midast:"*",midcir:"\u2AF0",middot:"\xB7",minus:"\u2212",minusb:"\u229F",minusd:"\u2238",minusdu:"\u2A2A",mlcp:"\u2ADB",mldr:"\u2026",mnplus:"\u2213",models:"\u22A7",mopf:"\u{1D55E}",mp:"\u2213",mscr:"\u{1D4C2}",mstpos:"\u223E",mu:"\u03BC",multimap:"\u22B8",mumap:"\u22B8",nGg:"\u22D9\u0338",nGt:"\u226B\u20D2",nGtv:"\u226B\u0338",nLeftarrow:"\u21CD",nLeftrightarrow:"\u21CE",nLl:"\u22D8\u0338",nLt:"\u226A\u20D2",nLtv:"\u226A\u0338",nRightarrow:"\u21CF",nVDash:"\u22AF",nVdash:"\u22AE",nabla:"\u2207",nacute:"\u0144",nang:"\u2220\u20D2",nap:"\u2249",napE:"\u2A70\u0338",napid:"\u224B\u0338",napos:"\u0149",napprox:"\u2249",natur:"\u266E",natural:"\u266E",naturals:"\u2115",nbsp:"\xA0",nbump:"\u224E\u0338",nbumpe:"\u224F\u0338",ncap:"\u2A43",ncaron:"\u0148",ncedil:"\u0146",ncong:"\u2247",ncongdot:"\u2A6D\u0338",ncup:"\u2A42",ncy:"\u043D",ndash:"\u2013",ne:"\u2260",neArr:"\u21D7",nearhk:"\u2924",nearr:"\u2197",nearrow:"\u2197",nedot:"\u2250\u0338",nequiv:"\u2262",nesear:"\u2928",nesim:"\u2242\u0338",nexist:"\u2204",nexists:"\u2204",nfr:"\u{1D52B}",ngE:"\u2267\u0338",nge:"\u2271",ngeq:"\u2271",ngeqq:"\u2267\u0338",ngeqslant:"\u2A7E\u0338",nges:"\u2A7E\u0338",ngsim:"\u2275",ngt:"\u226F",ngtr:"\u226F",nhArr:"\u21CE",nharr:"\u21AE",nhpar:"\u2AF2",ni:"\u220B",nis:"\u22FC",nisd:"\u22FA",niv:"\u220B",njcy:"\u045A",nlArr:"\u21CD",nlE:"\u2266\u0338",nlarr:"\u219A",nldr:"\u2025",nle:"\u2270",nleftarrow:"\u219A",nleftrightarrow:"\u21AE",nleq:"\u2270",nleqq:"\u2266\u0338",nleqslant:"\u2A7D\u0338",nles:"\u2A7D\u0338",nless:"\u226E",nlsim:"\u2274",nlt:"\u226E",nltri:"\u22EA",nltrie:"\u22EC",nmid:"\u2224",nopf:"\u{1D55F}",not:"\xAC",notin:"\u2209",notinE:"\u22F9\u0338",notindot:"\u22F5\u0338",notinva:"\u2209",notinvb:"\u22F7",notinvc:"\u22F6",notni:"\u220C",notniva:"\u220C",notnivb:"\u22FE",notnivc:"\u22FD",npar:"\u2226",nparallel:"\u2226",nparsl:"\u2AFD\u20E5",npart:"\u2202\u0338",npolint:"\u2A14",npr:"\u2280",nprcue:"\u22E0",npre:"\u2AAF\u0338",nprec:"\u2280",npreceq:"\u2AAF\u0338",nrArr:"\u21CF",nrarr:"\u219B",nrarrc:"\u2933\u0338",nrarrw:"\u219D\u0338",nrightarrow:"\u219B",nrtri:"\u22EB",nrtrie:"\u22ED",nsc:"\u2281",nsccue:"\u22E1",nsce:"\u2AB0\u0338",nscr:"\u{1D4C3}",nshortmid:"\u2224",nshortparallel:"\u2226",nsim:"\u2241",nsime:"\u2244",nsimeq:"\u2244",nsmid:"\u2224",nspar:"\u2226",nsqsube:"\u22E2",nsqsupe:"\u22E3",nsub:"\u2284",nsubE:"\u2AC5\u0338",nsube:"\u2288",nsubset:"\u2282\u20D2",nsubseteq:"\u2288",nsubseteqq:"\u2AC5\u0338",nsucc:"\u2281",nsucceq:"\u2AB0\u0338",nsup:"\u2285",nsupE:"\u2AC6\u0338",nsupe:"\u2289",nsupset:"\u2283\u20D2",nsupseteq:"\u2289",nsupseteqq:"\u2AC6\u0338",ntgl:"\u2279",ntilde:"\xF1",ntlg:"\u2278",ntriangleleft:"\u22EA",ntrianglelefteq:"\u22EC",ntriangleright:"\u22EB",ntrianglerighteq:"\u22ED",nu:"\u03BD",num:"#",numero:"\u2116",numsp:"\u2007",nvDash:"\u22AD",nvHarr:"\u2904",nvap:"\u224D\u20D2",nvdash:"\u22AC",nvge:"\u2265\u20D2",nvgt:">\u20D2",nvinfin:"\u29DE",nvlArr:"\u2902",nvle:"\u2264\u20D2",nvlt:"<\u20D2",nvltrie:"\u22B4\u20D2",nvrArr:"\u2903",nvrtrie:"\u22B5\u20D2",nvsim:"\u223C\u20D2",nwArr:"\u21D6",nwarhk:"\u2923",nwarr:"\u2196",nwarrow:"\u2196",nwnear:"\u2927",oS:"\u24C8",oacute:"\xF3",oast:"\u229B",ocir:"\u229A",ocirc:"\xF4",ocy:"\u043E",odash:"\u229D",odblac:"\u0151",odiv:"\u2A38",odot:"\u2299",odsold:"\u29BC",oelig:"\u0153",ofcir:"\u29BF",ofr:"\u{1D52C}",ogon:"\u02DB",ograve:"\xF2",ogt:"\u29C1",ohbar:"\u29B5",ohm:"\u03A9",oint:"\u222E",olarr:"\u21BA",olcir:"\u29BE",olcross:"\u29BB",oline:"\u203E",olt:"\u29C0",omacr:"\u014D",omega:"\u03C9",omicron:"\u03BF",omid:"\u29B6",ominus:"\u2296",oopf:"\u{1D560}",opar:"\u29B7",operp:"\u29B9",oplus:"\u2295",or:"\u2228",orarr:"\u21BB",ord:"\u2A5D",order:"\u2134",orderof:"\u2134",ordf:"\xAA",ordm:"\xBA",origof:"\u22B6",oror:"\u2A56",orslope:"\u2A57",orv:"\u2A5B",oscr:"\u2134",oslash:"\xF8",osol:"\u2298",otilde:"\xF5",otimes:"\u2297",otimesas:"\u2A36",ouml:"\xF6",ovbar:"\u233D",par:"\u2225",para:"\xB6",parallel:"\u2225",parsim:"\u2AF3",parsl:"\u2AFD",part:"\u2202",pcy:"\u043F",percnt:"%",period:".",permil:"\u2030",perp:"\u22A5",pertenk:"\u2031",pfr:"\u{1D52D}",phi:"\u03C6",phiv:"\u03D5",phmmat:"\u2133",phone:"\u260E",pi:"\u03C0",pitchfork:"\u22D4",piv:"\u03D6",planck:"\u210F",planckh:"\u210E",plankv:"\u210F",plus:"+",plusacir:"\u2A23",plusb:"\u229E",pluscir:"\u2A22",plusdo:"\u2214",plusdu:"\u2A25",pluse:"\u2A72",plusmn:"\xB1",plussim:"\u2A26",plustwo:"\u2A27",pm:"\xB1",pointint:"\u2A15",popf:"\u{1D561}",pound:"\xA3",pr:"\u227A",prE:"\u2AB3",prap:"\u2AB7",prcue:"\u227C",pre:"\u2AAF",prec:"\u227A",precapprox:"\u2AB7",preccurlyeq:"\u227C",preceq:"\u2AAF",precnapprox:"\u2AB9",precneqq:"\u2AB5",precnsim:"\u22E8",precsim:"\u227E",prime:"\u2032",primes:"\u2119",prnE:"\u2AB5",prnap:"\u2AB9",prnsim:"\u22E8",prod:"\u220F",profalar:"\u232E",profline:"\u2312",profsurf:"\u2313",prop:"\u221D",propto:"\u221D",prsim:"\u227E",prurel:"\u22B0",pscr:"\u{1D4C5}",psi:"\u03C8",puncsp:"\u2008",qfr:"\u{1D52E}",qint:"\u2A0C",qopf:"\u{1D562}",qprime:"\u2057",qscr:"\u{1D4C6}",quaternions:"\u210D",quatint:"\u2A16",quest:"?",questeq:"\u225F",quot:'"',rAarr:"\u21DB",rArr:"\u21D2",rAtail:"\u291C",rBarr:"\u290F",rHar:"\u2964",race:"\u223D\u0331",racute:"\u0155",radic:"\u221A",raemptyv:"\u29B3",rang:"\u27E9",rangd:"\u2992",range:"\u29A5",rangle:"\u27E9",raquo:"\xBB",rarr:"\u2192",rarrap:"\u2975",rarrb:"\u21E5",rarrbfs:"\u2920",rarrc:"\u2933",rarrfs:"\u291E",rarrhk:"\u21AA",rarrlp:"\u21AC",rarrpl:"\u2945",rarrsim:"\u2974",rarrtl:"\u21A3",rarrw:"\u219D",ratail:"\u291A",ratio:"\u2236",rationals:"\u211A",rbarr:"\u290D",rbbrk:"\u2773",rbrace:"}",rbrack:"]",rbrke:"\u298C",rbrksld:"\u298E",rbrkslu:"\u2990",rcaron:"\u0159",rcedil:"\u0157",rceil:"\u2309",rcub:"}",rcy:"\u0440",rdca:"\u2937",rdldhar:"\u2969",rdquo:"\u201D",rdquor:"\u201D",rdsh:"\u21B3",real:"\u211C",realine:"\u211B",realpart:"\u211C",reals:"\u211D",rect:"\u25AD",reg:"\xAE",rfisht:"\u297D",rfloor:"\u230B",rfr:"\u{1D52F}",rhard:"\u21C1",rharu:"\u21C0",rharul:"\u296C",rho:"\u03C1",rhov:"\u03F1",rightarrow:"\u2192",rightarrowtail:"\u21A3",rightharpoondown:"\u21C1",rightharpoonup:"\u21C0",rightleftarrows:"\u21C4",rightleftharpoons:"\u21CC",rightrightarrows:"\u21C9",rightsquigarrow:"\u219D",rightthreetimes:"\u22CC",ring:"\u02DA",risingdotseq:"\u2253",rlarr:"\u21C4",rlhar:"\u21CC",rlm:"\u200F",rmoust:"\u23B1",rmoustache:"\u23B1",rnmid:"\u2AEE",roang:"\u27ED",roarr:"\u21FE",robrk:"\u27E7",ropar:"\u2986",ropf:"\u{1D563}",roplus:"\u2A2E",rotimes:"\u2A35",rpar:")",rpargt:"\u2994",rppolint:"\u2A12",rrarr:"\u21C9",rsaquo:"\u203A",rscr:"\u{1D4C7}",rsh:"\u21B1",rsqb:"]",rsquo:"\u2019",rsquor:"\u2019",rthree:"\u22CC",rtimes:"\u22CA",rtri:"\u25B9",rtrie:"\u22B5",rtrif:"\u25B8",rtriltri:"\u29CE",ruluhar:"\u2968",rx:"\u211E",sacute:"\u015B",sbquo:"\u201A",sc:"\u227B",scE:"\u2AB4",scap:"\u2AB8",scaron:"\u0161",sccue:"\u227D",sce:"\u2AB0",scedil:"\u015F",scirc:"\u015D",scnE:"\u2AB6",scnap:"\u2ABA",scnsim:"\u22E9",scpolint:"\u2A13",scsim:"\u227F",scy:"\u0441",sdot:"\u22C5",sdotb:"\u22A1",sdote:"\u2A66",seArr:"\u21D8",searhk:"\u2925",searr:"\u2198",searrow:"\u2198",sect:"\xA7",semi:";",seswar:"\u2929",setminus:"\u2216",setmn:"\u2216",sext:"\u2736",sfr:"\u{1D530}",sfrown:"\u2322",sharp:"\u266F",shchcy:"\u0449",shcy:"\u0448",shortmid:"\u2223",shortparallel:"\u2225",shy:"\xAD",sigma:"\u03C3",sigmaf:"\u03C2",sigmav:"\u03C2",sim:"\u223C",simdot:"\u2A6A",sime:"\u2243",simeq:"\u2243",simg:"\u2A9E",simgE:"\u2AA0",siml:"\u2A9D",simlE:"\u2A9F",simne:"\u2246",simplus:"\u2A24",simrarr:"\u2972",slarr:"\u2190",smallsetminus:"\u2216",smashp:"\u2A33",smeparsl:"\u29E4",smid:"\u2223",smile:"\u2323",smt:"\u2AAA",smte:"\u2AAC",smtes:"\u2AAC\uFE00",softcy:"\u044C",sol:"/",solb:"\u29C4",solbar:"\u233F",sopf:"\u{1D564}",spades:"\u2660",spadesuit:"\u2660",spar:"\u2225",sqcap:"\u2293",sqcaps:"\u2293\uFE00",sqcup:"\u2294",sqcups:"\u2294\uFE00",sqsub:"\u228F",sqsube:"\u2291",sqsubset:"\u228F",sqsubseteq:"\u2291",sqsup:"\u2290",sqsupe:"\u2292",sqsupset:"\u2290",sqsupseteq:"\u2292",squ:"\u25A1",square:"\u25A1",squarf:"\u25AA",squf:"\u25AA",srarr:"\u2192",sscr:"\u{1D4C8}",ssetmn:"\u2216",ssmile:"\u2323",sstarf:"\u22C6",star:"\u2606",starf:"\u2605",straightepsilon:"\u03F5",straightphi:"\u03D5",strns:"\xAF",sub:"\u2282",subE:"\u2AC5",subdot:"\u2ABD",sube:"\u2286",subedot:"\u2AC3",submult:"\u2AC1",subnE:"\u2ACB",subne:"\u228A",subplus:"\u2ABF",subrarr:"\u2979",subset:"\u2282",subseteq:"\u2286",subseteqq:"\u2AC5",subsetneq:"\u228A",subsetneqq:"\u2ACB",subsim:"\u2AC7",subsub:"\u2AD5",subsup:"\u2AD3",succ:"\u227B",succapprox:"\u2AB8",succcurlyeq:"\u227D",succeq:"\u2AB0",succnapprox:"\u2ABA",succneqq:"\u2AB6",succnsim:"\u22E9",succsim:"\u227F",sum:"\u2211",sung:"\u266A",sup1:"\xB9",sup2:"\xB2",sup3:"\xB3",sup:"\u2283",supE:"\u2AC6",supdot:"\u2ABE",supdsub:"\u2AD8",supe:"\u2287",supedot:"\u2AC4",suphsol:"\u27C9",suphsub:"\u2AD7",suplarr:"\u297B",supmult:"\u2AC2",supnE:"\u2ACC",supne:"\u228B",supplus:"\u2AC0",supset:"\u2283",supseteq:"\u2287",supseteqq:"\u2AC6",supsetneq:"\u228B",supsetneqq:"\u2ACC",supsim:"\u2AC8",supsub:"\u2AD4",supsup:"\u2AD6",swArr:"\u21D9",swarhk:"\u2926",swarr:"\u2199",swarrow:"\u2199",swnwar:"\u292A",szlig:"\xDF",target:"\u2316",tau:"\u03C4",tbrk:"\u23B4",tcaron:"\u0165",tcedil:"\u0163",tcy:"\u0442",tdot:"\u20DB",telrec:"\u2315",tfr:"\u{1D531}",there4:"\u2234",therefore:"\u2234",theta:"\u03B8",thetasym:"\u03D1",thetav:"\u03D1",thickapprox:"\u2248",thicksim:"\u223C",thinsp:"\u2009",thkap:"\u2248",thksim:"\u223C",thorn:"\xFE",tilde:"\u02DC",times:"\xD7",timesb:"\u22A0",timesbar:"\u2A31",timesd:"\u2A30",tint:"\u222D",toea:"\u2928",top:"\u22A4",topbot:"\u2336",topcir:"\u2AF1",topf:"\u{1D565}",topfork:"\u2ADA",tosa:"\u2929",tprime:"\u2034",trade:"\u2122",triangle:"\u25B5",triangledown:"\u25BF",triangleleft:"\u25C3",trianglelefteq:"\u22B4",triangleq:"\u225C",triangleright:"\u25B9",trianglerighteq:"\u22B5",tridot:"\u25EC",trie:"\u225C",triminus:"\u2A3A",triplus:"\u2A39",trisb:"\u29CD",tritime:"\u2A3B",trpezium:"\u23E2",tscr:"\u{1D4C9}",tscy:"\u0446",tshcy:"\u045B",tstrok:"\u0167",twixt:"\u226C",twoheadleftarrow:"\u219E",twoheadrightarrow:"\u21A0",uArr:"\u21D1",uHar:"\u2963",uacute:"\xFA",uarr:"\u2191",ubrcy:"\u045E",ubreve:"\u016D",ucirc:"\xFB",ucy:"\u0443",udarr:"\u21C5",udblac:"\u0171",udhar:"\u296E",ufisht:"\u297E",ufr:"\u{1D532}",ugrave:"\xF9",uharl:"\u21BF",uharr:"\u21BE",uhblk:"\u2580",ulcorn:"\u231C",ulcorner:"\u231C",ulcrop:"\u230F",ultri:"\u25F8",umacr:"\u016B",uml:"\xA8",uogon:"\u0173",uopf:"\u{1D566}",uparrow:"\u2191",updownarrow:"\u2195",upharpoonleft:"\u21BF",upharpoonright:"\u21BE",uplus:"\u228E",upsi:"\u03C5",upsih:"\u03D2",upsilon:"\u03C5",upuparrows:"\u21C8",urcorn:"\u231D",urcorner:"\u231D",urcrop:"\u230E",uring:"\u016F",urtri:"\u25F9",uscr:"\u{1D4CA}",utdot:"\u22F0",utilde:"\u0169",utri:"\u25B5",utrif:"\u25B4",uuarr:"\u21C8",uuml:"\xFC",uwangle:"\u29A7",vArr:"\u21D5",vBar:"\u2AE8",vBarv:"\u2AE9",vDash:"\u22A8",vangrt:"\u299C",varepsilon:"\u03F5",varkappa:"\u03F0",varnothing:"\u2205",varphi:"\u03D5",varpi:"\u03D6",varpropto:"\u221D",varr:"\u2195",varrho:"\u03F1",varsigma:"\u03C2",varsubsetneq:"\u228A\uFE00",varsubsetneqq:"\u2ACB\uFE00",varsupsetneq:"\u228B\uFE00",varsupsetneqq:"\u2ACC\uFE00",vartheta:"\u03D1",vartriangleleft:"\u22B2",vartriangleright:"\u22B3",vcy:"\u0432",vdash:"\u22A2",vee:"\u2228",veebar:"\u22BB",veeeq:"\u225A",vellip:"\u22EE",verbar:"|",vert:"|",vfr:"\u{1D533}",vltri:"\u22B2",vnsub:"\u2282\u20D2",vnsup:"\u2283\u20D2",vopf:"\u{1D567}",vprop:"\u221D",vrtri:"\u22B3",vscr:"\u{1D4CB}",vsubnE:"\u2ACB\uFE00",vsubne:"\u228A\uFE00",vsupnE:"\u2ACC\uFE00",vsupne:"\u228B\uFE00",vzigzag:"\u299A",wcirc:"\u0175",wedbar:"\u2A5F",wedge:"\u2227",wedgeq:"\u2259",weierp:"\u2118",wfr:"\u{1D534}",wopf:"\u{1D568}",wp:"\u2118",wr:"\u2240",wreath:"\u2240",wscr:"\u{1D4CC}",xcap:"\u22C2",xcirc:"\u25EF",xcup:"\u22C3",xdtri:"\u25BD",xfr:"\u{1D535}",xhArr:"\u27FA",xharr:"\u27F7",xi:"\u03BE",xlArr:"\u27F8",xlarr:"\u27F5",xmap:"\u27FC",xnis:"\u22FB",xodot:"\u2A00",xopf:"\u{1D569}",xoplus:"\u2A01",xotime:"\u2A02",xrArr:"\u27F9",xrarr:"\u27F6",xscr:"\u{1D4CD}",xsqcup:"\u2A06",xuplus:"\u2A04",xutri:"\u25B3",xvee:"\u22C1",xwedge:"\u22C0",yacute:"\xFD",yacy:"\u044F",ycirc:"\u0177",ycy:"\u044B",yen:"\xA5",yfr:"\u{1D536}",yicy:"\u0457",yopf:"\u{1D56A}",yscr:"\u{1D4CE}",yucy:"\u044E",yuml:"\xFF",zacute:"\u017A",zcaron:"\u017E",zcy:"\u0437",zdot:"\u017C",zeetrf:"\u2128",zeta:"\u03B6",zfr:"\u{1D537}",zhcy:"\u0436",zigrarr:"\u21DD",zopf:"\u{1D56B}",zscr:"\u{1D4CF}",zwj:"\u200D",zwnj:"\u200C"}});function Lt(e){return nk.call(ji,e)?ji[e]:!1}var nk,$r=b(()=>{ql();nk={}.hasOwnProperty});function oe(e,t,r,n){let i=e.length,o=0,a;if(t<0?t=-t>i?0:i+t:t=t>i?i:t,r=r>0?r:0,n.length<1e4)a=Array.from(n),a.unshift(t,r),e.splice(...a);else for(r&&e.splice(t,r);o<n.length;)a=n.slice(o,o+1e4),a.unshift(t,0),e.splice(...a),o+=1e4,t+=1e4}function pe(e,t){return e.length>0?(oe(e,e.length,0,t),e):t}var Ge=b(()=>{});function Rl(e){let t={},r=-1;for(;++r<e.length;)ik(t,e[r]);return t}function ik(e,t){let r;for(r in t){let i=(Nl.call(e,r)?e[r]:void 0)||(e[r]={}),o=t[r],a;if(o)for(a in o){Nl.call(i,a)||(i[a]=[]);let s=o[a];ok(i[a],Array.isArray(s)?s:s?[s]:[])}}}function ok(e,t){let r=-1,n=[];for(;++r<t.length;)(t[r].add==="after"?e:n).push(t[r]);oe(e,0,0,n)}var Nl,Dl=b(()=>{Ge();Nl={}.hasOwnProperty});function Gr(e,t){let r=Number.parseInt(e,t);return r<9||r===11||r>13&&r<32||r>126&&r<160||r>55295&&r<57344||r>64975&&r<65008||(r&65535)===65535||(r&65535)===65534||r>1114111?"\uFFFD":String.fromCodePoint(r)}var Bi=b(()=>{});function We(e){return e.replace(/[\t\n\r ]+/g," ").replace(/^ | $/g,"").toLowerCase().toUpperCase()}var Jr=b(()=>{});function Xt(e){return e!==null&&(e<32||e===127)}function E(e){return e!==null&&e<-2}function Y(e){return e!==null&&(e<0||e===32)}function R(e){return e===-2||e===-1||e===32}function Je(e){return t;function t(r){return r!==null&&r>-1&&e.test(String.fromCharCode(r))}}var ve,he,Ml,Zt,jl,Bl,zl,Vl,$=b(()=>{ve=Je(/[A-Za-z]/),he=Je(/[\dA-Za-z]/),Ml=Je(/[#-'*+\--9=?A-Z^-~]/);Zt=Je(/\d/),jl=Je(/[\dA-Fa-f]/),Bl=Je(/[!-/:-@[-`{-~]/);zl=Je(/\p{P}|\p{S}/u),Vl=Je(/\s/)});function j(e,t,r,n){let i=n?n-1:Number.POSITIVE_INFINITY,o=0;return a;function a(l){return R(l)?(e.enter(r),s(l)):t(l)}function s(l){return R(l)&&o++<i?(e.consume(l),s):(e.exit(r),t(l))}}var re=b(()=>{$()});function ak(e){let t=e.attempt(this.parser.constructs.contentInitial,n,i),r;return t;function n(s){if(s===null){e.consume(s);return}return e.enter("lineEnding"),e.consume(s),e.exit("lineEnding"),j(e,t,"linePrefix")}function i(s){return e.enter("paragraph"),o(s)}function o(s){let l=e.enter("chunkText",{contentType:"text",previous:r});return r&&(r.next=l),r=l,a(s)}function a(s){if(s===null){e.exit("chunkText"),e.exit("paragraph"),e.consume(s);return}return E(s)?(e.consume(s),e.exit("chunkText"),o):(e.consume(s),a)}}var Wl,Ul=b(()=>{re();$();Wl={tokenize:ak}});function sk(e){let t=this,r=[],n=0,i,o,a;return s;function s(_){if(n<r.length){let V=r[n];return t.containerState=V[1],e.attempt(V[0].continuation,l,c)(_)}return c(_)}function l(_){if(n++,t.containerState._closeFlow){t.containerState._closeFlow=void 0,i&&F();let V=t.events.length,I=V,v;for(;I--;)if(t.events[I][0]==="exit"&&t.events[I][1].type==="chunkFlow"){v=t.events[I][1].end;break}k(n);let q=V;for(;q<t.events.length;)t.events[q][1].end={...v},q++;return oe(t.events,I+1,0,t.events.slice(V)),t.events.length=q,c(_)}return s(_)}function c(_){if(n===r.length){if(!i)return p(_);if(i.currentConstruct&&i.currentConstruct.concrete)return w(_);t.interrupt=!!(i.currentConstruct&&!i._gfmTableDynamicInterruptHack)}return t.containerState={},e.check(Hl,u,f)(_)}function u(_){return i&&F(),k(n),p(_)}function f(_){return t.parser.lazy[t.now().line]=n!==r.length,a=t.now().offset,w(_)}function p(_){return t.containerState={},e.attempt(Hl,h,w)(_)}function h(_){return n++,r.push([t.currentConstruct,t.containerState]),p(_)}function w(_){if(_===null){i&&F(),k(0),e.consume(_);return}return i=i||t.parser.flow(t.now()),e.enter("chunkFlow",{_tokenizer:i,contentType:"flow",previous:o}),g(_)}function g(_){if(_===null){x(e.exit("chunkFlow"),!0),k(0),e.consume(_);return}return E(_)?(e.consume(_),x(e.exit("chunkFlow")),n=0,t.interrupt=void 0,s):(e.consume(_),g)}function x(_,V){let I=t.sliceStream(_);if(V&&I.push(null),_.previous=o,o&&(o.next=_),o=_,i.defineSkip(_.start),i.write(I),t.parser.lazy[_.start.line]){let v=i.events.length;for(;v--;)if(i.events[v][1].start.offset<a&&(!i.events[v][1].end||i.events[v][1].end.offset>a))return;let q=t.events.length,D=q,O,P;for(;D--;)if(t.events[D][0]==="exit"&&t.events[D][1].type==="chunkFlow"){if(O){P=t.events[D][1].end;break}O=!0}for(k(n),v=q;v<t.events.length;)t.events[v][1].end={...P},v++;oe(t.events,D+1,0,t.events.slice(q)),t.events.length=v}}function k(_){let V=r.length;for(;V-- >_;){let I=r[V];t.containerState=I[1],I[0].exit.call(t,e)}r.length=_}function F(){i.write([null]),o=void 0,i=void 0,t.containerState._closeFlow=void 0}}function lk(e,t,r){return j(e,e.attempt(this.parser.constructs.document,t,r),"linePrefix",this.parser.constructs.disable.null.includes("codeIndented")?void 0:4)}var $l,Hl,Gl=b(()=>{re();$();Ge();$l={tokenize:sk},Hl={tokenize:lk}});function Tt(e){if(e===null||Y(e)||Vl(e))return 1;if(zl(e))return 2}var zi=b(()=>{$()});function It(e,t,r){let n=[],i=-1;for(;++i<e.length;){let o=e[i].resolveAll;o&&!n.includes(o)&&(t=o(t,r),n.push(o))}return t}var Qr=b(()=>{});function uk(e,t){let r=-1,n,i,o,a,s,l,c,u;for(;++r<e.length;)if(e[r][0]==="enter"&&e[r][1].type==="attentionSequence"&&e[r][1]._close){for(n=r;n--;)if(e[n][0]==="exit"&&e[n][1].type==="attentionSequence"&&e[n][1]._open&&t.sliceSerialize(e[n][1]).charCodeAt(0)===t.sliceSerialize(e[r][1]).charCodeAt(0)){if((e[n][1]._close||e[r][1]._open)&&(e[r][1].end.offset-e[r][1].start.offset)%3&&!((e[n][1].end.offset-e[n][1].start.offset+e[r][1].end.offset-e[r][1].start.offset)%3))continue;l=e[n][1].end.offset-e[n][1].start.offset>1&&e[r][1].end.offset-e[r][1].start.offset>1?2:1;let f={...e[n][1].end},p={...e[r][1].start};Jl(f,-l),Jl(p,l),a={type:l>1?"strongSequence":"emphasisSequence",start:f,end:{...e[n][1].end}},s={type:l>1?"strongSequence":"emphasisSequence",start:{...e[r][1].start},end:p},o={type:l>1?"strongText":"emphasisText",start:{...e[n][1].end},end:{...e[r][1].start}},i={type:l>1?"strong":"emphasis",start:{...a.start},end:{...s.end}},e[n][1].end={...a.start},e[r][1].start={...s.end},c=[],e[n][1].end.offset-e[n][1].start.offset&&(c=pe(c,[["enter",e[n][1],t],["exit",e[n][1],t]])),c=pe(c,[["enter",i,t],["enter",a,t],["exit",a,t],["enter",o,t]]),c=pe(c,It(t.parser.constructs.insideSpan.null,e.slice(n+1,r),t)),c=pe(c,[["exit",o,t],["enter",s,t],["exit",s,t],["exit",i,t]]),e[r][1].end.offset-e[r][1].start.offset?(u=2,c=pe(c,[["enter",e[r][1],t],["exit",e[r][1],t]])):u=0,oe(e,n-1,r-n+3,c),r=n+c.length-u-2;break}}for(r=-1;++r<e.length;)e[r][1].type==="attentionSequence"&&(e[r][1].type="data");return e}function ck(e,t){let r=this.parser.constructs.attentionMarkers.null,n=this.previous,i=Tt(n),o;return a;function a(l){return o=l,e.enter("attentionSequence"),s(l)}function s(l){if(l===o)return e.consume(l),s;let c=e.exit("attentionSequence"),u=Tt(l),f=!u||u===2&&i||r.includes(l),p=!i||i===2&&u||r.includes(n);return c._open=!!(o===42?f:f&&(i||!p)),c._close=!!(o===42?p:p&&(u||!f)),t(l)}}function Jl(e,t){e.column+=t,e.offset+=t,e._bufferIndex+=t}var er,Ql=b(()=>{Ge();zi();Qr();er={name:"attention",resolveAll:uk,tokenize:ck}});function fk(e,t,r){let n=0;return i;function i(h){return e.enter("autolink"),e.enter("autolinkMarker"),e.consume(h),e.exit("autolinkMarker"),e.enter("autolinkProtocol"),o}function o(h){return ve(h)?(e.consume(h),a):h===64?r(h):c(h)}function a(h){return h===43||h===45||h===46||he(h)?(n=1,s(h)):c(h)}function s(h){return h===58?(e.consume(h),n=0,l):(h===43||h===45||h===46||he(h))&&n++<32?(e.consume(h),s):(n=0,c(h))}function l(h){return h===62?(e.exit("autolinkProtocol"),e.enter("autolinkMarker"),e.consume(h),e.exit("autolinkMarker"),e.exit("autolink"),t):h===null||h===32||h===60||Xt(h)?r(h):(e.consume(h),l)}function c(h){return h===64?(e.consume(h),u):Ml(h)?(e.consume(h),c):r(h)}function u(h){return he(h)?f(h):r(h)}function f(h){return h===46?(e.consume(h),n=0,u):h===62?(e.exit("autolinkProtocol").type="autolinkEmail",e.enter("autolinkMarker"),e.consume(h),e.exit("autolinkMarker"),e.exit("autolink"),t):p(h)}function p(h){if((h===45||he(h))&&n++<63){let w=h===45?p:f;return e.consume(h),w}return r(h)}}var Vi,Yl=b(()=>{$();Vi={name:"autolink",tokenize:fk}});function pk(e,t,r){return n;function n(o){return R(o)?j(e,i,"linePrefix")(o):i(o)}function i(o){return o===null||E(o)?t(o):r(o)}}var Qe,Yr=b(()=>{re();$();Qe={partial:!0,tokenize:pk}});function hk(e,t,r){let n=this;return i;function i(a){if(a===62){let s=n.containerState;return s.open||(e.enter("blockQuote",{_container:!0}),s.open=!0),e.enter("blockQuotePrefix"),e.enter("blockQuoteMarker"),e.consume(a),e.exit("blockQuoteMarker"),o}return r(a)}function o(a){return R(a)?(e.enter("blockQuotePrefixWhitespace"),e.consume(a),e.exit("blockQuotePrefixWhitespace"),e.exit("blockQuotePrefix"),t):(e.exit("blockQuotePrefix"),t(a))}}function dk(e,t,r){let n=this;return i;function i(a){return R(a)?j(e,o,"linePrefix",n.parser.constructs.disable.null.includes("codeIndented")?void 0:4)(a):o(a)}function o(a){return e.attempt(Kr,t,r)(a)}}function mk(e){e.exit("blockQuote")}var Kr,Kl=b(()=>{re();$();Kr={continuation:{tokenize:dk},exit:mk,name:"blockQuote",tokenize:hk}});function gk(e,t,r){return n;function n(o){return e.enter("characterEscape"),e.enter("escapeMarker"),e.consume(o),e.exit("escapeMarker"),i}function i(o){return Bl(o)?(e.enter("characterEscapeValue"),e.consume(o),e.exit("characterEscapeValue"),e.exit("characterEscape"),t):r(o)}}var Xr,Xl=b(()=>{$();Xr={name:"characterEscape",tokenize:gk}});function bk(e,t,r){let n=this,i=0,o,a;return s;function s(f){return e.enter("characterReference"),e.enter("characterReferenceMarker"),e.consume(f),e.exit("characterReferenceMarker"),l}function l(f){return f===35?(e.enter("characterReferenceMarkerNumeric"),e.consume(f),e.exit("characterReferenceMarkerNumeric"),c):(e.enter("characterReferenceValue"),o=31,a=he,u(f))}function c(f){return f===88||f===120?(e.enter("characterReferenceMarkerHexadecimal"),e.consume(f),e.exit("characterReferenceMarkerHexadecimal"),e.enter("characterReferenceValue"),o=6,a=jl,u):(e.enter("characterReferenceValue"),o=7,a=Zt,u(f))}function u(f){if(f===59&&i){let p=e.exit("characterReferenceValue");return a===he&&!Lt(n.sliceSerialize(p))?r(f):(e.enter("characterReferenceMarker"),e.consume(f),e.exit("characterReferenceMarker"),e.exit("characterReference"),t)}return a(f)&&i++<o?(e.consume(f),u):r(f)}}var Zr,Zl=b(()=>{$r();$();Zr={name:"characterReference",tokenize:bk}});function wk(e,t,r){let n=this,i={partial:!0,tokenize:I},o=0,a=0,s;return l;function l(v){return c(v)}function c(v){let q=n.events[n.events.length-1];return o=q&&q[1].type==="linePrefix"?q[2].sliceSerialize(q[1],!0).length:0,s=v,e.enter("codeFenced"),e.enter("codeFencedFence"),e.enter("codeFencedFenceSequence"),u(v)}function u(v){return v===s?(a++,e.consume(v),u):a<3?r(v):(e.exit("codeFencedFenceSequence"),R(v)?j(e,f,"whitespace")(v):f(v))}function f(v){return v===null||E(v)?(e.exit("codeFencedFence"),n.interrupt?t(v):e.check(eu,g,V)(v)):(e.enter("codeFencedFenceInfo"),e.enter("chunkString",{contentType:"string"}),p(v))}function p(v){return v===null||E(v)?(e.exit("chunkString"),e.exit("codeFencedFenceInfo"),f(v)):R(v)?(e.exit("chunkString"),e.exit("codeFencedFenceInfo"),j(e,h,"whitespace")(v)):v===96&&v===s?r(v):(e.consume(v),p)}function h(v){return v===null||E(v)?f(v):(e.enter("codeFencedFenceMeta"),e.enter("chunkString",{contentType:"string"}),w(v))}function w(v){return v===null||E(v)?(e.exit("chunkString"),e.exit("codeFencedFenceMeta"),f(v)):v===96&&v===s?r(v):(e.consume(v),w)}function g(v){return e.attempt(i,V,x)(v)}function x(v){return e.enter("lineEnding"),e.consume(v),e.exit("lineEnding"),k}function k(v){return o>0&&R(v)?j(e,F,"linePrefix",o+1)(v):F(v)}function F(v){return v===null||E(v)?e.check(eu,g,V)(v):(e.enter("codeFlowValue"),_(v))}function _(v){return v===null||E(v)?(e.exit("codeFlowValue"),F(v)):(e.consume(v),_)}function V(v){return e.exit("codeFenced"),t(v)}function I(v,q,D){let O=0;return P;function P(T){return v.enter("lineEnding"),v.consume(T),v.exit("lineEnding"),B}function B(T){return v.enter("codeFencedFence"),R(T)?j(v,z,"linePrefix",n.parser.constructs.disable.null.includes("codeIndented")?void 0:4)(T):z(T)}function z(T){return T===s?(v.enter("codeFencedFenceSequence"),S(T)):D(T)}function S(T){return T===s?(O++,v.consume(T),S):O>=a?(v.exit("codeFencedFenceSequence"),R(T)?j(v,L,"whitespace")(T):L(T)):D(T)}function L(T){return T===null||E(T)?(v.exit("codeFencedFence"),q(T)):D(T)}}}function kk(e,t,r){let n=this;return i;function i(a){return a===null?r(a):(e.enter("lineEnding"),e.consume(a),e.exit("lineEnding"),o)}function o(a){return n.parser.lazy[n.now().line]?r(a):t(a)}}var eu,en,tu=b(()=>{re();$();eu={partial:!0,tokenize:kk},en={concrete:!0,name:"codeFenced",tokenize:wk}});function vk(e,t,r){let n=this;return i;function i(c){return e.enter("codeIndented"),j(e,o,"linePrefix",5)(c)}function o(c){let u=n.events[n.events.length-1];return u&&u[1].type==="linePrefix"&&u[2].sliceSerialize(u[1],!0).length>=4?a(c):r(c)}function a(c){return c===null?l(c):E(c)?e.attempt(yk,a,l)(c):(e.enter("codeFlowValue"),s(c))}function s(c){return c===null||E(c)?(e.exit("codeFlowValue"),a(c)):(e.consume(c),s)}function l(c){return e.exit("codeIndented"),t(c)}}function xk(e,t,r){let n=this;return i;function i(a){return n.parser.lazy[n.now().line]?r(a):E(a)?(e.enter("lineEnding"),e.consume(a),e.exit("lineEnding"),i):j(e,o,"linePrefix",5)(a)}function o(a){let s=n.events[n.events.length-1];return s&&s[1].type==="linePrefix"&&s[2].sliceSerialize(s[1],!0).length>=4?t(a):E(a)?i(a):r(a)}}var tr,yk,ru=b(()=>{re();$();tr={name:"codeIndented",tokenize:vk},yk={partial:!0,tokenize:xk}});function _k(e){let t=e.length-4,r=3,n,i;if((e[r][1].type==="lineEnding"||e[r][1].type==="space")&&(e[t][1].type==="lineEnding"||e[t][1].type==="space")){for(n=r;++n<t;)if(e[n][1].type==="codeTextData"){e[r][1].type="codeTextPadding",e[t][1].type="codeTextPadding",r+=2,t-=2;break}}for(n=r-1,t++;++n<=t;)i===void 0?n!==t&&e[n][1].type!=="lineEnding"&&(i=n):(n===t||e[n][1].type==="lineEnding")&&(e[i][1].type="codeTextData",n!==i+2&&(e[i][1].end=e[n-1][1].end,e.splice(i+2,n-i-2),t-=n-i-2,n=i+2),i=void 0);return e}function Ck(e){return e!==96||this.events[this.events.length-1][1].type==="characterEscape"}function Pk(e,t,r){let n=this,i=0,o,a;return s;function s(p){return e.enter("codeText"),e.enter("codeTextSequence"),l(p)}function l(p){return p===96?(e.consume(p),i++,l):(e.exit("codeTextSequence"),c(p))}function c(p){return p===null?r(p):p===32?(e.enter("space"),e.consume(p),e.exit("space"),c):p===96?(a=e.enter("codeTextSequence"),o=0,f(p)):E(p)?(e.enter("lineEnding"),e.consume(p),e.exit("lineEnding"),c):(e.enter("codeTextData"),u(p))}function u(p){return p===null||p===32||p===96||E(p)?(e.exit("codeTextData"),c(p)):(e.consume(p),u)}function f(p){return p===96?(e.consume(p),o++,f):o===i?(e.exit("codeTextSequence"),e.exit("codeText"),t(p)):(a.type="codeTextData",u(p))}}var Wi,nu=b(()=>{$();Wi={name:"codeText",previous:Ck,resolve:_k,tokenize:Pk}});function rr(e,t){let r=0;if(t.length<1e4)e.push(...t);else for(;r<t.length;)e.push(...t.slice(r,r+1e4)),r+=1e4}var tn,iu=b(()=>{tn=class{constructor(t){this.left=t?[...t]:[],this.right=[]}get(t){if(t<0||t>=this.left.length+this.right.length)throw new RangeError("Cannot access index `"+t+"` in a splice buffer of size `"+(this.left.length+this.right.length)+"`");return t<this.left.length?this.left[t]:this.right[this.right.length-t+this.left.length-1]}get length(){return this.left.length+this.right.length}shift(){return this.setCursor(0),this.right.pop()}slice(t,r){let n=r??Number.POSITIVE_INFINITY;return n<this.left.length?this.left.slice(t,n):t>this.left.length?this.right.slice(this.right.length-n+this.left.length,this.right.length-t+this.left.length).reverse():this.left.slice(t).concat(this.right.slice(this.right.length-n+this.left.length).reverse())}splice(t,r,n){let i=r||0;this.setCursor(Math.trunc(t));let o=this.right.splice(this.right.length-i,Number.POSITIVE_INFINITY);return n&&rr(this.left,n),o.reverse()}pop(){return this.setCursor(Number.POSITIVE_INFINITY),this.left.pop()}push(t){this.setCursor(Number.POSITIVE_INFINITY),this.left.push(t)}pushMany(t){this.setCursor(Number.POSITIVE_INFINITY),rr(this.left,t)}unshift(t){this.setCursor(0),this.right.push(t)}unshiftMany(t){this.setCursor(0),rr(this.right,t.reverse())}setCursor(t){if(!(t===this.left.length||t>this.left.length&&this.right.length===0||t<0&&this.left.length===0))if(t<this.left.length){let r=this.left.splice(t,Number.POSITIVE_INFINITY);rr(this.right,r.reverse())}else{let r=this.right.splice(this.left.length+this.right.length-t,Number.POSITIVE_INFINITY);rr(this.left,r.reverse())}}}});function rn(e){let t={},r=-1,n,i,o,a,s,l,c,u=new tn(e);for(;++r<u.length;){for(;r in t;)r=t[r];if(n=u.get(r),r&&n[1].type==="chunkFlow"&&u.get(r-1)[1].type==="listItemPrefix"&&(l=n[1]._tokenizer.events,o=0,o<l.length&&l[o][1].type==="lineEndingBlank"&&(o+=2),o<l.length&&l[o][1].type==="content"))for(;++o<l.length&&l[o][1].type!=="content";)l[o][1].type==="chunkText"&&(l[o][1]._isInFirstContentOfListItem=!0,o++);if(n[0]==="enter")n[1].contentType&&(Object.assign(t,Ak(u,r)),r=t[r],c=!0);else if(n[1]._container){for(o=r,i=void 0;o--&&(a=u.get(o),a[1].type==="lineEnding"||a[1].type==="lineEndingBlank");)a[0]==="enter"&&(i&&(u.get(i)[1].type="lineEndingBlank"),a[1].type="lineEnding",i=o);i&&(n[1].end={...u.get(i)[1].start},s=u.slice(i,r),s.unshift(n),u.splice(i,r-i+1,s))}}return oe(e,0,Number.POSITIVE_INFINITY,u.slice(0)),!c}function Ak(e,t){let r=e.get(t)[1],n=e.get(t)[2],i=t-1,o=[],a=r._tokenizer||n.parser[r.contentType](r.start),s=a.events,l=[],c={},u,f,p=-1,h=r,w=0,g=0,x=[g];for(;h;){for(;e.get(++i)[1]!==h;);o.push(i),h._tokenizer||(u=n.sliceStream(h),h.next||u.push(null),f&&a.defineSkip(h.start),h._isInFirstContentOfListItem&&(a._gfmTasklistFirstContentOfListItem=!0),a.write(u),h._isInFirstContentOfListItem&&(a._gfmTasklistFirstContentOfListItem=void 0)),f=h,h=h.next}for(h=r;++p<s.length;)s[p][0]==="exit"&&s[p-1][0]==="enter"&&s[p][1].type===s[p-1][1].type&&s[p][1].start.line!==s[p][1].end.line&&(g=p+1,x.push(g),h._tokenizer=void 0,h.previous=void 0,h=h.next);for(a.events=[],h?(h._tokenizer=void 0,h.previous=void 0):x.pop(),p=x.length;p--;){let k=s.slice(x[p],x[p+1]),F=o.pop();l.push([F,F+k.length-1]),e.splice(F,2,k)}for(l.reverse(),p=-1;++p<l.length;)c[w+l[p][0]]=w+l[p][1],w+=l[p][1]-l[p][0]-1;return c}var Ui=b(()=>{Ge();iu()});function Sk(e){return rn(e),e}function Fk(e,t){let r;return n;function n(s){return e.enter("content"),r=e.enter("chunkContent",{contentType:"content"}),i(s)}function i(s){return s===null?o(s):E(s)?e.check(Ek,a,o)(s):(e.consume(s),i)}function o(s){return e.exit("chunkContent"),e.exit("content"),t(s)}function a(s){return e.consume(s),e.exit("chunkContent"),r.next=e.enter("chunkContent",{contentType:"content",previous:r}),r=r.next,i}}function Ok(e,t,r){let n=this;return i;function i(a){return e.exit("chunkContent"),e.enter("lineEnding"),e.consume(a),e.exit("lineEnding"),j(e,o,"linePrefix")}function o(a){if(a===null||E(a))return r(a);let s=n.events[n.events.length-1];return!n.parser.constructs.disable.null.includes("codeIndented")&&s&&s[1].type==="linePrefix"&&s[2].sliceSerialize(s[1],!0).length>=4?t(a):e.interrupt(n.parser.constructs.flow,r,t)(a)}}var Hi,Ek,ou=b(()=>{re();$();Ui();Hi={resolve:Sk,tokenize:Fk},Ek={partial:!0,tokenize:Ok}});function nn(e,t,r,n,i,o,a,s,l){let c=l||Number.POSITIVE_INFINITY,u=0;return f;function f(k){return k===60?(e.enter(n),e.enter(i),e.enter(o),e.consume(k),e.exit(o),p):k===null||k===32||k===41||Xt(k)?r(k):(e.enter(n),e.enter(a),e.enter(s),e.enter("chunkString",{contentType:"string"}),g(k))}function p(k){return k===62?(e.enter(o),e.consume(k),e.exit(o),e.exit(i),e.exit(n),t):(e.enter(s),e.enter("chunkString",{contentType:"string"}),h(k))}function h(k){return k===62?(e.exit("chunkString"),e.exit(s),p(k)):k===null||k===60||E(k)?r(k):(e.consume(k),k===92?w:h)}function w(k){return k===60||k===62||k===92?(e.consume(k),h):h(k)}function g(k){return!u&&(k===null||k===41||Y(k))?(e.exit("chunkString"),e.exit(s),e.exit(a),e.exit(n),t(k)):u<c&&k===40?(e.consume(k),u++,g):k===41?(e.consume(k),u--,g):k===null||k===32||k===40||Xt(k)?r(k):(e.consume(k),k===92?x:g)}function x(k){return k===40||k===41||k===92?(e.consume(k),g):g(k)}}var $i=b(()=>{$()});function on(e,t,r,n,i,o){let a=this,s=0,l;return c;function c(h){return e.enter(n),e.enter(i),e.consume(h),e.exit(i),e.enter(o),u}function u(h){return s>999||h===null||h===91||h===93&&!l||h===94&&!s&&"_hiddenFootnoteSupport"in a.parser.constructs?r(h):h===93?(e.exit(o),e.enter(i),e.consume(h),e.exit(i),e.exit(n),t):E(h)?(e.enter("lineEnding"),e.consume(h),e.exit("lineEnding"),u):(e.enter("chunkString",{contentType:"string"}),f(h))}function f(h){return h===null||h===91||h===93||E(h)||s++>999?(e.exit("chunkString"),u(h)):(e.consume(h),l||(l=!R(h)),h===92?p:f)}function p(h){return h===91||h===92||h===93?(e.consume(h),s++,f):f(h)}}var Gi=b(()=>{$()});function an(e,t,r,n,i,o){let a;return s;function s(p){return p===34||p===39||p===40?(e.enter(n),e.enter(i),e.consume(p),e.exit(i),a=p===40?41:p,l):r(p)}function l(p){return p===a?(e.enter(i),e.consume(p),e.exit(i),e.exit(n),t):(e.enter(o),c(p))}function c(p){return p===a?(e.exit(o),l(a)):p===null?r(p):E(p)?(e.enter("lineEnding"),e.consume(p),e.exit("lineEnding"),j(e,c,"linePrefix")):(e.enter("chunkString",{contentType:"string"}),u(p))}function u(p){return p===a||p===null||E(p)?(e.exit("chunkString"),c(p)):(e.consume(p),p===92?f:u)}function f(p){return p===a||p===92?(e.consume(p),u):u(p)}}var Ji=b(()=>{re();$()});function ft(e,t){let r;return n;function n(i){return E(i)?(e.enter("lineEnding"),e.consume(i),e.exit("lineEnding"),r=!0,n):R(i)?j(e,n,r?"linePrefix":"lineSuffix")(i):t(i)}}var Qi=b(()=>{re();$()});function Tk(e,t,r){let n=this,i;return o;function o(h){return e.enter("definition"),a(h)}function a(h){return on.call(n,e,s,r,"definitionLabel","definitionLabelMarker","definitionLabelString")(h)}function s(h){return i=We(n.sliceSerialize(n.events[n.events.length-1][1]).slice(1,-1)),h===58?(e.enter("definitionMarker"),e.consume(h),e.exit("definitionMarker"),l):r(h)}function l(h){return Y(h)?ft(e,c)(h):c(h)}function c(h){return nn(e,u,r,"definitionDestination","definitionDestinationLiteral","definitionDestinationLiteralMarker","definitionDestinationRaw","definitionDestinationString")(h)}function u(h){return e.attempt(Lk,f,f)(h)}function f(h){return R(h)?j(e,p,"whitespace")(h):p(h)}function p(h){return h===null||E(h)?(e.exit("definition"),n.parser.defined.push(i),t(h)):r(h)}}function Ik(e,t,r){return n;function n(s){return Y(s)?ft(e,i)(s):r(s)}function i(s){return an(e,o,r,"definitionTitle","definitionTitleMarker","definitionTitleString")(s)}function o(s){return R(s)?j(e,a,"whitespace")(s):a(s)}function a(s){return s===null||E(s)?t(s):r(s)}}var Yi,Lk,au=b(()=>{$i();Gi();re();Ji();Qi();$();Jr();Yi={name:"definition",tokenize:Tk},Lk={partial:!0,tokenize:Ik}});function qk(e,t,r){return n;function n(o){return e.enter("hardBreakEscape"),e.consume(o),i}function i(o){return E(o)?(e.exit("hardBreakEscape"),t(o)):r(o)}}var Ki,su=b(()=>{$();Ki={name:"hardBreakEscape",tokenize:qk}});function Nk(e,t){let r=e.length-2,n=3,i,o;return e[n][1].type==="whitespace"&&(n+=2),r-2>n&&e[r][1].type==="whitespace"&&(r-=2),e[r][1].type==="atxHeadingSequence"&&(n===r-1||r-4>n&&e[r-2][1].type==="whitespace")&&(r-=n+1===r?2:4),r>n&&(i={type:"atxHeadingText",start:e[n][1].start,end:e[r][1].end},o={type:"chunkText",start:e[n][1].start,end:e[r][1].end,contentType:"text"},oe(e,n,r-n+1,[["enter",i,t],["enter",o,t],["exit",o,t],["exit",i,t]])),e}function Rk(e,t,r){let n=0;return i;function i(u){return e.enter("atxHeading"),o(u)}function o(u){return e.enter("atxHeadingSequence"),a(u)}function a(u){return u===35&&n++<6?(e.consume(u),a):u===null||Y(u)?(e.exit("atxHeadingSequence"),s(u)):r(u)}function s(u){return u===35?(e.enter("atxHeadingSequence"),l(u)):u===null||E(u)?(e.exit("atxHeading"),t(u)):R(u)?j(e,s,"whitespace")(u):(e.enter("atxHeadingText"),c(u))}function l(u){return u===35?(e.consume(u),l):(e.exit("atxHeadingSequence"),s(u))}function c(u){return u===null||u===35||Y(u)?(e.exit("atxHeadingText"),s(u)):(e.consume(u),c)}}var Xi,lu=b(()=>{re();$();Ge();Xi={name:"headingAtx",resolve:Nk,tokenize:Rk}});var uu,Zi,cu=b(()=>{uu=["address","article","aside","base","basefont","blockquote","body","caption","center","col","colgroup","dd","details","dialog","dir","div","dl","dt","fieldset","figcaption","figure","footer","form","frame","frameset","h1","h2","h3","h4","h5","h6","head","header","hr","html","iframe","legend","li","link","main","menu","menuitem","nav","noframes","ol","optgroup","option","p","param","search","section","summary","table","tbody","td","tfoot","th","thead","title","tr","track","ul"],Zi=["pre","script","style","textarea"]});function jk(e){let t=e.length;for(;t--&&!(e[t][0]==="enter"&&e[t][1].type==="htmlFlow"););return t>1&&e[t-2][1].type==="linePrefix"&&(e[t][1].start=e[t-2][1].start,e[t+1][1].start=e[t-2][1].start,e.splice(t-2,2)),e}function Bk(e,t,r){let n=this,i,o,a,s,l;return c;function c(m){return u(m)}function u(m){return e.enter("htmlFlow"),e.enter("htmlFlowData"),e.consume(m),f}function f(m){return m===33?(e.consume(m),p):m===47?(e.consume(m),o=!0,g):m===63?(e.consume(m),i=3,n.interrupt?t:d):ve(m)?(e.consume(m),a=String.fromCharCode(m),x):r(m)}function p(m){return m===45?(e.consume(m),i=2,h):m===91?(e.consume(m),i=5,s=0,w):ve(m)?(e.consume(m),i=4,n.interrupt?t:d):r(m)}function h(m){return m===45?(e.consume(m),n.interrupt?t:d):r(m)}function w(m){let we="CDATA[";return m===we.charCodeAt(s++)?(e.consume(m),s===we.length?n.interrupt?t:z:w):r(m)}function g(m){return ve(m)?(e.consume(m),a=String.fromCharCode(m),x):r(m)}function x(m){if(m===null||m===47||m===62||Y(m)){let we=m===47,rt=a.toLowerCase();return!we&&!o&&Zi.includes(rt)?(i=1,n.interrupt?t(m):z(m)):uu.includes(a.toLowerCase())?(i=6,we?(e.consume(m),k):n.interrupt?t(m):z(m)):(i=7,n.interrupt&&!n.parser.lazy[n.now().line]?r(m):o?F(m):_(m))}return m===45||he(m)?(e.consume(m),a+=String.fromCharCode(m),x):r(m)}function k(m){return m===62?(e.consume(m),n.interrupt?t:z):r(m)}function F(m){return R(m)?(e.consume(m),F):P(m)}function _(m){return m===47?(e.consume(m),P):m===58||m===95||ve(m)?(e.consume(m),V):R(m)?(e.consume(m),_):P(m)}function V(m){return m===45||m===46||m===58||m===95||he(m)?(e.consume(m),V):I(m)}function I(m){return m===61?(e.consume(m),v):R(m)?(e.consume(m),I):_(m)}function v(m){return m===null||m===60||m===61||m===62||m===96?r(m):m===34||m===39?(e.consume(m),l=m,q):R(m)?(e.consume(m),v):D(m)}function q(m){return m===l?(e.consume(m),l=null,O):m===null||E(m)?r(m):(e.consume(m),q)}function D(m){return m===null||m===34||m===39||m===47||m===60||m===61||m===62||m===96||Y(m)?I(m):(e.consume(m),D)}function O(m){return m===47||m===62||R(m)?_(m):r(m)}function P(m){return m===62?(e.consume(m),B):r(m)}function B(m){return m===null||E(m)?z(m):R(m)?(e.consume(m),B):r(m)}function z(m){return m===45&&i===2?(e.consume(m),A):m===60&&i===1?(e.consume(m),U):m===62&&i===4?(e.consume(m),Pe):m===63&&i===3?(e.consume(m),d):m===93&&i===5?(e.consume(m),X):E(m)&&(i===6||i===7)?(e.exit("htmlFlowData"),e.check(Dk,De,S)(m)):m===null||E(m)?(e.exit("htmlFlowData"),S(m)):(e.consume(m),z)}function S(m){return e.check(Mk,L,De)(m)}function L(m){return e.enter("lineEnding"),e.consume(m),e.exit("lineEnding"),T}function T(m){return m===null||E(m)?S(m):(e.enter("htmlFlowData"),z(m))}function A(m){return m===45?(e.consume(m),d):z(m)}function U(m){return m===47?(e.consume(m),a="",K):z(m)}function K(m){if(m===62){let we=a.toLowerCase();return Zi.includes(we)?(e.consume(m),Pe):z(m)}return ve(m)&&a.length<8?(e.consume(m),a+=String.fromCharCode(m),K):z(m)}function X(m){return m===93?(e.consume(m),d):z(m)}function d(m){return m===62?(e.consume(m),Pe):m===45&&i===2?(e.consume(m),d):z(m)}function Pe(m){return m===null||E(m)?(e.exit("htmlFlowData"),De(m)):(e.consume(m),Pe)}function De(m){return e.exit("htmlFlow"),t(m)}}function zk(e,t,r){let n=this;return i;function i(a){return E(a)?(e.enter("lineEnding"),e.consume(a),e.exit("lineEnding"),o):r(a)}function o(a){return n.parser.lazy[n.now().line]?r(a):t(a)}}function Vk(e,t,r){return n;function n(i){return e.enter("lineEnding"),e.consume(i),e.exit("lineEnding"),e.attempt(Qe,t,r)}}var eo,Dk,Mk,fu=b(()=>{$();cu();Yr();eo={concrete:!0,name:"htmlFlow",resolveTo:jk,tokenize:Bk},Dk={partial:!0,tokenize:Vk},Mk={partial:!0,tokenize:zk}});function Wk(e,t,r){let n=this,i,o,a;return s;function s(d){return e.enter("htmlText"),e.enter("htmlTextData"),e.consume(d),l}function l(d){return d===33?(e.consume(d),c):d===47?(e.consume(d),I):d===63?(e.consume(d),_):ve(d)?(e.consume(d),D):r(d)}function c(d){return d===45?(e.consume(d),u):d===91?(e.consume(d),o=0,w):ve(d)?(e.consume(d),F):r(d)}function u(d){return d===45?(e.consume(d),h):r(d)}function f(d){return d===null?r(d):d===45?(e.consume(d),p):E(d)?(a=f,U(d)):(e.consume(d),f)}function p(d){return d===45?(e.consume(d),h):f(d)}function h(d){return d===62?A(d):d===45?p(d):f(d)}function w(d){let Pe="CDATA[";return d===Pe.charCodeAt(o++)?(e.consume(d),o===Pe.length?g:w):r(d)}function g(d){return d===null?r(d):d===93?(e.consume(d),x):E(d)?(a=g,U(d)):(e.consume(d),g)}function x(d){return d===93?(e.consume(d),k):g(d)}function k(d){return d===62?A(d):d===93?(e.consume(d),k):g(d)}function F(d){return d===null||d===62?A(d):E(d)?(a=F,U(d)):(e.consume(d),F)}function _(d){return d===null?r(d):d===63?(e.consume(d),V):E(d)?(a=_,U(d)):(e.consume(d),_)}function V(d){return d===62?A(d):_(d)}function I(d){return ve(d)?(e.consume(d),v):r(d)}function v(d){return d===45||he(d)?(e.consume(d),v):q(d)}function q(d){return E(d)?(a=q,U(d)):R(d)?(e.consume(d),q):A(d)}function D(d){return d===45||he(d)?(e.consume(d),D):d===47||d===62||Y(d)?O(d):r(d)}function O(d){return d===47?(e.consume(d),A):d===58||d===95||ve(d)?(e.consume(d),P):E(d)?(a=O,U(d)):R(d)?(e.consume(d),O):A(d)}function P(d){return d===45||d===46||d===58||d===95||he(d)?(e.consume(d),P):B(d)}function B(d){return d===61?(e.consume(d),z):E(d)?(a=B,U(d)):R(d)?(e.consume(d),B):O(d)}function z(d){return d===null||d===60||d===61||d===62||d===96?r(d):d===34||d===39?(e.consume(d),i=d,S):E(d)?(a=z,U(d)):R(d)?(e.consume(d),z):(e.consume(d),L)}function S(d){return d===i?(e.consume(d),i=void 0,T):d===null?r(d):E(d)?(a=S,U(d)):(e.consume(d),S)}function L(d){return d===null||d===34||d===39||d===60||d===61||d===96?r(d):d===47||d===62||Y(d)?O(d):(e.consume(d),L)}function T(d){return d===47||d===62||Y(d)?O(d):r(d)}function A(d){return d===62?(e.consume(d),e.exit("htmlTextData"),e.exit("htmlText"),t):r(d)}function U(d){return e.exit("htmlTextData"),e.enter("lineEnding"),e.consume(d),e.exit("lineEnding"),K}function K(d){return R(d)?j(e,X,"linePrefix",n.parser.constructs.disable.null.includes("codeIndented")?void 0:4)(d):X(d)}function X(d){return e.enter("htmlTextData"),a(d)}}var to,pu=b(()=>{re();$();to={name:"htmlText",tokenize:Wk}});function Gk(e){let t=-1,r=[];for(;++t<e.length;){let n=e[t][1];if(r.push(e[t]),n.type==="labelImage"||n.type==="labelLink"||n.type==="labelEnd"){let i=n.type==="labelImage"?4:2;n.type="data",t+=i}}return e.length!==r.length&&oe(e,0,e.length,r),e}function Jk(e,t){let r=e.length,n=0,i,o,a,s;for(;r--;)if(i=e[r][1],o){if(i.type==="link"||i.type==="labelLink"&&i._inactive)break;e[r][0]==="enter"&&i.type==="labelLink"&&(i._inactive=!0)}else if(a){if(e[r][0]==="enter"&&(i.type==="labelImage"||i.type==="labelLink")&&!i._balanced&&(o=r,i.type!=="labelLink")){n=2;break}}else i.type==="labelEnd"&&(a=r);let l={type:e[o][1].type==="labelLink"?"link":"image",start:{...e[o][1].start},end:{...e[e.length-1][1].end}},c={type:"label",start:{...e[o][1].start},end:{...e[a][1].end}},u={type:"labelText",start:{...e[o+n+2][1].end},end:{...e[a-2][1].start}};return s=[["enter",l,t],["enter",c,t]],s=pe(s,e.slice(o+1,o+n+3)),s=pe(s,[["enter",u,t]]),s=pe(s,It(t.parser.constructs.insideSpan.null,e.slice(o+n+4,a-3),t)),s=pe(s,[["exit",u,t],e[a-2],e[a-1],["exit",c,t]]),s=pe(s,e.slice(a+1)),s=pe(s,[["exit",l,t]]),oe(e,o,e.length,s),e}function Qk(e,t,r){let n=this,i=n.events.length,o,a;for(;i--;)if((n.events[i][1].type==="labelImage"||n.events[i][1].type==="labelLink")&&!n.events[i][1]._balanced){o=n.events[i][1];break}return s;function s(p){return o?o._inactive?f(p):(a=n.parser.defined.includes(We(n.sliceSerialize({start:o.end,end:n.now()}))),e.enter("labelEnd"),e.enter("labelMarker"),e.consume(p),e.exit("labelMarker"),e.exit("labelEnd"),l):r(p)}function l(p){return p===40?e.attempt(Uk,u,a?u:f)(p):p===91?e.attempt(Hk,u,a?c:f)(p):a?u(p):f(p)}function c(p){return e.attempt($k,u,f)(p)}function u(p){return t(p)}function f(p){return o._balanced=!0,r(p)}}function Yk(e,t,r){return n;function n(f){return e.enter("resource"),e.enter("resourceMarker"),e.consume(f),e.exit("resourceMarker"),i}function i(f){return Y(f)?ft(e,o)(f):o(f)}function o(f){return f===41?u(f):nn(e,a,s,"resourceDestination","resourceDestinationLiteral","resourceDestinationLiteralMarker","resourceDestinationRaw","resourceDestinationString",32)(f)}function a(f){return Y(f)?ft(e,l)(f):u(f)}function s(f){return r(f)}function l(f){return f===34||f===39||f===40?an(e,c,r,"resourceTitle","resourceTitleMarker","resourceTitleString")(f):u(f)}function c(f){return Y(f)?ft(e,u)(f):u(f)}function u(f){return f===41?(e.enter("resourceMarker"),e.consume(f),e.exit("resourceMarker"),e.exit("resource"),t):r(f)}}function Kk(e,t,r){let n=this;return i;function i(s){return on.call(n,e,o,a,"reference","referenceMarker","referenceString")(s)}function o(s){return n.parser.defined.includes(We(n.sliceSerialize(n.events[n.events.length-1][1]).slice(1,-1)))?t(s):r(s)}function a(s){return r(s)}}function Xk(e,t,r){return n;function n(o){return e.enter("reference"),e.enter("referenceMarker"),e.consume(o),e.exit("referenceMarker"),i}function i(o){return o===93?(e.enter("referenceMarker"),e.consume(o),e.exit("referenceMarker"),e.exit("reference"),t):r(o)}}var pt,Uk,Hk,$k,sn=b(()=>{$i();Gi();Ji();Qi();$();Ge();Jr();Qr();pt={name:"labelEnd",resolveAll:Gk,resolveTo:Jk,tokenize:Qk},Uk={tokenize:Yk},Hk={tokenize:Kk},$k={tokenize:Xk}});function Zk(e,t,r){let n=this;return i;function i(s){return e.enter("labelImage"),e.enter("labelImageMarker"),e.consume(s),e.exit("labelImageMarker"),o}function o(s){return s===91?(e.enter("labelMarker"),e.consume(s),e.exit("labelMarker"),e.exit("labelImage"),a):r(s)}function a(s){return s===94&&"_hiddenFootnoteSupport"in n.parser.constructs?r(s):t(s)}}var ro,hu=b(()=>{sn();ro={name:"labelStartImage",resolveAll:pt.resolveAll,tokenize:Zk}});function ey(e,t,r){let n=this;return i;function i(a){return e.enter("labelLink"),e.enter("labelMarker"),e.consume(a),e.exit("labelMarker"),e.exit("labelLink"),o}function o(a){return a===94&&"_hiddenFootnoteSupport"in n.parser.constructs?r(a):t(a)}}var no,du=b(()=>{sn();no={name:"labelStartLink",resolveAll:pt.resolveAll,tokenize:ey}});function ty(e,t){return r;function r(n){return e.enter("lineEnding"),e.consume(n),e.exit("lineEnding"),j(e,t,"linePrefix")}}var nr,mu=b(()=>{re();nr={name:"lineEnding",tokenize:ty}});function ry(e,t,r){let n=0,i;return o;function o(c){return e.enter("thematicBreak"),a(c)}function a(c){return i=c,s(c)}function s(c){return c===i?(e.enter("thematicBreakSequence"),l(c)):n>=3&&(c===null||E(c))?(e.exit("thematicBreak"),t(c)):r(c)}function l(c){return c===i?(e.consume(c),n++,l):(e.exit("thematicBreakSequence"),R(c)?j(e,s,"whitespace")(c):s(c))}}var ht,io=b(()=>{re();$();ht={name:"thematicBreak",tokenize:ry}});function oy(e,t,r){let n=this,i=n.events[n.events.length-1],o=i&&i[1].type==="linePrefix"?i[2].sliceSerialize(i[1],!0).length:0,a=0;return s;function s(h){let w=n.containerState.type||(h===42||h===43||h===45?"listUnordered":"listOrdered");if(w==="listUnordered"?!n.containerState.marker||h===n.containerState.marker:Zt(h)){if(n.containerState.type||(n.containerState.type=w,e.enter(w,{_container:!0})),w==="listUnordered")return e.enter("listItemPrefix"),h===42||h===45?e.check(ht,r,c)(h):c(h);if(!n.interrupt||h===49)return e.enter("listItemPrefix"),e.enter("listItemValue"),l(h)}return r(h)}function l(h){return Zt(h)&&++a<10?(e.consume(h),l):(!n.interrupt||a<2)&&(n.containerState.marker?h===n.containerState.marker:h===41||h===46)?(e.exit("listItemValue"),c(h)):r(h)}function c(h){return e.enter("listItemMarker"),e.consume(h),e.exit("listItemMarker"),n.containerState.marker=n.containerState.marker||h,e.check(Qe,n.interrupt?r:u,e.attempt(ny,p,f))}function u(h){return n.containerState.initialBlankLine=!0,o++,p(h)}function f(h){return R(h)?(e.enter("listItemPrefixWhitespace"),e.consume(h),e.exit("listItemPrefixWhitespace"),p):r(h)}function p(h){return n.containerState.size=o+n.sliceSerialize(e.exit("listItemPrefix"),!0).length,t(h)}}function ay(e,t,r){let n=this;return n.containerState._closeFlow=void 0,e.check(Qe,i,o);function i(s){return n.containerState.furtherBlankLines=n.containerState.furtherBlankLines||n.containerState.initialBlankLine,j(e,t,"listItemIndent",n.containerState.size+1)(s)}function o(s){return n.containerState.furtherBlankLines||!R(s)?(n.containerState.furtherBlankLines=void 0,n.containerState.initialBlankLine=void 0,a(s)):(n.containerState.furtherBlankLines=void 0,n.containerState.initialBlankLine=void 0,e.attempt(iy,t,a)(s))}function a(s){return n.containerState._closeFlow=!0,n.interrupt=void 0,j(e,e.attempt(se,t,r),"linePrefix",n.parser.constructs.disable.null.includes("codeIndented")?void 0:4)(s)}}function sy(e,t,r){let n=this;return j(e,i,"listItemIndent",n.containerState.size+1);function i(o){let a=n.events[n.events.length-1];return a&&a[1].type==="listItemIndent"&&a[2].sliceSerialize(a[1],!0).length===n.containerState.size?t(o):r(o)}}function ly(e){e.exit(this.containerState.type)}function uy(e,t,r){let n=this;return j(e,i,"listItemPrefixWhitespace",n.parser.constructs.disable.null.includes("codeIndented")?void 0:5);function i(o){let a=n.events[n.events.length-1];return!R(o)&&a&&a[1].type==="listItemPrefixWhitespace"?t(o):r(o)}}var se,ny,iy,gu=b(()=>{re();$();Yr();io();se={continuation:{tokenize:ay},exit:ly,name:"list",tokenize:oy},ny={partial:!0,tokenize:uy},iy={partial:!0,tokenize:sy}});function cy(e,t){let r=e.length,n,i,o;for(;r--;)if(e[r][0]==="enter"){if(e[r][1].type==="content"){n=r;break}e[r][1].type==="paragraph"&&(i=r)}else e[r][1].type==="content"&&e.splice(r,1),!o&&e[r][1].type==="definition"&&(o=r);let a={type:"setextHeading",start:{...e[i][1].start},end:{...e[e.length-1][1].end}};return e[i][1].type="setextHeadingText",o?(e.splice(i,0,["enter",a,t]),e.splice(o+1,0,["exit",e[n][1],t]),e[n][1].end={...e[o][1].end}):e[n][1]=a,e.push(["exit",a,t]),e}function fy(e,t,r){let n=this,i;return o;function o(c){let u=n.events.length,f;for(;u--;)if(n.events[u][1].type!=="lineEnding"&&n.events[u][1].type!=="linePrefix"&&n.events[u][1].type!=="content"){f=n.events[u][1].type==="paragraph";break}return!n.parser.lazy[n.now().line]&&(n.interrupt||f)?(e.enter("setextHeadingLine"),i=c,a(c)):r(c)}function a(c){return e.enter("setextHeadingLineSequence"),s(c)}function s(c){return c===i?(e.consume(c),s):(e.exit("setextHeadingLineSequence"),R(c)?j(e,l,"lineSuffix")(c):l(c))}function l(c){return c===null||E(c)?(e.exit("setextHeadingLine"),t(c)):r(c)}}var ln,bu=b(()=>{re();$();ln={name:"setextUnderline",resolveTo:cy,tokenize:fy}});var oo=b(()=>{Ql();Yl();Yr();Kl();Xl();Zl();tu();ru();nu();ou();au();su();lu();fu();pu();sn();hu();du();mu();gu();bu();io()});function py(e){let t=this,r=e.attempt(Qe,n,e.attempt(this.parser.constructs.flowInitial,i,j(e,e.attempt(this.parser.constructs.flow,i,e.attempt(Hi,i)),"linePrefix")));return r;function n(o){if(o===null){e.consume(o);return}return e.enter("lineEndingBlank"),e.consume(o),e.exit("lineEndingBlank"),t.currentConstruct=void 0,r}function i(o){if(o===null){e.consume(o);return}return e.enter("lineEnding"),e.consume(o),e.exit("lineEnding"),t.currentConstruct=void 0,r}}var wu,ku=b(()=>{oo();re();wu={tokenize:py}});function _u(e){return{resolveAll:Cu(e==="text"?hy:void 0),tokenize:t};function t(r){let n=this,i=this.parser.constructs[e],o=r.attempt(i,a,s);return a;function a(u){return c(u)?o(u):s(u)}function s(u){if(u===null){r.consume(u);return}return r.enter("data"),r.consume(u),l}function l(u){return c(u)?(r.exit("data"),o(u)):(r.consume(u),l)}function c(u){if(u===null)return!0;let f=i[u],p=-1;if(f)for(;++p<f.length;){let h=f[p];if(!h.previous||h.previous.call(n,n.previous))return!0}return!1}}}function Cu(e){return t;function t(r,n){let i=-1,o;for(;++i<=r.length;)o===void 0?r[i]&&r[i][1].type==="data"&&(o=i,i++):(!r[i]||r[i][1].type!=="data")&&(i!==o+2&&(r[o][1].end=r[i-1][1].end,r.splice(o+2,i-o-2),i=o+2),o=void 0);return e?e(r,n):r}}function hy(e,t){let r=0;for(;++r<=e.length;)if((r===e.length||e[r][1].type==="lineEnding")&&e[r-1][1].type==="data"){let n=e[r-1][1],i=t.sliceStream(n),o=i.length,a=-1,s=0,l;for(;o--;){let c=i[o];if(typeof c=="string"){for(a=c.length;c.charCodeAt(a-1)===32;)s++,a--;if(a)break;a=-1}else if(c===-2)l=!0,s++;else if(c!==-1){o++;break}}if(s){let c={type:r===e.length||l||s<2?"lineSuffix":"hardBreakTrailing",start:{_bufferIndex:o?a:n.start._bufferIndex+a,_index:n.start._index+o,line:n.end.line,column:n.end.column-s,offset:n.end.offset-s},end:{...n.end}};n.end={...c.start},n.start.offset===n.end.offset?Object.assign(n,c):(e.splice(r,0,["enter",c,t],["exit",c,t]),r+=2)}r++}return e}var yu,vu,xu,ao=b(()=>{yu={resolveAll:Cu()},vu=_u("string"),xu=_u("text")});var so={};_r(so,{attentionMarkers:()=>vy,contentInitial:()=>my,disable:()=>xy,document:()=>dy,flow:()=>by,flowInitial:()=>gy,insideSpan:()=>yy,string:()=>wy,text:()=>ky});var dy,my,gy,by,wy,ky,yy,vy,xy,Pu=b(()=>{oo();ao();dy={42:se,43:se,45:se,48:se,49:se,50:se,51:se,52:se,53:se,54:se,55:se,56:se,57:se,62:Kr},my={91:Yi},gy={[-2]:tr,[-1]:tr,32:tr},by={35:Xi,42:ht,45:[ln,ht],60:eo,61:ln,95:ht,96:en,126:en},wy={38:Zr,92:Xr},ky={[-5]:nr,[-4]:nr,[-3]:nr,33:ro,38:Zr,42:er,60:[Vi,to],91:no,92:[Ki,Xr],93:pt,95:er,96:Wi},yy={null:[er,yu]},vy={null:[42,95]},xy={null:[]}});function Au(e,t,r){let n={_bufferIndex:-1,_index:0,line:r&&r.line||1,column:r&&r.column||1,offset:r&&r.offset||0},i={},o=[],a=[],s=[],l=!0,c={attempt:O(q),check:O(D),consume:V,enter:I,exit:v,interrupt:O(D,{interrupt:!0})},u={code:null,containerState:{},defineSkip:k,events:[],now:x,parser:e,previous:null,sliceSerialize:w,sliceStream:g,write:h},f=t.tokenize.call(u,c),p;return t.resolveAll&&o.push(t),u;function h(S){return a=pe(a,S),F(),a[a.length-1]!==null?[]:(P(t,0),u.events=It(o,u.events,u),u.events)}function w(S,L){return Cy(g(S),L)}function g(S){return _y(a,S)}function x(){let{_bufferIndex:S,_index:L,line:T,column:A,offset:U}=n;return{_bufferIndex:S,_index:L,line:T,column:A,offset:U}}function k(S){i[S.line]=S.column,z()}function F(){let S;for(;n._index<a.length;){let L=a[n._index];if(typeof L=="string")for(S=n._index,n._bufferIndex<0&&(n._bufferIndex=0);n._index===S&&n._bufferIndex<L.length;)_(L.charCodeAt(n._bufferIndex));else _(L)}}function _(S){l=void 0,p=S,f=f(S)}function V(S){E(S)?(n.line++,n.column=1,n.offset+=S===-3?2:1,z()):S!==-1&&(n.column++,n.offset++),n._bufferIndex<0?n._index++:(n._bufferIndex++,n._bufferIndex===a[n._index].length&&(n._bufferIndex=-1,n._index++)),u.previous=S,l=!0}function I(S,L){let T=L||{};return T.type=S,T.start=x(),u.events.push(["enter",T,u]),s.push(T),T}function v(S){let L=s.pop();return L.end=x(),u.events.push(["exit",L,u]),L}function q(S,L){P(S,L.from)}function D(S,L){L.restore()}function O(S,L){return T;function T(A,U,K){let X,d,Pe,De;return Array.isArray(A)?we(A):"tokenize"in A?we([A]):m(A);function m(ie){return zt;function zt(Me){let nt=Me!==null&&ie[Me],xt=Me!==null&&ie.null,zn=[...Array.isArray(nt)?nt:nt?[nt]:[],...Array.isArray(xt)?xt:xt?[xt]:[]];return we(zn)(Me)}}function we(ie){return X=ie,d=0,ie.length===0?K:rt(ie[d])}function rt(ie){return zt;function zt(Me){return De=B(),Pe=ie,ie.partial||(u.currentConstruct=ie),ie.name&&u.parser.constructs.disable.null.includes(ie.name)?vr(Me):ie.tokenize.call(L?Object.assign(Object.create(u),L):u,c,Bn,vr)(Me)}}function Bn(ie){return l=!0,S(Pe,De),U}function vr(ie){return l=!0,De.restore(),++d<X.length?rt(X[d]):K}}}function P(S,L){S.resolveAll&&!o.includes(S)&&o.push(S),S.resolve&&oe(u.events,L,u.events.length-L,S.resolve(u.events.slice(L),u)),S.resolveTo&&(u.events=S.resolveTo(u.events,u))}function B(){let S=x(),L=u.previous,T=u.currentConstruct,A=u.events.length,U=Array.from(s);return{from:A,restore:K};function K(){n=S,u.previous=L,u.currentConstruct=T,u.events.length=A,s=U,z()}}function z(){n.line in i&&n.column<2&&(n.column=i[n.line],n.offset+=i[n.line]-1)}}function _y(e,t){let r=t.start._index,n=t.start._bufferIndex,i=t.end._index,o=t.end._bufferIndex,a;if(r===i)a=[e[r].slice(n,o)];else{if(a=e.slice(r,i),n>-1){let s=a[0];typeof s=="string"?a[0]=s.slice(n):a.shift()}o>0&&a.push(e[i].slice(0,o))}return a}function Cy(e,t){let r=-1,n=[],i;for(;++r<e.length;){let o=e[r],a;if(typeof o=="string")a=o;else switch(o){case-5:{a="\r";break}case-4:{a=`
`;break}case-3:{a=`\r
`;break}case-2:{a=t?" ":"	";break}case-1:{if(!t&&i)continue;a=" ";break}default:a=String.fromCharCode(o)}i=o===-2,n.push(a)}return n.join("")}var Eu=b(()=>{$();Ge();Qr()});function lo(e){let n={constructs:Rl([so,...(e||{}).extensions||[]]),content:i(Wl),defined:[],document:i($l),flow:i(wu),lazy:{},string:i(vu),text:i(xu)};return n;function i(o){return a;function a(s){return Au(n,o,s)}}}var Su=b(()=>{Dl();Ul();Gl();ku();ao();Pu();Eu()});function uo(e){for(;!rn(e););return e}var TA,Fu=b(()=>{Ui();TA=globalThis.process??{browser:!0,cwd:()=>"/",env:{},platform:"android"}});function co(){let e=1,t="",r=!0,n;return i;function i(o,a,s){let l=[],c,u,f,p,h;for(o=t+(typeof o=="string"?o.toString():new TextDecoder(a||void 0).decode(o)),f=0,t="",r&&(o.charCodeAt(0)===65279&&f++,r=void 0);f<o.length;){if(Ou.lastIndex=f,c=Ou.exec(o),p=c&&c.index!==void 0?c.index:o.length,h=o.charCodeAt(p),!c){t=o.slice(f);break}if(h===10&&f===p&&n)l.push(-3),n=void 0;else switch(n&&(l.push(-5),n=void 0),f<p&&(l.push(o.slice(f,p)),e+=p-f),h){case 0:{l.push(65533),e++;break}case 9:{for(u=Math.ceil(e/4)*4,l.push(-2);e++<u;)l.push(-1);break}case 10:{l.push(-4),e=1;break}default:n=!0,e=1}f=p+1}return s&&(n&&l.push(-5),t&&l.push(t),l.push(null)),l}}var NA,Ou,Lu=b(()=>{NA=globalThis.process??{browser:!0,cwd:()=>"/",env:{},platform:"android"},Ou=/[\0\t\n\r]/g});var DA,Tu=b(()=>{Su();Fu();Lu();DA=globalThis.process??{browser:!0,cwd:()=>"/",env:{},platform:"android"}});function un(e){return e.replace(Py,Ay)}function Ay(e,t,r){if(t)return t;if(r.charCodeAt(0)===35){let i=r.charCodeAt(1),o=i===120||i===88;return Gr(r.slice(o?2:1),o?16:10)}return Lt(r)||e}var Py,fo=b(()=>{$r();Bi();Py=/\\([!-/:-@[-`{-~])|&(#(?:\d{1,7}|x[\da-f]{1,6})|[\da-z]{1,31});/gi});function Ye(e){return!e||typeof e!="object"?"":"position"in e||"type"in e?Iu(e.position):"start"in e||"end"in e?Iu(e):"line"in e||"column"in e?po(e):""}function po(e){return qu(e&&e.line)+":"+qu(e&&e.column)}function Iu(e){return po(e&&e.start)+"-"+po(e&&e.end)}function qu(e){return e&&typeof e=="number"?e:1}var Nu=b(()=>{});var ho=b(()=>{Nu()});function mo(e,t,r){return typeof t!="string"&&(r=t,t=void 0),Ey(r)(uo(lo(r).document().write(co()(e,t,!0))))}function Ey(e){let t={transforms:[],canContainEols:["emphasis","fragment","heading","paragraph","strong"],enter:{autolink:o(Da),autolinkProtocol:O,autolinkEmail:O,atxHeading:o(qa),blockQuote:o(Me),characterEscape:O,characterReference:O,codeFenced:o(nt),codeFencedFenceInfo:a,codeFencedFenceMeta:a,codeIndented:o(nt,a),codeText:o(xt,a),codeTextData:O,data:O,codeFlowValue:O,definition:o(zn),definitionDestinationString:a,definitionLabelString:a,definitionTitleString:a,emphasis:o(Fh),hardBreakEscape:o(Na),hardBreakTrailing:o(Na),htmlFlow:o(Ra,a),htmlFlowData:O,htmlText:o(Ra,a),htmlTextData:O,image:o(Oh),label:a,link:o(Da),listItem:o(Lh),listItemValue:p,listOrdered:o(Ma,f),listUnordered:o(Ma),paragraph:o(Th),reference:m,referenceString:a,resourceDestinationString:a,resourceTitleString:a,setextHeading:o(qa),strong:o(Ih),thematicBreak:o(Nh)},exit:{atxHeading:l(),atxHeadingSequence:I,autolink:l(),autolinkEmail:zt,autolinkProtocol:ie,blockQuote:l(),characterEscapeValue:P,characterReferenceMarkerHexadecimal:rt,characterReferenceMarkerNumeric:rt,characterReferenceValue:Bn,characterReference:vr,codeFenced:l(x),codeFencedFence:g,codeFencedFenceInfo:h,codeFencedFenceMeta:w,codeFlowValue:P,codeIndented:l(k),codeText:l(T),codeTextData:P,data:P,definition:l(),definitionDestinationString:V,definitionLabelString:F,definitionTitleString:_,emphasis:l(),hardBreakEscape:l(z),hardBreakTrailing:l(z),htmlFlow:l(S),htmlFlowData:P,htmlText:l(L),htmlTextData:P,image:l(U),label:X,labelText:K,lineEnding:B,link:l(A),listItem:l(),listOrdered:l(),listUnordered:l(),paragraph:l(),referenceString:we,resourceDestinationString:d,resourceTitleString:Pe,resource:De,setextHeading:l(D),setextHeadingLineSequence:q,setextHeadingText:v,strong:l(),thematicBreak:l()}};Mu(t,(e||{}).mdastExtensions||[]);let r={};return n;function n(y){let C={type:"root",children:[]},M={stack:[C],tokenStack:[],config:t,enter:s,exit:c,buffer:a,resume:u,data:r},W=[],G=-1;for(;++G<y.length;)if(y[G][1].type==="listOrdered"||y[G][1].type==="listUnordered")if(y[G][0]==="enter")W.push(G);else{let Fe=W.pop();G=i(y,Fe,G)}for(G=-1;++G<y.length;){let Fe=t[y[G][0]];Du.call(Fe,y[G][1].type)&&Fe[y[G][1].type].call(Object.assign({sliceSerialize:y[G][2].sliceSerialize},M),y[G][1])}if(M.tokenStack.length>0){let Fe=M.tokenStack[M.tokenStack.length-1];(Fe[1]||Ru).call(M,void 0,Fe[0])}for(C.position={start:Ke(y.length>0?y[0][1].start:{line:1,column:1,offset:0}),end:Ke(y.length>0?y[y.length-2][1].end:{line:1,column:1,offset:0})},G=-1;++G<t.transforms.length;)C=t.transforms[G](C)||C;return C}function i(y,C,M){let W=C-1,G=-1,Fe=!1,it,je,Vt,Wt;for(;++W<=M;){let ke=y[W];switch(ke[1].type){case"listUnordered":case"listOrdered":case"blockQuote":{ke[0]==="enter"?G++:G--,Wt=void 0;break}case"lineEndingBlank":{ke[0]==="enter"&&(it&&!Wt&&!G&&!Vt&&(Vt=W),Wt=void 0);break}case"linePrefix":case"listItemValue":case"listItemMarker":case"listItemPrefix":case"listItemPrefixWhitespace":break;default:Wt=void 0}if(!G&&ke[0]==="enter"&&ke[1].type==="listItemPrefix"||G===-1&&ke[0]==="exit"&&(ke[1].type==="listUnordered"||ke[1].type==="listOrdered")){if(it){let _t=W;for(je=void 0;_t--;){let Be=y[_t];if(Be[1].type==="lineEnding"||Be[1].type==="lineEndingBlank"){if(Be[0]==="exit")continue;je&&(y[je][1].type="lineEndingBlank",Fe=!0),Be[1].type="lineEnding",je=_t}else if(!(Be[1].type==="linePrefix"||Be[1].type==="blockQuotePrefix"||Be[1].type==="blockQuotePrefixWhitespace"||Be[1].type==="blockQuoteMarker"||Be[1].type==="listItemIndent"))break}Vt&&(!je||Vt<je)&&(it._spread=!0),it.end=Object.assign({},je?y[je][1].start:ke[1].end),y.splice(je||W,0,["exit",it,ke[2]]),W++,M++}if(ke[1].type==="listItemPrefix"){let _t={type:"listItem",_spread:!1,start:Object.assign({},ke[1].start),end:void 0};it=_t,y.splice(W,0,["enter",_t,ke[2]]),W++,M++,Vt=void 0,Wt=!0}}}return y[C][1]._spread=Fe,M}function o(y,C){return M;function M(W){s.call(this,y(W),W),C&&C.call(this,W)}}function a(){this.stack.push({type:"fragment",children:[]})}function s(y,C,M){this.stack[this.stack.length-1].children.push(y),this.stack.push(y),this.tokenStack.push([C,M||void 0]),y.position={start:Ke(C.start),end:void 0}}function l(y){return C;function C(M){y&&y.call(this,M),c.call(this,M)}}function c(y,C){let M=this.stack.pop(),W=this.tokenStack.pop();if(W)W[0].type!==y.type&&(C?C.call(this,y,W[0]):(W[1]||Ru).call(this,y,W[0]));else throw new Error("Cannot close `"+y.type+"` ("+Ye({start:y.start,end:y.end})+"): it\u2019s not open");M.position.end=Ke(y.end)}function u(){return ct(this.stack.pop())}function f(){this.data.expectingFirstListItemValue=!0}function p(y){if(this.data.expectingFirstListItemValue){let C=this.stack[this.stack.length-2];C.start=Number.parseInt(this.sliceSerialize(y),10),this.data.expectingFirstListItemValue=void 0}}function h(){let y=this.resume(),C=this.stack[this.stack.length-1];C.lang=y}function w(){let y=this.resume(),C=this.stack[this.stack.length-1];C.meta=y}function g(){this.data.flowCodeInside||(this.buffer(),this.data.flowCodeInside=!0)}function x(){let y=this.resume(),C=this.stack[this.stack.length-1];C.value=y.replace(/^(\r?\n|\r)|(\r?\n|\r)$/g,""),this.data.flowCodeInside=void 0}function k(){let y=this.resume(),C=this.stack[this.stack.length-1];C.value=y.replace(/(\r?\n|\r)$/g,"")}function F(y){let C=this.resume(),M=this.stack[this.stack.length-1];M.label=C,M.identifier=We(this.sliceSerialize(y)).toLowerCase()}function _(){let y=this.resume(),C=this.stack[this.stack.length-1];C.title=y}function V(){let y=this.resume(),C=this.stack[this.stack.length-1];C.url=y}function I(y){let C=this.stack[this.stack.length-1];if(!C.depth){let M=this.sliceSerialize(y).length;C.depth=M}}function v(){this.data.setextHeadingSlurpLineEnding=!0}function q(y){let C=this.stack[this.stack.length-1];C.depth=this.sliceSerialize(y).codePointAt(0)===61?1:2}function D(){this.data.setextHeadingSlurpLineEnding=void 0}function O(y){let M=this.stack[this.stack.length-1].children,W=M[M.length-1];(!W||W.type!=="text")&&(W=qh(),W.position={start:Ke(y.start),end:void 0},M.push(W)),this.stack.push(W)}function P(y){let C=this.stack.pop();C.value+=this.sliceSerialize(y),C.position.end=Ke(y.end)}function B(y){let C=this.stack[this.stack.length-1];if(this.data.atHardBreak){let M=C.children[C.children.length-1];M.position.end=Ke(y.end),this.data.atHardBreak=void 0;return}!this.data.setextHeadingSlurpLineEnding&&t.canContainEols.includes(C.type)&&(O.call(this,y),P.call(this,y))}function z(){this.data.atHardBreak=!0}function S(){let y=this.resume(),C=this.stack[this.stack.length-1];C.value=y}function L(){let y=this.resume(),C=this.stack[this.stack.length-1];C.value=y}function T(){let y=this.resume(),C=this.stack[this.stack.length-1];C.value=y}function A(){let y=this.stack[this.stack.length-1];if(this.data.inReference){let C=this.data.referenceType||"shortcut";y.type+="Reference",y.referenceType=C,delete y.url,delete y.title}else delete y.identifier,delete y.label;this.data.referenceType=void 0}function U(){let y=this.stack[this.stack.length-1];if(this.data.inReference){let C=this.data.referenceType||"shortcut";y.type+="Reference",y.referenceType=C,delete y.url,delete y.title}else delete y.identifier,delete y.label;this.data.referenceType=void 0}function K(y){let C=this.sliceSerialize(y),M=this.stack[this.stack.length-2];M.label=un(C),M.identifier=We(C).toLowerCase()}function X(){let y=this.stack[this.stack.length-1],C=this.resume(),M=this.stack[this.stack.length-1];if(this.data.inReference=!0,M.type==="link"){let W=y.children;M.children=W}else M.alt=C}function d(){let y=this.resume(),C=this.stack[this.stack.length-1];C.url=y}function Pe(){let y=this.resume(),C=this.stack[this.stack.length-1];C.title=y}function De(){this.data.inReference=void 0}function m(){this.data.referenceType="collapsed"}function we(y){let C=this.resume(),M=this.stack[this.stack.length-1];M.label=C,M.identifier=We(this.sliceSerialize(y)).toLowerCase(),this.data.referenceType="full"}function rt(y){this.data.characterReferenceType=y.type}function Bn(y){let C=this.sliceSerialize(y),M=this.data.characterReferenceType,W;M?(W=Gr(C,M==="characterReferenceMarkerNumeric"?10:16),this.data.characterReferenceType=void 0):W=Lt(C);let G=this.stack[this.stack.length-1];G.value+=W}function vr(y){let C=this.stack.pop();C.position.end=Ke(y.end)}function ie(y){P.call(this,y);let C=this.stack[this.stack.length-1];C.url=this.sliceSerialize(y)}function zt(y){P.call(this,y);let C=this.stack[this.stack.length-1];C.url="mailto:"+this.sliceSerialize(y)}function Me(){return{type:"blockquote",children:[]}}function nt(){return{type:"code",lang:null,meta:null,value:""}}function xt(){return{type:"inlineCode",value:""}}function zn(){return{type:"definition",identifier:"",label:null,title:null,url:""}}function Fh(){return{type:"emphasis",children:[]}}function qa(){return{type:"heading",depth:0,children:[]}}function Na(){return{type:"break"}}function Ra(){return{type:"html",value:""}}function Oh(){return{type:"image",title:null,url:"",alt:null}}function Da(){return{type:"link",title:null,url:"",children:[]}}function Ma(y){return{type:"list",ordered:y.type==="listOrdered",start:null,spread:y._spread,children:[]}}function Lh(y){return{type:"listItem",spread:y._spread,checked:null,children:[]}}function Th(){return{type:"paragraph",children:[]}}function Ih(){return{type:"strong",children:[]}}function qh(){return{type:"text",value:""}}function Nh(){return{type:"thematicBreak"}}}function Ke(e){return{line:e.line,column:e.column,offset:e.offset}}function Mu(e,t){let r=-1;for(;++r<t.length;){let n=t[r];Array.isArray(n)?Mu(e,n):Sy(e,n)}}function Sy(e,t){let r;for(r in t)if(Du.call(t,r))switch(r){case"canContainEols":{let n=t[r];n&&e[r].push(...n);break}case"transforms":{let n=t[r];n&&e[r].push(...n);break}case"enter":case"exit":{let n=t[r];n&&Object.assign(e[r],n);break}}}function Ru(e,t){throw e?new Error("Cannot close `"+e.type+"` ("+Ye({start:e.start,end:e.end})+"): a different token (`"+t.type+"`, "+Ye({start:t.start,end:t.end})+") is open"):new Error("Cannot close document, a token (`"+t.type+"`, "+Ye({start:t.start,end:t.end})+") is still open")}var JA,Du,ju=b(()=>{Hr();Tu();Bi();fo();Jr();$r();ho();JA=globalThis.process??{browser:!0,cwd:()=>"/",env:{},platform:"android"},Du={}.hasOwnProperty});var Bu=b(()=>{ju()});function ir(e){let t=this;t.parser=r;function r(n){return mo(n,{...t.data("settings"),...e,extensions:t.data("micromarkExtensions")||[],mdastExtensions:t.data("fromMarkdownExtensions")||[]})}}var zu=b(()=>{Bu()});var Vu={};_r(Vu,{default:()=>ir});var go=b(()=>{zu()});function Uu(e,t){let r=t||{};function n(i,...o){let a=n.invalid,s=n.handlers;if(i&&Wu.call(i,e)){let l=String(i[e]);a=Wu.call(s,l)?s[l]:n.unknown}if(a)return a.call(this,i,...o)}return n.handlers=r.handlers||{},n.invalid=r.invalid,n.unknown=r.unknown,n}var Wu,Hu=b(()=>{Wu={}.hasOwnProperty});function bo(e,t){let r=-1,n;if(t.extensions)for(;++r<t.extensions.length;)bo(e,t.extensions[r]);for(n in t)if(Fy.call(t,n))switch(n){case"extensions":break;case"unsafe":{$u(e[n],t[n]);break}case"join":{$u(e[n],t[n]);break}case"handlers":{Oy(e[n],t[n]);break}default:e.options[n]=t[n]}return e}function $u(e,t){t&&e.push(...t)}function Oy(e,t){t&&Object.assign(e,t)}var Fy,Gu=b(()=>{Fy={}.hasOwnProperty});function Ju(e,t,r,n){let i=r.enter("blockquote"),o=r.createTracker(n);o.move("> "),o.shift(2);let a=r.indentLines(r.containerFlow(e,o.current()),Ly);return i(),a}function Ly(e,t,r){return">"+(r?"":" ")+e}var Qu=b(()=>{});function cn(e,t){return Yu(e,t.inConstruct,!0)&&!Yu(e,t.notInConstruct,!1)}function Yu(e,t,r){if(typeof t=="string"&&(t=[t]),!t||t.length===0)return r;let n=-1;for(;++n<t.length;)if(e.includes(t[n]))return!0;return!1}var wo=b(()=>{});function ko(e,t,r,n){let i=-1;for(;++i<r.unsafe.length;)if(r.unsafe[i].character===`
`&&cn(r.stack,r.unsafe[i]))return/[ \t]/.test(n.before)?"":" ";return`\\
`}var Ku=b(()=>{wo()});function Xu(e,t){let r=String(e),n=r.indexOf(t),i=n,o=0,a=0;if(typeof t!="string")throw new TypeError("Expected substring");for(;n!==-1;)n===i?++o>a&&(a=o):o=1,i=n+t.length,n=r.indexOf(t,i);return a}var Zu=b(()=>{});function or(e,t){return!!(t.options.fences===!1&&e.value&&!e.lang&&/[^ \r\n]/.test(e.value)&&!/^[\t ]*(?:[\r\n]|$)|(?:^|[\r\n])[\t ]*$/.test(e.value))}var yo=b(()=>{});function ec(e){let t=e.options.fence||"`";if(t!=="`"&&t!=="~")throw new Error("Cannot serialize code with `"+t+"` for `options.fence`, expected `` ` `` or `~`");return t}var tc=b(()=>{});function rc(e,t,r,n){let i=ec(r),o=e.value||"",a=i==="`"?"GraveAccent":"Tilde";if(or(e,r)){let f=r.enter("codeIndented"),p=r.indentLines(o,Ty);return f(),p}let s=r.createTracker(n),l=i.repeat(Math.max(Xu(o,i)+1,3)),c=r.enter("codeFenced"),u=s.move(l);if(e.lang){let f=r.enter(`codeFencedLang${a}`);u+=s.move(r.safe(e.lang,{before:u,after:" ",encode:["`"],...s.current()})),f()}if(e.lang&&e.meta){let f=r.enter(`codeFencedMeta${a}`);u+=s.move(" "),u+=s.move(r.safe(e.meta,{before:u,after:`
`,encode:["`"],...s.current()})),f()}return u+=s.move(`
`),o&&(u+=s.move(o+`
`)),u+=s.move(l),c(),u}function Ty(e,t,r){return(r?"":"    ")+e}var nc=b(()=>{Zu();yo();tc()});function qt(e){let t=e.options.quote||'"';if(t!=='"'&&t!=="'")throw new Error("Cannot serialize title with `"+t+"` for `options.quote`, expected `\"`, or `'`");return t}var fn=b(()=>{});function ic(e,t,r,n){let i=qt(r),o=i==='"'?"Quote":"Apostrophe",a=r.enter("definition"),s=r.enter("label"),l=r.createTracker(n),c=l.move("[");return c+=l.move(r.safe(r.associationId(e),{before:c,after:"]",...l.current()})),c+=l.move("]: "),s(),!e.url||/[\0- \u007F]/.test(e.url)?(s=r.enter("destinationLiteral"),c+=l.move("<"),c+=l.move(r.safe(e.url,{before:c,after:">",...l.current()})),c+=l.move(">")):(s=r.enter("destinationRaw"),c+=l.move(r.safe(e.url,{before:c,after:e.title?" ":`
`,...l.current()}))),s(),e.title&&(s=r.enter(`title${o}`),c+=l.move(" "+i),c+=l.move(r.safe(e.title,{before:c,after:i,...l.current()})),c+=l.move(i),s()),a(),c}var oc=b(()=>{fn()});function ac(e){let t=e.options.emphasis||"*";if(t!=="*"&&t!=="_")throw new Error("Cannot serialize emphasis with `"+t+"` for `options.emphasis`, expected `*`, or `_`");return t}var sc=b(()=>{});function xe(e){return"&#x"+e.toString(16).toUpperCase()+";"}var Nt=b(()=>{});function Rt(e,t,r){let n=Tt(e),i=Tt(t);return n===void 0?i===void 0?r==="_"?{inside:!0,outside:!0}:{inside:!1,outside:!1}:i===1?{inside:!0,outside:!0}:{inside:!1,outside:!0}:n===1?i===void 0?{inside:!1,outside:!1}:i===1?{inside:!0,outside:!0}:{inside:!1,outside:!1}:i===void 0?{inside:!1,outside:!1}:i===1?{inside:!0,outside:!1}:{inside:!1,outside:!1}}var vo=b(()=>{zi()});function xo(e,t,r,n){let i=ac(r),o=r.enter("emphasis"),a=r.createTracker(n),s=a.move(i),l=a.move(r.containerPhrasing(e,{after:i,before:s,...a.current()})),c=l.charCodeAt(0),u=Rt(n.before.charCodeAt(n.before.length-1),c,i);u.inside&&(l=xe(c)+l.slice(1));let f=l.charCodeAt(l.length-1),p=Rt(n.after.charCodeAt(0),f,i);p.inside&&(l=l.slice(0,-1)+xe(f));let h=a.move(i);return o(),r.attentionEncodeSurroundingInfo={after:p.outside,before:u.outside},s+l+h}function Iy(e,t,r){return r.options.emphasis||"*"}var lc=b(()=>{sc();Nt();vo();xo.peek=Iy});function qy(e){let t=[],r=-1;for(;++r<e.length;)t[r]=Dt(e[r]);return pn(n);function n(...i){let o=-1;for(;++o<t.length;)if(t[o].apply(this,i))return!0;return!1}}function Ny(e){let t=e;return pn(r);function r(n){let i=n,o;for(o in e)if(i[o]!==t[o])return!1;return!0}}function Ry(e){return pn(t);function t(r){return r&&r.type===e}}function pn(e){return t;function t(r,n,i){return!!(My(r)&&e.call(this,r,typeof n=="number"?n:void 0,i||void 0))}}function Dy(){return!0}function My(e){return e!==null&&typeof e=="object"&&"type"in e}var Dt,uc=b(()=>{Dt=function(e){if(e==null)return Dy;if(typeof e=="function")return pn(e);if(typeof e=="object")return Array.isArray(e)?qy(e):Ny(e);if(typeof e=="string")return Ry(e);throw new Error("Expected function, string, or object as test")}});var _o=b(()=>{uc()});function cc(e){return"\x1B[33m"+e+"\x1B[39m"}var fc=b(()=>{});function Co(e,t,r,n){let i;typeof t=="function"&&typeof r!="function"?(n=r,r=t):i=t;let o=Dt(i),a=n?-1:1;s(e,void 0,[])();function s(l,c,u){let f=l&&typeof l=="object"?l:{};if(typeof f.type=="string"){let h=typeof f.tagName=="string"?f.tagName:typeof f.name=="string"?f.name:void 0;Object.defineProperty(p,"name",{value:"node ("+cc(l.type+(h?"<"+h+">":""))+")"})}return p;function p(){let h=pc,w,g,x;if((!t||o(l,c,u[u.length-1]||void 0))&&(h=jy(r(l,u)),h[0]===dt))return h;if("children"in l&&l.children){let k=l;if(k.children&&h[0]!==dn)for(g=(n?k.children.length:-1)+a,x=u.concat(k);g>-1&&g<k.children.length;){let F=k.children[g];if(w=s(F,g,x)(),w[0]===dt)return w;g=typeof w[1]=="number"?w[1]:g+a}}return h}}}function jy(e){return Array.isArray(e)?e:typeof e=="number"?[hn,e]:e==null?pc:[e]}var pc,hn,dt,dn,hc=b(()=>{_o();fc();pc=[],hn=!0,dt=!1,dn="skip"});var Po=b(()=>{hc()});function Ao(e,t,r,n){let i,o,a;typeof t=="function"&&typeof r!="function"?(o=void 0,a=t,i=r):(o=t,a=r,i=n),Co(e,o,s,i);function s(l,c){let u=c[c.length-1],f=u?u.children.indexOf(l):void 0;return a(l,f,u)}}var dc=b(()=>{Po();Po()});var mc=b(()=>{dc()});function mn(e,t){let r=!1;return Ao(e,function(n){if("value"in n&&/\r?\n|\r/.test(n.value)||n.type==="break")return r=!0,dt}),!!((!e.depth||e.depth<3)&&ct(e)&&(t.options.setext||r))}var Eo=b(()=>{mc();Hr()});function gc(e,t,r,n){let i=Math.max(Math.min(6,e.depth||1),1),o=r.createTracker(n);if(mn(e,r)){let u=r.enter("headingSetext"),f=r.enter("phrasing"),p=r.containerPhrasing(e,{...o.current(),before:`
`,after:`
`});return f(),u(),p+`
`+(i===1?"=":"-").repeat(p.length-(Math.max(p.lastIndexOf("\r"),p.lastIndexOf(`
`))+1))}let a="#".repeat(i),s=r.enter("headingAtx"),l=r.enter("phrasing");o.move(a+" ");let c=r.containerPhrasing(e,{before:"# ",after:`
`,...o.current()});return/^[\t ]/.test(c)&&(c=xe(c.charCodeAt(0))+c.slice(1)),c=c?a+" "+c:a,r.options.closeAtx&&(c+=" "+a),l(),s(),c}var bc=b(()=>{Nt();Eo()});function So(e){return e.value||""}function By(){return"<"}var wc=b(()=>{So.peek=By});function Fo(e,t,r,n){let i=qt(r),o=i==='"'?"Quote":"Apostrophe",a=r.enter("image"),s=r.enter("label"),l=r.createTracker(n),c=l.move("![");return c+=l.move(r.safe(e.alt,{before:c,after:"]",...l.current()})),c+=l.move("]("),s(),!e.url&&e.title||/[\0- \u007F]/.test(e.url)?(s=r.enter("destinationLiteral"),c+=l.move("<"),c+=l.move(r.safe(e.url,{before:c,after:">",...l.current()})),c+=l.move(">")):(s=r.enter("destinationRaw"),c+=l.move(r.safe(e.url,{before:c,after:e.title?" ":")",...l.current()}))),s(),e.title&&(s=r.enter(`title${o}`),c+=l.move(" "+i),c+=l.move(r.safe(e.title,{before:c,after:i,...l.current()})),c+=l.move(i),s()),c+=l.move(")"),a(),c}function zy(){return"!"}var kc=b(()=>{fn();Fo.peek=zy});function Oo(e,t,r,n){let i=e.referenceType,o=r.enter("imageReference"),a=r.enter("label"),s=r.createTracker(n),l=s.move("!["),c=r.safe(e.alt,{before:l,after:"]",...s.current()});l+=s.move(c+"]["),a();let u=r.stack;r.stack=[],a=r.enter("reference");let f=r.safe(r.associationId(e),{before:l,after:"]",...s.current()});return a(),r.stack=u,o(),i==="full"||!c||c!==f?l+=s.move(f+"]"):i==="shortcut"?l=l.slice(0,-1):l+=s.move("]"),l}function Vy(){return"!"}var yc=b(()=>{Oo.peek=Vy});function Lo(e,t,r){let n=e.value||"",i="`",o=-1;for(;new RegExp("(^|[^`])"+i+"([^`]|$)").test(n);)i+="`";for(/[^ \r\n]/.test(n)&&(/^[ \r\n]/.test(n)&&/[ \r\n]$/.test(n)||/^`|`$/.test(n))&&(n=" "+n+" ");++o<r.unsafe.length;){let a=r.unsafe[o],s=r.compilePattern(a),l;if(a.atBreak)for(;l=s.exec(n);){let c=l.index;n.charCodeAt(c)===10&&n.charCodeAt(c-1)===13&&c--,n=n.slice(0,c)+" "+n.slice(l.index+1)}}return i+n+i}function Wy(){return"`"}var vc=b(()=>{Lo.peek=Wy});function To(e,t){let r=ct(e);return!!(!t.options.resourceLink&&e.url&&!e.title&&e.children&&e.children.length===1&&e.children[0].type==="text"&&(r===e.url||"mailto:"+r===e.url)&&/^[a-z][a-z+.-]+:/i.test(e.url)&&!/[\0- <>\u007F]/.test(e.url))}var xc=b(()=>{Hr()});function Io(e,t,r,n){let i=qt(r),o=i==='"'?"Quote":"Apostrophe",a=r.createTracker(n),s,l;if(To(e,r)){let u=r.stack;r.stack=[],s=r.enter("autolink");let f=a.move("<");return f+=a.move(r.containerPhrasing(e,{before:f,after:">",...a.current()})),f+=a.move(">"),s(),r.stack=u,f}s=r.enter("link"),l=r.enter("label");let c=a.move("[");return c+=a.move(r.containerPhrasing(e,{before:c,after:"](",...a.current()})),c+=a.move("]("),l(),!e.url&&e.title||/[\0- \u007F]/.test(e.url)?(l=r.enter("destinationLiteral"),c+=a.move("<"),c+=a.move(r.safe(e.url,{before:c,after:">",...a.current()})),c+=a.move(">")):(l=r.enter("destinationRaw"),c+=a.move(r.safe(e.url,{before:c,after:e.title?" ":")",...a.current()}))),l(),e.title&&(l=r.enter(`title${o}`),c+=a.move(" "+i),c+=a.move(r.safe(e.title,{before:c,after:i,...a.current()})),c+=a.move(i),l()),c+=a.move(")"),s(),c}function Uy(e,t,r){return To(e,r)?"<":"["}var _c=b(()=>{fn();xc();Io.peek=Uy});function qo(e,t,r,n){let i=e.referenceType,o=r.enter("linkReference"),a=r.enter("label"),s=r.createTracker(n),l=s.move("["),c=r.containerPhrasing(e,{before:l,after:"]",...s.current()});l+=s.move(c+"]["),a();let u=r.stack;r.stack=[],a=r.enter("reference");let f=r.safe(r.associationId(e),{before:l,after:"]",...s.current()});return a(),r.stack=u,o(),i==="full"||!c||c!==f?l+=s.move(f+"]"):i==="shortcut"?l=l.slice(0,-1):l+=s.move("]"),l}function Hy(){return"["}var Cc=b(()=>{qo.peek=Hy});function Mt(e){let t=e.options.bullet||"*";if(t!=="*"&&t!=="+"&&t!=="-")throw new Error("Cannot serialize items with `"+t+"` for `options.bullet`, expected `*`, `+`, or `-`");return t}var gn=b(()=>{});function Pc(e){let t=Mt(e),r=e.options.bulletOther;if(!r)return t==="*"?"-":"*";if(r!=="*"&&r!=="+"&&r!=="-")throw new Error("Cannot serialize items with `"+r+"` for `options.bulletOther`, expected `*`, `+`, or `-`");if(r===t)throw new Error("Expected `bullet` (`"+t+"`) and `bulletOther` (`"+r+"`) to be different");return r}var Ac=b(()=>{gn()});function Ec(e){let t=e.options.bulletOrdered||".";if(t!=="."&&t!==")")throw new Error("Cannot serialize items with `"+t+"` for `options.bulletOrdered`, expected `.` or `)`");return t}var Sc=b(()=>{});function bn(e){let t=e.options.rule||"*";if(t!=="*"&&t!=="-"&&t!=="_")throw new Error("Cannot serialize rules with `"+t+"` for `options.rule`, expected `*`, `-`, or `_`");return t}var No=b(()=>{});function Fc(e,t,r,n){let i=r.enter("list"),o=r.bulletCurrent,a=e.ordered?Ec(r):Mt(r),s=e.ordered?a==="."?")":".":Pc(r),l=t&&r.bulletLastUsed?a===r.bulletLastUsed:!1;if(!e.ordered){let u=e.children?e.children[0]:void 0;if((a==="*"||a==="-")&&u&&(!u.children||!u.children[0])&&r.stack[r.stack.length-1]==="list"&&r.stack[r.stack.length-2]==="listItem"&&r.stack[r.stack.length-3]==="list"&&r.stack[r.stack.length-4]==="listItem"&&r.indexStack[r.indexStack.length-1]===0&&r.indexStack[r.indexStack.length-2]===0&&r.indexStack[r.indexStack.length-3]===0&&(l=!0),bn(r)===a&&u){let f=-1;for(;++f<e.children.length;){let p=e.children[f];if(p&&p.type==="listItem"&&p.children&&p.children[0]&&p.children[0].type==="thematicBreak"){l=!0;break}}}}l&&(a=s),r.bulletCurrent=a;let c=r.containerFlow(e,n);return r.bulletLastUsed=a,r.bulletCurrent=o,i(),c}var Oc=b(()=>{gn();Ac();Sc();No()});function Lc(e){let t=e.options.listItemIndent||"one";if(t!=="tab"&&t!=="one"&&t!=="mixed")throw new Error("Cannot serialize items with `"+t+"` for `options.listItemIndent`, expected `tab`, `one`, or `mixed`");return t}var Tc=b(()=>{});function Ic(e,t,r,n){let i=Lc(r),o=r.bulletCurrent||Mt(r);t&&t.type==="list"&&t.ordered&&(o=(typeof t.start=="number"&&t.start>-1?t.start:1)+(r.options.incrementListMarker===!1?0:t.children.indexOf(e))+o);let a=o.length+1;(i==="tab"||i==="mixed"&&(t&&t.type==="list"&&t.spread||e.spread))&&(a=Math.ceil(a/4)*4);let s=r.createTracker(n);s.move(o+" ".repeat(a-o.length)),s.shift(a);let l=r.enter("listItem"),c=r.indentLines(r.containerFlow(e,s.current()),u);return l(),c;function u(f,p,h){return p?(h?"":" ".repeat(a))+f:(h?o:o+" ".repeat(a-o.length))+f}}var qc=b(()=>{gn();Tc()});function Nc(e,t,r,n){let i=r.enter("paragraph"),o=r.enter("phrasing"),a=r.containerPhrasing(e,n);return o(),i(),a}var Rc=b(()=>{});var Ro,Dc=b(()=>{_o();Ro=Dt(["break","delete","emphasis","footnote","footnoteReference","image","imageReference","inlineCode","inlineMath","link","linkReference","mdxJsxTextElement","mdxTextExpression","strong","text","textDirective"])});var Mc=b(()=>{Dc()});function jc(e,t,r,n){return(e.children.some(function(a){return Ro(a)})?r.containerPhrasing:r.containerFlow).call(r,e,n)}var Bc=b(()=>{Mc()});function zc(e){let t=e.options.strong||"*";if(t!=="*"&&t!=="_")throw new Error("Cannot serialize strong with `"+t+"` for `options.strong`, expected `*`, or `_`");return t}var Vc=b(()=>{});function Do(e,t,r,n){let i=zc(r),o=r.enter("strong"),a=r.createTracker(n),s=a.move(i+i),l=a.move(r.containerPhrasing(e,{after:i,before:s,...a.current()})),c=l.charCodeAt(0),u=Rt(n.before.charCodeAt(n.before.length-1),c,i);u.inside&&(l=xe(c)+l.slice(1));let f=l.charCodeAt(l.length-1),p=Rt(n.after.charCodeAt(0),f,i);p.inside&&(l=l.slice(0,-1)+xe(f));let h=a.move(i+i);return o(),r.attentionEncodeSurroundingInfo={after:p.outside,before:u.outside},s+l+h}function $y(e,t,r){return r.options.strong||"*"}var Wc=b(()=>{Vc();Nt();vo();Do.peek=$y});function Uc(e,t,r,n){return r.safe(e.value,n)}var Hc=b(()=>{});function $c(e){let t=e.options.ruleRepetition||3;if(t<3)throw new Error("Cannot serialize rules with repetition `"+t+"` for `options.ruleRepetition`, expected `3` or more");return t}var Gc=b(()=>{});function Jc(e,t,r){let n=(bn(r)+(r.options.ruleSpaces?" ":"")).repeat($c(r));return r.options.ruleSpaces?n.slice(0,-1):n}var Qc=b(()=>{Gc();No()});var Yc,Kc=b(()=>{Qu();Ku();nc();oc();lc();bc();wc();kc();yc();vc();_c();Cc();Oc();qc();Rc();Bc();Wc();Hc();Qc();Yc={blockquote:Ju,break:ko,code:rc,definition:ic,emphasis:xo,hardBreak:ko,heading:gc,html:So,image:Fo,imageReference:Oo,inlineCode:Lo,link:Io,linkReference:qo,list:Fc,listItem:Ic,paragraph:Nc,root:jc,strong:Do,text:Uc,thematicBreak:Jc}});function Gy(e,t,r,n){if(t.type==="code"&&or(t,n)&&(e.type==="list"||e.type===t.type&&or(e,n)))return!1;if("spread"in r&&typeof r.spread=="boolean")return e.type==="paragraph"&&(e.type===t.type||t.type==="definition"||t.type==="heading"&&mn(t,n))?void 0:r.spread?1:0}var Xc,Zc=b(()=>{yo();Eo();Xc=[Gy]});var mt,ef,tf=b(()=>{mt=["autolink","destinationLiteral","destinationRaw","reference","titleQuote","titleApostrophe"],ef=[{character:"	",after:"[\\r\\n]",inConstruct:"phrasing"},{character:"	",before:"[\\r\\n]",inConstruct:"phrasing"},{character:"	",inConstruct:["codeFencedLangGraveAccent","codeFencedLangTilde"]},{character:"\r",inConstruct:["codeFencedLangGraveAccent","codeFencedLangTilde","codeFencedMetaGraveAccent","codeFencedMetaTilde","destinationLiteral","headingAtx"]},{character:`
`,inConstruct:["codeFencedLangGraveAccent","codeFencedLangTilde","codeFencedMetaGraveAccent","codeFencedMetaTilde","destinationLiteral","headingAtx"]},{character:" ",after:"[\\r\\n]",inConstruct:"phrasing"},{character:" ",before:"[\\r\\n]",inConstruct:"phrasing"},{character:" ",inConstruct:["codeFencedLangGraveAccent","codeFencedLangTilde"]},{character:"!",after:"\\[",inConstruct:"phrasing",notInConstruct:mt},{character:'"',inConstruct:"titleQuote"},{atBreak:!0,character:"#"},{character:"#",inConstruct:"headingAtx",after:`(?:[\r
]|$)`},{character:"&",after:"[#A-Za-z]",inConstruct:"phrasing"},{character:"'",inConstruct:"titleApostrophe"},{character:"(",inConstruct:"destinationRaw"},{before:"\\]",character:"(",inConstruct:"phrasing",notInConstruct:mt},{atBreak:!0,before:"\\d+",character:")"},{character:")",inConstruct:"destinationRaw"},{atBreak:!0,character:"*",after:`(?:[ 	\r
*])`},{character:"*",inConstruct:"phrasing",notInConstruct:mt},{atBreak:!0,character:"+",after:`(?:[ 	\r
])`},{atBreak:!0,character:"-",after:`(?:[ 	\r
-])`},{atBreak:!0,before:"\\d+",character:".",after:`(?:[ 	\r
]|$)`},{atBreak:!0,character:"<",after:"[!/?A-Za-z]"},{character:"<",after:"[!/?A-Za-z]",inConstruct:"phrasing",notInConstruct:mt},{character:"<",inConstruct:"destinationLiteral"},{atBreak:!0,character:"="},{atBreak:!0,character:">"},{character:">",inConstruct:"destinationLiteral"},{atBreak:!0,character:"["},{character:"[",inConstruct:"phrasing",notInConstruct:mt},{character:"[",inConstruct:["label","reference"]},{character:"\\",after:"[\\r\\n]",inConstruct:"phrasing"},{character:"]",inConstruct:["label","reference"]},{atBreak:!0,character:"_"},{character:"_",inConstruct:"phrasing",notInConstruct:mt},{atBreak:!0,character:"`"},{character:"`",inConstruct:["codeFencedLangGraveAccent","codeFencedMetaGraveAccent"]},{character:"`",inConstruct:"phrasing",notInConstruct:mt},{atBreak:!0,character:"~"}]});function rf(e){return e.label||!e.identifier?e.label||"":un(e.identifier)}var nf=b(()=>{fo()});function of(e){if(!e._compiled){let t=(e.atBreak?"[\\r\\n][\\t ]*":"")+(e.before?"(?:"+e.before+")":"");e._compiled=new RegExp((t?"("+t+")":"")+(/[|\\{}()[\]^$+*?.-]/.test(e.character)?"\\":"")+e.character+(e.after?"(?:"+e.after+")":""),"g")}return e._compiled}var af=b(()=>{});function sf(e,t,r){let n=t.indexStack,i=e.children||[],o=[],a=-1,s=r.before,l;n.push(-1);let c=t.createTracker(r);for(;++a<i.length;){let u=i[a],f;if(n[n.length-1]=a,a+1<i.length){let w=t.handle.handlers[i[a+1].type];w&&w.peek&&(w=w.peek),f=w?w(i[a+1],e,t,{before:"",after:"",...c.current()}).charAt(0):""}else f=r.after;o.length>0&&(s==="\r"||s===`
`)&&u.type==="html"&&(o[o.length-1]=o[o.length-1].replace(/(\r?\n|\r)$/," "),s=" ",c=t.createTracker(r),c.move(o.join("")));let p=t.handle(u,e,t,{...c.current(),after:f,before:s});l&&l===p.slice(0,1)&&(p=xe(l.charCodeAt(0))+p.slice(1));let h=t.attentionEncodeSurroundingInfo;t.attentionEncodeSurroundingInfo=void 0,l=void 0,h&&(o.length>0&&h.before&&s===o[o.length-1].slice(-1)&&(o[o.length-1]=o[o.length-1].slice(0,-1)+xe(s.charCodeAt(0))),h.after&&(l=f)),c.move(p),o.push(p),s=p.slice(-1)}return n.pop(),o.join("")}var lf=b(()=>{Nt()});function uf(e,t,r){let n=t.indexStack,i=e.children||[],o=t.createTracker(r),a=[],s=-1;for(n.push(-1);++s<i.length;){let l=i[s];n[n.length-1]=s,a.push(o.move(t.handle(l,e,t,{before:`
`,after:`
`,...o.current()}))),l.type!=="list"&&(t.bulletLastUsed=void 0),s<i.length-1&&a.push(o.move(Jy(l,i[s+1],e,t)))}return n.pop(),a.join("")}function Jy(e,t,r,n){let i=n.join.length;for(;i--;){let o=n.join[i](e,t,r,n);if(o===!0||o===1)break;if(typeof o=="number")return`
`.repeat(1+o);if(o===!1)return`

<!---->

`}return`

`}var cf=b(()=>{});function ff(e,t){let r=[],n=0,i=0,o;for(;o=Qy.exec(e);)a(e.slice(n,o.index)),r.push(o[0]),n=o.index+o[0].length,i++;return a(e.slice(n)),r.join("");function a(s){r.push(t(s,i,!s))}}var Qy,pf=b(()=>{Qy=/\r?\n|\r/g});function df(e,t,r){let n=(r.before||"")+(t||"")+(r.after||""),i=[],o=[],a={},s=-1;for(;++s<e.unsafe.length;){let u=e.unsafe[s];if(!cn(e.stack,u))continue;let f=e.compilePattern(u),p;for(;p=f.exec(n);){let h="before"in u||!!u.atBreak,w="after"in u,g=p.index+(h?p[1].length:0);i.includes(g)?(a[g].before&&!h&&(a[g].before=!1),a[g].after&&!w&&(a[g].after=!1)):(i.push(g),a[g]={before:h,after:w})}}i.sort(Yy);let l=r.before?r.before.length:0,c=n.length-(r.after?r.after.length:0);for(s=-1;++s<i.length;){let u=i[s];u<l||u>=c||u+1<c&&i[s+1]===u+1&&a[u].after&&!a[u+1].before&&!a[u+1].after||i[s-1]===u-1&&a[u].before&&!a[u-1].before&&!a[u-1].after||(l!==u&&o.push(hf(n.slice(l,u),"\\")),l=u,/[!-/:-@[-`{-~]/.test(n.charAt(u))&&(!r.encode||!r.encode.includes(n.charAt(u)))?o.push("\\"):(o.push(xe(n.charCodeAt(u))),l++))}return o.push(hf(n.slice(l,c),r.after)),o.join("")}function Yy(e,t){return e-t}function hf(e,t){let r=/\\(?=[!-/:-@[-`{-~])/g,n=[],i=[],o=e+t,a=-1,s=0,l;for(;l=r.exec(o);)n.push(l.index);for(;++a<n.length;)s!==n[a]&&i.push(e.slice(s,n[a])),i.push("\\"),s=n[a];return i.push(e.slice(s)),i.join("")}var mf=b(()=>{Nt();wo()});function gf(e){let t=e||{},r=t.now||{},n=t.lineShift||0,i=r.line||1,o=r.column||1;return{move:l,current:a,shift:s};function a(){return{now:{line:i,column:o},lineShift:n}}function s(c){n+=c}function l(c){let u=c||"",f=u.split(/\r?\n|\r/g),p=f[f.length-1];return i+=f.length-1,o=f.length===1?o+p.length:1+p.length+n,u}}var bf=b(()=>{});function Mo(e,t){let r=t||{},n={associationId:rf,containerPhrasing:ev,containerFlow:tv,createTracker:gf,compilePattern:of,enter:o,handlers:{...Yc},handle:void 0,indentLines:ff,indexStack:[],join:[...Xc],options:{},safe:rv,stack:[],unsafe:[...ef]};bo(n,r),n.options.tightDefinitions&&n.join.push(Zy),n.handle=Uu("type",{invalid:Ky,unknown:Xy,handlers:n.handlers});let i=n.handle(e,void 0,n,{before:`
`,after:`
`,now:{line:1,column:1},lineShift:0});return i&&i.charCodeAt(i.length-1)!==10&&i.charCodeAt(i.length-1)!==13&&(i+=`
`),i;function o(a){return n.stack.push(a),s;function s(){n.stack.pop()}}}function Ky(e){throw new Error("Cannot handle value `"+e+"`, expected node")}function Xy(e){let t=e;throw new Error("Cannot handle unknown node `"+t.type+"`")}function Zy(e,t){if(e.type==="definition"&&e.type===t.type)return 0}function ev(e,t){return sf(e,this,t)}function tv(e,t){return uf(e,this,t)}function rv(e,t){return df(this,e,t)}var wf=b(()=>{Hu();Gu();Kc();Zc();tf();nf();af();lf();cf();pf();mf();bf()});var kf=b(()=>{wf()});function wn(e){let t=this;t.compiler=r;function r(n){return Mo(n,{...t.data("settings"),...e,extensions:t.data("toMarkdownExtensions")||[]})}}var yf=b(()=>{kf()});var vf=b(()=>{yf()});function jo(e){if(e)throw e}var xf=b(()=>{});var Lf=N((VF,Of)=>{"use strict";var kn=Object.prototype.hasOwnProperty,Ff=Object.prototype.toString,_f=Object.defineProperty,Cf=Object.getOwnPropertyDescriptor,Pf=function(t){return typeof Array.isArray=="function"?Array.isArray(t):Ff.call(t)==="[object Array]"},Af=function(t){if(!t||Ff.call(t)!=="[object Object]")return!1;var r=kn.call(t,"constructor"),n=t.constructor&&t.constructor.prototype&&kn.call(t.constructor.prototype,"isPrototypeOf");if(t.constructor&&!r&&!n)return!1;var i;for(i in t);return typeof i>"u"||kn.call(t,i)},Ef=function(t,r){_f&&r.name==="__proto__"?_f(t,r.name,{enumerable:!0,configurable:!0,value:r.newValue,writable:!0}):t[r.name]=r.newValue},Sf=function(t,r){if(r==="__proto__")if(kn.call(t,r)){if(Cf)return Cf(t,r).value}else return;return t[r]};Of.exports=function e(){var t,r,n,i,o,a,s=arguments[0],l=1,c=arguments.length,u=!1;for(typeof s=="boolean"&&(u=s,s=arguments[1]||{},l=2),(s==null||typeof s!="object"&&typeof s!="function")&&(s={});l<c;++l)if(t=arguments[l],t!=null)for(r in t)n=Sf(s,r),i=Sf(t,r),s!==i&&(u&&i&&(Af(i)||(o=Pf(i)))?(o?(o=!1,a=n&&Pf(n)?n:[]):a=n&&Af(n)?n:{},Ef(s,{name:r,newValue:e(u,a,i)})):typeof i<"u"&&Ef(s,{name:r,newValue:i}));return s}});function ar(e){if(typeof e!="object"||e===null)return!1;let t=Object.getPrototypeOf(e);return(t===null||t===Object.prototype||Object.getPrototypeOf(t)===null)&&!(Symbol.toStringTag in e)&&!(Symbol.iterator in e)}var Tf=b(()=>{});function Bo(){let e=[],t={run:r,use:n};return t;function r(...i){let o=-1,a=i.pop();if(typeof a!="function")throw new TypeError("Expected function as last argument, not "+a);s(null,...i);function s(l,...c){let u=e[++o],f=-1;if(l){a(l);return}for(;++f<i.length;)(c[f]===null||c[f]===void 0)&&(c[f]=i[f]);i=c,u?If(u,s)(...c):a(null,...c)}}function n(i){if(typeof i!="function")throw new TypeError("Expected `middelware` to be a function, not "+i);return e.push(i),t}}function If(e,t){let r;return n;function n(...a){let s=e.length>a.length,l;s&&a.push(i);try{l=e.apply(this,a)}catch(c){let u=c;if(s&&r)throw u;return i(u)}s||(l&&l.then&&typeof l.then=="function"?l.then(o,i):l instanceof Error?i(l):o(l))}function i(a,...s){r||(r=!0,t(a,...s))}function o(a){i(null,a)}}var qf=b(()=>{});var Nf=b(()=>{qf()});var ne,Rf=b(()=>{ho();ne=class extends Error{constructor(t,r,n){super(),typeof r=="string"&&(n=r,r=void 0);let i="",o={},a=!1;if(r&&("line"in r&&"column"in r?o={place:r}:"start"in r&&"end"in r?o={place:r}:"type"in r?o={ancestors:[r],place:r.position}:o={...r}),typeof t=="string"?i=t:!o.cause&&t&&(a=!0,i=t.message,o.cause=t),!o.ruleId&&!o.source&&typeof n=="string"){let l=n.indexOf(":");l===-1?o.ruleId=n:(o.source=n.slice(0,l),o.ruleId=n.slice(l+1))}if(!o.place&&o.ancestors&&o.ancestors){let l=o.ancestors[o.ancestors.length-1];l&&(o.place=l.position)}let s=o.place&&"start"in o.place?o.place.start:o.place;this.ancestors=o.ancestors||void 0,this.cause=o.cause||void 0,this.column=s?s.column:void 0,this.fatal=void 0,this.file,this.message=i,this.line=s?s.line:void 0,this.name=Ye(o.place)||"1:1",this.place=o.place||void 0,this.reason=this.message,this.ruleId=o.ruleId||void 0,this.source=o.source||void 0,this.stack=a&&o.cause&&typeof o.cause.stack=="string"?o.cause.stack:"",this.actual,this.expected,this.note,this.url}};ne.prototype.file="";ne.prototype.name="";ne.prototype.reason="";ne.prototype.message="";ne.prototype.stack="";ne.prototype.column=void 0;ne.prototype.line=void 0;ne.prototype.ancestors=void 0;ne.prototype.cause=void 0;ne.prototype.fatal=void 0;ne.prototype.place=void 0;ne.prototype.ruleId=void 0;ne.prototype.source=void 0});var Df=b(()=>{Rf()});var Se,Mf=b(()=>{Se=H(require("node:path"),1)});var zo,XF,jf=b(()=>{zo=H(require("node:process"),1),XF=globalThis.process??{browser:!0,cwd:()=>"/",env:{},platform:"android"}});function yn(e){return!!(e!==null&&typeof e=="object"&&"href"in e&&e.href&&"protocol"in e&&e.protocol&&e.auth===void 0)}var Bf=b(()=>{});var Vo,zf=b(()=>{Vo=require("node:url");Bf()});function Uo(e,t){if(e&&e.includes(Se.default.sep))throw new Error("`"+t+"` cannot be a path: did not expect `"+Se.default.sep+"`")}function Ho(e,t){if(!e)throw new Error("`"+t+"` cannot be empty")}function Vf(e,t){if(!e)throw new Error("Setting `"+t+"` requires `path` to be set too")}function nv(e){return!!(e&&typeof e=="object"&&"byteLength"in e&&"byteOffset"in e)}var nO,Wo,sr,Wf=b(()=>{Df();Mf();jf();zf();nO=globalThis.process??{browser:!0,cwd:()=>"/",env:{},platform:"android"},Wo=["history","path","basename","stem","extname","dirname"],sr=class{constructor(t){let r;t?yn(t)?r={path:t}:typeof t=="string"||nv(t)?r={value:t}:r=t:r={},this.cwd="cwd"in r?"":zo.default.cwd(),this.data={},this.history=[],this.messages=[],this.value,this.map,this.result,this.stored;let n=-1;for(;++n<Wo.length;){let o=Wo[n];o in r&&r[o]!==void 0&&r[o]!==null&&(this[o]=o==="history"?[...r[o]]:r[o])}let i;for(i in r)Wo.includes(i)||(this[i]=r[i])}get basename(){return typeof this.path=="string"?Se.default.basename(this.path):void 0}set basename(t){Ho(t,"basename"),Uo(t,"basename"),this.path=Se.default.join(this.dirname||"",t)}get dirname(){return typeof this.path=="string"?Se.default.dirname(this.path):void 0}set dirname(t){Vf(this.basename,"dirname"),this.path=Se.default.join(t||"",this.basename)}get extname(){return typeof this.path=="string"?Se.default.extname(this.path):void 0}set extname(t){if(Uo(t,"extname"),Vf(this.dirname,"extname"),t){if(t.codePointAt(0)!==46)throw new Error("`extname` must start with `.`");if(t.includes(".",1))throw new Error("`extname` cannot contain multiple dots")}this.path=Se.default.join(this.dirname,this.stem+(t||""))}get path(){return this.history[this.history.length-1]}set path(t){yn(t)&&(t=(0,Vo.fileURLToPath)(t)),Ho(t,"path"),this.path!==t&&this.history.push(t)}get stem(){return typeof this.path=="string"?Se.default.basename(this.path,this.extname):void 0}set stem(t){Ho(t,"stem"),Uo(t,"stem"),this.path=Se.default.join(this.dirname||"",t+(this.extname||""))}fail(t,r,n){let i=this.message(t,r,n);throw i.fatal=!0,i}info(t,r,n){let i=this.message(t,r,n);return i.fatal=void 0,i}message(t,r,n){let i=new ne(t,r,n);return this.path&&(i.name=this.path+":"+i.name,i.file=this.path),i.fatal=!1,this.messages.push(i),i}toString(t){return this.value===void 0?"":typeof this.value=="string"?this.value:new TextDecoder(t||void 0).decode(this.value)}}});var Uf=b(()=>{Wf()});var Hf,$f=b(()=>{Hf=function(e){let n=this.constructor.prototype,i=n[e],o=function(){return i.apply(o,arguments)};return Object.setPrototypeOf(o,n),o}});function $o(e,t){if(typeof t!="function")throw new TypeError("Cannot `"+e+"` without `parser`")}function Go(e,t){if(typeof t!="function")throw new TypeError("Cannot `"+e+"` without `compiler`")}function Jo(e,t){if(t)throw new Error("Cannot call `"+e+"` on a frozen processor.\nCreate a new processor first, by calling it: use `processor()` instead of `processor`.")}function Gf(e){if(!ar(e)||typeof e.type!="string")throw new TypeError("Expected node, got `"+e+"`")}function Jf(e,t,r){if(!r)throw new Error("`"+e+"` finished async. Use `"+t+"` instead")}function vn(e){return ov(e)?e:new sr(e)}function ov(e){return!!(e&&typeof e=="object"&&"message"in e&&"messages"in e)}function av(e){return typeof e=="string"||sv(e)}function sv(e){return!!(e&&typeof e=="object"&&"byteLength"in e&&"byteOffset"in e)}var xn,pO,iv,Qo,Yo,Qf=b(()=>{xf();xn=H(Lf(),1);Tf();Nf();Uf();$f();pO=globalThis.process??{browser:!0,cwd:()=>"/",env:{},platform:"android"},iv={}.hasOwnProperty,Qo=class e extends Hf{constructor(){super("copy"),this.Compiler=void 0,this.Parser=void 0,this.attachers=[],this.compiler=void 0,this.freezeIndex=-1,this.frozen=void 0,this.namespace={},this.parser=void 0,this.transformers=Bo()}copy(){let t=new e,r=-1;for(;++r<this.attachers.length;){let n=this.attachers[r];t.use(...n)}return t.data((0,xn.default)(!0,{},this.namespace)),t}data(t,r){return typeof t=="string"?arguments.length===2?(Jo("data",this.frozen),this.namespace[t]=r,this):iv.call(this.namespace,t)&&this.namespace[t]||void 0:t?(Jo("data",this.frozen),this.namespace=t,this):this.namespace}freeze(){if(this.frozen)return this;let t=this;for(;++this.freezeIndex<this.attachers.length;){let[r,...n]=this.attachers[this.freezeIndex];if(n[0]===!1)continue;n[0]===!0&&(n[0]=void 0);let i=r.call(t,...n);typeof i=="function"&&this.transformers.use(i)}return this.frozen=!0,this.freezeIndex=Number.POSITIVE_INFINITY,this}parse(t){this.freeze();let r=vn(t),n=this.parser||this.Parser;return $o("parse",n),n(String(r),r)}process(t,r){let n=this;return this.freeze(),$o("process",this.parser||this.Parser),Go("process",this.compiler||this.Compiler),r?i(void 0,r):new Promise(i);function i(o,a){let s=vn(t),l=n.parse(s);n.run(l,s,function(u,f,p){if(u||!f||!p)return c(u);let h=f,w=n.stringify(h,p);av(w)?p.value=w:p.result=w,c(u,p)});function c(u,f){u||!f?a(u):o?o(f):r(void 0,f)}}}processSync(t){let r=!1,n;return this.freeze(),$o("processSync",this.parser||this.Parser),Go("processSync",this.compiler||this.Compiler),this.process(t,i),Jf("processSync","process",r),n;function i(o,a){r=!0,jo(o),n=a}}run(t,r,n){Gf(t),this.freeze();let i=this.transformers;return!n&&typeof r=="function"&&(n=r,r=void 0),n?o(void 0,n):new Promise(o);function o(a,s){let l=vn(r);i.run(t,l,c);function c(u,f,p){let h=f||t;u?s(u):a?a(h):n(void 0,h,p)}}}runSync(t,r){let n=!1,i;return this.run(t,r,o),Jf("runSync","run",n),i;function o(a,s){jo(a),i=s,n=!0}}stringify(t,r){this.freeze();let n=vn(r),i=this.compiler||this.Compiler;return Go("stringify",i),Gf(t),i(t,n)}use(t,...r){let n=this.attachers,i=this.namespace;if(Jo("use",this.frozen),t!=null)if(typeof t=="function")l(t,r);else if(typeof t=="object")Array.isArray(t)?s(t):a(t);else throw new TypeError("Expected usable value, not `"+t+"`");return this;function o(c){if(typeof c=="function")l(c,[]);else if(typeof c=="object")if(Array.isArray(c)){let[u,...f]=c;l(u,f)}else a(c);else throw new TypeError("Expected usable value, not `"+c+"`")}function a(c){if(!("plugins"in c)&&!("settings"in c))throw new Error("Expected usable value but received an empty preset, which is probably a mistake: presets typically come with `plugins` and sometimes with `settings`, but this has neither");s(c.plugins),c.settings&&(i.settings=(0,xn.default)(!0,i.settings,c.settings))}function s(c){let u=-1;if(c!=null)if(Array.isArray(c))for(;++u<c.length;){let f=c[u];o(f)}else throw new TypeError("Expected a list of plugins, not `"+c+"`")}function l(c,u){let f=-1,p=-1;for(;++f<n.length;)if(n[f][0]===c){p=f;break}if(p===-1)n.push([c,...u]);else if(u.length>0){let[h,...w]=u,g=n[p][1];ar(g)&&ar(h)&&(h=(0,xn.default)(!0,g,h)),n[p]=[c,h,...w]}}}},Yo=new Qo().freeze()});var Yf=b(()=>{Qf()});var Kf={};_r(Kf,{remark:()=>lv});var vO,lv,Xf=b(()=>{go();vf();Yf();vO=globalThis.process??{browser:!0,cwd:()=>"/",env:{},platform:"android"},lv=Yo().use(ir).use(wn).freeze()});var ep=N((PO,Zf)=>{(()=>{"use strict";var e={d:(w,g)=>{for(var x in g)e.o(g,x)&&!e.o(w,x)&&Object.defineProperty(w,x,{enumerable:!0,get:g[x]})},o:(w,g)=>Object.prototype.hasOwnProperty.call(w,g),r:w=>{typeof Symbol<"u"&&Symbol.toStringTag&&Object.defineProperty(w,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(w,"__esModule",{value:!0})}},t={};e.r(t),e.d(t,{default:()=>h,wikiLinkPlugin:()=>p});var r={horizontalTab:-2,virtualSpace:-1,nul:0,eof:null,space:32};function n(w){return w<r.nul||w===r.space}function i(w){return w<r.horizontalTab}var o={553:w=>{w.exports=function(g){var x,k;return g._compiled||(x=g.before?"(?:"+g.before+")":"",k=g.after?"(?:"+g.after+")":"",g.atBreak&&(x="[\\r\\n][\\t ]*"+x),g._compiled=new RegExp((x?"("+x+")":"")+(/[|\\{}()[\]^$+*?.-]/.test(g.character)?"\\":"")+g.character+(k||""),"g")),g._compiled}},112:w=>{function g(x,k,F){var _;if(!k)return F;for(typeof k=="string"&&(k=[k]),_=-1;++_<k.length;)if(x.indexOf(k[_])!==-1)return!0;return!1}w.exports=function(x,k){return g(x,k.inConstruct,!0)&&!g(x,k.notInConstruct)}},113:(w,g,x)=>{w.exports=function(I,v,q){for(var D,O,P,B,z,S,L,T,A=(q.before||"")+(v||"")+(q.after||""),U=[],K=[],X={},d=-1;++d<I.unsafe.length;)if(B=I.unsafe[d],F(I.stack,B))for(z=k(B);S=z.exec(A);)D="before"in B||B.atBreak,O="after"in B,P=S.index+(D?S[1].length:0),U.indexOf(P)===-1?(U.push(P),X[P]={before:D,after:O}):(X[P].before&&!D&&(X[P].before=!1),X[P].after&&!O&&(X[P].after=!1));for(U.sort(_),L=q.before?q.before.length:0,T=A.length-(q.after?q.after.length:0),d=-1;++d<U.length;)(P=U[d])<L||P>=T||P+1<T&&U[d+1]===P+1&&X[P].after&&!X[P+1].before&&!X[P+1].after||(L!==P&&K.push(V(A.slice(L,P),"\\")),L=P,!/[!-/:-@[-`{-~]/.test(A.charAt(P))||q.encode&&q.encode.indexOf(A.charAt(P))!==-1?(K.push("&#x"+A.charCodeAt(P).toString(16).toUpperCase()+";"),L++):K.push("\\"));return K.push(V(A.slice(L,T),q.after)),K.join("")};var k=x(553),F=x(112);function _(I,v){return I-v}function V(I,v){for(var q,D=/\\(?=[!-/:-@[-`{-~])/g,O=[],P=[],B=-1,z=0,S=I+v;q=D.exec(S);)O.push(q.index);for(;++B<O.length;)z!==O[B]&&P.push(I.slice(z,O[B])),P.push("\\"),z=O[B];return P.push(I.slice(z)),P.join("")}}},a={};function s(w){var g=a[w];if(g!==void 0)return g.exports;var x=a[w]={exports:{}};return o[w](x,x.exports,s),x.exports}s.n=w=>{var g=w&&w.__esModule?()=>w.default:()=>w;return s.d(g,{a:g}),g},s.d=(w,g)=>{for(var x in g)s.o(g,x)&&!s.o(w,x)&&Object.defineProperty(w,x,{enumerable:!0,get:g[x]})},s.o=(w,g)=>Object.prototype.hasOwnProperty.call(w,g);var l={};(()=>{function w(F={}){let _=F.permalinks||[],V=F.pageResolver||(P=>[P.replace(/ /g,"_").toLowerCase()]),I=F.newClassName||"new",v=F.wikiLinkClassName||"internal",q=F.hrefTemplate||(P=>`#/page/${P}`),D;function O(P){return P[P.length-1]}return{enter:{wikiLink:function(P){D={type:"wikiLink",value:null,data:{alias:null,permalink:null,exists:null}},this.enter(D,P)}},exit:{wikiLinkTarget:function(P){let B=this.sliceSerialize(P);O(this.stack).value=B},wikiLinkAlias:function(P){let B=this.sliceSerialize(P);O(this.stack).data.alias=B},wikiLink:function(P){this.exit(P);let B=D,z=V(B.value),S=z.find(K=>_.indexOf(K)!==-1),L=S!==void 0,T;T=L?S:z[0]||"";let A=B.value;B.data.alias&&(A=B.data.alias);let U=v;L||(U+=" "+I),B.data.alias=A,B.data.permalink=T,B.data.exists=L,B.data.hName="a",B.data.hProperties={className:U,href:q(T)},B.data.hChildren=[{type:"text",value:A}]}}}}s.d(l,{V:()=>w,x:()=>k});var g=s(113),x=s.n(g);function k(F={}){let _=F.aliasDivider||":";return{unsafe:[{character:"[",inConstruct:["phrasing","label","reference"]},{character:"]",inConstruct:["label","reference"]}],handlers:{wikiLink:function(V,I,v){let q=v.enter("wikiLink"),D=x()(v,V.value,{before:"[",after:"]"}),O=x()(v,V.data.alias,{before:"[",after:"]"}),P;return P=O!==D?`[[${D}${_}${O}]]`:`[[${D}]]`,q(),P}}}}})();var c=l.V,u=l.x;let f=!1;function p(w={}){let g=this.data();function x(k,F){g[k]?g[k].push(F):g[k]=[F]}!f&&(this.Parser&&this.Parser.prototype&&this.Parser.prototype.blockTokenizers||this.Compiler&&this.Compiler.prototype&&this.Compiler.prototype.visitors)&&(f=!0,console.warn("[remark-wiki-link] Warning: please upgrade to remark 13 to use this plugin")),x("micromarkExtensions",function(){var k=(arguments.length>0&&arguments[0]!==void 0?arguments[0]:{}).aliasDivider||":",F="]]";return{text:{91:{tokenize:function(_,V,I){var v,q,D=0,O=0,P=0;return function(A){return A!=="[[".charCodeAt(O)?I(A):(_.enter("wikiLink"),_.enter("wikiLinkMarker"),B(A))};function B(A){return O===2?(_.exit("wikiLinkMarker"),function(U){return i(U)||U===r.eof?I(U):(_.enter("wikiLinkData"),_.enter("wikiLinkTarget"),z(U))}(A)):A!=="[[".charCodeAt(O)?I(A):(_.consume(A),O++,B)}function z(A){return A===k.charCodeAt(D)?v?(_.exit("wikiLinkTarget"),_.enter("wikiLinkAliasMarker"),S(A)):I(A):A===F.charCodeAt(P)?v?(_.exit("wikiLinkTarget"),_.exit("wikiLinkData"),_.enter("wikiLinkMarker"),T(A)):I(A):i(A)||A===r.eof?I(A):(n(A)||(v=!0),_.consume(A),z)}function S(A){return D===k.length?(_.exit("wikiLinkAliasMarker"),_.enter("wikiLinkAlias"),L(A)):A!==k.charCodeAt(D)?I(A):(_.consume(A),D++,S)}function L(A){return A===F.charCodeAt(P)?q?(_.exit("wikiLinkAlias"),_.exit("wikiLinkData"),_.enter("wikiLinkMarker"),T(A)):I(A):i(A)||A===r.eof?I(A):(n(A)||(q=!0),_.consume(A),L)}function T(A){return P===2?(_.exit("wikiLinkMarker"),_.exit("wikiLink"),V(A)):A!==F.charCodeAt(P)?I(A):(_.consume(A),P++,T)}}}}}}(w)),x("fromMarkdownExtensions",c(w)),x("toMarkdownExtensions",u(w))}let h=p;Zf.exports=t})()});var np=N((AO,rp)=>{function uv(e){return e&&e.__esModule&&e.default?e.default:e}(function(){let t=require;require=Object.assign(r=>{let n=t(r)??{};return uv(n)},t)})();var Ko=Object.defineProperty,cv=Object.getOwnPropertyDescriptor,fv=Object.getOwnPropertyNames,pv=Object.prototype.hasOwnProperty,hv=(e,t)=>{for(var r in t)Ko(e,r,{get:t[r],enumerable:!0})},dv=(e,t,r,n)=>{if(t&&typeof t=="object"||typeof t=="function")for(let i of fv(t))!pv.call(e,i)&&i!==r&&Ko(e,i,{get:()=>t[i],enumerable:!(n=cv(t,i))||n.enumerable});return e},mv=e=>dv(Ko({},"__esModule",{value:!0}),e),tp={};hv(tp,{isUrl:()=>gv});rp.exports=mv(tp);function gv(e){try{return!e.includes("://")||e.trim()!==e?!1:(new URL(e),!0)}catch{return!1}}});var Zo=N((SO,op)=>{function bv(e){return e&&e.__esModule&&e.default?e.default:e}(function(){let t=require;require=Object.assign(r=>{let n=t(r)??{};return bv(n)},t)})();var Xo=Object.defineProperty,wv=Object.getOwnPropertyDescriptor,kv=Object.getOwnPropertyNames,yv=Object.prototype.hasOwnProperty,vv=(e,t)=>{for(var r in t)Xo(e,r,{get:t[r],enumerable:!0})},xv=(e,t,r,n)=>{if(t&&typeof t=="object"||typeof t=="function")for(let i of kv(t))!yv.call(e,i)&&i!==r&&Xo(e,i,{get:()=>t[i],enumerable:!(n=wv(t,i))||n.enumerable});return e},_v=e=>xv(Xo({},"__esModule",{value:!0}),e),ip={};vv(ip,{parseFrontmatter:()=>Pv,setFrontmatter:()=>Av});op.exports=_v(ip);var _n=require("obsidian"),Cv=At(),EO=globalThis.process??{browser:!0,cwd:()=>"/",env:{},platform:"android"};function Pv(e){let t=(0,_n.getFrontMatterInfo)(e);return(0,_n.parseYaml)(t.frontmatter)??{}}function Av(e,t){let r=(0,_n.getFrontMatterInfo)(e);if(Object.keys(t).length===0)return e.slice(r.contentStart);let n=(0,_n.stringifyYaml)(t);return r.exists?(0,Cv.insertAt)(e,n,r.from,r.to):`---
`+n+`---
`+e}});var gt=N((LO,pp)=>{function Ev(e){return e&&e.__esModule&&e.default?e.default:e}(function(){let t=require;require=Object.assign(r=>{let n=t(r)??{};return Ev(n)},t)})();var ea=Object.defineProperty,Sv=Object.getOwnPropertyDescriptor,Fv=Object.getOwnPropertyNames,Ov=Object.prototype.hasOwnProperty,Lv=(e,t)=>{for(var r in t)ea(e,r,{get:t[r],enumerable:!0})},Tv=(e,t,r,n)=>{if(t&&typeof t=="object"||typeof t=="function")for(let i of Fv(t))!Ov.call(e,i)&&i!==r&&ea(e,i,{get:()=>t[i],enumerable:!(n=Sv(t,i))||n.enumerable});return e},Iv=e=>Tv(ea({},"__esModule",{value:!0}),e),ap={};Lv(ap,{copySafe:()=>Rv,createFolderSafe:()=>Pn,createTempFile:()=>Dv,createTempFolder:()=>Cn,getAvailablePath:()=>ta,getMarkdownFilesSorted:()=>Mv,getNoteFilesSorted:()=>jv,getSafeRenamePath:()=>lp,isEmptyFolder:()=>Bv,listSafe:()=>up,process:()=>zv,readSafe:()=>cp,renameSafe:()=>Vv});pp.exports=Iv(ap);var FO=require("obsidian"),lr=Ct(),qv=zr(),sp=ot(),qe=Ve(),Nv=Sr(),le=Oe(),OO=globalThis.process??{browser:!0,cwd:()=>"/",env:{},platform:"android"};async function Rv(e,t,r){let n=(0,le.getFile)(e,t),i=(0,lr.parentFolderPath)(r);await Pn(e,i);let o=ta(e,r);try{await e.vault.copy(n,o)}catch(a){if(!await e.vault.exists(o))throw a}return o}async function Pn(e,t){if(await e.vault.adapter.exists(t))return!1;try{return await e.vault.createFolder(t),!0}catch(r){if(!await e.vault.exists(t))throw r;return!0}}async function Dv(e,t){let r=(0,le.getFileOrNull)(e,t);if(r)return sp.noopAsync;let n=await Cn(e,(0,lr.parentFolderPath)(t));try{await e.vault.create(t,"")}catch(i){if(!await e.vault.exists(t))throw i}return r=(0,le.getFile)(e,t),async()=>{r.deleted||await e.fileManager.trashFile(r),await n()}}async function Cn(e,t){let r=(0,le.getFolderOrNull)(e,t);if(r)return sp.noopAsync;let n=(0,lr.parentFolderPath)(t);await Cn(e,n);let i=await Cn(e,(0,lr.parentFolderPath)(t));return await Pn(e,t),r=(0,le.getFolder)(e,t),async()=>{r.deleted||await e.fileManager.trashFile(r),await i()}}function ta(e,t){let r=(0,qe.extname)(t);return e.vault.getAvailablePath((0,qe.join)((0,qe.dirname)(t),(0,qe.basename)(t,r)),r.slice(1))}function Mv(e){return e.vault.getMarkdownFiles().sort((t,r)=>t.path.localeCompare(r.path))}function jv(e){return e.vault.getAllLoadedFiles().filter(t=>(0,le.isFile)(t)&&(0,le.isNote)(e,t)).sort((t,r)=>t.path.localeCompare(r.path))}function lp(e,t,r){let n=(0,le.getPath)(e,t);if(e.vault.adapter.insensitive){let i=(0,qe.dirname)(r),o=(0,qe.basename)(r),a=null;for(;a=(0,le.getFolderOrNull)(e,i,!0),!a;)o=(0,qe.join)((0,qe.basename)(i),o),i=(0,qe.dirname)(i);r=(0,qe.join)(a.getParentPrefix(),o)}return n.toLowerCase()===r.toLowerCase()?r:ta(e,r)}async function Bv(e,t){let r=await up(e,(0,le.getPath)(e,t));return r.files.length===0&&r.folders.length===0}async function up(e,t){let r=(0,le.getPath)(e,t),n={files:[],folders:[]};if((await e.vault.adapter.stat(r))?.type!=="folder")return n;try{return await e.vault.adapter.list(r)}catch(i){if(await e.vault.exists(r))throw i;return n}}async function zv(e,t,r,n={}){let o={...{shouldFailOnMissingFile:!0},...n};await(0,qv.retryWithTimeout)(async()=>{let a=await cp(e,t);if(a===null)return u();let s=await(0,Nv.resolveValue)(r,a);if(s===null)return!1;let l=!0;if(!await fp(e,t,async f=>{await e.vault.process(f,p=>p!==a?(console.warn("Content has changed since it was read. Retrying...",{actualContent:p,expectedContent:a,path:f.path}),l=!1,p):s)}))return u();return l;function u(){if(o.shouldFailOnMissingFile){let f=(0,le.getPath)(e,t);throw new Error(`File '${f}' not found`)}return!0}},o)}async function cp(e,t){let r=null;return await fp(e,t,async n=>{r=await e.vault.read(n)}),r}async function Vv(e,t,r){let n=(0,le.getFile)(e,t,!1,!0),i=lp(e,t,r);if(n.path.toLowerCase()===i.toLowerCase())return n.path!==r&&await e.vault.rename(n,i),i;let o=(0,lr.parentFolderPath)(i);await Pn(e,o);try{await e.vault.rename(n,i)}catch(a){if(!await e.vault.exists(i)||await e.vault.exists(n.path))throw a}return i}async function fp(e,t,r){let n=(0,le.getPath)(e,t),i=(0,le.getFileOrNull)(e,n);if(!i||i.deleted)return!1;try{return await r(i),!0}catch(o){let a=(0,le.getFileOrNull)(e,n);if(!a||a.deleted)return!1;throw o}}});var ia=N((IO,mp)=>{function Wv(e){return e&&e.__esModule&&e.default?e.default:e}(function(){let t=require;require=Object.assign(r=>{let n=t(r)??{};return Wv(n)},t)})();var na=Object.defineProperty,Uv=Object.getOwnPropertyDescriptor,Hv=Object.getOwnPropertyNames,$v=Object.prototype.hasOwnProperty,Gv=(e,t)=>{for(var r in t)na(e,r,{get:t[r],enumerable:!0})},Jv=(e,t,r,n)=>{if(t&&typeof t=="object"||typeof t=="function")for(let i of Hv(t))!$v.call(e,i)&&i!==r&&na(e,i,{get:()=>t[i],enumerable:!(n=Uv(t,i))||n.enumerable});return e},Qv=e=>Jv(na({},"__esModule",{value:!0}),e),dp={};Gv(dp,{applyFileChanges:()=>Xv,isContentChange:()=>Xe,isFrontmatterChange:()=>ur});mp.exports=Qv(dp);var ra=ut(),Yv=Sr(),An=Oe(),hp=Zo(),Kv=gt(),TO=globalThis.process??{browser:!0,cwd:()=>"/",env:{},platform:"android"};async function Xv(e,t,r,n={}){await(0,Kv.process)(e,t,async i=>{let o=await(0,Yv.resolveValue)(r),a=(0,An.isCanvasFile)(e,t)?JSON.parse(i):(0,hp.parseFrontmatter)(i);for(let u of o)if(Xe(u)){let f=i.slice(u.startIndex,u.endIndex);if(f!==u.oldContent)return console.warn("Content mismatch",{actualContent:f,endIndex:u.endIndex,expectedContent:u.oldContent,path:(0,An.getPath)(e,t),startIndex:u.startIndex}),null}else if(ur(u)){let f=(0,ra.getNestedPropertyValue)(a,u.frontmatterKey);if(f!==u.oldContent)return console.warn("Content mismatch",{actualContent:f,expectedContent:u.oldContent,frontmatterKey:u.frontmatterKey,path:(0,An.getPath)(e,t)}),null}o.sort((u,f)=>Xe(u)&&Xe(f)?u.startIndex-f.startIndex:ur(u)&&ur(f)?u.frontmatterKey.localeCompare(f.frontmatterKey):Xe(u)?-1:1),o=o.filter((u,f)=>u.oldContent===u.newContent?!1:f===0?!0:!(0,ra.deepEqual)(u,o[f-1]));for(let u=1;u<o.length;u++){let f=o[u];if(!f)continue;let p=o[u-1];if(p&&Xe(p)&&Xe(f)&&p.endIndex&&f.startIndex&&p.endIndex>f.startIndex)return console.warn("Overlapping changes",{change:f,previousChange:p}),null}let s="",l=0,c=!1;for(let u of o)Xe(u)?(s+=i.slice(l,u.startIndex),s+=u.newContent,l=u.endIndex):ur(u)&&((0,ra.setNestedPropertyValue)(a,u.frontmatterKey,u.newContent),c=!0);return(0,An.isCanvasFile)(e,t)?s=JSON.stringify(a,null,"	"):(s+=i.slice(l),c&&(s=(0,hp.setFrontmatter)(s,a))),s},n)}function Xe(e){return e.startIndex!==void 0}function ur(e){return e.frontmatterKey!==void 0}});var En=N((qO,bp)=>{function Zv(e){return e&&e.__esModule&&e.default?e.default:e}(function(){let t=require;require=Object.assign(r=>{let n=t(r)??{};return Zv(n)},t)})();var oa=Object.defineProperty,ex=Object.getOwnPropertyDescriptor,tx=Object.getOwnPropertyNames,rx=Object.prototype.hasOwnProperty,nx=(e,t)=>{for(var r in t)oa(e,r,{get:t[r],enumerable:!0})},ix=(e,t,r,n)=>{if(t&&typeof t=="object"||typeof t=="function")for(let i of tx(t))!rx.call(e,i)&&i!==r&&oa(e,i,{get:()=>t[i],enumerable:!(n=ex(t,i))||n.enumerable});return e},ox=e=>ix(oa({},"__esModule",{value:!0}),e),gp={};nx(gp,{referenceToFileChange:()=>ax,sortReferences:()=>sx});bp.exports=ox(gp);var bt=Ct();function ax(e,t){if((0,bt.isReferenceCache)(e))return{endIndex:e.position.end.offset,newContent:t,oldContent:e.original,startIndex:e.position.start.offset};if((0,bt.isFrontmatterLinkCache)(e))return{frontmatterKey:e.key,newContent:t,oldContent:e.original};throw new Error("Unknown link type")}function sx(e){return e.sort((t,r)=>(0,bt.isFrontmatterLinkCache)(t)&&(0,bt.isFrontmatterLinkCache)(r)?t.key.localeCompare(r.key):(0,bt.isReferenceCache)(t)&&(0,bt.isReferenceCache)(r)?t.position.start.offset-r.position.start.offset:(0,bt.isFrontmatterLinkCache)(t)?1:-1)}});var jt=N((RO,Pp)=>{function lx(e){return e&&e.__esModule&&e.default?e.default:e}(function(){let t=require;require=Object.assign(r=>{let n=t(r)??{};return lx(n)},t)})();var aa=Object.defineProperty,ux=Object.getOwnPropertyDescriptor,cx=Object.getOwnPropertyNames,fx=Object.prototype.hasOwnProperty,px=(e,t)=>{for(var r in t)aa(e,r,{get:t[r],enumerable:!0})},hx=(e,t,r,n)=>{if(t&&typeof t=="object"||typeof t=="function")for(let i of cx(t))!fx.call(e,i)&&i!==r&&aa(e,i,{get:()=>t[i],enumerable:!(n=ux(t,i))||n.enumerable});return e},dx=e=>hx(aa({},"__esModule",{value:!0}),e),wp={};px(wp,{ensureMetadataCacheReady:()=>yp,getAllLinks:()=>xx,getBacklinksForFileOrPath:()=>vp,getBacklinksForFileSafe:()=>_x,getCacheSafe:()=>sa,getFrontmatterSafe:()=>Cx,registerFile:()=>xp,tempRegisterFileAndRun:()=>_p});Pp.exports=dx(wp);var mx=require("obsidian"),wt=Ct(),kp=zr(),gx=lt(),bx=ot(),wx=ut(),Ue=Oe(),kx=Zo(),yx=En(),vx=gt(),NO=globalThis.process??{browser:!0,cwd:()=>"/",env:{},platform:"android"},cr=(0,gx.getDebugger)("obsidian-dev-utils:MetadataCache:getCacheSafe");async function yp(e){for(let[t,r]of Object.entries(e.metadataCache.fileCache))r.hash&&(e.metadataCache.metadataCache[r.hash]||await sa(e,t))}function xx(e){let t=[];return e.links&&t.push(...e.links),e.embeds&&t.push(...e.embeds),e.frontmatterLinks&&t.push(...e.frontmatterLinks),(0,yx.sortReferences)(t),t=t.filter((r,n)=>{if(n===0)return!0;let i=t[n-1];return i?(0,wt.isReferenceCache)(r)&&(0,wt.isReferenceCache)(i)?r.position.start.offset!==i.position.start.offset:(0,wt.isFrontmatterLinkCache)(r)&&(0,wt.isFrontmatterLinkCache)(i)?r.key!==i.key:!0:!0}),t}function vp(e,t){let r=(0,Ue.getFile)(e,t,!0);return _p(e,r,()=>e.metadataCache.getBacklinksForFile(r))}async function _x(e,t,r={}){let n=e.metadataCache.getBacklinksForFile.safe;if(n)return n(t);let i=null;return await(0,kp.retryWithTimeout)(async()=>{let o=(0,Ue.getFile)(e,t);await yp(e),i=vp(e,o);for(let a of i.keys()){let s=(0,Ue.getFileOrNull)(e,a);if(!s)return!1;await Cp(e,s);let l=await(0,vx.readSafe)(e,s);if(!l)return!1;let c=(0,kx.parseFrontmatter)(l),u=i.get(a);if(!u)return!1;for(let f of u){let p;if((0,wt.isReferenceCache)(f))p=l.slice(f.position.start.offset,f.position.end.offset);else if((0,wt.isFrontmatterLinkCache)(f)){let h=(0,wx.getNestedPropertyValue)(c,f.key);if(typeof h!="string")return!1;p=h}else return!0;if(p!==f.original)return!1}}return!0},r),i}async function sa(e,t,r={}){let n=null;return await(0,kp.retryWithTimeout)(async()=>{let i=(0,Ue.getFileOrNull)(e,t);if(!i||i.deleted)return n=null,!0;await Cp(e,i);let o=e.metadataCache.getFileInfo(i.path),a=await e.vault.adapter.stat(i.path);return o?a?i.stat.mtime<a.mtime?(e.vault.onChange("modified",i.path,void 0,a),cr(`Cached timestamp for ${i.path} is from ${new Date(i.stat.mtime).toString()} which is older than the file system modification timestamp ${new Date(a.mtime).toString()}`),!1):o.mtime<a.mtime?(cr(`File cache info for ${i.path} is from ${new Date(o.mtime).toString()} which is older than the file modification timestamp ${new Date(a.mtime).toString()}`),!1):(n=e.metadataCache.getFileCache(i),n?!0:(cr(`File cache for ${i.path} is missing`),!1)):(cr(`File stat for ${i.path} is missing`),!1):(cr(`File cache info for ${i.path} is missing`),!1)},r),n}async function Cx(e,t){return(await sa(e,t))?.frontmatter??{}}function xp(e,t){if(!t.deleted)return bx.noop;let r=[],n=t;for(;n.deleted;)r.push(n.path),e.vault.fileMap[n.path]=n,n=n.parent??(0,Ue.getFolder)(e,(0,wt.parentFolderPath)(n.path),!0);return(0,Ue.isFile)(t)&&e.metadataCache.uniqueFileLookup.add(t.name.toLowerCase(),t),()=>{for(let i of r)delete e.vault.fileMap[i];(0,Ue.isFile)(t)&&e.metadataCache.uniqueFileLookup.remove(t.name.toLowerCase(),t)}}function _p(e,t,r){let n=xp(e,t);try{return r()}finally{n()}}async function Cp(e,t){if(!(0,Ue.isMarkdownFile)(e,t))return;let r=(0,Ue.getPath)(e,t);for(let n of e.workspace.getLeavesOfType("markdown"))n.view instanceof mx.MarkdownView&&n.view.file?.path===r&&await n.view.save()}});var Sp=N((DO,Ep)=>{function Px(e){return e&&e.__esModule&&e.default?e.default:e}(function(){let t=require;require=Object.assign(r=>{let n=t(r)??{};return Px(n)},t)})();var la=Object.defineProperty,Ax=Object.getOwnPropertyDescriptor,Ex=Object.getOwnPropertyNames,Sx=Object.prototype.hasOwnProperty,Fx=(e,t)=>{for(var r in t)la(e,r,{get:t[r],enumerable:!0})},Ox=(e,t,r,n)=>{if(t&&typeof t=="object"||typeof t=="function")for(let i of Ex(t))!Sx.call(e,i)&&i!==r&&la(e,i,{get:()=>t[i],enumerable:!(n=Ax(t,i))||n.enumerable});return e},Lx=e=>Ox(la({},"__esModule",{value:!0}),e),Ap={};Fx(Ap,{shouldUseRelativeLinks:()=>Tx,shouldUseWikilinks:()=>Ix});Ep.exports=Lx(Ap);function Tx(e){return e.vault.getConfig("newLinkFormat")==="relative"}function Ix(e){return!e.vault.getConfig("useMarkdownLinks")}});var On=N((jO,Wp)=>{function Lp(e){return e&&e.__esModule&&e.default?e.default:e}(function(){let t=require;require=Object.assign(r=>{let n=t(r)??{};return Lp(n)},t)})();var qx=Object.create,Fn=Object.defineProperty,Nx=Object.getOwnPropertyDescriptor,Rx=Object.getOwnPropertyNames,Dx=Object.getPrototypeOf,Mx=Object.prototype.hasOwnProperty,jx=(e,t)=>{for(var r in t)Fn(e,r,{get:t[r],enumerable:!0})},Tp=(e,t,r,n)=>{if(t&&typeof t=="object"||typeof t=="function")for(let i of Rx(t))!Mx.call(e,i)&&i!==r&&Fn(e,i,{get:()=>t[i],enumerable:!(n=Nx(t,i))||n.enumerable});return e},Bx=(e,t,r)=>(r=e!=null?qx(Dx(e)):{},Tp(t||!e||!e.__esModule?Fn(r,"default",{value:e,enumerable:!0}):r,e)),zx=e=>Tp(Fn({},"__esModule",{value:!0}),e),Ip={};jx(Ip,{convertLink:()=>Rp,editBacklinks:()=>Qx,editLinks:()=>ua,extractLinkFile:()=>Dp,generateMarkdownLink:()=>Mp,parseLink:()=>fr,shouldResetAlias:()=>jp,splitSubpath:()=>ca,testAngleBrackets:()=>Bp,testEmbed:()=>fa,testLeadingDot:()=>zp,testWikilink:()=>pa,updateLink:()=>Vp,updateLinksInFile:()=>Yx});Wp.exports=zx(Ip);var qp=require("obsidian"),Vx=(Xf(),Vn(Kf)),Wx=Bx(Lp((go(),Vn(Vu))),1),Ux=ep(),Ze=ut(),de=Ve(),Np=At(),Fp=np(),Hx=ia(),me=Oe(),Sn=jt(),Op=Sp(),$x=En(),MO=globalThis.process??{browser:!0,cwd:()=>"/",env:{},platform:"android"},Gx=/[\\\x00\x08\x0B\x0C\x0E-\x1F ]/g,Jx=/[\\[\]<>_*~=`$]/g;function Rp(e){let t=Dp(e.app,e.link,e.oldSourcePathOrFile??e.newSourcePathOrFile);return t?Vp((0,Ze.normalizeOptionalProperties)({app:e.app,link:e.link,newSourcePathOrFile:e.newSourcePathOrFile,newTargetPathOrFile:t,oldSourcePathOrFile:e.oldSourcePathOrFile,shouldForceMarkdownLinks:e.shouldForceMarkdownLinks,shouldUpdateFilenameAlias:e.shouldUpdateFilenameAlias})):e.link.original}async function Qx(e,t,r,n={}){let i=await(0,Sn.getBacklinksForFileSafe)(e,t,n);for(let o of i.keys()){let a=i.get(o)??[],s=new Set(a.map(l=>(0,Ze.toJson)(l)));await ua(e,o,l=>{let c=(0,Ze.toJson)(l);if(s.has(c))return r(l)},n)}}async function ua(e,t,r,n={}){await(0,Hx.applyFileChanges)(e,t,async()=>{let i=await(0,Sn.getCacheSafe)(e,t);if(!i)return[];let o=[];for(let a of(0,Sn.getAllLinks)(i)){let s=await r(a);s!==void 0&&o.push((0,$x.referenceToFileChange)(a,s))}return o},n)}function Dp(e,t,r){let{linkPath:n}=ca(t.link);return e.metadataCache.getFirstLinkpathDest(n,(0,me.getPath)(e,r))}function Mp(e){let{app:t}=e,n=(t.fileManager.generateMarkdownLink.defaultOptionsFn??(()=>({})))();e={...{isEmptyEmbedAliasAllowed:!0},...n,...e};let o=(0,me.getFile)(t,e.targetPathOrFile,e.isNonExistingFileAllowed);return(0,Sn.tempRegisterFileAndRun)(t,o,()=>{let a=(0,me.getPath)(t,e.sourcePathOrFile),s=e.subpath??"",l=e.alias??"",c=e.isEmbed??(e.originalLink?fa(e.originalLink):void 0)??!(0,me.isMarkdownFile)(t,o),u=e.isWikilink??(e.originalLink?pa(e.originalLink):void 0)??(0,Op.shouldUseWikilinks)(t),f=e.shouldForceRelativePath??(0,Op.shouldUseRelativeLinks)(t),p=e.shouldUseLeadingDot??(e.originalLink?zp(e.originalLink):void 0)??!1,h=e.shouldUseAngleBrackets??(e.originalLink?Bp(e.originalLink):void 0)??!1,w=o.path===a&&s?s:f?(0,de.relative)((0,de.dirname)(a),u?(0,me.trimMarkdownExtension)(t,o):o.path)+s:t.metadataCache.fileToLinktext(o,a,u)+s;f&&p&&!w.startsWith(".")&&!w.startsWith("#")&&(w="./"+w);let g=c?"!":"";if(u){l&&l.toLowerCase()===w.toLowerCase()&&(w=l,l="");let x=l?`|${l}`:"";return`${g}[[${w}${x}]]`}else return h?w=`<${w}>`:w=w.replace(Gx,function(x){return encodeURIComponent(x)}),!l&&(!c||!e.isEmptyEmbedAliasAllowed)&&(l=!e.shouldIncludeAttachmentExtensionToEmbedAlias||(0,me.isMarkdownFile)(t,o)?o.basename:o.name),l=l.replace(Jx,"\\$&"),`${g}[${l}](${w})`})}function fr(e){if((0,Fp.isUrl)(e))return{isEmbed:!1,isExternal:!0,isWikilink:!1,url:e};let t="!",r="<",n="](",i=")",o="|",a=e.startsWith(t);a&&(e=(0,Np.trimStart)(e,t));let l=(0,Vx.remark)().use(Wx.default).use(Ux.wikiLinkPlugin,{aliasDivider:o}).parse(e);if(l.children.length!==1)return null;let c=l.children[0];if(c?.type!=="paragraph"||c.children.length!==1)return null;let u=c.children[0];if(u?.position?.start.offset!==0||u.position.end.offset!==e.length)return null;switch(u.type){case"link":{let f=u,p=f.children[0],h=e.slice((p?.position?.end.offset??1)+n.length,(f.position?.end.offset??0)-i.length),w=e.startsWith(r)||h.startsWith(r),g=(0,Fp.isUrl)(f.url),x=f.url;if(!g&&!w)try{x=decodeURIComponent(x)}catch(k){console.error(`Failed to decode URL ${x}`,k)}return(0,Ze.normalizeOptionalProperties)({alias:p?.value,hasAngleBrackets:w,isEmbed:a,isExternal:g,isWikilink:!1,title:f.title??void 0,url:x})}case"wikiLink":{let f=u;return(0,Ze.normalizeOptionalProperties)({alias:e.includes(o)?f.data.alias:void 0,isEmbed:a,isWikilink:!0,url:f.value})}default:return null}}function jp(e){let{app:t,displayText:r,isWikilink:n,newSourcePathOrFile:i,oldSourcePathOrFile:o,oldTargetPath:a,targetPathOrFile:s}=e;if(n===!1)return!1;if(!r)return!0;let l=(0,me.getFile)(t,s,!0),c=(0,me.getPath)(t,i),u=(0,me.getPath)(t,o??i),f=(0,de.dirname)(c),p=(0,de.dirname)(u),h=new Set;for(let g of[l.path,a]){if(!g)continue;let x=(0,me.getPath)(t,g);h.add(x),h.add((0,de.basename)(x)),h.add((0,de.relative)(f,x)),h.add((0,de.relative)(p,x))}for(let g of[u,c])h.add(t.metadataCache.fileToLinktext(l,g,!1));let w=(0,qp.normalizePath)(r.split(" > ")[0]??"").replace(/^\.\//,"").toLowerCase();for(let g of h){if(g.toLowerCase()===w)return!0;let x=(0,de.dirname)(g),k=(0,de.basename)(g,(0,de.extname)(g));if((0,de.join)(x,k).toLowerCase()===w)return!0}return!1}function ca(e){let t=(0,qp.parseLinktext)((0,Np.normalize)(e));return{linkPath:t.path,subpath:t.subpath}}function Bp(e){return fr(e)?.hasAngleBrackets??!1}function fa(e){return fr(e)?.isEmbed??!1}function zp(e){return fr(e)?.url.startsWith("./")??!1}function pa(e){return fr(e)?.isWikilink??!1}function Vp(e){let{app:t,link:r,newSourcePathOrFile:n,newTargetPathOrFile:i,oldSourcePathOrFile:o,oldTargetPathOrFile:a,shouldForceMarkdownLinks:s,shouldUpdateFilenameAlias:l}=e;if(!i)return r.original;let c=(0,me.getFile)(t,i,!0),u=(0,me.getPath)(t,a??i),f=pa(r.original)&&s!==!0,{subpath:p}=ca(r.link);if((0,me.isCanvasFile)(t,n))return c.path+p;let h=jp((0,Ze.normalizeOptionalProperties)({app:t,displayText:r.displayText,isWikilink:f,newSourcePathOrFile:n,oldSourcePathOrFile:o,oldTargetPath:u,targetPathOrFile:c}))?void 0:r.displayText;return(l??!0)&&(h===(0,de.basename)(u,(0,de.extname)(u))?h=c.basename:h===(0,de.basename)(u)&&(h=c.name)),Mp((0,Ze.normalizeOptionalProperties)({alias:h,app:t,isWikilink:s?!1:void 0,originalLink:r.original,sourcePathOrFile:n,subpath:p,targetPathOrFile:c}))}async function Yx(e){let{app:t,newSourcePathOrFile:r,oldSourcePathOrFile:n,shouldForceMarkdownLinks:i,shouldUpdateEmbedOnlyLinks:o,shouldUpdateFilenameAlias:a}=e;(0,me.isCanvasFile)(t,r)&&!t.internalPlugins.getEnabledPluginById("canvas")||await ua(t,r,s=>{let l=fa(s.original);if(!(o!==void 0&&o!==l))return Rp((0,Ze.normalizeOptionalProperties)({app:t,link:s,newSourcePathOrFile:r,oldSourcePathOrFile:n,shouldForceMarkdownLinks:i,shouldUpdateFilenameAlias:a}))},e)}});var ga=N((BO,Hp)=>{function Kx(e){return e&&e.__esModule&&e.default?e.default:e}(function(){let t=require;require=Object.assign(r=>{let n=t(r)??{};return Kx(n)},t)})();var da=Object.defineProperty,Xx=Object.getOwnPropertyDescriptor,Zx=Object.getOwnPropertyNames,e_=Object.prototype.hasOwnProperty,t_=(e,t)=>{for(var r in t)da(e,r,{get:t[r],enumerable:!0})},r_=(e,t,r,n)=>{if(t&&typeof t=="object"||typeof t=="function")for(let i of Zx(t))!e_.call(e,i)&&i!==r&&da(e,i,{get:()=>t[i],enumerable:!(n=Xx(t,i))||n.enumerable});return e},n_=e=>r_(da({},"__esModule",{value:!0}),e),Up={};t_(Up,{deleteEmptyFolderHierarchy:()=>a_,deleteSafe:()=>ma});Hp.exports=n_(Up);var i_=ze(),pr=Oe(),o_=jt(),ha=gt();async function a_(e,t){let r=(0,pr.getFolderOrNull)(e,t);for(;r;){if(!await(0,ha.isEmptyFolder)(e,r))return;let n=r.parent;await ma(e,r.path),r=n}}async function ma(e,t,r,n,i){let o=(0,pr.getAbstractFileOrNull)(e,t);if(!o)return!1;let a=(0,pr.isFile)(o)||(i??!0);if((0,pr.isFile)(o)){let s=await(0,o_.getBacklinksForFileSafe)(e,o);r&&s.clear(r),s.count()!==0&&(n&&new Notice(`Attachment ${o.path} is still used by other notes. It will not be deleted.`),a=!1)}else if((0,pr.isFolder)(o)){let s=await(0,ha.listSafe)(e,o);for(let l of[...s.files,...s.folders])a&&=await ma(e,l,r,n);a&&=await(0,ha.isEmptyFolder)(e,o)}if(a)try{await e.fileManager.trashFile(o)}catch(s){await e.vault.exists(o.path)&&((0,i_.printError)(new Error(`Failed to delete ${o.path}`,{cause:s})),a=!1)}return a}});var Xp=N((zO,Kp)=>{function s_(e){return e&&e.__esModule&&e.default?e.default:e}(function(){let t=require;require=Object.assign(r=>{let n=t(r)??{};return s_(n)},t)})();var va=Object.defineProperty,l_=Object.getOwnPropertyDescriptor,u_=Object.getOwnPropertyNames,c_=Object.prototype.hasOwnProperty,f_=(e,t)=>{for(var r in t)va(e,r,{get:t[r],enumerable:!0})},p_=(e,t,r,n)=>{if(t&&typeof t=="object"||typeof t=="function")for(let i of u_(t))!c_.call(e,i)&&i!==r&&va(e,i,{get:()=>t[i],enumerable:!(n=l_(t,i))||n.enumerable});return e},h_=e=>p_(va({},"__esModule",{value:!0}),e),Qp={};f_(Qp,{registerRenameDeleteHandlers:()=>w_});Kp.exports=h_(Qp);var d_=yl(),m_=require("obsidian"),xa=lt(),g_=ot(),Tn=ut(),ge=Ve(),b_=Si(),hr=Mi(),_e=Oe(),dr=On(),kt=jt(),_a=Ti(),$p=gt(),wa=ga(),ka=new Map,mr=new Set,Ln=new Map;function w_(e,t){let r=In(e.app),n=e.manifest.id;r.set(n,t),Gp(e.app),e.register(()=>{r.delete(n),Gp(e.app)});let i=e.app;e.registerEvent(i.vault.on("delete",o=>{v_(e,o)})),e.registerEvent(i.vault.on("rename",(o,a)=>{P_(e,o,a)})),e.registerEvent(i.metadataCache.on("deleted",(o,a)=>{__(e,o,a)}))}async function k_(e,t,r,n,i){if(n.set(t,r),!(0,_e.isNote)(e,t))return;let o=gr(e),a=await(0,hr.getAttachmentFolderPath)(e,t),s=o.shouldRenameAttachmentFolder?await(0,hr.getAttachmentFolderPath)(e,r):a,l=(0,_e.getFolderOrNull)(e,a);if(!l||a===s&&!o.shouldRenameAttachmentFiles)return;let c=[];if(await(0,hr.hasOwnAttachmentFolder)(e,t))m_.Vault.recurseChildren(l,p=>{(0,_e.isFile)(p)&&c.push(p)});else for(let p of i){let h=(0,dr.extractLinkFile)(e,p,t);h&&h.path.startsWith(a)&&(await(0,kt.getBacklinksForFileSafe)(e,h)).keys().length===1&&c.push(h)}let u=(0,ge.basename)(t,(0,ge.extname)(t)),f=(0,ge.basename)(r,(0,ge.extname)(r));for(let p of c){if((0,_e.isNote)(e,p))continue;let h=(0,ge.relative)(a,p.path),w=(0,ge.join)(s,(0,ge.dirname)(h)),g=o.shouldRenameAttachmentFiles?p.basename.replaceAll(u,f):p.basename,x=(0,ge.join)(w,(0,ge.makeFileName)(g,p.extension));if(p.path!==x){if(o.shouldDeleteConflictingAttachments){let k=(0,_e.getFileOrNull)(e,x);k&&await e.fileManager.trashFile(k)}else x=e.vault.getAvailablePath((0,ge.join)(w,g),p.extension);n.set(p.path,x)}}}function In(e){return(0,b_.getObsidianDevUtilsState)(e,"renameDeleteHandlersMap",new Map).value}function gr(e){let t=In(e),r=Array.from(t.values()).reverse(),n={};for(let i of r){let o=i();n.shouldDeleteConflictingAttachments||=o.shouldDeleteConflictingAttachments??!1,n.shouldDeleteEmptyFolders||=o.shouldDeleteEmptyFolders??!1,n.shouldHandleDeletions||=o.shouldHandleDeletions??!1,n.shouldHandleRenames||=o.shouldHandleRenames??!1,n.shouldRenameAttachmentFiles||=o.shouldRenameAttachmentFiles??!1,n.shouldRenameAttachmentFolder||=o.shouldRenameAttachmentFolder??!1,n.shouldUpdateFilenameAliases||=o.shouldUpdateFilenameAliases??!1;let a=n.isPathIgnored;n.isPathIgnored=s=>a?.(s)??o.isPathIgnored?.(s)??!1}return n}async function y_(e,t){if((0,xa.getDebugger)("obsidian-dev-utils:RenameDeleteHandler:handleDelete")(`Handle Delete ${t}`),!(0,_e.isNote)(e,t))return;let r=gr(e);if(!r.shouldHandleDeletions||r.isPathIgnored?.(t))return;let n=ka.get(t);if(ka.delete(t),n){let a=(0,kt.getAllLinks)(n);for(let s of a){let l=(0,dr.extractLinkFile)(e,s,t);l&&((0,_e.isNote)(e,l)||await(0,wa.deleteSafe)(e,l,t,r.shouldDeleteEmptyFolders))}}let i=await(0,hr.getAttachmentFolderPath)(e,t),o=(0,_e.getFolderOrNull)(e,i);o&&await(0,hr.hasOwnAttachmentFolder)(e,t)&&await(0,wa.deleteSafe)(e,o,t,!1,r.shouldDeleteEmptyFolders)}function v_(e,t){let r=e.app;if(!Ca(e))return;let n=t.path;(0,_a.addToQueue)(r,()=>y_(r,n))}function x_(e,t,r){let n=gr(e);n.isPathIgnored?.(t.path)||n.shouldHandleDeletions&&(0,_e.isMarkdownFile)(e,t)&&r&&ka.set(t.path,r)}function __(e,t,r){Ca(e)&&x_(e.app,t,r)}function C_(e,t,r){let n=Yp(t,r);if((0,xa.getDebugger)("obsidian-dev-utils:RenameDeleteHandler:handleRename")(`Handle Rename ${n}`),mr.has(n)){mr.delete(n);return}let i=gr(e);if(!i.shouldHandleRenames||i.isPathIgnored?.(t)||i.isPathIgnored?.(r))return;let o=e.metadataCache.getCache(t)??e.metadataCache.getCache(r),a=o?(0,kt.getAllLinks)(o):[],s=(0,kt.getBacklinksForFileOrPath)(e,t).data;(0,_a.addToQueue)(e,()=>ya(e,t,r,s,a))}async function ya(e,t,r,n,i,o){let a=Ln.get(t);if(a){Ln.delete(t);for(let f of a)await ya(e,f.oldPath,r,n,i,f.combinedBacklinksMap)}let s=e.metadataCache.getCache(t)??e.metadataCache.getCache(r),l=s?(0,kt.getAllLinks)(s):[],c=(0,kt.getBacklinksForFileOrPath)(e,t).data;for(let f of l)i.includes(f)||i.push(f);if(e.vault.adapter.insensitive&&t.toLowerCase()===r.toLowerCase()){let f=(0,ge.join)((0,ge.dirname)(r),"__temp__"+(0,ge.basename)(r));await Jp(e,r,f),await ya(e,t,f,n,i),await e.vault.rename((0,_e.getFile)(e,f),r);return}let u=(0,d_.around)(e.fileManager,{updateAllLinks:()=>g_.noopAsync});try{let f=new Map;await k_(e,t,r,f,i);let p=new Map;ba(n,f,p,t),ba(c,f,p,t);for(let g of f.keys()){if(g===t)continue;let x=(await(0,kt.getBacklinksForFileSafe)(e,g)).data;ba(x,f,p,g)}let h=new Set;for(let[g,x]of f.entries()){if(g===t)continue;let k=await Jp(e,g,x);f.set(g,k),h.add((0,ge.dirname)(g))}let w=gr(e);if(w.shouldDeleteEmptyFolders)for(let g of h)await(0,wa.deleteEmptyFolderHierarchy)(e,g);for(let[g,x]of Array.from(p.entries()).concat(Array.from(o?.entries()??[])))await(0,dr.editLinks)(e,g,k=>{let F=x.get((0,Tn.toJson)(k));if(!F)return;let _=f.get(F);if(_)return(0,dr.updateLink)((0,Tn.normalizeOptionalProperties)({app:e,link:k,newSourcePathOrFile:g,newTargetPathOrFile:_,oldTargetPathOrFile:F,shouldUpdateFilenameAlias:w.shouldUpdateFilenameAliases}))},{shouldFailOnMissingFile:!1});if((0,_e.isNote)(e,r)&&await(0,dr.updateLinksInFile)((0,Tn.normalizeOptionalProperties)({app:e,newSourcePathOrFile:r,oldSourcePathOrFile:t,shouldFailOnMissingFile:!1,shouldUpdateFilenameAlias:w.shouldUpdateFilenameAliases})),!(0,_e.getFileOrNull)(e,r)){let g=Ln.get(r);g||(g=[],Ln.set(r,g)),g.push({combinedBacklinksMap:p,oldPath:t})}}finally{u();let f=Array.from(mr);(0,_a.addToQueue)(e,()=>{for(let p of f)mr.delete(p)})}}function P_(e,t,r){if(!Ca(e)||!(0,_e.isFile)(t))return;let n=t.path;C_(e.app,r,n)}function ba(e,t,r,n){for(let[i,o]of e.entries()){let a=t.get(i)??i,s=r.get(a)??new Map;r.set(a,s);for(let l of o)s.set((0,Tn.toJson)(l),n)}}function Gp(e){let t=In(e);(0,xa.getDebugger)("obsidian-dev-utils:RenameDeleteHandler:logRegisteredHandlers")(`Plugins with registered rename/delete handlers: ${JSON.stringify(Array.from(t.keys()))}`)}function Yp(e,t){return`${e} -> ${t}`}async function Jp(e,t,r){if(r=(0,$p.getSafeRenamePath)(e,t,r),t===r)return r;let n=Yp(t,r);return mr.add(n),r=await(0,$p.renameSafe)(e,t,r),r}function Ca(e){let t=e.app,r=e.manifest.id,n=In(t);return Array.from(n.keys())[0]===r}});var th=N((VO,eh)=>{function A_(e){return e&&e.__esModule&&e.default?e.default:e}(function(){let t=require;require=Object.assign(r=>{let n=t(r)??{};return A_(n)},t)})();var Aa=Object.defineProperty,E_=Object.getOwnPropertyDescriptor,S_=Object.getOwnPropertyNames,F_=Object.prototype.hasOwnProperty,O_=(e,t)=>{for(var r in t)Aa(e,r,{get:t[r],enumerable:!0})},L_=(e,t,r,n)=>{if(t&&typeof t=="object"||typeof t=="function")for(let i of S_(t))!F_.call(e,i)&&i!==r&&Aa(e,i,{get:()=>t[i],enumerable:!(n=E_(t,i))||n.enumerable});return e},T_=e=>L_(Aa({},"__esModule",{value:!0}),e),Zp={};O_(Zp,{PluginSettingsBase:()=>Pa});eh.exports=T_(Zp);var Pa=class{init(t){if(t!=null){if(typeof t!="object"||Array.isArray(t)){let r=Array.isArray(t)?"Array":typeof t;console.error(`Invalid data type. Expected Object, got: ${r}`);return}this.initFromRecord(t)}}shouldSaveAfterLoad(){return!1}toJSON(){return Object.fromEntries(Object.entries(this))}initFromRecord(t){for(let[r,n]of Object.entries(t))r in this?this[r]=n:console.error(`Unknown property: ${r}`)}}});var uh=N((UO,lh)=>{function I_(e){return e&&e.__esModule&&e.default?e.default:e}(function(){let t=require;require=Object.assign(r=>{let n=t(r)??{};return I_(n)},t)})();var Ea=Object.defineProperty,q_=Object.getOwnPropertyDescriptor,N_=Object.getOwnPropertyNames,R_=Object.prototype.hasOwnProperty,D_=(e,t)=>{for(var r in t)Ea(e,r,{get:t[r],enumerable:!0})},M_=(e,t,r,n)=>{if(t&&typeof t=="object"||typeof t=="function")for(let i of N_(t))!R_.call(e,i)&&i!==r&&Ea(e,i,{get:()=>t[i],enumerable:!(n=q_(t,i))||n.enumerable});return e},j_=e=>M_(Ea({},"__esModule",{value:!0}),e),sh={};D_(sh,{appendCodeBlock:()=>B_});lh.exports=j_(sh);function B_(e,t){e.createEl("strong",{cls:"markdown-rendered code"},r=>{r.createEl("code",{text:t})})}});var ph=N(($O,fh)=>{function z_(e){return e&&e.__esModule&&e.default?e.default:e}(function(){let t=require;require=Object.assign(r=>{let n=t(r)??{};return z_(n)},t)})();var Fa=Object.defineProperty,V_=Object.getOwnPropertyDescriptor,W_=Object.getOwnPropertyNames,U_=Object.prototype.hasOwnProperty,H_=(e,t)=>{for(var r in t)Fa(e,r,{get:t[r],enumerable:!0})},$_=(e,t,r,n)=>{if(t&&typeof t=="object"||typeof t=="function")for(let i of W_(t))!U_.call(e,i)&&i!==r&&Fa(e,i,{get:()=>t[i],enumerable:!(n=V_(t,i))||n.enumerable});return e},G_=e=>$_(Fa({},"__esModule",{value:!0}),e),ch={};H_(ch,{PluginSettingsTabBase:()=>Sa});fh.exports=G_(ch);var J_=require("obsidian"),HO=xi(),Sa=class extends J_.PluginSettingTab{constructor(t){super(t.app,t),this.plugin=t}}});var gh=N((GO,mh)=>{function Q_(e){return e&&e.__esModule&&e.default?e.default:e}(function(){let t=require;require=Object.assign(r=>{let n=t(r)??{};return Q_(n)},t)})();var La=Object.defineProperty,Y_=Object.getOwnPropertyDescriptor,K_=Object.getOwnPropertyNames,X_=Object.prototype.hasOwnProperty,Z_=(e,t)=>{for(var r in t)La(e,r,{get:t[r],enumerable:!0})},eC=(e,t,r,n)=>{if(t&&typeof t=="object"||typeof t=="function")for(let i of K_(t))!X_.call(e,i)&&i!==r&&La(e,i,{get:()=>t[i],enumerable:!(n=Y_(t,i))||n.enumerable});return e},tC=e=>eC(La({},"__esModule",{value:!0}),e),dh={};Z_(dh,{extend:()=>nC});mh.exports=tC(dh);var Nn=require("obsidian"),rC=ut(),Oa=class{constructor(t){this.valueComponent=t}asExtended(){return(0,rC.assignWithNonEnumerableProperties)({},this.valueComponent,this)}bind(t,r,n){let o={...{componentToPluginSettingsValueConverter:u=>u,pluginSettingsToComponentValueConverter:u=>u,shouldAutoSave:!0},...n},a=t,s=()=>o.pluginSettings??a.settingsCopy,l=u=>{if(!o.valueValidator)return!0;u??=this.valueComponent.getValue();let f=o.valueValidator(u),p=hh(this.valueComponent);return p&&(p.setCustomValidity(f??""),p.reportValidity()),!f};this.valueComponent.setValue(o.pluginSettingsToComponentValueConverter(s()[r])).onChange(async u=>{if(!l(u))return;let f=s();f[r]=o.componentToPluginSettingsValueConverter(u),o.shouldAutoSave&&await a.saveSettings(f),await o.onChanged?.()}),l();let c=hh(this.valueComponent);return c&&(c.addEventListener("focus",()=>l()),c.addEventListener("blur",()=>l())),this.asExtended()}};function nC(e){return new Oa(e).asExtended()}function hh(e){return e instanceof Nn.DropdownComponent?e.selectEl:e instanceof Nn.SliderComponent?e.sliderEl:e instanceof Nn.TextAreaComponent||e instanceof Nn.TextComponent?e.inputEl:null}});var oC={};_r(oC,{default:()=>iC});module.exports=Vn(oC);var Z=require("obsidian"),yr=H(ot(),1),be=H(Oe(),1),vt=H(Js(),1),Ph=H(yi(),1),Ah=H(xi(),1),et=H(Ti(),1),Eh=H(Xp(),1),tt=H(gt(),1),Sh=H(Ve(),1);var oh=H(th(),1),ah=H(Er(),1),rh=/(?:)/,nh=/$./,qn=class extends oh.PluginSettingsBase{autoCollectAttachments=!1;changeNoteBacklinksAlt=!0;consistencyReportFile="consistency-report.md";deleteAttachmentsWithNote=!1;deleteEmptyFolders=!0;deleteExistFilesWhenMoveNote=!1;moveAttachmentsWithNote=!1;showBackupWarning=!0;updateLinks=!0;get excludePaths(){return this.#e}set excludePaths(t){this.#e=t.filter(Boolean),this.#r=ih(this.#e,nh)}get hadDangerousSettingsReverted(){return this.#n}get includePaths(){return this.#t}set includePaths(t){this.#t=t.filter(Boolean),this.#i=ih(this.#t,rh)}#e=[];#r=nh;#n=!1;#t=[];#i=rh;constructor(t){super(),this.excludePaths=["/consistency-report\\.md$/"],this.init(t)}initFromRecord(t){let r=t;if(r.ignoreFiles||r.ignoreFolders){let n=r.excludePaths??[];for(let i of r.ignoreFiles??[])n.push(`/${i}$/`);for(let i of r.ignoreFolders??[])n.push(i);n.length>0&&(r.excludePaths=n),delete r.ignoreFiles,delete r.ignoreFolders}super.initFromRecord(r),this.showBackupWarning&&(this.#n=this.deleteAttachmentsWithNote||this.deleteExistFilesWhenMoveNote||this.moveAttachmentsWithNote||this.autoCollectAttachments,this.deleteAttachmentsWithNote=!1,this.deleteExistFilesWhenMoveNote=!1,this.moveAttachmentsWithNote=!1,this.autoCollectAttachments=!1)}isPathIgnored(t){return!this.#i.test(t)||this.#r.test(t)}toJSON(){return{...super.toJSON(),excludePaths:this.excludePaths,includePaths:this.includePaths}}};function ih(e,t){if(e.length===0)return t;let r=e.map(n=>n.startsWith("/")&&n.endsWith("/")?n.slice(1,-1):`^${(0,ah.escapeRegExp)(n)}`).map(n=>`(${n})`).join("|");return new RegExp(r)}var Ce=require("obsidian"),Ta=H(uh(),1),bh=H(yi(),1),wh=H(ph(),1),Le=H(gh(),1),kh=H(Er(),1),Rn=class extends wh.PluginSettingsTabBase{display(){this.containerEl.empty();let t="Move Attachments with Note";new Ce.Setting(this.containerEl).setName(t).setDesc("Automatically move attachments when a note is relocated. This includes attachments located in the same folder or any of its subfolders.").addToggle(a=>(0,Le.extend)(a).bind(this.plugin,"moveAttachmentsWithNote",{onChanged:async()=>{await this.checkDangerousSetting("moveAttachmentsWithNote",t)}}));let r="Delete Unused Attachments with Note";new Ce.Setting(this.containerEl).setName(r).setDesc("Automatically remove attachments that are no longer referenced in other notes when the note is deleted.").addToggle(a=>(0,Le.extend)(a).bind(this.plugin,"deleteAttachmentsWithNote",{onChanged:async()=>{await this.checkDangerousSetting("deleteAttachmentsWithNote",r)}})),new Ce.Setting(this.containerEl).setName("Update Links").setDesc("Automatically update links to attachments and other notes when moving notes or attachments.").addToggle(a=>(0,Le.extend)(a).bind(this.plugin,"updateLinks")),new Ce.Setting(this.containerEl).setName("Delete Empty Folders").setDesc("Automatically remove empty folders after moving notes with attachments.").addToggle(a=>(0,Le.extend)(a).bind(this.plugin,"deleteEmptyFolders"));let n="Delete Duplicate Attachments on Note Move";new Ce.Setting(this.containerEl).setName(n).setDesc("Automatically delete attachments when moving a note if a file with the same name exists in the destination folder. If disabled, the file will be renamed and moved.").addToggle(a=>(0,Le.extend)(a).bind(this.plugin,"deleteExistFilesWhenMoveNote",{onChanged:async()=>{await this.checkDangerousSetting("deleteExistFilesWhenMoveNote",n)}})),new Ce.Setting(this.containerEl).setName("Update Backlink Text on Note Rename").setDesc("When a note is renamed, its linked references are automatically updated. If this option is enabled, the text of backlinks to this note will also be modified.").addToggle(a=>(0,Le.extend)(a).bind(this.plugin,"changeNoteBacklinksAlt")),new Ce.Setting(this.containerEl).setName("Consistency Report Filename").setDesc("Specify the name of the file for the consistency report.").addText(a=>(0,Le.extend)(a).bind(this.plugin,"consistencyReportFile").setPlaceholder("Example: consistency-report.md"));let i={componentToPluginSettingsValueConverter:a=>a.split(`
`).filter(Boolean),pluginSettingsToComponentValueConverter:a=>a.join(`
`),valueValidator:a=>{let s=a.split(`
`);for(let l of s)if(l.startsWith("/")&&l.endsWith("/")){let c=l.slice(1,-1);if(!(0,kh.isValidRegExp)(c))return`Invalid regular expression ${l}`}return null}},o="Auto Collect Attachments";new Ce.Setting(this.containerEl).setName(o).setDesc("Automatically collect attachments when the note is edited.").addToggle(a=>(0,Le.extend)(a).bind(this.plugin,"autoCollectAttachments",{onChanged:async()=>{await this.checkDangerousSetting("autoCollectAttachments",o)}})),new Ce.Setting(this.containerEl).setName("Include paths").setDesc(createFragment(a=>{a.appendText("Include notes from the following paths"),a.createEl("br"),a.appendText("Insert each path on a new line"),a.createEl("br"),a.appendText("You can use path string or "),(0,Ta.appendCodeBlock)(a,"/regular expression/"),a.createEl("br"),a.appendText("If the setting is empty, all notes are included")})).addTextArea(a=>(0,Le.extend)(a).bind(this.plugin,"includePaths",i)),new Ce.Setting(this.containerEl).setName("Exclude paths").setDesc(createFragment(a=>{a.appendText("Exclude notes from the following paths"),a.createEl("br"),a.appendText("Insert each path on a new line"),a.createEl("br"),a.appendText("You can use path string or "),(0,Ta.appendCodeBlock)(a,"/regular expression/"),a.createEl("br"),a.appendText("If the setting is empty, no notes are excluded")})).addTextArea(a=>(0,Le.extend)(a).bind(this.plugin,"excludePaths",i))}async checkDangerousSetting(t,r){this.plugin.settingsCopy[t]&&await(0,bh.alert)({app:this.app,message:createFragment(n=>{n.createDiv({cls:"community-modal-readme"},i=>{i.appendText("You enabled "),i.createEl("strong",{cls:"markdown-rendered-code",text:r}),i.appendText(" setting. Without proper configuration it might lead to inconvenient attachment rearrangements or even data loss in your vault."),i.createEl("br"),i.appendText("It is "),i.createEl("strong",{text:"STRONGLY"}),i.appendText(" recommended to backup your vault before using the plugin."),i.createEl("br"),i.createEl("a",{href:"https://github.com/dy-sh/obsidian-consistent-attachments-and-links?tab=readme-ov-file",text:"Read more"}),i.appendText(" about how to use the plugin.")})}),title:createFragment(n=>{(0,Ce.setIcon)(n.createSpan(),"triangle-alert"),n.appendText(" Consistent Attachments and Links")})})}};var XO=require("obsidian"),_h=H(Mi(),1),wr=H(Oe(),1),Bt=H(On(),1),Mn=H(jt(),1),ce=H(gt(),1),Ch=H(ga(),1),Dn=H(Ve(),1);var yh=require("obsidian"),vh=H(ut(),1),Ia=H(ia(),1),Te=H(Oe(),1),ue=H(On(),1),Ne=H(jt(),1),xh=H(En(),1),Re=H(Ve(),1),yt=class extends Map{constructor(r){super();this.title=r}add(r,n){this.has(r)||this.set(r,[]);let i=this.get(r);i&&i.push(n)}toString(r,n){if(this.size>0){let i=`# ${this.title} (${this.size.toString()} files)
`;for(let o of this.keys()){let a=(0,Te.getFileOrNull)(r,o);if(!a)continue;let s=(0,ue.generateMarkdownLink)({app:r,sourcePathOrFile:n,targetPathOrFile:a});i+=`${s}:
`;for(let l of this.get(o)??[])i+=`- (line ${(l.position.start.line+1).toString()}): \`${l.link}\`
`;i+=`

`}return i}else return`# ${this.title}
No problems found

`}},br=class{constructor(t,r=""){this.plugin=t;this.consoleLogPrefix=r}async checkConsistency(t,r,n,i,o){if(this.plugin.settingsCopy.isPathIgnored(t.path))return;let a=await(0,Ne.getCacheSafe)(this.plugin.app,t.path);if(!a)return;let s=a.links??[],l=a.embeds??[];for(let c of s)await this.isValidLink(c,t.path)||r.add(t.path,c),(0,ue.testWikilink)(c.original)&&i.add(t.path,c);for(let c of l)await this.isValidLink(c,t.path)||n.add(t.path,c),(0,ue.testWikilink)(c.original)&&o.add(t.path,c)}async convertAllNoteEmbedsPathsToRelative(t){return this.convertAllNoteRefPathsToRelative(t,!0)}async convertAllNoteLinksPathsToRelative(t){return this.convertAllNoteRefPathsToRelative(t,!1)}async getCachedNotesThatHaveLinkToFile(t){let r=(0,Te.getFileOrNull)(this.plugin.app,t);return r?(await(0,Ne.getBacklinksForFileSafe)(this.plugin.app,r)).keys():[]}getFullPathForLink(t,r){({linkPath:t}=(0,ue.splitSubpath)(t));let n=(0,Re.dirname)(r);return(0,Re.join)(n,t)}async replaceAllNoteWikilinksWithMarkdownLinks(t,r){if(this.plugin.settingsCopy.isPathIgnored(t))return 0;let n=(0,Te.getFileOrNull)(this.plugin.app,t);if(!n)return console.warn(this.consoleLogPrefix+"can't update wikilinks in note, file not found: "+t),0;let i=await(0,Ne.getCacheSafe)(this.plugin.app,n);if(!i)return 0;let a=((r?i.embeds:i.links)??[]).filter(s=>(0,ue.testWikilink)(s.original)).length;return await(0,ue.updateLinksInFile)({app:this.plugin.app,newSourcePathOrFile:n,shouldForceMarkdownLinks:!0,shouldUpdateEmbedOnlyLinks:r}),a}async updateChangedPathsInNote(t,r){if(this.plugin.settingsCopy.isPathIgnored(t))return;let n=(0,Te.getFileOrNull)(this.plugin.app,t);if(!n){console.warn(this.consoleLogPrefix+"can't update links in note, file not found: "+t);return}let i=new Map;for(let o of r)i.set(o.oldPath,o.newPath);await this.updateLinks(n,n.path,i)}async convertAllNoteRefPathsToRelative(t,r){if(this.plugin.settingsCopy.isPathIgnored(t))return[];let n=(0,Te.getFileOrNull)(this.plugin.app,t);if(!n)return[];let i=[];return await(0,Ia.applyFileChanges)(this.plugin.app,n,async()=>{let o=await(0,Ne.getCacheSafe)(this.plugin.app,n);if(!o)return[];let a=(r?o.embeds:o.links)??[],s=[];for(let l of a){let c={endIndex:l.position.end.offset,newContent:this.convertLink({forceRelativePath:!0,link:l,note:n,oldNotePath:t}),oldContent:l.original,startIndex:l.position.start.offset};s.push(c),i.push({newLink:c.newContent,old:l})}return s}),i}convertLink({forceRelativePath:t,link:r,note:n,oldNotePath:i,pathChangeMap:o}){let{linkPath:a,subpath:s}=(0,ue.splitSubpath)(r.link),l=(0,ue.extractLinkFile)(this.plugin.app,r,i)?.path??(0,Re.join)((0,Re.dirname)(i),a),c=o?o.get(l):(0,ue.extractLinkFile)(this.plugin.app,r,n.path)?.path??(0,Re.join)((0,Re.dirname)(n.path),a);if(!c)return r.original;let u=(0,Te.getFileOrNull)(this.plugin.app,l)??(0,Te.getFileOrNull)(this.plugin.app,c);return u?(0,ue.generateMarkdownLink)((0,vh.normalizeOptionalProperties)({alias:r.displayText,app:this.plugin.app,originalLink:r.original,shouldForceRelativePath:t,sourcePathOrFile:n.path,subpath:s,targetPathOrFile:u})):r.original}async isValidLink(t,r){let{linkPath:n,subpath:i}=(0,ue.splitSubpath)(t.link),o;n?n.startsWith("/")?o=(0,yh.normalizePath)(n):o=(0,Re.join)((0,Re.dirname)(r),n):o=r;let a=(0,Te.getFileOrNull)(this.plugin.app,o);if(!a)return!1;if(!i)return!0;let s=a.extension.toLocaleLowerCase();if(s==="pdf")return i.startsWith("#page=");if(s!==Te.MARKDOWN_FILE_EXTENSION)return!1;let l=await(0,Ne.getCacheSafe)(this.plugin.app,a);return l?i.startsWith("#^")?Object.keys(l.blocks??{}).includes(i.slice(2)):(l.headings??[]).map(c=>c.heading.replaceAll("#"," ")).includes(i.slice(1)):!1}async updateLinks(t,r,n){await(0,Ia.applyFileChanges)(this.plugin.app,t,async()=>{let i=await(0,Ne.getCacheSafe)(this.plugin.app,t);return i?(0,Ne.getAllLinks)(i).map(a=>(0,xh.referenceToFileChange)(a,this.convertLink({link:a,note:t,oldNotePath:r,pathChangeMap:n}))):[]})}};var kr=class{constructor(t,r,n=""){this.plugin=t;this.lh=r;this.consoleLogPrefix=n}async collectAttachmentsForCachedNote(t,r,n){if(this.plugin.settingsCopy.isPathIgnored(t))return{movedAttachments:[],renamedFiles:[]};let i={movedAttachments:[],renamedFiles:[]},o=await(0,Mn.getCacheSafe)(this.plugin.app,t);if(!o)return i;for(let a of(0,Mn.getAllLinks)(o)){let{linkPath:s}=(0,Bt.splitSubpath)(a.link);if(!s)continue;let l=this.lh.getFullPathForLink(s,t);if(i.movedAttachments.findIndex(p=>p.oldPath==l)!=-1)continue;let c=(0,Bt.extractLinkFile)(this.plugin.app,a,t);if(!c){let p=(0,Bt.testEmbed)(a.original)?"embed":"link";console.warn(`${this.consoleLogPrefix}${t} has bad ${p} (file does not exist): ${s}`);continue}if(!this.isAttachment(c))continue;let u=await(0,_h.getAttachmentFilePath)(this.plugin.app,c.path,t);if((0,Dn.dirname)(u)===(0,Dn.dirname)(c.path))continue;let f=await this.moveAttachment(c,u,[t],r,n);i.movedAttachments=i.movedAttachments.concat(f.movedAttachments),i.renamedFiles=i.renamedFiles.concat(f.renamedFiles)}return i}async deleteEmptyFolders(t){if(this.plugin.settingsCopy.isPathIgnored(t))return;t.startsWith("./")&&(t=t.slice(2));let r=await(0,ce.listSafe)(this.plugin.app,t);for(let n of r.folders)await this.deleteEmptyFolders(n);if(r=await(0,ce.listSafe)(this.plugin.app,t),r.files.length==0&&r.folders.length==0&&(console.log(this.consoleLogPrefix+`delete empty folder: 
   `+t),await this.plugin.app.vault.exists(t)))try{await this.plugin.app.vault.adapter.rmdir(t,!1)}catch(n){if(await this.plugin.app.vault.adapter.exists(t))throw n}}async createFolderForAttachmentFromPath(t){await(0,ce.createFolderSafe)(this.plugin.app,(0,Dn.dirname)(t))}async deleteFile(t,r){if(await this.plugin.app.fileManager.trashFile(t),r){let n=t.parent;for(;n&&n.children.length===0;)await this.plugin.app.fileManager.trashFile(n),n=n.parent}}isAttachment(t){return!(0,wr.isNote)(this.plugin.app,t)}async moveAttachment(t,r,n,i,o){let a=t.path,s={movedAttachments:[],renamedFiles:[]};if(this.plugin.settingsCopy.isPathIgnored(a)||!this.isAttachment(t))return s;if(a==r)return console.warn(this.consoleLogPrefix+"Can't move file. Source and destination path the same."),s;await this.createFolderForAttachmentFromPath(r);let l=await this.lh.getCachedNotesThatHaveLinkToFile(a);for(let u of n)l.remove(u);if(a!==t.path)return console.warn(this.consoleLogPrefix+"File was moved already"),await this.moveAttachment(t,r,n,i,o);let c=t.parent;if(l.length==0)if(!(0,wr.getFileOrNull)(this.plugin.app,r))console.log(this.consoleLogPrefix+`move file [from, to]: 
   `+a+`
   `+r),s.movedAttachments.push({newPath:r,oldPath:a}),await(0,ce.renameSafe)(this.plugin.app,t,r);else if(i)console.log(this.consoleLogPrefix+`delete file: 
   `+a),s.movedAttachments.push({newPath:r,oldPath:a}),await this.deleteFile(t,o);else{let f=(0,ce.getAvailablePath)(this.plugin.app,r);console.log(this.consoleLogPrefix+`copy file with new name [from, to]: 
   `+a+`
   `+f),s.movedAttachments.push({newPath:f,oldPath:a}),await(0,ce.renameSafe)(this.plugin.app,t,f),s.renamedFiles.push({newPath:f,oldPath:r})}else if(!(0,wr.getFileOrNull)(this.plugin.app,r))console.log(this.consoleLogPrefix+`copy file [from, to]: 
   `+a+`
   `+r),s.movedAttachments.push({newPath:r,oldPath:a}),await(0,ce.renameSafe)(this.plugin.app,t,r),await(0,ce.copySafe)(this.plugin.app,t,a);else if(!i){let f=(0,ce.getAvailablePath)(this.plugin.app,r);console.log(this.consoleLogPrefix+`copy file with new name [from, to]: 
   `+a+`
   `+f),s.movedAttachments.push({newPath:f,oldPath:t.path}),await(0,ce.renameSafe)(this.plugin.app,t,f),await(0,ce.copySafe)(this.plugin.app,t,a),s.renamedFiles.push({newPath:f,oldPath:r})}return this.plugin.settingsCopy.deleteEmptyFolders&&await(0,Ch.deleteEmptyFolderHierarchy)(this.plugin.app,c),s}};var n1=globalThis.process??{browser:!0,cwd:()=>"/",env:{},platform:"android"},jn=class extends Ah.PluginBase{deletedNoteCache=new Map;fh;lh;async saveSettings(t){await super.saveSettings(t),this.lh=new br(this,"Consistent Attachments and Links: "),this.fh=new kr(this,this.lh,"Consistent Attachments and Links: ")}createPluginSettings(t){return new qn(t)}createPluginSettingsTab(){return new Rn(this)}async onLayoutReady(){await this.showBackupWarning()}onloadComplete(){this.registerEvent(this.app.metadataCache.on("deleted",(t,r)=>{r&&this.handleDeletedMetadata(t,r)})),(0,Eh.registerRenameDeleteHandlers)(this,()=>({isPathIgnored:r=>this.settings.isPathIgnored(r),shouldDeleteConflictingAttachments:this.settings.deleteExistFilesWhenMoveNote,shouldDeleteEmptyFolders:this.settings.deleteEmptyFolders,shouldHandleDeletions:this.settings.deleteAttachmentsWithNote,shouldHandleRenames:this.settings.updateLinks,shouldRenameAttachmentFolder:this.settings.moveAttachmentsWithNote,shouldUpdateFilenameAliases:this.settings.changeNoteBacklinksAlt})),this.addCommand({callback:()=>this.collectAllAttachments(),id:"collect-all-attachments",name:"Collect All Attachments"}),this.addCommand({checkCallback:t=>this.collectAttachmentsCurrentFolder(t),id:"collect-attachments-current-folder",name:"Collect Attachments in Current Folder"}),this.addCommand({checkCallback:this.collectAttachmentsCurrentNote.bind(this),id:"collect-attachments-current-note",name:"Collect Attachments in Current Note"}),this.addCommand({callback:()=>this.deleteEmptyFolders(),id:"delete-empty-folders",name:"Delete Empty Folders"}),this.addCommand({callback:()=>this.convertAllLinkPathsToRelative(),id:"convert-all-link-paths-to-relative",name:"Convert All Link Paths to Relative"}),this.addCommand({checkCallback:this.convertAllLinkPathsToRelativeCurrentNote.bind(this),id:"convert-all-link-paths-to-relative-current-note",name:"Convert All Link Paths to Relative in Current Note"}),this.addCommand({callback:()=>this.convertAllEmbedsPathsToRelative(),id:"convert-all-embed-paths-to-relative",name:"Convert All Embed Paths to Relative"}),this.addCommand({checkCallback:this.convertAllEmbedsPathsToRelativeCurrentNote.bind(this),id:"convert-all-embed-paths-to-relative-current-note",name:"Convert All Embed Paths to Relative in Current Note"}),this.addCommand({callback:()=>this.replaceAllWikilinksWithMarkdownLinks(),id:"replace-all-wikilinks-with-markdown-links",name:"Replace All Wiki Links with Markdown Links"}),this.addCommand({checkCallback:this.replaceAllWikilinksWithMarkdownLinksCurrentNote.bind(this),id:"replace-all-wikilinks-with-markdown-links-current-note",name:"Replace All Wiki Links with Markdown Links in Current Note"}),this.addCommand({callback:()=>this.replaceAllWikiEmbedsWithMarkdownEmbeds(),id:"replace-all-wiki-embeds-with-markdown-embeds",name:"Replace All Wiki Embeds with Markdown Embeds"}),this.addCommand({checkCallback:this.replaceAllWikiEmbedsWithMarkdownEmbedsCurrentNote.bind(this),id:"replace-all-wiki-embeds-with-markdown-embeds-current-note",name:"Replace All Wiki Embeds with Markdown Embeds in Current Note"}),this.addCommand({callback:()=>this.reorganizeVault(),id:"reorganize-vault",name:"Reorganize Vault"}),this.addCommand({callback:()=>this.checkConsistency(),id:"check-consistency",name:"Check Vault consistency"}),this.registerEvent(this.app.metadataCache.on("changed",t=>{(0,et.addToQueue)(this.app,()=>this.handleMetadataCacheChanged(t))})),this.registerEvent(this.app.workspace.on("file-menu",(t,r)=>{this.handleFileMenu(t,r)})),this.lh=new br(this,"Consistent Attachments and Links: "),this.fh=new kr(this,this.lh,"Consistent Attachments and Links: ")}async checkConsistency(){await this.saveAllOpenNotes();let t=new yt("Bad links"),r=new yt("Bad embeds"),n=new yt("Wiki links"),i=new yt("Wiki embeds");await(0,vt.loop)({abortSignal:this.abortSignal,buildNoticeMessage:(c,u)=>`Checking note ${u} - ${c.path}`,items:(0,tt.getMarkdownFilesSorted)(this.app),processItem:async c=>{await this.lh.checkConsistency(c,t,r,n,i)},shouldContinueOnError:!0});let o=this.settings.consistencyReportFile,a=t.toString(this.app,o)+r.toString(this.app,o)+n.toString(this.app,o)+i.toString(this.app,o);await(0,tt.createFolderSafe)(this.app,(0,Sh.dirname)(o));let s=await(0,be.getOrCreateFile)(this.app,o);await this.app.vault.modify(s,a);let l=!1;this.app.workspace.iterateAllLeaves(c=>{c.getDisplayText()!=""&&o.startsWith(c.getDisplayText())&&(l=!0)}),l||await this.app.workspace.openLinkText(o,"/",!1)}async collectAllAttachments(){await this.collectAttachmentsInFolder("/")}async collectAttachments(t,r=!0){if(this.settings.isPathIgnored(t.path)){new Z.Notice("Note path is ignored");return}await this.saveAllOpenNotes();let n=await this.fh.collectAttachmentsForCachedNote(t.path,this.settings.deleteExistFilesWhenMoveNote,this.settings.deleteEmptyFolders);n.movedAttachments.length>0&&await this.lh.updateChangedPathsInNote(t.path,n.movedAttachments),n.movedAttachments.length==0?r&&new Z.Notice("No files found that need to be moved"):new Z.Notice(`Moved ${n.movedAttachments.length.toString()} attachment${n.movedAttachments.length>1?"s":""}`)}collectAttachmentsCurrentFolder(t){let r=this.app.workspace.getActiveFile();return!r||!(0,be.isMarkdownFile)(this.app,r)?!1:(t||(0,et.addToQueue)(this.app,()=>this.collectAttachmentsInFolder(r.parent?.path??"/")),!0)}collectAttachmentsCurrentNote(t){let r=this.app.workspace.getActiveFile();return!r||!(0,be.isMarkdownFile)(this.app,r)?!1:(t||(0,et.addToQueue)(this.app,()=>this.collectAttachments(r)),!0)}async collectAttachmentsInFolder(t){let r=0,n=0;await this.saveAllOpenNotes(),await(0,vt.loop)({abortSignal:this.abortSignal,buildNoticeMessage:(i,o)=>`Collecting attachments ${o} - ${i.path}`,items:(0,be.getMarkdownFiles)(this.app,t,!0),processItem:async i=>{if(this.settings.isPathIgnored(i.path))return;let o=await this.fh.collectAttachmentsForCachedNote(i.path,this.settings.deleteExistFilesWhenMoveNote,this.settings.deleteEmptyFolders);o.movedAttachments.length>0&&(await this.lh.updateChangedPathsInNote(i.path,o.movedAttachments),r+=o.movedAttachments.length,n++)},shouldContinueOnError:!0}),r==0?new Z.Notice("No files found that need to be moved"):new Z.Notice(`Moved ${r.toString()} attachment${r>1?"s":""} from ${n.toString()} note${n>1?"s":""}`)}async convertAllEmbedsPathsToRelative(){await this.saveAllOpenNotes();let t=0,r=0;await(0,vt.loop)({abortSignal:this.abortSignal,buildNoticeMessage:(n,i)=>`Converting embed paths to relative ${i} - ${n.path}`,items:(0,tt.getMarkdownFilesSorted)(this.app),processItem:async n=>{if(this.settings.isPathIgnored(n.path))return;let i=await this.lh.convertAllNoteEmbedsPathsToRelative(n.path);i.length>0&&(t+=i.length,r++)},shouldContinueOnError:!0}),t==0?new Z.Notice("No embeds found that need to be converted"):new Z.Notice(`Converted ${t.toString()} embed${t>1?"s":""} from ${r.toString()} note${r>1?"s":""}`)}convertAllEmbedsPathsToRelativeCurrentNote(t){let r=this.app.workspace.getActiveFile();return!r||!(0,be.isMarkdownFile)(this.app,r)?!1:(t||(0,et.addToQueue)(this.app,(0,yr.omitAsyncReturnType)(()=>this.lh.convertAllNoteEmbedsPathsToRelative(r.path))),!0)}async convertAllLinkPathsToRelative(){await this.saveAllOpenNotes();let t=0,r=0;await(0,vt.loop)({abortSignal:this.abortSignal,buildNoticeMessage:(n,i)=>`Converting link paths to relative ${i} - ${n.path}`,items:(0,tt.getMarkdownFilesSorted)(this.app),processItem:async n=>{if(this.settings.isPathIgnored(n.path))return;let i=await this.lh.convertAllNoteLinksPathsToRelative(n.path);i.length>0&&(t+=i.length,r++)},shouldContinueOnError:!0}),t==0?new Z.Notice("No links found that need to be converted"):new Z.Notice(`Converted ${t.toString()} link${t>1?"s":""} from ${r.toString()} note${r>1?"s":""}`)}convertAllLinkPathsToRelativeCurrentNote(t){let r=this.app.workspace.getActiveFile();return!r||!(0,be.isMarkdownFile)(this.app,r)?!1:(t||(0,et.addToQueue)(this.app,(0,yr.omitAsyncReturnType)(()=>this.lh.convertAllNoteLinksPathsToRelative(r.path))),!0)}async deleteEmptyFolders(){await this.fh.deleteEmptyFolders("/")}handleDeletedMetadata(t,r){!this.settings.deleteAttachmentsWithNote||this.settings.isPathIgnored(t.path)||!(0,be.isMarkdownFile)(this.app,t)||this.deletedNoteCache.set(t.path,r)}handleFileMenu(t,r){(0,be.isFolder)(r)&&t.addItem(n=>{n.setTitle("Collect attachments in folder").setIcon("download").onClick(()=>this.collectAttachmentsInFolder(r.path))})}async handleMetadataCacheChanged(t){if(!this.settings.autoCollectAttachments)return;let r=document.querySelector(".suggestion-container");r&&r.style.display!=="none"||await this.collectAttachments(t,!1)}async reorganizeVault(){await this.saveAllOpenNotes(),await this.replaceAllWikilinksWithMarkdownLinks(),await this.replaceAllWikiEmbedsWithMarkdownEmbeds(),await this.convertAllEmbedsPathsToRelative(),await this.convertAllLinkPathsToRelative(),await this.collectAllAttachments(),await this.deleteEmptyFolders(),new Z.Notice("Reorganization of the vault completed")}async replaceAllWikiEmbedsWithMarkdownEmbeds(){await this.saveAllOpenNotes();let t=0,r=0;await(0,vt.loop)({abortSignal:this.abortSignal,buildNoticeMessage:(n,i)=>`Replacing wiki embeds with markdown embeds ${i} - ${n.path}`,items:(0,tt.getMarkdownFilesSorted)(this.app),processItem:async n=>{if(this.settings.isPathIgnored(n.path))return;let i=await this.lh.replaceAllNoteWikilinksWithMarkdownLinks(n.path,!0);t+=i,r++},shouldContinueOnError:!0}),t==0?new Z.Notice("No wiki embeds found that need to be replaced"):new Z.Notice(`Replaced ${t.toString()} wiki embed${t>1?"s":""} from ${r.toString()} note${r>1?"s":""}`)}replaceAllWikiEmbedsWithMarkdownEmbedsCurrentNote(t){let r=this.app.workspace.getActiveFile();return!r||!(0,be.isMarkdownFile)(this.app,r)?!1:(t||(0,et.addToQueue)(this.app,(0,yr.omitAsyncReturnType)(()=>this.lh.replaceAllNoteWikilinksWithMarkdownLinks(r.path,!0))),!0)}async replaceAllWikilinksWithMarkdownLinks(){await this.saveAllOpenNotes();let t=0,r=0;await(0,vt.loop)({abortSignal:this.abortSignal,buildNoticeMessage:(n,i)=>`Replacing wikilinks with markdown links ${i} - ${n.path}`,items:(0,tt.getMarkdownFilesSorted)(this.app),processItem:async n=>{if(this.settings.isPathIgnored(n.path))return;let i=await this.lh.replaceAllNoteWikilinksWithMarkdownLinks(n.path,!1);t+=i,r++},shouldContinueOnError:!0}),t==0?new Z.Notice("No wiki links found that need to be replaced"):new Z.Notice(`Replaced ${t.toString()} wikilink${t>1?"s":""} from ${r.toString()} note${r>1?"s":""}`)}replaceAllWikilinksWithMarkdownLinksCurrentNote(t){let r=this.app.workspace.getActiveFile();return!r||!(0,be.isMarkdownFile)(this.app,r)?!1:(t||(0,et.addToQueue)(this.app,(0,yr.omitAsyncReturnType)(()=>this.lh.replaceAllNoteWikilinksWithMarkdownLinks(r.path,!1))),!0)}async saveAllOpenNotes(){for(let t of this.app.workspace.getLeavesOfType("markdown"))t.view instanceof Z.MarkdownView&&await t.view.save()}async showBackupWarning(){this.settings.showBackupWarning&&(await(0,Ph.alert)({app:this.app,message:createFragment(t=>{t.createDiv({cls:"community-modal-readme"},r=>{r.appendText("Using 'Consistent Attachments and Links' plugin without proper configuration might lead to inconvenient attachment rearrangements or even data loss in your vault."),r.createEl("br"),r.appendText("It is "),r.createEl("strong",{text:"STRONGLY"}),r.appendText(" recommended to backup your vault before using the plugin."),r.createEl("br"),this.settings.hadDangerousSettingsReverted&&(r.appendText("Some of your plugin settings has been changed to their safe values."),r.createEl("br")),r.createEl("a",{href:"https://github.com/dy-sh/obsidian-consistent-attachments-and-links?tab=readme-ov-file",text:"Read more"}),r.appendText(" about how to use the plugin."),r.createEl("br"),r.appendText("This warning will not appear again.")})}),title:createFragment(t=>{(0,Z.setIcon)(t.createSpan(),"triangle-alert"),t.appendText(" Consistent Attachments and Links")})}),this.settings.showBackupWarning=!1,await this.saveSettings(this.settings))}};var iC=jn;

/* nosourcemap */