/*
 * Copyright (C) 1999-2001 Free Software Foundation, Inc.
 * This file is part of the GNU LIBICONV Library.
 *
 * The GNU LIBICONV Library is free software; you can redistribute it
 * and/or modify it under the terms of the GNU Library General Public
 * License as published by the Free Software Foundation; either version 2
 * of the License, or (at your option) any later version.
 *
 * The GNU LIBICONV Library is distributed in the hope that it will be
 * useful, but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
 * Library General Public License for more details.
 *
 * You should have received a copy of the GNU Library General Public
 * License along with the GNU LIBICONV Library; see the file COPYING.LIB.
 * If not, write to the Free Software Foundation, Inc., 51 Franklin Street,
 * Fifth Floor, Boston, MA 02110-1301, USA.
 */

/*
 * CP932 extensions
 */

static const unsigned short cp932ext_2uni_page87[92] = {
  /* 0x87 */
  0x2460, 0x2461, 0x2462, 0x2463, 0x2464, 0x2465, 0x2466, 0x2467,
  0x2468, 0x2469, 0x246a, 0x246b, 0x246c, 0x246d, 0x246e, 0x246f,
  0x2470, 0x2471, 0x2472, 0x2473, 0x2160, 0x2161, 0x2162, 0x2163,
  0x2164, 0x2165, 0x2166, 0x2167, 0x2168, 0x2169, 0xfffd, 0x3349,
  0x3314, 0x3322, 0x334d, 0x3318, 0x3327, 0x3303, 0x3336, 0x3351,
  0x3357, 0x330d, 0x3326, 0x3323, 0x332b, 0x334a, 0x333b, 0x339c,
  0x339d, 0x339e, 0x338e, 0x338f, 0x33c4, 0x33a1, 0xfffd, 0xfffd,
  0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0x337b, 0x301d,
  0x301f, 0x2116, 0x33cd, 0x2121, 0x32a4, 0x32a5, 0x32a6, 0x32a7,
  0x32a8, 0x3231, 0x3232, 0x3239, 0x337e, 0x337d, 0x337c, 0x2252,
  0x2261, 0x222b, 0x222e, 0x2211, 0x221a, 0x22a5, 0x2220, 0x221f,
  0x22bf, 0x2235, 0x2229, 0x222a,
};
static const unsigned short cp932ext_2uni_pageed[376] = {
  /* 0xed */
  0x7e8a, 0x891c, 0x9348, 0x9288, 0x84dc, 0x4fc9, 0x70bb, 0x6631,
  0x68c8, 0x92f9, 0x66fb, 0x5f45, 0x4e28, 0x4ee1, 0x4efc, 0x4f00,
  0x4f03, 0x4f39, 0x4f56, 0x4f92, 0x4f8a, 0x4f9a, 0x4f94, 0x4fcd,
  0x5040, 0x5022, 0x4fff, 0x501e, 0x5046, 0x5070, 0x5042, 0x5094,
  0x50f4, 0x50d8, 0x514a, 0x5164, 0x519d, 0x51be, 0x51ec, 0x5215,
  0x529c, 0x52a6, 0x52c0, 0x52db, 0x5300, 0x5307, 0x5324, 0x5372,
  0x5393, 0x53b2, 0x53dd, 0xfa0e, 0x549c, 0x548a, 0x54a9, 0x54ff,
  0x5586, 0x5759, 0x5765, 0x57ac, 0x57c8, 0x57c7, 0xfa0f, 0xfa10,
  0x589e, 0x58b2, 0x590b, 0x5953, 0x595b, 0x595d, 0x5963, 0x59a4,
  0x59ba, 0x5b56, 0x5bc0, 0x752f, 0x5bd8, 0x5bec, 0x5c1e, 0x5ca6,
  0x5cba, 0x5cf5, 0x5d27, 0x5d53, 0xfa11, 0x5d42, 0x5d6d, 0x5db8,
  0x5db9, 0x5dd0, 0x5f21, 0x5f34, 0x5f67, 0x5fb7, 0x5fde, 0x605d,
  0x6085, 0x608a, 0x60de, 0x60d5, 0x6120, 0x60f2, 0x6111, 0x6137,
  0x6130, 0x6198, 0x6213, 0x62a6, 0x63f5, 0x6460, 0x649d, 0x64ce,
  0x654e, 0x6600, 0x6615, 0x663b, 0x6609, 0x662e, 0x661e, 0x6624,
  0x6665, 0x6657, 0x6659, 0xfa12, 0x6673, 0x6699, 0x66a0, 0x66b2,
  0x66bf, 0x66fa, 0x670e, 0xf929, 0x6766, 0x67bb, 0x6852, 0x67c0,
  0x6801, 0x6844, 0x68cf, 0xfa13, 0x6968, 0xfa14, 0x6998, 0x69e2,
  0x6a30, 0x6a6b, 0x6a46, 0x6a73, 0x6a7e, 0x6ae2, 0x6ae4, 0x6bd6,
  0x6c3f, 0x6c5c, 0x6c86, 0x6c6f, 0x6cda, 0x6d04, 0x6d87, 0x6d6f,
  0x6d96, 0x6dac, 0x6dcf, 0x6df8, 0x6df2, 0x6dfc, 0x6e39, 0x6e5c,
  0x6e27, 0x6e3c, 0x6ebf, 0x6f88, 0x6fb5, 0x6ff5, 0x7005, 0x7007,
  0x7028, 0x7085, 0x70ab, 0x710f, 0x7104, 0x715c, 0x7146, 0x7147,
  0xfa15, 0x71c1, 0x71fe, 0x72b1,
  /* 0xee */
  0x72be, 0x7324, 0xfa16, 0x7377, 0x73bd, 0x73c9, 0x73d6, 0x73e3,
  0x73d2, 0x7407, 0x73f5, 0x7426, 0x742a, 0x7429, 0x742e, 0x7462,
  0x7489, 0x749f, 0x7501, 0x756f, 0x7682, 0x769c, 0x769e, 0x769b,
  0x76a6, 0xfa17, 0x7746, 0x52af, 0x7821, 0x784e, 0x7864, 0x787a,
  0x7930, 0xfa18, 0xfa19, 0xfa1a, 0x7994, 0xfa1b, 0x799b, 0x7ad1,
  0x7ae7, 0xfa1c, 0x7aeb, 0x7b9e, 0xfa1d, 0x7d48, 0x7d5c, 0x7db7,
  0x7da0, 0x7dd6, 0x7e52, 0x7f47, 0x7fa1, 0xfa1e, 0x8301, 0x8362,
  0x837f, 0x83c7, 0x83f6, 0x8448, 0x84b4, 0x8553, 0x8559, 0x856b,
  0xfa1f, 0x85b0, 0xfa20, 0xfa21, 0x8807, 0x88f5, 0x8a12, 0x8a37,
  0x8a79, 0x8aa7, 0x8abe, 0x8adf, 0xfa22, 0x8af6, 0x8b53, 0x8b7f,
  0x8cf0, 0x8cf4, 0x8d12, 0x8d76, 0xfa23, 0x8ecf, 0xfa24, 0xfa25,
  0x9067, 0x90de, 0xfa26, 0x9115, 0x9127, 0x91da, 0x91d7, 0x91de,
  0x91ed, 0x91ee, 0x91e4, 0x91e5, 0x9206, 0x9210, 0x920a, 0x923a,
  0x9240, 0x923c, 0x924e, 0x9259, 0x9251, 0x9239, 0x9267, 0x92a7,
  0x9277, 0x9278, 0x92e7, 0x92d7, 0x92d9, 0x92d0, 0xfa27, 0x92d5,
  0x92e0, 0x92d3, 0x9325, 0x9321, 0x92fb, 0xfa28, 0x931e, 0x92ff,
  0x931d, 0x9302, 0x9370, 0x9357, 0x93a4, 0x93c6, 0x93de, 0x93f8,
  0x9431, 0x9445, 0x9448, 0x9592, 0xf9dc, 0xfa29, 0x969d, 0x96af,
  0x9733, 0x973b, 0x9743, 0x974d, 0x974f, 0x9751, 0x9755, 0x9857,
  0x9865, 0xfa2a, 0xfa2b, 0x9927, 0xfa2c, 0x999e, 0x9a4e, 0x9ad9,
  0x9adc, 0x9b75, 0x9b72, 0x9b8f, 0x9bb1, 0x9bbb, 0x9c00, 0x9d70,
  0x9d6b, 0xfa2d, 0x9e19, 0x9ed1, 0xfffd, 0xfffd, 0x2170, 0x2171,
  0x2172, 0x2173, 0x2174, 0x2175, 0x2176, 0x2177, 0x2178, 0x2179,
  0xffe2, 0xffe4, 0xff07, 0xff02,
};
static const unsigned short cp932ext_2uni_pagefa[388] = {
  /* 0xfa */
  0x2170, 0x2171, 0x2172, 0x2173, 0x2174, 0x2175, 0x2176, 0x2177,
  0x2178, 0x2179, 0x2160, 0x2161, 0x2162, 0x2163, 0x2164, 0x2165,
  0x2166, 0x2167, 0x2168, 0x2169, 0xffe2, 0xffe4, 0xff07, 0xff02,
  0x3231, 0x2116, 0x2121, 0x2235, 0x7e8a, 0x891c, 0x9348, 0x9288,
  0x84dc, 0x4fc9, 0x70bb, 0x6631, 0x68c8, 0x92f9, 0x66fb, 0x5f45,
  0x4e28, 0x4ee1, 0x4efc, 0x4f00, 0x4f03, 0x4f39, 0x4f56, 0x4f92,
  0x4f8a, 0x4f9a, 0x4f94, 0x4fcd, 0x5040, 0x5022, 0x4fff, 0x501e,
  0x5046, 0x5070, 0x5042, 0x5094, 0x50f4, 0x50d8, 0x514a, 0x5164,
  0x519d, 0x51be, 0x51ec, 0x5215, 0x529c, 0x52a6, 0x52c0, 0x52db,
  0x5300, 0x5307, 0x5324, 0x5372, 0x5393, 0x53b2, 0x53dd, 0xfa0e,
  0x549c, 0x548a, 0x54a9, 0x54ff, 0x5586, 0x5759, 0x5765, 0x57ac,
  0x57c8, 0x57c7, 0xfa0f, 0xfa10, 0x589e, 0x58b2, 0x590b, 0x5953,
  0x595b, 0x595d, 0x5963, 0x59a4, 0x59ba, 0x5b56, 0x5bc0, 0x752f,
  0x5bd8, 0x5bec, 0x5c1e, 0x5ca6, 0x5cba, 0x5cf5, 0x5d27, 0x5d53,
  0xfa11, 0x5d42, 0x5d6d, 0x5db8, 0x5db9, 0x5dd0, 0x5f21, 0x5f34,
  0x5f67, 0x5fb7, 0x5fde, 0x605d, 0x6085, 0x608a, 0x60de, 0x60d5,
  0x6120, 0x60f2, 0x6111, 0x6137, 0x6130, 0x6198, 0x6213, 0x62a6,
  0x63f5, 0x6460, 0x649d, 0x64ce, 0x654e, 0x6600, 0x6615, 0x663b,
  0x6609, 0x662e, 0x661e, 0x6624, 0x6665, 0x6657, 0x6659, 0xfa12,
  0x6673, 0x6699, 0x66a0, 0x66b2, 0x66bf, 0x66fa, 0x670e, 0xf929,
  0x6766, 0x67bb, 0x6852, 0x67c0, 0x6801, 0x6844, 0x68cf, 0xfa13,
  0x6968, 0xfa14, 0x6998, 0x69e2, 0x6a30, 0x6a6b, 0x6a46, 0x6a73,
  0x6a7e, 0x6ae2, 0x6ae4, 0x6bd6, 0x6c3f, 0x6c5c, 0x6c86, 0x6c6f,
  0x6cda, 0x6d04, 0x6d87, 0x6d6f,
  /* 0xfb */
  0x6d96, 0x6dac, 0x6dcf, 0x6df8, 0x6df2, 0x6dfc, 0x6e39, 0x6e5c,
  0x6e27, 0x6e3c, 0x6ebf, 0x6f88, 0x6fb5, 0x6ff5, 0x7005, 0x7007,
  0x7028, 0x7085, 0x70ab, 0x710f, 0x7104, 0x715c, 0x7146, 0x7147,
  0xfa15, 0x71c1, 0x71fe, 0x72b1, 0x72be, 0x7324, 0xfa16, 0x7377,
  0x73bd, 0x73c9, 0x73d6, 0x73e3, 0x73d2, 0x7407, 0x73f5, 0x7426,
  0x742a, 0x7429, 0x742e, 0x7462, 0x7489, 0x749f, 0x7501, 0x756f,
  0x7682, 0x769c, 0x769e, 0x769b, 0x76a6, 0xfa17, 0x7746, 0x52af,
  0x7821, 0x784e, 0x7864, 0x787a, 0x7930, 0xfa18, 0xfa19, 0xfa1a,
  0x7994, 0xfa1b, 0x799b, 0x7ad1, 0x7ae7, 0xfa1c, 0x7aeb, 0x7b9e,
  0xfa1d, 0x7d48, 0x7d5c, 0x7db7, 0x7da0, 0x7dd6, 0x7e52, 0x7f47,
  0x7fa1, 0xfa1e, 0x8301, 0x8362, 0x837f, 0x83c7, 0x83f6, 0x8448,
  0x84b4, 0x8553, 0x8559, 0x856b, 0xfa1f, 0x85b0, 0xfa20, 0xfa21,
  0x8807, 0x88f5, 0x8a12, 0x8a37, 0x8a79, 0x8aa7, 0x8abe, 0x8adf,
  0xfa22, 0x8af6, 0x8b53, 0x8b7f, 0x8cf0, 0x8cf4, 0x8d12, 0x8d76,
  0xfa23, 0x8ecf, 0xfa24, 0xfa25, 0x9067, 0x90de, 0xfa26, 0x9115,
  0x9127, 0x91da, 0x91d7, 0x91de, 0x91ed, 0x91ee, 0x91e4, 0x91e5,
  0x9206, 0x9210, 0x920a, 0x923a, 0x9240, 0x923c, 0x924e, 0x9259,
  0x9251, 0x9239, 0x9267, 0x92a7, 0x9277, 0x9278, 0x92e7, 0x92d7,
  0x92d9, 0x92d0, 0xfa27, 0x92d5, 0x92e0, 0x92d3, 0x9325, 0x9321,
  0x92fb, 0xfa28, 0x931e, 0x92ff, 0x931d, 0x9302, 0x9370, 0x9357,
  0x93a4, 0x93c6, 0x93de, 0x93f8, 0x9431, 0x9445, 0x9448, 0x9592,
  0xf9dc, 0xfa29, 0x969d, 0x96af, 0x9733, 0x973b, 0x9743, 0x974d,
  0x974f, 0x9751, 0x9755, 0x9857, 0x9865, 0xfa2a, 0xfa2b, 0x9927,
  0xfa2c, 0x999e, 0x9a4e, 0x9ad9,
  /* 0xfc */
  0x9adc, 0x9b75, 0x9b72, 0x9b8f, 0x9bb1, 0x9bbb, 0x9c00, 0x9d70,
  0x9d6b, 0xfa2d, 0x9e19, 0x9ed1,
};

static int
cp932ext_mbtowc (conv_t conv, ucs4_t *pwc, const unsigned char *s, int n)
{
  unsigned char c1 = s[0];
  if ((c1 == 0x87) || (c1 >= 0xed && c1 <= 0xee) || (c1 >= 0xfa && c1 <= 0xfc)) {
    if (n >= 2) {
      unsigned char c2 = s[1];
      if ((c2 >= 0x40 && c2 < 0x7f) || (c2 >= 0x80 && c2 < 0xfd)) {
        unsigned int i = 188 * (c1 - (c1 >= 0xe0 ? 0xc1 : 0x81)) + (c2 - (c2 >= 0x80 ? 0x41 : 0x40));
        unsigned short wc = 0xfffd;
        if (i < 8272) {
          if (i < 1220)
            wc = cp932ext_2uni_page87[i-1128];
        } else if (i < 10716) {
          if (i < 8648)
            wc = cp932ext_2uni_pageed[i-8272];
        } else {
          if (i < 11104)
            wc = cp932ext_2uni_pagefa[i-10716];
        }
        if (wc != 0xfffd) {
          *pwc = (ucs4_t) wc;
          return 2;
        }
      }
      return RET_ILSEQ;
    }
    return RET_TOOFEW(0);
  }
  return RET_ILSEQ;
}

static const unsigned short cp932ext_2charset[457] = {
  0xfa59, 0xfa5a, 0xfa4a, 0xfa4b, 0xfa4c, 0xfa4d, 0xfa4e, 0xfa4f,
  0xfa50, 0xfa51, 0xfa52, 0xfa53, 0xfa40, 0xfa41, 0xfa42, 0xfa43,
  0xfa44, 0xfa45, 0xfa46, 0xfa47, 0xfa48, 0xfa49, 0x8794, 0x8795,
  0x8798, 0x8797, 0x879b, 0x879c, 0x8792, 0x8793, 0xfa5b, 0x8790,
  0x8791, 0x8796, 0x8799, 0x8740, 0x8741, 0x8742, 0x8743, 0x8744,
  0x8745, 0x8746, 0x8747, 0x8748, 0x8749, 0x874a, 0x874b, 0x874c,
  0x874d, 0x874e, 0x874f, 0x8750, 0x8751, 0x8752, 0x8753, 0x8780,
  0x8781, 0xfa58, 0x878b, 0x878c, 0x8785, 0x8786, 0x8787, 0x8788,
  0x8789, 0x8765, 0x8769, 0x8760, 0x8763, 0x8761, 0x876b, 0x876a,
  0x8764, 0x876c, 0x8766, 0x876e, 0x875f, 0x876d, 0x8762, 0x8767,
  0x8768, 0x877e, 0x878f, 0x878e, 0x878d, 0x8772, 0x8773, 0x876f,
  0x8770, 0x8771, 0x8775, 0x8774, 0x8783, 0xfa68, 0xfa69, 0xfa6a,
  0xfa6b, 0xfa6c, 0xfa6d, 0xfa6e, 0xfa70, 0xfa6f, 0xfa72, 0xfa71,
  0xfa61, 0xfa73, 0xfa76, 0xfa77, 0xfa75, 0xfa74, 0xfa7a, 0xfa78,
  0xfa79, 0xfa7b, 0xfa7d, 0xfa7c, 0xfa7e, 0xfa80, 0xfa81, 0xfa82,
  0xfa83, 0xfa84, 0xfa85, 0xfa86, 0xfb77, 0xfa87, 0xfa88, 0xfa89,
  0xfa8a, 0xfa8b, 0xfa8c, 0xfa8d, 0xfa8e, 0xfa8f, 0xfa92, 0xfa91,
  0xfa93, 0xfa94, 0xfa95, 0xfa96, 0xfa97, 0xfa98, 0xfa9a, 0xfa99,
  0xfa9d, 0xfa9e, 0xfa9f, 0xfaa0, 0xfaa1, 0xfaa2, 0xfaa3, 0xfaa4,
  0xfaa5, 0xfaa6, 0xfaa7, 0xfaa9, 0xfaaa, 0xfaab, 0xfaac, 0xfaad,
  0xfaae, 0xfaaf, 0xfab2, 0xfab0, 0xfab3, 0xfab4, 0xfab5, 0xfab6,
  0xfab7, 0xfab8, 0xfa67, 0xfab9, 0xfaba, 0xfabb, 0xfabc, 0xfabd,
  0xfabe, 0xfac0, 0xfabf, 0xfac2, 0xfac3, 0xfac1, 0xfac5, 0xfac4,
  0xfac6, 0xfac7, 0xfac8, 0xfac9, 0xfaca, 0xfacb, 0xfacc, 0xfacd,
  0xface, 0xfad1, 0xfacf, 0xfad3, 0xfad4, 0xfad2, 0xfa63, 0xfad0,
  0xfad6, 0xfad7, 0xfad5, 0xfad9, 0xfada, 0xfadb, 0xfadc, 0xfadd,
  0xfade, 0xfa66, 0xfadf, 0xfae1, 0xfae2, 0xfae4, 0xfae5, 0xfae6,
  0xfae3, 0xfa64, 0xfae7, 0xfae9, 0xfaeb, 0xfaec, 0xfaed, 0xfaef,
  0xfaee, 0xfaf0, 0xfaf1, 0xfaf2, 0xfaf3, 0xfaf4, 0xfaf5, 0xfaf6,
  0xfaf8, 0xfaf7, 0xfaf9, 0xfafa, 0xfafc, 0xfafb, 0xfb40, 0xfb41,
  0xfb42, 0xfb44, 0xfb43, 0xfb45, 0xfb48, 0xfb46, 0xfb49, 0xfb47,
  0xfb4a, 0xfb4b, 0xfb4c, 0xfb4d, 0xfb4e, 0xfb4f, 0xfb50, 0xfb51,
  0xfb52, 0xfa62, 0xfb54, 0xfb53, 0xfb56, 0xfb57, 0xfb55, 0xfb59,
  0xfb5a, 0xfb5b, 0xfb5c, 0xfb5d, 0xfb5f, 0xfb60, 0xfb61, 0xfb64,
  0xfb62, 0xfb63, 0xfb66, 0xfb65, 0xfb67, 0xfb69, 0xfb68, 0xfb6a,
  0xfb6b, 0xfb6c, 0xfb6d, 0xfb6e, 0xfaa8, 0xfb6f, 0xfb70, 0xfb73,
  0xfb71, 0xfb72, 0xfb74, 0xfb76, 0xfb78, 0xfb79, 0xfb7a, 0xfb7b,
  0xfb7c, 0xfb81, 0xfb83, 0xfb84, 0xfb85, 0xfb87, 0xfb88, 0xfb8a,
  0xfb8b, 0xfb8d, 0xfb8c, 0xfb8e, 0xfb8f, 0xfa5c, 0xfb90, 0xfb91,
  0xfb93, 0xfb94, 0xfb95, 0xfb96, 0xfb97, 0xfb98, 0xfb99, 0xfa60,
  0xfb9a, 0xfb9b, 0xfb9c, 0xfb9e, 0xfba1, 0xfba2, 0xfa5d, 0xfba3,
  0xfba4, 0xfba5, 0xfba6, 0xfba7, 0xfba8, 0xfbaa, 0xfbab, 0xfbac,
  0xfbad, 0xfbae, 0xfbaf, 0xfbb0, 0xfbb2, 0xfbb5, 0xfbb6, 0xfbb8,
  0xfbb9, 0xfbbb, 0xfbba, 0xfbbc, 0xfbbf, 0xfbc0, 0xfbbd, 0xfbbe,
  0xfbc1, 0xfbc3, 0xfbc2, 0xfbca, 0xfbc4, 0xfbc6, 0xfbc5, 0xfbc7,
  0xfbc9, 0xfbc8, 0xfbcb, 0xfbcd, 0xfbce, 0xfa5f, 0xfbcc, 0xfbd2,
  0xfbd6, 0xfbd4, 0xfbd0, 0xfbd1, 0xfbd5, 0xfbcf, 0xfa65, 0xfbd9,
  0xfbdc, 0xfbde, 0xfbdd, 0xfbdb, 0xfbd8, 0xfbd7, 0xfa5e, 0xfbe0,
  0xfbdf, 0xfbe1, 0xfbe2, 0xfbe3, 0xfbe4, 0xfbe5, 0xfbe6, 0xfbe7,
  0xfbe8, 0xfbeb, 0xfbec, 0xfbed, 0xfbee, 0xfbef, 0xfbf0, 0xfbf1,
  0xfbf2, 0xfbf3, 0xfbf4, 0xfbf5, 0xfbf8, 0xfbfa, 0xfbfb, 0xfbfc,
  0xfc40, 0xfc42, 0xfc41, 0xfc43, 0xfc44, 0xfc45, 0xfc46, 0xfc48,
  0xfc47, 0xfc4a, 0xfc4b, 0xfae0, 0xfbe9, 0xfa90, 0xfa9b, 0xfa9c,
  0xfab1, 0xfad8, 0xfae8, 0xfaea, 0xfb58, 0xfb5e, 0xfb75, 0xfb7d,
  0xfb7e, 0xfb80, 0xfb82, 0xfb86, 0xfb89, 0xfb92, 0xfb9d, 0xfb9f,
  0xfba0, 0xfba9, 0xfbb1, 0xfbb3, 0xfbb4, 0xfbb7, 0xfbd3, 0xfbda,
  0xfbea, 0xfbf6, 0xfbf7, 0xfbf9, 0xfc49, 0xfa57, 0xfa56, 0xfa54,
  0xfa55,
};

static const Summary16 cp932ext_uni2indx_page21[28] = {
  /* 0x2100 */
  {    0, 0x0000 }, {    0, 0x0040 }, {    1, 0x0002 }, {    2, 0x0000 },
  {    2, 0x0000 }, {    2, 0x0000 }, {    2, 0x03ff }, {   12, 0x03ff },
  {   22, 0x0000 }, {   22, 0x0000 }, {   22, 0x0000 }, {   22, 0x0000 },
  {   22, 0x0000 }, {   22, 0x0000 }, {   22, 0x0000 }, {   22, 0x0000 },
  /* 0x2200 */
  {   22, 0x0000 }, {   22, 0x8402 }, {   25, 0x4e01 }, {   30, 0x0020 },
  {   31, 0x0000 }, {   31, 0x0004 }, {   32, 0x0002 }, {   33, 0x0000 },
  {   33, 0x0000 }, {   33, 0x0000 }, {   33, 0x0020 }, {   34, 0x8000 },
};
static const Summary16 cp932ext_uni2indx_page24[8] = {
  /* 0x2400 */
  {   35, 0x0000 }, {   35, 0x0000 }, {   35, 0x0000 }, {   35, 0x0000 },
  {   35, 0x0000 }, {   35, 0x0000 }, {   35, 0xffff }, {   51, 0x000f },
};
static const Summary16 cp932ext_uni2indx_page30[2] = {
  /* 0x3000 */
  {   55, 0x0000 }, {   55, 0xa000 },
};
static const Summary16 cp932ext_uni2indx_page32[29] = {
  /* 0x3200 */
  {   57, 0x0000 }, {   57, 0x0000 }, {   57, 0x0000 }, {   57, 0x0206 },
  {   60, 0x0000 }, {   60, 0x0000 }, {   60, 0x0000 }, {   60, 0x0000 },
  {   60, 0x0000 }, {   60, 0x0000 }, {   60, 0x01f0 }, {   65, 0x0000 },
  {   65, 0x0000 }, {   65, 0x0000 }, {   65, 0x0000 }, {   65, 0x0000 },
  /* 0x3300 */
  {   65, 0x2008 }, {   67, 0x0110 }, {   69, 0x08cc }, {   74, 0x0840 },
  {   76, 0x2600 }, {   79, 0x0082 }, {   81, 0x0000 }, {   81, 0x7800 },
  {   85, 0xc000 }, {   87, 0x7000 }, {   90, 0x0002 }, {   91, 0x0000 },
  {   91, 0x2010 },
};
static const Summary16 cp932ext_uni2indx_page4e[121] = {
  /* 0x4e00 */
  {   93, 0x0000 }, {   93, 0x0000 }, {   93, 0x0100 }, {   94, 0x0000 },
  {   94, 0x0000 }, {   94, 0x0000 }, {   94, 0x0000 }, {   94, 0x0000 },
  {   94, 0x0000 }, {   94, 0x0000 }, {   94, 0x0000 }, {   94, 0x0000 },
  {   94, 0x0000 }, {   94, 0x0000 }, {   94, 0x0002 }, {   95, 0x1000 },
  /* 0x4f00 */
  {   96, 0x0009 }, {   98, 0x0000 }, {   98, 0x0000 }, {   98, 0x0200 },
  {   99, 0x0000 }, {   99, 0x0040 }, {  100, 0x0000 }, {  100, 0x0000 },
  {  100, 0x0400 }, {  101, 0x0414 }, {  104, 0x0000 }, {  104, 0x0000 },
  {  104, 0x2200 }, {  106, 0x0000 }, {  106, 0x0000 }, {  106, 0x8000 },
  /* 0x5000 */
  {  107, 0x0000 }, {  107, 0x4000 }, {  108, 0x0004 }, {  109, 0x0000 },
  {  109, 0x0045 }, {  112, 0x0000 }, {  112, 0x0000 }, {  112, 0x0001 },
  {  113, 0x0000 }, {  113, 0x0010 }, {  114, 0x0000 }, {  114, 0x0000 },
  {  114, 0x0000 }, {  114, 0x0100 }, {  115, 0x0000 }, {  115, 0x0010 },
  /* 0x5100 */
  {  116, 0x0000 }, {  116, 0x0000 }, {  116, 0x0000 }, {  116, 0x0000 },
  {  116, 0x0400 }, {  117, 0x0000 }, {  117, 0x0010 }, {  118, 0x0000 },
  {  118, 0x0000 }, {  118, 0x2000 }, {  119, 0x0000 }, {  119, 0x4000 },
  {  120, 0x0000 }, {  120, 0x0000 }, {  120, 0x1000 }, {  121, 0x0000 },
  /* 0x5200 */
  {  121, 0x0000 }, {  121, 0x0020 }, {  122, 0x0000 }, {  122, 0x0000 },
  {  122, 0x0000 }, {  122, 0x0000 }, {  122, 0x0000 }, {  122, 0x0000 },
  {  122, 0x0000 }, {  122, 0x1000 }, {  123, 0x8040 }, {  125, 0x0000 },
  {  125, 0x0001 }, {  126, 0x0800 }, {  127, 0x0000 }, {  127, 0x0000 },
  /* 0x5300 */
  {  127, 0x0081 }, {  129, 0x0000 }, {  129, 0x0010 }, {  130, 0x0000 },
  {  130, 0x0000 }, {  130, 0x0000 }, {  130, 0x0000 }, {  130, 0x0004 },
  {  131, 0x0000 }, {  131, 0x0008 }, {  132, 0x0000 }, {  132, 0x0004 },
  {  133, 0x0000 }, {  133, 0x2000 }, {  134, 0x0000 }, {  134, 0x0000 },
  /* 0x5400 */
  {  134, 0x0000 }, {  134, 0x0000 }, {  134, 0x0000 }, {  134, 0x0000 },
  {  134, 0x0000 }, {  134, 0x0000 }, {  134, 0x0000 }, {  134, 0x0000 },
  {  134, 0x0400 }, {  135, 0x1000 }, {  136, 0x0200 }, {  137, 0x0000 },
  {  137, 0x0000 }, {  137, 0x0000 }, {  137, 0x0000 }, {  137, 0x8000 },
  /* 0x5500 */
  {  138, 0x0000 }, {  138, 0x0000 }, {  138, 0x0000 }, {  138, 0x0000 },
  {  138, 0x0000 }, {  138, 0x0000 }, {  138, 0x0000 }, {  138, 0x0000 },
  {  138, 0x0040 },
};
static const Summary16 cp932ext_uni2indx_page57[44] = {
  /* 0x5700 */
  {  139, 0x0000 }, {  139, 0x0000 }, {  139, 0x0000 }, {  139, 0x0000 },
  {  139, 0x0000 }, {  139, 0x0200 }, {  140, 0x0020 }, {  141, 0x0000 },
  {  141, 0x0000 }, {  141, 0x0000 }, {  141, 0x1000 }, {  142, 0x0000 },
  {  142, 0x0180 }, {  144, 0x0000 }, {  144, 0x0000 }, {  144, 0x0000 },
  /* 0x5800 */
  {  144, 0x0000 }, {  144, 0x0000 }, {  144, 0x0000 }, {  144, 0x0000 },
  {  144, 0x0000 }, {  144, 0x0000 }, {  144, 0x0000 }, {  144, 0x0000 },
  {  144, 0x0000 }, {  144, 0x4000 }, {  145, 0x0000 }, {  145, 0x0004 },
  {  146, 0x0000 }, {  146, 0x0000 }, {  146, 0x0000 }, {  146, 0x0000 },
  /* 0x5900 */
  {  146, 0x0800 }, {  147, 0x0000 }, {  147, 0x0000 }, {  147, 0x0000 },
  {  147, 0x0000 }, {  147, 0x2808 }, {  150, 0x0008 }, {  151, 0x0000 },
  {  151, 0x0000 }, {  151, 0x0000 }, {  151, 0x0010 }, {  152, 0x0400 },
};
static const Summary16 cp932ext_uni2indx_page5b[46] = {
  /* 0x5b00 */
  {  153, 0x0000 }, {  153, 0x0000 }, {  153, 0x0000 }, {  153, 0x0000 },
  {  153, 0x0000 }, {  153, 0x0040 }, {  154, 0x0000 }, {  154, 0x0000 },
  {  154, 0x0000 }, {  154, 0x0000 }, {  154, 0x0000 }, {  154, 0x0000 },
  {  154, 0x0001 }, {  155, 0x0100 }, {  156, 0x1000 }, {  157, 0x0000 },
  /* 0x5c00 */
  {  157, 0x0000 }, {  157, 0x4000 }, {  158, 0x0000 }, {  158, 0x0000 },
  {  158, 0x0000 }, {  158, 0x0000 }, {  158, 0x0000 }, {  158, 0x0000 },
  {  158, 0x0000 }, {  158, 0x0000 }, {  158, 0x0040 }, {  159, 0x0400 },
  {  160, 0x0000 }, {  160, 0x0000 }, {  160, 0x0000 }, {  160, 0x0020 },
  /* 0x5d00 */
  {  161, 0x0000 }, {  161, 0x0000 }, {  161, 0x0080 }, {  162, 0x0000 },
  {  162, 0x0004 }, {  163, 0x0008 }, {  164, 0x2000 }, {  165, 0x0000 },
  {  165, 0x0000 }, {  165, 0x0000 }, {  165, 0x0000 }, {  165, 0x0300 },
  {  167, 0x0000 }, {  167, 0x0001 },
};
static const Summary16 cp932ext_uni2indx_page5f[458] = {
  /* 0x5f00 */
  {  168, 0x0000 }, {  168, 0x0000 }, {  168, 0x0002 }, {  169, 0x0010 },
  {  170, 0x0020 }, {  171, 0x0000 }, {  171, 0x0080 }, {  172, 0x0000 },
  {  172, 0x0000 }, {  172, 0x0000 }, {  172, 0x0000 }, {  172, 0x0080 },
  {  173, 0x0000 }, {  173, 0x4000 }, {  174, 0x0000 }, {  174, 0x0000 },
  /* 0x6000 */
  {  174, 0x0000 }, {  174, 0x0000 }, {  174, 0x0000 }, {  174, 0x0000 },
  {  174, 0x0000 }, {  174, 0x2000 }, {  175, 0x0000 }, {  175, 0x0000 },
  {  175, 0x0420 }, {  177, 0x0000 }, {  177, 0x0000 }, {  177, 0x0000 },
  {  177, 0x0000 }, {  177, 0x4020 }, {  179, 0x0000 }, {  179, 0x0004 },
  /* 0x6100 */
  {  180, 0x0000 }, {  180, 0x0002 }, {  181, 0x0001 }, {  182, 0x0081 },
  {  184, 0x0000 }, {  184, 0x0000 }, {  184, 0x0000 }, {  184, 0x0000 },
  {  184, 0x0000 }, {  184, 0x0100 }, {  185, 0x0000 }, {  185, 0x0000 },
  {  185, 0x0000 }, {  185, 0x0000 }, {  185, 0x0000 }, {  185, 0x0000 },
  /* 0x6200 */
  {  185, 0x0000 }, {  185, 0x0008 }, {  186, 0x0000 }, {  186, 0x0000 },
  {  186, 0x0000 }, {  186, 0x0000 }, {  186, 0x0000 }, {  186, 0x0000 },
  {  186, 0x0000 }, {  186, 0x0000 }, {  186, 0x0040 }, {  187, 0x0000 },
  {  187, 0x0000 }, {  187, 0x0000 }, {  187, 0x0000 }, {  187, 0x0000 },
  /* 0x6300 */
  {  187, 0x0000 }, {  187, 0x0000 }, {  187, 0x0000 }, {  187, 0x0000 },
  {  187, 0x0000 }, {  187, 0x0000 }, {  187, 0x0000 }, {  187, 0x0000 },
  {  187, 0x0000 }, {  187, 0x0000 }, {  187, 0x0000 }, {  187, 0x0000 },
  {  187, 0x0000 }, {  187, 0x0000 }, {  187, 0x0000 }, {  187, 0x0020 },
  /* 0x6400 */
  {  188, 0x0000 }, {  188, 0x0000 }, {  188, 0x0000 }, {  188, 0x0000 },
  {  188, 0x0000 }, {  188, 0x0000 }, {  188, 0x0001 }, {  189, 0x0000 },
  {  189, 0x0000 }, {  189, 0x2000 }, {  190, 0x0000 }, {  190, 0x0000 },
  {  190, 0x4000 }, {  191, 0x0000 }, {  191, 0x0000 }, {  191, 0x0000 },
  /* 0x6500 */
  {  191, 0x0000 }, {  191, 0x0000 }, {  191, 0x0000 }, {  191, 0x0000 },
  {  191, 0x4000 }, {  192, 0x0000 }, {  192, 0x0000 }, {  192, 0x0000 },
  {  192, 0x0000 }, {  192, 0x0000 }, {  192, 0x0000 }, {  192, 0x0000 },
  {  192, 0x0000 }, {  192, 0x0000 }, {  192, 0x0000 }, {  192, 0x0000 },
  /* 0x6600 */
  {  192, 0x0201 }, {  194, 0x4020 }, {  196, 0x4010 }, {  198, 0x0802 },
  {  200, 0x0000 }, {  200, 0x0280 }, {  202, 0x0020 }, {  203, 0x0008 },
  {  204, 0x0000 }, {  204, 0x0200 }, {  205, 0x0001 }, {  206, 0x8004 },
  {  208, 0x0000 }, {  208, 0x0000 }, {  208, 0x0000 }, {  208, 0x0c00 },
  /* 0x6700 */
  {  210, 0x4000 }, {  211, 0x0000 }, {  211, 0x0000 }, {  211, 0x0000 },
  {  211, 0x0000 }, {  211, 0x0000 }, {  211, 0x0040 }, {  212, 0x0000 },
  {  212, 0x0000 }, {  212, 0x0000 }, {  212, 0x0000 }, {  212, 0x0800 },
  {  213, 0x0001 }, {  214, 0x0000 }, {  214, 0x0000 }, {  214, 0x0000 },
  /* 0x6800 */
  {  214, 0x0002 }, {  215, 0x0000 }, {  215, 0x0000 }, {  215, 0x0000 },
  {  215, 0x0010 }, {  216, 0x0004 }, {  217, 0x0000 }, {  217, 0x0000 },
  {  217, 0x0000 }, {  217, 0x0000 }, {  217, 0x0000 }, {  217, 0x0000 },
  {  217, 0x8100 }, {  219, 0x0000 }, {  219, 0x0000 }, {  219, 0x0000 },
  /* 0x6900 */
  {  219, 0x0000 }, {  219, 0x0000 }, {  219, 0x0000 }, {  219, 0x0000 },
  {  219, 0x0000 }, {  219, 0x0000 }, {  219, 0x0100 }, {  220, 0x0000 },
  {  220, 0x0000 }, {  220, 0x0100 }, {  221, 0x0000 }, {  221, 0x0000 },
  {  221, 0x0000 }, {  221, 0x0000 }, {  221, 0x0004 }, {  222, 0x0000 },
  /* 0x6a00 */
  {  222, 0x0000 }, {  222, 0x0000 }, {  222, 0x0000 }, {  222, 0x0001 },
  {  223, 0x0040 }, {  224, 0x0000 }, {  224, 0x0800 }, {  225, 0x4008 },
  {  227, 0x0000 }, {  227, 0x0000 }, {  227, 0x0000 }, {  227, 0x0000 },
  {  227, 0x0000 }, {  227, 0x0000 }, {  227, 0x0014 }, {  229, 0x0000 },
  /* 0x6b00 */
  {  229, 0x0000 }, {  229, 0x0000 }, {  229, 0x0000 }, {  229, 0x0000 },
  {  229, 0x0000 }, {  229, 0x0000 }, {  229, 0x0000 }, {  229, 0x0000 },
  {  229, 0x0000 }, {  229, 0x0000 }, {  229, 0x0000 }, {  229, 0x0000 },
  {  229, 0x0000 }, {  229, 0x0040 }, {  230, 0x0000 }, {  230, 0x0000 },
  /* 0x6c00 */
  {  230, 0x0000 }, {  230, 0x0000 }, {  230, 0x0000 }, {  230, 0x8000 },
  {  231, 0x0000 }, {  231, 0x1000 }, {  232, 0x8000 }, {  233, 0x0000 },
  {  233, 0x0040 }, {  234, 0x0000 }, {  234, 0x0000 }, {  234, 0x0000 },
  {  234, 0x0000 }, {  234, 0x0400 }, {  235, 0x0000 }, {  235, 0x0000 },
  /* 0x6d00 */
  {  235, 0x0010 }, {  236, 0x0000 }, {  236, 0x0000 }, {  236, 0x0000 },
  {  236, 0x0000 }, {  236, 0x0000 }, {  236, 0x8000 }, {  237, 0x0000 },
  {  237, 0x0080 }, {  238, 0x0040 }, {  239, 0x1000 }, {  240, 0x0000 },
  {  240, 0x8000 }, {  241, 0x0000 }, {  241, 0x0000 }, {  241, 0x1104 },
  /* 0x6e00 */
  {  244, 0x0000 }, {  244, 0x0000 }, {  244, 0x0080 }, {  245, 0x1200 },
  {  247, 0x0000 }, {  247, 0x1000 }, {  248, 0x0000 }, {  248, 0x0000 },
  {  248, 0x0000 }, {  248, 0x0000 }, {  248, 0x0000 }, {  248, 0x8000 },
  {  249, 0x0000 }, {  249, 0x0000 }, {  249, 0x0000 }, {  249, 0x0000 },
  /* 0x6f00 */
  {  249, 0x0000 }, {  249, 0x0000 }, {  249, 0x0000 }, {  249, 0x0000 },
  {  249, 0x0000 }, {  249, 0x0000 }, {  249, 0x0000 }, {  249, 0x0000 },
  {  249, 0x0100 }, {  250, 0x0000 }, {  250, 0x0000 }, {  250, 0x0020 },
  {  251, 0x0000 }, {  251, 0x0000 }, {  251, 0x0000 }, {  251, 0x0020 },
  /* 0x7000 */
  {  252, 0x00a0 }, {  254, 0x0000 }, {  254, 0x0100 }, {  255, 0x0000 },
  {  255, 0x0000 }, {  255, 0x0000 }, {  255, 0x0000 }, {  255, 0x0000 },
  {  255, 0x0020 }, {  256, 0x0000 }, {  256, 0x0800 }, {  257, 0x0800 },
  {  258, 0x0000 }, {  258, 0x0000 }, {  258, 0x0000 }, {  258, 0x0000 },
  /* 0x7100 */
  {  258, 0x8010 }, {  260, 0x0000 }, {  260, 0x0000 }, {  260, 0x0000 },
  {  260, 0x00c0 }, {  262, 0x1000 }, {  263, 0x0000 }, {  263, 0x0000 },
  {  263, 0x0000 }, {  263, 0x0000 }, {  263, 0x0000 }, {  263, 0x0000 },
  {  263, 0x0002 }, {  264, 0x0000 }, {  264, 0x0000 }, {  264, 0x4000 },
  /* 0x7200 */
  {  265, 0x0000 }, {  265, 0x0000 }, {  265, 0x0000 }, {  265, 0x0000 },
  {  265, 0x0000 }, {  265, 0x0000 }, {  265, 0x0000 }, {  265, 0x0000 },
  {  265, 0x0000 }, {  265, 0x0000 }, {  265, 0x0000 }, {  265, 0x4002 },
  {  267, 0x0000 }, {  267, 0x0000 }, {  267, 0x0000 }, {  267, 0x0000 },
  /* 0x7300 */
  {  267, 0x0000 }, {  267, 0x0000 }, {  267, 0x0010 }, {  268, 0x0000 },
  {  268, 0x0000 }, {  268, 0x0000 }, {  268, 0x0000 }, {  268, 0x0080 },
  {  269, 0x0000 }, {  269, 0x0000 }, {  269, 0x0000 }, {  269, 0x2000 },
  {  270, 0x0200 }, {  271, 0x0044 }, {  273, 0x0008 }, {  274, 0x0020 },
  /* 0x7400 */
  {  275, 0x0080 }, {  276, 0x0000 }, {  276, 0x4640 }, {  280, 0x0000 },
  {  280, 0x0000 }, {  280, 0x0000 }, {  280, 0x0004 }, {  281, 0x0000 },
  {  281, 0x0200 }, {  282, 0x8000 }, {  283, 0x0000 }, {  283, 0x0000 },
  {  283, 0x0000 }, {  283, 0x0000 }, {  283, 0x0000 }, {  283, 0x0000 },
  /* 0x7500 */
  {  283, 0x0002 }, {  284, 0x0000 }, {  284, 0x8000 }, {  285, 0x0000 },
  {  285, 0x0000 }, {  285, 0x0000 }, {  285, 0x8000 }, {  286, 0x0000 },
  {  286, 0x0000 }, {  286, 0x0000 }, {  286, 0x0000 }, {  286, 0x0000 },
  {  286, 0x0000 }, {  286, 0x0000 }, {  286, 0x0000 }, {  286, 0x0000 },
  /* 0x7600 */
  {  286, 0x0000 }, {  286, 0x0000 }, {  286, 0x0000 }, {  286, 0x0000 },
  {  286, 0x0000 }, {  286, 0x0000 }, {  286, 0x0000 }, {  286, 0x0000 },
  {  286, 0x0004 }, {  287, 0x5800 }, {  290, 0x0040 }, {  291, 0x0000 },
  {  291, 0x0000 }, {  291, 0x0000 }, {  291, 0x0000 }, {  291, 0x0000 },
  /* 0x7700 */
  {  291, 0x0000 }, {  291, 0x0000 }, {  291, 0x0000 }, {  291, 0x0000 },
  {  291, 0x0040 }, {  292, 0x0000 }, {  292, 0x0000 }, {  292, 0x0000 },
  {  292, 0x0000 }, {  292, 0x0000 }, {  292, 0x0000 }, {  292, 0x0000 },
  {  292, 0x0000 }, {  292, 0x0000 }, {  292, 0x0000 }, {  292, 0x0000 },
  /* 0x7800 */
  {  292, 0x0000 }, {  292, 0x0000 }, {  292, 0x0002 }, {  293, 0x0000 },
  {  293, 0x4000 }, {  294, 0x0000 }, {  294, 0x0010 }, {  295, 0x0400 },
  {  296, 0x0000 }, {  296, 0x0000 }, {  296, 0x0000 }, {  296, 0x0000 },
  {  296, 0x0000 }, {  296, 0x0000 }, {  296, 0x0000 }, {  296, 0x0000 },
  /* 0x7900 */
  {  296, 0x0000 }, {  296, 0x0000 }, {  296, 0x0000 }, {  296, 0x0001 },
  {  297, 0x0000 }, {  297, 0x0000 }, {  297, 0x0000 }, {  297, 0x0000 },
  {  297, 0x0000 }, {  297, 0x0810 }, {  299, 0x0000 }, {  299, 0x0000 },
  {  299, 0x0000 }, {  299, 0x0000 }, {  299, 0x0000 }, {  299, 0x0000 },
  /* 0x7a00 */
  {  299, 0x0000 }, {  299, 0x0000 }, {  299, 0x0000 }, {  299, 0x0000 },
  {  299, 0x0000 }, {  299, 0x0000 }, {  299, 0x0000 }, {  299, 0x0000 },
  {  299, 0x0000 }, {  299, 0x0000 }, {  299, 0x0000 }, {  299, 0x0000 },
  {  299, 0x0000 }, {  299, 0x0002 }, {  300, 0x0880 }, {  302, 0x0000 },
  /* 0x7b00 */
  {  302, 0x0000 }, {  302, 0x0000 }, {  302, 0x0000 }, {  302, 0x0000 },
  {  302, 0x0000 }, {  302, 0x0000 }, {  302, 0x0000 }, {  302, 0x0000 },
  {  302, 0x0000 }, {  302, 0x4000 },
};
static const Summary16 cp932ext_uni2indx_page7d[43] = {
  /* 0x7d00 */
  {  303, 0x0000 }, {  303, 0x0000 }, {  303, 0x0000 }, {  303, 0x0000 },
  {  303, 0x0100 }, {  304, 0x1000 }, {  305, 0x0000 }, {  305, 0x0000 },
  {  305, 0x0000 }, {  305, 0x0000 }, {  305, 0x0001 }, {  306, 0x0080 },
  {  307, 0x0000 }, {  307, 0x0040 }, {  308, 0x0000 }, {  308, 0x0000 },
  /* 0x7e00 */
  {  308, 0x0000 }, {  308, 0x0000 }, {  308, 0x0000 }, {  308, 0x0000 },
  {  308, 0x0000 }, {  308, 0x0004 }, {  309, 0x0000 }, {  309, 0x0000 },
  {  309, 0x0400 }, {  310, 0x0000 }, {  310, 0x0000 }, {  310, 0x0000 },
  {  310, 0x0000 }, {  310, 0x0000 }, {  310, 0x0000 }, {  310, 0x0000 },
  /* 0x7f00 */
  {  310, 0x0000 }, {  310, 0x0000 }, {  310, 0x0000 }, {  310, 0x0000 },
  {  310, 0x0080 }, {  311, 0x0000 }, {  311, 0x0000 }, {  311, 0x0000 },
  {  311, 0x0000 }, {  311, 0x0000 }, {  311, 0x0002 },
};
static const Summary16 cp932ext_uni2indx_page83[44] = {
  /* 0x8300 */
  {  312, 0x0002 }, {  313, 0x0000 }, {  313, 0x0000 }, {  313, 0x0000 },
  {  313, 0x0000 }, {  313, 0x0000 }, {  313, 0x0004 }, {  314, 0x8000 },
  {  315, 0x0000 }, {  315, 0x0000 }, {  315, 0x0000 }, {  315, 0x0000 },
  {  315, 0x0080 }, {  316, 0x0000 }, {  316, 0x0000 }, {  316, 0x0040 },
  /* 0x8400 */
  {  317, 0x0000 }, {  317, 0x0000 }, {  317, 0x0000 }, {  317, 0x0000 },
  {  317, 0x0100 }, {  318, 0x0000 }, {  318, 0x0000 }, {  318, 0x0000 },
  {  318, 0x0000 }, {  318, 0x0000 }, {  318, 0x0000 }, {  318, 0x0010 },
  {  319, 0x0000 }, {  319, 0x1000 }, {  320, 0x0000 }, {  320, 0x0000 },
  /* 0x8500 */
  {  320, 0x0000 }, {  320, 0x0000 }, {  320, 0x0000 }, {  320, 0x0000 },
  {  320, 0x0000 }, {  320, 0x0208 }, {  322, 0x0800 }, {  323, 0x0000 },
  {  323, 0x0000 }, {  323, 0x0000 }, {  323, 0x0000 }, {  323, 0x0001 },
};
static const Summary16 cp932ext_uni2indx_page88[109] = {
  /* 0x8800 */
  {  324, 0x0080 }, {  325, 0x0000 }, {  325, 0x0000 }, {  325, 0x0000 },
  {  325, 0x0000 }, {  325, 0x0000 }, {  325, 0x0000 }, {  325, 0x0000 },
  {  325, 0x0000 }, {  325, 0x0000 }, {  325, 0x0000 }, {  325, 0x0000 },
  {  325, 0x0000 }, {  325, 0x0000 }, {  325, 0x0000 }, {  325, 0x0020 },
  /* 0x8900 */
  {  326, 0x0000 }, {  326, 0x1000 }, {  327, 0x0000 }, {  327, 0x0000 },
  {  327, 0x0000 }, {  327, 0x0000 }, {  327, 0x0000 }, {  327, 0x0000 },
  {  327, 0x0000 }, {  327, 0x0000 }, {  327, 0x0000 }, {  327, 0x0000 },
  {  327, 0x0000 }, {  327, 0x0000 }, {  327, 0x0000 }, {  327, 0x0000 },
  /* 0x8a00 */
  {  327, 0x0000 }, {  327, 0x0004 }, {  328, 0x0000 }, {  328, 0x0080 },
  {  329, 0x0000 }, {  329, 0x0000 }, {  329, 0x0000 }, {  329, 0x0200 },
  {  330, 0x0000 }, {  330, 0x0000 }, {  330, 0x0080 }, {  331, 0x4000 },
  {  332, 0x0000 }, {  332, 0x8000 }, {  333, 0x0000 }, {  333, 0x0040 },
  /* 0x8b00 */
  {  334, 0x0000 }, {  334, 0x0000 }, {  334, 0x0000 }, {  334, 0x0000 },
  {  334, 0x0000 }, {  334, 0x0008 }, {  335, 0x0000 }, {  335, 0x8000 },
  {  336, 0x0000 }, {  336, 0x0000 }, {  336, 0x0000 }, {  336, 0x0000 },
  {  336, 0x0000 }, {  336, 0x0000 }, {  336, 0x0000 }, {  336, 0x0000 },
  /* 0x8c00 */
  {  336, 0x0000 }, {  336, 0x0000 }, {  336, 0x0000 }, {  336, 0x0000 },
  {  336, 0x0000 }, {  336, 0x0000 }, {  336, 0x0000 }, {  336, 0x0000 },
  {  336, 0x0000 }, {  336, 0x0000 }, {  336, 0x0000 }, {  336, 0x0000 },
  {  336, 0x0000 }, {  336, 0x0000 }, {  336, 0x0000 }, {  336, 0x0011 },
  /* 0x8d00 */
  {  338, 0x0000 }, {  338, 0x0004 }, {  339, 0x0000 }, {  339, 0x0000 },
  {  339, 0x0000 }, {  339, 0x0000 }, {  339, 0x0000 }, {  339, 0x0040 },
  {  340, 0x0000 }, {  340, 0x0000 }, {  340, 0x0000 }, {  340, 0x0000 },
  {  340, 0x0000 }, {  340, 0x0000 }, {  340, 0x0000 }, {  340, 0x0000 },
  /* 0x8e00 */
  {  340, 0x0000 }, {  340, 0x0000 }, {  340, 0x0000 }, {  340, 0x0000 },
  {  340, 0x0000 }, {  340, 0x0000 }, {  340, 0x0000 }, {  340, 0x0000 },
  {  340, 0x0000 }, {  340, 0x0000 }, {  340, 0x0000 }, {  340, 0x0000 },
  {  340, 0x8000 },
};
static const Summary16 cp932ext_uni2indx_page90[238] = {
  /* 0x9000 */
  {  341, 0x0000 }, {  341, 0x0000 }, {  341, 0x0000 }, {  341, 0x0000 },
  {  341, 0x0000 }, {  341, 0x0000 }, {  341, 0x0080 }, {  342, 0x0000 },
  {  342, 0x0000 }, {  342, 0x0000 }, {  342, 0x0000 }, {  342, 0x0000 },
  {  342, 0x0000 }, {  342, 0x4000 }, {  343, 0x0000 }, {  343, 0x0000 },
  /* 0x9100 */
  {  343, 0x0000 }, {  343, 0x0020 }, {  344, 0x0080 }, {  345, 0x0000 },
  {  345, 0x0000 }, {  345, 0x0000 }, {  345, 0x0000 }, {  345, 0x0000 },
  {  345, 0x0000 }, {  345, 0x0000 }, {  345, 0x0000 }, {  345, 0x0000 },
  {  345, 0x0000 }, {  345, 0x4480 }, {  348, 0x6030 }, {  352, 0x0000 },
  /* 0x9200 */
  {  352, 0x0440 }, {  354, 0x0001 }, {  355, 0x0000 }, {  355, 0x1600 },
  {  358, 0x4001 }, {  360, 0x0202 }, {  362, 0x0080 }, {  363, 0x0180 },
  {  365, 0x0100 }, {  366, 0x0000 }, {  366, 0x0080 }, {  367, 0x0000 },
  {  367, 0x0000 }, {  367, 0x02a9 }, {  372, 0x0081 }, {  374, 0x8a00 },
  /* 0x9300 */
  {  377, 0x0004 }, {  378, 0x6000 }, {  380, 0x0022 }, {  382, 0x0000 },
  {  382, 0x0100 }, {  383, 0x0080 }, {  384, 0x0000 }, {  384, 0x0001 },
  {  385, 0x0000 }, {  385, 0x0000 }, {  385, 0x0010 }, {  386, 0x0000 },
  {  386, 0x0040 }, {  387, 0x4000 }, {  388, 0x0000 }, {  388, 0x0100 },
  /* 0x9400 */
  {  389, 0x0000 }, {  389, 0x0000 }, {  389, 0x0000 }, {  389, 0x0002 },
  {  390, 0x0120 }, {  392, 0x0000 }, {  392, 0x0000 }, {  392, 0x0000 },
  {  392, 0x0000 }, {  392, 0x0000 }, {  392, 0x0000 }, {  392, 0x0000 },
  {  392, 0x0000 }, {  392, 0x0000 }, {  392, 0x0000 }, {  392, 0x0000 },
  /* 0x9500 */
  {  392, 0x0000 }, {  392, 0x0000 }, {  392, 0x0000 }, {  392, 0x0000 },
  {  392, 0x0000 }, {  392, 0x0000 }, {  392, 0x0000 }, {  392, 0x0000 },
  {  392, 0x0000 }, {  392, 0x0004 }, {  393, 0x0000 }, {  393, 0x0000 },
  {  393, 0x0000 }, {  393, 0x0000 }, {  393, 0x0000 }, {  393, 0x0000 },
  /* 0x9600 */
  {  393, 0x0000 }, {  393, 0x0000 }, {  393, 0x0000 }, {  393, 0x0000 },
  {  393, 0x0000 }, {  393, 0x0000 }, {  393, 0x0000 }, {  393, 0x0000 },
  {  393, 0x0000 }, {  393, 0x2000 }, {  394, 0x8000 }, {  395, 0x0000 },
  {  395, 0x0000 }, {  395, 0x0000 }, {  395, 0x0000 }, {  395, 0x0000 },
  /* 0x9700 */
  {  395, 0x0000 }, {  395, 0x0000 }, {  395, 0x0000 }, {  395, 0x0808 },
  {  397, 0xa008 }, {  400, 0x0022 }, {  402, 0x0000 }, {  402, 0x0000 },
  {  402, 0x0000 }, {  402, 0x0000 }, {  402, 0x0000 }, {  402, 0x0000 },
  {  402, 0x0000 }, {  402, 0x0000 }, {  402, 0x0000 }, {  402, 0x0000 },
  /* 0x9800 */
  {  402, 0x0000 }, {  402, 0x0000 }, {  402, 0x0000 }, {  402, 0x0000 },
  {  402, 0x0000 }, {  402, 0x0080 }, {  403, 0x0020 }, {  404, 0x0000 },
  {  404, 0x0000 }, {  404, 0x0000 }, {  404, 0x0000 }, {  404, 0x0000 },
  {  404, 0x0000 }, {  404, 0x0000 }, {  404, 0x0000 }, {  404, 0x0000 },
  /* 0x9900 */
  {  404, 0x0000 }, {  404, 0x0000 }, {  404, 0x0080 }, {  405, 0x0000 },
  {  405, 0x0000 }, {  405, 0x0000 }, {  405, 0x0000 }, {  405, 0x0000 },
  {  405, 0x0000 }, {  405, 0x4000 }, {  406, 0x0000 }, {  406, 0x0000 },
  {  406, 0x0000 }, {  406, 0x0000 }, {  406, 0x0000 }, {  406, 0x0000 },
  /* 0x9a00 */
  {  406, 0x0000 }, {  406, 0x0000 }, {  406, 0x0000 }, {  406, 0x0000 },
  {  406, 0x4000 }, {  407, 0x0000 }, {  407, 0x0000 }, {  407, 0x0000 },
  {  407, 0x0000 }, {  407, 0x0000 }, {  407, 0x0000 }, {  407, 0x0000 },
  {  407, 0x0000 }, {  407, 0x1200 }, {  409, 0x0000 }, {  409, 0x0000 },
  /* 0x9b00 */
  {  409, 0x0000 }, {  409, 0x0000 }, {  409, 0x0000 }, {  409, 0x0000 },
  {  409, 0x0000 }, {  409, 0x0000 }, {  409, 0x0000 }, {  409, 0x0024 },
  {  411, 0x8000 }, {  412, 0x0000 }, {  412, 0x0000 }, {  412, 0x0802 },
  {  414, 0x0000 }, {  414, 0x0000 }, {  414, 0x0000 }, {  414, 0x0000 },
  /* 0x9c00 */
  {  414, 0x0001 }, {  415, 0x0000 }, {  415, 0x0000 }, {  415, 0x0000 },
  {  415, 0x0000 }, {  415, 0x0000 }, {  415, 0x0000 }, {  415, 0x0000 },
  {  415, 0x0000 }, {  415, 0x0000 }, {  415, 0x0000 }, {  415, 0x0000 },
  {  415, 0x0000 }, {  415, 0x0000 }, {  415, 0x0000 }, {  415, 0x0000 },
  /* 0x9d00 */
  {  415, 0x0000 }, {  415, 0x0000 }, {  415, 0x0000 }, {  415, 0x0000 },
  {  415, 0x0000 }, {  415, 0x0000 }, {  415, 0x0800 }, {  416, 0x0001 },
  {  417, 0x0000 }, {  417, 0x0000 }, {  417, 0x0000 }, {  417, 0x0000 },
  {  417, 0x0000 }, {  417, 0x0000 }, {  417, 0x0000 }, {  417, 0x0000 },
  /* 0x9e00 */
  {  417, 0x0000 }, {  417, 0x0200 }, {  418, 0x0000 }, {  418, 0x0000 },
  {  418, 0x0000 }, {  418, 0x0000 }, {  418, 0x0000 }, {  418, 0x0000 },
  {  418, 0x0000 }, {  418, 0x0000 }, {  418, 0x0000 }, {  418, 0x0000 },
  {  418, 0x0000 }, {  418, 0x0002 },
};
static const Summary16 cp932ext_uni2indx_pagef9[19] = {
  /* 0xf900 */
  {  419, 0x0000 }, {  419, 0x0000 }, {  419, 0x0200 }, {  420, 0x0000 },
  {  420, 0x0000 }, {  420, 0x0000 }, {  420, 0x0000 }, {  420, 0x0000 },
  {  420, 0x0000 }, {  420, 0x0000 }, {  420, 0x0000 }, {  420, 0x0000 },
  {  420, 0x0000 }, {  420, 0x1000 }, {  421, 0x0000 }, {  421, 0x0000 },
  /* 0xfa00 */
  {  421, 0xc000 }, {  423, 0xffff }, {  439, 0x3fff },
};
static const Summary16 cp932ext_uni2indx_pageff[15] = {
  /* 0xff00 */
  {  453, 0x0084 }, {  455, 0x0000 }, {  455, 0x0000 }, {  455, 0x0000 },
  {  455, 0x0000 }, {  455, 0x0000 }, {  455, 0x0000 }, {  455, 0x0000 },
  {  455, 0x0000 }, {  455, 0x0000 }, {  455, 0x0000 }, {  455, 0x0000 },
  {  455, 0x0000 }, {  455, 0x0000 }, {  455, 0x0014 },
};

static int
cp932ext_wctomb (conv_t conv, unsigned char *r, ucs4_t wc, int n)
{
  if (n >= 2) {
    const Summary16 *summary = NULL;
    if (wc >= 0x2100 && wc < 0x22c0)
      summary = &cp932ext_uni2indx_page21[(wc>>4)-0x210];
    else if (wc >= 0x2400 && wc < 0x2480)
      summary = &cp932ext_uni2indx_page24[(wc>>4)-0x240];
    else if (wc >= 0x3000 && wc < 0x3020)
      summary = &cp932ext_uni2indx_page30[(wc>>4)-0x300];
    else if (wc >= 0x3200 && wc < 0x33d0)
      summary = &cp932ext_uni2indx_page32[(wc>>4)-0x320];
    else if (wc >= 0x4e00 && wc < 0x5590)
      summary = &cp932ext_uni2indx_page4e[(wc>>4)-0x4e0];
    else if (wc >= 0x5700 && wc < 0x59c0)
      summary = &cp932ext_uni2indx_page57[(wc>>4)-0x570];
    else if (wc >= 0x5b00 && wc < 0x5de0)
      summary = &cp932ext_uni2indx_page5b[(wc>>4)-0x5b0];
    else if (wc >= 0x5f00 && wc < 0x7ba0)
      summary = &cp932ext_uni2indx_page5f[(wc>>4)-0x5f0];
    else if (wc >= 0x7d00 && wc < 0x7fb0)
      summary = &cp932ext_uni2indx_page7d[(wc>>4)-0x7d0];
    else if (wc >= 0x8300 && wc < 0x85c0)
      summary = &cp932ext_uni2indx_page83[(wc>>4)-0x830];
    else if (wc >= 0x8800 && wc < 0x8ed0)
      summary = &cp932ext_uni2indx_page88[(wc>>4)-0x880];
    else if (wc >= 0x9000 && wc < 0x9ee0)
      summary = &cp932ext_uni2indx_page90[(wc>>4)-0x900];
    else if (wc >= 0xf900 && wc < 0xfa30)
      summary = &cp932ext_uni2indx_pagef9[(wc>>4)-0xf90];
    else if (wc >= 0xff00 && wc < 0xfff0)
      summary = &cp932ext_uni2indx_pageff[(wc>>4)-0xff0];
    if (summary) {
      unsigned short used = summary->used;
      unsigned int i = wc & 0x0f;
      if (used & ((unsigned short) 1 << i)) {
        unsigned short c;
        /* Keep in `used' only the bits 0..i-1. */
        used &= ((unsigned short) 1 << i) - 1;
        /* Add `summary->indx' and the number of bits set in `used'. */
        used = (used & 0x5555) + ((used & 0xaaaa) >> 1);
        used = (used & 0x3333) + ((used & 0xcccc) >> 2);
        used = (used & 0x0f0f) + ((used & 0xf0f0) >> 4);
        used = (used & 0x00ff) + (used >> 8);
        c = cp932ext_2charset[summary->indx + used];
        r[0] = (c >> 8); r[1] = (c & 0xff);
        return 2;
      }
    }
    return RET_ILUNI;
  }
  return RET_TOOSMALL;
}
