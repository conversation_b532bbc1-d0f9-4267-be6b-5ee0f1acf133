.fn-whitespace-stop-collapsing .nav-folder-title-content {
    flex-grow: 1 !important;
    display: flex;
    padding-bottom: 4px !important;
    padding-top: 4px !important;
}

.fn-whitespace-stop-collapsing .collapse-icon {
    padding-top: 4px !important;
}

.mod-rtl .fn-whitespace-stop-collapsing .nav-folder-title-content {
    padding-left: 8px !important;
}

.fn-whitespace-stop-collapsing .nav-folder-title {
    padding-bottom: 0 !important;
    padding-top: 0 !important;
}

body:not(.mod-rtl).fn-whitespace-stop-collapsing .nav-folder-title {
    padding-right: 0 !important;
}

.mod-rtl.fn-whitespace-stop-collapsing .nav-folder-title {
    padding-left: 0 !important;
}

.fn-whitespace-stop-collapsing .nav-folder-collapse-indicator {
    margin-top: 4px !important;
}

body:not(.is-grabbing) .tree-item-self.fn-is-active:hover,
body:not(.disable-folder-highlight) .tree-item-self.fn-is-active {
    color: var(--nav-item-color-active);
    background-color: var(--nav-item-background-active);
    font-weight: var(--nav-item-weight-active);
}

.has-folder-note .nav-folder-title-content:hover,
.has-folder-note.view-header-breadcrumb:hover {
    cursor: pointer;
}


.hide-folder-note .is-folder-note {
    display: none;
}

.hide-folder .folder-name {
    display: none;
}

.nav-folder-collapse-indicator:hover {
    cursor: pointer;
}

.fn-excluded-folder-heading {
    margin-top: 0 !important;
    border-top: 1px solid var(--background-modifier-border);
}

.add-exclude-folder-item {
    padding-bottom: 0 !important;
}

.fn-exclude-folder-list {
    padding-bottom: 0 !important;
}

.fn-exclude-folder-list.setting-item {
    border-top: 0 !important;
    border-bottom: 0 !important;
}

.fn-exclude-folder-list .setting-item-control {
    display: flex;
    justify-content: flex-start !important;
}

.fn-exclude-folder-list .setting-item-info {
    display: none !important;
}

.fn-exclude-folder-list .search-input-container {
    width: 100%;
}


.fn-confirmation-modal {
    padding-bottom: 0;
}

:not(.is-phone) .fn-confirmation-modal-button {
    margin-right: 0.7rem;

}

:not(.is-phone) .fn-delete-confirmation-modal-buttons {
    display: flex;
    align-items: center;
    margin-top: 10px;
}

.fn-delete-confirmation-modal-buttons span:hover,
.fn-delete-confirmation-modal-buttons input:hover {
    cursor: pointer;
}

:not(.is-phone) .fn-delete-confirmation-modal-buttons .fn-confirmation-modal-button {
    margin-left: auto;
}

:not(.is-phone) .fn-delete-confirmation-modal-buttons input[type="checkbox"] {
    margin-right: 5px;
}

.is-phone .fn-delete-confirmation-modal-buttons {
    display: flex;
    flex-direction: column;
    align-items: center;
}

.is-phone .fn-delete-confirmation-modal-buttons .fn-confirmation-modal-button {
    margin-top: 10px;
}

/* Folder overview */

.fn-folder-overview-collapse-icon {
    display: block !important;
}

.fn-has-no-files .collapse-icon {
    display: none !important;
}

.folder-overview-list {
    margin-top: 0 !important;
    margin-bottom: 0 !important;
    padding-bottom: 1.200 !important;
    padding-top: 1.200 !important;
}

.folder-overview-list-item {
    display: flex;
}


.folder-overview-list::marker {
    color: var(--text-faint);
}

.folder-list::marker {
    color: var(--text-normal) !important;
}

.folder-overview-grid {
    display: grid;
    grid-gap: 20px;
    grid-template-columns: repeat(3, 1fr);
}

.folder-overview-grid-item-article article {
    padding: 15px;
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
}

.folder-overview-grid-item-article a {
    text-decoration: none !important;
}

.folder-overview-grid-item-article h1 {
    font-size: 1.2rem;
}


.folder-overview-grid-item {
    flex: 1 1 auto;
    margin-right: 1.200rem;
    margin-bottom: 1.200rem;
}

.fn-confirmation-modal .setting-item {
    border-top: 0 !important;
    padding-top: 0 !important;
}

.pointer-cursor {
    cursor: pointer !important;
}


/* Setting tab style */
.fn-settings-tab-bar {
    display: flex;
    flex-direction: row;
    padding-bottom: 1rem;
}

.fn-settings-tab {
    display: flex;
    flex-direction: row;
    align-items: center;
    gap: var(--size-4-2);
    padding: 10px;
    border: 1px solid var(--background-modifier-border)
}

.fn-settings-tab-active {
    background-color: var(--color-accent);
    color: var(--text-on-accent);
}

.fn-settings-tab-name {
    font-weight: bold;
}

.fn-settings-tab-icon {
    display: flex;
}

/* File explorer & path styles */

.folder-note-underline .has-folder-note .nav-folder-title-content {
    text-decoration-line: underline;
    text-decoration-color: var(--text-faint);
    text-decoration-thickness: 2px;
    text-underline-offset: 1px;
}

.folder-note-underline-path .has-folder-note.view-header-breadcrumb {
    text-decoration-line: underline;
    text-decoration-color: var(--text-faint);
    text-decoration-thickness: 1px;
    text-underline-offset: 2px;
}

.folder-note-bold .has-folder-note .nav-folder-title-content,
.folder-note-bold-path .has-folder-note.view-header-breadcrumb {
    font-weight: bold;
}

.folder-note-cursive .has-folder-note .nav-folder-title-content,
.folder-note-cursive-path .has-folder-note.view-header-breadcrumb {
    font-style: italic;
}

.fn-hide-collapse-icon .has-folder-note.only-has-folder-note .tree-item-icon {
    display: none;
}

.fn-hide-empty-collapse-icon .fn-empty-folder .tree-item-icon {
    display: none;
}


.fn-suggestion-container {
    position: absolute;
    overflow: hidden;
    display: flex;
    flex-direction: column;
    background-color: var(--background-primary);
    max-width: 500px;
    max-height: 300px;
    border-radius: var(--radius-m);
    border: 1px solid var(--background-modifier-border);
    box-shadow: var(--shadow-s);
    z-index: var(--layer-notice);
}
