# 1. 流程图


```plantuml
title 查询期权计划迁移 
participant APP
participant 股权激励中台服务
participant 股权激励数据库
participant 数据中间件
participant 股权激励前置机通讯服务
participant 柜台
APP->股权激励中台服务:查询期权计划



股权激励中台服务->数据中间件:查历史行权委托，获取已行权数据
股权激励中台服务<-数据中间件:return
股权激励中台服务->股权激励数据库:查询期权计划信息
股权激励中台服务<-股权激励数据库:return
loop 每条期权计划
股权激励中台服务->股权激励前置机通讯服务:查询期权计划信息
股权激励前置机通讯服务->柜台:查询275[410231]-取权证行权价格
股权激励前置机通讯服务<-柜台:return
股权激励中台服务<-股权激励前置机通讯服务:return
股权激励中台服务->股权激励中台服务:优先使用行权价格为授予价格
股权激励中台服务->股权激励前置机通讯服务:查询317获取交易市场
股权激励中台服务<-股权激励前置机通讯服务:return
股权激励中台服务->股权激励中台服务:计算未行权数量

end
APP<-股权激励中台服务:return
```

# 2. 涉及的数据库、Redis、Kafka 等中间件的修改/新增图或者说明

不涉及

# 3. 前后端交互接口信息

## 3.1. 查询期权计划

### 3.1.1. 接口路径

POST /queryOptionPlanNewV2

### 3.1.2. 接口入参

无

### 3.1.3. 接口出参

| 名称          | 类型                                                                         | 必选  | 约束 | 说明                                     |
| ------------- | ---------------------------------------------------------------------------- | ----- | ---- | ---------------------------------------- |
| CompanyId     | string                                                                       | false | none | 公司ID                                   |
| MARKET        | string                                                                       | false | none | 交易市场-非上市公司为空                  |
| Market        | string                                                                       | false | none | 交易市场-非上市公司为空                  |
| Plans         | 期权计划信息| false | none | 期权激励计划                             |
| STOCKCODE     | string                                                                       | false | none | 股票代码-非上市公司为空                  |
| STOCKNAME     | string                                                                       | false | none | 股票名称-非上市公司为公司名称            |
| companyId     | string                                                                       | false | none | none                                     |
| listedCompany | string                                                                       | false | none | 是否上市公司，0：非上市公司，1：上市公司 |

期权计划信息

### 属性

| 名称           | 类型   | 必选  | 约束 | 说明       |
| -------------- | ------ | ----- | ---- | ---------- |
| CANCELSHARENUM | string | false | none | 注销股数   |
| CANEXERCISENUM | string | false | none | 可行权     |
| EXERCISEDNUM   | string | false | none | 已行权     |
| NOEXERCISEDNUM | string | false | none | 待行权     |
| OPTIONCODE     | string | false | none | 行权代码   |
| PLANID         | string | false | none | 计划ID     |
| PLANNAME       | string | false | none | 计划名称   |
| SHAREDATE      | string | false | none | 授予日     |
| SHAREPRICE     | string | false | none | 授予价格   |
| TOTALSHARENUM  | string | false | none | 总授予股数 |
| VERSIONID      | string | false | none | 版本ID     |

# 4. 配置&部署修改信息

不涉及

# 5. 新增技术组件说明

不涉及

# 6. 影响范围

不涉及

# 7. 外部依赖项

不涉及
