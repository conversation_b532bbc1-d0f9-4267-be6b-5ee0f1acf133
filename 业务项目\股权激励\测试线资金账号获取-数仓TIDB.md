### 1. 拿到TEL_JOINKEY 去步骤2中拿到资金账号 
select t.TEL_JOINKEY ,t.TEL_POST_ENCRY from dwd.TEL_ENCRY_MAPPING t where t.TEL_JOINKEY in ( select t.CON_MOBILE_JOINKEY from dws.ACCOUNT_BASIC_INFO t where t.CUST_CODE in ( select distinct CUST_CODE from dws.ACCOUNT_FUND_INFO ) );

### 2. 根据TEL_JOINKEY 拿到资金账号
select t.CUST_CODE from dws.ACCOUNT_BASIC_INFO t where t.CON_MOBILE_JOINKEY ='dc0c0313b220821eb21d7e49a54b593a'; select t.FUND_CODE , t.* from dws.ACCOUNT_FUND_INFO t where t.CUST_CODE =************;

### 3. 根据步骤1中的TEL_POST_ENCRY 加解密服务
[http://***********:8083/dc/dcdeencryptBatch](http://***********:8083/dc/dcdeencryptBatch) 
-- 入参：data为上面步骤1中的TEL_ENCRY_MAPPING 
{ "serverno": "sn_integrated_mang_test", "data": "aca8a5da9b168c01edfd71b844966680", "bizno": "54389f7aabc84b618234c32b2caaa28a", "ip": "***********", "version": "1" }


```
select a.*,t.CUST_CODE,t.CUST_NAME,e.FUND_CODE,t.ID_CODE from(select t.TEL_JOINKEY ,t.TEL_POST_ENCRY from dwd.TEL_ENCRY_MAPPING t where t.TEL_JOINKEY in ( select t.CON_MOBILE_JOINKEY from dws.ACCOUNT_BASIC_INFO t where t.CUST_CODE in ( select distinct CUST_CODE from dws.ACCOUNT_FUND_INFO ) ) )a left join dws.ACCOUNT_BASIC_INFO t on a.TEL_JOINKEY=t.CON_MOBILE_JOINKEY left join dws.ACCOUNT_FUND_INFO e on t.CUST_CODE=e.CUST_CODE limit 50

```
***********-************-************

***********-************

| TEL_JOINKEY                      | TEL_POST_ENCRY                   | CUST_CODE    | CUST_NAME    | FUND_CODE    | ID_CODE            |
| -------------------------------- | -------------------------------- | ------------ | ------------ | ------------ | ------------------ |
| 8e1d7c6eab8f48361209f76a35f72bd2 | d07b3a803f755c91455bfdd1a49c8e93 | 109842       | aa1          | ************ | 370687111111111111 |
| d733233db0cea3567eb729c1c14a0a6c | d07b3a803f755c91492c847933c52cf4 | ************ | aa2          | null         | 121212             |
| a496d7142c2a51c8d0cd2c215371cd71 | d07b3a803f755c9130a559fca56da659 | ************ | aa4          | ************ | 111111111111222222 |
| 22b52bee6d347d895f63eed091e67e8f | d07b3a803f755c915f1da7050d303146 | ************ | aa5          | ************ | 111111111111222222 |
| 015658aa2b743f89c4ce91a9dbf97720 | d07b3a803f755c91458e862e6022fffa | 541300282765 | 测试           | 541300282765 | 111111111111222222 |
| 19c20399314488ff8a34a8468dfb2074 | d07b3a803f755c9190255e03f7ae99aa | 1005036      | aa8          | 510200004359 | 111111111111222222 |
| 00ac81cd2ce0ed3e767305ff854fbfde | d07b3a803f755c91cceb88df4df1d0fd | ************ | aa9          | 541217000042 | 111111111111222222 |
| 00ac81cd2ce0ed3e767305ff854fbfde | d07b3a803f755c91cceb88df4df1d0fd | ************ | aa9          | 541210303382 | 111111111111222222 |
| 00ac81cd2ce0ed3e767305ff854fbfde | d07b3a803f755c91cceb88df4df1d0fd | ************ | aa9          | ************ | 111111111111222222 |
| 45e88223960196ee110a45f3868495bb | d07b3a803f755c916b11d6618436a1bd | 1213243      | aa10         | 370300012625 | 111111111111222222 |
| 45e88223960196ee110a45f3868495bb | d07b3a803f755c916b11d6618436a1bd | 1213243      | aa10         | 370317000061 | 111111111111222222 |
| 45e88223960196ee110a45f3868495bb | d07b3a803f755c916b11d6618436a1bd | 1213243      | aa10         | 370310012625 | 111111111111222222 |
| dc0c0313b220821eb21d7e49a54b593a | aca8a5da9b168c01edfd71b844966680 | 110100063368 | aa22         | 110100063368 | 111111111111222222 |
| dc0c0313b220821eb21d7e49a54b593a | aca8a5da9b168c01edfd71b844966680 | 540640125970 | aa105        | 540640125970 | 111111111111222222 |
| d733233db0cea3567eb729c1c14a0a6c | d07b3a803f755c91492c847933c52cf4 | 540700049586 | 540700000003 | 540710049763 | 111111111111222222 |
| d733233db0cea3567eb729c1c14a0a6c | d07b3a803f755c91492c847933c52cf4 | 540700049586 | 540700000003 | 540703214306 | 111111111111222222 |
| d733233db0cea3567eb729c1c14a0a6c | d07b3a803f755c91492c847933c52cf4 | 540700049586 | 540700000003 | 540700049763 | 111111111111222222 |
| 8e1d7c6eab8f48361209f76a35f72bd2 | d07b3a803f755c91455bfdd1a49c8e93 | 540700054062 | kjctest1     | 540717000009 | 111111111111222222 |
| 8e1d7c6eab8f48361209f76a35f72bd2 | d07b3a803f755c91455bfdd1a49c8e93 | 540700054062 | kjctest1     | 540710054268 | 111111111111222222 |
| 8e1d7c6eab8f48361209f76a35f72bd2 | d07b3a803f755c91455bfdd1a49c8e93 | 540700054062 | kjctest1     | 540700054268 | 111111111111222222 |
| 8e1d7c6eab8f48361209f76a35f72bd2 | d07b3a803f755c91455bfdd1a49c8e93 | 540231541120 | zltest       | null         | 111111111111222222 |
| 4f1b96c6df5ea5128c01245be2757ddd | d07b3a803f755c91239981d5b5358d12 | 540700001019 | 派大星          | 540700001012 | 111111111111222222 |
| 8e1d7c6eab8f48361209f76a35f72bd2 | d07b3a803f755c91455bfdd1a49c8e93 | ************ | aa1          | ************ | 370687111111111111 |
| 8e1d7c6eab8f48361209f76a35f72bd2 | d07b3a803f755c91455bfdd1a49c8e93 | 110700010878 | aa1          | 110717000052 | 370687111111111111 |
| 8e1d7c6eab8f48361209f76a35f72bd2 | d07b3a803f755c91455bfdd1a49c8e93 | 110700010878 | aa1          | 110710300113 | 370687111111111111 |
| 8e1d7c6eab8f48361209f76a35f72bd2 | d07b3a803f755c91455bfdd1a49c8e93 | 110700010878 | aa1          | 110700010878 | 370687111111111111 |
| 8e1d7c6eab8f48361209f76a35f72bd2 | d07b3a803f755c91455bfdd1a49c8e93 | ************ | aa1          | ************ | 370687111111111111 |
| 8e1d7c6eab8f48361209f76a35f72bd2 | d07b3a803f755c91455bfdd1a49c8e93 | 110200000300 | aa1          | 110200000283 | 370687111111111111 |
| 8e1d7c6eab8f48361209f76a35f72bd2 | d07b3a803f755c91455bfdd1a49c8e93 | 110200012093 | aa1          | 110200012093 | 370687111111111111 |
| 20240829005                      | d07b3a803f755c914b979cd4284abd0e | 10195973     | aa1          | 540300198110 | 370687111111111111 |
| 20240829004                      | d07b3a803f755c91f7f1a1fd7fd03589 | 540700034616 | aa1          | 540700034731 | 370687111111111111 |
| 20240829003                      | d07b3a803f755c914acef724d2ca39db | 110200014017 | aa1          | 110200014017 | 370687111111111111 |
| 20240829002                      | d07b3a803f755c9105a8ab72060259f3 | 110200010738 | aa1          | 110200010738 | 370687111111111111 |
| 20240829001                      | d07b3a803f755c91408e5f2f8dd236d6 | 110200010718 | aa1          | 110200010718 | 370687111111111111 |
| 8e1d7c6eab8f48361209f76a35f72bd2 | d07b3a803f755c91455bfdd1a49c8e93 | 540700265470 | aa1          | null         | 370687111111111111 |
