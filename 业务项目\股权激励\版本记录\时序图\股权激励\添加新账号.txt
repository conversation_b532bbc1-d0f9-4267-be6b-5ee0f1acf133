title 添加新账号

participant backend
participant DB
participant 通行证系统

participant Choice-crm

backend->DB:根据手机号查询是否存在账号
backend<--DB:返回结果
backend->通行证系统:根据手机号查询通行证\n
backend<-通行证系统:通行证

DB->(3)Choice-crm
backend->DB:根据公司号查询公司最新创建的账号及公司前缀
backend<-DB:公司最新创建的账号及公司前缀
backend->backend:根据公司前缀生成账号
backend->DB:保存账号信息到数据库
backend<-DB:保存结果
backend->backend:判断是否保存成功
backend->Choice-crm:同步用户信息
backend<-Choice-crm:同步结果
backend->backend:短信提示用户开通了企业通权限，以及对应的账号名，并提示使用该手机对应的的东方财富账号密码登录或手机短信登录