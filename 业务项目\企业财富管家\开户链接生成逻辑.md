开户链接模板：https://ceshi.securities.eastmoney.com:7151/?orgid={}&channelname=80&channelvalue={}&channelvalue2={}

channelname固定80，为企业通渠道



{}为参数

1. 第一个参数：ORGID 取DEPARTMENTKEy

   需查询开户库

   ```
    
   
   select c.category,c.DEPARTMENTKEy,c.DEPARTMENTNAME,c.EID  from emcrm.omp_sys_user a
   left join emcrm.omp_sys_user_department b
   on a.eid=b.usereid
   LEFT JOIN emcrm.OMP_SYS_DEPARTMENT C
   ON b.DEPARTMENTEID =c.EID
   where b.usereid=#{userEid}
   ```

// 企业客户部

查出来的sql的，如果category为3，那么直接取。
如果category为2，那么取同名的营业部列表
SELECT departmentkey FROM emcrm.OMP_SYS_DEPARTMENT WHERE parentEid=c.eid AND departmentname=c.departmentname  取第一个departmentkey  如果没取到，那么填写 3102
如果category都不属于，那么直接取3102

userEid为服务经理的userId

2. 第二个参数：channelvalue

   需查询EMOPR运营库

   取DCOMP.OMP_ENTERPRISE_CUST_MANAGE_USERINFO中的eid字段

   ```
      SELECT eid,userid FROM DCOMP.OMP_ENTERPRISE_CUST_MANAGE_USERINFO  where oacode=#{工号}
   ```

3. 第三个参数：channelvalue2

   传当前用户公司的证券代码