# 1. 流程图

```plantuml

title 查询用户股权激励信息迁移

participant APP
participant 股权激励中台服务
participant 股权激励数据库
participant 数据中间件
participant 行情中心
APP->股权激励中台服务:查询用户股权激励信息

股权激励中台服务->股权激励数据库:查询用户股权激励信息
股权激励中台服务<-股权激励数据库:return


股权激励中台服务->数据中间件:查询历史行权数量
股权激励中台服务<-数据中间件:return
股权激励中台服务->行情中心:查询股票最新市价
股权激励中台服务<-行情中心:return
股权激励中台服务->股权激励中台服务:计算股权激励价值
APP<-股权激励中台服务:return
```

# 2. 涉及的数据库、Redis、Kafka 等中间件的修改/新增图或者说明

不涉及

# 3. 前后端交互接口信息

## 3.1. 查询用户股权激励信息V3

### 3.1.1. 接口路径

POST /queryUserIncentNewV3

### 3.1.2. 接口入参

无

### 3.1.3. 接口出参

| 名称                 | 类型    | 必选  | 约束 | 说明                                             |
| -------------------- | ------- | ----- | ---- | ------------------------------------------------ |
| hasFirstStockIncent  | boolean | false | none | 是否有第一类限制性股票激励                       |
| hasOptionIncent      | boolean | false | none | 是否有股票期权激励                               |
| hasSecondStockIncent | boolean | false | none | 是否有第二类限制性股票激励                       |
| hasSharePlan         | boolean | false | none | 是否有员工持股计划                               |
| toHide               | boolean | false | none | 是否隐藏(展示为XXXXXXX)，true-隐藏，false-不隐藏 |
| totalMarketValue     | string  | false | none | 总授予股数市价                                   |
| totalNumber          | string  | false | none | 总授予股数                                       |
| totalValue           | string  | false | none | 总价值                                           |
| unlockedNumber       | string  | false | none | 已解锁股数                                       |

# 4. 配置&部署修改信息

不涉及

# 5. 新增技术组件说明

不涉及

# 6. 影响范围

不涉及

# 7. 外部依赖项

不涉及
