title 行权扣税数据同步



participant 股权激励管理端服务

database 股权激励数据库
database 交易业务库
participant 数据中间件
participant 数仓oracle
participant 数仓tidb





group 定时任务：每日00:10:00和下午17:00:00执行
股权激励管理端服务->股权激励数据库:查询所有实际解锁股份（量价）大于0的行权解锁记录

股权激励管理端服务<-股权激励数据库:return
loop 行权解锁记录

股权激励管理端服务->股权激励数据库:查询行权委托记录
股权激励管理端服务<-股权激励数据库:return
股权激励管理端服务->交易业务库:查询行权委托记录
股权激励管理端服务<-交易业务库:return 委托序号
股权激励管理端服务->数据中间件:查询当日委托详情
数据中间件->数仓oracle:查询当日委托详情
数仓oracle->数据中间件:return
股权激励管理端服务<-数据中间件:当日委托详情
股权激励管理端服务->数据中间件:查询历史委托详情
数据中间件->数仓tidb:查询历史委托详情
数仓tidb->数据中间件:return
股权激励管理端服务<-数据中间件:历史委托详情
股权激励管理端服务->股权激励管理端服务:处理行权扣税数据\n
end
股权激励管理端服务->股权激励数据库:保存行权扣税数据\n
end
