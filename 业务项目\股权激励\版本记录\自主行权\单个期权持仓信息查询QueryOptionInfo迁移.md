# 1. 流程图


```plantuml
title 单个期权持仓信息查询迁移 
participant APP
participant 股权激励中台服务
participant 股权激励前置机通讯服务
participant 柜台
APP->股权激励中台服务:查询单个期权持仓信息




股权激励中台服务->股权激励前置机通讯服务:查询可用资金
股权激励前置机通讯服务->柜台:查询302 可用资金
股权激励前置机通讯服务<-柜台:return
股权激励中台服务<-股权激励前置机通讯服务:return
股权激励中台服务->股权激励前置机通讯服务:查询275[410231]-取权证信息
股权激励前置机通讯服务->柜台:查询275[410231]-取权证信息
股权激励前置机通讯服务<-柜台:return
股权激励中台服务<-股权激励前置机通讯服务:return
股权激励中台服务->股权激励前置机通讯服务:查询283 - 最大可行权量
股权激励前置机通讯服务->柜台:查询283 - 最大可行权量
股权激励前置机通讯服务<-柜台:return
股权激励中台服务<-股权激励前置机通讯服务:return
股权激励中台服务->股权激励前置机通讯服务:查询281 - 股权激励人员持仓信息 
股权激励前置机通讯服务->柜台:查询281 - 股权激励人员持仓信息 
股权激励前置机通讯服务<-柜台:return
股权激励中台服务<-股权激励前置机通讯服务:return
APP<-股权激励中台服务:return
```

# 2. 涉及的数据库、Redis、Kafka 等中间件的修改/新增图或者说明

不涉及

# 3. 前后端交互接口信息

## 3.1. 查询单个期权持仓信息

### 3.1.1. 接口路径

POST /queryOptionInfo

### 3.1.2. 接口入参

无

### 3.1.3. 接口出参

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|gfsl|string|false|none||最大可行权数量|
|kyzj|string|false|none||可用资金|
|sfclsds|string|false|none||是否处理所得税(0:不处理,1:处理)|
|xqjg|string|false|none||行权价格|
|xqmc|string|false|none||行权名称|
# 4. 配置&部署修改信息

不涉及

# 5. 新增技术组件说明

不涉及

# 6. 影响范围

不涉及

# 7. 外部依赖项

柜台
