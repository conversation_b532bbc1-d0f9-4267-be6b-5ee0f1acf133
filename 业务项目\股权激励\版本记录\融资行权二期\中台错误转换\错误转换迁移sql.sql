INSERT INTO LCIMS2.ERROR_MESSAGE_CONVERT
(EID, EITIME, EUTIME, ERR_ACTION, ERR_CODE, ORI_MSG, MATCH_RULES, EXTRACT_RULES, DES_MSG, STATUS)
SELECT
    m.EID,
    m.EITIME,
    m.EUTIME,
    m.ERR_ACTION,
    m.ERR_CODE,
    m.ORI_MSG,
    -- 构建MATCH_RULES：处理特殊字符
    CASE
        WHEN m.ORI_MSG IS NOT NULL THEN
            '[{"keyword":"' ||
            REPLACE(
                REPLACE(
                    SUBSTR(m.ORI_MSG, 1, 100),
                    '"', '\"'
                ),
                '''', ''''''
            ) ||
            '"}]'
        ELSE NULL
    END AS MATCH_RULES,
    -- 构建EXTRACT_RULES：处理特殊字符
    CASE
        WHEN m.IS_VARIABLE = '1' AND m.VAR_START IS NOT NULL THEN
            '[{"start":"' ||
            REPLACE(
                REPLACE(
                    m.VAR_START,
                    '"', '\"'
                ),
                '''', ''''''
            ) ||
            '"' ||
            CASE
                WHEN m.VAR_END IS NOT NULL THEN
                    ',"end":"' ||
                    REPLACE(
                        REPLACE(
                            m.VAR_END,
                            '"', '\"'
                        ),
                        '''', ''''''
                    ) ||
                    '"'
                ELSE ''
            END ||
            '}]'
        ELSE NULL
    END AS EXTRACT_RULES,
    m.DES_MSG,
    '1' AS STATUS
FROM LCIMS2.EM_TRADE_MWARE_MSGCONVERT m
WHERE m.ERR_CODE IS NOT NULL;


INSERT INTO LCIMS2.ERROR_MESSAGE_CONVERT
(EID, EITIME, EUTIME, ERR_ACTION, ERR_CODE, ORI_MSG, MATCH_RULES, EXTRACT_RULES, DES_MSG, STATUS)
SELECT
    m.EID,
    m.EITIME,
    m.EUTIME,
    m.ERR_ACTION,
    m.ERR_CODE,
    m.ORI_MSG,
    -- 构建MATCH_RULES：使用KEYWORD字段
    CASE
        WHEN m.KEYWORD IS NOT NULL THEN
            '[{"keyword":"' ||
            REPLACE(
                REPLACE(
                    m.KEYWORD,
                    '"', '\"'
                ),
                '''', ''''''
            ) ||
            '"}]'
        ELSE NULL
    END AS MATCH_RULES,
    -- 构建EXTRACT_RULES：处理特殊字符
    CASE
        WHEN m.IS_VARIABLE = '1' AND m.VAR_START IS NOT NULL THEN
            '[{"start":"' ||
            REPLACE(
                REPLACE(
                    m.VAR_START,
                    '"', '\"'
                ),
                '''', ''''''
            ) ||
            '"' ||
            CASE
                WHEN m.VAR_END IS NOT NULL THEN
                    ',"end":"' ||
                    REPLACE(
                        REPLACE(
                            m.VAR_END,
                            '"', '\"'
                        ),
                        '''', ''''''
                    ) ||
                    '"'
                ELSE ''
            END ||
            '}]'
        ELSE NULL
    END AS EXTRACT_RULES,
    m.DES_MSG,
    '1' AS STATUS
FROM LCIMS2.EM_TRADE_MWARE_MSGCONVERTNEW m
WHERE m.ERR_CODE IS NOT NULL;


