@echo off
setlocal enabledelayedexpansion

REM Set password
set PASSWORD=980306

REM Set 7zip path
set SEVENZIP="D:\Program Files\7-Zip\7z.exe"

REM Check if 7zip exists
if not exist %SEVENZIP% (
    echo Error: Cannot find 7zip program at: %SEVENZIP%
    pause
    exit /b 1
)

echo Starting to add password to zip files in archive directory...
echo Password: %PASSWORD%
echo 7zip path: %SEVENZIP%
echo.

REM Create encrypted files directory
set "ENCRYPTED_DIR=archive_encrypted"
if not exist "%ENCRYPTED_DIR%" mkdir "%ENCRYPTED_DIR%"

REM Process all zip files in archive directory
for /r "归档" %%f in (*.zip) do (
    echo Processing: %%f

    REM Get filename and relative path
    set "FULL_PATH=%%f"
    set "FILENAME=%%~nxf"

    REM Calculate relative path
    call set "REL_PATH=%%FULL_PATH:*归档\=%%"
    set "TARGET_FILE=%ENCRYPTED_DIR%\!REL_PATH!"

    REM Create target directory structure
    for %%d in ("!TARGET_FILE!") do (
        if not exist "%%~dpd" mkdir "%%~dpd" 2>nul
    )

    REM Create temporary directory
    set "TEMP_DIR=temp_!RANDOM!"
    mkdir "!TEMP_DIR!" 2>nul

    REM Extract original file to temporary directory
    %SEVENZIP% x "%%f" -o"!TEMP_DIR!" -y >nul 2>&1

    if !errorlevel! equ 0 (
        REM Recompress with password to new location
        %SEVENZIP% a "!TARGET_FILE!" "!TEMP_DIR!\*" -p%PASSWORD% -mhe=on -y >nul 2>&1

        if !errorlevel! equ 0 (
            echo Success: !FILENAME! to !REL_PATH!
        ) else (
            echo Error: Cannot add password to !FILENAME!
        )
    ) else (
        echo Error: Cannot extract !FILENAME!
    )

    REM Clean up temporary directory
    if exist "!TEMP_DIR!" rmdir /s /q "!TEMP_DIR!" 2>nul
    echo.
)

echo.
echo Processing completed!
echo Original files unchanged, encrypted versions saved in: %ENCRYPTED_DIR%
pause
