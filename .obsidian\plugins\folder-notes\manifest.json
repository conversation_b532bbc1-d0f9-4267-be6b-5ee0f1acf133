{"id": "folder-notes", "name": "Folder notes", "version": "1.7.35", "minAppVersion": "0.15.0", "description": "Create notes within folders that can be accessed without collapsing the folder, similar to the functionality offered in Notion.", "author": "Lost <PERSON>", "authorUrl": "https://github.com/LostPaul", "fundingUrl": "https://ko-fi.com/paul305844", "helpUrl": "https://lostpaul.github.io/obsidian-folder-notes/", "isDesktopOnly": false}