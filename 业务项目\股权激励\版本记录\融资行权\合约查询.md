---
created: 2024-10-15 16:32
updated: 2024-10-29 11:05
---

# 1. 流程图

# 2. 涉及的数据库、Redis、Kafka 等中间件的修改/新增图或者说明

# 3. 前后端交互接口信息

## 3.1. 查询用户当前合约状态为“未了结”的合约

### 3.1.1. 接口路径

POST：contract/queryCurrentUnsettledContracts

### 3.1.2. 接口入参

无

### 3.1.3. 接口出参

| 参数名称         | 参数含义         | 参数类型   | 是否必填 | 备注                                                                                    |
| ---------------- | ---------------- | ---------- | -------- | --------------------------------------------------------------------------------------- |
| sysdate          | 交易日期         | Integer    | 是       | 格式：20140101                                                                          |
| rzsno            | 合约编号         | String     | 是       | 长度64                                                                                  |
| orgid            | 机构编码         | String     | 是       | 长度4                                                                                   |
| custid           | 客户代码         | Long       | 是       |                                                                                         |
| custname         | 客户名称         | String     | 是       | 长度16                                                                                  |
| fundid           | 资金账户         | Long       | 是       |                                                                                         |
| moneytype        | 币种             | String     | 是       | 长度1                                                                                   |
| market           | 市场             | String     | 是       | 长度1                                                                                   |
| seat             | 席位             | String     | 是       | 长度6                                                                                   |
| secuid           | 股东账户         | String     | 是       | 长度10                                                                                  |
| orderid          | 委托序号         | String     | 是       | 长度10                                                                                  |
| stkcode          | 证券代码         | String     | 是       | 长度8                                                                                   |
| stkname          | 证券名称         | String     | 是       | 长度32                                                                                  |
| targetstk        | 标的证券         | String     | 是       | 长度8                                                                                   |
| orderprice       | 委托价格         | BigDecimal | 是       | 精度9,3                                                                                 |
| conqty           | 行权数量         | Long       | 是       |                                                                                         |
| conamt           | 行权金额         | BigDecimal | 是       | 精度19,2                                                                                |
| payconamt        | 已归还本金       | BigDecimal | 是       | 累加                                                                                    |
| payconamt_real   | 实时归还本金     | BigDecimal | 是       | 日间实时累加                                                                            |
| taxamt           | 扣税金额         | BigDecimal | 是       | 精度19,2                                                                                |
| paytaxamt        | 已还扣税金额     | BigDecimal | 是       | 累加                                                                                    |
| paytaxamt_real   | 实时归还扣税金额 | BigDecimal | 是       | 日间实时累加                                                                            |
| intcaldate       | 上次计息日期     | Integer    | 是       | 格式：20140101                                                                          |
| duerate          | 利息年利率       | BigDecimal | 是       | 精度12,8                                                                                |
| dueintr          | 应付利息         | BigDecimal | 是       | 精度19,2                                                                                |
| paydueintr       | 已还利息         | BigDecimal | 是       | 累加                                                                                    |
| paydueintr_real  | 实时归还利息     | BigDecimal | 是       | 日间实时累加                                                                            |
| punirate         | 违约日利率       | BigDecimal | 是       | 精度12,8                                                                                |
| puniintr         | 应付违约金       | BigDecimal | 是       | 精度19,2                                                                                |
| paypuniintr      | 已还违约金       | BigDecimal | 是       | 累加                                                                                    |
| paypuniintr_real | 实时归还违约金   | BigDecimal | 是       | 日间实时累加                                                                            |
| enddate          | 到期日期         | Integer    | 是       | 格式：20140101                                                                          |
| closedate        | 了结日期         | Integer    | 是       | 格式：20140101                                                                          |
| status           | 合约状态         | String     | 是       | 0:未了结<br><br>1:已了结<br><br>2:到期未还<br><br>A:T日生成<br><br>B:作废合约(交收失败) |
| rzdebt           | 客户需偿还负债   | BigDecimal | 是       | 精度19,2                                                                                |
| isplegde         | 是否转质押       | String     | 是       | 长度1                                                                                   |
| targetqty        | 获得标的数量     | Long       | 是       |                                                                                         |
| mctargetqty      | 卖出标的数量     | Long       | 是       |                                                                                         |
| frztargetqty     | 冻结标的数量     | Long       | 是       |                                                                                         |
| mcfrztragetqty   | 卖出冻结标的数量 | Long       | 是       |                                                                                         |
| remark           | 备注             | String     | 是       | 长度64                                                                                  |
| renegestatus     | 违约状态         | String     | 是       | 0：正常, 1：违约                                                                        |

# 4. 配置&部署修改信息

不涉及

# 5. 新增技术组件说明

不涉及

# 6. 影响范围

不涉及

# 7. 外部依赖项

柜台服务
