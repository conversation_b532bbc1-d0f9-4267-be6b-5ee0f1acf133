{"commitMessage": "自动备份: {{date}}", "commitDateFormat": "YYYY-MM-DD HH:mm:ss", "autoSaveInterval": 30, "autoPushInterval": 60, "autoPullInterval": 60, "autoPullOnBoot": true, "disablePush": false, "pullBeforePush": true, "disablePopups": false, "disablePopupsForNoChanges": false, "listChangedFilesInMessageBody": true, "showStatusBar": true, "updateSubmodules": false, "syncMethod": "merge", "customMessageOnAutoBackup": false, "autoBackupAfterFileChange": true, "treeStructure": false, "refreshSourceControl": true, "basePath": "", "differentIntervalCommitAndPush": true, "changedFilesInStatusBar": false, "showedMobileNotice": true, "refreshSourceControlTimer": 7000, "showBranchStatusBar": true, "setLastSaveToLastCommit": false, "submoduleRecurseCheckout": false, "gitDir": "", "showFileMenu": true, "authorInHistoryView": "hide", "dateInHistoryView": true, "diffStyle": "split", "lineAuthor": {"show": false, "followMovement": "inactive", "authorDisplay": "initials", "showCommitHash": false, "dateTimeFormatOptions": "date", "dateTimeFormatCustomString": "YYYY-MM-DD HH:mm", "dateTimeTimezone": "viewer-local", "coloringMaxAge": "1y", "colorNew": {"r": 255, "g": 150, "b": 150}, "colorOld": {"r": 120, "g": 160, "b": 255}, "textColorCss": "var(--text-muted)", "ignoreWhitespace": false, "gutterSpacingFallbackLength": 5, "lastShownAuthorDisplay": "initials", "lastShownDateTimeFormatOptions": "date"}, "autoCommitMessage": "自动备份: {{date}}"}