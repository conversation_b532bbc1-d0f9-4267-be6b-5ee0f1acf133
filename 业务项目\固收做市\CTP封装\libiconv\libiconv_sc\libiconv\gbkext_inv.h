/*
 * Copyright (C) 1999-2001 Free Software Foundation, Inc.
 * This file is part of the GNU LIBICONV Library.
 *
 * The GNU LIBICONV Library is free software; you can redistribute it
 * and/or modify it under the terms of the GNU Library General Public
 * License as published by the Free Software Foundation; either version 2
 * of the License, or (at your option) any later version.
 *
 * The GNU LIBICONV Library is distributed in the hope that it will be
 * useful, but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
 * Library General Public License for more details.
 *
 * You should have received a copy of the GNU Library General Public
 * License along with the GNU LIBICONV Library; see the file COPYING.LIB.
 * If not, write to the Free Software Foundation, Inc., 51 Franklin Street,
 * Fifth Floor, Boston, MA 02110-1301, USA.
 */

/*
 * GBK extensions
 */

static const unsigned short gbkext_inv_2charset[14313] = {
  0xa840, 0xa841, 0xa842, 0xa95c, 0xa843, 0xa844, 0xa845, 0xa846,
  0xa847, 0xa848, 0xa959, 0xa849, 0xa84a, 0xa84b, 0xa84c, 0xa84d,
  0xa84e, 0xa84f, 0xa850, 0xa851, 0xa852, 0xa892, 0xa853, 0xa854,
  0xa855, 0xa856, 0xa857, 0xa858, 0xa859, 0xa85a, 0xa85b, 0xa85c,
  0xa85d, 0xa85e, 0xa85f, 0xa860, 0xa861, 0xa862, 0xa863, 0xa864,
  0xa865, 0xa866, 0xa867, 0xa868, 0xa869, 0xa86a, 0xa86b, 0xa86c,
  0xa86d, 0xa86e, 0xa86f, 0xa870, 0xa871, 0xa872, 0xa873, 0xa874,
  0xa875, 0xa876, 0xa877, 0xa878, 0xa879, 0xa87a, 0xa87b, 0xa87c,
  0xa87d, 0xa87e, 0xa880, 0xa881, 0xa882, 0xa883, 0xa884, 0xa885,
  0xa886, 0xa887, 0xa888, 0xa889, 0xa88a, 0xa88b, 0xa88c, 0xa88d,
  0xa88e, 0xa88f, 0xa890, 0xa891, 0xa965, 0xa996, 0xa893, 0xa894,
  0xa895, 0xa940, 0xa941, 0xa942, 0xa943, 0xa944, 0xa945, 0xa946,
  0xa947, 0xa948, 0xa961, 0xa962, 0xa966, 0xa967, 0xa960, 0xa963,
  0xa964, 0xa95a, 0xa949, 0xa94a, 0xa94b, 0xa94c, 0xa94d, 0xa94e,
  0xa94f, 0xa950, 0xa951, 0xa952, 0xa953, 0xa954, 0x8140, 0x8141,
  0x8142, 0x8143, 0x8144, 0x8145, 0x8146, 0x8147, 0x8148, 0x8149,
  0x814a, 0x814b, 0x814c, 0x814d, 0x814e, 0x814f, 0x8150, 0x8151,
  0x8152, 0x8153, 0x8154, 0x8155, 0x8156, 0x8157, 0x8158, 0x8159,
  0x815a, 0x815b, 0x815c, 0x815d, 0x815e, 0x815f, 0x8160, 0x8161,
  0x8162, 0x8163, 0x8164, 0x8165, 0x8166, 0x8167, 0x8168, 0x8169,
  0x816a, 0x816b, 0x816c, 0x816d, 0x816e, 0x816f, 0x8170, 0x8171,
  0x8172, 0x8173, 0x8174, 0x8175, 0x8176, 0x8177, 0x8178, 0x8179,
  0x817a, 0x817b, 0x817c, 0x817d, 0x817e, 0x8180, 0x8181, 0x8182,
  0x8183, 0x8184, 0x8185, 0x8186, 0x8187, 0x8188, 0x8189, 0x818a,
  0x818b, 0x818c, 0x818d, 0x818e, 0x818f, 0x8190, 0x8191, 0x8192,
  0x8193, 0x8194, 0x8195, 0x8196, 0x8197, 0x8198, 0x8199, 0x819a,
  0x819b, 0x819c, 0x819d, 0x819e, 0x819f, 0x81a0, 0x81a1, 0x81a2,
  0x81a3, 0x81a4, 0x81a5, 0x81a6, 0x81a7, 0x81a8, 0x81a9, 0x81aa,
  0x81ab, 0x81ac, 0x81ad, 0x81ae, 0x81af, 0x81b0, 0x81b1, 0x81b2,
  0x81b3, 0x81b4, 0x81b5, 0x81b6, 0x81b7, 0x81b8, 0x81b9, 0x81ba,
  0x81bb, 0x81bc, 0x81bd, 0x81be, 0x81bf, 0x81c0, 0x81c1, 0x81c2,
  0x81c3, 0x81c4, 0x81c5, 0x81c6, 0x81c7, 0x81c8, 0x81c9, 0x81ca,
  0x81cb, 0x81cc, 0x81cd, 0x81ce, 0x81cf, 0x81d0, 0x81d1, 0x81d2,
  0x81d3, 0x81d4, 0x81d5, 0x81d6, 0x81d7, 0x81d8, 0x81d9, 0x81da,
  0x81db, 0x81dc, 0x81dd, 0x81de, 0x81df, 0x81e0, 0x81e1, 0x81e2,
  0x81e3, 0x81e4, 0x81e5, 0x81e6, 0x81e7, 0x81e8, 0x81e9, 0x81ea,
  0x81eb, 0x81ec, 0x81ed, 0x81ee, 0x81ef, 0x81f0, 0x81f1, 0x81f2,
  0x81f3, 0x81f4, 0x81f5, 0x81f6, 0x81f7, 0x81f8, 0x81f9, 0x81fa,
  0x81fb, 0x81fc, 0x81fd, 0x81fe, 0x8240, 0x8241, 0x8242, 0x8243,
  0x8244, 0x8245, 0x8246, 0x8247, 0x8248, 0x8249, 0x824a, 0x824b,
  0x824c, 0x824d, 0x824e, 0x824f, 0x8250, 0x8251, 0x8252, 0x8253,
  0x8254, 0x8255, 0x8256, 0x8257, 0x8258, 0x8259, 0x825a, 0x825b,
  0x825c, 0x825d, 0x825e, 0x825f, 0x8260, 0x8261, 0x8262, 0x8263,
  0x8264, 0x8265, 0x8266, 0x8267, 0x8268, 0x8269, 0x826a, 0x826b,
  0x826c, 0x826d, 0x826e, 0x826f, 0x8270, 0x8271, 0x8272, 0x8273,
  0x8274, 0x8275, 0x8276, 0x8277, 0x8278, 0x8279, 0x827a, 0x827b,
  0x827c, 0x827d, 0x827e, 0x8280, 0x8281, 0x8282, 0x8283, 0x8284,
  0x8285, 0x8286, 0x8287, 0x8288, 0x8289, 0x828a, 0x828b, 0x828c,
  0x828d, 0x828e, 0x828f, 0x8290, 0x8291, 0x8292, 0x8293, 0x8294,
  0x8295, 0x8296, 0x8297, 0x8298, 0x8299, 0x829a, 0x829b, 0x829c,
  0x829d, 0x829e, 0x829f, 0x82a0, 0x82a1, 0x82a2, 0x82a3, 0x82a4,
  0x82a5, 0x82a6, 0x82a7, 0x82a8, 0x82a9, 0x82aa, 0x82ab, 0x82ac,
  0x82ad, 0x82ae, 0x82af, 0x82b0, 0x82b1, 0x82b2, 0x82b3, 0x82b4,
  0x82b5, 0x82b6, 0x82b7, 0x82b8, 0x82b9, 0x82ba, 0x82bb, 0x82bc,
  0x82bd, 0x82be, 0x82bf, 0x82c0, 0x82c1, 0x82c2, 0x82c3, 0x82c4,
  0x82c5, 0x82c6, 0x82c7, 0x82c8, 0x82c9, 0x82ca, 0x82cb, 0x82cc,
  0x82cd, 0x82ce, 0x82cf, 0x82d0, 0x82d1, 0x82d2, 0x82d3, 0x82d4,
  0x82d5, 0x82d6, 0x82d7, 0x82d8, 0x82d9, 0x82da, 0x82db, 0x82dc,
  0x82dd, 0x82de, 0x82df, 0x82e0, 0x82e1, 0x82e2, 0x82e3, 0x82e4,
  0x82e5, 0x82e6, 0x82e7, 0x82e8, 0x82e9, 0x82ea, 0x82eb, 0x82ec,
  0x82ed, 0x82ee, 0x82ef, 0x82f0, 0x82f1, 0x82f2, 0x82f3, 0x82f4,
  0x82f5, 0x82f6, 0x82f7, 0x82f8, 0x82f9, 0x82fa, 0x82fb, 0x82fc,
  0x82fd, 0x82fe, 0x8340, 0x8341, 0x8342, 0x8343, 0x8344, 0x8345,
  0x8346, 0x8347, 0x8348, 0x8349, 0x834a, 0x834b, 0x834c, 0x834d,
  0x834e, 0x834f, 0x8350, 0x8351, 0x8352, 0x8353, 0x8354, 0x8355,
  0x8356, 0x8357, 0x8358, 0x8359, 0x835a, 0x835b, 0x835c, 0x835d,
  0x835e, 0x835f, 0x8360, 0x8361, 0x8362, 0x8363, 0x8364, 0x8365,
  0x8366, 0x8367, 0x8368, 0x8369, 0x836a, 0x836b, 0x836c, 0x836d,
  0x836e, 0x836f, 0x8370, 0x8371, 0x8372, 0x8373, 0x8374, 0x8375,
  0x8376, 0x8377, 0x8378, 0x8379, 0x837a, 0x837b, 0x837c, 0x837d,
  0x837e, 0x8380, 0x8381, 0x8382, 0x8383, 0x8384, 0x8385, 0x8386,
  0x8387, 0x8388, 0x8389, 0x838a, 0x838b, 0x838c, 0x838d, 0x838e,
  0x838f, 0x8390, 0x8391, 0x8392, 0x8393, 0x8394, 0x8395, 0x8396,
  0x8397, 0x8398, 0x8399, 0x839a, 0x839b, 0x839c, 0x839d, 0x839e,
  0x839f, 0x83a0, 0x83a1, 0x83a2, 0x83a3, 0x83a4, 0x83a5, 0x83a6,
  0x83a7, 0x83a8, 0x83a9, 0x83aa, 0x83ab, 0x83ac, 0x83ad, 0x83ae,
  0x83af, 0x83b0, 0x83b1, 0x83b2, 0x83b3, 0x83b4, 0x83b5, 0x83b6,
  0x83b7, 0x83b8, 0x83b9, 0x83ba, 0x83bb, 0x83bc, 0x83bd, 0x83be,
  0x83bf, 0x83c0, 0x83c1, 0x83c2, 0x83c3, 0x83c4, 0x83c5, 0x83c6,
  0x83c7, 0x83c8, 0x83c9, 0x83ca, 0x83cb, 0x83cc, 0x83cd, 0x83ce,
  0x83cf, 0x83d0, 0x83d1, 0x83d2, 0x83d3, 0x83d4, 0x83d5, 0x83d6,
  0x83d7, 0x83d8, 0x83d9, 0x83da, 0x83db, 0x83dc, 0x83dd, 0x83de,
  0x83df, 0x83e0, 0x83e1, 0x83e2, 0x83e3, 0x83e4, 0x83e5, 0x83e6,
  0x83e7, 0x83e8, 0x83e9, 0x83ea, 0x83eb, 0x83ec, 0x83ed, 0x83ee,
  0x83ef, 0x83f0, 0x83f1, 0x83f2, 0x83f3, 0x83f4, 0x83f5, 0x83f6,
  0x83f7, 0x83f8, 0x83f9, 0x83fa, 0x83fb, 0x83fc, 0x83fd, 0x83fe,
  0x8440, 0x8441, 0x8442, 0x8443, 0x8444, 0x8445, 0x8446, 0x8447,
  0x8448, 0x8449, 0x844a, 0x844b, 0x844c, 0x844d, 0x844e, 0x844f,
  0x8450, 0x8451, 0x8452, 0x8453, 0x8454, 0x8455, 0x8456, 0x8457,
  0x8458, 0x8459, 0x845a, 0x845b, 0x845c, 0x845d, 0x845e, 0x845f,
  0x8460, 0x8461, 0x8462, 0x8463, 0x8464, 0x8465, 0x8466, 0x8467,
  0x8468, 0x8469, 0x846a, 0x846b, 0x846c, 0x846d, 0x846e, 0x846f,
  0x8470, 0x8471, 0x8472, 0x8473, 0x8474, 0x8475, 0x8476, 0x8477,
  0x8478, 0x8479, 0x847a, 0x847b, 0x847c, 0x847d, 0x847e, 0x8480,
  0x8481, 0x8482, 0x8483, 0x8484, 0x8485, 0x8486, 0x8487, 0x8488,
  0x8489, 0x848a, 0x848b, 0x848c, 0x848d, 0x848e, 0x848f, 0x8490,
  0x8491, 0x8492, 0x8493, 0x8494, 0x8495, 0x8496, 0x8497, 0x8498,
  0x8499, 0x849a, 0x849b, 0x849c, 0x849d, 0x849e, 0x849f, 0x84a0,
  0x84a1, 0x84a2, 0x84a3, 0x84a4, 0x84a5, 0x84a6, 0x84a7, 0x84a8,
  0x84a9, 0x84aa, 0x84ab, 0x84ac, 0x84ad, 0x84ae, 0x84af, 0x84b0,
  0x84b1, 0x84b2, 0x84b3, 0x84b4, 0x84b5, 0x84b6, 0x84b7, 0x84b8,
  0x84b9, 0x84ba, 0x84bb, 0x84bc, 0x84bd, 0x84be, 0x84bf, 0x84c0,
  0x84c1, 0x84c2, 0x84c3, 0x84c4, 0x84c5, 0x84c6, 0x84c7, 0x84c8,
  0x84c9, 0x84ca, 0x84cb, 0x84cc, 0x84cd, 0x84ce, 0x84cf, 0x84d0,
  0x84d1, 0x84d2, 0x84d3, 0x84d4, 0x84d5, 0x84d6, 0x84d7, 0x84d8,
  0x84d9, 0x84da, 0x84db, 0x84dc, 0x84dd, 0x84de, 0x84df, 0x84e0,
  0x84e1, 0x84e2, 0x84e3, 0x84e4, 0x84e5, 0x84e6, 0x84e7, 0x84e8,
  0x84e9, 0x84ea, 0x84eb, 0x84ec, 0x84ed, 0x84ee, 0x84ef, 0x84f0,
  0x84f1, 0x84f2, 0x84f3, 0x84f4, 0x84f5, 0x84f6, 0x84f7, 0x84f8,
  0x84f9, 0x84fa, 0x84fb, 0x84fc, 0x84fd, 0x84fe, 0x8540, 0x8541,
  0x8542, 0x8543, 0x8544, 0x8545, 0x8546, 0x8547, 0x8548, 0x8549,
  0x854a, 0x854b, 0x854c, 0x854d, 0x854e, 0x854f, 0x8550, 0x8551,
  0x8552, 0x8553, 0x8554, 0x8555, 0x8556, 0x8557, 0x8558, 0x8559,
  0x855a, 0x855b, 0x855c, 0x855d, 0x855e, 0x855f, 0x8560, 0x8561,
  0x8562, 0x8563, 0x8564, 0x8565, 0x8566, 0x8567, 0x8568, 0x8569,
  0x856a, 0x856b, 0x856c, 0x856d, 0x856e, 0x856f, 0x8570, 0x8571,
  0x8572, 0x8573, 0x8574, 0x8575, 0x8576, 0x8577, 0x8578, 0x8579,
  0x857a, 0x857b, 0x857c, 0x857d, 0x857e, 0x8580, 0x8581, 0x8582,
  0x8583, 0x8584, 0x8585, 0x8586, 0x8587, 0x8588, 0x8589, 0x858a,
  0x858b, 0x858c, 0x858d, 0x858e, 0x858f, 0x8590, 0x8591, 0x8592,
  0x8593, 0x8594, 0x8595, 0x8596, 0x8597, 0x8598, 0x8599, 0x859a,
  0x859b, 0x859c, 0x859d, 0x859e, 0x859f, 0x85a0, 0x85a1, 0x85a2,
  0x85a3, 0x85a4, 0x85a5, 0x85a6, 0x85a7, 0x85a8, 0x85a9, 0x85aa,
  0x85ab, 0x85ac, 0x85ad, 0x85ae, 0x85af, 0x85b0, 0x85b1, 0x85b2,
  0x85b3, 0x85b4, 0x85b5, 0x85b6, 0x85b7, 0x85b8, 0x85b9, 0x85ba,
  0x85bb, 0x85bc, 0x85bd, 0x85be, 0x85bf, 0x85c0, 0x85c1, 0x85c2,
  0x85c3, 0x85c4, 0x85c5, 0x85c6, 0x85c7, 0x85c8, 0x85c9, 0x85ca,
  0x85cb, 0x85cc, 0x85cd, 0x85ce, 0x85cf, 0x85d0, 0x85d1, 0x85d2,
  0x85d3, 0x85d4, 0x85d5, 0x85d6, 0x85d7, 0x85d8, 0x85d9, 0x85da,
  0x85db, 0x85dc, 0x85dd, 0x85de, 0x85df, 0x85e0, 0x85e1, 0x85e2,
  0x85e3, 0x85e4, 0x85e5, 0x85e6, 0x85e7, 0x85e8, 0x85e9, 0x85ea,
  0x85eb, 0x85ec, 0x85ed, 0x85ee, 0x85ef, 0x85f0, 0x85f1, 0x85f2,
  0x85f3, 0x85f4, 0x85f5, 0x85f6, 0x85f7, 0x85f8, 0x85f9, 0x85fa,
  0x85fb, 0x85fc, 0x85fd, 0x85fe, 0x8640, 0x8641, 0x8642, 0x8643,
  0x8644, 0x8645, 0x8646, 0x8647, 0x8648, 0x8649, 0x864a, 0x864b,
  0x864c, 0x864d, 0x864e, 0x864f, 0x8650, 0x8651, 0x8652, 0x8653,
  0x8654, 0x8655, 0x8656, 0x8657, 0x8658, 0x8659, 0x865a, 0x865b,
  0x865c, 0x865d, 0x865e, 0x865f, 0x8660, 0x8661, 0x8662, 0x8663,
  0x8664, 0x8665, 0x8666, 0x8667, 0x8668, 0x8669, 0x866a, 0x866b,
  0x866c, 0x866d, 0x866e, 0x866f, 0x8670, 0x8671, 0x8672, 0x8673,
  0x8674, 0x8675, 0x8676, 0x8677, 0x8678, 0x8679, 0x867a, 0x867b,
  0x867c, 0x867d, 0x867e, 0x8680, 0x8681, 0x8682, 0x8683, 0x8684,
  0x8685, 0x8686, 0x8687, 0x8688, 0x8689, 0x868a, 0x868b, 0x868c,
  0x868d, 0x868e, 0x868f, 0x8690, 0x8691, 0x8692, 0x8693, 0x8694,
  0x8695, 0x8696, 0x8697, 0x8698, 0x8699, 0x869a, 0x869b, 0x869c,
  0x869d, 0x869e, 0x869f, 0x86a0, 0x86a1, 0x86a2, 0x86a3, 0x86a4,
  0x86a5, 0x86a6, 0x86a7, 0x86a8, 0x86a9, 0x86aa, 0x86ab, 0x86ac,
  0x86ad, 0x86ae, 0x86af, 0x86b0, 0x86b1, 0x86b2, 0x86b3, 0x86b4,
  0x86b5, 0x86b6, 0x86b7, 0x86b8, 0x86b9, 0x86ba, 0x86bb, 0x86bc,
  0x86bd, 0x86be, 0x86bf, 0x86c0, 0x86c1, 0x86c2, 0x86c3, 0x86c4,
  0x86c5, 0x86c6, 0x86c7, 0x86c8, 0x86c9, 0x86ca, 0x86cb, 0x86cc,
  0x86cd, 0x86ce, 0x86cf, 0x86d0, 0x86d1, 0x86d2, 0x86d3, 0x86d4,
  0x86d5, 0x86d6, 0x86d7, 0x86d8, 0x86d9, 0x86da, 0x86db, 0x86dc,
  0x86dd, 0x86de, 0x86df, 0x86e0, 0x86e1, 0x86e2, 0x86e3, 0x86e4,
  0x86e5, 0x86e6, 0x86e7, 0x86e8, 0x86e9, 0x86ea, 0x86eb, 0x86ec,
  0x86ed, 0x86ee, 0x86ef, 0x86f0, 0x86f1, 0x86f2, 0x86f3, 0x86f4,
  0x86f5, 0x86f6, 0x86f7, 0x86f8, 0x86f9, 0x86fa, 0x86fb, 0x86fc,
  0x86fd, 0x86fe, 0x8740, 0x8741, 0x8742, 0x8743, 0x8744, 0x8745,
  0x8746, 0x8747, 0x8748, 0x8749, 0x874a, 0x874b, 0x874c, 0x874d,
  0x874e, 0x874f, 0x8750, 0x8751, 0x8752, 0x8753, 0x8754, 0x8755,
  0x8756, 0x8757, 0x8758, 0x8759, 0x875a, 0x875b, 0x875c, 0x875d,
  0x875e, 0x875f, 0x8760, 0x8761, 0x8762, 0x8763, 0x8764, 0x8765,
  0x8766, 0x8767, 0x8768, 0x8769, 0x876a, 0x876b, 0x876c, 0x876d,
  0x876e, 0x876f, 0x8770, 0x8771, 0x8772, 0x8773, 0x8774, 0x8775,
  0x8776, 0x8777, 0x8778, 0x8779, 0x877a, 0x877b, 0x877c, 0x877d,
  0x877e, 0x8780, 0x8781, 0x8782, 0x8783, 0x8784, 0x8785, 0x8786,
  0x8787, 0x8788, 0x8789, 0x878a, 0x878b, 0x878c, 0x878d, 0x878e,
  0x878f, 0x8790, 0x8791, 0x8792, 0x8793, 0x8794, 0x8795, 0x8796,
  0x8797, 0x8798, 0x8799, 0x879a, 0x879b, 0x879c, 0x879d, 0x879e,
  0x879f, 0x87a0, 0x87a1, 0x87a2, 0x87a3, 0x87a4, 0x87a5, 0x87a6,
  0x87a7, 0x87a8, 0x87a9, 0x87aa, 0x87ab, 0x87ac, 0x87ad, 0x87ae,
  0x87af, 0x87b0, 0x87b1, 0x87b2, 0x87b3, 0x87b4, 0x87b5, 0x87b6,
  0x87b7, 0x87b8, 0x87b9, 0x87ba, 0x87bb, 0x87bc, 0x87bd, 0x87be,
  0x87bf, 0x87c0, 0x87c1, 0x87c2, 0x87c3, 0x87c4, 0x87c5, 0x87c6,
  0x87c7, 0x87c8, 0x87c9, 0x87ca, 0x87cb, 0x87cc, 0x87cd, 0x87ce,
  0x87cf, 0x87d0, 0x87d1, 0x87d2, 0x87d3, 0x87d4, 0x87d5, 0x87d6,
  0x87d7, 0x87d8, 0x87d9, 0x87da, 0x87db, 0x87dc, 0x87dd, 0x87de,
  0x87df, 0x87e0, 0x87e1, 0x87e2, 0x87e3, 0x87e4, 0x87e5, 0x87e6,
  0x87e7, 0x87e8, 0x87e9, 0x87ea, 0x87eb, 0x87ec, 0x87ed, 0x87ee,
  0x87ef, 0x87f0, 0x87f1, 0x87f2, 0x87f3, 0x87f4, 0x87f5, 0x87f6,
  0x87f7, 0x87f8, 0x87f9, 0x87fa, 0x87fb, 0x87fc, 0x87fd, 0x87fe,
  0x8840, 0x8841, 0x8842, 0x8843, 0x8844, 0x8845, 0x8846, 0x8847,
  0x8848, 0x8849, 0x884a, 0x884b, 0x884c, 0x884d, 0x884e, 0x884f,
  0x8850, 0x8851, 0x8852, 0x8853, 0x8854, 0x8855, 0x8856, 0x8857,
  0x8858, 0x8859, 0x885a, 0x885b, 0x885c, 0x885d, 0x885e, 0x885f,
  0x8860, 0x8861, 0x8862, 0x8863, 0x8864, 0x8865, 0x8866, 0x8867,
  0x8868, 0x8869, 0x886a, 0x886b, 0x886c, 0x886d, 0x886e, 0x886f,
  0x8870, 0x8871, 0x8872, 0x8873, 0x8874, 0x8875, 0x8876, 0x8877,
  0x8878, 0x8879, 0x887a, 0x887b, 0x887c, 0x887d, 0x887e, 0x8880,
  0x8881, 0x8882, 0x8883, 0x8884, 0x8885, 0x8886, 0x8887, 0x8888,
  0x8889, 0x888a, 0x888b, 0x888c, 0x888d, 0x888e, 0x888f, 0x8890,
  0x8891, 0x8892, 0x8893, 0x8894, 0x8895, 0x8896, 0x8897, 0x8898,
  0x8899, 0x889a, 0x889b, 0x889c, 0x889d, 0x889e, 0x889f, 0x88a0,
  0x88a1, 0x88a2, 0x88a3, 0x88a4, 0x88a5, 0x88a6, 0x88a7, 0x88a8,
  0x88a9, 0x88aa, 0x88ab, 0x88ac, 0x88ad, 0x88ae, 0x88af, 0x88b0,
  0x88b1, 0x88b2, 0x88b3, 0x88b4, 0x88b5, 0x88b6, 0x88b7, 0x88b8,
  0x88b9, 0x88ba, 0x88bb, 0x88bc, 0x88bd, 0x88be, 0x88bf, 0x88c0,
  0x88c1, 0x88c2, 0x88c3, 0x88c4, 0x88c5, 0x88c6, 0x88c7, 0x88c8,
  0x88c9, 0x88ca, 0x88cb, 0x88cc, 0x88cd, 0x88ce, 0x88cf, 0x88d0,
  0x88d1, 0x88d2, 0x88d3, 0x88d4, 0x88d5, 0x88d6, 0x88d7, 0x88d8,
  0x88d9, 0x88da, 0x88db, 0x88dc, 0x88dd, 0x88de, 0x88df, 0x88e0,
  0x88e1, 0x88e2, 0x88e3, 0x88e4, 0x88e5, 0x88e6, 0x88e7, 0x88e8,
  0x88e9, 0x88ea, 0x88eb, 0x88ec, 0x88ed, 0x88ee, 0x88ef, 0x88f0,
  0x88f1, 0x88f2, 0x88f3, 0x88f4, 0x88f5, 0x88f6, 0x88f7, 0x88f8,
  0x88f9, 0x88fa, 0x88fb, 0x88fc, 0x88fd, 0x88fe, 0x8940, 0x8941,
  0x8942, 0x8943, 0x8944, 0x8945, 0x8946, 0x8947, 0x8948, 0x8949,
  0x894a, 0x894b, 0x894c, 0x894d, 0x894e, 0x894f, 0x8950, 0x8951,
  0x8952, 0x8953, 0x8954, 0x8955, 0x8956, 0x8957, 0x8958, 0x8959,
  0x895a, 0x895b, 0x895c, 0x895d, 0x895e, 0x895f, 0x8960, 0x8961,
  0x8962, 0x8963, 0x8964, 0x8965, 0x8966, 0x8967, 0x8968, 0x8969,
  0x896a, 0x896b, 0x896c, 0x896d, 0x896e, 0x896f, 0x8970, 0x8971,
  0x8972, 0x8973, 0x8974, 0x8975, 0x8976, 0x8977, 0x8978, 0x8979,
  0x897a, 0x897b, 0x897c, 0x897d, 0x897e, 0x8980, 0x8981, 0x8982,
  0x8983, 0x8984, 0x8985, 0x8986, 0x8987, 0x8988, 0x8989, 0x898a,
  0x898b, 0x898c, 0x898d, 0x898e, 0x898f, 0x8990, 0x8991, 0x8992,
  0x8993, 0x8994, 0x8995, 0x8996, 0x8997, 0x8998, 0x8999, 0x899a,
  0x899b, 0x899c, 0x899d, 0x899e, 0x899f, 0x89a0, 0x89a1, 0x89a2,
  0x89a3, 0x89a4, 0x89a5, 0x89a6, 0x89a7, 0x89a8, 0x89a9, 0x89aa,
  0x89ab, 0x89ac, 0x89ad, 0x89ae, 0x89af, 0x89b0, 0x89b1, 0x89b2,
  0x89b3, 0x89b4, 0x89b5, 0x89b6, 0x89b7, 0x89b8, 0x89b9, 0x89ba,
  0x89bb, 0x89bc, 0x89bd, 0x89be, 0x89bf, 0x89c0, 0x89c1, 0x89c2,
  0x89c3, 0x89c4, 0x89c5, 0x89c6, 0x89c7, 0x89c8, 0x89c9, 0x89ca,
  0x89cb, 0x89cc, 0x89cd, 0x89ce, 0x89cf, 0x89d0, 0x89d1, 0x89d2,
  0x89d3, 0x89d4, 0x89d5, 0x89d6, 0x89d7, 0x89d8, 0x89d9, 0x89da,
  0x89db, 0x89dc, 0x89dd, 0x89de, 0x89df, 0x89e0, 0x89e1, 0x89e2,
  0x89e3, 0x89e4, 0x89e5, 0x89e6, 0x89e7, 0x89e8, 0x89e9, 0x89ea,
  0x89eb, 0x89ec, 0x89ed, 0x89ee, 0x89ef, 0x89f0, 0x89f1, 0x89f2,
  0x89f3, 0x89f4, 0x89f5, 0x89f6, 0x89f7, 0x89f8, 0x89f9, 0x89fa,
  0x89fb, 0x89fc, 0x89fd, 0x89fe, 0x8a40, 0x8a41, 0x8a42, 0x8a43,
  0x8a44, 0x8a45, 0x8a46, 0x8a47, 0x8a48, 0x8a49, 0x8a4a, 0x8a4b,
  0x8a4c, 0x8a4d, 0x8a4e, 0x8a4f, 0x8a50, 0x8a51, 0x8a52, 0x8a53,
  0x8a54, 0x8a55, 0x8a56, 0x8a57, 0x8a58, 0x8a59, 0x8a5a, 0x8a5b,
  0x8a5c, 0x8a5d, 0x8a5e, 0x8a5f, 0x8a60, 0x8a61, 0x8a62, 0x8a63,
  0x8a64, 0x8a65, 0x8a66, 0x8a67, 0x8a68, 0x8a69, 0x8a6a, 0x8a6b,
  0x8a6c, 0x8a6d, 0x8a6e, 0x8a6f, 0x8a70, 0x8a71, 0x8a72, 0x8a73,
  0x8a74, 0x8a75, 0x8a76, 0x8a77, 0x8a78, 0x8a79, 0x8a7a, 0x8a7b,
  0x8a7c, 0x8a7d, 0x8a7e, 0x8a80, 0x8a81, 0x8a82, 0x8a83, 0x8a84,
  0x8a85, 0x8a86, 0x8a87, 0x8a88, 0x8a89, 0x8a8a, 0x8a8b, 0x8a8c,
  0x8a8d, 0x8a8e, 0x8a8f, 0x8a90, 0x8a91, 0x8a92, 0x8a93, 0x8a94,
  0x8a95, 0x8a96, 0x8a97, 0x8a98, 0x8a99, 0x8a9a, 0x8a9b, 0x8a9c,
  0x8a9d, 0x8a9e, 0x8a9f, 0x8aa0, 0x8aa1, 0x8aa2, 0x8aa3, 0x8aa4,
  0x8aa5, 0x8aa6, 0x8aa7, 0x8aa8, 0x8aa9, 0x8aaa, 0x8aab, 0x8aac,
  0x8aad, 0x8aae, 0x8aaf, 0x8ab0, 0x8ab1, 0x8ab2, 0x8ab3, 0x8ab4,
  0x8ab5, 0x8ab6, 0x8ab7, 0x8ab8, 0x8ab9, 0x8aba, 0x8abb, 0x8abc,
  0x8abd, 0x8abe, 0x8abf, 0x8ac0, 0x8ac1, 0x8ac2, 0x8ac3, 0x8ac4,
  0x8ac5, 0x8ac6, 0x8ac7, 0x8ac8, 0x8ac9, 0x8aca, 0x8acb, 0x8acc,
  0x8acd, 0x8ace, 0x8acf, 0x8ad0, 0x8ad1, 0x8ad2, 0x8ad3, 0x8ad4,
  0x8ad5, 0x8ad6, 0x8ad7, 0x8ad8, 0x8ad9, 0x8ada, 0x8adb, 0x8adc,
  0x8add, 0x8ade, 0x8adf, 0x8ae0, 0x8ae1, 0x8ae2, 0x8ae3, 0x8ae4,
  0x8ae5, 0x8ae6, 0x8ae7, 0x8ae8, 0x8ae9, 0x8aea, 0x8aeb, 0x8aec,
  0x8aed, 0x8aee, 0x8aef, 0x8af0, 0x8af1, 0x8af2, 0x8af3, 0x8af4,
  0x8af5, 0x8af6, 0x8af7, 0x8af8, 0x8af9, 0x8afa, 0x8afb, 0x8afc,
  0x8afd, 0x8afe, 0x8b40, 0x8b41, 0x8b42, 0x8b43, 0x8b44, 0x8b45,
  0x8b46, 0x8b47, 0x8b48, 0x8b49, 0x8b4a, 0x8b4b, 0x8b4c, 0x8b4d,
  0x8b4e, 0x8b4f, 0x8b50, 0x8b51, 0x8b52, 0x8b53, 0x8b54, 0x8b55,
  0x8b56, 0x8b57, 0x8b58, 0x8b59, 0x8b5a, 0x8b5b, 0x8b5c, 0x8b5d,
  0x8b5e, 0x8b5f, 0x8b60, 0x8b61, 0x8b62, 0x8b63, 0x8b64, 0x8b65,
  0x8b66, 0x8b67, 0x8b68, 0x8b69, 0x8b6a, 0x8b6b, 0x8b6c, 0x8b6d,
  0x8b6e, 0x8b6f, 0x8b70, 0x8b71, 0x8b72, 0x8b73, 0x8b74, 0x8b75,
  0x8b76, 0x8b77, 0x8b78, 0x8b79, 0x8b7a, 0x8b7b, 0x8b7c, 0x8b7d,
  0x8b7e, 0x8b80, 0x8b81, 0x8b82, 0x8b83, 0x8b84, 0x8b85, 0x8b86,
  0x8b87, 0x8b88, 0x8b89, 0x8b8a, 0x8b8b, 0x8b8c, 0x8b8d, 0x8b8e,
  0x8b8f, 0x8b90, 0x8b91, 0x8b92, 0x8b93, 0x8b94, 0x8b95, 0x8b96,
  0x8b97, 0x8b98, 0x8b99, 0x8b9a, 0x8b9b, 0x8b9c, 0x8b9d, 0x8b9e,
  0x8b9f, 0x8ba0, 0x8ba1, 0x8ba2, 0x8ba3, 0x8ba4, 0x8ba5, 0x8ba6,
  0x8ba7, 0x8ba8, 0x8ba9, 0x8baa, 0x8bab, 0x8bac, 0x8bad, 0x8bae,
  0x8baf, 0x8bb0, 0x8bb1, 0x8bb2, 0x8bb3, 0x8bb4, 0x8bb5, 0x8bb6,
  0x8bb7, 0x8bb8, 0x8bb9, 0x8bba, 0x8bbb, 0x8bbc, 0x8bbd, 0x8bbe,
  0x8bbf, 0x8bc0, 0x8bc1, 0x8bc2, 0x8bc3, 0x8bc4, 0x8bc5, 0x8bc6,
  0x8bc7, 0x8bc8, 0x8bc9, 0x8bca, 0x8bcb, 0x8bcc, 0x8bcd, 0x8bce,
  0x8bcf, 0x8bd0, 0x8bd1, 0x8bd2, 0x8bd3, 0x8bd4, 0x8bd5, 0x8bd6,
  0x8bd7, 0x8bd8, 0x8bd9, 0x8bda, 0x8bdb, 0x8bdc, 0x8bdd, 0x8bde,
  0x8bdf, 0x8be0, 0x8be1, 0x8be2, 0x8be3, 0x8be4, 0x8be5, 0x8be6,
  0x8be7, 0x8be8, 0x8be9, 0x8bea, 0x8beb, 0x8bec, 0x8bed, 0x8bee,
  0x8bef, 0x8bf0, 0x8bf1, 0x8bf2, 0x8bf3, 0x8bf4, 0x8bf5, 0x8bf6,
  0x8bf7, 0x8bf8, 0x8bf9, 0x8bfa, 0x8bfb, 0x8bfc, 0x8bfd, 0x8bfe,
  0x8c40, 0x8c41, 0x8c42, 0x8c43, 0x8c44, 0x8c45, 0x8c46, 0x8c47,
  0x8c48, 0x8c49, 0x8c4a, 0x8c4b, 0x8c4c, 0x8c4d, 0x8c4e, 0x8c4f,
  0x8c50, 0x8c51, 0x8c52, 0x8c53, 0x8c54, 0x8c55, 0x8c56, 0x8c57,
  0x8c58, 0x8c59, 0x8c5a, 0x8c5b, 0x8c5c, 0x8c5d, 0x8c5e, 0x8c5f,
  0x8c60, 0x8c61, 0x8c62, 0x8c63, 0x8c64, 0x8c65, 0x8c66, 0x8c67,
  0x8c68, 0x8c69, 0x8c6a, 0x8c6b, 0x8c6c, 0x8c6d, 0x8c6e, 0x8c6f,
  0x8c70, 0x8c71, 0x8c72, 0x8c73, 0x8c74, 0x8c75, 0x8c76, 0x8c77,
  0x8c78, 0x8c79, 0x8c7a, 0x8c7b, 0x8c7c, 0x8c7d, 0x8c7e, 0x8c80,
  0x8c81, 0x8c82, 0x8c83, 0x8c84, 0x8c85, 0x8c86, 0x8c87, 0x8c88,
  0x8c89, 0x8c8a, 0x8c8b, 0x8c8c, 0x8c8d, 0x8c8e, 0x8c8f, 0x8c90,
  0x8c91, 0x8c92, 0x8c93, 0x8c94, 0x8c95, 0x8c96, 0x8c97, 0x8c98,
  0x8c99, 0x8c9a, 0x8c9b, 0x8c9c, 0x8c9d, 0x8c9e, 0x8c9f, 0x8ca0,
  0x8ca1, 0x8ca2, 0x8ca3, 0x8ca4, 0x8ca5, 0x8ca6, 0x8ca7, 0x8ca8,
  0x8ca9, 0x8caa, 0x8cab, 0x8cac, 0x8cad, 0x8cae, 0x8caf, 0x8cb0,
  0x8cb1, 0x8cb2, 0x8cb3, 0x8cb4, 0x8cb5, 0x8cb6, 0x8cb7, 0x8cb8,
  0x8cb9, 0x8cba, 0x8cbb, 0x8cbc, 0x8cbd, 0x8cbe, 0x8cbf, 0x8cc0,
  0x8cc1, 0x8cc2, 0x8cc3, 0x8cc4, 0x8cc5, 0x8cc6, 0x8cc7, 0x8cc8,
  0x8cc9, 0x8cca, 0x8ccb, 0x8ccc, 0x8ccd, 0x8cce, 0x8ccf, 0x8cd0,
  0x8cd1, 0x8cd2, 0x8cd3, 0x8cd4, 0x8cd5, 0x8cd6, 0x8cd7, 0x8cd8,
  0x8cd9, 0x8cda, 0x8cdb, 0x8cdc, 0x8cdd, 0x8cde, 0x8cdf, 0x8ce0,
  0x8ce1, 0x8ce2, 0x8ce3, 0x8ce4, 0x8ce5, 0x8ce6, 0x8ce7, 0x8ce8,
  0x8ce9, 0x8cea, 0x8ceb, 0x8cec, 0x8ced, 0x8cee, 0x8cef, 0x8cf0,
  0x8cf1, 0x8cf2, 0x8cf3, 0x8cf4, 0x8cf5, 0x8cf6, 0x8cf7, 0x8cf8,
  0x8cf9, 0x8cfa, 0x8cfb, 0x8cfc, 0x8cfd, 0x8cfe, 0x8d40, 0x8d41,
  0x8d42, 0x8d43, 0x8d44, 0x8d45, 0x8d46, 0x8d47, 0x8d48, 0x8d49,
  0x8d4a, 0x8d4b, 0x8d4c, 0x8d4d, 0x8d4e, 0x8d4f, 0x8d50, 0x8d51,
  0x8d52, 0x8d53, 0x8d54, 0x8d55, 0x8d56, 0x8d57, 0x8d58, 0x8d59,
  0x8d5a, 0x8d5b, 0x8d5c, 0x8d5d, 0x8d5e, 0x8d5f, 0x8d60, 0x8d61,
  0x8d62, 0x8d63, 0x8d64, 0x8d65, 0x8d66, 0x8d67, 0x8d68, 0x8d69,
  0x8d6a, 0x8d6b, 0x8d6c, 0x8d6d, 0x8d6e, 0x8d6f, 0x8d70, 0x8d71,
  0x8d72, 0x8d73, 0x8d74, 0x8d75, 0x8d76, 0x8d77, 0x8d78, 0x8d79,
  0x8d7a, 0x8d7b, 0x8d7c, 0x8d7d, 0x8d7e, 0x8d80, 0x8d81, 0x8d82,
  0x8d83, 0x8d84, 0x8d85, 0x8d86, 0x8d87, 0x8d88, 0x8d89, 0x8d8a,
  0x8d8b, 0x8d8c, 0x8d8d, 0x8d8e, 0x8d8f, 0x8d90, 0x8d91, 0x8d92,
  0x8d93, 0x8d94, 0x8d95, 0x8d96, 0x8d97, 0x8d98, 0x8d99, 0x8d9a,
  0x8d9b, 0x8d9c, 0x8d9d, 0x8d9e, 0x8d9f, 0x8da0, 0x8da1, 0x8da2,
  0x8da3, 0x8da4, 0x8da5, 0x8da6, 0x8da7, 0x8da8, 0x8da9, 0x8daa,
  0x8dab, 0x8dac, 0x8dad, 0x8dae, 0x8daf, 0x8db0, 0x8db1, 0x8db2,
  0x8db3, 0x8db4, 0x8db5, 0x8db6, 0x8db7, 0x8db8, 0x8db9, 0x8dba,
  0x8dbb, 0x8dbc, 0x8dbd, 0x8dbe, 0x8dbf, 0x8dc0, 0x8dc1, 0x8dc2,
  0x8dc3, 0x8dc4, 0x8dc5, 0x8dc6, 0x8dc7, 0x8dc8, 0x8dc9, 0x8dca,
  0x8dcb, 0x8dcc, 0x8dcd, 0x8dce, 0x8dcf, 0x8dd0, 0x8dd1, 0x8dd2,
  0x8dd3, 0x8dd4, 0x8dd5, 0x8dd6, 0x8dd7, 0x8dd8, 0x8dd9, 0x8dda,
  0x8ddb, 0x8ddc, 0x8ddd, 0x8dde, 0x8ddf, 0x8de0, 0x8de1, 0x8de2,
  0x8de3, 0x8de4, 0x8de5, 0x8de6, 0x8de7, 0x8de8, 0x8de9, 0x8dea,
  0x8deb, 0x8dec, 0x8ded, 0x8dee, 0x8def, 0x8df0, 0x8df1, 0x8df2,
  0x8df3, 0x8df4, 0x8df5, 0x8df6, 0x8df7, 0x8df8, 0x8df9, 0x8dfa,
  0x8dfb, 0x8dfc, 0x8dfd, 0x8dfe, 0x8e40, 0x8e41, 0x8e42, 0x8e43,
  0x8e44, 0x8e45, 0x8e46, 0x8e47, 0x8e48, 0x8e49, 0x8e4a, 0x8e4b,
  0x8e4c, 0x8e4d, 0x8e4e, 0x8e4f, 0x8e50, 0x8e51, 0x8e52, 0x8e53,
  0x8e54, 0x8e55, 0x8e56, 0x8e57, 0x8e58, 0x8e59, 0x8e5a, 0x8e5b,
  0x8e5c, 0x8e5d, 0x8e5e, 0x8e5f, 0x8e60, 0x8e61, 0x8e62, 0x8e63,
  0x8e64, 0x8e65, 0x8e66, 0x8e67, 0x8e68, 0x8e69, 0x8e6a, 0x8e6b,
  0x8e6c, 0x8e6d, 0x8e6e, 0x8e6f, 0x8e70, 0x8e71, 0x8e72, 0x8e73,
  0x8e74, 0x8e75, 0x8e76, 0x8e77, 0x8e78, 0x8e79, 0x8e7a, 0x8e7b,
  0x8e7c, 0x8e7d, 0x8e7e, 0x8e80, 0x8e81, 0x8e82, 0x8e83, 0x8e84,
  0x8e85, 0x8e86, 0x8e87, 0x8e88, 0x8e89, 0x8e8a, 0x8e8b, 0x8e8c,
  0x8e8d, 0x8e8e, 0x8e8f, 0x8e90, 0x8e91, 0x8e92, 0x8e93, 0x8e94,
  0x8e95, 0x8e96, 0x8e97, 0x8e98, 0x8e99, 0x8e9a, 0x8e9b, 0x8e9c,
  0x8e9d, 0x8e9e, 0x8e9f, 0x8ea0, 0x8ea1, 0x8ea2, 0x8ea3, 0x8ea4,
  0x8ea5, 0x8ea6, 0x8ea7, 0x8ea8, 0x8ea9, 0x8eaa, 0x8eab, 0x8eac,
  0x8ead, 0x8eae, 0x8eaf, 0x8eb0, 0x8eb1, 0x8eb2, 0x8eb3, 0x8eb4,
  0x8eb5, 0x8eb6, 0x8eb7, 0x8eb8, 0x8eb9, 0x8eba, 0x8ebb, 0x8ebc,
  0x8ebd, 0x8ebe, 0x8ebf, 0x8ec0, 0x8ec1, 0x8ec2, 0x8ec3, 0x8ec4,
  0x8ec5, 0x8ec6, 0x8ec7, 0x8ec8, 0x8ec9, 0x8eca, 0x8ecb, 0x8ecc,
  0x8ecd, 0x8ece, 0x8ecf, 0x8ed0, 0x8ed1, 0x8ed2, 0x8ed3, 0x8ed4,
  0x8ed5, 0x8ed6, 0x8ed7, 0x8ed8, 0x8ed9, 0x8eda, 0x8edb, 0x8edc,
  0x8edd, 0x8ede, 0x8edf, 0x8ee0, 0x8ee1, 0x8ee2, 0x8ee3, 0x8ee4,
  0x8ee5, 0x8ee6, 0x8ee7, 0x8ee8, 0x8ee9, 0x8eea, 0x8eeb, 0x8eec,
  0x8eed, 0x8eee, 0x8eef, 0x8ef0, 0x8ef1, 0x8ef2, 0x8ef3, 0x8ef4,
  0x8ef5, 0x8ef6, 0x8ef7, 0x8ef8, 0x8ef9, 0x8efa, 0x8efb, 0x8efc,
  0x8efd, 0x8efe, 0x8f40, 0x8f41, 0x8f42, 0x8f43, 0x8f44, 0x8f45,
  0x8f46, 0x8f47, 0x8f48, 0x8f49, 0x8f4a, 0x8f4b, 0x8f4c, 0x8f4d,
  0x8f4e, 0x8f4f, 0x8f50, 0x8f51, 0x8f52, 0x8f53, 0x8f54, 0x8f55,
  0x8f56, 0x8f57, 0x8f58, 0x8f59, 0x8f5a, 0x8f5b, 0x8f5c, 0x8f5d,
  0x8f5e, 0x8f5f, 0x8f60, 0x8f61, 0x8f62, 0x8f63, 0x8f64, 0x8f65,
  0x8f66, 0x8f67, 0x8f68, 0x8f69, 0x8f6a, 0x8f6b, 0x8f6c, 0x8f6d,
  0x8f6e, 0x8f6f, 0x8f70, 0x8f71, 0x8f72, 0x8f73, 0x8f74, 0x8f75,
  0x8f76, 0x8f77, 0x8f78, 0x8f79, 0x8f7a, 0x8f7b, 0x8f7c, 0x8f7d,
  0x8f7e, 0x8f80, 0x8f81, 0x8f82, 0x8f83, 0x8f84, 0x8f85, 0x8f86,
  0x8f87, 0x8f88, 0x8f89, 0x8f8a, 0x8f8b, 0x8f8c, 0x8f8d, 0x8f8e,
  0x8f8f, 0x8f90, 0x8f91, 0x8f92, 0x8f93, 0x8f94, 0x8f95, 0x8f96,
  0x8f97, 0x8f98, 0x8f99, 0x8f9a, 0x8f9b, 0x8f9c, 0x8f9d, 0x8f9e,
  0x8f9f, 0x8fa0, 0x8fa1, 0x8fa2, 0x8fa3, 0x8fa4, 0x8fa5, 0x8fa6,
  0x8fa7, 0x8fa8, 0x8fa9, 0x8faa, 0x8fab, 0x8fac, 0x8fad, 0x8fae,
  0x8faf, 0x8fb0, 0x8fb1, 0x8fb2, 0x8fb3, 0x8fb4, 0x8fb5, 0x8fb6,
  0x8fb7, 0x8fb8, 0x8fb9, 0x8fba, 0x8fbb, 0x8fbc, 0x8fbd, 0x8fbe,
  0x8fbf, 0x8fc0, 0x8fc1, 0x8fc2, 0x8fc3, 0x8fc4, 0x8fc5, 0x8fc6,
  0x8fc7, 0x8fc8, 0x8fc9, 0x8fca, 0x8fcb, 0x8fcc, 0x8fcd, 0x8fce,
  0x8fcf, 0x8fd0, 0x8fd1, 0x8fd2, 0x8fd3, 0x8fd4, 0x8fd5, 0x8fd6,
  0x8fd7, 0x8fd8, 0x8fd9, 0x8fda, 0x8fdb, 0x8fdc, 0x8fdd, 0x8fde,
  0x8fdf, 0x8fe0, 0x8fe1, 0x8fe2, 0x8fe3, 0x8fe4, 0x8fe5, 0x8fe6,
  0x8fe7, 0x8fe8, 0x8fe9, 0x8fea, 0x8feb, 0x8fec, 0x8fed, 0x8fee,
  0x8fef, 0x8ff0, 0x8ff1, 0x8ff2, 0x8ff3, 0x8ff4, 0x8ff5, 0x8ff6,
  0x8ff7, 0x8ff8, 0x8ff9, 0x8ffa, 0x8ffb, 0x8ffc, 0x8ffd, 0x8ffe,
  0x9040, 0x9041, 0x9042, 0x9043, 0x9044, 0x9045, 0x9046, 0x9047,
  0x9048, 0x9049, 0x904a, 0x904b, 0x904c, 0x904d, 0x904e, 0x904f,
  0x9050, 0x9051, 0x9052, 0x9053, 0x9054, 0x9055, 0x9056, 0x9057,
  0x9058, 0x9059, 0x905a, 0x905b, 0x905c, 0x905d, 0x905e, 0x905f,
  0x9060, 0x9061, 0x9062, 0x9063, 0x9064, 0x9065, 0x9066, 0x9067,
  0x9068, 0x9069, 0x906a, 0x906b, 0x906c, 0x906d, 0x906e, 0x906f,
  0x9070, 0x9071, 0x9072, 0x9073, 0x9074, 0x9075, 0x9076, 0x9077,
  0x9078, 0x9079, 0x907a, 0x907b, 0x907c, 0x907d, 0x907e, 0x9080,
  0x9081, 0x9082, 0x9083, 0x9084, 0x9085, 0x9086, 0x9087, 0x9088,
  0x9089, 0x908a, 0x908b, 0x908c, 0x908d, 0x908e, 0x908f, 0x9090,
  0x9091, 0x9092, 0x9093, 0x9094, 0x9095, 0x9096, 0x9097, 0x9098,
  0x9099, 0x909a, 0x909b, 0x909c, 0x909d, 0x909e, 0x909f, 0x90a0,
  0x90a1, 0x90a2, 0x90a3, 0x90a4, 0x90a5, 0x90a6, 0x90a7, 0x90a8,
  0x90a9, 0x90aa, 0x90ab, 0x90ac, 0x90ad, 0x90ae, 0x90af, 0x90b0,
  0x90b1, 0x90b2, 0x90b3, 0x90b4, 0x90b5, 0x90b6, 0x90b7, 0x90b8,
  0x90b9, 0x90ba, 0x90bb, 0x90bc, 0x90bd, 0x90be, 0x90bf, 0x90c0,
  0x90c1, 0x90c2, 0x90c3, 0x90c4, 0x90c5, 0x90c6, 0x90c7, 0x90c8,
  0x90c9, 0x90ca, 0x90cb, 0x90cc, 0x90cd, 0x90ce, 0x90cf, 0x90d0,
  0x90d1, 0x90d2, 0x90d3, 0x90d4, 0x90d5, 0x90d6, 0x90d7, 0x90d8,
  0x90d9, 0x90da, 0x90db, 0x90dc, 0x90dd, 0x90de, 0x90df, 0x90e0,
  0x90e1, 0x90e2, 0x90e3, 0x90e4, 0x90e5, 0x90e6, 0x90e7, 0x90e8,
  0x90e9, 0x90ea, 0x90eb, 0x90ec, 0x90ed, 0x90ee, 0x90ef, 0x90f0,
  0x90f1, 0x90f2, 0x90f3, 0x90f4, 0x90f5, 0x90f6, 0x90f7, 0x90f8,
  0x90f9, 0x90fa, 0x90fb, 0x90fc, 0x90fd, 0x90fe, 0x9140, 0x9141,
  0x9142, 0x9143, 0x9144, 0x9145, 0x9146, 0x9147, 0x9148, 0x9149,
  0x914a, 0x914b, 0x914c, 0x914d, 0x914e, 0x914f, 0x9150, 0x9151,
  0x9152, 0x9153, 0x9154, 0x9155, 0x9156, 0x9157, 0x9158, 0x9159,
  0x915a, 0x915b, 0x915c, 0x915d, 0x915e, 0x915f, 0x9160, 0x9161,
  0x9162, 0x9163, 0x9164, 0x9165, 0x9166, 0x9167, 0x9168, 0x9169,
  0x916a, 0x916b, 0x916c, 0x916d, 0x916e, 0x916f, 0x9170, 0x9171,
  0x9172, 0x9173, 0x9174, 0x9175, 0x9176, 0x9177, 0x9178, 0x9179,
  0x917a, 0x917b, 0x917c, 0x917d, 0x917e, 0x9180, 0x9181, 0x9182,
  0x9183, 0x9184, 0x9185, 0x9186, 0x9187, 0x9188, 0x9189, 0x918a,
  0x918b, 0x918c, 0x918d, 0x918e, 0x918f, 0x9190, 0x9191, 0x9192,
  0x9193, 0x9194, 0x9195, 0x9196, 0x9197, 0x9198, 0x9199, 0x919a,
  0x919b, 0x919c, 0x919d, 0x919e, 0x919f, 0x91a0, 0x91a1, 0x91a2,
  0x91a3, 0x91a4, 0x91a5, 0x91a6, 0x91a7, 0x91a8, 0x91a9, 0x91aa,
  0x91ab, 0x91ac, 0x91ad, 0x91ae, 0x91af, 0x91b0, 0x91b1, 0x91b2,
  0x91b3, 0x91b4, 0x91b5, 0x91b6, 0x91b7, 0x91b8, 0x91b9, 0x91ba,
  0x91bb, 0x91bc, 0x91bd, 0x91be, 0x91bf, 0x91c0, 0x91c1, 0x91c2,
  0x91c3, 0x91c4, 0x91c5, 0x91c6, 0x91c7, 0x91c8, 0x91c9, 0x91ca,
  0x91cb, 0x91cc, 0x91cd, 0x91ce, 0x91cf, 0x91d0, 0x91d1, 0x91d2,
  0x91d3, 0x91d4, 0x91d5, 0x91d6, 0x91d7, 0x91d8, 0x91d9, 0x91da,
  0x91db, 0x91dc, 0x91dd, 0x91de, 0x91df, 0x91e0, 0x91e1, 0x91e2,
  0x91e3, 0x91e4, 0x91e5, 0x91e6, 0x91e7, 0x91e8, 0x91e9, 0x91ea,
  0x91eb, 0x91ec, 0x91ed, 0x91ee, 0x91ef, 0x91f0, 0x91f1, 0x91f2,
  0x91f3, 0x91f4, 0x91f5, 0x91f6, 0x91f7, 0x91f8, 0x91f9, 0x91fa,
  0x91fb, 0x91fc, 0x91fd, 0x91fe, 0x9240, 0x9241, 0x9242, 0x9243,
  0x9244, 0x9245, 0x9246, 0x9247, 0x9248, 0x9249, 0x924a, 0x924b,
  0x924c, 0x924d, 0x924e, 0x924f, 0x9250, 0x9251, 0x9252, 0x9253,
  0x9254, 0x9255, 0x9256, 0x9257, 0x9258, 0x9259, 0x925a, 0x925b,
  0x925c, 0x925d, 0x925e, 0x925f, 0x9260, 0x9261, 0x9262, 0x9263,
  0x9264, 0x9265, 0x9266, 0x9267, 0x9268, 0x9269, 0x926a, 0x926b,
  0x926c, 0x926d, 0x926e, 0x926f, 0x9270, 0x9271, 0x9272, 0x9273,
  0x9274, 0x9275, 0x9276, 0x9277, 0x9278, 0x9279, 0x927a, 0x927b,
  0x927c, 0x927d, 0x927e, 0x9280, 0x9281, 0x9282, 0x9283, 0x9284,
  0x9285, 0x9286, 0x9287, 0x9288, 0x9289, 0x928a, 0x928b, 0x928c,
  0x928d, 0x928e, 0x928f, 0x9290, 0x9291, 0x9292, 0x9293, 0x9294,
  0x9295, 0x9296, 0x9297, 0x9298, 0x9299, 0x929a, 0x929b, 0x929c,
  0x929d, 0x929e, 0x929f, 0x92a0, 0x92a1, 0x92a2, 0x92a3, 0x92a4,
  0x92a5, 0x92a6, 0x92a7, 0x92a8, 0x92a9, 0x92aa, 0x92ab, 0x92ac,
  0x92ad, 0x92ae, 0x92af, 0x92b0, 0x92b1, 0x92b2, 0x92b3, 0x92b4,
  0x92b5, 0x92b6, 0x92b7, 0x92b8, 0x92b9, 0x92ba, 0x92bb, 0x92bc,
  0x92bd, 0x92be, 0x92bf, 0x92c0, 0x92c1, 0x92c2, 0x92c3, 0x92c4,
  0x92c5, 0x92c6, 0x92c7, 0x92c8, 0x92c9, 0x92ca, 0x92cb, 0x92cc,
  0x92cd, 0x92ce, 0x92cf, 0x92d0, 0x92d1, 0x92d2, 0x92d3, 0x92d4,
  0x92d5, 0x92d6, 0x92d7, 0x92d8, 0x92d9, 0x92da, 0x92db, 0x92dc,
  0x92dd, 0x92de, 0x92df, 0x92e0, 0x92e1, 0x92e2, 0x92e3, 0x92e4,
  0x92e5, 0x92e6, 0x92e7, 0x92e8, 0x92e9, 0x92ea, 0x92eb, 0x92ec,
  0x92ed, 0x92ee, 0x92ef, 0x92f0, 0x92f1, 0x92f2, 0x92f3, 0x92f4,
  0x92f5, 0x92f6, 0x92f7, 0x92f8, 0x92f9, 0x92fa, 0x92fb, 0x92fc,
  0x92fd, 0x92fe, 0x9340, 0x9341, 0x9342, 0x9343, 0x9344, 0x9345,
  0x9346, 0x9347, 0x9348, 0x9349, 0x934a, 0x934b, 0x934c, 0x934d,
  0x934e, 0x934f, 0x9350, 0x9351, 0x9352, 0x9353, 0x9354, 0x9355,
  0x9356, 0x9357, 0x9358, 0x9359, 0x935a, 0x935b, 0x935c, 0x935d,
  0x935e, 0x935f, 0x9360, 0x9361, 0x9362, 0x9363, 0x9364, 0x9365,
  0x9366, 0x9367, 0x9368, 0x9369, 0x936a, 0x936b, 0x936c, 0x936d,
  0x936e, 0x936f, 0x9370, 0x9371, 0x9372, 0x9373, 0x9374, 0x9375,
  0x9376, 0x9377, 0x9378, 0x9379, 0x937a, 0x937b, 0x937c, 0x937d,
  0x937e, 0x9380, 0x9381, 0x9382, 0x9383, 0x9384, 0x9385, 0x9386,
  0x9387, 0x9388, 0x9389, 0x938a, 0x938b, 0x938c, 0x938d, 0x938e,
  0x938f, 0x9390, 0x9391, 0x9392, 0x9393, 0x9394, 0x9395, 0x9396,
  0x9397, 0x9398, 0x9399, 0x939a, 0x939b, 0x939c, 0x939d, 0x939e,
  0x939f, 0x93a0, 0x93a1, 0x93a2, 0x93a3, 0x93a4, 0x93a5, 0x93a6,
  0x93a7, 0x93a8, 0x93a9, 0x93aa, 0x93ab, 0x93ac, 0x93ad, 0x93ae,
  0x93af, 0x93b0, 0x93b1, 0x93b2, 0x93b3, 0x93b4, 0x93b5, 0x93b6,
  0x93b7, 0x93b8, 0x93b9, 0x93ba, 0x93bb, 0x93bc, 0x93bd, 0x93be,
  0x93bf, 0x93c0, 0x93c1, 0x93c2, 0x93c3, 0x93c4, 0x93c5, 0x93c6,
  0x93c7, 0x93c8, 0x93c9, 0x93ca, 0x93cb, 0x93cc, 0x93cd, 0x93ce,
  0x93cf, 0x93d0, 0x93d1, 0x93d2, 0x93d3, 0x93d4, 0x93d5, 0x93d6,
  0x93d7, 0x93d8, 0x93d9, 0x93da, 0x93db, 0x93dc, 0x93dd, 0x93de,
  0x93df, 0x93e0, 0x93e1, 0x93e2, 0x93e3, 0x93e4, 0x93e5, 0x93e6,
  0x93e7, 0x93e8, 0x93e9, 0x93ea, 0x93eb, 0x93ec, 0x93ed, 0x93ee,
  0x93ef, 0x93f0, 0x93f1, 0x93f2, 0x93f3, 0x93f4, 0x93f5, 0x93f6,
  0x93f7, 0x93f8, 0x93f9, 0x93fa, 0x93fb, 0x93fc, 0x93fd, 0x93fe,
  0x9440, 0x9441, 0x9442, 0x9443, 0x9444, 0x9445, 0x9446, 0x9447,
  0x9448, 0x9449, 0x944a, 0x944b, 0x944c, 0x944d, 0x944e, 0x944f,
  0x9450, 0x9451, 0x9452, 0x9453, 0x9454, 0x9455, 0x9456, 0x9457,
  0x9458, 0x9459, 0x945a, 0x945b, 0x945c, 0x945d, 0x945e, 0x945f,
  0x9460, 0x9461, 0x9462, 0x9463, 0x9464, 0x9465, 0x9466, 0x9467,
  0x9468, 0x9469, 0x946a, 0x946b, 0x946c, 0x946d, 0x946e, 0x946f,
  0x9470, 0x9471, 0x9472, 0x9473, 0x9474, 0x9475, 0x9476, 0x9477,
  0x9478, 0x9479, 0x947a, 0x947b, 0x947c, 0x947d, 0x947e, 0x9480,
  0x9481, 0x9482, 0x9483, 0x9484, 0x9485, 0x9486, 0x9487, 0x9488,
  0x9489, 0x948a, 0x948b, 0x948c, 0x948d, 0x948e, 0x948f, 0x9490,
  0x9491, 0x9492, 0x9493, 0x9494, 0x9495, 0x9496, 0x9497, 0x9498,
  0x9499, 0x949a, 0x949b, 0x949c, 0x949d, 0x949e, 0x949f, 0x94a0,
  0x94a1, 0x94a2, 0x94a3, 0x94a4, 0x94a5, 0x94a6, 0x94a7, 0x94a8,
  0x94a9, 0x94aa, 0x94ab, 0x94ac, 0x94ad, 0x94ae, 0x94af, 0x94b0,
  0x94b1, 0x94b2, 0x94b3, 0x94b4, 0x94b5, 0x94b6, 0x94b7, 0x94b8,
  0x94b9, 0x94ba, 0x94bb, 0x94bc, 0x94bd, 0x94be, 0x94bf, 0x94c0,
  0x94c1, 0x94c2, 0x94c3, 0x94c4, 0x94c5, 0x94c6, 0x94c7, 0x94c8,
  0x94c9, 0x94ca, 0x94cb, 0x94cc, 0x94cd, 0x94ce, 0x94cf, 0x94d0,
  0x94d1, 0x94d2, 0x94d3, 0x94d4, 0x94d5, 0x94d6, 0x94d7, 0x94d8,
  0x94d9, 0x94da, 0x94db, 0x94dc, 0x94dd, 0x94de, 0x94df, 0x94e0,
  0x94e1, 0x94e2, 0x94e3, 0x94e4, 0x94e5, 0x94e6, 0x94e7, 0x94e8,
  0x94e9, 0x94ea, 0x94eb, 0x94ec, 0x94ed, 0x94ee, 0x94ef, 0x94f0,
  0x94f1, 0x94f2, 0x94f3, 0x94f4, 0x94f5, 0x94f6, 0x94f7, 0x94f8,
  0x94f9, 0x94fa, 0x94fb, 0x94fc, 0x94fd, 0x94fe, 0x9540, 0x9541,
  0x9542, 0x9543, 0x9544, 0x9545, 0x9546, 0x9547, 0x9548, 0x9549,
  0x954a, 0x954b, 0x954c, 0x954d, 0x954e, 0x954f, 0x9550, 0x9551,
  0x9552, 0x9553, 0x9554, 0x9555, 0x9556, 0x9557, 0x9558, 0x9559,
  0x955a, 0x955b, 0x955c, 0x955d, 0x955e, 0x955f, 0x9560, 0x9561,
  0x9562, 0x9563, 0x9564, 0x9565, 0x9566, 0x9567, 0x9568, 0x9569,
  0x956a, 0x956b, 0x956c, 0x956d, 0x956e, 0x956f, 0x9570, 0x9571,
  0x9572, 0x9573, 0x9574, 0x9575, 0x9576, 0x9577, 0x9578, 0x9579,
  0x957a, 0x957b, 0x957c, 0x957d, 0x957e, 0x9580, 0x9581, 0x9582,
  0x9583, 0x9584, 0x9585, 0x9586, 0x9587, 0x9588, 0x9589, 0x958a,
  0x958b, 0x958c, 0x958d, 0x958e, 0x958f, 0x9590, 0x9591, 0x9592,
  0x9593, 0x9594, 0x9595, 0x9596, 0x9597, 0x9598, 0x9599, 0x959a,
  0x959b, 0x959c, 0x959d, 0x959e, 0x959f, 0x95a0, 0x95a1, 0x95a2,
  0x95a3, 0x95a4, 0x95a5, 0x95a6, 0x95a7, 0x95a8, 0x95a9, 0x95aa,
  0x95ab, 0x95ac, 0x95ad, 0x95ae, 0x95af, 0x95b0, 0x95b1, 0x95b2,
  0x95b3, 0x95b4, 0x95b5, 0x95b6, 0x95b7, 0x95b8, 0x95b9, 0x95ba,
  0x95bb, 0x95bc, 0x95bd, 0x95be, 0x95bf, 0x95c0, 0x95c1, 0x95c2,
  0x95c3, 0x95c4, 0x95c5, 0x95c6, 0x95c7, 0x95c8, 0x95c9, 0x95ca,
  0x95cb, 0x95cc, 0x95cd, 0x95ce, 0x95cf, 0x95d0, 0x95d1, 0x95d2,
  0x95d3, 0x95d4, 0x95d5, 0x95d6, 0x95d7, 0x95d8, 0x95d9, 0x95da,
  0x95db, 0x95dc, 0x95dd, 0x95de, 0x95df, 0x95e0, 0x95e1, 0x95e2,
  0x95e3, 0x95e4, 0x95e5, 0x95e6, 0x95e7, 0x95e8, 0x95e9, 0x95ea,
  0x95eb, 0x95ec, 0x95ed, 0x95ee, 0x95ef, 0x95f0, 0x95f1, 0x95f2,
  0x95f3, 0x95f4, 0x95f5, 0x95f6, 0x95f7, 0x95f8, 0x95f9, 0x95fa,
  0x95fb, 0x95fc, 0x95fd, 0x95fe, 0x9640, 0x9641, 0x9642, 0x9643,
  0x9644, 0x9645, 0x9646, 0x9647, 0x9648, 0x9649, 0x964a, 0x964b,
  0x964c, 0x964d, 0x964e, 0x964f, 0x9650, 0x9651, 0x9652, 0x9653,
  0x9654, 0x9655, 0x9656, 0x9657, 0x9658, 0x9659, 0x965a, 0x965b,
  0x965c, 0x965d, 0x965e, 0x965f, 0x9660, 0x9661, 0x9662, 0x9663,
  0x9664, 0x9665, 0x9666, 0x9667, 0x9668, 0x9669, 0x966a, 0x966b,
  0x966c, 0x966d, 0x966e, 0x966f, 0x9670, 0x9671, 0x9672, 0x9673,
  0x9674, 0x9675, 0x9676, 0x9677, 0x9678, 0x9679, 0x967a, 0x967b,
  0x967c, 0x967d, 0x967e, 0x9680, 0x9681, 0x9682, 0x9683, 0x9684,
  0x9685, 0x9686, 0x9687, 0x9688, 0x9689, 0x968a, 0x968b, 0x968c,
  0x968d, 0x968e, 0x968f, 0x9690, 0x9691, 0x9692, 0x9693, 0x9694,
  0x9695, 0x9696, 0x9697, 0x9698, 0x9699, 0x969a, 0x969b, 0x969c,
  0x969d, 0x969e, 0x969f, 0x96a0, 0x96a1, 0x96a2, 0x96a3, 0x96a4,
  0x96a5, 0x96a6, 0x96a7, 0x96a8, 0x96a9, 0x96aa, 0x96ab, 0x96ac,
  0x96ad, 0x96ae, 0x96af, 0x96b0, 0x96b1, 0x96b2, 0x96b3, 0x96b4,
  0x96b5, 0x96b6, 0x96b7, 0x96b8, 0x96b9, 0x96ba, 0x96bb, 0x96bc,
  0x96bd, 0x96be, 0x96bf, 0x96c0, 0x96c1, 0x96c2, 0x96c3, 0x96c4,
  0x96c5, 0x96c6, 0x96c7, 0x96c8, 0x96c9, 0x96ca, 0x96cb, 0x96cc,
  0x96cd, 0x96ce, 0x96cf, 0x96d0, 0x96d1, 0x96d2, 0x96d3, 0x96d4,
  0x96d5, 0x96d6, 0x96d7, 0x96d8, 0x96d9, 0x96da, 0x96db, 0x96dc,
  0x96dd, 0x96de, 0x96df, 0x96e0, 0x96e1, 0x96e2, 0x96e3, 0x96e4,
  0x96e5, 0x96e6, 0x96e7, 0x96e8, 0x96e9, 0x96ea, 0x96eb, 0x96ec,
  0x96ed, 0x96ee, 0x96ef, 0x96f0, 0x96f1, 0x96f2, 0x96f3, 0x96f4,
  0x96f5, 0x96f6, 0x96f7, 0x96f8, 0x96f9, 0x96fa, 0x96fb, 0x96fc,
  0x96fd, 0x96fe, 0x9740, 0x9741, 0x9742, 0x9743, 0x9744, 0x9745,
  0x9746, 0x9747, 0x9748, 0x9749, 0x974a, 0x974b, 0x974c, 0x974d,
  0x974e, 0x974f, 0x9750, 0x9751, 0x9752, 0x9753, 0x9754, 0x9755,
  0x9756, 0x9757, 0x9758, 0x9759, 0x975a, 0x975b, 0x975c, 0x975d,
  0x975e, 0x975f, 0x9760, 0x9761, 0x9762, 0x9763, 0x9764, 0x9765,
  0x9766, 0x9767, 0x9768, 0x9769, 0x976a, 0x976b, 0x976c, 0x976d,
  0x976e, 0x976f, 0x9770, 0x9771, 0x9772, 0x9773, 0x9774, 0x9775,
  0x9776, 0x9777, 0x9778, 0x9779, 0x977a, 0x977b, 0x977c, 0x977d,
  0x977e, 0x9780, 0x9781, 0x9782, 0x9783, 0x9784, 0x9785, 0x9786,
  0x9787, 0x9788, 0x9789, 0x978a, 0x978b, 0x978c, 0x978d, 0x978e,
  0x978f, 0x9790, 0x9791, 0x9792, 0x9793, 0x9794, 0x9795, 0x9796,
  0x9797, 0x9798, 0x9799, 0x979a, 0x979b, 0x979c, 0x979d, 0x979e,
  0x979f, 0x97a0, 0x97a1, 0x97a2, 0x97a3, 0x97a4, 0x97a5, 0x97a6,
  0x97a7, 0x97a8, 0x97a9, 0x97aa, 0x97ab, 0x97ac, 0x97ad, 0x97ae,
  0x97af, 0x97b0, 0x97b1, 0x97b2, 0x97b3, 0x97b4, 0x97b5, 0x97b6,
  0x97b7, 0x97b8, 0x97b9, 0x97ba, 0x97bb, 0x97bc, 0x97bd, 0x97be,
  0x97bf, 0x97c0, 0x97c1, 0x97c2, 0x97c3, 0x97c4, 0x97c5, 0x97c6,
  0x97c7, 0x97c8, 0x97c9, 0x97ca, 0x97cb, 0x97cc, 0x97cd, 0x97ce,
  0x97cf, 0x97d0, 0x97d1, 0x97d2, 0x97d3, 0x97d4, 0x97d5, 0x97d6,
  0x97d7, 0x97d8, 0x97d9, 0x97da, 0x97db, 0x97dc, 0x97dd, 0x97de,
  0x97df, 0x97e0, 0x97e1, 0x97e2, 0x97e3, 0x97e4, 0x97e5, 0x97e6,
  0x97e7, 0x97e8, 0x97e9, 0x97ea, 0x97eb, 0x97ec, 0x97ed, 0x97ee,
  0x97ef, 0x97f0, 0x97f1, 0x97f2, 0x97f3, 0x97f4, 0x97f5, 0x97f6,
  0x97f7, 0x97f8, 0x97f9, 0x97fa, 0x97fb, 0x97fc, 0x97fd, 0x97fe,
  0x9840, 0x9841, 0x9842, 0x9843, 0x9844, 0x9845, 0x9846, 0x9847,
  0x9848, 0x9849, 0x984a, 0x984b, 0x984c, 0x984d, 0x984e, 0x984f,
  0x9850, 0x9851, 0x9852, 0x9853, 0x9854, 0x9855, 0x9856, 0x9857,
  0x9858, 0x9859, 0x985a, 0x985b, 0x985c, 0x985d, 0x985e, 0x985f,
  0x9860, 0x9861, 0x9862, 0x9863, 0x9864, 0x9865, 0x9866, 0x9867,
  0x9868, 0x9869, 0x986a, 0x986b, 0x986c, 0x986d, 0x986e, 0x986f,
  0x9870, 0x9871, 0x9872, 0x9873, 0x9874, 0x9875, 0x9876, 0x9877,
  0x9878, 0x9879, 0x987a, 0x987b, 0x987c, 0x987d, 0x987e, 0x9880,
  0x9881, 0x9882, 0x9883, 0x9884, 0x9885, 0x9886, 0x9887, 0x9888,
  0x9889, 0x988a, 0x988b, 0x988c, 0x988d, 0x988e, 0x988f, 0x9890,
  0x9891, 0x9892, 0x9893, 0x9894, 0x9895, 0x9896, 0x9897, 0x9898,
  0x9899, 0x989a, 0x989b, 0x989c, 0x989d, 0x989e, 0x989f, 0x98a0,
  0x98a1, 0x98a2, 0x98a3, 0x98a4, 0x98a5, 0x98a6, 0x98a7, 0x98a8,
  0x98a9, 0x98aa, 0x98ab, 0x98ac, 0x98ad, 0x98ae, 0x98af, 0x98b0,
  0x98b1, 0x98b2, 0x98b3, 0x98b4, 0x98b5, 0x98b6, 0x98b7, 0x98b8,
  0x98b9, 0x98ba, 0x98bb, 0x98bc, 0x98bd, 0x98be, 0x98bf, 0x98c0,
  0x98c1, 0x98c2, 0x98c3, 0x98c4, 0x98c5, 0x98c6, 0x98c7, 0x98c8,
  0x98c9, 0x98ca, 0x98cb, 0x98cc, 0x98cd, 0x98ce, 0x98cf, 0x98d0,
  0x98d1, 0x98d2, 0x98d3, 0x98d4, 0x98d5, 0x98d6, 0x98d7, 0x98d8,
  0x98d9, 0x98da, 0x98db, 0x98dc, 0x98dd, 0x98de, 0x98df, 0x98e0,
  0x98e1, 0x98e2, 0x98e3, 0x98e4, 0x98e5, 0x98e6, 0x98e7, 0x98e8,
  0x98e9, 0x98ea, 0x98eb, 0x98ec, 0x98ed, 0x98ee, 0x98ef, 0x98f0,
  0x98f1, 0x98f2, 0x98f3, 0x98f4, 0x98f5, 0x98f6, 0x98f7, 0x98f8,
  0x98f9, 0x98fa, 0x98fb, 0x98fc, 0x98fd, 0x98fe, 0x9940, 0x9941,
  0x9942, 0x9943, 0x9944, 0x9945, 0x9946, 0x9947, 0x9948, 0x9949,
  0x994a, 0x994b, 0x994c, 0x994d, 0x994e, 0x994f, 0x9950, 0x9951,
  0x9952, 0x9953, 0x9954, 0x9955, 0x9956, 0x9957, 0x9958, 0x9959,
  0x995a, 0x995b, 0x995c, 0x995d, 0x995e, 0x995f, 0x9960, 0x9961,
  0x9962, 0x9963, 0x9964, 0x9965, 0x9966, 0x9967, 0x9968, 0x9969,
  0x996a, 0x996b, 0x996c, 0x996d, 0x996e, 0x996f, 0x9970, 0x9971,
  0x9972, 0x9973, 0x9974, 0x9975, 0x9976, 0x9977, 0x9978, 0x9979,
  0x997a, 0x997b, 0x997c, 0x997d, 0x997e, 0x9980, 0x9981, 0x9982,
  0x9983, 0x9984, 0x9985, 0x9986, 0x9987, 0x9988, 0x9989, 0x998a,
  0x998b, 0x998c, 0x998d, 0x998e, 0x998f, 0x9990, 0x9991, 0x9992,
  0x9993, 0x9994, 0x9995, 0x9996, 0x9997, 0x9998, 0x9999, 0x999a,
  0x999b, 0x999c, 0x999d, 0x999e, 0x999f, 0x99a0, 0x99a1, 0x99a2,
  0x99a3, 0x99a4, 0x99a5, 0x99a6, 0x99a7, 0x99a8, 0x99a9, 0x99aa,
  0x99ab, 0x99ac, 0x99ad, 0x99ae, 0x99af, 0x99b0, 0x99b1, 0x99b2,
  0x99b3, 0x99b4, 0x99b5, 0x99b6, 0x99b7, 0x99b8, 0x99b9, 0x99ba,
  0x99bb, 0x99bc, 0x99bd, 0x99be, 0x99bf, 0x99c0, 0x99c1, 0x99c2,
  0x99c3, 0x99c4, 0x99c5, 0x99c6, 0x99c7, 0x99c8, 0x99c9, 0x99ca,
  0x99cb, 0x99cc, 0x99cd, 0x99ce, 0x99cf, 0x99d0, 0x99d1, 0x99d2,
  0x99d3, 0x99d4, 0x99d5, 0x99d6, 0x99d7, 0x99d8, 0x99d9, 0x99da,
  0x99db, 0x99dc, 0x99dd, 0x99de, 0x99df, 0x99e0, 0x99e1, 0x99e2,
  0x99e3, 0x99e4, 0x99e5, 0x99e6, 0x99e7, 0x99e8, 0x99e9, 0x99ea,
  0x99eb, 0x99ec, 0x99ed, 0x99ee, 0x99ef, 0x99f0, 0x99f1, 0x99f2,
  0x99f3, 0x99f4, 0x99f5, 0x99f6, 0x99f7, 0x99f8, 0x99f9, 0x99fa,
  0x99fb, 0x99fc, 0x99fd, 0x99fe, 0x9a40, 0x9a41, 0x9a42, 0x9a43,
  0x9a44, 0x9a45, 0x9a46, 0x9a47, 0x9a48, 0x9a49, 0x9a4a, 0x9a4b,
  0x9a4c, 0x9a4d, 0x9a4e, 0x9a4f, 0x9a50, 0x9a51, 0x9a52, 0x9a53,
  0x9a54, 0x9a55, 0x9a56, 0x9a57, 0x9a58, 0x9a59, 0x9a5a, 0x9a5b,
  0x9a5c, 0x9a5d, 0x9a5e, 0x9a5f, 0x9a60, 0x9a61, 0x9a62, 0x9a63,
  0x9a64, 0x9a65, 0x9a66, 0x9a67, 0x9a68, 0x9a69, 0x9a6a, 0x9a6b,
  0x9a6c, 0x9a6d, 0x9a6e, 0x9a6f, 0x9a70, 0x9a71, 0x9a72, 0x9a73,
  0x9a74, 0x9a75, 0x9a76, 0x9a77, 0x9a78, 0x9a79, 0x9a7a, 0x9a7b,
  0x9a7c, 0x9a7d, 0x9a7e, 0x9a80, 0x9a81, 0x9a82, 0x9a83, 0x9a84,
  0x9a85, 0x9a86, 0x9a87, 0x9a88, 0x9a89, 0x9a8a, 0x9a8b, 0x9a8c,
  0x9a8d, 0x9a8e, 0x9a8f, 0x9a90, 0x9a91, 0x9a92, 0x9a93, 0x9a94,
  0x9a95, 0x9a96, 0x9a97, 0x9a98, 0x9a99, 0x9a9a, 0x9a9b, 0x9a9c,
  0x9a9d, 0x9a9e, 0x9a9f, 0x9aa0, 0x9aa1, 0x9aa2, 0x9aa3, 0x9aa4,
  0x9aa5, 0x9aa6, 0x9aa7, 0x9aa8, 0x9aa9, 0x9aaa, 0x9aab, 0x9aac,
  0x9aad, 0x9aae, 0x9aaf, 0x9ab0, 0x9ab1, 0x9ab2, 0x9ab3, 0x9ab4,
  0x9ab5, 0x9ab6, 0x9ab7, 0x9ab8, 0x9ab9, 0x9aba, 0x9abb, 0x9abc,
  0x9abd, 0x9abe, 0x9abf, 0x9ac0, 0x9ac1, 0x9ac2, 0x9ac3, 0x9ac4,
  0x9ac5, 0x9ac6, 0x9ac7, 0x9ac8, 0x9ac9, 0x9aca, 0x9acb, 0x9acc,
  0x9acd, 0x9ace, 0x9acf, 0x9ad0, 0x9ad1, 0x9ad2, 0x9ad3, 0x9ad4,
  0x9ad5, 0x9ad6, 0x9ad7, 0x9ad8, 0x9ad9, 0x9ada, 0x9adb, 0x9adc,
  0x9add, 0x9ade, 0x9adf, 0x9ae0, 0x9ae1, 0x9ae2, 0x9ae3, 0x9ae4,
  0x9ae5, 0x9ae6, 0x9ae7, 0x9ae8, 0x9ae9, 0x9aea, 0x9aeb, 0x9aec,
  0x9aed, 0x9aee, 0x9aef, 0x9af0, 0x9af1, 0x9af2, 0x9af3, 0x9af4,
  0x9af5, 0x9af6, 0x9af7, 0x9af8, 0x9af9, 0x9afa, 0x9afb, 0x9afc,
  0x9afd, 0x9afe, 0x9b40, 0x9b41, 0x9b42, 0x9b43, 0x9b44, 0x9b45,
  0x9b46, 0x9b47, 0x9b48, 0x9b49, 0x9b4a, 0x9b4b, 0x9b4c, 0x9b4d,
  0x9b4e, 0x9b4f, 0x9b50, 0x9b51, 0x9b52, 0x9b53, 0x9b54, 0x9b55,
  0x9b56, 0x9b57, 0x9b58, 0x9b59, 0x9b5a, 0x9b5b, 0x9b5c, 0x9b5d,
  0x9b5e, 0x9b5f, 0x9b60, 0x9b61, 0x9b62, 0x9b63, 0x9b64, 0x9b65,
  0x9b66, 0x9b67, 0x9b68, 0x9b69, 0x9b6a, 0x9b6b, 0x9b6c, 0x9b6d,
  0x9b6e, 0x9b6f, 0x9b70, 0x9b71, 0x9b72, 0x9b73, 0x9b74, 0x9b75,
  0x9b76, 0x9b77, 0x9b78, 0x9b79, 0x9b7a, 0x9b7b, 0x9b7c, 0x9b7d,
  0x9b7e, 0x9b80, 0x9b81, 0x9b82, 0x9b83, 0x9b84, 0x9b85, 0x9b86,
  0x9b87, 0x9b88, 0x9b89, 0x9b8a, 0x9b8b, 0x9b8c, 0x9b8d, 0x9b8e,
  0x9b8f, 0x9b90, 0x9b91, 0x9b92, 0x9b93, 0x9b94, 0x9b95, 0x9b96,
  0x9b97, 0x9b98, 0x9b99, 0x9b9a, 0x9b9b, 0x9b9c, 0x9b9d, 0x9b9e,
  0x9b9f, 0x9ba0, 0x9ba1, 0x9ba2, 0x9ba3, 0x9ba4, 0x9ba5, 0x9ba6,
  0x9ba7, 0x9ba8, 0x9ba9, 0x9baa, 0x9bab, 0x9bac, 0x9bad, 0x9bae,
  0x9baf, 0x9bb0, 0x9bb1, 0x9bb2, 0x9bb3, 0x9bb4, 0x9bb5, 0x9bb6,
  0x9bb7, 0x9bb8, 0x9bb9, 0x9bba, 0x9bbb, 0x9bbc, 0x9bbd, 0x9bbe,
  0x9bbf, 0x9bc0, 0x9bc1, 0x9bc2, 0x9bc3, 0x9bc4, 0x9bc5, 0x9bc6,
  0x9bc7, 0x9bc8, 0x9bc9, 0x9bca, 0x9bcb, 0x9bcc, 0x9bcd, 0x9bce,
  0x9bcf, 0x9bd0, 0x9bd1, 0x9bd2, 0x9bd3, 0x9bd4, 0x9bd5, 0x9bd6,
  0x9bd7, 0x9bd8, 0x9bd9, 0x9bda, 0x9bdb, 0x9bdc, 0x9bdd, 0x9bde,
  0x9bdf, 0x9be0, 0x9be1, 0x9be2, 0x9be3, 0x9be4, 0x9be5, 0x9be6,
  0x9be7, 0x9be8, 0x9be9, 0x9bea, 0x9beb, 0x9bec, 0x9bed, 0x9bee,
  0x9bef, 0x9bf0, 0x9bf1, 0x9bf2, 0x9bf3, 0x9bf4, 0x9bf5, 0x9bf6,
  0x9bf7, 0x9bf8, 0x9bf9, 0x9bfa, 0x9bfb, 0x9bfc, 0x9bfd, 0x9bfe,
  0x9c40, 0x9c41, 0x9c42, 0x9c43, 0x9c44, 0x9c45, 0x9c46, 0x9c47,
  0x9c48, 0x9c49, 0x9c4a, 0x9c4b, 0x9c4c, 0x9c4d, 0x9c4e, 0x9c4f,
  0x9c50, 0x9c51, 0x9c52, 0x9c53, 0x9c54, 0x9c55, 0x9c56, 0x9c57,
  0x9c58, 0x9c59, 0x9c5a, 0x9c5b, 0x9c5c, 0x9c5d, 0x9c5e, 0x9c5f,
  0x9c60, 0x9c61, 0x9c62, 0x9c63, 0x9c64, 0x9c65, 0x9c66, 0x9c67,
  0x9c68, 0x9c69, 0x9c6a, 0x9c6b, 0x9c6c, 0x9c6d, 0x9c6e, 0x9c6f,
  0x9c70, 0x9c71, 0x9c72, 0x9c73, 0x9c74, 0x9c75, 0x9c76, 0x9c77,
  0x9c78, 0x9c79, 0x9c7a, 0x9c7b, 0x9c7c, 0x9c7d, 0x9c7e, 0x9c80,
  0x9c81, 0x9c82, 0x9c83, 0x9c84, 0x9c85, 0x9c86, 0x9c87, 0x9c88,
  0x9c89, 0x9c8a, 0x9c8b, 0x9c8c, 0x9c8d, 0x9c8e, 0x9c8f, 0x9c90,
  0x9c91, 0x9c92, 0x9c93, 0x9c94, 0x9c95, 0x9c96, 0x9c97, 0x9c98,
  0x9c99, 0x9c9a, 0x9c9b, 0x9c9c, 0x9c9d, 0x9c9e, 0x9c9f, 0x9ca0,
  0x9ca1, 0x9ca2, 0x9ca3, 0x9ca4, 0x9ca5, 0x9ca6, 0x9ca7, 0x9ca8,
  0x9ca9, 0x9caa, 0x9cab, 0x9cac, 0x9cad, 0x9cae, 0x9caf, 0x9cb0,
  0x9cb1, 0x9cb2, 0x9cb3, 0x9cb4, 0x9cb5, 0x9cb6, 0x9cb7, 0x9cb8,
  0x9cb9, 0x9cba, 0x9cbb, 0x9cbc, 0x9cbd, 0x9cbe, 0x9cbf, 0x9cc0,
  0x9cc1, 0x9cc2, 0x9cc3, 0x9cc4, 0x9cc5, 0x9cc6, 0x9cc7, 0x9cc8,
  0x9cc9, 0x9cca, 0x9ccb, 0x9ccc, 0x9ccd, 0x9cce, 0x9ccf, 0x9cd0,
  0x9cd1, 0x9cd2, 0x9cd3, 0x9cd4, 0x9cd5, 0x9cd6, 0x9cd7, 0x9cd8,
  0x9cd9, 0x9cda, 0x9cdb, 0x9cdc, 0x9cdd, 0x9cde, 0x9cdf, 0x9ce0,
  0x9ce1, 0x9ce2, 0x9ce3, 0x9ce4, 0x9ce5, 0x9ce6, 0x9ce7, 0x9ce8,
  0x9ce9, 0x9cea, 0x9ceb, 0x9cec, 0x9ced, 0x9cee, 0x9cef, 0x9cf0,
  0x9cf1, 0x9cf2, 0x9cf3, 0x9cf4, 0x9cf5, 0x9cf6, 0x9cf7, 0x9cf8,
  0x9cf9, 0x9cfa, 0x9cfb, 0x9cfc, 0x9cfd, 0x9cfe, 0x9d40, 0x9d41,
  0x9d42, 0x9d43, 0x9d44, 0x9d45, 0x9d46, 0x9d47, 0x9d48, 0x9d49,
  0x9d4a, 0x9d4b, 0x9d4c, 0x9d4d, 0x9d4e, 0x9d4f, 0x9d50, 0x9d51,
  0x9d52, 0x9d53, 0x9d54, 0x9d55, 0x9d56, 0x9d57, 0x9d58, 0x9d59,
  0x9d5a, 0x9d5b, 0x9d5c, 0x9d5d, 0x9d5e, 0x9d5f, 0x9d60, 0x9d61,
  0x9d62, 0x9d63, 0x9d64, 0x9d65, 0x9d66, 0x9d67, 0x9d68, 0x9d69,
  0x9d6a, 0x9d6b, 0x9d6c, 0x9d6d, 0x9d6e, 0x9d6f, 0x9d70, 0x9d71,
  0x9d72, 0x9d73, 0x9d74, 0x9d75, 0x9d76, 0x9d77, 0x9d78, 0x9d79,
  0x9d7a, 0x9d7b, 0x9d7c, 0x9d7d, 0x9d7e, 0x9d80, 0x9d81, 0x9d82,
  0x9d83, 0x9d84, 0x9d85, 0x9d86, 0x9d87, 0x9d88, 0x9d89, 0x9d8a,
  0x9d8b, 0x9d8c, 0x9d8d, 0x9d8e, 0x9d8f, 0x9d90, 0x9d91, 0x9d92,
  0x9d93, 0x9d94, 0x9d95, 0x9d96, 0x9d97, 0x9d98, 0x9d99, 0x9d9a,
  0x9d9b, 0x9d9c, 0x9d9d, 0x9d9e, 0x9d9f, 0x9da0, 0x9da1, 0x9da2,
  0x9da3, 0x9da4, 0x9da5, 0x9da6, 0x9da7, 0x9da8, 0x9da9, 0x9daa,
  0x9dab, 0x9dac, 0x9dad, 0x9dae, 0x9daf, 0x9db0, 0x9db1, 0x9db2,
  0x9db3, 0x9db4, 0x9db5, 0x9db6, 0x9db7, 0x9db8, 0x9db9, 0x9dba,
  0x9dbb, 0x9dbc, 0x9dbd, 0x9dbe, 0x9dbf, 0x9dc0, 0x9dc1, 0x9dc2,
  0x9dc3, 0x9dc4, 0x9dc5, 0x9dc6, 0x9dc7, 0x9dc8, 0x9dc9, 0x9dca,
  0x9dcb, 0x9dcc, 0x9dcd, 0x9dce, 0x9dcf, 0x9dd0, 0x9dd1, 0x9dd2,
  0x9dd3, 0x9dd4, 0x9dd5, 0x9dd6, 0x9dd7, 0x9dd8, 0x9dd9, 0x9dda,
  0x9ddb, 0x9ddc, 0x9ddd, 0x9dde, 0x9ddf, 0x9de0, 0x9de1, 0x9de2,
  0x9de3, 0x9de4, 0x9de5, 0x9de6, 0x9de7, 0x9de8, 0x9de9, 0x9dea,
  0x9deb, 0x9dec, 0x9ded, 0x9dee, 0x9def, 0x9df0, 0x9df1, 0x9df2,
  0x9df3, 0x9df4, 0x9df5, 0x9df6, 0x9df7, 0x9df8, 0x9df9, 0x9dfa,
  0x9dfb, 0x9dfc, 0x9dfd, 0x9dfe, 0x9e40, 0x9e41, 0x9e42, 0x9e43,
  0x9e44, 0x9e45, 0x9e46, 0x9e47, 0x9e48, 0x9e49, 0x9e4a, 0x9e4b,
  0x9e4c, 0x9e4d, 0x9e4e, 0x9e4f, 0x9e50, 0x9e51, 0x9e52, 0x9e53,
  0x9e54, 0x9e55, 0x9e56, 0x9e57, 0x9e58, 0x9e59, 0x9e5a, 0x9e5b,
  0x9e5c, 0x9e5d, 0x9e5e, 0x9e5f, 0x9e60, 0x9e61, 0x9e62, 0x9e63,
  0x9e64, 0x9e65, 0x9e66, 0x9e67, 0x9e68, 0x9e69, 0x9e6a, 0x9e6b,
  0x9e6c, 0x9e6d, 0x9e6e, 0x9e6f, 0x9e70, 0x9e71, 0x9e72, 0x9e73,
  0x9e74, 0x9e75, 0x9e76, 0x9e77, 0x9e78, 0x9e79, 0x9e7a, 0x9e7b,
  0x9e7c, 0x9e7d, 0x9e7e, 0x9e80, 0x9e81, 0x9e82, 0x9e83, 0x9e84,
  0x9e85, 0x9e86, 0x9e87, 0x9e88, 0x9e89, 0x9e8a, 0x9e8b, 0x9e8c,
  0x9e8d, 0x9e8e, 0x9e8f, 0x9e90, 0x9e91, 0x9e92, 0x9e93, 0x9e94,
  0x9e95, 0x9e96, 0x9e97, 0x9e98, 0x9e99, 0x9e9a, 0x9e9b, 0x9e9c,
  0x9e9d, 0x9e9e, 0x9e9f, 0x9ea0, 0x9ea1, 0x9ea2, 0x9ea3, 0x9ea4,
  0x9ea5, 0x9ea6, 0x9ea7, 0x9ea8, 0x9ea9, 0x9eaa, 0x9eab, 0x9eac,
  0x9ead, 0x9eae, 0x9eaf, 0x9eb0, 0x9eb1, 0x9eb2, 0x9eb3, 0x9eb4,
  0x9eb5, 0x9eb6, 0x9eb7, 0x9eb8, 0x9eb9, 0x9eba, 0x9ebb, 0x9ebc,
  0x9ebd, 0x9ebe, 0x9ebf, 0x9ec0, 0x9ec1, 0x9ec2, 0x9ec3, 0x9ec4,
  0x9ec5, 0x9ec6, 0x9ec7, 0x9ec8, 0x9ec9, 0x9eca, 0x9ecb, 0x9ecc,
  0x9ecd, 0x9ece, 0x9ecf, 0x9ed0, 0x9ed1, 0x9ed2, 0x9ed3, 0x9ed4,
  0x9ed5, 0x9ed6, 0x9ed7, 0x9ed8, 0x9ed9, 0x9eda, 0x9edb, 0x9edc,
  0x9edd, 0x9ede, 0x9edf, 0x9ee0, 0x9ee1, 0x9ee2, 0x9ee3, 0x9ee4,
  0x9ee5, 0x9ee6, 0x9ee7, 0x9ee8, 0x9ee9, 0x9eea, 0x9eeb, 0x9eec,
  0x9eed, 0x9eee, 0x9eef, 0x9ef0, 0x9ef1, 0x9ef2, 0x9ef3, 0x9ef4,
  0x9ef5, 0x9ef6, 0x9ef7, 0x9ef8, 0x9ef9, 0x9efa, 0x9efb, 0x9efc,
  0x9efd, 0x9efe, 0x9f40, 0x9f41, 0x9f42, 0x9f43, 0x9f44, 0x9f45,
  0x9f46, 0x9f47, 0x9f48, 0x9f49, 0x9f4a, 0x9f4b, 0x9f4c, 0x9f4d,
  0x9f4e, 0x9f4f, 0x9f50, 0x9f51, 0x9f52, 0x9f53, 0x9f54, 0x9f55,
  0x9f56, 0x9f57, 0x9f58, 0x9f59, 0x9f5a, 0x9f5b, 0x9f5c, 0x9f5d,
  0x9f5e, 0x9f5f, 0x9f60, 0x9f61, 0x9f62, 0x9f63, 0x9f64, 0x9f65,
  0x9f66, 0x9f67, 0x9f68, 0x9f69, 0x9f6a, 0x9f6b, 0x9f6c, 0x9f6d,
  0x9f6e, 0x9f6f, 0x9f70, 0x9f71, 0x9f72, 0x9f73, 0x9f74, 0x9f75,
  0x9f76, 0x9f77, 0x9f78, 0x9f79, 0x9f7a, 0x9f7b, 0x9f7c, 0x9f7d,
  0x9f7e, 0x9f80, 0x9f81, 0x9f82, 0x9f83, 0x9f84, 0x9f85, 0x9f86,
  0x9f87, 0x9f88, 0x9f89, 0x9f8a, 0x9f8b, 0x9f8c, 0x9f8d, 0x9f8e,
  0x9f8f, 0x9f90, 0x9f91, 0x9f92, 0x9f93, 0x9f94, 0x9f95, 0x9f96,
  0x9f97, 0x9f98, 0x9f99, 0x9f9a, 0x9f9b, 0x9f9c, 0x9f9d, 0x9f9e,
  0x9f9f, 0x9fa0, 0x9fa1, 0x9fa2, 0x9fa3, 0x9fa4, 0x9fa5, 0x9fa6,
  0x9fa7, 0x9fa8, 0x9fa9, 0x9faa, 0x9fab, 0x9fac, 0x9fad, 0x9fae,
  0x9faf, 0x9fb0, 0x9fb1, 0x9fb2, 0x9fb3, 0x9fb4, 0x9fb5, 0x9fb6,
  0x9fb7, 0x9fb8, 0x9fb9, 0x9fba, 0x9fbb, 0x9fbc, 0x9fbd, 0x9fbe,
  0x9fbf, 0x9fc0, 0x9fc1, 0x9fc2, 0x9fc3, 0x9fc4, 0x9fc5, 0x9fc6,
  0x9fc7, 0x9fc8, 0x9fc9, 0x9fca, 0x9fcb, 0x9fcc, 0x9fcd, 0x9fce,
  0x9fcf, 0x9fd0, 0x9fd1, 0x9fd2, 0x9fd3, 0x9fd4, 0x9fd5, 0x9fd6,
  0x9fd7, 0x9fd8, 0x9fd9, 0x9fda, 0x9fdb, 0x9fdc, 0x9fdd, 0x9fde,
  0x9fdf, 0x9fe0, 0x9fe1, 0x9fe2, 0x9fe3, 0x9fe4, 0x9fe5, 0x9fe6,
  0x9fe7, 0x9fe8, 0x9fe9, 0x9fea, 0x9feb, 0x9fec, 0x9fed, 0x9fee,
  0x9fef, 0x9ff0, 0x9ff1, 0x9ff2, 0x9ff3, 0x9ff4, 0x9ff5, 0x9ff6,
  0x9ff7, 0x9ff8, 0x9ff9, 0x9ffa, 0x9ffb, 0x9ffc, 0x9ffd, 0x9ffe,
  0xa040, 0xa041, 0xa042, 0xa043, 0xa044, 0xa045, 0xa046, 0xa047,
  0xa048, 0xa049, 0xa04a, 0xa04b, 0xa04c, 0xa04d, 0xa04e, 0xa04f,
  0xa050, 0xa051, 0xa052, 0xa053, 0xa054, 0xa055, 0xa056, 0xa057,
  0xa058, 0xa059, 0xa05a, 0xa05b, 0xa05c, 0xa05d, 0xa05e, 0xa05f,
  0xa060, 0xa061, 0xa062, 0xa063, 0xa064, 0xa065, 0xa066, 0xa067,
  0xa068, 0xa069, 0xa06a, 0xa06b, 0xa06c, 0xa06d, 0xa06e, 0xa06f,
  0xa070, 0xa071, 0xa072, 0xa073, 0xa074, 0xa075, 0xa076, 0xa077,
  0xa078, 0xa079, 0xa07a, 0xa07b, 0xa07c, 0xa07d, 0xa07e, 0xa080,
  0xa081, 0xa082, 0xa083, 0xa084, 0xa085, 0xa086, 0xa087, 0xa088,
  0xa089, 0xa08a, 0xa08b, 0xa08c, 0xa08d, 0xa08e, 0xa08f, 0xa090,
  0xa091, 0xa092, 0xa093, 0xa094, 0xa095, 0xa096, 0xa097, 0xa098,
  0xa099, 0xa09a, 0xa09b, 0xa09c, 0xa09d, 0xa09e, 0xa09f, 0xa0a0,
  0xa0a1, 0xa0a2, 0xa0a3, 0xa0a4, 0xa0a5, 0xa0a6, 0xa0a7, 0xa0a8,
  0xa0a9, 0xa0aa, 0xa0ab, 0xa0ac, 0xa0ad, 0xa0ae, 0xa0af, 0xa0b0,
  0xa0b1, 0xa0b2, 0xa0b3, 0xa0b4, 0xa0b5, 0xa0b6, 0xa0b7, 0xa0b8,
  0xa0b9, 0xa0ba, 0xa0bb, 0xa0bc, 0xa0bd, 0xa0be, 0xa0bf, 0xa0c0,
  0xa0c1, 0xa0c2, 0xa0c3, 0xa0c4, 0xa0c5, 0xa0c6, 0xa0c7, 0xa0c8,
  0xa0c9, 0xa0ca, 0xa0cb, 0xa0cc, 0xa0cd, 0xa0ce, 0xa0cf, 0xa0d0,
  0xa0d1, 0xa0d2, 0xa0d3, 0xa0d4, 0xa0d5, 0xa0d6, 0xa0d7, 0xa0d8,
  0xa0d9, 0xa0da, 0xa0db, 0xa0dc, 0xa0dd, 0xa0de, 0xa0df, 0xa0e0,
  0xa0e1, 0xa0e2, 0xa0e3, 0xa0e4, 0xa0e5, 0xa0e6, 0xa0e7, 0xa0e8,
  0xa0e9, 0xa0ea, 0xa0eb, 0xa0ec, 0xa0ed, 0xa0ee, 0xa0ef, 0xa0f0,
  0xa0f1, 0xa0f2, 0xa0f3, 0xa0f4, 0xa0f5, 0xa0f6, 0xa0f7, 0xa0f8,
  0xa0f9, 0xa0fa, 0xa0fb, 0xa0fc, 0xa0fd, 0xa0fe, 0xaa40, 0xaa41,
  0xaa42, 0xaa43, 0xaa44, 0xaa45, 0xaa46, 0xaa47, 0xaa48, 0xaa49,
  0xaa4a, 0xaa4b, 0xaa4c, 0xaa4d, 0xaa4e, 0xaa4f, 0xaa50, 0xaa51,
  0xaa52, 0xaa53, 0xaa54, 0xaa55, 0xaa56, 0xaa57, 0xaa58, 0xaa59,
  0xaa5a, 0xaa5b, 0xaa5c, 0xaa5d, 0xaa5e, 0xaa5f, 0xaa60, 0xaa61,
  0xaa62, 0xaa63, 0xaa64, 0xaa65, 0xaa66, 0xaa67, 0xaa68, 0xaa69,
  0xaa6a, 0xaa6b, 0xaa6c, 0xaa6d, 0xaa6e, 0xaa6f, 0xaa70, 0xaa71,
  0xaa72, 0xaa73, 0xaa74, 0xaa75, 0xaa76, 0xaa77, 0xaa78, 0xaa79,
  0xaa7a, 0xaa7b, 0xaa7c, 0xaa7d, 0xaa7e, 0xaa80, 0xaa81, 0xaa82,
  0xaa83, 0xaa84, 0xaa85, 0xaa86, 0xaa87, 0xaa88, 0xaa89, 0xaa8a,
  0xaa8b, 0xaa8c, 0xaa8d, 0xaa8e, 0xaa8f, 0xaa90, 0xaa91, 0xaa92,
  0xaa93, 0xaa94, 0xaa95, 0xaa96, 0xaa97, 0xaa98, 0xaa99, 0xaa9a,
  0xaa9b, 0xaa9c, 0xaa9d, 0xaa9e, 0xaa9f, 0xaaa0, 0xab40, 0xab41,
  0xab42, 0xab43, 0xab44, 0xab45, 0xab46, 0xab47, 0xab48, 0xab49,
  0xab4a, 0xab4b, 0xab4c, 0xab4d, 0xab4e, 0xab4f, 0xab50, 0xab51,
  0xab52, 0xab53, 0xab54, 0xab55, 0xab56, 0xab57, 0xab58, 0xab59,
  0xab5a, 0xab5b, 0xab5c, 0xab5d, 0xab5e, 0xab5f, 0xab60, 0xab61,
  0xab62, 0xab63, 0xab64, 0xab65, 0xab66, 0xab67, 0xab68, 0xab69,
  0xab6a, 0xab6b, 0xab6c, 0xab6d, 0xab6e, 0xab6f, 0xab70, 0xab71,
  0xab72, 0xab73, 0xab74, 0xab75, 0xab76, 0xab77, 0xab78, 0xab79,
  0xab7a, 0xab7b, 0xab7c, 0xab7d, 0xab7e, 0xab80, 0xab81, 0xab82,
  0xab83, 0xab84, 0xab85, 0xab86, 0xab87, 0xab88, 0xab89, 0xab8a,
  0xab8b, 0xab8c, 0xab8d, 0xab8e, 0xab8f, 0xab90, 0xab91, 0xab92,
  0xab93, 0xab94, 0xab95, 0xab96, 0xab97, 0xab98, 0xab99, 0xab9a,
  0xab9b, 0xab9c, 0xab9d, 0xab9e, 0xab9f, 0xaba0, 0xac40, 0xac41,
  0xac42, 0xac43, 0xac44, 0xac45, 0xac46, 0xac47, 0xac48, 0xac49,
  0xac4a, 0xac4b, 0xac4c, 0xac4d, 0xac4e, 0xac4f, 0xac50, 0xac51,
  0xac52, 0xac53, 0xac54, 0xac55, 0xac56, 0xac57, 0xac58, 0xac59,
  0xac5a, 0xac5b, 0xac5c, 0xac5d, 0xac5e, 0xac5f, 0xac60, 0xac61,
  0xac62, 0xac63, 0xac64, 0xac65, 0xac66, 0xac67, 0xac68, 0xac69,
  0xac6a, 0xac6b, 0xac6c, 0xac6d, 0xac6e, 0xac6f, 0xac70, 0xac71,
  0xac72, 0xac73, 0xac74, 0xac75, 0xac76, 0xac77, 0xac78, 0xac79,
  0xac7a, 0xac7b, 0xac7c, 0xac7d, 0xac7e, 0xac80, 0xac81, 0xac82,
  0xac83, 0xac84, 0xac85, 0xac86, 0xac87, 0xac88, 0xac89, 0xac8a,
  0xac8b, 0xac8c, 0xac8d, 0xac8e, 0xac8f, 0xac90, 0xac91, 0xac92,
  0xac93, 0xac94, 0xac95, 0xac96, 0xac97, 0xac98, 0xac99, 0xac9a,
  0xac9b, 0xac9c, 0xac9d, 0xac9e, 0xac9f, 0xaca0, 0xad40, 0xad41,
  0xad42, 0xad43, 0xad44, 0xad45, 0xad46, 0xad47, 0xad48, 0xad49,
  0xad4a, 0xad4b, 0xad4c, 0xad4d, 0xad4e, 0xad4f, 0xad50, 0xad51,
  0xad52, 0xad53, 0xad54, 0xad55, 0xad56, 0xad57, 0xad58, 0xad59,
  0xad5a, 0xad5b, 0xad5c, 0xad5d, 0xad5e, 0xad5f, 0xad60, 0xad61,
  0xad62, 0xad63, 0xad64, 0xad65, 0xad66, 0xad67, 0xad68, 0xad69,
  0xad6a, 0xad6b, 0xad6c, 0xad6d, 0xad6e, 0xad6f, 0xad70, 0xad71,
  0xad72, 0xad73, 0xad74, 0xad75, 0xad76, 0xad77, 0xad78, 0xad79,
  0xad7a, 0xad7b, 0xad7c, 0xad7d, 0xad7e, 0xad80, 0xad81, 0xad82,
  0xad83, 0xad84, 0xad85, 0xad86, 0xad87, 0xad88, 0xad89, 0xad8a,
  0xad8b, 0xad8c, 0xad8d, 0xad8e, 0xad8f, 0xad90, 0xad91, 0xad92,
  0xad93, 0xad94, 0xad95, 0xad96, 0xad97, 0xad98, 0xad99, 0xad9a,
  0xad9b, 0xad9c, 0xad9d, 0xad9e, 0xad9f, 0xada0, 0xae40, 0xae41,
  0xae42, 0xae43, 0xae44, 0xae45, 0xae46, 0xae47, 0xae48, 0xae49,
  0xae4a, 0xae4b, 0xae4c, 0xae4d, 0xae4e, 0xae4f, 0xae50, 0xae51,
  0xae52, 0xae53, 0xae54, 0xae55, 0xae56, 0xae57, 0xae58, 0xae59,
  0xae5a, 0xae5b, 0xae5c, 0xae5d, 0xae5e, 0xae5f, 0xae60, 0xae61,
  0xae62, 0xae63, 0xae64, 0xae65, 0xae66, 0xae67, 0xae68, 0xae69,
  0xae6a, 0xae6b, 0xae6c, 0xae6d, 0xae6e, 0xae6f, 0xae70, 0xae71,
  0xae72, 0xae73, 0xae74, 0xae75, 0xae76, 0xae77, 0xae78, 0xae79,
  0xae7a, 0xae7b, 0xae7c, 0xae7d, 0xae7e, 0xae80, 0xae81, 0xae82,
  0xae83, 0xae84, 0xae85, 0xae86, 0xae87, 0xae88, 0xae89, 0xae8a,
  0xae8b, 0xae8c, 0xae8d, 0xae8e, 0xae8f, 0xae90, 0xae91, 0xae92,
  0xae93, 0xae94, 0xae95, 0xae96, 0xae97, 0xae98, 0xae99, 0xae9a,
  0xae9b, 0xae9c, 0xae9d, 0xae9e, 0xae9f, 0xaea0, 0xaf40, 0xaf41,
  0xaf42, 0xaf43, 0xaf44, 0xaf45, 0xaf46, 0xaf47, 0xaf48, 0xaf49,
  0xaf4a, 0xaf4b, 0xaf4c, 0xaf4d, 0xaf4e, 0xaf4f, 0xaf50, 0xaf51,
  0xaf52, 0xaf53, 0xaf54, 0xaf55, 0xaf56, 0xaf57, 0xaf58, 0xaf59,
  0xaf5a, 0xaf5b, 0xaf5c, 0xaf5d, 0xaf5e, 0xaf5f, 0xaf60, 0xaf61,
  0xaf62, 0xaf63, 0xaf64, 0xaf65, 0xaf66, 0xaf67, 0xaf68, 0xaf69,
  0xaf6a, 0xaf6b, 0xaf6c, 0xaf6d, 0xaf6e, 0xaf6f, 0xaf70, 0xaf71,
  0xaf72, 0xaf73, 0xaf74, 0xaf75, 0xaf76, 0xaf77, 0xaf78, 0xaf79,
  0xaf7a, 0xaf7b, 0xaf7c, 0xaf7d, 0xaf7e, 0xaf80, 0xaf81, 0xaf82,
  0xaf83, 0xaf84, 0xaf85, 0xaf86, 0xaf87, 0xaf88, 0xaf89, 0xaf8a,
  0xaf8b, 0xaf8c, 0xaf8d, 0xaf8e, 0xaf8f, 0xaf90, 0xaf91, 0xaf92,
  0xaf93, 0xaf94, 0xaf95, 0xaf96, 0xaf97, 0xaf98, 0xaf99, 0xaf9a,
  0xaf9b, 0xaf9c, 0xaf9d, 0xaf9e, 0xaf9f, 0xafa0, 0xb040, 0xb041,
  0xb042, 0xb043, 0xb044, 0xb045, 0xb046, 0xb047, 0xb048, 0xb049,
  0xb04a, 0xb04b, 0xb04c, 0xb04d, 0xb04e, 0xb04f, 0xb050, 0xb051,
  0xb052, 0xb053, 0xb054, 0xb055, 0xb056, 0xb057, 0xb058, 0xb059,
  0xb05a, 0xb05b, 0xb05c, 0xb05d, 0xb05e, 0xb05f, 0xb060, 0xb061,
  0xb062, 0xb063, 0xb064, 0xb065, 0xb066, 0xb067, 0xb068, 0xb069,
  0xb06a, 0xb06b, 0xb06c, 0xb06d, 0xb06e, 0xb06f, 0xb070, 0xb071,
  0xb072, 0xb073, 0xb074, 0xb075, 0xb076, 0xb077, 0xb078, 0xb079,
  0xb07a, 0xb07b, 0xb07c, 0xb07d, 0xb07e, 0xb080, 0xb081, 0xb082,
  0xb083, 0xb084, 0xb085, 0xb086, 0xb087, 0xb088, 0xb089, 0xb08a,
  0xb08b, 0xb08c, 0xb08d, 0xb08e, 0xb08f, 0xb090, 0xb091, 0xb092,
  0xb093, 0xb094, 0xb095, 0xb096, 0xb097, 0xb098, 0xb099, 0xb09a,
  0xb09b, 0xb09c, 0xb09d, 0xb09e, 0xb09f, 0xb0a0, 0xb140, 0xb141,
  0xb142, 0xb143, 0xb144, 0xb145, 0xb146, 0xb147, 0xb148, 0xb149,
  0xb14a, 0xb14b, 0xb14c, 0xb14d, 0xb14e, 0xb14f, 0xb150, 0xb151,
  0xb152, 0xb153, 0xb154, 0xb155, 0xb156, 0xb157, 0xb158, 0xb159,
  0xb15a, 0xb15b, 0xb15c, 0xb15d, 0xb15e, 0xb15f, 0xb160, 0xb161,
  0xb162, 0xb163, 0xb164, 0xb165, 0xb166, 0xb167, 0xb168, 0xb169,
  0xb16a, 0xb16b, 0xb16c, 0xb16d, 0xb16e, 0xb16f, 0xb170, 0xb171,
  0xb172, 0xb173, 0xb174, 0xb175, 0xb176, 0xb177, 0xb178, 0xb179,
  0xb17a, 0xb17b, 0xb17c, 0xb17d, 0xb17e, 0xb180, 0xb181, 0xb182,
  0xb183, 0xb184, 0xb185, 0xb186, 0xb187, 0xb188, 0xb189, 0xb18a,
  0xb18b, 0xb18c, 0xb18d, 0xb18e, 0xb18f, 0xb190, 0xb191, 0xb192,
  0xb193, 0xb194, 0xb195, 0xb196, 0xb197, 0xb198, 0xb199, 0xb19a,
  0xb19b, 0xb19c, 0xb19d, 0xb19e, 0xb19f, 0xb1a0, 0xb240, 0xb241,
  0xb242, 0xb243, 0xb244, 0xb245, 0xb246, 0xb247, 0xb248, 0xb249,
  0xb24a, 0xb24b, 0xb24c, 0xb24d, 0xb24e, 0xb24f, 0xb250, 0xb251,
  0xb252, 0xb253, 0xb254, 0xb255, 0xb256, 0xb257, 0xb258, 0xb259,
  0xb25a, 0xb25b, 0xb25c, 0xb25d, 0xb25e, 0xb25f, 0xb260, 0xb261,
  0xb262, 0xb263, 0xb264, 0xb265, 0xb266, 0xb267, 0xb268, 0xb269,
  0xb26a, 0xb26b, 0xb26c, 0xb26d, 0xb26e, 0xb26f, 0xb270, 0xb271,
  0xb272, 0xb273, 0xb274, 0xb275, 0xb276, 0xb277, 0xb278, 0xb279,
  0xb27a, 0xb27b, 0xb27c, 0xb27d, 0xb27e, 0xb280, 0xb281, 0xb282,
  0xb283, 0xb284, 0xb285, 0xb286, 0xb287, 0xb288, 0xb289, 0xb28a,
  0xb28b, 0xb28c, 0xb28d, 0xb28e, 0xb28f, 0xb290, 0xb291, 0xb292,
  0xb293, 0xb294, 0xb295, 0xb296, 0xb297, 0xb298, 0xb299, 0xb29a,
  0xb29b, 0xb29c, 0xb29d, 0xb29e, 0xb29f, 0xb2a0, 0xb340, 0xb341,
  0xb342, 0xb343, 0xb344, 0xb345, 0xb346, 0xb347, 0xb348, 0xb349,
  0xb34a, 0xb34b, 0xb34c, 0xb34d, 0xb34e, 0xb34f, 0xb350, 0xb351,
  0xb352, 0xb353, 0xb354, 0xb355, 0xb356, 0xb357, 0xb358, 0xb359,
  0xb35a, 0xb35b, 0xb35c, 0xb35d, 0xb35e, 0xb35f, 0xb360, 0xb361,
  0xb362, 0xb363, 0xb364, 0xb365, 0xb366, 0xb367, 0xb368, 0xb369,
  0xb36a, 0xb36b, 0xb36c, 0xb36d, 0xb36e, 0xb36f, 0xb370, 0xb371,
  0xb372, 0xb373, 0xb374, 0xb375, 0xb376, 0xb377, 0xb378, 0xb379,
  0xb37a, 0xb37b, 0xb37c, 0xb37d, 0xb37e, 0xb380, 0xb381, 0xb382,
  0xb383, 0xb384, 0xb385, 0xb386, 0xb387, 0xb388, 0xb389, 0xb38a,
  0xb38b, 0xb38c, 0xb38d, 0xb38e, 0xb38f, 0xb390, 0xb391, 0xb392,
  0xb393, 0xb394, 0xb395, 0xb396, 0xb397, 0xb398, 0xb399, 0xb39a,
  0xb39b, 0xb39c, 0xb39d, 0xb39e, 0xb39f, 0xb3a0, 0xb440, 0xb441,
  0xb442, 0xb443, 0xb444, 0xb445, 0xb446, 0xb447, 0xb448, 0xb449,
  0xb44a, 0xb44b, 0xb44c, 0xb44d, 0xb44e, 0xb44f, 0xb450, 0xb451,
  0xb452, 0xb453, 0xb454, 0xb455, 0xb456, 0xb457, 0xb458, 0xb459,
  0xb45a, 0xb45b, 0xb45c, 0xb45d, 0xb45e, 0xb45f, 0xb460, 0xb461,
  0xb462, 0xb463, 0xb464, 0xb465, 0xb466, 0xb467, 0xb468, 0xb469,
  0xb46a, 0xb46b, 0xb46c, 0xb46d, 0xb46e, 0xb46f, 0xb470, 0xb471,
  0xb472, 0xb473, 0xb474, 0xb475, 0xb476, 0xb477, 0xb478, 0xb479,
  0xb47a, 0xb47b, 0xb47c, 0xb47d, 0xb47e, 0xb480, 0xb481, 0xb482,
  0xb483, 0xb484, 0xb485, 0xb486, 0xb487, 0xb488, 0xb489, 0xb48a,
  0xb48b, 0xb48c, 0xb48d, 0xb48e, 0xb48f, 0xb490, 0xb491, 0xb492,
  0xb493, 0xb494, 0xb495, 0xb496, 0xb497, 0xb498, 0xb499, 0xb49a,
  0xb49b, 0xb49c, 0xb49d, 0xb49e, 0xb49f, 0xb4a0, 0xb540, 0xb541,
  0xb542, 0xb543, 0xb544, 0xb545, 0xb546, 0xb547, 0xb548, 0xb549,
  0xb54a, 0xb54b, 0xb54c, 0xb54d, 0xb54e, 0xb54f, 0xb550, 0xb551,
  0xb552, 0xb553, 0xb554, 0xb555, 0xb556, 0xb557, 0xb558, 0xb559,
  0xb55a, 0xb55b, 0xb55c, 0xb55d, 0xb55e, 0xb55f, 0xb560, 0xb561,
  0xb562, 0xb563, 0xb564, 0xb565, 0xb566, 0xb567, 0xb568, 0xb569,
  0xb56a, 0xb56b, 0xb56c, 0xb56d, 0xb56e, 0xb56f, 0xb570, 0xb571,
  0xb572, 0xb573, 0xb574, 0xb575, 0xb576, 0xb577, 0xb578, 0xb579,
  0xb57a, 0xb57b, 0xb57c, 0xb57d, 0xb57e, 0xb580, 0xb581, 0xb582,
  0xb583, 0xb584, 0xb585, 0xb586, 0xb587, 0xb588, 0xb589, 0xb58a,
  0xb58b, 0xb58c, 0xb58d, 0xb58e, 0xb58f, 0xb590, 0xb591, 0xb592,
  0xb593, 0xb594, 0xb595, 0xb596, 0xb597, 0xb598, 0xb599, 0xb59a,
  0xb59b, 0xb59c, 0xb59d, 0xb59e, 0xb59f, 0xb5a0, 0xb640, 0xb641,
  0xb642, 0xb643, 0xb644, 0xb645, 0xb646, 0xb647, 0xb648, 0xb649,
  0xb64a, 0xb64b, 0xb64c, 0xb64d, 0xb64e, 0xb64f, 0xb650, 0xb651,
  0xb652, 0xb653, 0xb654, 0xb655, 0xb656, 0xb657, 0xb658, 0xb659,
  0xb65a, 0xb65b, 0xb65c, 0xb65d, 0xb65e, 0xb65f, 0xb660, 0xb661,
  0xb662, 0xb663, 0xb664, 0xb665, 0xb666, 0xb667, 0xb668, 0xb669,
  0xb66a, 0xb66b, 0xb66c, 0xb66d, 0xb66e, 0xb66f, 0xb670, 0xb671,
  0xb672, 0xb673, 0xb674, 0xb675, 0xb676, 0xb677, 0xb678, 0xb679,
  0xb67a, 0xb67b, 0xb67c, 0xb67d, 0xb67e, 0xb680, 0xb681, 0xb682,
  0xb683, 0xb684, 0xb685, 0xb686, 0xb687, 0xb688, 0xb689, 0xb68a,
  0xb68b, 0xb68c, 0xb68d, 0xb68e, 0xb68f, 0xb690, 0xb691, 0xb692,
  0xb693, 0xb694, 0xb695, 0xb696, 0xb697, 0xb698, 0xb699, 0xb69a,
  0xb69b, 0xb69c, 0xb69d, 0xb69e, 0xb69f, 0xb6a0, 0xb740, 0xb741,
  0xb742, 0xb743, 0xb744, 0xb745, 0xb746, 0xb747, 0xb748, 0xb749,
  0xb74a, 0xb74b, 0xb74c, 0xb74d, 0xb74e, 0xb74f, 0xb750, 0xb751,
  0xb752, 0xb753, 0xb754, 0xb755, 0xb756, 0xb757, 0xb758, 0xb759,
  0xb75a, 0xb75b, 0xb75c, 0xb75d, 0xb75e, 0xb75f, 0xb760, 0xb761,
  0xb762, 0xb763, 0xb764, 0xb765, 0xb766, 0xb767, 0xb768, 0xb769,
  0xb76a, 0xb76b, 0xb76c, 0xb76d, 0xb76e, 0xb76f, 0xb770, 0xb771,
  0xb772, 0xb773, 0xb774, 0xb775, 0xb776, 0xb777, 0xb778, 0xb779,
  0xb77a, 0xb77b, 0xb77c, 0xb77d, 0xb77e, 0xb780, 0xb781, 0xb782,
  0xb783, 0xb784, 0xb785, 0xb786, 0xb787, 0xb788, 0xb789, 0xb78a,
  0xb78b, 0xb78c, 0xb78d, 0xb78e, 0xb78f, 0xb790, 0xb791, 0xb792,
  0xb793, 0xb794, 0xb795, 0xb796, 0xb797, 0xb798, 0xb799, 0xb79a,
  0xb79b, 0xb79c, 0xb79d, 0xb79e, 0xb79f, 0xb7a0, 0xb840, 0xb841,
  0xb842, 0xb843, 0xb844, 0xb845, 0xb846, 0xb847, 0xb848, 0xb849,
  0xb84a, 0xb84b, 0xb84c, 0xb84d, 0xb84e, 0xb84f, 0xb850, 0xb851,
  0xb852, 0xb853, 0xb854, 0xb855, 0xb856, 0xb857, 0xb858, 0xb859,
  0xb85a, 0xb85b, 0xb85c, 0xb85d, 0xb85e, 0xb85f, 0xb860, 0xb861,
  0xb862, 0xb863, 0xb864, 0xb865, 0xb866, 0xb867, 0xb868, 0xb869,
  0xb86a, 0xb86b, 0xb86c, 0xb86d, 0xb86e, 0xb86f, 0xb870, 0xb871,
  0xb872, 0xb873, 0xb874, 0xb875, 0xb876, 0xb877, 0xb878, 0xb879,
  0xb87a, 0xb87b, 0xb87c, 0xb87d, 0xb87e, 0xb880, 0xb881, 0xb882,
  0xb883, 0xb884, 0xb885, 0xb886, 0xb887, 0xb888, 0xb889, 0xb88a,
  0xb88b, 0xb88c, 0xb88d, 0xb88e, 0xb88f, 0xb890, 0xb891, 0xb892,
  0xb893, 0xb894, 0xb895, 0xb896, 0xb897, 0xb898, 0xb899, 0xb89a,
  0xb89b, 0xb89c, 0xb89d, 0xb89e, 0xb89f, 0xb8a0, 0xb940, 0xb941,
  0xb942, 0xb943, 0xb944, 0xb945, 0xb946, 0xb947, 0xb948, 0xb949,
  0xb94a, 0xb94b, 0xb94c, 0xb94d, 0xb94e, 0xb94f, 0xb950, 0xb951,
  0xb952, 0xb953, 0xb954, 0xb955, 0xb956, 0xb957, 0xb958, 0xb959,
  0xb95a, 0xb95b, 0xb95c, 0xb95d, 0xb95e, 0xb95f, 0xb960, 0xb961,
  0xb962, 0xb963, 0xb964, 0xb965, 0xb966, 0xb967, 0xb968, 0xb969,
  0xb96a, 0xb96b, 0xb96c, 0xb96d, 0xb96e, 0xb96f, 0xb970, 0xb971,
  0xb972, 0xb973, 0xb974, 0xb975, 0xb976, 0xb977, 0xb978, 0xb979,
  0xb97a, 0xb97b, 0xb97c, 0xb97d, 0xb97e, 0xb980, 0xb981, 0xb982,
  0xb983, 0xb984, 0xb985, 0xb986, 0xb987, 0xb988, 0xb989, 0xb98a,
  0xb98b, 0xb98c, 0xb98d, 0xb98e, 0xb98f, 0xb990, 0xb991, 0xb992,
  0xb993, 0xb994, 0xb995, 0xb996, 0xb997, 0xb998, 0xb999, 0xb99a,
  0xb99b, 0xb99c, 0xb99d, 0xb99e, 0xb99f, 0xb9a0, 0xba40, 0xba41,
  0xba42, 0xba43, 0xba44, 0xba45, 0xba46, 0xba47, 0xba48, 0xba49,
  0xba4a, 0xba4b, 0xba4c, 0xba4d, 0xba4e, 0xba4f, 0xba50, 0xba51,
  0xba52, 0xba53, 0xba54, 0xba55, 0xba56, 0xba57, 0xba58, 0xba59,
  0xba5a, 0xba5b, 0xba5c, 0xba5d, 0xba5e, 0xba5f, 0xba60, 0xba61,
  0xba62, 0xba63, 0xba64, 0xba65, 0xba66, 0xba67, 0xba68, 0xba69,
  0xba6a, 0xba6b, 0xba6c, 0xba6d, 0xba6e, 0xba6f, 0xba70, 0xba71,
  0xba72, 0xba73, 0xba74, 0xba75, 0xba76, 0xba77, 0xba78, 0xba79,
  0xba7a, 0xba7b, 0xba7c, 0xba7d, 0xba7e, 0xba80, 0xba81, 0xba82,
  0xba83, 0xba84, 0xba85, 0xba86, 0xba87, 0xba88, 0xba89, 0xba8a,
  0xba8b, 0xba8c, 0xba8d, 0xba8e, 0xba8f, 0xba90, 0xba91, 0xba92,
  0xba93, 0xba94, 0xba95, 0xba96, 0xba97, 0xba98, 0xba99, 0xba9a,
  0xba9b, 0xba9c, 0xba9d, 0xba9e, 0xba9f, 0xbaa0, 0xbb40, 0xbb41,
  0xbb42, 0xbb43, 0xbb44, 0xbb45, 0xbb46, 0xbb47, 0xbb48, 0xbb49,
  0xbb4a, 0xbb4b, 0xbb4c, 0xbb4d, 0xbb4e, 0xbb4f, 0xbb50, 0xbb51,
  0xbb52, 0xbb53, 0xbb54, 0xbb55, 0xbb56, 0xbb57, 0xbb58, 0xbb59,
  0xbb5a, 0xbb5b, 0xbb5c, 0xbb5d, 0xbb5e, 0xbb5f, 0xbb60, 0xbb61,
  0xbb62, 0xbb63, 0xbb64, 0xbb65, 0xbb66, 0xbb67, 0xbb68, 0xbb69,
  0xbb6a, 0xbb6b, 0xbb6c, 0xbb6d, 0xbb6e, 0xbb6f, 0xbb70, 0xbb71,
  0xbb72, 0xbb73, 0xbb74, 0xbb75, 0xbb76, 0xbb77, 0xbb78, 0xbb79,
  0xbb7a, 0xbb7b, 0xbb7c, 0xbb7d, 0xbb7e, 0xbb80, 0xbb81, 0xbb82,
  0xbb83, 0xbb84, 0xbb85, 0xbb86, 0xbb87, 0xbb88, 0xbb89, 0xbb8a,
  0xbb8b, 0xbb8c, 0xbb8d, 0xbb8e, 0xbb8f, 0xbb90, 0xbb91, 0xbb92,
  0xbb93, 0xbb94, 0xbb95, 0xbb96, 0xbb97, 0xbb98, 0xbb99, 0xbb9a,
  0xbb9b, 0xbb9c, 0xbb9d, 0xbb9e, 0xbb9f, 0xbba0, 0xbc40, 0xbc41,
  0xbc42, 0xbc43, 0xbc44, 0xbc45, 0xbc46, 0xbc47, 0xbc48, 0xbc49,
  0xbc4a, 0xbc4b, 0xbc4c, 0xbc4d, 0xbc4e, 0xbc4f, 0xbc50, 0xbc51,
  0xbc52, 0xbc53, 0xbc54, 0xbc55, 0xbc56, 0xbc57, 0xbc58, 0xbc59,
  0xbc5a, 0xbc5b, 0xbc5c, 0xbc5d, 0xbc5e, 0xbc5f, 0xbc60, 0xbc61,
  0xbc62, 0xbc63, 0xbc64, 0xbc65, 0xbc66, 0xbc67, 0xbc68, 0xbc69,
  0xbc6a, 0xbc6b, 0xbc6c, 0xbc6d, 0xbc6e, 0xbc6f, 0xbc70, 0xbc71,
  0xbc72, 0xbc73, 0xbc74, 0xbc75, 0xbc76, 0xbc77, 0xbc78, 0xbc79,
  0xbc7a, 0xbc7b, 0xbc7c, 0xbc7d, 0xbc7e, 0xbc80, 0xbc81, 0xbc82,
  0xbc83, 0xbc84, 0xbc85, 0xbc86, 0xbc87, 0xbc88, 0xbc89, 0xbc8a,
  0xbc8b, 0xbc8c, 0xbc8d, 0xbc8e, 0xbc8f, 0xbc90, 0xbc91, 0xbc92,
  0xbc93, 0xbc94, 0xbc95, 0xbc96, 0xbc97, 0xbc98, 0xbc99, 0xbc9a,
  0xbc9b, 0xbc9c, 0xbc9d, 0xbc9e, 0xbc9f, 0xbca0, 0xbd40, 0xbd41,
  0xbd42, 0xbd43, 0xbd44, 0xbd45, 0xbd46, 0xbd47, 0xbd48, 0xbd49,
  0xbd4a, 0xbd4b, 0xbd4c, 0xbd4d, 0xbd4e, 0xbd4f, 0xbd50, 0xbd51,
  0xbd52, 0xbd53, 0xbd54, 0xbd55, 0xbd56, 0xbd57, 0xbd58, 0xbd59,
  0xbd5a, 0xbd5b, 0xbd5c, 0xbd5d, 0xbd5e, 0xbd5f, 0xbd60, 0xbd61,
  0xbd62, 0xbd63, 0xbd64, 0xbd65, 0xbd66, 0xbd67, 0xbd68, 0xbd69,
  0xbd6a, 0xbd6b, 0xbd6c, 0xbd6d, 0xbd6e, 0xbd6f, 0xbd70, 0xbd71,
  0xbd72, 0xbd73, 0xbd74, 0xbd75, 0xbd76, 0xbd77, 0xbd78, 0xbd79,
  0xbd7a, 0xbd7b, 0xbd7c, 0xbd7d, 0xbd7e, 0xbd80, 0xbd81, 0xbd82,
  0xbd83, 0xbd84, 0xbd85, 0xbd86, 0xbd87, 0xbd88, 0xbd89, 0xbd8a,
  0xbd8b, 0xbd8c, 0xbd8d, 0xbd8e, 0xbd8f, 0xbd90, 0xbd91, 0xbd92,
  0xbd93, 0xbd94, 0xbd95, 0xbd96, 0xbd97, 0xbd98, 0xbd99, 0xbd9a,
  0xbd9b, 0xbd9c, 0xbd9d, 0xbd9e, 0xbd9f, 0xbda0, 0xbe40, 0xbe41,
  0xbe42, 0xbe43, 0xbe44, 0xbe45, 0xbe46, 0xbe47, 0xbe48, 0xbe49,
  0xbe4a, 0xbe4b, 0xbe4c, 0xbe4d, 0xbe4e, 0xbe4f, 0xbe50, 0xbe51,
  0xbe52, 0xbe53, 0xbe54, 0xbe55, 0xbe56, 0xbe57, 0xbe58, 0xbe59,
  0xbe5a, 0xbe5b, 0xbe5c, 0xbe5d, 0xbe5e, 0xbe5f, 0xbe60, 0xbe61,
  0xbe62, 0xbe63, 0xbe64, 0xbe65, 0xbe66, 0xbe67, 0xbe68, 0xbe69,
  0xbe6a, 0xbe6b, 0xbe6c, 0xbe6d, 0xbe6e, 0xbe6f, 0xbe70, 0xbe71,
  0xbe72, 0xbe73, 0xbe74, 0xbe75, 0xbe76, 0xbe77, 0xbe78, 0xbe79,
  0xbe7a, 0xbe7b, 0xbe7c, 0xbe7d, 0xbe7e, 0xbe80, 0xbe81, 0xbe82,
  0xbe83, 0xbe84, 0xbe85, 0xbe86, 0xbe87, 0xbe88, 0xbe89, 0xbe8a,
  0xbe8b, 0xbe8c, 0xbe8d, 0xbe8e, 0xbe8f, 0xbe90, 0xbe91, 0xbe92,
  0xbe93, 0xbe94, 0xbe95, 0xbe96, 0xbe97, 0xbe98, 0xbe99, 0xbe9a,
  0xbe9b, 0xbe9c, 0xbe9d, 0xbe9e, 0xbe9f, 0xbea0, 0xbf40, 0xbf41,
  0xbf42, 0xbf43, 0xbf44, 0xbf45, 0xbf46, 0xbf47, 0xbf48, 0xbf49,
  0xbf4a, 0xbf4b, 0xbf4c, 0xbf4d, 0xbf4e, 0xbf4f, 0xbf50, 0xbf51,
  0xbf52, 0xbf53, 0xbf54, 0xbf55, 0xbf56, 0xbf57, 0xbf58, 0xbf59,
  0xbf5a, 0xbf5b, 0xbf5c, 0xbf5d, 0xbf5e, 0xbf5f, 0xbf60, 0xbf61,
  0xbf62, 0xbf63, 0xbf64, 0xbf65, 0xbf66, 0xbf67, 0xbf68, 0xbf69,
  0xbf6a, 0xbf6b, 0xbf6c, 0xbf6d, 0xbf6e, 0xbf6f, 0xbf70, 0xbf71,
  0xbf72, 0xbf73, 0xbf74, 0xbf75, 0xbf76, 0xbf77, 0xbf78, 0xbf79,
  0xbf7a, 0xbf7b, 0xbf7c, 0xbf7d, 0xbf7e, 0xbf80, 0xbf81, 0xbf82,
  0xbf83, 0xbf84, 0xbf85, 0xbf86, 0xbf87, 0xbf88, 0xbf89, 0xbf8a,
  0xbf8b, 0xbf8c, 0xbf8d, 0xbf8e, 0xbf8f, 0xbf90, 0xbf91, 0xbf92,
  0xbf93, 0xbf94, 0xbf95, 0xbf96, 0xbf97, 0xbf98, 0xbf99, 0xbf9a,
  0xbf9b, 0xbf9c, 0xbf9d, 0xbf9e, 0xbf9f, 0xbfa0, 0xc040, 0xc041,
  0xc042, 0xc043, 0xc044, 0xc045, 0xc046, 0xc047, 0xc048, 0xc049,
  0xc04a, 0xc04b, 0xc04c, 0xc04d, 0xc04e, 0xc04f, 0xc050, 0xc051,
  0xc052, 0xc053, 0xc054, 0xc055, 0xc056, 0xc057, 0xc058, 0xc059,
  0xc05a, 0xc05b, 0xc05c, 0xc05d, 0xc05e, 0xc05f, 0xc060, 0xc061,
  0xc062, 0xc063, 0xc064, 0xc065, 0xc066, 0xc067, 0xc068, 0xc069,
  0xc06a, 0xc06b, 0xc06c, 0xc06d, 0xc06e, 0xc06f, 0xc070, 0xc071,
  0xc072, 0xc073, 0xc074, 0xc075, 0xc076, 0xc077, 0xc078, 0xc079,
  0xc07a, 0xc07b, 0xc07c, 0xc07d, 0xc07e, 0xc080, 0xc081, 0xc082,
  0xc083, 0xc084, 0xc085, 0xc086, 0xc087, 0xc088, 0xc089, 0xc08a,
  0xc08b, 0xc08c, 0xc08d, 0xc08e, 0xc08f, 0xc090, 0xc091, 0xc092,
  0xc093, 0xc094, 0xc095, 0xc096, 0xc097, 0xc098, 0xc099, 0xc09a,
  0xc09b, 0xc09c, 0xc09d, 0xc09e, 0xc09f, 0xc0a0, 0xc140, 0xc141,
  0xc142, 0xc143, 0xc144, 0xc145, 0xc146, 0xc147, 0xc148, 0xc149,
  0xc14a, 0xc14b, 0xc14c, 0xc14d, 0xc14e, 0xc14f, 0xc150, 0xc151,
  0xc152, 0xc153, 0xc154, 0xc155, 0xc156, 0xc157, 0xc158, 0xc159,
  0xc15a, 0xc15b, 0xc15c, 0xc15d, 0xc15e, 0xc15f, 0xc160, 0xc161,
  0xc162, 0xc163, 0xc164, 0xc165, 0xc166, 0xc167, 0xc168, 0xc169,
  0xc16a, 0xc16b, 0xc16c, 0xc16d, 0xc16e, 0xc16f, 0xc170, 0xc171,
  0xc172, 0xc173, 0xc174, 0xc175, 0xc176, 0xc177, 0xc178, 0xc179,
  0xc17a, 0xc17b, 0xc17c, 0xc17d, 0xc17e, 0xc180, 0xc181, 0xc182,
  0xc183, 0xc184, 0xc185, 0xc186, 0xc187, 0xc188, 0xc189, 0xc18a,
  0xc18b, 0xc18c, 0xc18d, 0xc18e, 0xc18f, 0xc190, 0xc191, 0xc192,
  0xc193, 0xc194, 0xc195, 0xc196, 0xc197, 0xc198, 0xc199, 0xc19a,
  0xc19b, 0xc19c, 0xc19d, 0xc19e, 0xc19f, 0xc1a0, 0xc240, 0xc241,
  0xc242, 0xc243, 0xc244, 0xc245, 0xc246, 0xc247, 0xc248, 0xc249,
  0xc24a, 0xc24b, 0xc24c, 0xc24d, 0xc24e, 0xc24f, 0xc250, 0xc251,
  0xc252, 0xc253, 0xc254, 0xc255, 0xc256, 0xc257, 0xc258, 0xc259,
  0xc25a, 0xc25b, 0xc25c, 0xc25d, 0xc25e, 0xc25f, 0xc260, 0xc261,
  0xc262, 0xc263, 0xc264, 0xc265, 0xc266, 0xc267, 0xc268, 0xc269,
  0xc26a, 0xc26b, 0xc26c, 0xc26d, 0xc26e, 0xc26f, 0xc270, 0xc271,
  0xc272, 0xc273, 0xc274, 0xc275, 0xc276, 0xc277, 0xc278, 0xc279,
  0xc27a, 0xc27b, 0xc27c, 0xc27d, 0xc27e, 0xc280, 0xc281, 0xc282,
  0xc283, 0xc284, 0xc285, 0xc286, 0xc287, 0xc288, 0xc289, 0xc28a,
  0xc28b, 0xc28c, 0xc28d, 0xc28e, 0xc28f, 0xc290, 0xc291, 0xc292,
  0xc293, 0xc294, 0xc295, 0xc296, 0xc297, 0xc298, 0xc299, 0xc29a,
  0xc29b, 0xc29c, 0xc29d, 0xc29e, 0xc29f, 0xc2a0, 0xc340, 0xc341,
  0xc342, 0xc343, 0xc344, 0xc345, 0xc346, 0xc347, 0xc348, 0xc349,
  0xc34a, 0xc34b, 0xc34c, 0xc34d, 0xc34e, 0xc34f, 0xc350, 0xc351,
  0xc352, 0xc353, 0xc354, 0xc355, 0xc356, 0xc357, 0xc358, 0xc359,
  0xc35a, 0xc35b, 0xc35c, 0xc35d, 0xc35e, 0xc35f, 0xc360, 0xc361,
  0xc362, 0xc363, 0xc364, 0xc365, 0xc366, 0xc367, 0xc368, 0xc369,
  0xc36a, 0xc36b, 0xc36c, 0xc36d, 0xc36e, 0xc36f, 0xc370, 0xc371,
  0xc372, 0xc373, 0xc374, 0xc375, 0xc376, 0xc377, 0xc378, 0xc379,
  0xc37a, 0xc37b, 0xc37c, 0xc37d, 0xc37e, 0xc380, 0xc381, 0xc382,
  0xc383, 0xc384, 0xc385, 0xc386, 0xc387, 0xc388, 0xc389, 0xc38a,
  0xc38b, 0xc38c, 0xc38d, 0xc38e, 0xc38f, 0xc390, 0xc391, 0xc392,
  0xc393, 0xc394, 0xc395, 0xc396, 0xc397, 0xc398, 0xc399, 0xc39a,
  0xc39b, 0xc39c, 0xc39d, 0xc39e, 0xc39f, 0xc3a0, 0xc440, 0xc441,
  0xc442, 0xc443, 0xc444, 0xc445, 0xc446, 0xc447, 0xc448, 0xc449,
  0xc44a, 0xc44b, 0xc44c, 0xc44d, 0xc44e, 0xc44f, 0xc450, 0xc451,
  0xc452, 0xc453, 0xc454, 0xc455, 0xc456, 0xc457, 0xc458, 0xc459,
  0xc45a, 0xc45b, 0xc45c, 0xc45d, 0xc45e, 0xc45f, 0xc460, 0xc461,
  0xc462, 0xc463, 0xc464, 0xc465, 0xc466, 0xc467, 0xc468, 0xc469,
  0xc46a, 0xc46b, 0xc46c, 0xc46d, 0xc46e, 0xc46f, 0xc470, 0xc471,
  0xc472, 0xc473, 0xc474, 0xc475, 0xc476, 0xc477, 0xc478, 0xc479,
  0xc47a, 0xc47b, 0xc47c, 0xc47d, 0xc47e, 0xc480, 0xc481, 0xc482,
  0xc483, 0xc484, 0xc485, 0xc486, 0xc487, 0xc488, 0xc489, 0xc48a,
  0xc48b, 0xc48c, 0xc48d, 0xc48e, 0xc48f, 0xc490, 0xc491, 0xc492,
  0xc493, 0xc494, 0xc495, 0xc496, 0xc497, 0xc498, 0xc499, 0xc49a,
  0xc49b, 0xc49c, 0xc49d, 0xc49e, 0xc49f, 0xc4a0, 0xc540, 0xc541,
  0xc542, 0xc543, 0xc544, 0xc545, 0xc546, 0xc547, 0xc548, 0xc549,
  0xc54a, 0xc54b, 0xc54c, 0xc54d, 0xc54e, 0xc54f, 0xc550, 0xc551,
  0xc552, 0xc553, 0xc554, 0xc555, 0xc556, 0xc557, 0xc558, 0xc559,
  0xc55a, 0xc55b, 0xc55c, 0xc55d, 0xc55e, 0xc55f, 0xc560, 0xc561,
  0xc562, 0xc563, 0xc564, 0xc565, 0xc566, 0xc567, 0xc568, 0xc569,
  0xc56a, 0xc56b, 0xc56c, 0xc56d, 0xc56e, 0xc56f, 0xc570, 0xc571,
  0xc572, 0xc573, 0xc574, 0xc575, 0xc576, 0xc577, 0xc578, 0xc579,
  0xc57a, 0xc57b, 0xc57c, 0xc57d, 0xc57e, 0xc580, 0xc581, 0xc582,
  0xc583, 0xc584, 0xc585, 0xc586, 0xc587, 0xc588, 0xc589, 0xc58a,
  0xc58b, 0xc58c, 0xc58d, 0xc58e, 0xc58f, 0xc590, 0xc591, 0xc592,
  0xc593, 0xc594, 0xc595, 0xc596, 0xc597, 0xc598, 0xc599, 0xc59a,
  0xc59b, 0xc59c, 0xc59d, 0xc59e, 0xc59f, 0xc5a0, 0xc640, 0xc641,
  0xc642, 0xc643, 0xc644, 0xc645, 0xc646, 0xc647, 0xc648, 0xc649,
  0xc64a, 0xc64b, 0xc64c, 0xc64d, 0xc64e, 0xc64f, 0xc650, 0xc651,
  0xc652, 0xc653, 0xc654, 0xc655, 0xc656, 0xc657, 0xc658, 0xc659,
  0xc65a, 0xc65b, 0xc65c, 0xc65d, 0xc65e, 0xc65f, 0xc660, 0xc661,
  0xc662, 0xc663, 0xc664, 0xc665, 0xc666, 0xc667, 0xc668, 0xc669,
  0xc66a, 0xc66b, 0xc66c, 0xc66d, 0xc66e, 0xc66f, 0xc670, 0xc671,
  0xc672, 0xc673, 0xc674, 0xc675, 0xc676, 0xc677, 0xc678, 0xc679,
  0xc67a, 0xc67b, 0xc67c, 0xc67d, 0xc67e, 0xc680, 0xc681, 0xc682,
  0xc683, 0xc684, 0xc685, 0xc686, 0xc687, 0xc688, 0xc689, 0xc68a,
  0xc68b, 0xc68c, 0xc68d, 0xc68e, 0xc68f, 0xc690, 0xc691, 0xc692,
  0xc693, 0xc694, 0xc695, 0xc696, 0xc697, 0xc698, 0xc699, 0xc69a,
  0xc69b, 0xc69c, 0xc69d, 0xc69e, 0xc69f, 0xc6a0, 0xc740, 0xc741,
  0xc742, 0xc743, 0xc744, 0xc745, 0xc746, 0xc747, 0xc748, 0xc749,
  0xc74a, 0xc74b, 0xc74c, 0xc74d, 0xc74e, 0xc74f, 0xc750, 0xc751,
  0xc752, 0xc753, 0xc754, 0xc755, 0xc756, 0xc757, 0xc758, 0xc759,
  0xc75a, 0xc75b, 0xc75c, 0xc75d, 0xc75e, 0xc75f, 0xc760, 0xc761,
  0xc762, 0xc763, 0xc764, 0xc765, 0xc766, 0xc767, 0xc768, 0xc769,
  0xc76a, 0xc76b, 0xc76c, 0xc76d, 0xc76e, 0xc76f, 0xc770, 0xc771,
  0xc772, 0xc773, 0xc774, 0xc775, 0xc776, 0xc777, 0xc778, 0xc779,
  0xc77a, 0xc77b, 0xc77c, 0xc77d, 0xc77e, 0xc780, 0xc781, 0xc782,
  0xc783, 0xc784, 0xc785, 0xc786, 0xc787, 0xc788, 0xc789, 0xc78a,
  0xc78b, 0xc78c, 0xc78d, 0xc78e, 0xc78f, 0xc790, 0xc791, 0xc792,
  0xc793, 0xc794, 0xc795, 0xc796, 0xc797, 0xc798, 0xc799, 0xc79a,
  0xc79b, 0xc79c, 0xc79d, 0xc79e, 0xc79f, 0xc7a0, 0xc840, 0xc841,
  0xc842, 0xc843, 0xc844, 0xc845, 0xc846, 0xc847, 0xc848, 0xc849,
  0xc84a, 0xc84b, 0xc84c, 0xc84d, 0xc84e, 0xc84f, 0xc850, 0xc851,
  0xc852, 0xc853, 0xc854, 0xc855, 0xc856, 0xc857, 0xc858, 0xc859,
  0xc85a, 0xc85b, 0xc85c, 0xc85d, 0xc85e, 0xc85f, 0xc860, 0xc861,
  0xc862, 0xc863, 0xc864, 0xc865, 0xc866, 0xc867, 0xc868, 0xc869,
  0xc86a, 0xc86b, 0xc86c, 0xc86d, 0xc86e, 0xc86f, 0xc870, 0xc871,
  0xc872, 0xc873, 0xc874, 0xc875, 0xc876, 0xc877, 0xc878, 0xc879,
  0xc87a, 0xc87b, 0xc87c, 0xc87d, 0xc87e, 0xc880, 0xc881, 0xc882,
  0xc883, 0xc884, 0xc885, 0xc886, 0xc887, 0xc888, 0xc889, 0xc88a,
  0xc88b, 0xc88c, 0xc88d, 0xc88e, 0xc88f, 0xc890, 0xc891, 0xc892,
  0xc893, 0xc894, 0xc895, 0xc896, 0xc897, 0xc898, 0xc899, 0xc89a,
  0xc89b, 0xc89c, 0xc89d, 0xc89e, 0xc89f, 0xc8a0, 0xc940, 0xc941,
  0xc942, 0xc943, 0xc944, 0xc945, 0xc946, 0xc947, 0xc948, 0xc949,
  0xc94a, 0xc94b, 0xc94c, 0xc94d, 0xc94e, 0xc94f, 0xc950, 0xc951,
  0xc952, 0xc953, 0xc954, 0xc955, 0xc956, 0xc957, 0xc958, 0xc959,
  0xc95a, 0xc95b, 0xc95c, 0xc95d, 0xc95e, 0xc95f, 0xc960, 0xc961,
  0xc962, 0xc963, 0xc964, 0xc965, 0xc966, 0xc967, 0xc968, 0xc969,
  0xc96a, 0xc96b, 0xc96c, 0xc96d, 0xc96e, 0xc96f, 0xc970, 0xc971,
  0xc972, 0xc973, 0xc974, 0xc975, 0xc976, 0xc977, 0xc978, 0xc979,
  0xc97a, 0xc97b, 0xc97c, 0xc97d, 0xc97e, 0xc980, 0xc981, 0xc982,
  0xc983, 0xc984, 0xc985, 0xc986, 0xc987, 0xc988, 0xc989, 0xc98a,
  0xc98b, 0xc98c, 0xc98d, 0xc98e, 0xc98f, 0xc990, 0xc991, 0xc992,
  0xc993, 0xc994, 0xc995, 0xc996, 0xc997, 0xc998, 0xc999, 0xc99a,
  0xc99b, 0xc99c, 0xc99d, 0xc99e, 0xc99f, 0xc9a0, 0xca40, 0xca41,
  0xca42, 0xca43, 0xca44, 0xca45, 0xca46, 0xca47, 0xca48, 0xca49,
  0xca4a, 0xca4b, 0xca4c, 0xca4d, 0xca4e, 0xca4f, 0xca50, 0xca51,
  0xca52, 0xca53, 0xca54, 0xca55, 0xca56, 0xca57, 0xca58, 0xca59,
  0xca5a, 0xca5b, 0xca5c, 0xca5d, 0xca5e, 0xca5f, 0xca60, 0xca61,
  0xca62, 0xca63, 0xca64, 0xca65, 0xca66, 0xca67, 0xca68, 0xca69,
  0xca6a, 0xca6b, 0xca6c, 0xca6d, 0xca6e, 0xca6f, 0xca70, 0xca71,
  0xca72, 0xca73, 0xca74, 0xca75, 0xca76, 0xca77, 0xca78, 0xca79,
  0xca7a, 0xca7b, 0xca7c, 0xca7d, 0xca7e, 0xca80, 0xca81, 0xca82,
  0xca83, 0xca84, 0xca85, 0xca86, 0xca87, 0xca88, 0xca89, 0xca8a,
  0xca8b, 0xca8c, 0xca8d, 0xca8e, 0xca8f, 0xca90, 0xca91, 0xca92,
  0xca93, 0xca94, 0xca95, 0xca96, 0xca97, 0xca98, 0xca99, 0xca9a,
  0xca9b, 0xca9c, 0xca9d, 0xca9e, 0xca9f, 0xcaa0, 0xcb40, 0xcb41,
  0xcb42, 0xcb43, 0xcb44, 0xcb45, 0xcb46, 0xcb47, 0xcb48, 0xcb49,
  0xcb4a, 0xcb4b, 0xcb4c, 0xcb4d, 0xcb4e, 0xcb4f, 0xcb50, 0xcb51,
  0xcb52, 0xcb53, 0xcb54, 0xcb55, 0xcb56, 0xcb57, 0xcb58, 0xcb59,
  0xcb5a, 0xcb5b, 0xcb5c, 0xcb5d, 0xcb5e, 0xcb5f, 0xcb60, 0xcb61,
  0xcb62, 0xcb63, 0xcb64, 0xcb65, 0xcb66, 0xcb67, 0xcb68, 0xcb69,
  0xcb6a, 0xcb6b, 0xcb6c, 0xcb6d, 0xcb6e, 0xcb6f, 0xcb70, 0xcb71,
  0xcb72, 0xcb73, 0xcb74, 0xcb75, 0xcb76, 0xcb77, 0xcb78, 0xcb79,
  0xcb7a, 0xcb7b, 0xcb7c, 0xcb7d, 0xcb7e, 0xcb80, 0xcb81, 0xcb82,
  0xcb83, 0xcb84, 0xcb85, 0xcb86, 0xcb87, 0xcb88, 0xcb89, 0xcb8a,
  0xcb8b, 0xcb8c, 0xcb8d, 0xcb8e, 0xcb8f, 0xcb90, 0xcb91, 0xcb92,
  0xcb93, 0xcb94, 0xcb95, 0xcb96, 0xcb97, 0xcb98, 0xcb99, 0xcb9a,
  0xcb9b, 0xcb9c, 0xcb9d, 0xcb9e, 0xcb9f, 0xcba0, 0xcc40, 0xcc41,
  0xcc42, 0xcc43, 0xcc44, 0xcc45, 0xcc46, 0xcc47, 0xcc48, 0xcc49,
  0xcc4a, 0xcc4b, 0xcc4c, 0xcc4d, 0xcc4e, 0xcc4f, 0xcc50, 0xcc51,
  0xcc52, 0xcc53, 0xcc54, 0xcc55, 0xcc56, 0xcc57, 0xcc58, 0xcc59,
  0xcc5a, 0xcc5b, 0xcc5c, 0xcc5d, 0xcc5e, 0xcc5f, 0xcc60, 0xcc61,
  0xcc62, 0xcc63, 0xcc64, 0xcc65, 0xcc66, 0xcc67, 0xcc68, 0xcc69,
  0xcc6a, 0xcc6b, 0xcc6c, 0xcc6d, 0xcc6e, 0xcc6f, 0xcc70, 0xcc71,
  0xcc72, 0xcc73, 0xcc74, 0xcc75, 0xcc76, 0xcc77, 0xcc78, 0xcc79,
  0xcc7a, 0xcc7b, 0xcc7c, 0xcc7d, 0xcc7e, 0xcc80, 0xcc81, 0xcc82,
  0xcc83, 0xcc84, 0xcc85, 0xcc86, 0xcc87, 0xcc88, 0xcc89, 0xcc8a,
  0xcc8b, 0xcc8c, 0xcc8d, 0xcc8e, 0xcc8f, 0xcc90, 0xcc91, 0xcc92,
  0xcc93, 0xcc94, 0xcc95, 0xcc96, 0xcc97, 0xcc98, 0xcc99, 0xcc9a,
  0xcc9b, 0xcc9c, 0xcc9d, 0xcc9e, 0xcc9f, 0xcca0, 0xcd40, 0xcd41,
  0xcd42, 0xcd43, 0xcd44, 0xcd45, 0xcd46, 0xcd47, 0xcd48, 0xcd49,
  0xcd4a, 0xcd4b, 0xcd4c, 0xcd4d, 0xcd4e, 0xcd4f, 0xcd50, 0xcd51,
  0xcd52, 0xcd53, 0xcd54, 0xcd55, 0xcd56, 0xcd57, 0xcd58, 0xcd59,
  0xcd5a, 0xcd5b, 0xcd5c, 0xcd5d, 0xcd5e, 0xcd5f, 0xcd60, 0xcd61,
  0xcd62, 0xcd63, 0xcd64, 0xcd65, 0xcd66, 0xcd67, 0xcd68, 0xcd69,
  0xcd6a, 0xcd6b, 0xcd6c, 0xcd6d, 0xcd6e, 0xcd6f, 0xcd70, 0xcd71,
  0xcd72, 0xcd73, 0xcd74, 0xcd75, 0xcd76, 0xcd77, 0xcd78, 0xcd79,
  0xcd7a, 0xcd7b, 0xcd7c, 0xcd7d, 0xcd7e, 0xcd80, 0xcd81, 0xcd82,
  0xcd83, 0xcd84, 0xcd85, 0xcd86, 0xcd87, 0xcd88, 0xcd89, 0xcd8a,
  0xcd8b, 0xcd8c, 0xcd8d, 0xcd8e, 0xcd8f, 0xcd90, 0xcd91, 0xcd92,
  0xcd93, 0xcd94, 0xcd95, 0xcd96, 0xcd97, 0xcd98, 0xcd99, 0xcd9a,
  0xcd9b, 0xcd9c, 0xcd9d, 0xcd9e, 0xcd9f, 0xcda0, 0xce40, 0xce41,
  0xce42, 0xce43, 0xce44, 0xce45, 0xce46, 0xce47, 0xce48, 0xce49,
  0xce4a, 0xce4b, 0xce4c, 0xce4d, 0xce4e, 0xce4f, 0xce50, 0xce51,
  0xce52, 0xce53, 0xce54, 0xce55, 0xce56, 0xce57, 0xce58, 0xce59,
  0xce5a, 0xce5b, 0xce5c, 0xce5d, 0xce5e, 0xce5f, 0xce60, 0xce61,
  0xce62, 0xce63, 0xce64, 0xce65, 0xce66, 0xce67, 0xce68, 0xce69,
  0xce6a, 0xce6b, 0xce6c, 0xce6d, 0xce6e, 0xce6f, 0xce70, 0xce71,
  0xce72, 0xce73, 0xce74, 0xce75, 0xce76, 0xce77, 0xce78, 0xce79,
  0xce7a, 0xce7b, 0xce7c, 0xce7d, 0xce7e, 0xce80, 0xce81, 0xce82,
  0xce83, 0xce84, 0xce85, 0xce86, 0xce87, 0xce88, 0xce89, 0xce8a,
  0xce8b, 0xce8c, 0xce8d, 0xce8e, 0xce8f, 0xce90, 0xce91, 0xce92,
  0xce93, 0xce94, 0xce95, 0xce96, 0xce97, 0xce98, 0xce99, 0xce9a,
  0xce9b, 0xce9c, 0xce9d, 0xce9e, 0xce9f, 0xcea0, 0xcf40, 0xcf41,
  0xcf42, 0xcf43, 0xcf44, 0xcf45, 0xcf46, 0xcf47, 0xcf48, 0xcf49,
  0xcf4a, 0xcf4b, 0xcf4c, 0xcf4d, 0xcf4e, 0xcf4f, 0xcf50, 0xcf51,
  0xcf52, 0xcf53, 0xcf54, 0xcf55, 0xcf56, 0xcf57, 0xcf58, 0xcf59,
  0xcf5a, 0xcf5b, 0xcf5c, 0xcf5d, 0xcf5e, 0xcf5f, 0xcf60, 0xcf61,
  0xcf62, 0xcf63, 0xcf64, 0xcf65, 0xcf66, 0xcf67, 0xcf68, 0xcf69,
  0xcf6a, 0xcf6b, 0xcf6c, 0xcf6d, 0xcf6e, 0xcf6f, 0xcf70, 0xcf71,
  0xcf72, 0xcf73, 0xcf74, 0xcf75, 0xcf76, 0xcf77, 0xcf78, 0xcf79,
  0xcf7a, 0xcf7b, 0xcf7c, 0xcf7d, 0xcf7e, 0xcf80, 0xcf81, 0xcf82,
  0xcf83, 0xcf84, 0xcf85, 0xcf86, 0xcf87, 0xcf88, 0xcf89, 0xcf8a,
  0xcf8b, 0xcf8c, 0xcf8d, 0xcf8e, 0xcf8f, 0xcf90, 0xcf91, 0xcf92,
  0xcf93, 0xcf94, 0xcf95, 0xcf96, 0xcf97, 0xcf98, 0xcf99, 0xcf9a,
  0xcf9b, 0xcf9c, 0xcf9d, 0xcf9e, 0xcf9f, 0xcfa0, 0xd040, 0xd041,
  0xd042, 0xd043, 0xd044, 0xd045, 0xd046, 0xd047, 0xd048, 0xd049,
  0xd04a, 0xd04b, 0xd04c, 0xd04d, 0xd04e, 0xd04f, 0xd050, 0xd051,
  0xd052, 0xd053, 0xd054, 0xd055, 0xd056, 0xd057, 0xd058, 0xd059,
  0xd05a, 0xd05b, 0xd05c, 0xd05d, 0xd05e, 0xd05f, 0xd060, 0xd061,
  0xd062, 0xd063, 0xd064, 0xd065, 0xd066, 0xd067, 0xd068, 0xd069,
  0xd06a, 0xd06b, 0xd06c, 0xd06d, 0xd06e, 0xd06f, 0xd070, 0xd071,
  0xd072, 0xd073, 0xd074, 0xd075, 0xd076, 0xd077, 0xd078, 0xd079,
  0xd07a, 0xd07b, 0xd07c, 0xd07d, 0xd07e, 0xd080, 0xd081, 0xd082,
  0xd083, 0xd084, 0xd085, 0xd086, 0xd087, 0xd088, 0xd089, 0xd08a,
  0xd08b, 0xd08c, 0xd08d, 0xd08e, 0xd08f, 0xd090, 0xd091, 0xd092,
  0xd093, 0xd094, 0xd095, 0xd096, 0xd097, 0xd098, 0xd099, 0xd09a,
  0xd09b, 0xd09c, 0xd09d, 0xd09e, 0xd09f, 0xd0a0, 0xd140, 0xd141,
  0xd142, 0xd143, 0xd144, 0xd145, 0xd146, 0xd147, 0xd148, 0xd149,
  0xd14a, 0xd14b, 0xd14c, 0xd14d, 0xd14e, 0xd14f, 0xd150, 0xd151,
  0xd152, 0xd153, 0xd154, 0xd155, 0xd156, 0xd157, 0xd158, 0xd159,
  0xd15a, 0xd15b, 0xd15c, 0xd15d, 0xd15e, 0xd15f, 0xd160, 0xd161,
  0xd162, 0xd163, 0xd164, 0xd165, 0xd166, 0xd167, 0xd168, 0xd169,
  0xd16a, 0xd16b, 0xd16c, 0xd16d, 0xd16e, 0xd16f, 0xd170, 0xd171,
  0xd172, 0xd173, 0xd174, 0xd175, 0xd176, 0xd177, 0xd178, 0xd179,
  0xd17a, 0xd17b, 0xd17c, 0xd17d, 0xd17e, 0xd180, 0xd181, 0xd182,
  0xd183, 0xd184, 0xd185, 0xd186, 0xd187, 0xd188, 0xd189, 0xd18a,
  0xd18b, 0xd18c, 0xd18d, 0xd18e, 0xd18f, 0xd190, 0xd191, 0xd192,
  0xd193, 0xd194, 0xd195, 0xd196, 0xd197, 0xd198, 0xd199, 0xd19a,
  0xd19b, 0xd19c, 0xd19d, 0xd19e, 0xd19f, 0xd1a0, 0xd240, 0xd241,
  0xd242, 0xd243, 0xd244, 0xd245, 0xd246, 0xd247, 0xd248, 0xd249,
  0xd24a, 0xd24b, 0xd24c, 0xd24d, 0xd24e, 0xd24f, 0xd250, 0xd251,
  0xd252, 0xd253, 0xd254, 0xd255, 0xd256, 0xd257, 0xd258, 0xd259,
  0xd25a, 0xd25b, 0xd25c, 0xd25d, 0xd25e, 0xd25f, 0xd260, 0xd261,
  0xd262, 0xd263, 0xd264, 0xd265, 0xd266, 0xd267, 0xd268, 0xd269,
  0xd26a, 0xd26b, 0xd26c, 0xd26d, 0xd26e, 0xd26f, 0xd270, 0xd271,
  0xd272, 0xd273, 0xd274, 0xd275, 0xd276, 0xd277, 0xd278, 0xd279,
  0xd27a, 0xd27b, 0xd27c, 0xd27d, 0xd27e, 0xd280, 0xd281, 0xd282,
  0xd283, 0xd284, 0xd285, 0xd286, 0xd287, 0xd288, 0xd289, 0xd28a,
  0xd28b, 0xd28c, 0xd28d, 0xd28e, 0xd28f, 0xd290, 0xd291, 0xd292,
  0xd293, 0xd294, 0xd295, 0xd296, 0xd297, 0xd298, 0xd299, 0xd29a,
  0xd29b, 0xd29c, 0xd29d, 0xd29e, 0xd29f, 0xd2a0, 0xd340, 0xd341,
  0xd342, 0xd343, 0xd344, 0xd345, 0xd346, 0xd347, 0xd348, 0xd349,
  0xd34a, 0xd34b, 0xd34c, 0xd34d, 0xd34e, 0xd34f, 0xd350, 0xd351,
  0xd352, 0xd353, 0xd354, 0xd355, 0xd356, 0xd357, 0xd358, 0xd359,
  0xd35a, 0xd35b, 0xd35c, 0xd35d, 0xd35e, 0xd35f, 0xd360, 0xd361,
  0xd362, 0xd363, 0xd364, 0xd365, 0xd366, 0xd367, 0xd368, 0xd369,
  0xd36a, 0xd36b, 0xd36c, 0xd36d, 0xd36e, 0xd36f, 0xd370, 0xd371,
  0xd372, 0xd373, 0xd374, 0xd375, 0xd376, 0xd377, 0xd378, 0xd379,
  0xd37a, 0xd37b, 0xd37c, 0xd37d, 0xd37e, 0xd380, 0xd381, 0xd382,
  0xd383, 0xd384, 0xd385, 0xd386, 0xd387, 0xd388, 0xd389, 0xd38a,
  0xd38b, 0xd38c, 0xd38d, 0xd38e, 0xd38f, 0xd390, 0xd391, 0xd392,
  0xd393, 0xd394, 0xd395, 0xd396, 0xd397, 0xd398, 0xd399, 0xd39a,
  0xd39b, 0xd39c, 0xd39d, 0xd39e, 0xd39f, 0xd3a0, 0xd440, 0xd441,
  0xd442, 0xd443, 0xd444, 0xd445, 0xd446, 0xd447, 0xd448, 0xd449,
  0xd44a, 0xd44b, 0xd44c, 0xd44d, 0xd44e, 0xd44f, 0xd450, 0xd451,
  0xd452, 0xd453, 0xd454, 0xd455, 0xd456, 0xd457, 0xd458, 0xd459,
  0xd45a, 0xd45b, 0xd45c, 0xd45d, 0xd45e, 0xd45f, 0xd460, 0xd461,
  0xd462, 0xd463, 0xd464, 0xd465, 0xd466, 0xd467, 0xd468, 0xd469,
  0xd46a, 0xd46b, 0xd46c, 0xd46d, 0xd46e, 0xd46f, 0xd470, 0xd471,
  0xd472, 0xd473, 0xd474, 0xd475, 0xd476, 0xd477, 0xd478, 0xd479,
  0xd47a, 0xd47b, 0xd47c, 0xd47d, 0xd47e, 0xd480, 0xd481, 0xd482,
  0xd483, 0xd484, 0xd485, 0xd486, 0xd487, 0xd488, 0xd489, 0xd48a,
  0xd48b, 0xd48c, 0xd48d, 0xd48e, 0xd48f, 0xd490, 0xd491, 0xd492,
  0xd493, 0xd494, 0xd495, 0xd496, 0xd497, 0xd498, 0xd499, 0xd49a,
  0xd49b, 0xd49c, 0xd49d, 0xd49e, 0xd49f, 0xd4a0, 0xd540, 0xd541,
  0xd542, 0xd543, 0xd544, 0xd545, 0xd546, 0xd547, 0xd548, 0xd549,
  0xd54a, 0xd54b, 0xd54c, 0xd54d, 0xd54e, 0xd54f, 0xd550, 0xd551,
  0xd552, 0xd553, 0xd554, 0xd555, 0xd556, 0xd557, 0xd558, 0xd559,
  0xd55a, 0xd55b, 0xd55c, 0xd55d, 0xd55e, 0xd55f, 0xd560, 0xd561,
  0xd562, 0xd563, 0xd564, 0xd565, 0xd566, 0xd567, 0xd568, 0xd569,
  0xd56a, 0xd56b, 0xd56c, 0xd56d, 0xd56e, 0xd56f, 0xd570, 0xd571,
  0xd572, 0xd573, 0xd574, 0xd575, 0xd576, 0xd577, 0xd578, 0xd579,
  0xd57a, 0xd57b, 0xd57c, 0xd57d, 0xd57e, 0xd580, 0xd581, 0xd582,
  0xd583, 0xd584, 0xd585, 0xd586, 0xd587, 0xd588, 0xd589, 0xd58a,
  0xd58b, 0xd58c, 0xd58d, 0xd58e, 0xd58f, 0xd590, 0xd591, 0xd592,
  0xd593, 0xd594, 0xd595, 0xd596, 0xd597, 0xd598, 0xd599, 0xd59a,
  0xd59b, 0xd59c, 0xd59d, 0xd59e, 0xd59f, 0xd5a0, 0xd640, 0xd641,
  0xd642, 0xd643, 0xd644, 0xd645, 0xd646, 0xd647, 0xd648, 0xd649,
  0xd64a, 0xd64b, 0xd64c, 0xd64d, 0xd64e, 0xd64f, 0xd650, 0xd651,
  0xd652, 0xd653, 0xd654, 0xd655, 0xd656, 0xd657, 0xd658, 0xd659,
  0xd65a, 0xd65b, 0xd65c, 0xd65d, 0xd65e, 0xd65f, 0xd660, 0xd661,
  0xd662, 0xd663, 0xd664, 0xd665, 0xd666, 0xd667, 0xd668, 0xd669,
  0xd66a, 0xd66b, 0xd66c, 0xd66d, 0xd66e, 0xd66f, 0xd670, 0xd671,
  0xd672, 0xd673, 0xd674, 0xd675, 0xd676, 0xd677, 0xd678, 0xd679,
  0xd67a, 0xd67b, 0xd67c, 0xd67d, 0xd67e, 0xd680, 0xd681, 0xd682,
  0xd683, 0xd684, 0xd685, 0xd686, 0xd687, 0xd688, 0xd689, 0xd68a,
  0xd68b, 0xd68c, 0xd68d, 0xd68e, 0xd68f, 0xd690, 0xd691, 0xd692,
  0xd693, 0xd694, 0xd695, 0xd696, 0xd697, 0xd698, 0xd699, 0xd69a,
  0xd69b, 0xd69c, 0xd69d, 0xd69e, 0xd69f, 0xd6a0, 0xd740, 0xd741,
  0xd742, 0xd743, 0xd744, 0xd745, 0xd746, 0xd747, 0xd748, 0xd749,
  0xd74a, 0xd74b, 0xd74c, 0xd74d, 0xd74e, 0xd74f, 0xd750, 0xd751,
  0xd752, 0xd753, 0xd754, 0xd755, 0xd756, 0xd757, 0xd758, 0xd759,
  0xd75a, 0xd75b, 0xd75c, 0xd75d, 0xd75e, 0xd75f, 0xd760, 0xd761,
  0xd762, 0xd763, 0xd764, 0xd765, 0xd766, 0xd767, 0xd768, 0xd769,
  0xd76a, 0xd76b, 0xd76c, 0xd76d, 0xd76e, 0xd76f, 0xd770, 0xd771,
  0xd772, 0xd773, 0xd774, 0xd775, 0xd776, 0xd777, 0xd778, 0xd779,
  0xd77a, 0xd77b, 0xd77c, 0xd77d, 0xd77e, 0xd780, 0xd781, 0xd782,
  0xd783, 0xd784, 0xd785, 0xd786, 0xd787, 0xd788, 0xd789, 0xd78a,
  0xd78b, 0xd78c, 0xd78d, 0xd78e, 0xd78f, 0xd790, 0xd791, 0xd792,
  0xd793, 0xd794, 0xd795, 0xd796, 0xd797, 0xd798, 0xd799, 0xd79a,
  0xd79b, 0xd79c, 0xd79d, 0xd79e, 0xd79f, 0xd7a0, 0xd840, 0xd841,
  0xd842, 0xd843, 0xd844, 0xd845, 0xd846, 0xd847, 0xd848, 0xd849,
  0xd84a, 0xd84b, 0xd84c, 0xd84d, 0xd84e, 0xd84f, 0xd850, 0xd851,
  0xd852, 0xd853, 0xd854, 0xd855, 0xd856, 0xd857, 0xd858, 0xd859,
  0xd85a, 0xd85b, 0xd85c, 0xd85d, 0xd85e, 0xd85f, 0xd860, 0xd861,
  0xd862, 0xd863, 0xd864, 0xd865, 0xd866, 0xd867, 0xd868, 0xd869,
  0xd86a, 0xd86b, 0xd86c, 0xd86d, 0xd86e, 0xd86f, 0xd870, 0xd871,
  0xd872, 0xd873, 0xd874, 0xd875, 0xd876, 0xd877, 0xd878, 0xd879,
  0xd87a, 0xd87b, 0xd87c, 0xd87d, 0xd87e, 0xd880, 0xd881, 0xd882,
  0xd883, 0xd884, 0xd885, 0xd886, 0xd887, 0xd888, 0xd889, 0xd88a,
  0xd88b, 0xd88c, 0xd88d, 0xd88e, 0xd88f, 0xd890, 0xd891, 0xd892,
  0xd893, 0xd894, 0xd895, 0xd896, 0xd897, 0xd898, 0xd899, 0xd89a,
  0xd89b, 0xd89c, 0xd89d, 0xd89e, 0xd89f, 0xd8a0, 0xd940, 0xd941,
  0xd942, 0xd943, 0xd944, 0xd945, 0xd946, 0xd947, 0xd948, 0xd949,
  0xd94a, 0xd94b, 0xd94c, 0xd94d, 0xd94e, 0xd94f, 0xd950, 0xd951,
  0xd952, 0xd953, 0xd954, 0xd955, 0xd956, 0xd957, 0xd958, 0xd959,
  0xd95a, 0xd95b, 0xd95c, 0xd95d, 0xd95e, 0xd95f, 0xd960, 0xd961,
  0xd962, 0xd963, 0xd964, 0xd965, 0xd966, 0xd967, 0xd968, 0xd969,
  0xd96a, 0xd96b, 0xd96c, 0xd96d, 0xd96e, 0xd96f, 0xd970, 0xd971,
  0xd972, 0xd973, 0xd974, 0xd975, 0xd976, 0xd977, 0xd978, 0xd979,
  0xd97a, 0xd97b, 0xd97c, 0xd97d, 0xd97e, 0xd980, 0xd981, 0xd982,
  0xd983, 0xd984, 0xd985, 0xd986, 0xd987, 0xd988, 0xd989, 0xd98a,
  0xd98b, 0xd98c, 0xd98d, 0xd98e, 0xd98f, 0xd990, 0xd991, 0xd992,
  0xd993, 0xd994, 0xd995, 0xd996, 0xd997, 0xd998, 0xd999, 0xd99a,
  0xd99b, 0xd99c, 0xd99d, 0xd99e, 0xd99f, 0xd9a0, 0xda40, 0xda41,
  0xda42, 0xda43, 0xda44, 0xda45, 0xda46, 0xda47, 0xda48, 0xda49,
  0xda4a, 0xda4b, 0xda4c, 0xda4d, 0xda4e, 0xda4f, 0xda50, 0xda51,
  0xda52, 0xda53, 0xda54, 0xda55, 0xda56, 0xda57, 0xda58, 0xda59,
  0xda5a, 0xda5b, 0xda5c, 0xda5d, 0xda5e, 0xda5f, 0xda60, 0xda61,
  0xda62, 0xda63, 0xda64, 0xda65, 0xda66, 0xda67, 0xda68, 0xda69,
  0xda6a, 0xda6b, 0xda6c, 0xda6d, 0xda6e, 0xda6f, 0xda70, 0xda71,
  0xda72, 0xda73, 0xda74, 0xda75, 0xda76, 0xda77, 0xda78, 0xda79,
  0xda7a, 0xda7b, 0xda7c, 0xda7d, 0xda7e, 0xda80, 0xda81, 0xda82,
  0xda83, 0xda84, 0xda85, 0xda86, 0xda87, 0xda88, 0xda89, 0xda8a,
  0xda8b, 0xda8c, 0xda8d, 0xda8e, 0xda8f, 0xda90, 0xda91, 0xda92,
  0xda93, 0xda94, 0xda95, 0xda96, 0xda97, 0xda98, 0xda99, 0xda9a,
  0xda9b, 0xda9c, 0xda9d, 0xda9e, 0xda9f, 0xdaa0, 0xdb40, 0xdb41,
  0xdb42, 0xdb43, 0xdb44, 0xdb45, 0xdb46, 0xdb47, 0xdb48, 0xdb49,
  0xdb4a, 0xdb4b, 0xdb4c, 0xdb4d, 0xdb4e, 0xdb4f, 0xdb50, 0xdb51,
  0xdb52, 0xdb53, 0xdb54, 0xdb55, 0xdb56, 0xdb57, 0xdb58, 0xdb59,
  0xdb5a, 0xdb5b, 0xdb5c, 0xdb5d, 0xdb5e, 0xdb5f, 0xdb60, 0xdb61,
  0xdb62, 0xdb63, 0xdb64, 0xdb65, 0xdb66, 0xdb67, 0xdb68, 0xdb69,
  0xdb6a, 0xdb6b, 0xdb6c, 0xdb6d, 0xdb6e, 0xdb6f, 0xdb70, 0xdb71,
  0xdb72, 0xdb73, 0xdb74, 0xdb75, 0xdb76, 0xdb77, 0xdb78, 0xdb79,
  0xdb7a, 0xdb7b, 0xdb7c, 0xdb7d, 0xdb7e, 0xdb80, 0xdb81, 0xdb82,
  0xdb83, 0xdb84, 0xdb85, 0xdb86, 0xdb87, 0xdb88, 0xdb89, 0xdb8a,
  0xdb8b, 0xdb8c, 0xdb8d, 0xdb8e, 0xdb8f, 0xdb90, 0xdb91, 0xdb92,
  0xdb93, 0xdb94, 0xdb95, 0xdb96, 0xdb97, 0xdb98, 0xdb99, 0xdb9a,
  0xdb9b, 0xdb9c, 0xdb9d, 0xdb9e, 0xdb9f, 0xdba0, 0xdc40, 0xdc41,
  0xdc42, 0xdc43, 0xdc44, 0xdc45, 0xdc46, 0xdc47, 0xdc48, 0xdc49,
  0xdc4a, 0xdc4b, 0xdc4c, 0xdc4d, 0xdc4e, 0xdc4f, 0xdc50, 0xdc51,
  0xdc52, 0xdc53, 0xdc54, 0xdc55, 0xdc56, 0xdc57, 0xdc58, 0xdc59,
  0xdc5a, 0xdc5b, 0xdc5c, 0xdc5d, 0xdc5e, 0xdc5f, 0xdc60, 0xdc61,
  0xdc62, 0xdc63, 0xdc64, 0xdc65, 0xdc66, 0xdc67, 0xdc68, 0xdc69,
  0xdc6a, 0xdc6b, 0xdc6c, 0xdc6d, 0xdc6e, 0xdc6f, 0xdc70, 0xdc71,
  0xdc72, 0xdc73, 0xdc74, 0xdc75, 0xdc76, 0xdc77, 0xdc78, 0xdc79,
  0xdc7a, 0xdc7b, 0xdc7c, 0xdc7d, 0xdc7e, 0xdc80, 0xdc81, 0xdc82,
  0xdc83, 0xdc84, 0xdc85, 0xdc86, 0xdc87, 0xdc88, 0xdc89, 0xdc8a,
  0xdc8b, 0xdc8c, 0xdc8d, 0xdc8e, 0xdc8f, 0xdc90, 0xdc91, 0xdc92,
  0xdc93, 0xdc94, 0xdc95, 0xdc96, 0xdc97, 0xdc98, 0xdc99, 0xdc9a,
  0xdc9b, 0xdc9c, 0xdc9d, 0xdc9e, 0xdc9f, 0xdca0, 0xdd40, 0xdd41,
  0xdd42, 0xdd43, 0xdd44, 0xdd45, 0xdd46, 0xdd47, 0xdd48, 0xdd49,
  0xdd4a, 0xdd4b, 0xdd4c, 0xdd4d, 0xdd4e, 0xdd4f, 0xdd50, 0xdd51,
  0xdd52, 0xdd53, 0xdd54, 0xdd55, 0xdd56, 0xdd57, 0xdd58, 0xdd59,
  0xdd5a, 0xdd5b, 0xdd5c, 0xdd5d, 0xdd5e, 0xdd5f, 0xdd60, 0xdd61,
  0xdd62, 0xdd63, 0xdd64, 0xdd65, 0xdd66, 0xdd67, 0xdd68, 0xdd69,
  0xdd6a, 0xdd6b, 0xdd6c, 0xdd6d, 0xdd6e, 0xdd6f, 0xdd70, 0xdd71,
  0xdd72, 0xdd73, 0xdd74, 0xdd75, 0xdd76, 0xdd77, 0xdd78, 0xdd79,
  0xdd7a, 0xdd7b, 0xdd7c, 0xdd7d, 0xdd7e, 0xdd80, 0xdd81, 0xdd82,
  0xdd83, 0xdd84, 0xdd85, 0xdd86, 0xdd87, 0xdd88, 0xdd89, 0xdd8a,
  0xdd8b, 0xdd8c, 0xdd8d, 0xdd8e, 0xdd8f, 0xdd90, 0xdd91, 0xdd92,
  0xdd93, 0xdd94, 0xdd95, 0xdd96, 0xdd97, 0xdd98, 0xdd99, 0xdd9a,
  0xdd9b, 0xdd9c, 0xdd9d, 0xdd9e, 0xdd9f, 0xdda0, 0xde40, 0xde41,
  0xde42, 0xde43, 0xde44, 0xde45, 0xde46, 0xde47, 0xde48, 0xde49,
  0xde4a, 0xde4b, 0xde4c, 0xde4d, 0xde4e, 0xde4f, 0xde50, 0xde51,
  0xde52, 0xde53, 0xde54, 0xde55, 0xde56, 0xde57, 0xde58, 0xde59,
  0xde5a, 0xde5b, 0xde5c, 0xde5d, 0xde5e, 0xde5f, 0xde60, 0xde61,
  0xde62, 0xde63, 0xde64, 0xde65, 0xde66, 0xde67, 0xde68, 0xde69,
  0xde6a, 0xde6b, 0xde6c, 0xde6d, 0xde6e, 0xde6f, 0xde70, 0xde71,
  0xde72, 0xde73, 0xde74, 0xde75, 0xde76, 0xde77, 0xde78, 0xde79,
  0xde7a, 0xde7b, 0xde7c, 0xde7d, 0xde7e, 0xde80, 0xde81, 0xde82,
  0xde83, 0xde84, 0xde85, 0xde86, 0xde87, 0xde88, 0xde89, 0xde8a,
  0xde8b, 0xde8c, 0xde8d, 0xde8e, 0xde8f, 0xde90, 0xde91, 0xde92,
  0xde93, 0xde94, 0xde95, 0xde96, 0xde97, 0xde98, 0xde99, 0xde9a,
  0xde9b, 0xde9c, 0xde9d, 0xde9e, 0xde9f, 0xdea0, 0xdf40, 0xdf41,
  0xdf42, 0xdf43, 0xdf44, 0xdf45, 0xdf46, 0xdf47, 0xdf48, 0xdf49,
  0xdf4a, 0xdf4b, 0xdf4c, 0xdf4d, 0xdf4e, 0xdf4f, 0xdf50, 0xdf51,
  0xdf52, 0xdf53, 0xdf54, 0xdf55, 0xdf56, 0xdf57, 0xdf58, 0xdf59,
  0xdf5a, 0xdf5b, 0xdf5c, 0xdf5d, 0xdf5e, 0xdf5f, 0xdf60, 0xdf61,
  0xdf62, 0xdf63, 0xdf64, 0xdf65, 0xdf66, 0xdf67, 0xdf68, 0xdf69,
  0xdf6a, 0xdf6b, 0xdf6c, 0xdf6d, 0xdf6e, 0xdf6f, 0xdf70, 0xdf71,
  0xdf72, 0xdf73, 0xdf74, 0xdf75, 0xdf76, 0xdf77, 0xdf78, 0xdf79,
  0xdf7a, 0xdf7b, 0xdf7c, 0xdf7d, 0xdf7e, 0xdf80, 0xdf81, 0xdf82,
  0xdf83, 0xdf84, 0xdf85, 0xdf86, 0xdf87, 0xdf88, 0xdf89, 0xdf8a,
  0xdf8b, 0xdf8c, 0xdf8d, 0xdf8e, 0xdf8f, 0xdf90, 0xdf91, 0xdf92,
  0xdf93, 0xdf94, 0xdf95, 0xdf96, 0xdf97, 0xdf98, 0xdf99, 0xdf9a,
  0xdf9b, 0xdf9c, 0xdf9d, 0xdf9e, 0xdf9f, 0xdfa0, 0xe040, 0xe041,
  0xe042, 0xe043, 0xe044, 0xe045, 0xe046, 0xe047, 0xe048, 0xe049,
  0xe04a, 0xe04b, 0xe04c, 0xe04d, 0xe04e, 0xe04f, 0xe050, 0xe051,
  0xe052, 0xe053, 0xe054, 0xe055, 0xe056, 0xe057, 0xe058, 0xe059,
  0xe05a, 0xe05b, 0xe05c, 0xe05d, 0xe05e, 0xe05f, 0xe060, 0xe061,
  0xe062, 0xe063, 0xe064, 0xe065, 0xe066, 0xe067, 0xe068, 0xe069,
  0xe06a, 0xe06b, 0xe06c, 0xe06d, 0xe06e, 0xe06f, 0xe070, 0xe071,
  0xe072, 0xe073, 0xe074, 0xe075, 0xe076, 0xe077, 0xe078, 0xe079,
  0xe07a, 0xe07b, 0xe07c, 0xe07d, 0xe07e, 0xe080, 0xe081, 0xe082,
  0xe083, 0xe084, 0xe085, 0xe086, 0xe087, 0xe088, 0xe089, 0xe08a,
  0xe08b, 0xe08c, 0xe08d, 0xe08e, 0xe08f, 0xe090, 0xe091, 0xe092,
  0xe093, 0xe094, 0xe095, 0xe096, 0xe097, 0xe098, 0xe099, 0xe09a,
  0xe09b, 0xe09c, 0xe09d, 0xe09e, 0xe09f, 0xe0a0, 0xe140, 0xe141,
  0xe142, 0xe143, 0xe144, 0xe145, 0xe146, 0xe147, 0xe148, 0xe149,
  0xe14a, 0xe14b, 0xe14c, 0xe14d, 0xe14e, 0xe14f, 0xe150, 0xe151,
  0xe152, 0xe153, 0xe154, 0xe155, 0xe156, 0xe157, 0xe158, 0xe159,
  0xe15a, 0xe15b, 0xe15c, 0xe15d, 0xe15e, 0xe15f, 0xe160, 0xe161,
  0xe162, 0xe163, 0xe164, 0xe165, 0xe166, 0xe167, 0xe168, 0xe169,
  0xe16a, 0xe16b, 0xe16c, 0xe16d, 0xe16e, 0xe16f, 0xe170, 0xe171,
  0xe172, 0xe173, 0xe174, 0xe175, 0xe176, 0xe177, 0xe178, 0xe179,
  0xe17a, 0xe17b, 0xe17c, 0xe17d, 0xe17e, 0xe180, 0xe181, 0xe182,
  0xe183, 0xe184, 0xe185, 0xe186, 0xe187, 0xe188, 0xe189, 0xe18a,
  0xe18b, 0xe18c, 0xe18d, 0xe18e, 0xe18f, 0xe190, 0xe191, 0xe192,
  0xe193, 0xe194, 0xe195, 0xe196, 0xe197, 0xe198, 0xe199, 0xe19a,
  0xe19b, 0xe19c, 0xe19d, 0xe19e, 0xe19f, 0xe1a0, 0xe240, 0xe241,
  0xe242, 0xe243, 0xe244, 0xe245, 0xe246, 0xe247, 0xe248, 0xe249,
  0xe24a, 0xe24b, 0xe24c, 0xe24d, 0xe24e, 0xe24f, 0xe250, 0xe251,
  0xe252, 0xe253, 0xe254, 0xe255, 0xe256, 0xe257, 0xe258, 0xe259,
  0xe25a, 0xe25b, 0xe25c, 0xe25d, 0xe25e, 0xe25f, 0xe260, 0xe261,
  0xe262, 0xe263, 0xe264, 0xe265, 0xe266, 0xe267, 0xe268, 0xe269,
  0xe26a, 0xe26b, 0xe26c, 0xe26d, 0xe26e, 0xe26f, 0xe270, 0xe271,
  0xe272, 0xe273, 0xe274, 0xe275, 0xe276, 0xe277, 0xe278, 0xe279,
  0xe27a, 0xe27b, 0xe27c, 0xe27d, 0xe27e, 0xe280, 0xe281, 0xe282,
  0xe283, 0xe284, 0xe285, 0xe286, 0xe287, 0xe288, 0xe289, 0xe28a,
  0xe28b, 0xe28c, 0xe28d, 0xe28e, 0xe28f, 0xe290, 0xe291, 0xe292,
  0xe293, 0xe294, 0xe295, 0xe296, 0xe297, 0xe298, 0xe299, 0xe29a,
  0xe29b, 0xe29c, 0xe29d, 0xe29e, 0xe29f, 0xe2a0, 0xe340, 0xe341,
  0xe342, 0xe343, 0xe344, 0xe345, 0xe346, 0xe347, 0xe348, 0xe349,
  0xe34a, 0xe34b, 0xe34c, 0xe34d, 0xe34e, 0xe34f, 0xe350, 0xe351,
  0xe352, 0xe353, 0xe354, 0xe355, 0xe356, 0xe357, 0xe358, 0xe359,
  0xe35a, 0xe35b, 0xe35c, 0xe35d, 0xe35e, 0xe35f, 0xe360, 0xe361,
  0xe362, 0xe363, 0xe364, 0xe365, 0xe366, 0xe367, 0xe368, 0xe369,
  0xe36a, 0xe36b, 0xe36c, 0xe36d, 0xe36e, 0xe36f, 0xe370, 0xe371,
  0xe372, 0xe373, 0xe374, 0xe375, 0xe376, 0xe377, 0xe378, 0xe379,
  0xe37a, 0xe37b, 0xe37c, 0xe37d, 0xe37e, 0xe380, 0xe381, 0xe382,
  0xe383, 0xe384, 0xe385, 0xe386, 0xe387, 0xe388, 0xe389, 0xe38a,
  0xe38b, 0xe38c, 0xe38d, 0xe38e, 0xe38f, 0xe390, 0xe391, 0xe392,
  0xe393, 0xe394, 0xe395, 0xe396, 0xe397, 0xe398, 0xe399, 0xe39a,
  0xe39b, 0xe39c, 0xe39d, 0xe39e, 0xe39f, 0xe3a0, 0xe440, 0xe441,
  0xe442, 0xe443, 0xe444, 0xe445, 0xe446, 0xe447, 0xe448, 0xe449,
  0xe44a, 0xe44b, 0xe44c, 0xe44d, 0xe44e, 0xe44f, 0xe450, 0xe451,
  0xe452, 0xe453, 0xe454, 0xe455, 0xe456, 0xe457, 0xe458, 0xe459,
  0xe45a, 0xe45b, 0xe45c, 0xe45d, 0xe45e, 0xe45f, 0xe460, 0xe461,
  0xe462, 0xe463, 0xe464, 0xe465, 0xe466, 0xe467, 0xe468, 0xe469,
  0xe46a, 0xe46b, 0xe46c, 0xe46d, 0xe46e, 0xe46f, 0xe470, 0xe471,
  0xe472, 0xe473, 0xe474, 0xe475, 0xe476, 0xe477, 0xe478, 0xe479,
  0xe47a, 0xe47b, 0xe47c, 0xe47d, 0xe47e, 0xe480, 0xe481, 0xe482,
  0xe483, 0xe484, 0xe485, 0xe486, 0xe487, 0xe488, 0xe489, 0xe48a,
  0xe48b, 0xe48c, 0xe48d, 0xe48e, 0xe48f, 0xe490, 0xe491, 0xe492,
  0xe493, 0xe494, 0xe495, 0xe496, 0xe497, 0xe498, 0xe499, 0xe49a,
  0xe49b, 0xe49c, 0xe49d, 0xe49e, 0xe49f, 0xe4a0, 0xe540, 0xe541,
  0xe542, 0xe543, 0xe544, 0xe545, 0xe546, 0xe547, 0xe548, 0xe549,
  0xe54a, 0xe54b, 0xe54c, 0xe54d, 0xe54e, 0xe54f, 0xe550, 0xe551,
  0xe552, 0xe553, 0xe554, 0xe555, 0xe556, 0xe557, 0xe558, 0xe559,
  0xe55a, 0xe55b, 0xe55c, 0xe55d, 0xe55e, 0xe55f, 0xe560, 0xe561,
  0xe562, 0xe563, 0xe564, 0xe565, 0xe566, 0xe567, 0xe568, 0xe569,
  0xe56a, 0xe56b, 0xe56c, 0xe56d, 0xe56e, 0xe56f, 0xe570, 0xe571,
  0xe572, 0xe573, 0xe574, 0xe575, 0xe576, 0xe577, 0xe578, 0xe579,
  0xe57a, 0xe57b, 0xe57c, 0xe57d, 0xe57e, 0xe580, 0xe581, 0xe582,
  0xe583, 0xe584, 0xe585, 0xe586, 0xe587, 0xe588, 0xe589, 0xe58a,
  0xe58b, 0xe58c, 0xe58d, 0xe58e, 0xe58f, 0xe590, 0xe591, 0xe592,
  0xe593, 0xe594, 0xe595, 0xe596, 0xe597, 0xe598, 0xe599, 0xe59a,
  0xe59b, 0xe59c, 0xe59d, 0xe59e, 0xe59f, 0xe5a0, 0xe640, 0xe641,
  0xe642, 0xe643, 0xe644, 0xe645, 0xe646, 0xe647, 0xe648, 0xe649,
  0xe64a, 0xe64b, 0xe64c, 0xe64d, 0xe64e, 0xe64f, 0xe650, 0xe651,
  0xe652, 0xe653, 0xe654, 0xe655, 0xe656, 0xe657, 0xe658, 0xe659,
  0xe65a, 0xe65b, 0xe65c, 0xe65d, 0xe65e, 0xe65f, 0xe660, 0xe661,
  0xe662, 0xe663, 0xe664, 0xe665, 0xe666, 0xe667, 0xe668, 0xe669,
  0xe66a, 0xe66b, 0xe66c, 0xe66d, 0xe66e, 0xe66f, 0xe670, 0xe671,
  0xe672, 0xe673, 0xe674, 0xe675, 0xe676, 0xe677, 0xe678, 0xe679,
  0xe67a, 0xe67b, 0xe67c, 0xe67d, 0xe67e, 0xe680, 0xe681, 0xe682,
  0xe683, 0xe684, 0xe685, 0xe686, 0xe687, 0xe688, 0xe689, 0xe68a,
  0xe68b, 0xe68c, 0xe68d, 0xe68e, 0xe68f, 0xe690, 0xe691, 0xe692,
  0xe693, 0xe694, 0xe695, 0xe696, 0xe697, 0xe698, 0xe699, 0xe69a,
  0xe69b, 0xe69c, 0xe69d, 0xe69e, 0xe69f, 0xe6a0, 0xe740, 0xe741,
  0xe742, 0xe743, 0xe744, 0xe745, 0xe746, 0xe747, 0xe748, 0xe749,
  0xe74a, 0xe74b, 0xe74c, 0xe74d, 0xe74e, 0xe74f, 0xe750, 0xe751,
  0xe752, 0xe753, 0xe754, 0xe755, 0xe756, 0xe757, 0xe758, 0xe759,
  0xe75a, 0xe75b, 0xe75c, 0xe75d, 0xe75e, 0xe75f, 0xe760, 0xe761,
  0xe762, 0xe763, 0xe764, 0xe765, 0xe766, 0xe767, 0xe768, 0xe769,
  0xe76a, 0xe76b, 0xe76c, 0xe76d, 0xe76e, 0xe76f, 0xe770, 0xe771,
  0xe772, 0xe773, 0xe774, 0xe775, 0xe776, 0xe777, 0xe778, 0xe779,
  0xe77a, 0xe77b, 0xe77c, 0xe77d, 0xe77e, 0xe780, 0xe781, 0xe782,
  0xe783, 0xe784, 0xe785, 0xe786, 0xe787, 0xe788, 0xe789, 0xe78a,
  0xe78b, 0xe78c, 0xe78d, 0xe78e, 0xe78f, 0xe790, 0xe791, 0xe792,
  0xe793, 0xe794, 0xe795, 0xe796, 0xe797, 0xe798, 0xe799, 0xe79a,
  0xe79b, 0xe79c, 0xe79d, 0xe79e, 0xe79f, 0xe7a0, 0xe840, 0xe841,
  0xe842, 0xe843, 0xe844, 0xe845, 0xe846, 0xe847, 0xe848, 0xe849,
  0xe84a, 0xe84b, 0xe84c, 0xe84d, 0xe84e, 0xe84f, 0xe850, 0xe851,
  0xe852, 0xe853, 0xe854, 0xe855, 0xe856, 0xe857, 0xe858, 0xe859,
  0xe85a, 0xe85b, 0xe85c, 0xe85d, 0xe85e, 0xe85f, 0xe860, 0xe861,
  0xe862, 0xe863, 0xe864, 0xe865, 0xe866, 0xe867, 0xe868, 0xe869,
  0xe86a, 0xe86b, 0xe86c, 0xe86d, 0xe86e, 0xe86f, 0xe870, 0xe871,
  0xe872, 0xe873, 0xe874, 0xe875, 0xe876, 0xe877, 0xe878, 0xe879,
  0xe87a, 0xe87b, 0xe87c, 0xe87d, 0xe87e, 0xe880, 0xe881, 0xe882,
  0xe883, 0xe884, 0xe885, 0xe886, 0xe887, 0xe888, 0xe889, 0xe88a,
  0xe88b, 0xe88c, 0xe88d, 0xe88e, 0xe88f, 0xe890, 0xe891, 0xe892,
  0xe893, 0xe894, 0xe895, 0xe896, 0xe897, 0xe898, 0xe899, 0xe89a,
  0xe89b, 0xe89c, 0xe89d, 0xe89e, 0xe89f, 0xe8a0, 0xe940, 0xe941,
  0xe942, 0xe943, 0xe944, 0xe945, 0xe946, 0xe947, 0xe948, 0xe949,
  0xe94a, 0xe94b, 0xe94c, 0xe94d, 0xe94e, 0xe94f, 0xe950, 0xe951,
  0xe952, 0xe953, 0xe954, 0xe955, 0xe956, 0xe957, 0xe958, 0xe959,
  0xe95a, 0xe95b, 0xe95c, 0xe95d, 0xe95e, 0xe95f, 0xe960, 0xe961,
  0xe962, 0xe963, 0xe964, 0xe965, 0xe966, 0xe967, 0xe968, 0xe969,
  0xe96a, 0xe96b, 0xe96c, 0xe96d, 0xe96e, 0xe96f, 0xe970, 0xe971,
  0xe972, 0xe973, 0xe974, 0xe975, 0xe976, 0xe977, 0xe978, 0xe979,
  0xe97a, 0xe97b, 0xe97c, 0xe97d, 0xe97e, 0xe980, 0xe981, 0xe982,
  0xe983, 0xe984, 0xe985, 0xe986, 0xe987, 0xe988, 0xe989, 0xe98a,
  0xe98b, 0xe98c, 0xe98d, 0xe98e, 0xe98f, 0xe990, 0xe991, 0xe992,
  0xe993, 0xe994, 0xe995, 0xe996, 0xe997, 0xe998, 0xe999, 0xe99a,
  0xe99b, 0xe99c, 0xe99d, 0xe99e, 0xe99f, 0xe9a0, 0xea40, 0xea41,
  0xea42, 0xea43, 0xea44, 0xea45, 0xea46, 0xea47, 0xea48, 0xea49,
  0xea4a, 0xea4b, 0xea4c, 0xea4d, 0xea4e, 0xea4f, 0xea50, 0xea51,
  0xea52, 0xea53, 0xea54, 0xea55, 0xea56, 0xea57, 0xea58, 0xea59,
  0xea5a, 0xea5b, 0xea5c, 0xea5d, 0xea5e, 0xea5f, 0xea60, 0xea61,
  0xea62, 0xea63, 0xea64, 0xea65, 0xea66, 0xea67, 0xea68, 0xea69,
  0xea6a, 0xea6b, 0xea6c, 0xea6d, 0xea6e, 0xea6f, 0xea70, 0xea71,
  0xea72, 0xea73, 0xea74, 0xea75, 0xea76, 0xea77, 0xea78, 0xea79,
  0xea7a, 0xea7b, 0xea7c, 0xea7d, 0xea7e, 0xea80, 0xea81, 0xea82,
  0xea83, 0xea84, 0xea85, 0xea86, 0xea87, 0xea88, 0xea89, 0xea8a,
  0xea8b, 0xea8c, 0xea8d, 0xea8e, 0xea8f, 0xea90, 0xea91, 0xea92,
  0xea93, 0xea94, 0xea95, 0xea96, 0xea97, 0xea98, 0xea99, 0xea9a,
  0xea9b, 0xea9c, 0xea9d, 0xea9e, 0xea9f, 0xeaa0, 0xeb40, 0xeb41,
  0xeb42, 0xeb43, 0xeb44, 0xeb45, 0xeb46, 0xeb47, 0xeb48, 0xeb49,
  0xeb4a, 0xeb4b, 0xeb4c, 0xeb4d, 0xeb4e, 0xeb4f, 0xeb50, 0xeb51,
  0xeb52, 0xeb53, 0xeb54, 0xeb55, 0xeb56, 0xeb57, 0xeb58, 0xeb59,
  0xeb5a, 0xeb5b, 0xeb5c, 0xeb5d, 0xeb5e, 0xeb5f, 0xeb60, 0xeb61,
  0xeb62, 0xeb63, 0xeb64, 0xeb65, 0xeb66, 0xeb67, 0xeb68, 0xeb69,
  0xeb6a, 0xeb6b, 0xeb6c, 0xeb6d, 0xeb6e, 0xeb6f, 0xeb70, 0xeb71,
  0xeb72, 0xeb73, 0xeb74, 0xeb75, 0xeb76, 0xeb77, 0xeb78, 0xeb79,
  0xeb7a, 0xeb7b, 0xeb7c, 0xeb7d, 0xeb7e, 0xeb80, 0xeb81, 0xeb82,
  0xeb83, 0xeb84, 0xeb85, 0xeb86, 0xeb87, 0xeb88, 0xeb89, 0xeb8a,
  0xeb8b, 0xeb8c, 0xeb8d, 0xeb8e, 0xeb8f, 0xeb90, 0xeb91, 0xeb92,
  0xeb93, 0xeb94, 0xeb95, 0xeb96, 0xeb97, 0xeb98, 0xeb99, 0xeb9a,
  0xeb9b, 0xeb9c, 0xeb9d, 0xeb9e, 0xeb9f, 0xeba0, 0xec40, 0xec41,
  0xec42, 0xec43, 0xec44, 0xec45, 0xec46, 0xec47, 0xec48, 0xec49,
  0xec4a, 0xec4b, 0xec4c, 0xec4d, 0xec4e, 0xec4f, 0xec50, 0xec51,
  0xec52, 0xec53, 0xec54, 0xec55, 0xec56, 0xec57, 0xec58, 0xec59,
  0xec5a, 0xec5b, 0xec5c, 0xec5d, 0xec5e, 0xec5f, 0xec60, 0xec61,
  0xec62, 0xec63, 0xec64, 0xec65, 0xec66, 0xec67, 0xec68, 0xec69,
  0xec6a, 0xec6b, 0xec6c, 0xec6d, 0xec6e, 0xec6f, 0xec70, 0xec71,
  0xec72, 0xec73, 0xec74, 0xec75, 0xec76, 0xec77, 0xec78, 0xec79,
  0xec7a, 0xec7b, 0xec7c, 0xec7d, 0xec7e, 0xec80, 0xec81, 0xec82,
  0xec83, 0xec84, 0xec85, 0xec86, 0xec87, 0xec88, 0xec89, 0xec8a,
  0xec8b, 0xec8c, 0xec8d, 0xec8e, 0xec8f, 0xec90, 0xec91, 0xec92,
  0xec93, 0xec94, 0xec95, 0xec96, 0xec97, 0xec98, 0xec99, 0xec9a,
  0xec9b, 0xec9c, 0xec9d, 0xec9e, 0xec9f, 0xeca0, 0xed40, 0xed41,
  0xed42, 0xed43, 0xed44, 0xed45, 0xed46, 0xed47, 0xed48, 0xed49,
  0xed4a, 0xed4b, 0xed4c, 0xed4d, 0xed4e, 0xed4f, 0xed50, 0xed51,
  0xed52, 0xed53, 0xed54, 0xed55, 0xed56, 0xed57, 0xed58, 0xed59,
  0xed5a, 0xed5b, 0xed5c, 0xed5d, 0xed5e, 0xed5f, 0xed60, 0xed61,
  0xed62, 0xed63, 0xed64, 0xed65, 0xed66, 0xed67, 0xed68, 0xed69,
  0xed6a, 0xed6b, 0xed6c, 0xed6d, 0xed6e, 0xed6f, 0xed70, 0xed71,
  0xed72, 0xed73, 0xed74, 0xed75, 0xed76, 0xed77, 0xed78, 0xed79,
  0xed7a, 0xed7b, 0xed7c, 0xed7d, 0xed7e, 0xed80, 0xed81, 0xed82,
  0xed83, 0xed84, 0xed85, 0xed86, 0xed87, 0xed88, 0xed89, 0xed8a,
  0xed8b, 0xed8c, 0xed8d, 0xed8e, 0xed8f, 0xed90, 0xed91, 0xed92,
  0xed93, 0xed94, 0xed95, 0xed96, 0xed97, 0xed98, 0xed99, 0xed9a,
  0xed9b, 0xed9c, 0xed9d, 0xed9e, 0xed9f, 0xeda0, 0xee40, 0xee41,
  0xee42, 0xee43, 0xee44, 0xee45, 0xee46, 0xee47, 0xee48, 0xee49,
  0xee4a, 0xee4b, 0xee4c, 0xee4d, 0xee4e, 0xee4f, 0xee50, 0xee51,
  0xee52, 0xee53, 0xee54, 0xee55, 0xee56, 0xee57, 0xee58, 0xee59,
  0xee5a, 0xee5b, 0xee5c, 0xee5d, 0xee5e, 0xee5f, 0xee60, 0xee61,
  0xee62, 0xee63, 0xee64, 0xee65, 0xee66, 0xee67, 0xee68, 0xee69,
  0xee6a, 0xee6b, 0xee6c, 0xee6d, 0xee6e, 0xee6f, 0xee70, 0xee71,
  0xee72, 0xee73, 0xee74, 0xee75, 0xee76, 0xee77, 0xee78, 0xee79,
  0xee7a, 0xee7b, 0xee7c, 0xee7d, 0xee7e, 0xee80, 0xee81, 0xee82,
  0xee83, 0xee84, 0xee85, 0xee86, 0xee87, 0xee88, 0xee89, 0xee8a,
  0xee8b, 0xee8c, 0xee8d, 0xee8e, 0xee8f, 0xee90, 0xee91, 0xee92,
  0xee93, 0xee94, 0xee95, 0xee96, 0xee97, 0xee98, 0xee99, 0xee9a,
  0xee9b, 0xee9c, 0xee9d, 0xee9e, 0xee9f, 0xeea0, 0xef40, 0xef41,
  0xef42, 0xef43, 0xef44, 0xef45, 0xef46, 0xef47, 0xef48, 0xef49,
  0xef4a, 0xef4b, 0xef4c, 0xef4d, 0xef4e, 0xef4f, 0xef50, 0xef51,
  0xef52, 0xef53, 0xef54, 0xef55, 0xef56, 0xef57, 0xef58, 0xef59,
  0xef5a, 0xef5b, 0xef5c, 0xef5d, 0xef5e, 0xef5f, 0xef60, 0xef61,
  0xef62, 0xef63, 0xef64, 0xef65, 0xef66, 0xef67, 0xef68, 0xef69,
  0xef6a, 0xef6b, 0xef6c, 0xef6d, 0xef6e, 0xef6f, 0xef70, 0xef71,
  0xef72, 0xef73, 0xef74, 0xef75, 0xef76, 0xef77, 0xef78, 0xef79,
  0xef7a, 0xef7b, 0xef7c, 0xef7d, 0xef7e, 0xef80, 0xef81, 0xef82,
  0xef83, 0xef84, 0xef85, 0xef86, 0xef87, 0xef88, 0xef89, 0xef8a,
  0xef8b, 0xef8c, 0xef8d, 0xef8e, 0xef8f, 0xef90, 0xef91, 0xef92,
  0xef93, 0xef94, 0xef95, 0xef96, 0xef97, 0xef98, 0xef99, 0xef9a,
  0xef9b, 0xef9c, 0xef9d, 0xef9e, 0xef9f, 0xefa0, 0xf040, 0xf041,
  0xf042, 0xf043, 0xf044, 0xf045, 0xf046, 0xf047, 0xf048, 0xf049,
  0xf04a, 0xf04b, 0xf04c, 0xf04d, 0xf04e, 0xf04f, 0xf050, 0xf051,
  0xf052, 0xf053, 0xf054, 0xf055, 0xf056, 0xf057, 0xf058, 0xf059,
  0xf05a, 0xf05b, 0xf05c, 0xf05d, 0xf05e, 0xf05f, 0xf060, 0xf061,
  0xf062, 0xf063, 0xf064, 0xf065, 0xf066, 0xf067, 0xf068, 0xf069,
  0xf06a, 0xf06b, 0xf06c, 0xf06d, 0xf06e, 0xf06f, 0xf070, 0xf071,
  0xf072, 0xf073, 0xf074, 0xf075, 0xf076, 0xf077, 0xf078, 0xf079,
  0xf07a, 0xf07b, 0xf07c, 0xf07d, 0xf07e, 0xf080, 0xf081, 0xf082,
  0xf083, 0xf084, 0xf085, 0xf086, 0xf087, 0xf088, 0xf089, 0xf08a,
  0xf08b, 0xf08c, 0xf08d, 0xf08e, 0xf08f, 0xf090, 0xf091, 0xf092,
  0xf093, 0xf094, 0xf095, 0xf096, 0xf097, 0xf098, 0xf099, 0xf09a,
  0xf09b, 0xf09c, 0xf09d, 0xf09e, 0xf09f, 0xf0a0, 0xf140, 0xf141,
  0xf142, 0xf143, 0xf144, 0xf145, 0xf146, 0xf147, 0xf148, 0xf149,
  0xf14a, 0xf14b, 0xf14c, 0xf14d, 0xf14e, 0xf14f, 0xf150, 0xf151,
  0xf152, 0xf153, 0xf154, 0xf155, 0xf156, 0xf157, 0xf158, 0xf159,
  0xf15a, 0xf15b, 0xf15c, 0xf15d, 0xf15e, 0xf15f, 0xf160, 0xf161,
  0xf162, 0xf163, 0xf164, 0xf165, 0xf166, 0xf167, 0xf168, 0xf169,
  0xf16a, 0xf16b, 0xf16c, 0xf16d, 0xf16e, 0xf16f, 0xf170, 0xf171,
  0xf172, 0xf173, 0xf174, 0xf175, 0xf176, 0xf177, 0xf178, 0xf179,
  0xf17a, 0xf17b, 0xf17c, 0xf17d, 0xf17e, 0xf180, 0xf181, 0xf182,
  0xf183, 0xf184, 0xf185, 0xf186, 0xf187, 0xf188, 0xf189, 0xf18a,
  0xf18b, 0xf18c, 0xf18d, 0xf18e, 0xf18f, 0xf190, 0xf191, 0xf192,
  0xf193, 0xf194, 0xf195, 0xf196, 0xf197, 0xf198, 0xf199, 0xf19a,
  0xf19b, 0xf19c, 0xf19d, 0xf19e, 0xf19f, 0xf1a0, 0xf240, 0xf241,
  0xf242, 0xf243, 0xf244, 0xf245, 0xf246, 0xf247, 0xf248, 0xf249,
  0xf24a, 0xf24b, 0xf24c, 0xf24d, 0xf24e, 0xf24f, 0xf250, 0xf251,
  0xf252, 0xf253, 0xf254, 0xf255, 0xf256, 0xf257, 0xf258, 0xf259,
  0xf25a, 0xf25b, 0xf25c, 0xf25d, 0xf25e, 0xf25f, 0xf260, 0xf261,
  0xf262, 0xf263, 0xf264, 0xf265, 0xf266, 0xf267, 0xf268, 0xf269,
  0xf26a, 0xf26b, 0xf26c, 0xf26d, 0xf26e, 0xf26f, 0xf270, 0xf271,
  0xf272, 0xf273, 0xf274, 0xf275, 0xf276, 0xf277, 0xf278, 0xf279,
  0xf27a, 0xf27b, 0xf27c, 0xf27d, 0xf27e, 0xf280, 0xf281, 0xf282,
  0xf283, 0xf284, 0xf285, 0xf286, 0xf287, 0xf288, 0xf289, 0xf28a,
  0xf28b, 0xf28c, 0xf28d, 0xf28e, 0xf28f, 0xf290, 0xf291, 0xf292,
  0xf293, 0xf294, 0xf295, 0xf296, 0xf297, 0xf298, 0xf299, 0xf29a,
  0xf29b, 0xf29c, 0xf29d, 0xf29e, 0xf29f, 0xf2a0, 0xf340, 0xf341,
  0xf342, 0xf343, 0xf344, 0xf345, 0xf346, 0xf347, 0xf348, 0xf349,
  0xf34a, 0xf34b, 0xf34c, 0xf34d, 0xf34e, 0xf34f, 0xf350, 0xf351,
  0xf352, 0xf353, 0xf354, 0xf355, 0xf356, 0xf357, 0xf358, 0xf359,
  0xf35a, 0xf35b, 0xf35c, 0xf35d, 0xf35e, 0xf35f, 0xf360, 0xf361,
  0xf362, 0xf363, 0xf364, 0xf365, 0xf366, 0xf367, 0xf368, 0xf369,
  0xf36a, 0xf36b, 0xf36c, 0xf36d, 0xf36e, 0xf36f, 0xf370, 0xf371,
  0xf372, 0xf373, 0xf374, 0xf375, 0xf376, 0xf377, 0xf378, 0xf379,
  0xf37a, 0xf37b, 0xf37c, 0xf37d, 0xf37e, 0xf380, 0xf381, 0xf382,
  0xf383, 0xf384, 0xf385, 0xf386, 0xf387, 0xf388, 0xf389, 0xf38a,
  0xf38b, 0xf38c, 0xf38d, 0xf38e, 0xf38f, 0xf390, 0xf391, 0xf392,
  0xf393, 0xf394, 0xf395, 0xf396, 0xf397, 0xf398, 0xf399, 0xf39a,
  0xf39b, 0xf39c, 0xf39d, 0xf39e, 0xf39f, 0xf3a0, 0xf440, 0xf441,
  0xf442, 0xf443, 0xf444, 0xf445, 0xf446, 0xf447, 0xf448, 0xf449,
  0xf44a, 0xf44b, 0xf44c, 0xf44d, 0xf44e, 0xf44f, 0xf450, 0xf451,
  0xf452, 0xf453, 0xf454, 0xf455, 0xf456, 0xf457, 0xf458, 0xf459,
  0xf45a, 0xf45b, 0xf45c, 0xf45d, 0xf45e, 0xf45f, 0xf460, 0xf461,
  0xf462, 0xf463, 0xf464, 0xf465, 0xf466, 0xf467, 0xf468, 0xf469,
  0xf46a, 0xf46b, 0xf46c, 0xf46d, 0xf46e, 0xf46f, 0xf470, 0xf471,
  0xf472, 0xf473, 0xf474, 0xf475, 0xf476, 0xf477, 0xf478, 0xf479,
  0xf47a, 0xf47b, 0xf47c, 0xf47d, 0xf47e, 0xf480, 0xf481, 0xf482,
  0xf483, 0xf484, 0xf485, 0xf486, 0xf487, 0xf488, 0xf489, 0xf48a,
  0xf48b, 0xf48c, 0xf48d, 0xf48e, 0xf48f, 0xf490, 0xf491, 0xf492,
  0xf493, 0xf494, 0xf495, 0xf496, 0xf497, 0xf498, 0xf499, 0xf49a,
  0xf49b, 0xf49c, 0xf49d, 0xf49e, 0xf49f, 0xf4a0, 0xf540, 0xf541,
  0xf542, 0xf543, 0xf544, 0xf545, 0xf546, 0xf547, 0xf548, 0xf549,
  0xf54a, 0xf54b, 0xf54c, 0xf54d, 0xf54e, 0xf54f, 0xf550, 0xf551,
  0xf552, 0xf553, 0xf554, 0xf555, 0xf556, 0xf557, 0xf558, 0xf559,
  0xf55a, 0xf55b, 0xf55c, 0xf55d, 0xf55e, 0xf55f, 0xf560, 0xf561,
  0xf562, 0xf563, 0xf564, 0xf565, 0xf566, 0xf567, 0xf568, 0xf569,
  0xf56a, 0xf56b, 0xf56c, 0xf56d, 0xf56e, 0xf56f, 0xf570, 0xf571,
  0xf572, 0xf573, 0xf574, 0xf575, 0xf576, 0xf577, 0xf578, 0xf579,
  0xf57a, 0xf57b, 0xf57c, 0xf57d, 0xf57e, 0xf580, 0xf581, 0xf582,
  0xf583, 0xf584, 0xf585, 0xf586, 0xf587, 0xf588, 0xf589, 0xf58a,
  0xf58b, 0xf58c, 0xf58d, 0xf58e, 0xf58f, 0xf590, 0xf591, 0xf592,
  0xf593, 0xf594, 0xf595, 0xf596, 0xf597, 0xf598, 0xf599, 0xf59a,
  0xf59b, 0xf59c, 0xf59d, 0xf59e, 0xf59f, 0xf5a0, 0xf640, 0xf641,
  0xf642, 0xf643, 0xf644, 0xf645, 0xf646, 0xf647, 0xf648, 0xf649,
  0xf64a, 0xf64b, 0xf64c, 0xf64d, 0xf64e, 0xf64f, 0xf650, 0xf651,
  0xf652, 0xf653, 0xf654, 0xf655, 0xf656, 0xf657, 0xf658, 0xf659,
  0xf65a, 0xf65b, 0xf65c, 0xf65d, 0xf65e, 0xf65f, 0xf660, 0xf661,
  0xf662, 0xf663, 0xf664, 0xf665, 0xf666, 0xf667, 0xf668, 0xf669,
  0xf66a, 0xf66b, 0xf66c, 0xf66d, 0xf66e, 0xf66f, 0xf670, 0xf671,
  0xf672, 0xf673, 0xf674, 0xf675, 0xf676, 0xf677, 0xf678, 0xf679,
  0xf67a, 0xf67b, 0xf67c, 0xf67d, 0xf67e, 0xf680, 0xf681, 0xf682,
  0xf683, 0xf684, 0xf685, 0xf686, 0xf687, 0xf688, 0xf689, 0xf68a,
  0xf68b, 0xf68c, 0xf68d, 0xf68e, 0xf68f, 0xf690, 0xf691, 0xf692,
  0xf693, 0xf694, 0xf695, 0xf696, 0xf697, 0xf698, 0xf699, 0xf69a,
  0xf69b, 0xf69c, 0xf69d, 0xf69e, 0xf69f, 0xf6a0, 0xf740, 0xf741,
  0xf742, 0xf743, 0xf744, 0xf745, 0xf746, 0xf747, 0xf748, 0xf749,
  0xf74a, 0xf74b, 0xf74c, 0xf74d, 0xf74e, 0xf74f, 0xf750, 0xf751,
  0xf752, 0xf753, 0xf754, 0xf755, 0xf756, 0xf757, 0xf758, 0xf759,
  0xf75a, 0xf75b, 0xf75c, 0xf75d, 0xf75e, 0xf75f, 0xf760, 0xf761,
  0xf762, 0xf763, 0xf764, 0xf765, 0xf766, 0xf767, 0xf768, 0xf769,
  0xf76a, 0xf76b, 0xf76c, 0xf76d, 0xf76e, 0xf76f, 0xf770, 0xf771,
  0xf772, 0xf773, 0xf774, 0xf775, 0xf776, 0xf777, 0xf778, 0xf779,
  0xf77a, 0xf77b, 0xf77c, 0xf77d, 0xf77e, 0xf780, 0xf781, 0xf782,
  0xf783, 0xf784, 0xf785, 0xf786, 0xf787, 0xf788, 0xf789, 0xf78a,
  0xf78b, 0xf78c, 0xf78d, 0xf78e, 0xf78f, 0xf790, 0xf791, 0xf792,
  0xf793, 0xf794, 0xf795, 0xf796, 0xf797, 0xf798, 0xf799, 0xf79a,
  0xf79b, 0xf79c, 0xf79d, 0xf79e, 0xf79f, 0xf7a0, 0xf840, 0xf841,
  0xf842, 0xf843, 0xf844, 0xf845, 0xf846, 0xf847, 0xf848, 0xf849,
  0xf84a, 0xf84b, 0xf84c, 0xf84d, 0xf84e, 0xf84f, 0xf850, 0xf851,
  0xf852, 0xf853, 0xf854, 0xf855, 0xf856, 0xf857, 0xf858, 0xf859,
  0xf85a, 0xf85b, 0xf85c, 0xf85d, 0xf85e, 0xf85f, 0xf860, 0xf861,
  0xf862, 0xf863, 0xf864, 0xf865, 0xf866, 0xf867, 0xf868, 0xf869,
  0xf86a, 0xf86b, 0xf86c, 0xf86d, 0xf86e, 0xf86f, 0xf870, 0xf871,
  0xf872, 0xf873, 0xf874, 0xf875, 0xf876, 0xf877, 0xf878, 0xf879,
  0xf87a, 0xf87b, 0xf87c, 0xf87d, 0xf87e, 0xf880, 0xf881, 0xf882,
  0xf883, 0xf884, 0xf885, 0xf886, 0xf887, 0xf888, 0xf889, 0xf88a,
  0xf88b, 0xf88c, 0xf88d, 0xf88e, 0xf88f, 0xf890, 0xf891, 0xf892,
  0xf893, 0xf894, 0xf895, 0xf896, 0xf897, 0xf898, 0xf899, 0xf89a,
  0xf89b, 0xf89c, 0xf89d, 0xf89e, 0xf89f, 0xf8a0, 0xf940, 0xf941,
  0xf942, 0xf943, 0xf944, 0xf945, 0xf946, 0xf947, 0xf948, 0xf949,
  0xf94a, 0xf94b, 0xf94c, 0xf94d, 0xf94e, 0xf94f, 0xf950, 0xf951,
  0xf952, 0xf953, 0xf954, 0xf955, 0xf956, 0xf957, 0xf958, 0xf959,
  0xf95a, 0xf95b, 0xf95c, 0xf95d, 0xf95e, 0xf95f, 0xf960, 0xf961,
  0xf962, 0xf963, 0xf964, 0xf965, 0xf966, 0xf967, 0xf968, 0xf969,
  0xf96a, 0xf96b, 0xf96c, 0xf96d, 0xf96e, 0xf96f, 0xf970, 0xf971,
  0xf972, 0xf973, 0xf974, 0xf975, 0xf976, 0xf977, 0xf978, 0xf979,
  0xf97a, 0xf97b, 0xf97c, 0xf97d, 0xf97e, 0xf980, 0xf981, 0xf982,
  0xf983, 0xf984, 0xf985, 0xf986, 0xf987, 0xf988, 0xf989, 0xf98a,
  0xf98b, 0xf98c, 0xf98d, 0xf98e, 0xf98f, 0xf990, 0xf991, 0xf992,
  0xf993, 0xf994, 0xf995, 0xf996, 0xf997, 0xf998, 0xf999, 0xf99a,
  0xf99b, 0xf99c, 0xf99d, 0xf99e, 0xf99f, 0xf9a0, 0xfa40, 0xfa41,
  0xfa42, 0xfa43, 0xfa44, 0xfa45, 0xfa46, 0xfa47, 0xfa48, 0xfa49,
  0xfa4a, 0xfa4b, 0xfa4c, 0xfa4d, 0xfa4e, 0xfa4f, 0xfa50, 0xfa51,
  0xfa52, 0xfa53, 0xfa54, 0xfa55, 0xfa56, 0xfa57, 0xfa58, 0xfa59,
  0xfa5a, 0xfa5b, 0xfa5c, 0xfa5d, 0xfa5e, 0xfa5f, 0xfa60, 0xfa61,
  0xfa62, 0xfa63, 0xfa64, 0xfa65, 0xfa66, 0xfa67, 0xfa68, 0xfa69,
  0xfa6a, 0xfa6b, 0xfa6c, 0xfa6d, 0xfa6e, 0xfa6f, 0xfa70, 0xfa71,
  0xfa72, 0xfa73, 0xfa74, 0xfa75, 0xfa76, 0xfa77, 0xfa78, 0xfa79,
  0xfa7a, 0xfa7b, 0xfa7c, 0xfa7d, 0xfa7e, 0xfa80, 0xfa81, 0xfa82,
  0xfa83, 0xfa84, 0xfa85, 0xfa86, 0xfa87, 0xfa88, 0xfa89, 0xfa8a,
  0xfa8b, 0xfa8c, 0xfa8d, 0xfa8e, 0xfa8f, 0xfa90, 0xfa91, 0xfa92,
  0xfa93, 0xfa94, 0xfa95, 0xfa96, 0xfa97, 0xfa98, 0xfa99, 0xfa9a,
  0xfa9b, 0xfa9c, 0xfa9d, 0xfa9e, 0xfa9f, 0xfaa0, 0xfb40, 0xfb41,
  0xfb42, 0xfb43, 0xfb44, 0xfb45, 0xfb46, 0xfb47, 0xfb48, 0xfb49,
  0xfb4a, 0xfb4b, 0xfb4c, 0xfb4d, 0xfb4e, 0xfb4f, 0xfb50, 0xfb51,
  0xfb52, 0xfb53, 0xfb54, 0xfb55, 0xfb56, 0xfb57, 0xfb58, 0xfb59,
  0xfb5a, 0xfb5b, 0xfb5c, 0xfb5d, 0xfb5e, 0xfb5f, 0xfb60, 0xfb61,
  0xfb62, 0xfb63, 0xfb64, 0xfb65, 0xfb66, 0xfb67, 0xfb68, 0xfb69,
  0xfb6a, 0xfb6b, 0xfb6c, 0xfb6d, 0xfb6e, 0xfb6f, 0xfb70, 0xfb71,
  0xfb72, 0xfb73, 0xfb74, 0xfb75, 0xfb76, 0xfb77, 0xfb78, 0xfb79,
  0xfb7a, 0xfb7b, 0xfb7c, 0xfb7d, 0xfb7e, 0xfb80, 0xfb81, 0xfb82,
  0xfb83, 0xfb84, 0xfb85, 0xfb86, 0xfb87, 0xfb88, 0xfb89, 0xfb8a,
  0xfb8b, 0xfb8c, 0xfb8d, 0xfb8e, 0xfb8f, 0xfb90, 0xfb91, 0xfb92,
  0xfb93, 0xfb94, 0xfb95, 0xfb96, 0xfb97, 0xfb98, 0xfb99, 0xfb9a,
  0xfb9b, 0xfb9c, 0xfb9d, 0xfb9e, 0xfb9f, 0xfba0, 0xfc40, 0xfc41,
  0xfc42, 0xfc43, 0xfc44, 0xfc45, 0xfc46, 0xfc47, 0xfc48, 0xfc49,
  0xfc4a, 0xfc4b, 0xfc4c, 0xfc4d, 0xfc4e, 0xfc4f, 0xfc50, 0xfc51,
  0xfc52, 0xfc53, 0xfc54, 0xfc55, 0xfc56, 0xfc57, 0xfc58, 0xfc59,
  0xfc5a, 0xfc5b, 0xfc5c, 0xfc5d, 0xfc5e, 0xfc5f, 0xfc60, 0xfc61,
  0xfc62, 0xfc63, 0xfc64, 0xfc65, 0xfc66, 0xfc67, 0xfc68, 0xfc69,
  0xfc6a, 0xfc6b, 0xfc6c, 0xfc6d, 0xfc6e, 0xfc6f, 0xfc70, 0xfc71,
  0xfc72, 0xfc73, 0xfc74, 0xfc75, 0xfc76, 0xfc77, 0xfc78, 0xfc79,
  0xfc7a, 0xfc7b, 0xfc7c, 0xfc7d, 0xfc7e, 0xfc80, 0xfc81, 0xfc82,
  0xfc83, 0xfc84, 0xfc85, 0xfc86, 0xfc87, 0xfc88, 0xfc89, 0xfc8a,
  0xfc8b, 0xfc8c, 0xfc8d, 0xfc8e, 0xfc8f, 0xfc90, 0xfc91, 0xfc92,
  0xfc93, 0xfc94, 0xfc95, 0xfc96, 0xfc97, 0xfc98, 0xfc99, 0xfc9a,
  0xfc9b, 0xfc9c, 0xfc9d, 0xfc9e, 0xfc9f, 0xfca0, 0xfd40, 0xfd41,
  0xfd42, 0xfd43, 0xfd44, 0xfd45, 0xfd46, 0xfd47, 0xfd48, 0xfd49,
  0xfd4a, 0xfd4b, 0xfd4c, 0xfd4d, 0xfd4e, 0xfd4f, 0xfd50, 0xfd51,
  0xfd52, 0xfd53, 0xfd54, 0xfd55, 0xfd56, 0xfd57, 0xfd58, 0xfd59,
  0xfd5a, 0xfd5b, 0xfd5c, 0xfd5d, 0xfd5e, 0xfd5f, 0xfd60, 0xfd61,
  0xfd62, 0xfd63, 0xfd64, 0xfd65, 0xfd66, 0xfd67, 0xfd68, 0xfd69,
  0xfd6a, 0xfd6b, 0xfd6c, 0xfd6d, 0xfd6e, 0xfd6f, 0xfd70, 0xfd71,
  0xfd72, 0xfd73, 0xfd74, 0xfd75, 0xfd76, 0xfd77, 0xfd78, 0xfd79,
  0xfd7a, 0xfd7b, 0xfd7c, 0xfd7d, 0xfd7e, 0xfd80, 0xfd81, 0xfd82,
  0xfd83, 0xfd84, 0xfd85, 0xfd86, 0xfd87, 0xfd88, 0xfd89, 0xfd8a,
  0xfd8b, 0xfd8c, 0xfd8d, 0xfd8e, 0xfd8f, 0xfd90, 0xfd91, 0xfd92,
  0xfd93, 0xfd94, 0xfd95, 0xfd96, 0xfd97, 0xfd98, 0xfd99, 0xfd9a,
  0xfd9b, 0xfd9c, 0xfd9d, 0xfd9e, 0xfd9f, 0xfda0, 0xfe40, 0xfe41,
  0xfe42, 0xfe43, 0xfe44, 0xfe45, 0xfe46, 0xfe47, 0xfe48, 0xfe49,
  0xfe4a, 0xfe4b, 0xfe4c, 0xfe4d, 0xfe4e, 0xfe4f, 0xa955, 0xa968,
  0xa969, 0xa96a, 0xa96b, 0xa96c, 0xa96d, 0xa96e, 0xa96f, 0xa970,
  0xa971, 0xa972, 0xa973, 0xa974, 0xa975, 0xa976, 0xa977, 0xa978,
  0xa979, 0xa97a, 0xa97b, 0xa97c, 0xa97d, 0xa97e, 0xa980, 0xa981,
  0xa982, 0xa983, 0xa984, 0xa985, 0xa986, 0xa987, 0xa988, 0xa956,
  0xa957,
};

static const Summary16 gbkext_inv_uni2indx_page02[14] = {
  /* 0x0200 */
  {    0, 0x0000 }, {    0, 0x0000 }, {    0, 0x0000 }, {    0, 0x0000 },
  {    0, 0x0000 }, {    0, 0x0000 }, {    0, 0x0000 }, {    0, 0x0000 },
  {    0, 0x0000 }, {    0, 0x0000 }, {    0, 0x0000 }, {    0, 0x0000 },
  {    0, 0x0c00 }, {    2, 0x0200 },
};
static const Summary16 gbkext_inv_uni2indx_page20[44] = {
  /* 0x2000 */
  {    3, 0x0000 }, {    3, 0x0029 }, {    6, 0x0020 }, {    7, 0x0020 },
  {    8, 0x0000 }, {    8, 0x0000 }, {    8, 0x0000 }, {    8, 0x0000 },
  {    8, 0x0000 }, {    8, 0x0000 }, {    8, 0x0000 }, {    8, 0x0000 },
  {    8, 0x0000 }, {    8, 0x0000 }, {    8, 0x0000 }, {    8, 0x0000 },
  /* 0x2100 */
  {    8, 0x0220 }, {   10, 0x0000 }, {   10, 0x0002 }, {   11, 0x0000 },
  {   11, 0x0000 }, {   11, 0x0000 }, {   11, 0x0000 }, {   11, 0x0000 },
  {   11, 0x0000 }, {   11, 0x03c0 }, {   15, 0x0000 }, {   15, 0x0000 },
  {   15, 0x0000 }, {   15, 0x0000 }, {   15, 0x0000 }, {   15, 0x0000 },
  /* 0x2200 */
  {   15, 0x0000 }, {   15, 0x8020 }, {   17, 0x0008 }, {   18, 0x0000 },
  {   18, 0x0000 }, {   18, 0x0004 }, {   19, 0x00c0 }, {   21, 0x0000 },
  {   21, 0x0000 }, {   21, 0x0020 }, {   22, 0x0000 }, {   22, 0x8000 },
};
static const Summary16 gbkext_inv_uni2indx_page25[17] = {
  /* 0x2500 */
  {   23, 0x0000 }, {   23, 0x0000 }, {   23, 0x0000 }, {   23, 0x0000 },
  {   23, 0x0000 }, {   23, 0xffff }, {   39, 0xffff }, {   55, 0x000f },
  {   59, 0xfffe }, {   74, 0x0038 }, {   77, 0x0000 }, {   77, 0x3000 },
  {   79, 0x0000 }, {   79, 0x0000 }, {   79, 0x003c }, {   83, 0x0000 },
  /* 0x2600 */
  {   83, 0x0200 },
};
static const Summary16 gbkext_inv_uni2indx_page30[16] = {
  /* 0x3000 */
  {   84, 0x00c0 }, {   86, 0x6004 }, {   89, 0x03fe }, {   98, 0x0000 },
  {   98, 0x0000 }, {   98, 0x0000 }, {   98, 0x0000 }, {   98, 0x0000 },
  {   98, 0x0000 }, {   98, 0x7800 }, {  102, 0x0000 }, {  102, 0x0000 },
  {  102, 0x0000 }, {  102, 0x0000 }, {  102, 0x0000 }, {  102, 0x7000 },
};
static const Summary16 gbkext_inv_uni2indx_page32[30] = {
  /* 0x3200 */
  {  105, 0x0000 }, {  105, 0x0000 }, {  105, 0x0000 }, {  105, 0x0002 },
  {  106, 0x0000 }, {  106, 0x0000 }, {  106, 0x0000 }, {  106, 0x0000 },
  {  106, 0x0000 }, {  106, 0x0000 }, {  106, 0x0008 }, {  107, 0x0000 },
  {  107, 0x0000 }, {  107, 0x0000 }, {  107, 0x0000 }, {  107, 0x0000 },
  /* 0x3300 */
  {  107, 0x0000 }, {  107, 0x0000 }, {  107, 0x0000 }, {  107, 0x0000 },
  {  107, 0x0000 }, {  107, 0x0000 }, {  107, 0x0000 }, {  107, 0x0000 },
  {  107, 0xc000 }, {  109, 0x7000 }, {  112, 0x0002 }, {  113, 0x0000 },
  {  113, 0x4010 }, {  115, 0x0026 },
};
static const Summary16 gbkext_inv_uni2indx_page4e[1307] = {
  /* 0x4e00 */
  {  118, 0x8074 }, {  123, 0x8084 }, {  126, 0xc24b }, {  133, 0x10aa },
  {  138, 0x0457 }, {  144, 0x0ca2 }, {  149, 0xfdbc }, {  161, 0xbff4 },
  {  173, 0x04bf }, {  181, 0x72c1 }, {  188, 0x8408 }, {  191, 0x73d3 },
  {  201, 0x9100 }, {  204, 0x1c05 }, {  209, 0xe2c5 }, {  217, 0x5712 },
  /* 0x4f00 */
  {  224, 0x19fd }, {  234, 0x307c }, {  241, 0x730a }, {  248, 0xcaaa },
  {  256, 0x1fb7 }, {  267, 0x0054 }, {  270, 0x6d46 }, {  278, 0x27a6 },
  {  286, 0x54e7 }, {  295, 0xd76d }, {  306, 0x2816 }, {  311, 0x7fdf },
  {  325, 0x3bc7 }, {  335, 0x0a7c }, {  342, 0x18b5 }, {  349, 0xbaf5 },
  /* 0x5000 */
  {  360, 0x4fff }, {  373, 0x68eb }, {  382, 0x889d }, {  389, 0xabff },
  {  402, 0x2e77 }, {  412, 0xebdf }, {  425, 0xefdf }, {  439, 0x373f },
  {  450, 0xdede }, {  462, 0xffff }, {  478, 0xec57 }, {  488, 0xf3fb },
  {  501, 0x7fff }, {  516, 0xfbbf }, {  530, 0x8f3f }, {  541, 0xf7d7 },
  /* 0x5100 */
  {  554, 0xf73f }, {  567, 0xfffb }, {  582, 0xfffd }, {  597, 0x7fff },
  {  612, 0xd484 }, {  618, 0xeb8d }, {  628, 0x86db }, {  637, 0xc404 },
  {  641, 0xccd8 }, {  649, 0xe51b }, {  658, 0x67ca }, {  667, 0xc710 },
  {  673, 0x652e }, {  681, 0xd7fd }, {  694, 0x57ec }, {  704, 0x4096 },
  /* 0x5200 */
  {  709, 0x9a30 }, {  715, 0xd039 }, {  722, 0x94ee }, {  731, 0x5036 },
  {  737, 0xcbf0 }, {  746, 0xafac }, {  756, 0x795d }, {  766, 0x5ffb },
  {  779, 0xfef9 }, {  792, 0x17f6 }, {  802, 0xc0f0 }, {  808, 0x3ff1 },
  {  819, 0xf577 }, {  831, 0x7eba }, {  842, 0xffef }, {  857, 0x39fe },
  /* 0x5300 */
  {  868, 0x5e9e }, {  878, 0xd91e }, {  887, 0xbbb4 }, {  897, 0x31ff },
  {  908, 0x3855 }, {  915, 0x2b11 }, {  921, 0x3520 }, {  926, 0x7a44 },
  {  933, 0xc58b }, {  941, 0x5adf }, {  952, 0xbc93 }, {  961, 0x77bf },
  {  974, 0xc0f9 }, {  982, 0x742d }, {  990, 0x0086 }, {  993, 0xc410 },
  /* 0x5400 */
  {  997, 0x08a5 }, { 1002, 0x1710 }, { 1007, 0x0434 }, { 1011, 0xa4c9 },
  { 1018, 0xf2b6 }, { 1028, 0xe402 }, { 1033, 0xfeab }, { 1045, 0xc611 },
  { 1051, 0x27aa }, { 1059, 0xd18a }, { 1066, 0x4027 }, { 1071, 0x56e5 },
  { 1080, 0x0c28 }, { 1084, 0x0940 }, { 1087, 0x981f }, { 1095, 0x4bf3 },
  /* 0x5500 */
  { 1105, 0x7d3d }, { 1116, 0xf7ec }, { 1128, 0x2b62 }, { 1135, 0x2f74 },
  { 1144, 0xf9a5 }, { 1154, 0xef9e }, { 1166, 0x8b0d }, { 1173, 0xa61f },
  { 1182, 0x7060 }, { 1187, 0x4ced }, { 1196, 0xff7f }, { 1211, 0x9555 },
  { 1219, 0xcdcf }, { 1230, 0x4fa1 }, { 1238, 0x6285 }, { 1244, 0x9f53 },
  /* 0x5600 */
  { 1254, 0x2cfc }, { 1263, 0x36ff }, { 1275, 0xcf67 }, { 1286, 0x75a9 },
  { 1295, 0x8fff }, { 1308, 0xec6f }, { 1319, 0xe0eb }, { 1328, 0xe7bd },
  { 1340, 0x3f9f }, { 1352, 0xfff7 }, { 1367, 0x7ff7 }, { 1381, 0xef7f },
  { 1395, 0xfbff }, { 1410, 0x136f }, { 1419, 0xd7e8 }, { 1429, 0x19cc },
  /* 0x5700 */
  { 1436, 0xf8a7 }, { 1446, 0x6fff }, { 1460, 0x08f7 }, { 1468, 0xb1f6 },
  { 1478, 0x0b7a }, { 1486, 0x037c }, { 1493, 0x50ac }, { 1499, 0xe737 },
  { 1510, 0xe783 }, { 1519, 0xf7f3 }, { 1532, 0x9520 }, { 1537, 0xfeeb },
  { 1550, 0x37f3 }, { 1561, 0x58cb }, { 1569, 0x5fee }, { 1581, 0xd8ef },
  /* 0x5800 */
  { 1592, 0xd73a }, { 1602, 0xbddd }, { 1614, 0xfbec }, { 1626, 0xffde },
  { 1640, 0xcfef }, { 1653, 0xbeed }, { 1665, 0xe7df }, { 1678, 0xbfff },
  { 1693, 0xfdd4 }, { 1704, 0x39f3 }, { 1714, 0xfcff }, { 1728, 0xefff },
  { 1743, 0xffdd }, { 1757, 0xffdd }, { 1771, 0xa7ef }, { 1783, 0xfdb6 },
  /* 0x5900 */
  { 1795, 0x5f6b }, { 1806, 0x698f }, { 1815, 0x114f }, { 1822, 0xe86d },
  { 1831, 0x3469 }, { 1838, 0xfa0d }, { 1847, 0xffda }, { 1860, 0xdca7 },
  { 1870, 0xda21 }, { 1877, 0xbd33 }, { 1887, 0x30c7 }, { 1894, 0xb5fb },
  { 1906, 0xf3bf }, { 1919, 0xca60 }, { 1925, 0xeed7 }, { 1937, 0x75ff },
  /* 0x5a00 */
  { 1950, 0xec05 }, { 1957, 0x6ef5 }, { 1968, 0xfdd6 }, { 1980, 0xefa9 },
  { 1991, 0xf9be }, { 2003, 0xfbdf }, { 2017, 0xfb7b }, { 2030, 0x7b0f },
  { 2040, 0xffff }, { 2056, 0xf3fb }, { 2069, 0xfbff }, { 2084, 0xbed3 },
  { 2095, 0xedf9 }, { 2107, 0xeeab }, { 2118, 0xf5b4 }, { 2128, 0xfffd },
  /* 0x5b00 */
  { 2143, 0xfdff }, { 2158, 0xff3f }, { 2172, 0xffff }, { 2188, 0xff6b },
  { 2201, 0xfffe }, { 2216, 0x4044 }, { 2219, 0xe983 }, { 2227, 0xdbd4 },
  { 2237, 0x6444 }, { 2242, 0x8057 }, { 2248, 0xf380 }, { 2255, 0x1c86 },
  { 2261, 0xef0b }, { 2271, 0x1ff2 }, { 2281, 0xbecd }, { 2292, 0x60fe },
  /* 0x5c00 */
  { 2301, 0x79ad }, { 2311, 0xca8d }, { 2319, 0xef4b }, { 2330, 0x00ed },
  { 2336, 0x30d8 }, { 2342, 0xbddc }, { 2353, 0x3f94 }, { 2362, 0x79fd },
  { 2374, 0xcef9 }, { 2385, 0xe02c }, { 2391, 0xc5f3 }, { 2401, 0x5e55 },
  { 2410, 0xf7ed }, { 2423, 0xfdfb }, { 2437, 0xda8d }, { 2446, 0xf7fe },
  /* 0x5d00 */
  { 2460, 0xbf33 }, { 2471, 0xb7af }, { 2483, 0x9d2f }, { 2493, 0x9fef },
  { 2506, 0xe37f }, { 2518, 0xd6ff }, { 2531, 0x65ff }, { 2543, 0xffef },
  { 2558, 0xfffb }, { 2573, 0xddff }, { 2587, 0xffff }, { 2603, 0xff7f },
  { 2618, 0xdfdf }, { 2632, 0x97ff }, { 2645, 0x3419 }, { 2651, 0x9f61 },
  /* 0x5e00 */
  { 2660, 0x6e91 }, { 2668, 0xc08c }, { 2673, 0x9f3f }, { 2685, 0xc67d },
  { 2695, 0xefcb }, { 2707, 0xb7cf }, { 2719, 0xfff9 }, { 2733, 0x42a3 },
  { 2739, 0x732e }, { 2748, 0x2904 }, { 2752, 0xdf1e }, { 2763, 0xbc17 },
  { 2772, 0xf9ff }, { 2786, 0xf7b1 }, { 2797, 0xfaff }, { 2811, 0x3b2f },
  /* 0x5f00 */
  { 2821, 0x72e0 }, { 2828, 0x7655 }, { 2837, 0x591e }, { 2845, 0xe9fd },
  { 2857, 0xfffe }, { 2872, 0xde12 }, { 2880, 0xc9a9 }, { 2888, 0xe574 },
  { 2897, 0xe048 }, { 2902, 0xec5a }, { 2911, 0x9afd }, { 2922, 0xcf5f },
  { 2934, 0x4d87 }, { 2942, 0xdc38 }, { 2950, 0x936c }, { 2958, 0x16dd },
  /* 0x6000 */
  { 2967, 0x1b80 }, { 2972, 0xc58b }, { 2980, 0x701c }, { 2986, 0x67df },
  { 2998, 0xd7f1 }, { 3009, 0xd9da }, { 3019, 0x4063 }, { 3024, 0x40b6 },
  { 3030, 0xcde7 }, { 3041, 0x53ab }, { 3050, 0x46b6 }, { 3058, 0xe6e9 },
  { 3068, 0xf39f }, { 3080, 0x4add }, { 3089, 0x043e }, { 3095, 0xf9a6 },
  /* 0x6100 */
  { 3105, 0x1cbc }, { 3113, 0x7bdf }, { 3126, 0xf726 }, { 3136, 0x7fff },
  { 3151, 0xaaff }, { 3163, 0xdfdd }, { 3176, 0xfe7b }, { 3189, 0xff5e },
  { 3202, 0xb7ff }, { 3216, 0xdfef }, { 3230, 0xec7f }, { 3242, 0xbf7f },
  { 3256, 0xf2fb }, { 3268, 0xffe9 }, { 3281, 0xffbf }, { 3296, 0x7fdf },
  /* 0x6200 */
  { 3310, 0x02bf }, { 3318, 0x7218 }, { 3324, 0xabc9 }, { 3333, 0x1f67 },
  { 3343, 0x8474 }, { 3349, 0xf6e1 }, { 3359, 0x0137 }, { 3365, 0x2db6 },
  { 3374, 0xf9ee }, { 3386, 0x7211 }, { 3392, 0xe6c8 }, { 3400, 0x45dd },
  { 3409, 0x880b }, { 3414, 0x6022 }, { 3418, 0x0c13 }, { 3423, 0x0f25 },
  /* 0x6300 */
  { 3430, 0xbc79 }, { 3440, 0x13bd }, { 3449, 0x72c0 }, { 3455, 0xd9fb },
  { 3467, 0x0593 }, { 3473, 0x3fde }, { 3485, 0x9d71 }, { 3494, 0xf33d },
  { 3505, 0x287a }, { 3512, 0xfeba }, { 3524, 0x8852 }, { 3529, 0xaa66 },
  { 3537, 0x1daf }, { 3547, 0xbfba }, { 3559, 0xd9f4 }, { 3569, 0x5eab },
  /* 0x6400 */
  { 3579, 0x67d8 }, { 3588, 0xa7e6 }, { 3598, 0xcbbc }, { 3608, 0x5bef },
  { 3620, 0xfa0d }, { 3629, 0xbeeb }, { 3641, 0xdd7f }, { 3654, 0xf8ff },
  { 3667, 0xff4b }, { 3679, 0xbd99 }, { 3689, 0x8def }, { 3700, 0xea5e },
  { 3710, 0x9fda }, { 3721, 0xbe7a }, { 3732, 0xffab }, { 3745, 0xffff },
  /* 0x6500 */
  { 3761, 0xfdfe }, { 3775, 0xfefb }, { 3789, 0x37df }, { 3801, 0x348f },
  { 3809, 0x6cdf }, { 3820, 0x959d }, { 3829, 0xe7b3 }, { 3840, 0xff6a },
  { 3852, 0xe77f }, { 3865, 0x6574 }, { 3873, 0x554d }, { 3881, 0xcdfe },
  { 3893, 0x2785 }, { 3900, 0xff3b }, { 3913, 0x0c1a }, { 3918, 0xfb3c },
  /* 0x6600 */
  { 3929, 0x2bb2 }, { 3937, 0x5dc7 }, { 3947, 0x5e5e }, { 3957, 0xaf8d },
  { 3967, 0x67f5 }, { 3978, 0x7b03 }, { 3986, 0x3ead }, { 3996, 0xbb2e },
  { 4006, 0xef6b }, { 4018, 0xdf3d }, { 4030, 0xbe7f }, { 4043, 0xbdef },
  { 4056, 0xffff }, { 4072, 0xc5ff }, { 4084, 0xfdbf }, { 4098, 0x2d62 },
  /* 0x6700 */
  { 4105, 0xd0fe }, { 4115, 0x574e }, { 4124, 0x42bf }, { 4133, 0xdbcd },
  { 4144, 0x2cb2 }, { 4151, 0x2fb4 }, { 4160, 0x58dc }, { 4168, 0x2f52 },
  { 4176, 0xf56d }, { 4187, 0x8a5e }, { 4195, 0x5253 }, { 4202, 0xfe16 },
  { 4212, 0x7fe5 }, { 4224, 0x88e0 }, { 4229, 0x6dda }, { 4239, 0x5fe4 },
  /* 0x6800 */
  { 4249, 0x205e }, { 4255, 0xdf35 }, { 4266, 0xf9fd }, { 4279, 0x8c73 },
  { 4287, 0xa880 }, { 4291, 0xffc4 }, { 4302, 0xf400 }, { 4307, 0xff2f },
  { 4320, 0x7f95 }, { 4331, 0xff77 }, { 4345, 0x5e3b }, { 4355, 0xffd6 },
  { 4368, 0xd5fa }, { 4379, 0xfadb }, { 4391, 0xbff6 }, { 4404, 0xe9dc },
  /* 0x6900 */
  { 4414, 0x97dd }, { 4425, 0x7ffa }, { 4438, 0xdfee }, { 4451, 0x5dee },
  { 4462, 0xfffb }, { 4477, 0x9b6f }, { 4488, 0xb7b6 }, { 4499, 0xec7d },
  { 4510, 0xdc2a }, { 4518, 0xe6cf }, { 4529, 0xd67f }, { 4541, 0xf76d },
  { 4553, 0xabfd }, { 4565, 0x77ee }, { 4577, 0xdffe }, { 4591, 0x5ffb },
  /* 0x6a00 */
  { 4604, 0xfbff }, { 4619, 0x7e7f }, { 4632, 0x7afd }, { 4644, 0x9fdd },
  { 4656, 0xff6f }, { 4670, 0xf4fe }, { 4682, 0xffdd }, { 4696, 0xedfd },
  { 4709, 0xbfee }, { 4722, 0xff7c }, { 4735, 0xe5fe }, { 4747, 0xffff },
  { 4763, 0xffff }, { 4779, 0xffff }, { 4795, 0xffff }, { 4811, 0xffff },
  /* 0x6b00 */
  { 4827, 0xffff }, { 4843, 0xffff }, { 4859, 0xff60 }, { 4869, 0xb97b },
  { 4880, 0xed37 }, { 4891, 0xfdff }, { 4906, 0xfb03 }, { 4915, 0xe5ff },
  { 4928, 0xd121 }, { 4934, 0xf3b3 }, { 4945, 0xfbfd }, { 4959, 0x7f47 },
  { 4970, 0x57d9 }, { 4980, 0xf503 }, { 4988, 0x73fd }, { 5000, 0xddd7 },
  /* 0x6c00 */
  { 5012, 0x5f1f }, { 5023, 0x7084 }, { 5028, 0x3829 }, { 5034, 0xdeca },
  { 5044, 0xf938 }, { 5053, 0x074e }, { 5060, 0xf8ec }, { 5070, 0x9daa },
  { 5079, 0x6c91 }, { 5086, 0x75e6 }, { 5096, 0x9105 }, { 5101, 0x04f1 },
  { 5107, 0xe9cf }, { 5118, 0xb706 }, { 5126, 0x32d0 }, { 5132, 0x8214 },
  /* 0x6d00 */
  { 5136, 0xa76d }, { 5146, 0xb17b }, { 5156, 0xb35f }, { 5167, 0x85d1 },
  { 5174, 0x1215 }, { 5179, 0xa9e1 }, { 5187, 0x39b6 }, { 5196, 0xee6f },
  { 5208, 0xacdb }, { 5218, 0x17c5 }, { 5226, 0x3024 }, { 5230, 0x7edb },
  { 5242, 0xe70e }, { 5251, 0x9cbd }, { 5261, 0xa7ac }, { 5270, 0xe575 },
  /* 0x6e00 */
  { 5280, 0x8bdf }, { 5291, 0xdb2c }, { 5300, 0x55c4 }, { 5307, 0xfaeb },
  { 5319, 0x9fe7 }, { 5331, 0x76a7 }, { 5341, 0xb7ff }, { 5355, 0x3fff },
  { 5369, 0x7d97 }, { 5380, 0x6efe }, { 5392, 0x7b5b }, { 5403, 0xd329 },
  { 5411, 0x7779 }, { 5422, 0x3b45 }, { 5430, 0xfc88 }, { 5438, 0xfdef },
  /* 0x6f00 */
  { 5452, 0x7dbb }, { 5464, 0xffc7 }, { 5477, 0x51ee }, { 5486, 0xbfb5 },
  { 5498, 0xd73f }, { 5510, 0xaeff }, { 5523, 0x9fbb }, { 5535, 0xeaeb },
  { 5546, 0x8cef }, { 5556, 0xefff }, { 5571, 0xff7d }, { 5585, 0xfdb7 },
  { 5598, 0xfdfa }, { 5611, 0xbff9 }, { 5624, 0x3ffc }, { 5636, 0xffff },
  /* 0x7000 */
  { 5652, 0xffff }, { 5668, 0xf3fd }, { 5681, 0xfff7 }, { 5696, 0xfddf },
  { 5710, 0x6fff }, { 5724, 0xbfff }, { 5739, 0x47ff }, { 5751, 0x2e9e },
  { 5760, 0xb9de }, { 5771, 0xcd8b }, { 5780, 0x07ff }, { 5791, 0xc475 },
  { 5799, 0xfaf0 }, { 5809, 0x74ff }, { 5821, 0x442f }, { 5828, 0xdd7f },
  /* 0x7100 */
  { 5841, 0xf9ff }, { 5855, 0xf896 }, { 5864, 0x7fbf }, { 5878, 0xffbc },
  { 5891, 0xabdf }, { 5903, 0xafff }, { 5917, 0xbe2f }, { 5928, 0xdaf3 },
  { 5939, 0x7bef }, { 5952, 0x7cef }, { 5964, 0xeefe }, { 5977, 0xfdd7 },
  { 5990, 0xbff7 }, { 6004, 0xffcf }, { 6018, 0xbf5e }, { 6030, 0xfdff },
  /* 0x7200 */
  { 6045, 0xffbf }, { 6060, 0xdfff }, { 6075, 0xeaff }, { 6088, 0x541c },
  { 6094, 0xce7f }, { 6106, 0x55bb }, { 6116, 0x3d39 }, { 6125, 0x39db },
  { 6135, 0x53ec }, { 6144, 0x7ffb }, { 6158, 0x4fff }, { 6171, 0xfc2e },
  { 6181, 0x9ee1 }, { 6190, 0xbd7a }, { 6201, 0x0cfc }, { 6209, 0xe260 },
  /* 0x7300 */
  { 6215, 0xbbf5 }, { 6227, 0x8717 }, { 6235, 0xa1d9 }, { 6243, 0x3c6d },
  { 6252, 0xdfff }, { 6267, 0xff7a }, { 6280, 0x4ffe }, { 6292, 0xbfff },
  { 6307, 0xb56f }, { 6318, 0x77bd }, { 6330, 0x35fb }, { 6341, 0xf372 },
  { 6351, 0x58fa }, { 6360, 0xbdfc }, { 6372, 0xdd5e }, { 6383, 0xfffb },
  /* 0x7400 */
  { 6398, 0x7997 }, { 6408, 0xf3fe }, { 6421, 0xaa9b }, { 6430, 0xef86 },
  { 6440, 0xfffd }, { 6455, 0x215f }, { 6463, 0xdfff }, { 6478, 0xbf3e },
  { 6490, 0xb774 }, { 6500, 0xaffe }, { 6513, 0xfc7f }, { 6526, 0xfbff },
  { 6541, 0xffff }, { 6557, 0xaffb }, { 6570, 0x3fa2 }, { 6579, 0x7f2f },
  /* 0x7500 */
  { 6591, 0x5fef }, { 6604, 0x68f5 }, { 6613, 0x44df }, { 6622, 0xb250 },
  { 6628, 0x26de }, { 6637, 0xe1ef }, { 6648, 0xfb9f }, { 6661, 0x7ceb },
  { 6672, 0x77b7 }, { 6684, 0x5929 }, { 6691, 0x27c4 }, { 6698, 0x8cc0 },
  { 6703, 0xd843 }, { 6710, 0xb68b }, { 6719, 0xf223 }, { 6727, 0x6dec },
  /* 0x7600 */
  { 6737, 0xebd4 }, { 6747, 0x745e }, { 6756, 0xd18a }, { 6763, 0x2ec6 },
  { 6771, 0xcff6 }, { 6783, 0xafaf }, { 6795, 0x77f7 }, { 6808, 0x96ff },
  { 6820, 0xb62b }, { 6829, 0xfdb5 }, { 6841, 0xbfef }, { 6855, 0x7fe9 },
  { 6867, 0x1a9b }, { 6875, 0x7628 }, { 6882, 0x3fdf }, { 6895, 0xace9 },
  /* 0x7700 */
  { 6904, 0xd46d }, { 6913, 0x79ff }, { 6926, 0x5cba }, { 6935, 0xea1f },
  { 6945, 0xff74 }, { 6957, 0xf3fc }, { 6969, 0xe691 }, { 6977, 0x1dff },
  { 6989, 0x8fce }, { 6999, 0x7ff9 }, { 7012, 0xe95a }, { 7021, 0x57d6 },
  { 7031, 0xdfff }, { 7046, 0xe77f }, { 7059, 0x8553 }, { 7066, 0x1eb7 },
  /* 0x7800 */
  { 7076, 0xcdf8 }, { 7086, 0x4a29 }, { 7092, 0xcd17 }, { 7101, 0xa06e },
  { 7108, 0xaf5e }, { 7119, 0xdf1a }, { 7129, 0x83ff }, { 7140, 0xef7f },
  { 7154, 0x8d7f }, { 7165, 0x6275 }, { 7173, 0xff55 }, { 7185, 0xbde0 },
  { 7194, 0xf1dd }, { 7205, 0xfdce }, { 7217, 0xeeff }, { 7231, 0xfb6b },
  /* 0x7900 */
  { 7243, 0xffdd }, { 7257, 0xbff7 }, { 7271, 0xffef }, { 7286, 0xa3ef },
  { 7297, 0xfcbc }, { 7308, 0x0337 }, { 7315, 0x5e5a }, { 7324, 0xfa7f },
  { 7337, 0x7bcc }, { 7347, 0xfbff }, { 7362, 0xff7f }, { 7377, 0x91f7 },
  { 7387, 0xd5b4 }, { 7396, 0x7ed9 }, { 7407, 0x5527 }, { 7415, 0xd6fe },
  /* 0x7a00 */
  { 7427, 0x97b2 }, { 7436, 0xbb6f }, { 7448, 0xfff6 }, { 7462, 0x4577 },
  { 7471, 0xffbf }, { 7486, 0xff7d }, { 7500, 0xffff }, { 7516, 0x782e },
  { 7524, 0xdea4 }, { 7533, 0x4e19 }, { 7540, 0xce9e }, { 7550, 0x7ff7 },
  { 7564, 0xf7ff }, { 7579, 0x3dbf }, { 7591, 0x5f96 }, { 7601, 0x59ff },
  /* 0x7b00 */
  { 7613, 0x72a7 }, { 7622, 0xb5cd }, { 7632, 0xa28e }, { 7639, 0xaaf5 },
  { 7649, 0x655f }, { 7659, 0xd2a8 }, { 7666, 0xbffa }, { 7679, 0xb559 },
  { 7688, 0xdfde }, { 7701, 0xcf4e }, { 7711, 0xc039 }, { 7717, 0xfeed },
  { 7730, 0xef3d }, { 7742, 0xd9f5 }, { 7753, 0xbb9d }, { 7764, 0xaf7d },
  /* 0x7c00 */
  { 7776, 0x677f }, { 7788, 0x7fbf }, { 7802, 0xfb3f }, { 7815, 0x7eff },
  { 7829, 0xdffc }, { 7842, 0xffff }, { 7858, 0xffff }, { 7874, 0xc7e7 },
  { 7885, 0xfdff }, { 7900, 0x0e59 }, { 7907, 0xbbcb }, { 7918, 0x8df1 },
  { 7927, 0xca5d }, { 7936, 0x6d1f }, { 7946, 0x7efe }, { 7959, 0xf6ff },
  /* 0x7d00 */
  { 7973, 0xfbff }, { 7988, 0xffff }, { 8004, 0x777a }, { 8015, 0xffff },
  { 8031, 0xffff }, { 8047, 0xffff }, { 8063, 0xbfff }, { 8078, 0xff7f },
  { 8093, 0xffff }, { 8109, 0xffff }, { 8125, 0xbfbf }, { 8139, 0xffff },
  { 8155, 0xffff }, { 8171, 0xffff }, { 8187, 0xffff }, { 8203, 0xffff },
  /* 0x7e00 */
  { 8219, 0xffff }, { 8235, 0xffff }, { 8251, 0xffff }, { 8267, 0xf7ff },
  { 8282, 0xff7d }, { 8296, 0xffff }, { 8312, 0xffff }, { 8328, 0xffff },
  { 8344, 0xfffb }, { 8359, 0x77ff }, { 8373, 0x4000 }, { 8374, 0x1810 },
  { 8377, 0x0000 }, { 8377, 0x0040 }, { 8378, 0x1010 }, { 8380, 0x0200 },
  /* 0x7f00 */
  { 8381, 0x0400 }, { 8382, 0x4001 }, { 8384, 0x0000 }, { 8384, 0xfa80 },
  { 8391, 0xffcb }, { 8404, 0x7a4c }, { 8412, 0xb8f9 }, { 8422, 0xbde9 },
  { 8433, 0xabfd }, { 8445, 0x1bef }, { 8456, 0x7f6d }, { 8468, 0x4cfa },
  { 8477, 0xabdd }, { 8488, 0x7ecf }, { 8500, 0xbd9c }, { 8510, 0xe7f4 },
  /* 0x8000 */
  { 8521, 0xc784 }, { 8528, 0xec0a }, { 8535, 0xf81a }, { 8543, 0x5615 },
  { 8550, 0xc3b3 }, { 8559, 0xfaeb }, { 8571, 0xf9ff }, { 8585, 0x7ffd },
  { 8599, 0xe526 }, { 8607, 0x42b7 }, { 8615, 0x11c8 }, { 8620, 0x0b69 },
  { 8627, 0x8fa0 }, { 8634, 0x813f }, { 8642, 0x404d }, { 8647, 0xcaa0 },
  /* 0x8100 */
  { 8653, 0x19bb }, { 8662, 0xbaa0 }, { 8669, 0x6fff }, { 8683, 0xbeb9 },
  { 8694, 0xe2bf }, { 8705, 0xf9c4 }, { 8714, 0x9d5e }, { 8724, 0x01ec },
  { 8730, 0x7afa }, { 8741, 0xc6fd }, { 8752, 0xfab7 }, { 8764, 0xf3f7 },
  { 8777, 0xebb0 }, { 8786, 0xffff }, { 8802, 0xcb77 }, { 8813, 0xa7e7 },
  /* 0x8200 */
  { 8824, 0xcf88 }, { 8832, 0x27ea }, { 8841, 0x42f1 }, { 8848, 0xb404 },
  { 8853, 0x756f }, { 8864, 0x7aff }, { 8877, 0x3eff }, { 8890, 0x19e2 },
  { 8897, 0x12eb }, { 8905, 0x4c79 }, { 8913, 0x008d }, { 8917, 0x9c64 },
  { 8924, 0x026d }, { 8930, 0x2641 }, { 8935, 0x7784 }, { 8943, 0xf56d },
  /* 0x8300 */
  { 8954, 0x2c01 }, { 8958, 0xe34d }, { 8967, 0x467f }, { 8977, 0xe885 },
  { 8984, 0x7d36 }, { 8994, 0x23e8 }, { 9001, 0x0004 }, { 9002, 0xc67f },
  { 9013, 0xbd9f }, { 9025, 0xa6f3 }, { 9035, 0xf0fe }, { 9046, 0xc820 },
  { 9050, 0x6b5c }, { 9059, 0x4eaf }, { 9069, 0xf9dc }, { 9080, 0xdcf8 },
  /* 0x8400 */
  { 9090, 0x07a5 }, { 9097, 0xcefd }, { 9109, 0xfe0f }, { 9120, 0xcefd },
  { 9132, 0xffbf }, { 9147, 0xe17d }, { 9157, 0xc5f5 }, { 9167, 0xfa95 },
  { 9177, 0xa47b }, { 9186, 0xed7f }, { 9199, 0x7ffd }, { 9213, 0x58eb },
  { 9222, 0xd9ed }, { 9233, 0x5fb4 }, { 9243, 0xef96 }, { 9254, 0x6ffe },
  /* 0x8500 */
  { 9267, 0xefff }, { 9282, 0x7b75 }, { 9293, 0xe7fd }, { 9306, 0xc07f },
  { 9315, 0xf8f7 }, { 9327, 0xbdbf }, { 9340, 0xfeef }, { 9354, 0xb1eb },
  { 9364, 0x7f4f }, { 9376, 0xe7ff }, { 9390, 0x3aef }, { 9401, 0xfd7e },
  { 9414, 0x7dfd }, { 9427, 0xefd6 }, { 9439, 0xfdef }, { 9453, 0x77ff },
  /* 0x8600 */
  { 9467, 0xffdf }, { 9482, 0xffbd }, { 9496, 0xfd7f }, { 9510, 0xeeff },
  { 9524, 0x1fff }, { 9537, 0xbbec }, { 9548, 0xa7fb }, { 9560, 0x01fd },
  { 9568, 0xc3f8 }, { 9577, 0xcfd7 }, { 9589, 0x6867 }, { 9597, 0xfb8c },
  { 9607, 0x312e }, { 9614, 0x34ec }, { 9622, 0x9def }, { 9634, 0xbce0 },
  /* 0x8700 */
  { 9642, 0xd872 }, { 9650, 0xaa53 }, { 9658, 0xbdd1 }, { 9668, 0x376d },
  { 9678, 0xac7f }, { 9689, 0xfd77 }, { 9702, 0xbfc6 }, { 9713, 0x87ae },
  { 9722, 0xd6d3 }, { 9732, 0x7f77 }, { 9745, 0x46ff }, { 9756, 0xdbd7 },
  { 9768, 0xf3be }, { 9780, 0xf7f1 }, { 9792, 0xbbde }, { 9804, 0xbdff },
  /* 0x8800 */
  { 9818, 0xfbf7 }, { 9832, 0xf797 }, { 9844, 0xfff9 }, { 9858, 0xedfb },
  { 9871, 0xcfce }, { 9882, 0xfd6f }, { 9895, 0xa4c1 }, { 9901, 0x1f7a },
  { 9911, 0xd6c9 }, { 9920, 0xefbb }, { 9933, 0xd7eb }, { 9945, 0xef7d },
  { 9958, 0xbd99 }, { 9968, 0x7ccb }, { 9978, 0xfec3 }, { 9989, 0xace4 },
  /* 0x8900 */
  { 9997, 0xfbfb }, { 10011, 0xf1f2 }, { 10021, 0xf3dd }, { 10033, 0xffae },
  { 10046, 0xffed }, { 10060, 0x3fff }, { 10074, 0xffbf }, { 10089, 0x77ff },
  { 10103, 0xffb5 }, { 10116, 0xffff }, { 10132, 0xffff }, { 10148, 0xffff },
  { 10164, 0x2009 }, { 10167, 0xabb8 }, { 10176, 0x7797 }, { 10187, 0xfff7 },
  /* 0x8a00 */
  { 10202, 0xff7e }, { 10216, 0xffff }, { 10232, 0xffff }, { 10248, 0xbfff },
  { 10263, 0xfeff }, { 10278, 0xffff }, { 10294, 0xffff }, { 10310, 0xfdff },
  { 10325, 0xf9ff }, { 10339, 0xfff7 }, { 10354, 0xffff }, { 10370, 0xffff },
  { 10386, 0xffff }, { 10402, 0xffff }, { 10418, 0xffff }, { 10434, 0xffff },
  /* 0x8b00 */
  { 10450, 0xff7f }, { 10465, 0xffff }, { 10481, 0xffbf }, { 10496, 0xffff },
  { 10512, 0xffff }, { 10528, 0xffff }, { 10544, 0xefbf }, { 10558, 0xffff },
  { 10574, 0xffff }, { 10590, 0xffff }, { 10606, 0x1000 }, { 10607, 0x0802 },
  { 10609, 0x0080 }, { 10610, 0x0001 }, { 10611, 0x0400 }, { 10612, 0x0000 },
  /* 0x8c00 */
  { 10612, 0x0200 }, { 10613, 0x4000 }, { 10614, 0x0000 }, { 10614, 0xff00 },
  { 10622, 0xed3d }, { 10633, 0xfbdf }, { 10647, 0xf3f9 }, { 10659, 0xf8f7 },
  { 10671, 0xe9db }, { 10682, 0xfeef }, { 10696, 0xffff }, { 10712, 0xffff },
  { 10728, 0xffff }, { 10744, 0xffff }, { 10760, 0xffff }, { 10776, 0xffff },
  /* 0x8d00 */
  { 10792, 0xffff }, { 10808, 0x1fff }, { 10821, 0x0001 }, { 10822, 0x0000 },
  { 10822, 0x0000 }, { 10822, 0x8086 }, { 10826, 0xd720 }, { 10833, 0xff06 },
  { 10843, 0xf3cd }, { 10854, 0x7fed }, { 10867, 0xfff7 }, { 10882, 0x2ac5 },
  { 10889, 0x27a7 }, { 10898, 0x133d }, { 10906, 0x62e7 }, { 10915, 0xd057 },
  /* 0x8e00 */
  { 10923, 0x69df }, { 10934, 0x1fef }, { 10946, 0x29f3 }, { 10955, 0xd9dd },
  { 10966, 0xf068 }, { 10973, 0xfdf9 }, { 10986, 0x4dbf }, { 10997, 0x6faa },
  { 11007, 0x7f5d }, { 11019, 0xafee }, { 11031, 0x67ff }, { 11044, 0xfbfb },
  { 11058, 0xbfff }, { 11073, 0xffff }, { 11089, 0xffff }, { 11105, 0xffff },
  /* 0x8f00 */
  { 11121, 0xffff }, { 11137, 0xffff }, { 11153, 0xffff }, { 11169, 0xffff },
  { 11185, 0xffff }, { 11201, 0xffff }, { 11217, 0x043f }, { 11224, 0x0000 },
  { 11224, 0x1001 }, { 11226, 0x2004 }, { 11228, 0xf4f7 }, { 11240, 0x9dbc },
  { 11250, 0xbe49 }, { 11259, 0x04c4 }, { 11263, 0x908b }, { 11269, 0xdc76 },
  /* 0x9000 */
  { 11279, 0x5180 }, { 11283, 0x1328 }, { 11288, 0x1fb8 }, { 11297, 0xa69f },
  { 11307, 0x5f69 }, { 11317, 0xf670 }, { 11326, 0x9ed3 }, { 11336, 0x5fcf },
  { 11348, 0xf6f2 }, { 11359, 0xd555 }, { 11368, 0x2bb1 }, { 11376, 0xb084 },
  { 11381, 0x3b4d }, { 11390, 0xc774 }, { 11399, 0x5639 }, { 11407, 0x9eef },
  /* 0x9100 */
  { 11419, 0xffeb }, { 11433, 0xbdff }, { 11447, 0x7ff3 }, { 11460, 0xfdfd },
  { 11474, 0x01b7 }, { 11481, 0x9b7a }, { 11491, 0x29c1 }, { 11497, 0x1c08 },
  { 11501, 0xc55f }, { 11511, 0xf3f8 }, { 11522, 0x1bf3 }, { 11532, 0xfbcf },
  { 11545, 0x097f }, { 11554, 0xeffd }, { 11568, 0xffff }, { 11584, 0xffff },
  /* 0x9200 */
  { 11600, 0xffff }, { 11616, 0xffff }, { 11632, 0xffff }, { 11648, 0xffff },
  { 11664, 0xffff }, { 11680, 0xffff }, { 11696, 0xffff }, { 11712, 0xffef },
  { 11727, 0xbfff }, { 11742, 0xffff }, { 11758, 0xbfff }, { 11773, 0xffff },
  { 11789, 0xfeff }, { 11804, 0xffff }, { 11820, 0xffff }, { 11836, 0xffff },
  /* 0x9300 */
  { 11852, 0xffff }, { 11868, 0xffff }, { 11884, 0xffff }, { 11900, 0xbfff },
  { 11915, 0xffff }, { 11931, 0xffff }, { 11947, 0xfbff }, { 11962, 0xffff },
  { 11978, 0x7fff }, { 11993, 0xffff }, { 12009, 0xffff }, { 12025, 0xffff },
  { 12041, 0xfbff }, { 12056, 0xffbf }, { 12071, 0xffff }, { 12087, 0xffff },
  /* 0x9400 */
  { 12103, 0xffff }, { 12119, 0xffff }, { 12135, 0xffff }, { 12151, 0xbfff },
  { 12166, 0xffff }, { 12182, 0xffff }, { 12198, 0xf7ff }, { 12213, 0xffff },
  { 12229, 0x001f }, { 12234, 0x0142 }, { 12237, 0x0000 }, { 12237, 0x0000 },
  { 12237, 0x8080 }, { 12239, 0x0418 }, { 12242, 0x0040 }, { 12243, 0x0800 },
  /* 0x9500 */
  { 12244, 0x0000 }, { 12244, 0x1000 }, { 12245, 0x0081 }, { 12247, 0x2008 },
  { 12249, 0x0908 }, { 12252, 0x0420 }, { 12254, 0x4001 }, { 12256, 0x7fb0 },
  { 12266, 0xffff }, { 12282, 0xffff }, { 12298, 0xffff }, { 12314, 0xffff },
  { 12330, 0xffff }, { 12346, 0xffff }, { 12362, 0x10ff }, { 12371, 0x8000 },
  /* 0x9600 */
  { 12372, 0x0080 }, { 12373, 0x4908 }, { 12377, 0xbbf9 }, { 12389, 0x4781 },
  { 12395, 0xc40a }, { 12400, 0x77ce }, { 12411, 0xe869 }, { 12419, 0xff0b },
  { 12430, 0x569f }, { 12440, 0xec6e }, { 12450, 0xff7f }, { 12465, 0x8db6 },
  { 12474, 0x0d0c }, { 12479, 0xffdb }, { 12493, 0x78fe }, { 12504, 0xbd37 },
  /* 0x9700 */
  { 12515, 0x1c2c }, { 12521, 0xafb7 }, { 12533, 0xdbff }, { 12547, 0xbcfa },
  { 12558, 0xffff }, { 12574, 0xb5b3 }, { 12584, 0xfdd8 }, { 12595, 0xefa7 },
  { 12607, 0xd7df }, { 12620, 0xfee9 }, { 12632, 0x57f6 }, { 12643, 0xffeb },
  { 12657, 0xffff }, { 12673, 0xffff }, { 12689, 0xc13f }, { 12698, 0xff97 },
  /* 0x9800 */
  { 12711, 0xffff }, { 12727, 0xffff }, { 12743, 0xffff }, { 12759, 0xffff },
  { 12775, 0xffff }, { 12791, 0xffff }, { 12807, 0xffff }, { 12823, 0x001f },
  { 12828, 0x4800 }, { 12830, 0x0224 }, { 12833, 0xff08 }, { 12842, 0xffff },
  { 12858, 0xbfff }, { 12873, 0x38d1 }, { 12880, 0xfe7f }, { 12894, 0xffff },
  /* 0x9900 */
  { 12910, 0xdfff }, { 12925, 0xfffe }, { 12940, 0xbfff }, { 12955, 0xffff },
  { 12971, 0xffff }, { 12987, 0xffcf }, { 13001, 0x0057 }, { 13006, 0x4b08 },
  { 13011, 0x520c }, { 13016, 0xfc00 }, { 13022, 0xfedf }, { 13036, 0xffff },
  { 13052, 0xffff }, { 13068, 0xffff }, { 13084, 0xffff }, { 13100, 0xffff },
  /* 0x9a00 */
  { 13116, 0xffff }, { 13132, 0xffff }, { 13148, 0xffff }, { 13164, 0xffff },
  { 13180, 0xffff }, { 13196, 0xffff }, { 13212, 0x0fff }, { 13224, 0x0004 },
  { 13225, 0x6208 }, { 13229, 0x0230 }, { 13232, 0xfe40 }, { 13240, 0xea3c },
  { 13249, 0xe7d8 }, { 13259, 0x7ef5 }, { 13271, 0x57bd }, { 13282, 0xf5ff },
  /* 0x9b00 */
  { 13296, 0x7ef7 }, { 13309, 0x7ff7 }, { 13323, 0x7ff7 }, { 13337, 0xe7fb },
  { 13350, 0x5c41 }, { 13356, 0xffed }, { 13370, 0xffff }, { 13386, 0xffff },
  { 13402, 0xffff }, { 13418, 0xffff }, { 13434, 0xffff }, { 13450, 0xffff },
  { 13466, 0xffff }, { 13482, 0xffff }, { 13498, 0xffff }, { 13514, 0xffff },
  /* 0x9c00 */
  { 13530, 0xffff }, { 13546, 0xffff }, { 13562, 0xffff }, { 13578, 0xffff },
  { 13594, 0xffff }, { 13610, 0xffff }, { 13626, 0xffff }, { 13642, 0x6fff },
  { 13656, 0x9619 }, { 13663, 0x23c8 }, { 13669, 0x9400 }, { 13672, 0xc200 },
  { 13675, 0x0307 }, { 13680, 0x0c06 }, { 13684, 0xfffb }, { 13699, 0xffff },
  /* 0x9d00 */
  { 13715, 0xffff }, { 13731, 0xffff }, { 13747, 0xffff }, { 13763, 0xffff },
  { 13779, 0xffff }, { 13795, 0xffff }, { 13811, 0xffff }, { 13827, 0xffff },
  { 13843, 0xffff }, { 13859, 0xffff }, { 13875, 0xffff }, { 13891, 0xffff },
  { 13907, 0xffff }, { 13923, 0xffff }, { 13939, 0xffff }, { 13955, 0xffff },
  /* 0x9e00 */
  { 13971, 0xffff }, { 13987, 0x7fff }, { 14002, 0x4090 }, { 14005, 0x1811 },
  { 14009, 0x2001 }, { 14011, 0xa25d }, { 14019, 0xc027 }, { 14025, 0x3ff4 },
  { 14036, 0xf67b }, { 14048, 0x5ff3 }, { 14060, 0xffbf }, { 14075, 0x96ef },
  { 14086, 0x1def }, { 14097, 0x46ed }, { 14106, 0x795a }, { 14115, 0xa5ff },
  /* 0x9f00 */
  { 14127, 0x97ff }, { 14140, 0xfd76 }, { 14152, 0x6ffa }, { 14164, 0x957f },
  { 14175, 0xffef }, { 14190, 0xfffc }, { 14204, 0xffff }, { 14220, 0x7fff },
  { 14235, 0xe006 }, { 14240, 0x71ff }, { 14252, 0x003e },
};
static const Summary16 gbkext_inv_uni2indx_pagef9[19] = {
  /* 0xf900 */
  { 14257, 0x0000 }, { 14257, 0x0000 }, { 14257, 0x1000 }, { 14258, 0x0000 },
  { 14258, 0x0000 }, { 14258, 0x0000 }, { 14258, 0x0000 }, { 14258, 0x0200 },
  { 14259, 0x0000 }, { 14259, 0x0020 }, { 14260, 0x0000 }, { 14260, 0x0000 },
  { 14260, 0x0000 }, { 14260, 0x0000 }, { 14260, 0x0080 }, { 14261, 0x0002 },
  /* 0xfa00 */
  { 14262, 0xf000 }, { 14266, 0x811a }, { 14271, 0x039b },
};
static const Summary16 gbkext_inv_uni2indx_pagefe[31] = {
  /* 0xfe00 */
  { 14278, 0x0000 }, { 14278, 0x0000 }, { 14278, 0x0000 }, { 14278, 0x0001 },
  { 14279, 0xfe00 }, { 14286, 0xfef7 }, { 14300, 0x0f7f }, { 14311, 0x0000 },
  { 14311, 0x0000 }, { 14311, 0x0000 }, { 14311, 0x0000 }, { 14311, 0x0000 },
  { 14311, 0x0000 }, { 14311, 0x0000 }, { 14311, 0x0000 }, { 14311, 0x0000 },
  /* 0xff00 */
  { 14311, 0x0000 }, { 14311, 0x0000 }, { 14311, 0x0000 }, { 14311, 0x0000 },
  { 14311, 0x0000 }, { 14311, 0x0000 }, { 14311, 0x0000 }, { 14311, 0x0000 },
  { 14311, 0x0000 }, { 14311, 0x0000 }, { 14311, 0x0000 }, { 14311, 0x0000 },
  { 14311, 0x0000 }, { 14311, 0x0000 }, { 14311, 0x0014 },
};

static int
gbkext_inv_wctomb (conv_t conv, unsigned char *r, ucs4_t wc, int n)
{
  if (n >= 2) {
    const Summary16 *summary = NULL;
    if (wc >= 0x0200 && wc < 0x02e0)
      summary = &gbkext_inv_uni2indx_page02[(wc>>4)-0x020];
    else if (wc >= 0x2000 && wc < 0x22c0)
      summary = &gbkext_inv_uni2indx_page20[(wc>>4)-0x200];
    else if (wc >= 0x2500 && wc < 0x2610)
      summary = &gbkext_inv_uni2indx_page25[(wc>>4)-0x250];
    else if (wc >= 0x3000 && wc < 0x3100)
      summary = &gbkext_inv_uni2indx_page30[(wc>>4)-0x300];
    else if (wc >= 0x3200 && wc < 0x33e0)
      summary = &gbkext_inv_uni2indx_page32[(wc>>4)-0x320];
    else if (wc >= 0x4e00 && wc < 0x9fb0)
      summary = &gbkext_inv_uni2indx_page4e[(wc>>4)-0x4e0];
    else if (wc >= 0xf900 && wc < 0xfa30)
      summary = &gbkext_inv_uni2indx_pagef9[(wc>>4)-0xf90];
    else if (wc >= 0xfe00 && wc < 0xfff0)
      summary = &gbkext_inv_uni2indx_pagefe[(wc>>4)-0xfe0];
    if (summary) {
      unsigned short used = summary->used;
      unsigned int i = wc & 0x0f;
      if (used & ((unsigned short) 1 << i)) {
        unsigned short c;
        /* Keep in `used' only the bits 0..i-1. */
        used &= ((unsigned short) 1 << i) - 1;
        /* Add `summary->indx' and the number of bits set in `used'. */
        used = (used & 0x5555) + ((used & 0xaaaa) >> 1);
        used = (used & 0x3333) + ((used & 0xcccc) >> 2);
        used = (used & 0x0f0f) + ((used & 0xf0f0) >> 4);
        used = (used & 0x00ff) + (used >> 8);
        c = gbkext_inv_2charset[summary->indx + used];
        r[0] = (c >> 8); r[1] = (c & 0xff);
        return 2;
      }
    }
    return RET_ILUNI;
  }
  return RET_TOOSMALL;
}
