#*************************企业通**********************************************

CI：
em-enterprise-management-stock-web，用户数据-企业通-业务平台-证券周边  em-enterprise-management-gdweb
em-enterprise-management-data-service，用户数据-企业通-数据落地服务
em-enterprise-management-backend，用户数据-企业通-运营后台
em-enterprise-management-web，用户数据-企业通-业务平台
em-enterprise-management-service，用户数据-企业通-数据服务
em-enterprise-management-api，用户数据-企业通-单点登录

	svn：
	https://172.30.64.71/svn/AccountData/em-enterprise-management，所有服务在线

	CI：
	#######*********   em-enterprise-management-stock-web，用户数据-企业通-业务平台-证券周边      em-enterprise-management-gdweb
	接口信息:http://61.129.116.7:59248/service/swagger-ui.html
	    ------ShareHolderController  咨询数据、地域分布、激励工具、解禁日收益率等   <-----  redis
		------UserController         <-------- 企业通登录验证接口 http://61.129.116.7:59248/ent/auth/verifylogin


	#######*********   em-enterprise-management-data-service，用户数据-企业通-数据落地服务
	(http://172.30.64.71:8092/pages/viewpage.action?pageId=********) 

	----business 
	----实时数据更新-kafka数据(热词的功能)  Java KafkaListener监听  -----热词的功能 count次数 --->
	落地到mysql(代码写的是mongodb) --->逻辑有些繁琐

		----cron job 同步数据
		--------SyncAuthorSearchJob  同步账号数据 --- 暂未写入mongodb  --- 废弃
		--------SyncGbPostUserJob    同步股吧数据
		--------SyncOrgSearchJob     从东财同步企业机构相关数据   ----暂未写入mongodb  ---废弃
		--------SyncReportSearchJob    同步研报数据
		--------SyncSelfSelectStockJob  自选股数据
		--------SyncStockArtCountJob    爬虫 （媒体关注指数)
		--------SyncYqClickJob         ----暂未写入mongodb  ---废弃 
		--------SyncYqHotWordTopJob    热点数据
		--------SyncYqSearchJob         舆情
		--------SyncYqSourceTopJob      舆情来源


		----service层    都是为了给cron-job 用于同步状态判断
		--------EnterpriseDataService    注:企业数据服务 -->mysql -- queryAllEnterprise 查询企业数据  用于各个job做逻辑判断
		--------GbPostUserService        注:股吧服务    ----> mongodb 
		--------MindMappingDataService   注:脑图数据   ----> mongodb 
		--------ReadDataService          注:  kafka实时热词有用到   ----> mongodb
		--------ReportSearchDataService  注:研报    ----> mongodb  
		--------SelfSelectStockService   注:自选股    ----> mongodb
		--------StockArtCountService     注: 爬虫 （媒体关注指数)   ----> mongodb
		--------UpdateTimeService        注：更新job-热词同步时间用于管理 ---->msyql
		--------YqClickDataService       注: 热点数据 ----> mongodb   ----废弃
		--------YqHotWordTopDataService  注:热词数据    ----> mongodb
		--------YqResearchDataService    注:舆情   ----> mongodb
		--------YqSourceDataService      注:舆情来源    ----> mongodb


		----dao层服务 
		主要是 queryAllEnterprise 查询企业数据  IUpdateTimeMapper 更新企业实体数据
		----db 有mongodb mysql 

	 
	#######*********   em-enterprise-management-service， 企业通内部数据同步缓存服务  
	http://172.30.64.71:8092/pages/viewpage.action?pageId=90982768

		----JOB
		---------ShareHolderJob     注：同步股东数据  将mysql的数据写入到redis     
										Redis key 
										shareholder:areadist 小定增服务—地区分布
										shareholder:issuingobject 小定增服务—发行对象类型
										shareholder:liftingday 小定增服务—解禁日收益率
										shareholder:stocksource 股权激励—股份来源
										shareholder:motivationtool 股权激励—激励工具
										shareholder:motivationnumber 股权激励—激励人数
										shareholder:plantime 股权激励—计划时间
										
		---------SyncCompanyAccountJob     注: 同步企业通上市公司账号数据到redis缓存  
										Redis key
										companyAccount:id:+加密的用户名 单个账户数据
										companyAccount:phone:+加密的手机号 单个手机号数据
										
		--------SyncMenuJob           注:同步菜单数据到redis缓存 
			
										Redis key
										menu:clientId:+菜单clientId 单个菜单
										roleMenu:menus 所有菜单

		--------SyncRoleMenuJob       注:同步角色的菜单权限数据到缓存
										Redis key
										roleMenu:roleId:+角色id 单个角色的菜单权限                                
			  
		--------SyncModuleJob         注:同步模块信息到redis 
									  Redis key 
									  roleModule:modules
									  module:clientId:客户号
									  
		--------SyncRoleModuleJob     注：同步角色和模块映射信息
									  Redis key
									  roleModule:roleId:getRoleId()
									  
		----service层                 注：核心是用于job任务进行数据查询
		--------CompanyAccountDataService   注:查询企业通上市公司账号数据
		--------MenuDataService    			注:查询菜单数据
		--------ModuleDataService  			注:查询模块数据
		--------RoleMenuDataService         注:查询角色<->菜单数据
		--------RoleMenuDataService         注:查询角色<->模块数据
		--------ShareHolderService         注:查询股东数据<->菜单数据  表(stocksource等)
								  


	#######*********   em-enterprise-management-backend，用户数据-企业通-运营后台   
	接口信息(http://61.129.116.7:59248/ent-admin/swagger-ui.html)  
	    ------AccountController                   注：账户相关接口   
        ------AskAnswerCardController             注：问董秘卡片管理相关接口   
		------ConfigController                    注：配置管理相关接口
		------DataController                      注：全量数据相关接口
		------EnterpriseController                注：公司相关接口
		------LoginController                     注：单点登录相关接口
		------MenuController                      注：菜单相关接口
		------ModuleController                    注：模块相关接口
		------RoleController                      注：角色管理接口
		------ShareholderController               注：股东服务相关接口
		crud
		<-----------------   mysql  : ent_dev数据库
		----------------->   mysql  : ent_dev数据库

	#######*********   em-enterprise-management-web，用户数据-企业通-业务平台   
	接口信息(http://61.129.116.7:59248/ent/swagger-ui.html)
	文档：http://172.30.64.71:8092/pages/viewpage.action?pageId=90982813      
	    ------ChoiceController                    注：Choice相关接口          <-------- mongodb
		------GubaController                      注：股吧相关接口            <-------- mongodb
		------LoginController                     注：登录相关接口            <-------- redis
		------MediaAttentionController            注：媒体关注相关接口          <-------- mongodb
		------SelfStockController                 注：自选股相关接口          <-------- mongodb
		------SetPasswordController               注：设置密码页面接口        <-------- redis
		------StockPledgeController               注：股票质押相关接口        <-------- mysql : ent_dev数据库


	#######*********   em-enterprise-management-api，用户数据-企业通-单点登录    
	http://172.30.64.71:8092/pages/viewpage.action?pageId=********
        ------EnterpriseController                注：获取企业列表接口          <-------- mysql ：ent_dev
		------LoginController                     注：单点登录相关接口          <-------- http://audit.zmt.zptest.emapd.com/sso/    api/Account/login   带上token
		------UserController                      注：企业通用户相关接口        <-------- redis



#*************数据流程----股东服务**************
CI：
em-integrated-management-service，上市公司综合管理-数据处理服务
em-integrated-management-web，上市公司综合管理

em-enterprise-management-gdweb 股东服务
  ShareHolderController
     地区分布      <------ redis key：shareholder:areadist
	 发行对象类型  <------ redis key：redis key：shareholder:issuingobject
	 解禁日收益率  <------ redis key：redis key：shareholder:liftingday
	 股份来源      <------ redis key：redis key：shareholder:stocksource
	 激励工具      <------ redis key：redis key：shareholder:motivationtool
	 激励人数      <------ redis key：redis key：shareholder:motivationnumber
	 计划时间      <------ redis key：redis key：shareholder:plantime
	 
em-enterprise-management-service 内部同步缓存数据
  ShareHolderJob
	 地区分布      <------ mysql：ent_dev  ------> redis key： shareholder:areadist 
	 发行对象类型  <------ mysql：ent_dev  ------> redis key：shareholder:issuingobject
	 解禁日收益率  <------ mysql：ent_dev  ------> redis key：shareholder:liftingday
	 股份来源      <------ mysql：ent_dev  ------> redis key：shareholder:stocksource
	 激励工具      <------ mysql：ent_dev  ------> redis key：shareholder:motivationtool
	 激励人数      <------ mysql：ent_dev  ------> redis key：shareholder:motivationnumber
	 计划时间      <------ mysql：ent_dev  ------> redis key：shareholder:plantime

em-enterprise-management-backend  运营后台
  ShareholderController    mysql：crud
    地区分布
	发行对象类型
    解禁日收益率	
	股份来源
	激励工具
	激励人数
	计划时间


结论：
  股东服务gdweb：
    1、只读reids
  运营后台backend：
    1、backend 运营后台只有mysql crud,没有redis、mongodb
  业务平台web：
    1、股票质押  mysql（企业通业务库）
	2、短信、验证码、登录   mysql、redis
	3、choice、股吧、媒体关注指数、自选股 mongodb
	



	  

	 
  
  

#**************************************股权激励**********************************************************
	文档：
	http://172.30.64.71:8092/pages/viewpage.action?pageId=69108568

	git：
	http://172.30.64.82/em-integrated-management
	em-integrated-management-web，在线
	em-integrated-management-service，在线

	#######*********   em-integrated-management-web  股权激励-上市公司综合管理
	接口整理在excel

	#######*********   em-integrated-management-service，股权激励-上市公司综合管理-数据处理服务

	数据库：
	------- LCIMS
	------- FIQS
	------- EMZQDC
	------- EMBASE


	jobs：定时任务执行同步
	BaseRedisJob：redis缓存员工、行权清算等数据

	-----  LCIMS库 数据同步至 FIQS 库 --------
	IncentOptionPlanJob：客户期权激励计划同步
	 -- IncentOptionService 落地 FIQS库
	 
	IncentShareHoldingPlanJob：员工持股计划同步
	 -- IncentShareHoldingService 落地 FIQS库
	 
	IncentStockPlanJob：客户限制性股票激励计划同步
	 -- IncentStockService  落地 FIQS库
	 
	 UpdatDateJob：更新核算交易日
	 UpdateOrderJob：更新行权数据
	 UpdatePriceJob：更新市价
	 UpdateRepoJob：核算任务（股权激励计划【一类、二类】、股票期权股权激励计划、员工持股计划股权激励计划、计划显示方案、量价调整
	 
	 reids:缓存员工、行权清算等数据
	  问题：web端并没有调用？
	 
	 
	incent.option.sync.cron=0 0/5 * * * ?
	incent.share.sync.cron=0 0/5 * * * ?
	incent.stock.sync.cron=0 0/5 * * * ?
	base.redis.sync.cron = 0 0/2 * * * ?
	update.order.cron = 0 0/3 * * * ?
	update.price.cron = 0 0/3 * * * ?
	update.date.cron = 0 0/3 * * * ?
	update.repo.cron = 0 0/3 * * * ?